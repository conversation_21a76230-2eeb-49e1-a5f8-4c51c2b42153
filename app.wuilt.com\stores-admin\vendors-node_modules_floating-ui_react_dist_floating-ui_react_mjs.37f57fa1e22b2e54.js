(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_floating-ui_react_dist_floating-ui_react_mjs"], {

        /***/
        "../../node_modules/tabbable/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    focusable: () => ( /* binding */ focusable),
                    /* harmony export */
                    getTabIndex: () => ( /* binding */ getTabIndex),
                    /* harmony export */
                    isFocusable: () => ( /* binding */ isFocusable),
                    /* harmony export */
                    isTabbable: () => ( /* binding */ isTabbable),
                    /* harmony export */
                    tabbable: () => ( /* binding */ tabbable)
                    /* harmony export */
                });
                /*!
                 * tabbable 6.2.0
                 * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
                 */
                // NOTE: separate `:not()` selectors has broader browser support than the newer
                //  `:not([inert], [inert] *)` (Feb 2023)
                // CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes
                //  the entire query to fail, resulting in no nodes found, which will break a lot
                //  of things... so we have to rely on JS to identify nodes inside an inert container
                var candidateSelectors = ['input:not([inert])', 'select:not([inert])', 'textarea:not([inert])', 'a[href]:not([inert])', 'button:not([inert])', '[tabindex]:not(slot):not([inert])', 'audio[controls]:not([inert])', 'video[controls]:not([inert])', '[contenteditable]:not([contenteditable="false"]):not([inert])', 'details>summary:first-of-type:not([inert])', 'details:not([inert])'];
                var candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');
                var NoElement = typeof Element === 'undefined';
                var matches = NoElement ? function() {} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
                var getRootNode = !NoElement && Element.prototype.getRootNode ? function(element) {
                    var _element$getRootNode;
                    return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);
                } : function(element) {
                    return element === null || element === void 0 ? void 0 : element.ownerDocument;
                };

                /**
                 * Determines if a node is inert or in an inert ancestor.
                 * @param {Element} [node]
                 * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to
                 *  see if any of them are inert. If false, only `node` itself is considered.
                 * @returns {boolean} True if inert itself or by way of being in an inert ancestor.
                 *  False if `node` is falsy.
                 */
                var isInert = function isInert(node, lookUp) {
                    var _node$getAttribute;
                    if (lookUp === void 0) {
                        lookUp = true;
                    }
                    // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`
                    //  JS API property; we have to check the attribute, which can either be empty or 'true';
                    //  if it's `null` (not specified) or 'false', it's an active element
                    var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'inert');
                    var inert = inertAtt === '' || inertAtt === 'true';

                    // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`
                    //  if it weren't for `matches()` not being a function on shadow roots; the following
                    //  code works for any kind of node
                    // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`
                    //  so it likely would not support `:is([inert] *)` either...
                    var result = inert || lookUp && node && isInert(node.parentNode); // recursive

                    return result;
                };

                /**
                 * Determines if a node's content is editable.
                 * @param {Element} [node]
                 * @returns True if it's content-editable; false if it's not or `node` is falsy.
                 */
                var isContentEditable = function isContentEditable(node) {
                    var _node$getAttribute2;
                    // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have
                    //  to use the attribute directly to check for this, which can either be empty or 'true';
                    //  if it's `null` (not specified) or 'false', it's a non-editable element
                    var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, 'contenteditable');
                    return attValue === '' || attValue === 'true';
                };

                /**
                 * @param {Element} el container to check in
                 * @param {boolean} includeContainer add container to check
                 * @param {(node: Element) => boolean} filter filter candidates
                 * @returns {Element[]}
                 */
                var getCandidates = function getCandidates(el, includeContainer, filter) {
                    // even if `includeContainer=false`, we still have to check it for inertness because
                    //  if it's inert, all its children are inert
                    if (isInert(el)) {
                        return [];
                    }
                    var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));
                    if (includeContainer && matches.call(el, candidateSelector)) {
                        candidates.unshift(el);
                    }
                    candidates = candidates.filter(filter);
                    return candidates;
                };

                /**
                 * @callback GetShadowRoot
                 * @param {Element} element to check for shadow root
                 * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.
                 */

                /**
                 * @callback ShadowRootFilter
                 * @param {Element} shadowHostNode the element which contains shadow content
                 * @returns {boolean} true if a shadow root could potentially contain valid candidates.
                 */

                /**
                 * @typedef {Object} CandidateScope
                 * @property {Element} scopeParent contains inner candidates
                 * @property {Element[]} candidates list of candidates found in the scope parent
                 */

                /**
                 * @typedef {Object} IterativeOptions
                 * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;
                 *  if a function, implies shadow support is enabled and either returns the shadow root of an element
                 *  or a boolean stating if it has an undisclosed shadow root
                 * @property {(node: Element) => boolean} filter filter candidates
                 * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list
                 * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;
                 */

                /**
                 * @param {Element[]} elements list of element containers to match candidates from
                 * @param {boolean} includeContainer add container list to check
                 * @param {IterativeOptions} options
                 * @returns {Array.<Element|CandidateScope>}
                 */
                var getCandidatesIteratively = function getCandidatesIteratively(elements, includeContainer, options) {
                    var candidates = [];
                    var elementsToCheck = Array.from(elements);
                    while (elementsToCheck.length) {
                        var element = elementsToCheck.shift();
                        if (isInert(element, false)) {
                            // no need to look up since we're drilling down
                            // anything inside this container will also be inert
                            continue;
                        }
                        if (element.tagName === 'SLOT') {
                            // add shadow dom slot scope (slot itself cannot be focusable)
                            var assigned = element.assignedElements();
                            var content = assigned.length ? assigned : element.children;
                            var nestedCandidates = getCandidatesIteratively(content, true, options);
                            if (options.flatten) {
                                candidates.push.apply(candidates, nestedCandidates);
                            } else {
                                candidates.push({
                                    scopeParent: element,
                                    candidates: nestedCandidates
                                });
                            }
                        } else {
                            // check candidate element
                            var validCandidate = matches.call(element, candidateSelector);
                            if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {
                                candidates.push(element);
                            }

                            // iterate over shadow content if possible
                            var shadowRoot = element.shadowRoot ||
                                // check for an undisclosed shadow
                                typeof options.getShadowRoot === 'function' && options.getShadowRoot(element);

                            // no inert look up because we're already drilling down and checking for inertness
                            //  on the way down, so all containers to this root node should have already been
                            //  vetted as non-inert
                            var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));
                            if (shadowRoot && validShadowRoot) {
                                // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed
                                //  shadow exists, so look at light dom children as fallback BUT create a scope for any
                                //  child candidates found because they're likely slotted elements (elements that are
                                //  children of the web component element (which has the shadow), in the light dom, but
                                //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,
                                //  _after_ we return from this recursive call
                                var _nestedCandidates = getCandidatesIteratively(shadowRoot === true ? element.children : shadowRoot.children, true, options);
                                if (options.flatten) {
                                    candidates.push.apply(candidates, _nestedCandidates);
                                } else {
                                    candidates.push({
                                        scopeParent: element,
                                        candidates: _nestedCandidates
                                    });
                                }
                            } else {
                                // there's not shadow so just dig into the element's (light dom) children
                                //  __without__ giving the element special scope treatment
                                elementsToCheck.unshift.apply(elementsToCheck, element.children);
                            }
                        }
                    }
                    return candidates;
                };

                /**
                 * @private
                 * Determines if the node has an explicitly specified `tabindex` attribute.
                 * @param {HTMLElement} node
                 * @returns {boolean} True if so; false if not.
                 */
                var hasTabIndex = function hasTabIndex(node) {
                    return !isNaN(parseInt(node.getAttribute('tabindex'), 10));
                };

                /**
                 * Determine the tab index of a given node.
                 * @param {HTMLElement} node
                 * @returns {number} Tab order (negative, 0, or positive number).
                 * @throws {Error} If `node` is falsy.
                 */
                var getTabIndex = function getTabIndex(node) {
                    if (!node) {
                        throw new Error('No node provided');
                    }
                    if (node.tabIndex < 0) {
                        // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default
                        // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,
                        // yet they are still part of the regular tab order; in FF, they get a default
                        // `tabIndex` of 0; since Chrome still puts those elements in the regular tab
                        // order, consider their tab index to be 0.
                        // Also browsers do not return `tabIndex` correctly for contentEditable nodes;
                        // so if they don't have a tabindex attribute specifically set, assume it's 0.
                        if ((/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && !hasTabIndex(node)) {
                            return 0;
                        }
                    }
                    return node.tabIndex;
                };

                /**
                 * Determine the tab index of a given node __for sort order purposes__.
                 * @param {HTMLElement} node
                 * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,
                 *  has tabIndex -1, but needs to be sorted by document order in order for its content to be
                 *  inserted into the correct sort position.
                 * @returns {number} Tab order (negative, 0, or positive number).
                 */
                var getSortOrderTabIndex = function getSortOrderTabIndex(node, isScope) {
                    var tabIndex = getTabIndex(node);
                    if (tabIndex < 0 && isScope && !hasTabIndex(node)) {
                        return 0;
                    }
                    return tabIndex;
                };
                var sortOrderedTabbables = function sortOrderedTabbables(a, b) {
                    return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;
                };
                var isInput = function isInput(node) {
                    return node.tagName === 'INPUT';
                };
                var isHiddenInput = function isHiddenInput(node) {
                    return isInput(node) && node.type === 'hidden';
                };
                var isDetailsWithSummary = function isDetailsWithSummary(node) {
                    var r = node.tagName === 'DETAILS' && Array.prototype.slice.apply(node.children).some(function(child) {
                        return child.tagName === 'SUMMARY';
                    });
                    return r;
                };
                var getCheckedRadio = function getCheckedRadio(nodes, form) {
                    for (var i = 0; i < nodes.length; i++) {
                        if (nodes[i].checked && nodes[i].form === form) {
                            return nodes[i];
                        }
                    }
                };
                var isTabbableRadio = function isTabbableRadio(node) {
                    if (!node.name) {
                        return true;
                    }
                    var radioScope = node.form || getRootNode(node);
                    var queryRadios = function queryRadios(name) {
                        return radioScope.querySelectorAll('input[type="radio"][name="' + name + '"]');
                    };
                    var radioSet;
                    if (typeof window !== 'undefined' && typeof window.CSS !== 'undefined' && typeof window.CSS.escape === 'function') {
                        radioSet = queryRadios(window.CSS.escape(node.name));
                    } else {
                        try {
                            radioSet = queryRadios(node.name);
                        } catch (err) {
                            // eslint-disable-next-line no-console
                            console.error('Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s', err.message);
                            return false;
                        }
                    }
                    var checked = getCheckedRadio(radioSet, node.form);
                    return !checked || checked === node;
                };
                var isRadio = function isRadio(node) {
                    return isInput(node) && node.type === 'radio';
                };
                var isNonTabbableRadio = function isNonTabbableRadio(node) {
                    return isRadio(node) && !isTabbableRadio(node);
                };

                // determines if a node is ultimately attached to the window's document
                var isNodeAttached = function isNodeAttached(node) {
                    var _nodeRoot;
                    // The root node is the shadow root if the node is in a shadow DOM; some document otherwise
                    //  (but NOT _the_ document; see second 'If' comment below for more).
                    // If rootNode is shadow root, it'll have a host, which is the element to which the shadow
                    //  is attached, and the one we need to check if it's in the document or not (because the
                    //  shadow, and all nodes it contains, is never considered in the document since shadows
                    //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,
                    //  is hidden, or is not in the document itself but is detached, it will affect the shadow's
                    //  visibility, including all the nodes it contains). The host could be any normal node,
                    //  or a custom element (i.e. web component). Either way, that's the one that is considered
                    //  part of the document, not the shadow root, nor any of its children (i.e. the node being
                    //  tested).
                    // To further complicate things, we have to look all the way up until we find a shadow HOST
                    //  that is attached (or find none) because the node might be in nested shadows...
                    // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the
                    //  document (per the docs) and while it's a Document-type object, that document does not
                    //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer
                    //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,
                    //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when
                    //  node is actually detached.
                    // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible
                    //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed
                    //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then
                    //  `ownerDocument` will be `null`, hence the optional chaining on it.
                    var nodeRoot = node && getRootNode(node);
                    var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;

                    // in some cases, a detached node will return itself as the root instead of a document or
                    //  shadow root object, in which case, we shouldn't try to look further up the host chain
                    var attached = false;
                    if (nodeRoot && nodeRoot !== node) {
                        var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;
                        attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));
                        while (!attached && nodeRootHost) {
                            var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;
                            // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,
                            //  which means we need to get the host's host and check if that parent host is contained
                            //  in (i.e. attached to) the document
                            nodeRoot = getRootNode(nodeRootHost);
                            nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;
                            attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));
                        }
                    }
                    return attached;
                };
                var isZeroArea = function isZeroArea(node) {
                    var _node$getBoundingClie = node.getBoundingClientRect(),
                        width = _node$getBoundingClie.width,
                        height = _node$getBoundingClie.height;
                    return width === 0 && height === 0;
                };
                var isHidden = function isHidden(node, _ref) {
                    var displayCheck = _ref.displayCheck,
                        getShadowRoot = _ref.getShadowRoot;
                    // NOTE: visibility will be `undefined` if node is detached from the document
                    //  (see notes about this further down), which means we will consider it visible
                    //  (this is legacy behavior from a very long way back)
                    // NOTE: we check this regardless of `displayCheck="none"` because this is a
                    //  _visibility_ check, not a _display_ check
                    if (getComputedStyle(node).visibility === 'hidden') {
                        return true;
                    }
                    var isDirectSummary = matches.call(node, 'details>summary:first-of-type');
                    var nodeUnderDetails = isDirectSummary ? node.parentElement : node;
                    if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {
                        return true;
                    }
                    if (!displayCheck || displayCheck === 'full' || displayCheck === 'legacy-full') {
                        if (typeof getShadowRoot === 'function') {
                            // figure out if we should consider the node to be in an undisclosed shadow and use the
                            //  'non-zero-area' fallback
                            var originalNode = node;
                            while (node) {
                                var parentElement = node.parentElement;
                                var rootNode = getRootNode(node);
                                if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true // check if there's an undisclosed shadow
                                ) {
                                    // node has an undisclosed shadow which means we can only treat it as a black box, so we
                                    //  fall back to a non-zero-area test
                                    return isZeroArea(node);
                                } else if (node.assignedSlot) {
                                    // iterate up slot
                                    node = node.assignedSlot;
                                } else if (!parentElement && rootNode !== node.ownerDocument) {
                                    // cross shadow boundary
                                    node = rootNode.host;
                                } else {
                                    // iterate up normal dom
                                    node = parentElement;
                                }
                            }
                            node = originalNode;
                        }
                        // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support
                        //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or
                        //  it might be a falsy value, which means shadow DOM support is disabled

                        // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)
                        //  now we can just test to see if it would normally be visible or not, provided it's
                        //  attached to the main document.
                        // NOTE: We must consider case where node is inside a shadow DOM and given directly to
                        //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.

                        if (isNodeAttached(node)) {
                            // this works wherever the node is: if there's at least one client rect, it's
                            //  somehow displayed; it also covers the CSS 'display: contents' case where the
                            //  node itself is hidden in place of its contents; and there's no need to search
                            //  up the hierarchy either
                            return !node.getClientRects().length;
                        }

                        // Else, the node isn't attached to the document, which means the `getClientRects()`
                        //  API will __always__ return zero rects (this can happen, for example, if React
                        //  is used to render nodes onto a detached tree, as confirmed in this thread:
                        //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)
                        //
                        // It also means that even window.getComputedStyle(node).display will return `undefined`
                        //  because styles are only computed for nodes that are in the document.
                        //
                        // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable
                        //  somehow. Though it was never stated officially, anyone who has ever used tabbable
                        //  APIs on nodes in detached containers has actually implicitly used tabbable in what
                        //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck="none"` mode -- essentially
                        //  considering __everything__ to be visible because of the innability to determine styles.
                        //
                        // v6.0.0: As of this major release, the default 'full' option __no longer treats detached
                        //  nodes as visible with the 'none' fallback.__
                        if (displayCheck !== 'legacy-full') {
                            return true; // hidden
                        }
                        // else, fallback to 'none' mode and consider the node visible
                    } else if (displayCheck === 'non-zero-area') {
                        // NOTE: Even though this tests that the node's client rect is non-zero to determine
                        //  whether it's displayed, and that a detached node will __always__ have a zero-area
                        //  client rect, we don't special-case for whether the node is attached or not. In
                        //  this mode, we do want to consider nodes that have a zero area to be hidden at all
                        //  times, and that includes attached or not.
                        return isZeroArea(node);
                    }

                    // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume
                    //  it's visible
                    return false;
                };

                // form fields (nested) inside a disabled fieldset are not focusable/tabbable
                //  unless they are in the _first_ <legend> element of the top-most disabled
                //  fieldset
                var isDisabledFromFieldset = function isDisabledFromFieldset(node) {
                    if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {
                        var parentNode = node.parentElement;
                        // check if `node` is contained in a disabled <fieldset>
                        while (parentNode) {
                            if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {
                                // look for the first <legend> among the children of the disabled <fieldset>
                                for (var i = 0; i < parentNode.children.length; i++) {
                                    var child = parentNode.children.item(i);
                                    // when the first <legend> (in document order) is found
                                    if (child.tagName === 'LEGEND') {
                                        // if its parent <fieldset> is not nested in another disabled <fieldset>,
                                        // return whether `node` is a descendant of its first <legend>
                                        return matches.call(parentNode, 'fieldset[disabled] *') ? true : !child.contains(node);
                                    }
                                }
                                // the disabled <fieldset> containing `node` has no <legend>
                                return true;
                            }
                            parentNode = parentNode.parentElement;
                        }
                    }

                    // else, node's tabbable/focusable state should not be affected by a fieldset's
                    //  enabled/disabled state
                    return false;
                };
                var isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable(options, node) {
                    if (node.disabled ||
                        // we must do an inert look up to filter out any elements inside an inert ancestor
                        //  because we're limited in the type of selectors we can use in JSDom (see related
                        //  note related to `candidateSelectors`)
                        isInert(node) || isHiddenInput(node) || isHidden(node, options) ||
                        // For a details element with a summary, the summary element gets the focus
                        isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {
                        return false;
                    }
                    return true;
                };
                var isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable(options, node) {
                    if (isNonTabbableRadio(node) || getTabIndex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {
                        return false;
                    }
                    return true;
                };
                var isValidShadowRootTabbable = function isValidShadowRootTabbable(shadowHostNode) {
                    var tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);
                    if (isNaN(tabIndex) || tabIndex >= 0) {
                        return true;
                    }
                    // If a custom element has an explicit negative tabindex,
                    // browsers will not allow tab targeting said element's children.
                    return false;
                };

                /**
                 * @param {Array.<Element|CandidateScope>} candidates
                 * @returns Element[]
                 */
                var sortByOrder = function sortByOrder(candidates) {
                    var regularTabbables = [];
                    var orderedTabbables = [];
                    candidates.forEach(function(item, i) {
                        var isScope = !!item.scopeParent;
                        var element = isScope ? item.scopeParent : item;
                        var candidateTabindex = getSortOrderTabIndex(element, isScope);
                        var elements = isScope ? sortByOrder(item.candidates) : element;
                        if (candidateTabindex === 0) {
                            isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);
                        } else {
                            orderedTabbables.push({
                                documentOrder: i,
                                tabIndex: candidateTabindex,
                                item: item,
                                isScope: isScope,
                                content: elements
                            });
                        }
                    });
                    return orderedTabbables.sort(sortOrderedTabbables).reduce(function(acc, sortable) {
                        sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);
                        return acc;
                    }, []).concat(regularTabbables);
                };
                var tabbable = function tabbable(container, options) {
                    options = options || {};
                    var candidates;
                    if (options.getShadowRoot) {
                        candidates = getCandidatesIteratively([container], options.includeContainer, {
                            filter: isNodeMatchingSelectorTabbable.bind(null, options),
                            flatten: false,
                            getShadowRoot: options.getShadowRoot,
                            shadowRootFilter: isValidShadowRootTabbable
                        });
                    } else {
                        candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));
                    }
                    return sortByOrder(candidates);
                };
                var focusable = function focusable(container, options) {
                    options = options || {};
                    var candidates;
                    if (options.getShadowRoot) {
                        candidates = getCandidatesIteratively([container], options.includeContainer, {
                            filter: isNodeMatchingSelectorFocusable.bind(null, options),
                            flatten: true,
                            getShadowRoot: options.getShadowRoot
                        });
                    } else {
                        candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorFocusable.bind(null, options));
                    }
                    return candidates;
                };
                var isTabbable = function isTabbable(node, options) {
                    options = options || {};
                    if (!node) {
                        throw new Error('No node provided');
                    }
                    if (matches.call(node, candidateSelector) === false) {
                        return false;
                    }
                    return isNodeMatchingSelectorTabbable(options, node);
                };
                var focusableCandidateSelector = /* #__PURE__ */ candidateSelectors.concat('iframe').join(',');
                var isFocusable = function isFocusable(node, options) {
                    options = options || {};
                    if (!node) {
                        throw new Error('No node provided');
                    }
                    if (matches.call(node, focusableCandidateSelector) === false) {
                        return false;
                    }
                    return isNodeMatchingSelectorFocusable(options, node);
                };




                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    arrow: () => ( /* binding */ arrow),
                    /* harmony export */
                    autoPlacement: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.autoPlacement),
                    /* harmony export */
                    autoUpdate: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__.autoUpdate),
                    /* harmony export */
                    computePosition: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__.computePosition),
                    /* harmony export */
                    detectOverflow: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.detectOverflow),
                    /* harmony export */
                    flip: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.flip),
                    /* harmony export */
                    getOverflowAncestors: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_2__.getOverflowAncestors),
                    /* harmony export */
                    hide: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.hide),
                    /* harmony export */
                    inline: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.inline),
                    /* harmony export */
                    limitShift: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.limitShift),
                    /* harmony export */
                    offset: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.offset),
                    /* harmony export */
                    platform: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__.platform),
                    /* harmony export */
                    shift: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.shift),
                    /* harmony export */
                    size: () => ( /* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.size),
                    /* harmony export */
                    useFloating: () => ( /* binding */ useFloating)
                    /* harmony export */
                });
                /* harmony import */
                var _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@floating-ui/react-dom/node_modules/@floating-ui/core/dist/floating-ui.core.mjs");
                /* harmony import */
                var _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@floating-ui/react-dom/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs");
                /* harmony import */
                var _floating_ui_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@floating-ui/utils/dom/dist/floating-ui.utils.dom.mjs");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("webpack/sharing/consume/default/react/react?a146");
                /* harmony import */
                var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("webpack/sharing/consume/default/react-dom/react-dom?5573");






                /**
                 * Provides data to position an inner element of the floating element so that it
                 * appears centered to the reference element.
                 * This wraps the core `arrow` middleware to allow React refs as the element.
                 * @see https://floating-ui.com/docs/arrow
                 */
                const arrow = options => {
                    function isRef(value) {
                        return {}.hasOwnProperty.call(value, 'current');
                    }
                    return {
                        name: 'arrow',
                        options,
                        fn(state) {
                            const {
                                element,
                                padding
                            } = typeof options === 'function' ? options(state) : options;
                            if (element && isRef(element)) {
                                if (element.current != null) {
                                    return (0, _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.arrow)({
                                        element: element.current,
                                        padding
                                    }).fn(state);
                                }
                                return {};
                            } else if (element) {
                                return (0, _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.arrow)({
                                    element,
                                    padding
                                }).fn(state);
                            }
                            return {};
                        }
                    };
                };

                var index = typeof document !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_3__.useEffect;

                // Fork of `fast-deep-equal` that only does the comparisons we need and compares
                // functions
                function deepEqual(a, b) {
                    if (a === b) {
                        return true;
                    }
                    if (typeof a !== typeof b) {
                        return false;
                    }
                    if (typeof a === 'function' && a.toString() === b.toString()) {
                        return true;
                    }
                    let length, i, keys;
                    if (a && b && typeof a == 'object') {
                        if (Array.isArray(a)) {
                            length = a.length;
                            if (length != b.length) return false;
                            for (i = length; i-- !== 0;) {
                                if (!deepEqual(a[i], b[i])) {
                                    return false;
                                }
                            }
                            return true;
                        }
                        keys = Object.keys(a);
                        length = keys.length;
                        if (length !== Object.keys(b).length) {
                            return false;
                        }
                        for (i = length; i-- !== 0;) {
                            if (!{}.hasOwnProperty.call(b, keys[i])) {
                                return false;
                            }
                        }
                        for (i = length; i-- !== 0;) {
                            const key = keys[i];
                            if (key === '_owner' && a.$$typeof) {
                                continue;
                            }
                            if (!deepEqual(a[key], b[key])) {
                                return false;
                            }
                        }
                        return true;
                    }
                    return a !== a && b !== b;
                }

                function getDPR(element) {
                    if (typeof window === 'undefined') {
                        return 1;
                    }
                    const win = element.ownerDocument.defaultView || window;
                    return win.devicePixelRatio || 1;
                }

                function roundByDPR(element, value) {
                    const dpr = getDPR(element);
                    return Math.round(value * dpr) / dpr;
                }

                function useLatestRef(value) {
                    const ref = react__WEBPACK_IMPORTED_MODULE_3__.useRef(value);
                    index(() => {
                        ref.current = value;
                    });
                    return ref;
                }

                /**
                 * Provides data to position a floating element.
                 * @see https://floating-ui.com/docs/react
                 */
                function useFloating(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    const {
                        placement = 'bottom',
                            strategy = 'absolute',
                            middleware = [],
                            platform,
                            elements: {
                                reference: externalReference,
                                floating: externalFloating
                            } = {},
                            transform = true,
                            whileElementsMounted,
                            open
                    } = options;
                    const [data, setData] = react__WEBPACK_IMPORTED_MODULE_3__.useState({
                        x: 0,
                        y: 0,
                        strategy,
                        placement,
                        middlewareData: {},
                        isPositioned: false
                    });
                    const [latestMiddleware, setLatestMiddleware] = react__WEBPACK_IMPORTED_MODULE_3__.useState(middleware);
                    if (!deepEqual(latestMiddleware, middleware)) {
                        setLatestMiddleware(middleware);
                    }
                    const [_reference, _setReference] = react__WEBPACK_IMPORTED_MODULE_3__.useState(null);
                    const [_floating, _setFloating] = react__WEBPACK_IMPORTED_MODULE_3__.useState(null);
                    const setReference = react__WEBPACK_IMPORTED_MODULE_3__.useCallback(node => {
                        if (node != referenceRef.current) {
                            referenceRef.current = node;
                            _setReference(node);
                        }
                    }, [_setReference]);
                    const setFloating = react__WEBPACK_IMPORTED_MODULE_3__.useCallback(node => {
                        if (node !== floatingRef.current) {
                            floatingRef.current = node;
                            _setFloating(node);
                        }
                    }, [_setFloating]);
                    const referenceEl = externalReference || _reference;
                    const floatingEl = externalFloating || _floating;
                    const referenceRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(null);
                    const floatingRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(null);
                    const dataRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(data);
                    const whileElementsMountedRef = useLatestRef(whileElementsMounted);
                    const platformRef = useLatestRef(platform);
                    const update = react__WEBPACK_IMPORTED_MODULE_3__.useCallback(() => {
                        if (!referenceRef.current || !floatingRef.current) {
                            return;
                        }
                        const config = {
                            placement,
                            strategy,
                            middleware: latestMiddleware
                        };
                        if (platformRef.current) {
                            config.platform = platformRef.current;
                        }
                        (0, _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__.computePosition)(referenceRef.current, floatingRef.current, config).then(data => {
                            const fullData = {
                                ...data,
                                isPositioned: true
                            };
                            if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {
                                dataRef.current = fullData;
                                react_dom__WEBPACK_IMPORTED_MODULE_4__.flushSync(() => {
                                    setData(fullData);
                                });
                            }
                        });
                    }, [latestMiddleware, placement, strategy, platformRef]);
                    index(() => {
                        if (open === false && dataRef.current.isPositioned) {
                            dataRef.current.isPositioned = false;
                            setData(data => ({
                                ...data,
                                isPositioned: false
                            }));
                        }
                    }, [open]);
                    const isMountedRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(false);
                    index(() => {
                        isMountedRef.current = true;
                        return () => {
                            isMountedRef.current = false;
                        };
                    }, []);
                    index(() => {
                        if (referenceEl) referenceRef.current = referenceEl;
                        if (floatingEl) floatingRef.current = floatingEl;
                        if (referenceEl && floatingEl) {
                            if (whileElementsMountedRef.current) {
                                return whileElementsMountedRef.current(referenceEl, floatingEl, update);
                            } else {
                                update();
                            }
                        }
                    }, [referenceEl, floatingEl, update, whileElementsMountedRef]);
                    const refs = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(() => ({
                        reference: referenceRef,
                        floating: floatingRef,
                        setReference,
                        setFloating
                    }), [setReference, setFloating]);
                    const elements = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(() => ({
                        reference: referenceEl,
                        floating: floatingEl
                    }), [referenceEl, floatingEl]);
                    const floatingStyles = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(() => {
                        const initialStyles = {
                            position: strategy,
                            left: 0,
                            top: 0
                        };
                        if (!elements.floating) {
                            return initialStyles;
                        }
                        const x = roundByDPR(elements.floating, data.x);
                        const y = roundByDPR(elements.floating, data.y);
                        if (transform) {
                            return {
                                ...initialStyles,
                                transform: "translate(" + x + "px, " + y + "px)",
                                ...(getDPR(elements.floating) >= 1.5 && {
                                    willChange: 'transform'
                                })
                            };
                        }
                        return {
                            position: strategy,
                            left: x,
                            top: y
                        };
                    }, [strategy, transform, elements.floating, data.x, data.y]);
                    return react__WEBPACK_IMPORTED_MODULE_3__.useMemo(() => ({
                        ...data,
                        update,
                        refs,
                        elements,
                        floatingStyles
                    }), [data, update, refs, elements, floatingStyles]);
                }




                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/react-dom/node_modules/@floating-ui/core/dist/floating-ui.core.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    arrow: () => ( /* binding */ arrow),
                    /* harmony export */
                    autoPlacement: () => ( /* binding */ autoPlacement),
                    /* harmony export */
                    computePosition: () => ( /* binding */ computePosition),
                    /* harmony export */
                    detectOverflow: () => ( /* binding */ detectOverflow),
                    /* harmony export */
                    flip: () => ( /* binding */ flip),
                    /* harmony export */
                    hide: () => ( /* binding */ hide),
                    /* harmony export */
                    inline: () => ( /* binding */ inline),
                    /* harmony export */
                    limitShift: () => ( /* binding */ limitShift),
                    /* harmony export */
                    offset: () => ( /* binding */ offset),
                    /* harmony export */
                    rectToClientRect: () => ( /* reexport safe */ _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect),
                    /* harmony export */
                    shift: () => ( /* binding */ shift),
                    /* harmony export */
                    size: () => ( /* binding */ size)
                    /* harmony export */
                });
                /* harmony import */
                var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs");



                function computeCoordsFromPlacement(_ref, placement, rtl) {
                    let {
                        reference,
                        floating
                    } = _ref;
                    const sideAxis = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);
                    const alignmentAxis = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);
                    const alignLength = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(alignmentAxis);
                    const side = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);
                    const isVertical = sideAxis === 'y';
                    const commonX = reference.x + reference.width / 2 - floating.width / 2;
                    const commonY = reference.y + reference.height / 2 - floating.height / 2;
                    const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;
                    let coords;
                    switch (side) {
                        case 'top':
                            coords = {
                                x: commonX,
                                y: reference.y - floating.height
                            };
                            break;
                        case 'bottom':
                            coords = {
                                x: commonX,
                                y: reference.y + reference.height
                            };
                            break;
                        case 'right':
                            coords = {
                                x: reference.x + reference.width,
                                y: commonY
                            };
                            break;
                        case 'left':
                            coords = {
                                x: reference.x - floating.width,
                                y: commonY
                            };
                            break;
                        default:
                            coords = {
                                x: reference.x,
                                y: reference.y
                            };
                    }
                    switch ((0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement)) {
                        case 'start':
                            coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);
                            break;
                        case 'end':
                            coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);
                            break;
                    }
                    return coords;
                }

                /**
                 * Computes the `x` and `y` coordinates that will place the floating element
                 * next to a reference element when it is given a certain positioning strategy.
                 *
                 * This export does not have any `platform` interface logic. You will need to
                 * write one for the platform you are using Floating UI with.
                 */
                const computePosition = async (reference, floating, config) => {
                    const {
                        placement = 'bottom',
                            strategy = 'absolute',
                            middleware = [],
                            platform
                    } = config;
                    const validMiddleware = middleware.filter(Boolean);
                    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));
                    let rects = await platform.getElementRects({
                        reference,
                        floating,
                        strategy
                    });
                    let {
                        x,
                        y
                    } = computeCoordsFromPlacement(rects, placement, rtl);
                    let statefulPlacement = placement;
                    let middlewareData = {};
                    let resetCount = 0;
                    for (let i = 0; i < validMiddleware.length; i++) {
                        const {
                            name,
                            fn
                        } = validMiddleware[i];
                        const {
                            x: nextX,
                            y: nextY,
                            data,
                            reset
                        } = await fn({
                            x,
                            y,
                            initialPlacement: placement,
                            placement: statefulPlacement,
                            strategy,
                            middlewareData,
                            rects,
                            platform,
                            elements: {
                                reference,
                                floating
                            }
                        });
                        x = nextX != null ? nextX : x;
                        y = nextY != null ? nextY : y;
                        middlewareData = {
                            ...middlewareData,
                            [name]: {
                                ...middlewareData[name],
                                ...data
                            }
                        };
                        if (reset && resetCount <= 50) {
                            resetCount++;
                            if (typeof reset === 'object') {
                                if (reset.placement) {
                                    statefulPlacement = reset.placement;
                                }
                                if (reset.rects) {
                                    rects = reset.rects === true ? await platform.getElementRects({
                                        reference,
                                        floating,
                                        strategy
                                    }) : reset.rects;
                                }
                                ({
                                    x,
                                    y
                                } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));
                            }
                            i = -1;
                            continue;
                        }
                    }
                    return {
                        x,
                        y,
                        placement: statefulPlacement,
                        strategy,
                        middlewareData
                    };
                };

                /**
                 * Resolves with an object of overflow side offsets that determine how much the
                 * element is overflowing a given clipping boundary on each side.
                 * - positive = overflowing the boundary by that number of pixels
                 * - negative = how many pixels left before it will overflow
                 * - 0 = lies flush with the boundary
                 * @see https://floating-ui.com/docs/detectOverflow
                 */
                async function detectOverflow(state, options) {
                    var _await$platform$isEle;
                    if (options === void 0) {
                        options = {};
                    }
                    const {
                        x,
                        y,
                        platform,
                        rects,
                        elements,
                        strategy
                    } = state;
                    const {
                        boundary = 'clippingAncestors',
                            rootBoundary = 'viewport',
                            elementContext = 'floating',
                            altBoundary = false,
                            padding = 0
                    } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);
                    const paddingObject = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);
                    const altContext = elementContext === 'floating' ? 'reference' : 'floating';
                    const element = elements[altBoundary ? altContext : elementContext];
                    const clippingClientRect = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(await platform.getClippingRect({
                        element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),
                        boundary,
                        rootBoundary,
                        strategy
                    }));
                    const rect = elementContext === 'floating' ? {
                        ...rects.floating,
                        x,
                        y
                    } : rects.reference;
                    const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));
                    const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {
                        x: 1,
                        y: 1
                    } : {
                        x: 1,
                        y: 1
                    };
                    const elementClientRect = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({
                        rect,
                        offsetParent,
                        strategy
                    }) : rect);
                    return {
                        top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,
                        bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,
                        left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,
                        right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x
                    };
                }

                /**
                 * Provides data to position an inner element of the floating element so that it
                 * appears centered to the reference element.
                 * @see https://floating-ui.com/docs/arrow
                 */
                const arrow = options => ({
                    name: 'arrow',
                    options,
                    async fn(state) {
                        const {
                            x,
                            y,
                            placement,
                            rects,
                            platform,
                            elements
                        } = state;
                        // Since `element` is required, we don't Partial<> the type.
                        const {
                            element,
                            padding = 0
                        } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state) || {};
                        if (element == null) {
                            return {};
                        }
                        const paddingObject = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);
                        const coords = {
                            x,
                            y
                        };
                        const axis = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);
                        const length = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(axis);
                        const arrowDimensions = await platform.getDimensions(element);
                        const isYAxis = axis === 'y';
                        const minProp = isYAxis ? 'top' : 'left';
                        const maxProp = isYAxis ? 'bottom' : 'right';
                        const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';
                        const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];
                        const startDiff = coords[axis] - rects.reference[axis];
                        const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));
                        let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;

                        // DOM platform can return `window` as the `offsetParent`.
                        if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {
                            clientSize = elements.floating[clientProp] || rects.floating[length];
                        }
                        const centerToReference = endDiff / 2 - startDiff / 2;

                        // If the padding is large enough that it causes the arrow to no longer be
                        // centered, modify the padding so that it is centered.
                        const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;
                        const minPadding = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[minProp], largestPossiblePadding);
                        const maxPadding = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[maxProp], largestPossiblePadding);

                        // Make sure the arrow doesn't overflow the floating element if the center
                        // point is outside the floating element's bounds.
                        const min$1 = minPadding;
                        const max = clientSize - arrowDimensions[length] - maxPadding;
                        const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;
                        const offset = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min$1, center, max);

                        // If the reference is small enough that the arrow's padding causes it to
                        // to point to nothing for an aligned placement, adjust the offset of the
                        // floating element itself. This stops `shift()` from taking action, but can
                        // be worked around by calling it again after the `arrow()` if desired.
                        const shouldAddOffset = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) != null && center != offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;
                        const alignmentOffset = shouldAddOffset ? center < min$1 ? min$1 - center : max - center : 0;
                        return {
                            [axis]: coords[axis] - alignmentOffset,
                            data: {
                                [axis]: offset,
                                centerOffset: center - offset + alignmentOffset
                            }
                        };
                    }
                });

                function getPlacementList(alignment, autoAlignment, allowedPlacements) {
                    const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment), ...allowedPlacements.filter(placement => (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) !== alignment)] : allowedPlacements.filter(placement => (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === placement);
                    return allowedPlacementsSortedByAlignment.filter(placement => {
                        if (alignment) {
                            return (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment || (autoAlignment ? (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAlignmentPlacement)(placement) !== placement : false);
                        }
                        return true;
                    });
                }
                /**
                 * Optimizes the visibility of the floating element by choosing the placement
                 * that has the most space available automatically, without needing to specify a
                 * preferred placement. Alternative to `flip`.
                 * @see https://floating-ui.com/docs/autoPlacement
                 */
                const autoPlacement = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'autoPlacement',
                        options,
                        async fn(state) {
                            var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;
                            const {
                                rects,
                                middlewareData,
                                placement,
                                platform,
                                elements
                            } = state;
                            const {
                                crossAxis = false,
                                    alignment,
                                    allowedPlacements = _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements,
                                    autoAlignment = true,
                                    ...detectOverflowOptions
                            } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);
                            const placements$1 = alignment !== undefined || allowedPlacements === _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;
                            const overflow = await detectOverflow(state, detectOverflowOptions);
                            const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;
                            const currentPlacement = placements$1[currentIndex];
                            if (currentPlacement == null) {
                                return {};
                            }
                            const alignmentSides = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));

                            // Make `computeCoords` start from the right place.
                            if (placement !== currentPlacement) {
                                return {
                                    reset: {
                                        placement: placements$1[0]
                                    }
                                };
                            }
                            const currentOverflows = [overflow[(0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];
                            const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {
                                placement: currentPlacement,
                                overflows: currentOverflows
                            }];
                            const nextPlacement = placements$1[currentIndex + 1];

                            // There are more placements to check.
                            if (nextPlacement) {
                                return {
                                    data: {
                                        index: currentIndex + 1,
                                        overflows: allOverflows
                                    },
                                    reset: {
                                        placement: nextPlacement
                                    }
                                };
                            }
                            const placementsSortedByMostSpace = allOverflows.map(d => {
                                const alignment = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d.placement);
                                return [d.placement, alignment && crossAxis ?
                                    // Check along the mainAxis and main crossAxis side.
                                    d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :
                                    // Check only the mainAxis.
                                    d.overflows[0], d.overflows
                                ];
                            }).sort((a, b) => a[1] - b[1]);
                            const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,
                                // Aligned placements should not check their opposite crossAxis
                                // side.
                                (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d[0]) ? 2 : 3).every(v => v <= 0));
                            const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];
                            if (resetPlacement !== placement) {
                                return {
                                    data: {
                                        index: currentIndex + 1,
                                        overflows: allOverflows
                                    },
                                    reset: {
                                        placement: resetPlacement
                                    }
                                };
                            }
                            return {};
                        }
                    };
                };

                /**
                 * Optimizes the visibility of the floating element by flipping the `placement`
                 * in order to keep it in view when the preferred placement(s) will overflow the
                 * clipping boundary. Alternative to `autoPlacement`.
                 * @see https://floating-ui.com/docs/flip
                 */
                const flip = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'flip',
                        options,
                        async fn(state) {
                            var _middlewareData$flip;
                            const {
                                placement,
                                middlewareData,
                                rects,
                                initialPlacement,
                                platform,
                                elements
                            } = state;
                            const {
                                mainAxis: checkMainAxis = true,
                                crossAxis: checkCrossAxis = true,
                                fallbackPlacements: specifiedFallbackPlacements,
                                fallbackStrategy = 'bestFit',
                                fallbackAxisSideDirection = 'none',
                                flipAlignment = true,
                                ...detectOverflowOptions
                            } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);
                            const side = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);
                            const isBasePlacement = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(initialPlacement) === initialPlacement;
                            const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));
                            const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [(0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositePlacement)(initialPlacement)] : (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getExpandedPlacements)(initialPlacement));
                            if (!specifiedFallbackPlacements && fallbackAxisSideDirection !== 'none') {
                                fallbackPlacements.push(...(0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxisPlacements)(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));
                            }
                            const placements = [initialPlacement, ...fallbackPlacements];
                            const overflow = await detectOverflow(state, detectOverflowOptions);
                            const overflows = [];
                            let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];
                            if (checkMainAxis) {
                                overflows.push(overflow[side]);
                            }
                            if (checkCrossAxis) {
                                const sides = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(placement, rects, rtl);
                                overflows.push(overflow[sides[0]], overflow[sides[1]]);
                            }
                            overflowsData = [...overflowsData, {
                                placement,
                                overflows
                            }];

                            // One or more sides is overflowing.
                            if (!overflows.every(side => side <= 0)) {
                                var _middlewareData$flip2, _overflowsData$filter;
                                const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;
                                const nextPlacement = placements[nextIndex];
                                if (nextPlacement) {
                                    // Try next placement and re-run the lifecycle.
                                    return {
                                        data: {
                                            index: nextIndex,
                                            overflows: overflowsData
                                        },
                                        reset: {
                                            placement: nextPlacement
                                        }
                                    };
                                }

                                // First, find the candidates that fit on the mainAxis side of overflow,
                                // then find the placement that fits the best on the main crossAxis side.
                                let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;

                                // Otherwise fallback.
                                if (!resetPlacement) {
                                    switch (fallbackStrategy) {
                                        case 'bestFit':
                                            {
                                                var _overflowsData$map$so;
                                                const placement = (_overflowsData$map$so = overflowsData.map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$map$so[0];
                                                if (placement) {
                                                    resetPlacement = placement;
                                                }
                                                break;
                                            }
                                        case 'initialPlacement':
                                            resetPlacement = initialPlacement;
                                            break;
                                    }
                                }
                                if (placement !== resetPlacement) {
                                    return {
                                        reset: {
                                            placement: resetPlacement
                                        }
                                    };
                                }
                            }
                            return {};
                        }
                    };
                };

                function getSideOffsets(overflow, rect) {
                    return {
                        top: overflow.top - rect.height,
                        right: overflow.right - rect.width,
                        bottom: overflow.bottom - rect.height,
                        left: overflow.left - rect.width
                    };
                }

                function isAnySideFullyClipped(overflow) {
                    return _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.sides.some(side => overflow[side] >= 0);
                }
                /**
                 * Provides data to hide the floating element in applicable situations, such as
                 * when it is not in the same clipping context as the reference element.
                 * @see https://floating-ui.com/docs/hide
                 */
                const hide = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'hide',
                        options,
                        async fn(state) {
                            const {
                                rects
                            } = state;
                            const {
                                strategy = 'referenceHidden',
                                    ...detectOverflowOptions
                            } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);
                            switch (strategy) {
                                case 'referenceHidden':
                                    {
                                        const overflow = await detectOverflow(state, {
                                            ...detectOverflowOptions,
                                            elementContext: 'reference'
                                        });
                                        const offsets = getSideOffsets(overflow, rects.reference);
                                        return {
                                            data: {
                                                referenceHiddenOffsets: offsets,
                                                referenceHidden: isAnySideFullyClipped(offsets)
                                            }
                                        };
                                    }
                                case 'escaped':
                                    {
                                        const overflow = await detectOverflow(state, {
                                            ...detectOverflowOptions,
                                            altBoundary: true
                                        });
                                        const offsets = getSideOffsets(overflow, rects.floating);
                                        return {
                                            data: {
                                                escapedOffsets: offsets,
                                                escaped: isAnySideFullyClipped(offsets)
                                            }
                                        };
                                    }
                                default:
                                    {
                                        return {};
                                    }
                            }
                        }
                    };
                };

                function getBoundingRect(rects) {
                    const minX = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map(rect => rect.left));
                    const minY = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map(rect => rect.top));
                    const maxX = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map(rect => rect.right));
                    const maxY = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map(rect => rect.bottom));
                    return {
                        x: minX,
                        y: minY,
                        width: maxX - minX,
                        height: maxY - minY
                    };
                }

                function getRectsByLine(rects) {
                    const sortedRects = rects.slice().sort((a, b) => a.y - b.y);
                    const groups = [];
                    let prevRect = null;
                    for (let i = 0; i < sortedRects.length; i++) {
                        const rect = sortedRects[i];
                        if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {
                            groups.push([rect]);
                        } else {
                            groups[groups.length - 1].push(rect);
                        }
                        prevRect = rect;
                    }
                    return groups.map(rect => (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(rect)));
                }
                /**
                 * Provides improved positioning for inline reference elements that can span
                 * over multiple lines, such as hyperlinks or range selections.
                 * @see https://floating-ui.com/docs/inline
                 */
                const inline = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'inline',
                        options,
                        async fn(state) {
                            const {
                                placement,
                                elements,
                                rects,
                                platform,
                                strategy
                            } = state;
                            // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a
                            // ClientRect's bounds, despite the event listener being triggered. A
                            // padding of 2 seems to handle this issue.
                            const {
                                padding = 2,
                                    x,
                                    y
                            } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);
                            const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);
                            const clientRects = getRectsByLine(nativeClientRects);
                            const fallback = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(nativeClientRects));
                            const paddingObject = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);

                            function getBoundingClientRect() {
                                // There are two rects and they are disjoined.
                                if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {
                                    // Find the first rect in which the point is fully inside.
                                    return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;
                                }

                                // There are 2 or more connected rects.
                                if (clientRects.length >= 2) {
                                    if ((0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y') {
                                        const firstRect = clientRects[0];
                                        const lastRect = clientRects[clientRects.length - 1];
                                        const isTop = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === 'top';
                                        const top = firstRect.top;
                                        const bottom = lastRect.bottom;
                                        const left = isTop ? firstRect.left : lastRect.left;
                                        const right = isTop ? firstRect.right : lastRect.right;
                                        const width = right - left;
                                        const height = bottom - top;
                                        return {
                                            top,
                                            bottom,
                                            left,
                                            right,
                                            width,
                                            height,
                                            x: left,
                                            y: top
                                        };
                                    }
                                    const isLeftSide = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === 'left';
                                    const maxRight = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...clientRects.map(rect => rect.right));
                                    const minLeft = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...clientRects.map(rect => rect.left));
                                    const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);
                                    const top = measureRects[0].top;
                                    const bottom = measureRects[measureRects.length - 1].bottom;
                                    const left = minLeft;
                                    const right = maxRight;
                                    const width = right - left;
                                    const height = bottom - top;
                                    return {
                                        top,
                                        bottom,
                                        left,
                                        right,
                                        width,
                                        height,
                                        x: left,
                                        y: top
                                    };
                                }
                                return fallback;
                            }
                            const resetRects = await platform.getElementRects({
                                reference: {
                                    getBoundingClientRect
                                },
                                floating: elements.floating,
                                strategy
                            });
                            if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {
                                return {
                                    reset: {
                                        rects: resetRects
                                    }
                                };
                            }
                            return {};
                        }
                    };
                };

                // For type backwards-compatibility, the `OffsetOptions` type was also
                // Derivable.
                async function convertValueToCoords(state, options) {
                    const {
                        placement,
                        platform,
                        elements
                    } = state;
                    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));
                    const side = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);
                    const alignment = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);
                    const isVertical = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y';
                    const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;
                    const crossAxisMulti = rtl && isVertical ? -1 : 1;
                    const rawValue = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);

                    // eslint-disable-next-line prefer-const
                    let {
                        mainAxis,
                        crossAxis,
                        alignmentAxis
                    } = typeof rawValue === 'number' ? {
                        mainAxis: rawValue,
                        crossAxis: 0,
                        alignmentAxis: null
                    } : {
                        mainAxis: 0,
                        crossAxis: 0,
                        alignmentAxis: null,
                        ...rawValue
                    };
                    if (alignment && typeof alignmentAxis === 'number') {
                        crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;
                    }
                    return isVertical ? {
                        x: crossAxis * crossAxisMulti,
                        y: mainAxis * mainAxisMulti
                    } : {
                        x: mainAxis * mainAxisMulti,
                        y: crossAxis * crossAxisMulti
                    };
                }

                /**
                 * Modifies the placement by translating the floating element along the
                 * specified axes.
                 * A number (shorthand for `mainAxis` or distance), or an axes configuration
                 * object may be passed.
                 * @see https://floating-ui.com/docs/offset
                 */
                const offset = function(options) {
                    if (options === void 0) {
                        options = 0;
                    }
                    return {
                        name: 'offset',
                        options,
                        async fn(state) {
                            const {
                                x,
                                y
                            } = state;
                            const diffCoords = await convertValueToCoords(state, options);
                            return {
                                x: x + diffCoords.x,
                                y: y + diffCoords.y,
                                data: diffCoords
                            };
                        }
                    };
                };

                /**
                 * Optimizes the visibility of the floating element by shifting it in order to
                 * keep it in view when it will overflow the clipping boundary.
                 * @see https://floating-ui.com/docs/shift
                 */
                const shift = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'shift',
                        options,
                        async fn(state) {
                            const {
                                x,
                                y,
                                placement
                            } = state;
                            const {
                                mainAxis: checkMainAxis = true,
                                crossAxis: checkCrossAxis = false,
                                limiter = {
                                    fn: _ref => {
                                        let {
                                            x,
                                            y
                                        } = _ref;
                                        return {
                                            x,
                                            y
                                        };
                                    }
                                },
                                ...detectOverflowOptions
                            } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);
                            const coords = {
                                x,
                                y
                            };
                            const overflow = await detectOverflow(state, detectOverflowOptions);
                            const crossAxis = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)((0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));
                            const mainAxis = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);
                            let mainAxisCoord = coords[mainAxis];
                            let crossAxisCoord = coords[crossAxis];
                            if (checkMainAxis) {
                                const minSide = mainAxis === 'y' ? 'top' : 'left';
                                const maxSide = mainAxis === 'y' ? 'bottom' : 'right';
                                const min = mainAxisCoord + overflow[minSide];
                                const max = mainAxisCoord - overflow[maxSide];
                                mainAxisCoord = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, mainAxisCoord, max);
                            }
                            if (checkCrossAxis) {
                                const minSide = crossAxis === 'y' ? 'top' : 'left';
                                const maxSide = crossAxis === 'y' ? 'bottom' : 'right';
                                const min = crossAxisCoord + overflow[minSide];
                                const max = crossAxisCoord - overflow[maxSide];
                                crossAxisCoord = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, crossAxisCoord, max);
                            }
                            const limitedCoords = limiter.fn({
                                ...state,
                                [mainAxis]: mainAxisCoord,
                                [crossAxis]: crossAxisCoord
                            });
                            return {
                                ...limitedCoords,
                                data: {
                                    x: limitedCoords.x - x,
                                    y: limitedCoords.y - y
                                }
                            };
                        }
                    };
                };
                /**
                 * Built-in `limiter` that will stop `shift()` at a certain point.
                 */
                const limitShift = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        options,
                        fn(state) {
                            const {
                                x,
                                y,
                                placement,
                                rects,
                                middlewareData
                            } = state;
                            const {
                                offset = 0,
                                    mainAxis: checkMainAxis = true,
                                    crossAxis: checkCrossAxis = true
                            } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);
                            const coords = {
                                x,
                                y
                            };
                            const crossAxis = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);
                            const mainAxis = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);
                            let mainAxisCoord = coords[mainAxis];
                            let crossAxisCoord = coords[crossAxis];
                            const rawOffset = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(offset, state);
                            const computedOffset = typeof rawOffset === 'number' ? {
                                mainAxis: rawOffset,
                                crossAxis: 0
                            } : {
                                mainAxis: 0,
                                crossAxis: 0,
                                ...rawOffset
                            };
                            if (checkMainAxis) {
                                const len = mainAxis === 'y' ? 'height' : 'width';
                                const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;
                                const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;
                                if (mainAxisCoord < limitMin) {
                                    mainAxisCoord = limitMin;
                                } else if (mainAxisCoord > limitMax) {
                                    mainAxisCoord = limitMax;
                                }
                            }
                            if (checkCrossAxis) {
                                var _middlewareData$offse, _middlewareData$offse2;
                                const len = mainAxis === 'y' ? 'width' : 'height';
                                const isOriginSide = ['top', 'left'].includes((0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));
                                const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);
                                const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);
                                if (crossAxisCoord < limitMin) {
                                    crossAxisCoord = limitMin;
                                } else if (crossAxisCoord > limitMax) {
                                    crossAxisCoord = limitMax;
                                }
                            }
                            return {
                                [mainAxis]: mainAxisCoord,
                                [crossAxis]: crossAxisCoord
                            };
                        }
                    };
                };

                /**
                 * Provides data that allows you to change the size of the floating element —
                 * for instance, prevent it from overflowing the clipping boundary or match the
                 * width of the reference element.
                 * @see https://floating-ui.com/docs/size
                 */
                const size = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'size',
                        options,
                        async fn(state) {
                            const {
                                placement,
                                rects,
                                platform,
                                elements
                            } = state;
                            const {
                                apply = () => {},
                                    ...detectOverflowOptions
                            } = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);
                            const overflow = await detectOverflow(state, detectOverflowOptions);
                            const side = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);
                            const alignment = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);
                            const isYAxis = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y';
                            const {
                                width,
                                height
                            } = rects.floating;
                            let heightSide;
                            let widthSide;
                            if (side === 'top' || side === 'bottom') {
                                heightSide = side;
                                widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';
                            } else {
                                widthSide = side;
                                heightSide = alignment === 'end' ? 'top' : 'bottom';
                            }
                            const overflowAvailableHeight = height - overflow[heightSide];
                            const overflowAvailableWidth = width - overflow[widthSide];
                            const noShift = !state.middlewareData.shift;
                            let availableHeight = overflowAvailableHeight;
                            let availableWidth = overflowAvailableWidth;
                            if (isYAxis) {
                                const maximumClippingWidth = width - overflow.left - overflow.right;
                                availableWidth = alignment || noShift ? (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(overflowAvailableWidth, maximumClippingWidth) : maximumClippingWidth;
                            } else {
                                const maximumClippingHeight = height - overflow.top - overflow.bottom;
                                availableHeight = alignment || noShift ? (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(overflowAvailableHeight, maximumClippingHeight) : maximumClippingHeight;
                            }
                            if (noShift && !alignment) {
                                const xMin = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, 0);
                                const xMax = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.right, 0);
                                const yMin = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, 0);
                                const yMax = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.bottom, 0);
                                if (isYAxis) {
                                    availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, overflow.right));
                                } else {
                                    availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, overflow.bottom));
                                }
                            }
                            await apply({
                                ...state,
                                availableWidth,
                                availableHeight
                            });
                            const nextDimensions = await platform.getDimensions(elements.floating);
                            if (width !== nextDimensions.width || height !== nextDimensions.height) {
                                return {
                                    reset: {
                                        rects: true
                                    }
                                };
                            }
                            return {};
                        }
                    };
                };




                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/react-dom/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    arrow: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.arrow),
                    /* harmony export */
                    autoPlacement: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.autoPlacement),
                    /* harmony export */
                    autoUpdate: () => ( /* binding */ autoUpdate),
                    /* harmony export */
                    computePosition: () => ( /* binding */ computePosition),
                    /* harmony export */
                    detectOverflow: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.detectOverflow),
                    /* harmony export */
                    flip: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.flip),
                    /* harmony export */
                    getOverflowAncestors: () => ( /* reexport safe */ _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getOverflowAncestors),
                    /* harmony export */
                    hide: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.hide),
                    /* harmony export */
                    inline: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.inline),
                    /* harmony export */
                    limitShift: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.limitShift),
                    /* harmony export */
                    offset: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.offset),
                    /* harmony export */
                    platform: () => ( /* binding */ platform),
                    /* harmony export */
                    shift: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.shift),
                    /* harmony export */
                    size: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.size)
                    /* harmony export */
                });
                /* harmony import */
                var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs");
                /* harmony import */
                var _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@floating-ui/react-dom/node_modules/@floating-ui/core/dist/floating-ui.core.mjs");
                /* harmony import */
                var _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@floating-ui/utils/dom/dist/floating-ui.utils.dom.mjs");






                function getCssDimensions(element) {
                    const css = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(element);
                    // In testing environments, the `width` and `height` properties are empty
                    // strings for SVG elements, returning NaN. Fallback to `0` in this case.
                    let width = parseFloat(css.width) || 0;
                    let height = parseFloat(css.height) || 0;
                    const hasOffset = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(element);
                    const offsetWidth = hasOffset ? element.offsetWidth : width;
                    const offsetHeight = hasOffset ? element.offsetHeight : height;
                    const shouldFallback = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.round)(width) !== offsetWidth || (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.round)(height) !== offsetHeight;
                    if (shouldFallback) {
                        width = offsetWidth;
                        height = offsetHeight;
                    }
                    return {
                        width,
                        height,
                        $: shouldFallback
                    };
                }

                function unwrapElement(element) {
                    return !(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isElement)(element) ? element.contextElement : element;
                }

                function getScale(element) {
                    const domElement = unwrapElement(element);
                    if (!(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(domElement)) {
                        return (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.createCoords)(1);
                    }
                    const rect = domElement.getBoundingClientRect();
                    const {
                        width,
                        height,
                        $
                    } = getCssDimensions(domElement);
                    let x = ($ ? (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.round)(rect.width) : rect.width) / width;
                    let y = ($ ? (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.round)(rect.height) : rect.height) / height;

                    // 0, NaN, or Infinity should always fallback to 1.

                    if (!x || !Number.isFinite(x)) {
                        x = 1;
                    }
                    if (!y || !Number.isFinite(y)) {
                        y = 1;
                    }
                    return {
                        x,
                        y
                    };
                }

                const noOffsets = /*#__PURE__*/ (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.createCoords)(0);

                function getVisualOffsets(element) {
                    const win = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getWindow)(element);
                    if (!(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isWebKit)() || !win.visualViewport) {
                        return noOffsets;
                    }
                    return {
                        x: win.visualViewport.offsetLeft,
                        y: win.visualViewport.offsetTop
                    };
                }

                function shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {
                    if (isFixed === void 0) {
                        isFixed = false;
                    }
                    if (!floatingOffsetParent || isFixed && floatingOffsetParent !== (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getWindow)(element)) {
                        return false;
                    }
                    return isFixed;
                }

                function getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {
                    if (includeScale === void 0) {
                        includeScale = false;
                    }
                    if (isFixedStrategy === void 0) {
                        isFixedStrategy = false;
                    }
                    const clientRect = element.getBoundingClientRect();
                    const domElement = unwrapElement(element);
                    let scale = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.createCoords)(1);
                    if (includeScale) {
                        if (offsetParent) {
                            if ((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isElement)(offsetParent)) {
                                scale = getScale(offsetParent);
                            }
                        } else {
                            scale = getScale(element);
                        }
                    }
                    const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.createCoords)(0);
                    let x = (clientRect.left + visualOffsets.x) / scale.x;
                    let y = (clientRect.top + visualOffsets.y) / scale.y;
                    let width = clientRect.width / scale.x;
                    let height = clientRect.height / scale.y;
                    if (domElement) {
                        const win = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getWindow)(domElement);
                        const offsetWin = offsetParent && (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isElement)(offsetParent) ? (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getWindow)(offsetParent) : offsetParent;
                        let currentIFrame = win.frameElement;
                        while (currentIFrame && offsetParent && offsetWin !== win) {
                            const iframeScale = getScale(currentIFrame);
                            const iframeRect = currentIFrame.getBoundingClientRect();
                            const css = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(currentIFrame);
                            const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;
                            const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;
                            x *= iframeScale.x;
                            y *= iframeScale.y;
                            width *= iframeScale.x;
                            height *= iframeScale.y;
                            x += left;
                            y += top;
                            currentIFrame = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getWindow)(currentIFrame).frameElement;
                        }
                    }
                    return (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.rectToClientRect)({
                        width,
                        height,
                        x,
                        y
                    });
                }

                function convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {
                    let {
                        rect,
                        offsetParent,
                        strategy
                    } = _ref;
                    const isOffsetParentAnElement = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent);
                    const documentElement = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getDocumentElement)(offsetParent);
                    if (offsetParent === documentElement) {
                        return rect;
                    }
                    let scroll = {
                        scrollLeft: 0,
                        scrollTop: 0
                    };
                    let scale = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.createCoords)(1);
                    const offsets = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.createCoords)(0);
                    if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {
                        if ((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeName)(offsetParent) !== 'body' || (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isOverflowElement)(documentElement)) {
                            scroll = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeScroll)(offsetParent);
                        }
                        if ((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent)) {
                            const offsetRect = getBoundingClientRect(offsetParent);
                            scale = getScale(offsetParent);
                            offsets.x = offsetRect.x + offsetParent.clientLeft;
                            offsets.y = offsetRect.y + offsetParent.clientTop;
                        }
                    }
                    return {
                        width: rect.width * scale.x,
                        height: rect.height * scale.y,
                        x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,
                        y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y
                    };
                }

                function getClientRects(element) {
                    return Array.from(element.getClientRects());
                }

                function getWindowScrollBarX(element) {
                    // If <html> has a CSS width greater than the viewport, then this will be
                    // incorrect for RTL.
                    return getBoundingClientRect((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getDocumentElement)(element)).left + (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeScroll)(element).scrollLeft;
                }

                // Gets the entire size of the scrollable document area, even extending outside
                // of the `<html>` and `<body>` rect bounds if horizontally scrollable.
                function getDocumentRect(element) {
                    const html = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getDocumentElement)(element);
                    const scroll = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeScroll)(element);
                    const body = element.ownerDocument.body;
                    const width = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.max)(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);
                    const height = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.max)(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);
                    let x = -scroll.scrollLeft + getWindowScrollBarX(element);
                    const y = -scroll.scrollTop;
                    if ((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(body).direction === 'rtl') {
                        x += (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.max)(html.clientWidth, body.clientWidth) - width;
                    }
                    return {
                        width,
                        height,
                        x,
                        y
                    };
                }

                function getViewportRect(element, strategy) {
                    const win = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getWindow)(element);
                    const html = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getDocumentElement)(element);
                    const visualViewport = win.visualViewport;
                    let width = html.clientWidth;
                    let height = html.clientHeight;
                    let x = 0;
                    let y = 0;
                    if (visualViewport) {
                        width = visualViewport.width;
                        height = visualViewport.height;
                        const visualViewportBased = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isWebKit)();
                        if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {
                            x = visualViewport.offsetLeft;
                            y = visualViewport.offsetTop;
                        }
                    }
                    return {
                        width,
                        height,
                        x,
                        y
                    };
                }

                // Returns the inner client rect, subtracting scrollbars if present.
                function getInnerBoundingClientRect(element, strategy) {
                    const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');
                    const top = clientRect.top + element.clientTop;
                    const left = clientRect.left + element.clientLeft;
                    const scale = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(element) ? getScale(element) : (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.createCoords)(1);
                    const width = element.clientWidth * scale.x;
                    const height = element.clientHeight * scale.y;
                    const x = left * scale.x;
                    const y = top * scale.y;
                    return {
                        width,
                        height,
                        x,
                        y
                    };
                }

                function getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {
                    let rect;
                    if (clippingAncestor === 'viewport') {
                        rect = getViewportRect(element, strategy);
                    } else if (clippingAncestor === 'document') {
                        rect = getDocumentRect((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getDocumentElement)(element));
                    } else if ((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isElement)(clippingAncestor)) {
                        rect = getInnerBoundingClientRect(clippingAncestor, strategy);
                    } else {
                        const visualOffsets = getVisualOffsets(element);
                        rect = {
                            ...clippingAncestor,
                            x: clippingAncestor.x - visualOffsets.x,
                            y: clippingAncestor.y - visualOffsets.y
                        };
                    }
                    return (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.rectToClientRect)(rect);
                }

                function hasFixedPositionAncestor(element, stopNode) {
                    const parentNode = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getParentNode)(element);
                    if (parentNode === stopNode || !(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isElement)(parentNode) || (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isLastTraversableNode)(parentNode)) {
                        return false;
                    }
                    return (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);
                }

                // A "clipping ancestor" is an `overflow` element with the characteristic of
                // clipping (or hiding) child elements. This returns all clipping ancestors
                // of the given element up the tree.
                function getClippingElementAncestors(element, cache) {
                    const cachedResult = cache.get(element);
                    if (cachedResult) {
                        return cachedResult;
                    }
                    let result = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getOverflowAncestors)(element).filter(el => (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isElement)(el) && (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeName)(el) !== 'body');
                    let currentContainingBlockComputedStyle = null;
                    const elementIsFixed = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(element).position === 'fixed';
                    let currentNode = elementIsFixed ? (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getParentNode)(element) : element;

                    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block
                    while ((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isElement)(currentNode) && !(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isLastTraversableNode)(currentNode)) {
                        const computedStyle = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(currentNode);
                        const currentNodeIsContaining = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isContainingBlock)(currentNode);
                        if (!currentNodeIsContaining && computedStyle.position === 'fixed') {
                            currentContainingBlockComputedStyle = null;
                        }
                        const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isOverflowElement)(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);
                        if (shouldDropCurrentNode) {
                            // Drop non-containing blocks.
                            result = result.filter(ancestor => ancestor !== currentNode);
                        } else {
                            // Record last containing block for next iteration.
                            currentContainingBlockComputedStyle = computedStyle;
                        }
                        currentNode = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getParentNode)(currentNode);
                    }
                    cache.set(element, result);
                    return result;
                }

                // Gets the maximum area that the element is visible in due to any number of
                // clipping ancestors.
                function getClippingRect(_ref) {
                    let {
                        element,
                        boundary,
                        rootBoundary,
                        strategy
                    } = _ref;
                    const elementClippingAncestors = boundary === 'clippingAncestors' ? getClippingElementAncestors(element, this._c) : [].concat(boundary);
                    const clippingAncestors = [...elementClippingAncestors, rootBoundary];
                    const firstClippingAncestor = clippingAncestors[0];
                    const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {
                        const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);
                        accRect.top = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.max)(rect.top, accRect.top);
                        accRect.right = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.min)(rect.right, accRect.right);
                        accRect.bottom = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.min)(rect.bottom, accRect.bottom);
                        accRect.left = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.max)(rect.left, accRect.left);
                        return accRect;
                    }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));
                    return {
                        width: clippingRect.right - clippingRect.left,
                        height: clippingRect.bottom - clippingRect.top,
                        x: clippingRect.left,
                        y: clippingRect.top
                    };
                }

                function getDimensions(element) {
                    return getCssDimensions(element);
                }

                function getRectRelativeToOffsetParent(element, offsetParent, strategy) {
                    const isOffsetParentAnElement = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent);
                    const documentElement = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getDocumentElement)(offsetParent);
                    const isFixed = strategy === 'fixed';
                    const rect = getBoundingClientRect(element, true, isFixed, offsetParent);
                    let scroll = {
                        scrollLeft: 0,
                        scrollTop: 0
                    };
                    const offsets = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.createCoords)(0);
                    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
                        if ((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeName)(offsetParent) !== 'body' || (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isOverflowElement)(documentElement)) {
                            scroll = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeScroll)(offsetParent);
                        }
                        if (isOffsetParentAnElement) {
                            const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);
                            offsets.x = offsetRect.x + offsetParent.clientLeft;
                            offsets.y = offsetRect.y + offsetParent.clientTop;
                        } else if (documentElement) {
                            offsets.x = getWindowScrollBarX(documentElement);
                        }
                    }
                    return {
                        x: rect.left + scroll.scrollLeft - offsets.x,
                        y: rect.top + scroll.scrollTop - offsets.y,
                        width: rect.width,
                        height: rect.height
                    };
                }

                function getTrueOffsetParent(element, polyfill) {
                    if (!(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(element) || (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(element).position === 'fixed') {
                        return null;
                    }
                    if (polyfill) {
                        return polyfill(element);
                    }
                    return element.offsetParent;
                }

                // Gets the closest ancestor positioned element. Handles some edge cases,
                // such as table ancestors and cross browser bugs.
                function getOffsetParent(element, polyfill) {
                    const window = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getWindow)(element);
                    if (!(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(element)) {
                        return window;
                    }
                    let offsetParent = getTrueOffsetParent(element, polyfill);
                    while (offsetParent && (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isTableElement)(offsetParent) && (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(offsetParent).position === 'static') {
                        offsetParent = getTrueOffsetParent(offsetParent, polyfill);
                    }
                    if (offsetParent && ((0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeName)(offsetParent) === 'html' || (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getNodeName)(offsetParent) === 'body' && (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(offsetParent).position === 'static' && !(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isContainingBlock)(offsetParent))) {
                        return window;
                    }
                    return offsetParent || (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getContainingBlock)(element) || window;
                }

                const getElementRects = async function(_ref) {
                    let {
                        reference,
                        floating,
                        strategy
                    } = _ref;
                    const getOffsetParentFn = this.getOffsetParent || getOffsetParent;
                    const getDimensionsFn = this.getDimensions;
                    return {
                        reference: getRectRelativeToOffsetParent(reference, await getOffsetParentFn(floating), strategy),
                        floating: {
                            x: 0,
                            y: 0,
                            ...(await getDimensionsFn(floating))
                        }
                    };
                };

                function isRTL(element) {
                    return (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getComputedStyle)(element).direction === 'rtl';
                }

                const platform = {
                    convertOffsetParentRelativeRectToViewportRelativeRect,
                    getDocumentElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getDocumentElement,
                    getClippingRect,
                    getOffsetParent,
                    getElementRects,
                    getClientRects,
                    getDimensions,
                    getScale,
                    isElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.isElement,
                    isRTL
                };

                // https://samthor.au/2021/observing-dom/
                function observeMove(element, onMove) {
                    let io = null;
                    let timeoutId;
                    const root = (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getDocumentElement)(element);

                    function cleanup() {
                        clearTimeout(timeoutId);
                        io && io.disconnect();
                        io = null;
                    }

                    function refresh(skip, threshold) {
                        if (skip === void 0) {
                            skip = false;
                        }
                        if (threshold === void 0) {
                            threshold = 1;
                        }
                        cleanup();
                        const {
                            left,
                            top,
                            width,
                            height
                        } = element.getBoundingClientRect();
                        if (!skip) {
                            onMove();
                        }
                        if (!width || !height) {
                            return;
                        }
                        const insetTop = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.floor)(top);
                        const insetRight = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.floor)(root.clientWidth - (left + width));
                        const insetBottom = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.floor)(root.clientHeight - (top + height));
                        const insetLeft = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.floor)(left);
                        const rootMargin = -insetTop + "px " + -insetRight + "px " + -insetBottom + "px " + -insetLeft + "px";
                        const options = {
                            rootMargin,
                            threshold: (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.max)(0, (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_2__.min)(1, threshold)) || 1
                        };
                        let isFirstUpdate = true;

                        function handleObserve(entries) {
                            const ratio = entries[0].intersectionRatio;
                            if (ratio !== threshold) {
                                if (!isFirstUpdate) {
                                    return refresh();
                                }
                                if (!ratio) {
                                    timeoutId = setTimeout(() => {
                                        refresh(false, 1e-7);
                                    }, 100);
                                } else {
                                    refresh(false, ratio);
                                }
                            }
                            isFirstUpdate = false;
                        }

                        // Older browsers don't support a `document` as the root and will throw an
                        // error.
                        try {
                            io = new IntersectionObserver(handleObserve, {
                                ...options,
                                // Handle <iframe>s
                                root: root.ownerDocument
                            });
                        } catch (e) {
                            io = new IntersectionObserver(handleObserve, options);
                        }
                        io.observe(element);
                    }
                    refresh(true);
                    return cleanup;
                }

                /**
                 * Automatically updates the position of the floating element when necessary.
                 * Should only be called when the floating element is mounted on the DOM or
                 * visible on the screen.
                 * @returns cleanup function that should be invoked when the floating element is
                 * removed from the DOM or hidden from the screen.
                 * @see https://floating-ui.com/docs/autoUpdate
                 */
                function autoUpdate(reference, floating, update, options) {
                    if (options === void 0) {
                        options = {};
                    }
                    const {
                        ancestorScroll = true,
                            ancestorResize = true,
                            elementResize = typeof ResizeObserver === 'function',
                            layoutShift = typeof IntersectionObserver === 'function',
                            animationFrame = false
                    } = options;
                    const referenceEl = unwrapElement(reference);
                    const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getOverflowAncestors)(referenceEl) : []), ...(0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_1__.getOverflowAncestors)(floating)] : [];
                    ancestors.forEach(ancestor => {
                        ancestorScroll && ancestor.addEventListener('scroll', update, {
                            passive: true
                        });
                        ancestorResize && ancestor.addEventListener('resize', update);
                    });
                    const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;
                    let reobserveFrame = -1;
                    let resizeObserver = null;
                    if (elementResize) {
                        resizeObserver = new ResizeObserver(_ref => {
                            let [firstEntry] = _ref;
                            if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {
                                // Prevent update loops when using the `size` middleware.
                                // https://github.com/floating-ui/floating-ui/issues/1740
                                resizeObserver.unobserve(floating);
                                cancelAnimationFrame(reobserveFrame);
                                reobserveFrame = requestAnimationFrame(() => {
                                    resizeObserver && resizeObserver.observe(floating);
                                });
                            }
                            update();
                        });
                        if (referenceEl && !animationFrame) {
                            resizeObserver.observe(referenceEl);
                        }
                        resizeObserver.observe(floating);
                    }
                    let frameId;
                    let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;
                    if (animationFrame) {
                        frameLoop();
                    }

                    function frameLoop() {
                        const nextRefRect = getBoundingClientRect(reference);
                        if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {
                            update();
                        }
                        prevRefRect = nextRefRect;
                        frameId = requestAnimationFrame(frameLoop);
                    }
                    update();
                    return () => {
                        ancestors.forEach(ancestor => {
                            ancestorScroll && ancestor.removeEventListener('scroll', update);
                            ancestorResize && ancestor.removeEventListener('resize', update);
                        });
                        cleanupIo && cleanupIo();
                        resizeObserver && resizeObserver.disconnect();
                        resizeObserver = null;
                        if (animationFrame) {
                            cancelAnimationFrame(frameId);
                        }
                    };
                }

                /**
                 * Computes the `x` and `y` coordinates that will place the floating element
                 * next to a reference element when it is given a certain CSS positioning
                 * strategy.
                 */
                const computePosition = (reference, floating, options) => {
                    // This caches the expensive `getClippingElementAncestors` function so that
                    // multiple lifecycle resets re-use the same result. It only lives for a
                    // single call. If other functions become expensive, we can add them as well.
                    const cache = new Map();
                    const mergedOptions = {
                        platform,
                        ...options
                    };
                    const platformWithCache = {
                        ...mergedOptions.platform,
                        _c: cache
                    };
                    return (0, _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.computePosition)(reference, floating, {
                        ...mergedOptions,
                        platform: platformWithCache
                    });
                };




                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/react/dist/floating-ui.react.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;
                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    Composite: () => ( /* binding */ Composite),
                    /* harmony export */
                    CompositeItem: () => ( /* binding */ CompositeItem),
                    /* harmony export */
                    FloatingArrow: () => ( /* binding */ FloatingArrow),
                    /* harmony export */
                    FloatingDelayGroup: () => ( /* binding */ FloatingDelayGroup),
                    /* harmony export */
                    FloatingFocusManager: () => ( /* binding */ FloatingFocusManager),
                    /* harmony export */
                    FloatingList: () => ( /* binding */ FloatingList),
                    /* harmony export */
                    FloatingNode: () => ( /* binding */ FloatingNode),
                    /* harmony export */
                    FloatingOverlay: () => ( /* binding */ FloatingOverlay),
                    /* harmony export */
                    FloatingPortal: () => ( /* binding */ FloatingPortal),
                    /* harmony export */
                    FloatingTree: () => ( /* binding */ FloatingTree),
                    /* harmony export */
                    arrow: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_1__.arrow),
                    /* harmony export */
                    autoPlacement: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.autoPlacement),
                    /* harmony export */
                    autoUpdate: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_3__.autoUpdate),
                    /* harmony export */
                    computePosition: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_3__.computePosition),
                    /* harmony export */
                    detectOverflow: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.detectOverflow),
                    /* harmony export */
                    flip: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.flip),
                    /* harmony export */
                    getOverflowAncestors: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.getOverflowAncestors),
                    /* harmony export */
                    hide: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.hide),
                    /* harmony export */
                    inline: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.inline),
                    /* harmony export */
                    inner: () => ( /* binding */ inner),
                    /* harmony export */
                    limitShift: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.limitShift),
                    /* harmony export */
                    offset: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.offset),
                    /* harmony export */
                    platform: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_3__.platform),
                    /* harmony export */
                    safePolygon: () => ( /* binding */ safePolygon),
                    /* harmony export */
                    shift: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.shift),
                    /* harmony export */
                    size: () => ( /* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.size),
                    /* harmony export */
                    useClick: () => ( /* binding */ useClick),
                    /* harmony export */
                    useClientPoint: () => ( /* binding */ useClientPoint),
                    /* harmony export */
                    useDelayGroup: () => ( /* binding */ useDelayGroup),
                    /* harmony export */
                    useDelayGroupContext: () => ( /* binding */ useDelayGroupContext),
                    /* harmony export */
                    useDismiss: () => ( /* binding */ useDismiss),
                    /* harmony export */
                    useFloating: () => ( /* binding */ useFloating),
                    /* harmony export */
                    useFloatingNodeId: () => ( /* binding */ useFloatingNodeId),
                    /* harmony export */
                    useFloatingParentNodeId: () => ( /* binding */ useFloatingParentNodeId),
                    /* harmony export */
                    useFloatingPortalNode: () => ( /* binding */ useFloatingPortalNode),
                    /* harmony export */
                    useFloatingTree: () => ( /* binding */ useFloatingTree),
                    /* harmony export */
                    useFocus: () => ( /* binding */ useFocus),
                    /* harmony export */
                    useHover: () => ( /* binding */ useHover),
                    /* harmony export */
                    useId: () => ( /* binding */ useId),
                    /* harmony export */
                    useInnerOffset: () => ( /* binding */ useInnerOffset),
                    /* harmony export */
                    useInteractions: () => ( /* binding */ useInteractions),
                    /* harmony export */
                    useListItem: () => ( /* binding */ useListItem),
                    /* harmony export */
                    useListNavigation: () => ( /* binding */ useListNavigation),
                    /* harmony export */
                    useMergeRefs: () => ( /* binding */ useMergeRefs),
                    /* harmony export */
                    useRole: () => ( /* binding */ useRole),
                    /* harmony export */
                    useTransitionStatus: () => ( /* binding */ useTransitionStatus),
                    /* harmony export */
                    useTransitionStyles: () => ( /* binding */ useTransitionStyles),
                    /* harmony export */
                    useTypeahead: () => ( /* binding */ useTypeahead)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?a146");
                /* harmony import */
                var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__("../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs");
                /* harmony import */
                var _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__("../../node_modules/@floating-ui/utils/react/dist/floating-ui.utils.react.mjs");
                /* harmony import */
                var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@floating-ui/react-dom/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs");
                /* harmony import */
                var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/@floating-ui/utils/dom/dist/floating-ui.utils.dom.mjs");
                /* harmony import */
                var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs");
                /* harmony import */
                var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@floating-ui/react-dom/node_modules/@floating-ui/core/dist/floating-ui.core.mjs");
                /* harmony import */
                var tabbable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__("../../node_modules/tabbable/dist/index.esm.js");
                /* harmony import */
                var react_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("webpack/sharing/consume/default/react-dom/react-dom?5573");










                /**
                 * Merges an array of refs into a single memoized callback ref or `null`.
                 * @see https://floating-ui.com/docs/useMergeRefs
                 */
                function useMergeRefs(refs) {
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (refs.every(ref => ref == null)) {
                            return null;
                        }
                        return value => {
                            refs.forEach(ref => {
                                if (typeof ref === 'function') {
                                    ref(value);
                                } else if (ref != null) {
                                    ref.current = value;
                                }
                            });
                        };
                        // eslint-disable-next-line react-hooks/exhaustive-deps
                    }, refs);
                }

                const ARROW_UP = 'ArrowUp';
                const ARROW_DOWN = 'ArrowDown';
                const ARROW_LEFT = 'ArrowLeft';
                const ARROW_RIGHT = 'ArrowRight';

                function isDifferentRow(index, cols, prevRow) {
                    return Math.floor(index / cols) !== prevRow;
                }

                function isIndexOutOfBounds(listRef, index) {
                    return index < 0 || index >= listRef.current.length;
                }

                function getMinIndex(listRef, disabledIndices) {
                    return findNonDisabledIndex(listRef, {
                        disabledIndices
                    });
                }

                function getMaxIndex(listRef, disabledIndices) {
                    return findNonDisabledIndex(listRef, {
                        decrement: true,
                        startingIndex: listRef.current.length,
                        disabledIndices
                    });
                }

                function findNonDisabledIndex(listRef, _temp) {
                    let {
                        startingIndex = -1,
                            decrement = false,
                            disabledIndices,
                            amount = 1
                    } = _temp === void 0 ? {} : _temp;
                    const list = listRef.current;
                    let index = startingIndex;
                    do {
                        var _list$index, _list$index2;
                        index = index + (decrement ? -amount : amount);
                    } while (index >= 0 && index <= list.length - 1 && (disabledIndices ? disabledIndices.includes(index) : list[index] == null || ((_list$index = list[index]) == null ? void 0 : _list$index.hasAttribute('disabled')) || ((_list$index2 = list[index]) == null ? void 0 : _list$index2.getAttribute('aria-disabled')) === 'true'));
                    return index;
                }

                function getGridNavigatedIndex(elementsRef, _ref) {
                    let {
                        event,
                        orientation,
                        loop,
                        cols,
                        disabledIndices,
                        minIndex,
                        maxIndex,
                        prevIndex,
                        stopEvent: stop = false
                    } = _ref;
                    let nextIndex = prevIndex;
                    if (event.key === ARROW_UP) {
                        stop && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                        if (prevIndex === -1) {
                            nextIndex = maxIndex;
                        } else {
                            nextIndex = findNonDisabledIndex(elementsRef, {
                                startingIndex: nextIndex,
                                amount: cols,
                                decrement: true,
                                disabledIndices
                            });
                            if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {
                                const col = prevIndex % cols;
                                const maxCol = maxIndex % cols;
                                const offset = maxIndex - (maxCol - col);
                                if (maxCol === col) {
                                    nextIndex = maxIndex;
                                } else {
                                    nextIndex = maxCol > col ? offset : offset - cols;
                                }
                            }
                        }
                        if (isIndexOutOfBounds(elementsRef, nextIndex)) {
                            nextIndex = prevIndex;
                        }
                    }
                    if (event.key === ARROW_DOWN) {
                        stop && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                        if (prevIndex === -1) {
                            nextIndex = minIndex;
                        } else {
                            nextIndex = findNonDisabledIndex(elementsRef, {
                                startingIndex: prevIndex,
                                amount: cols,
                                disabledIndices
                            });
                            if (loop && prevIndex + cols > maxIndex) {
                                nextIndex = findNonDisabledIndex(elementsRef, {
                                    startingIndex: prevIndex % cols - cols,
                                    amount: cols,
                                    disabledIndices
                                });
                            }
                        }
                        if (isIndexOutOfBounds(elementsRef, nextIndex)) {
                            nextIndex = prevIndex;
                        }
                    }

                    // Remains on the same row/column.
                    if (orientation === 'both') {
                        const prevRow = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_7__.floor)(prevIndex / cols);
                        if (event.key === ARROW_RIGHT) {
                            stop && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                            if (prevIndex % cols !== cols - 1) {
                                nextIndex = findNonDisabledIndex(elementsRef, {
                                    startingIndex: prevIndex,
                                    disabledIndices
                                });
                                if (loop && isDifferentRow(nextIndex, cols, prevRow)) {
                                    nextIndex = findNonDisabledIndex(elementsRef, {
                                        startingIndex: prevIndex - prevIndex % cols - 1,
                                        disabledIndices
                                    });
                                }
                            } else if (loop) {
                                nextIndex = findNonDisabledIndex(elementsRef, {
                                    startingIndex: prevIndex - prevIndex % cols - 1,
                                    disabledIndices
                                });
                            }
                            if (isDifferentRow(nextIndex, cols, prevRow)) {
                                nextIndex = prevIndex;
                            }
                        }
                        if (event.key === ARROW_LEFT) {
                            stop && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                            if (prevIndex % cols !== 0) {
                                nextIndex = findNonDisabledIndex(elementsRef, {
                                    startingIndex: prevIndex,
                                    disabledIndices,
                                    decrement: true
                                });
                                if (loop && isDifferentRow(nextIndex, cols, prevRow)) {
                                    nextIndex = findNonDisabledIndex(elementsRef, {
                                        startingIndex: prevIndex + (cols - prevIndex % cols),
                                        decrement: true,
                                        disabledIndices
                                    });
                                }
                            } else if (loop) {
                                nextIndex = findNonDisabledIndex(elementsRef, {
                                    startingIndex: prevIndex + (cols - prevIndex % cols),
                                    decrement: true,
                                    disabledIndices
                                });
                            }
                            if (isDifferentRow(nextIndex, cols, prevRow)) {
                                nextIndex = prevIndex;
                            }
                        }
                        const lastRow = (0, _floating_ui_utils__WEBPACK_IMPORTED_MODULE_7__.floor)(maxIndex / cols) === prevRow;
                        if (isIndexOutOfBounds(elementsRef, nextIndex)) {
                            if (loop && lastRow) {
                                nextIndex = event.key === ARROW_LEFT ? maxIndex : findNonDisabledIndex(elementsRef, {
                                    startingIndex: prevIndex - prevIndex % cols - 1,
                                    disabledIndices
                                });
                            } else {
                                nextIndex = prevIndex;
                            }
                        }
                    }
                    return nextIndex;
                }

                let rafId = 0;

                function enqueueFocus(el, options) {
                    if (options === void 0) {
                        options = {};
                    }
                    const {
                        preventScroll = false,
                            cancelPrevious = true,
                            sync = false
                    } = options;
                    cancelPrevious && cancelAnimationFrame(rafId);
                    const exec = () => el == null ? void 0 : el.focus({
                        preventScroll
                    });
                    if (sync) {
                        exec();
                    } else {
                        rafId = requestAnimationFrame(exec);
                    }
                }

                var index = typeof document !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;

                function sortByDocumentPosition(a, b) {
                    const position = a.compareDocumentPosition(b);
                    if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {
                        return -1;
                    }
                    if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {
                        return 1;
                    }
                    return 0;
                }

                function areMapsEqual(map1, map2) {
                    if (map1.size !== map2.size) {
                        return false;
                    }
                    for (const [key, value] of map1.entries()) {
                        if (value !== map2.get(key)) {
                            return false;
                        }
                    }
                    return true;
                }
                const FloatingListContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({
                    register: () => {},
                    unregister: () => {},
                    map: /*#__PURE__*/ new Map(),
                    elementsRef: {
                        current: []
                    }
                });
                /**
                 * Provides context for a list of items within the floating element.
                 * @see https://floating-ui.com/docs/FloatingList
                 */
                function FloatingList(_ref) {
                    let {
                        children,
                        elementsRef,
                        labelsRef
                    } = _ref;
                    const [map, setMap] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new Map());
                    const register = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {
                        setMap(prevMap => new Map(prevMap).set(node, null));
                    }, []);
                    const unregister = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {
                        setMap(prevMap => {
                            const map = new Map(prevMap);
                            map.delete(node);
                            return map;
                        });
                    }, []);
                    index(() => {
                        const newMap = new Map(map);
                        const nodes = Array.from(newMap.keys()).sort(sortByDocumentPosition);
                        nodes.forEach((node, index) => {
                            newMap.set(node, index);
                        });
                        if (!areMapsEqual(map, newMap)) {
                            setMap(newMap);
                        }
                    }, [map]);
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FloatingListContext.Provider, {
                        value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                            register,
                            unregister,
                            map,
                            elementsRef,
                            labelsRef
                        }), [register, unregister, map, elementsRef, labelsRef])
                    }, children);
                }

                function useListItem(_temp) {
                    let {
                        label
                    } = _temp === void 0 ? {} : _temp;
                    const [index$1, setIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);
                    const componentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const {
                        register,
                        unregister,
                        map,
                        elementsRef,
                        labelsRef
                    } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(FloatingListContext);
                    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {
                        componentRef.current = node;
                        if (index$1 !== null) {
                            elementsRef.current[index$1] = node;
                            if (labelsRef) {
                                var _node$textContent;
                                const isLabelDefined = label !== undefined;
                                labelsRef.current[index$1] = isLabelDefined ? label : (_node$textContent = node == null ? void 0 : node.textContent) != null ? _node$textContent : null;
                            }
                        }
                    }, [index$1, elementsRef, labelsRef, label]);
                    index(() => {
                        const node = componentRef.current;
                        if (node) {
                            register(node);
                            return () => {
                                unregister(node);
                            };
                        }
                    }, [register, unregister]);
                    index(() => {
                        const index = componentRef.current ? map.get(componentRef.current) : null;
                        if (index != null) {
                            setIndex(index);
                        }
                    }, [map]);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        ref,
                        index: index$1 == null ? -1 : index$1
                    }), [index$1, ref]);
                }

                function renderJsx(render, computedProps) {
                    if (typeof render === 'function') {
                        return render(computedProps);
                    } else if (render) {
                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(render, computedProps);
                    }
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", computedProps);
                }
                const CompositeContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({
                    activeIndex: 0,
                    setActiveIndex: () => {}
                });
                const horizontalKeys = [ARROW_LEFT, ARROW_RIGHT];
                const verticalKeys = [ARROW_UP, ARROW_DOWN];
                const allKeys = [...horizontalKeys, ...verticalKeys];
                const Composite = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Composite(_ref, forwardedRef) {
                    let {
                        render,
                        orientation = 'both',
                        loop = true,
                        cols = 1,
                        disabledIndices,
                        ...props
                    } = _ref;
                    const [activeIndex, setActiveIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);
                    const elementsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);
                    const renderElementProps = render && typeof render !== 'function' ? render.props : {};
                    const contextValue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        activeIndex,
                        setActiveIndex
                    }), [activeIndex]);
                    const isGrid = cols > 1;

                    function handleKeyDown(event) {
                        if (!allKeys.includes(event.key)) return;
                        const minIndex = getMinIndex(elementsRef, disabledIndices);
                        const maxIndex = getMaxIndex(elementsRef, disabledIndices);
                        const prevIndex = activeIndex;
                        let nextIndex = activeIndex;
                        if (isGrid) {
                            nextIndex = getGridNavigatedIndex(elementsRef, {
                                event,
                                orientation,
                                loop,
                                cols,
                                disabledIndices,
                                minIndex,
                                maxIndex,
                                prevIndex
                            });
                        }
                        const toEndKeys = {
                            horizontal: [ARROW_RIGHT],
                            vertical: [ARROW_DOWN],
                            both: [ARROW_RIGHT, ARROW_DOWN]
                        }[orientation];
                        const toStartKeys = {
                            horizontal: [ARROW_LEFT],
                            vertical: [ARROW_UP],
                            both: [ARROW_LEFT, ARROW_UP]
                        }[orientation];
                        const preventedKeys = isGrid ? allKeys : {
                            horizontal: horizontalKeys,
                            vertical: verticalKeys,
                            both: allKeys
                        }[orientation];
                        if (nextIndex === activeIndex && [...toEndKeys, ...toStartKeys].includes(event.key)) {
                            if (loop && nextIndex === maxIndex && toEndKeys.includes(event.key)) {
                                nextIndex = minIndex;
                            } else if (loop && nextIndex === minIndex && toStartKeys.includes(event.key)) {
                                nextIndex = maxIndex;
                            } else {
                                nextIndex = findNonDisabledIndex(elementsRef, {
                                    startingIndex: nextIndex,
                                    decrement: toStartKeys.includes(event.key),
                                    disabledIndices
                                });
                            }
                        }
                        if (nextIndex !== activeIndex && !isIndexOutOfBounds(elementsRef, nextIndex)) {
                            event.stopPropagation();
                            if (preventedKeys.includes(event.key)) {
                                event.preventDefault();
                            }
                            setActiveIndex(nextIndex);

                            // Wait for FocusManager `returnFocus` to execute.
                            queueMicrotask(() => {
                                enqueueFocus(elementsRef.current[nextIndex]);
                            });
                        }
                    }
                    const computedProps = {
                        ...props,
                        ...renderElementProps,
                        ref: forwardedRef,
                        'aria-orientation': orientation === 'both' ? undefined : orientation,
                        onKeyDown(e) {
                            props.onKeyDown == null ? void 0 : props.onKeyDown(e);
                            renderElementProps.onKeyDown == null ? void 0 : renderElementProps.onKeyDown(e);
                            handleKeyDown(e);
                        }
                    };
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CompositeContext.Provider, {
                        value: contextValue
                    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FloatingList, {
                        elementsRef: elementsRef
                    }, renderJsx(render, computedProps)));
                });
                const CompositeItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function CompositeItem(_ref2, forwardedRef) {
                    let {
                        render,
                        ...props
                    } = _ref2;
                    const renderElementProps = render && typeof render !== 'function' ? render.props : {};
                    const {
                        activeIndex,
                        setActiveIndex
                    } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CompositeContext);
                    const {
                        ref,
                        index
                    } = useListItem();
                    const mergedRef = useMergeRefs([ref, forwardedRef, renderElementProps.ref]);
                    const isActive = activeIndex === index;
                    const computedProps = {
                        ...props,
                        ...renderElementProps,
                        ref: mergedRef,
                        tabIndex: isActive ? 0 : -1,
                        'data-active': isActive ? '' : undefined,
                        onFocus(e) {
                            props.onFocus == null ? void 0 : props.onFocus(e);
                            renderElementProps.onFocus == null ? void 0 : renderElementProps.onFocus(e);
                            setActiveIndex(index);
                        }
                    };
                    return renderJsx(render, computedProps);
                });

                function _extends() {
                    _extends = Object.assign ? Object.assign.bind() : function(target) {
                        for (var i = 1; i < arguments.length; i++) {
                            var source = arguments[i];
                            for (var key in source) {
                                if (Object.prototype.hasOwnProperty.call(source, key)) {
                                    target[key] = source[key];
                                }
                            }
                        }
                        return target;
                    };
                    return _extends.apply(this, arguments);
                }

                let serverHandoffComplete = false;
                let count = 0;
                const genId = () => "floating-ui-" + count++;

                function useFloatingId() {
                    const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => serverHandoffComplete ? genId() : undefined);
                    index(() => {
                        if (id == null) {
                            setId(genId());
                        }
                        // eslint-disable-next-line react-hooks/exhaustive-deps
                    }, []);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!serverHandoffComplete) {
                            serverHandoffComplete = true;
                        }
                    }, []);
                    return id;
                }

                // `toString()` prevents bundlers from trying to `import { useId } from 'react'`
                const useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[ /*#__PURE__*/ 'useId'.toString()];

                /**
                 * Uses React 18's built-in `useId()` when available, or falls back to a
                 * slightly less performant (requiring a double render) implementation for
                 * earlier React versions.
                 * @see https://floating-ui.com/docs/useId
                 */
                const useId = useReactId || useFloatingId;

                /**
                 * Renders a pointing arrow triangle.
                 * @see https://floating-ui.com/docs/FloatingArrow
                 */
                const FloatingArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function FloatingArrow(_ref, ref) {
                    let {
                        context: {
                            placement,
                            elements: {
                                floating
                            },
                            middlewareData: {
                                arrow
                            }
                        },
                        width = 14,
                        height = 7,
                        tipRadius = 0,
                        strokeWidth = 0,
                        staticOffset,
                        stroke,
                        d,
                        style: {
                            transform,
                            ...restStyle
                        } = {},
                        ...rest
                    } = _ref;
                    if (true) {
                        if (!ref) {
                            console.warn('Floating UI: The `ref` prop is required for the `FloatingArrow`', 'component.');
                        }
                    }
                    const clipPathId = useId();
                    if (!floating) {
                        return null;
                    }

                    // Strokes must be double the border width, this ensures the stroke's width
                    // works as you'd expect.
                    strokeWidth *= 2;
                    const halfStrokeWidth = strokeWidth / 2;
                    const svgX = width / 2 * (tipRadius / -8 + 1);
                    const svgY = height / 2 * tipRadius / 4;
                    const [side, alignment] = placement.split('-');
                    const isRTL = _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_3__.platform.isRTL(floating);
                    const isCustomShape = !!d;
                    const isVerticalSide = side === 'top' || side === 'bottom';
                    const yOffsetProp = staticOffset && alignment === 'end' ? 'bottom' : 'top';
                    let xOffsetProp = staticOffset && alignment === 'end' ? 'right' : 'left';
                    if (staticOffset && isRTL) {
                        xOffsetProp = alignment === 'end' ? 'left' : 'right';
                    }
                    const arrowX = (arrow == null ? void 0 : arrow.x) != null ? staticOffset || arrow.x : '';
                    const arrowY = (arrow == null ? void 0 : arrow.y) != null ? staticOffset || arrow.y : '';
                    const dValue = d || 'M0,0' + (" H" + width) + (" L" + (width - svgX) + "," + (height - svgY)) + (" Q" + width / 2 + "," + height + " " + svgX + "," + (height - svgY)) + ' Z';
                    const rotation = {
                        top: isCustomShape ? 'rotate(180deg)' : '',
                        left: isCustomShape ? 'rotate(90deg)' : 'rotate(-90deg)',
                        bottom: isCustomShape ? '' : 'rotate(180deg)',
                        right: isCustomShape ? 'rotate(-90deg)' : 'rotate(90deg)'
                    }[side];
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("svg", _extends({}, rest, {
                        "aria-hidden": true,
                        ref: ref,
                        width: isCustomShape ? width : width + strokeWidth,
                        height: width,
                        viewBox: "0 0 " + width + " " + (height > width ? height : width),
                        style: {
                            position: 'absolute',
                            pointerEvents: 'none',
                            [xOffsetProp]: arrowX,
                            [yOffsetProp]: arrowY,
                            [side]: isVerticalSide || isCustomShape ? '100%' : "calc(100% - " + strokeWidth / 2 + "px)",
                            transform: "" + rotation + (transform != null ? transform : ''),
                            ...restStyle
                        }
                    }), strokeWidth > 0 && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("path", {
                        clipPath: "url(#" + clipPathId + ")",
                        fill: "none",
                        stroke: stroke
                            // Account for the stroke on the fill path rendered below.
                            ,
                        strokeWidth: strokeWidth + (d ? 0 : 1),
                        d: dValue
                    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("path", {
                        stroke: strokeWidth && !d ? rest.fill : 'none',
                        d: dValue
                    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("clipPath", {
                        id: clipPathId
                    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("rect", {
                        x: -halfStrokeWidth,
                        y: halfStrokeWidth * (isCustomShape ? -1 : 1),
                        width: width + strokeWidth,
                        height: width
                    })));
                });

                function createPubSub() {
                    const map = new Map();
                    return {
                        emit(event, data) {
                            var _map$get;
                            (_map$get = map.get(event)) == null ? void 0 : _map$get.forEach(handler => handler(data));
                        },
                        on(event, listener) {
                            map.set(event, [...(map.get(event) || []), listener]);
                        },
                        off(event, listener) {
                            var _map$get2;
                            map.set(event, ((_map$get2 = map.get(event)) == null ? void 0 : _map$get2.filter(l => l !== listener)) || []);
                        }
                    };
                }

                const FloatingNodeContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);
                const FloatingTreeContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);
                const useFloatingParentNodeId = () => {
                    var _React$useContext;
                    return ((_React$useContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(FloatingNodeContext)) == null ? void 0 : _React$useContext.id) || null;
                };
                const useFloatingTree = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(FloatingTreeContext);

                /**
                 * Registers a node into the floating tree, returning its id.
                 */
                function useFloatingNodeId(customParentId) {
                    const id = useId();
                    const tree = useFloatingTree();
                    const reactParentId = useFloatingParentNodeId();
                    const parentId = customParentId || reactParentId;
                    index(() => {
                        const node = {
                            id,
                            parentId
                        };
                        tree == null ? void 0 : tree.addNode(node);
                        return () => {
                            tree == null ? void 0 : tree.removeNode(node);
                        };
                    }, [tree, id, parentId]);
                    return id;
                }

                /**
                 * Provides parent node context for nested floating elements.
                 * @see https://floating-ui.com/docs/FloatingTree
                 */
                function FloatingNode(_ref) {
                    let {
                        children,
                        id
                    } = _ref;
                    const parentId = useFloatingParentNodeId();
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FloatingNodeContext.Provider, {
                        value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                            id,
                            parentId
                        }), [id, parentId])
                    }, children);
                }

                /**
                 * Provides context for nested floating elements when they are not children of
                 * each other on the DOM (i.e. portalled to a common node, rather than their
                 * respective parent).
                 * @see https://floating-ui.com/docs/FloatingTree
                 */
                function FloatingTree(_ref2) {
                    let {
                        children
                    } = _ref2;
                    const nodesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);
                    const addNode = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {
                        nodesRef.current = [...nodesRef.current, node];
                    }, []);
                    const removeNode = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {
                        nodesRef.current = nodesRef.current.filter(n => n !== node);
                    }, []);
                    const events = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => createPubSub())[0];
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FloatingTreeContext.Provider, {
                        value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                            nodesRef,
                            addNode,
                            removeNode,
                            events
                        }), [nodesRef, addNode, removeNode, events])
                    }, children);
                }

                function createAttribute(name) {
                    return "data-floating-ui-" + name;
                }

                function useLatestRef(value) {
                    const ref = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);
                    index(() => {
                        ref.current = value;
                    });
                    return ref;
                }

                const safePolygonIdentifier = /*#__PURE__*/ createAttribute('safe-polygon');

                function getDelay(value, prop, pointerType) {
                    if (pointerType && !(0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isMouseLikePointerType)(pointerType)) {
                        return 0;
                    }
                    if (typeof value === 'number') {
                        return value;
                    }
                    return value == null ? void 0 : value[prop];
                }
                /**
                 * Opens the floating element while hovering over the reference element, like
                 * CSS `:hover`.
                 * @see https://floating-ui.com/docs/useHover
                 */
                function useHover(context, props) {
                    if (props === void 0) {
                        props = {};
                    }
                    const {
                        open,
                        onOpenChange,
                        dataRef,
                        events,
                        elements: {
                            domReference,
                            floating
                        },
                        refs
                    } = context;
                    const {
                        enabled = true,
                            delay = 0,
                            handleClose = null,
                            mouseOnly = false,
                            restMs = 0,
                            move = true
                    } = props;
                    const tree = useFloatingTree();
                    const parentId = useFloatingParentNodeId();
                    const handleCloseRef = useLatestRef(handleClose);
                    const delayRef = useLatestRef(delay);
                    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();
                    const timeoutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();
                    const handlerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();
                    const restTimeoutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();
                    const blockMouseMoveRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);
                    const performedPointerEventsMutationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const unbindMouseMoveRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {});
                    const isHoverOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {
                        var _dataRef$current$open;
                        const type = (_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type;
                        return (type == null ? void 0 : type.includes('mouse')) && type !== 'mousedown';
                    }, [dataRef]);

                    // When dismissing before opening, clear the delay timeouts to cancel it
                    // from showing.
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!enabled) {
                            return;
                        }

                        function onDismiss() {
                            clearTimeout(timeoutRef.current);
                            clearTimeout(restTimeoutRef.current);
                            blockMouseMoveRef.current = true;
                        }
                        events.on('dismiss', onDismiss);
                        return () => {
                            events.off('dismiss', onDismiss);
                        };
                    }, [enabled, events]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!enabled || !handleCloseRef.current || !open) {
                            return;
                        }

                        function onLeave(event) {
                            if (isHoverOpen()) {
                                onOpenChange(false, event);
                            }
                        }
                        const html = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating).documentElement;
                        html.addEventListener('mouseleave', onLeave);
                        return () => {
                            html.removeEventListener('mouseleave', onLeave);
                        };
                    }, [floating, open, onOpenChange, enabled, handleCloseRef, dataRef, isHoverOpen]);
                    const closeWithDelay = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(event, runElseBranch) {
                        if (runElseBranch === void 0) {
                            runElseBranch = true;
                        }
                        const closeDelay = getDelay(delayRef.current, 'close', pointerTypeRef.current);
                        if (closeDelay && !handlerRef.current) {
                            clearTimeout(timeoutRef.current);
                            timeoutRef.current = setTimeout(() => onOpenChange(false, event), closeDelay);
                        } else if (runElseBranch) {
                            clearTimeout(timeoutRef.current);
                            onOpenChange(false, event);
                        }
                    }, [delayRef, onOpenChange]);
                    const cleanupMouseMoveHandler = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {
                        unbindMouseMoveRef.current();
                        handlerRef.current = undefined;
                    }, []);
                    const clearPointerEvents = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {
                        if (performedPointerEventsMutationRef.current) {
                            const body = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(refs.floating.current).body;
                            body.style.pointerEvents = '';
                            body.removeAttribute(safePolygonIdentifier);
                            performedPointerEventsMutationRef.current = false;
                        }
                    }, [refs]);

                    // Registering the mouse events on the reference directly to bypass React's
                    // delegation system. If the cursor was on a disabled element and then entered
                    // the reference (no gap), `mouseenter` doesn't fire in the delegation system.
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!enabled) {
                            return;
                        }

                        function isClickLikeOpenEvent() {
                            return dataRef.current.openEvent ? ['click', 'mousedown'].includes(dataRef.current.openEvent.type) : false;
                        }

                        function onMouseEnter(event) {
                            clearTimeout(timeoutRef.current);
                            blockMouseMoveRef.current = false;
                            if (mouseOnly && !(0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isMouseLikePointerType)(pointerTypeRef.current) || restMs > 0 && getDelay(delayRef.current, 'open') === 0) {
                                return;
                            }
                            const openDelay = getDelay(delayRef.current, 'open', pointerTypeRef.current);
                            if (openDelay) {
                                timeoutRef.current = setTimeout(() => {
                                    onOpenChange(true, event);
                                }, openDelay);
                            } else {
                                onOpenChange(true, event);
                            }
                        }

                        function onMouseLeave(event) {
                            if (isClickLikeOpenEvent()) {
                                return;
                            }
                            unbindMouseMoveRef.current();
                            const doc = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating);
                            clearTimeout(restTimeoutRef.current);
                            if (handleCloseRef.current) {
                                // Prevent clearing `onScrollMouseLeave` timeout.
                                if (!open) {
                                    clearTimeout(timeoutRef.current);
                                }
                                handlerRef.current = handleCloseRef.current({
                                    ...context,
                                    tree,
                                    x: event.clientX,
                                    y: event.clientY,
                                    onClose() {
                                        clearPointerEvents();
                                        cleanupMouseMoveHandler();
                                        // Should the event expose that it was closed by `safePolygon`?
                                        closeWithDelay(event);
                                    }
                                });
                                const handler = handlerRef.current;
                                doc.addEventListener('mousemove', handler);
                                unbindMouseMoveRef.current = () => {
                                    doc.removeEventListener('mousemove', handler);
                                };
                                return;
                            }

                            // Allow interactivity without `safePolygon` on touch devices. With a
                            // pointer, a short close delay is an alternative, so it should work
                            // consistently.
                            const shouldClose = pointerTypeRef.current === 'touch' ? !(0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(floating, event.relatedTarget) : true;
                            if (shouldClose) {
                                closeWithDelay(event);
                            }
                        }

                        // Ensure the floating element closes after scrolling even if the pointer
                        // did not move.
                        // https://github.com/floating-ui/floating-ui/discussions/1692
                        function onScrollMouseLeave(event) {
                            if (isClickLikeOpenEvent()) {
                                return;
                            }
                            handleCloseRef.current == null ? void 0 : handleCloseRef.current({
                                ...context,
                                tree,
                                x: event.clientX,
                                y: event.clientY,
                                onClose() {
                                    clearPointerEvents();
                                    cleanupMouseMoveHandler();
                                    closeWithDelay(event);
                                }
                            })(event);
                        }
                        if ((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(domReference)) {
                            const ref = domReference;
                            open && ref.addEventListener('mouseleave', onScrollMouseLeave);
                            floating == null ? void 0 : floating.addEventListener('mouseleave', onScrollMouseLeave);
                            move && ref.addEventListener('mousemove', onMouseEnter, {
                                once: true
                            });
                            ref.addEventListener('mouseenter', onMouseEnter);
                            ref.addEventListener('mouseleave', onMouseLeave);
                            return () => {
                                open && ref.removeEventListener('mouseleave', onScrollMouseLeave);
                                floating == null ? void 0 : floating.removeEventListener('mouseleave', onScrollMouseLeave);
                                move && ref.removeEventListener('mousemove', onMouseEnter);
                                ref.removeEventListener('mouseenter', onMouseEnter);
                                ref.removeEventListener('mouseleave', onMouseLeave);
                            };
                        }
                    }, [domReference, floating, enabled, context, mouseOnly, restMs, move, closeWithDelay, cleanupMouseMoveHandler, clearPointerEvents, onOpenChange, open, tree, delayRef, handleCloseRef, dataRef]);

                    // Block pointer-events of every element other than the reference and floating
                    // while the floating element is open and has a `handleClose` handler. Also
                    // handles nested floating elements.
                    // https://github.com/floating-ui/floating-ui/issues/1722
                    index(() => {
                        var _handleCloseRef$curre;
                        if (!enabled) {
                            return;
                        }
                        if (open && (_handleCloseRef$curre = handleCloseRef.current) != null && _handleCloseRef$curre.__options.blockPointerEvents && isHoverOpen()) {
                            const body = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating).body;
                            body.setAttribute(safePolygonIdentifier, '');
                            body.style.pointerEvents = 'none';
                            performedPointerEventsMutationRef.current = true;
                            if ((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(domReference) && floating) {
                                var _tree$nodesRef$curren, _tree$nodesRef$curren2;
                                const ref = domReference;
                                const parentFloating = tree == null ? void 0 : (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null ? void 0 : (_tree$nodesRef$curren2 = _tree$nodesRef$curren.context) == null ? void 0 : _tree$nodesRef$curren2.elements.floating;
                                if (parentFloating) {
                                    parentFloating.style.pointerEvents = '';
                                }
                                ref.style.pointerEvents = 'auto';
                                floating.style.pointerEvents = 'auto';
                                return () => {
                                    ref.style.pointerEvents = '';
                                    floating.style.pointerEvents = '';
                                };
                            }
                        }
                    }, [enabled, open, parentId, floating, domReference, tree, handleCloseRef, dataRef, isHoverOpen]);
                    index(() => {
                        if (!open) {
                            pointerTypeRef.current = undefined;
                            cleanupMouseMoveHandler();
                            clearPointerEvents();
                        }
                    }, [open, cleanupMouseMoveHandler, clearPointerEvents]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        return () => {
                            cleanupMouseMoveHandler();
                            clearTimeout(timeoutRef.current);
                            clearTimeout(restTimeoutRef.current);
                            clearPointerEvents();
                        };
                    }, [enabled, domReference, cleanupMouseMoveHandler, clearPointerEvents]);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (!enabled) {
                            return {};
                        }

                        function setPointerRef(event) {
                            pointerTypeRef.current = event.pointerType;
                        }
                        return {
                            reference: {
                                onPointerDown: setPointerRef,
                                onPointerEnter: setPointerRef,
                                onMouseMove(event) {
                                    if (open || restMs === 0) {
                                        return;
                                    }
                                    clearTimeout(restTimeoutRef.current);
                                    restTimeoutRef.current = setTimeout(() => {
                                        if (!blockMouseMoveRef.current) {
                                            onOpenChange(true, event.nativeEvent);
                                        }
                                    }, restMs);
                                }
                            },
                            floating: {
                                onMouseEnter() {
                                    clearTimeout(timeoutRef.current);
                                },
                                onMouseLeave(event) {
                                    events.emit('dismiss', {
                                        type: 'mouseLeave',
                                        data: {
                                            returnFocus: false
                                        }
                                    });
                                    closeWithDelay(event.nativeEvent, false);
                                }
                            }
                        };
                    }, [events, enabled, restMs, open, onOpenChange, closeWithDelay]);
                }

                const FloatingDelayGroupContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({
                    delay: 0,
                    initialDelay: 0,
                    timeoutMs: 0,
                    currentId: null,
                    setCurrentId: () => {},
                    setState: () => {},
                    isInstantPhase: false
                });
                const useDelayGroupContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(FloatingDelayGroupContext);
                /**
                 * Provides context for a group of floating elements that should share a
                 * `delay`.
                 * @see https://floating-ui.com/docs/FloatingDelayGroup
                 */
                const FloatingDelayGroup = _ref => {
                    let {
                        children,
                        delay,
                        timeoutMs = 0
                    } = _ref;
                    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((prev, next) => ({
                        ...prev,
                        ...next
                    }), {
                        delay,
                        timeoutMs,
                        initialDelay: delay,
                        currentId: null,
                        isInstantPhase: false
                    });
                    const initialCurrentIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const setCurrentId = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(currentId => {
                        setState({
                            currentId
                        });
                    }, []);
                    index(() => {
                        if (state.currentId) {
                            if (initialCurrentIdRef.current === null) {
                                initialCurrentIdRef.current = state.currentId;
                            } else {
                                setState({
                                    isInstantPhase: true
                                });
                            }
                        } else {
                            setState({
                                isInstantPhase: false
                            });
                            initialCurrentIdRef.current = null;
                        }
                    }, [state.currentId]);
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FloatingDelayGroupContext.Provider, {
                        value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                            ...state,
                            setState,
                            setCurrentId
                        }), [state, setState, setCurrentId])
                    }, children);
                };
                const useDelayGroup = (_ref2, _ref3) => {
                    let {
                        open,
                        onOpenChange
                    } = _ref2;
                    let {
                        id
                    } = _ref3;
                    const {
                        currentId,
                        setCurrentId,
                        initialDelay,
                        setState,
                        timeoutMs
                    } = useDelayGroupContext();
                    index(() => {
                        if (currentId) {
                            setState({
                                delay: {
                                    open: 1,
                                    close: getDelay(initialDelay, 'close')
                                }
                            });
                            if (currentId !== id) {
                                onOpenChange(false);
                            }
                        }
                    }, [id, onOpenChange, setState, currentId, initialDelay]);
                    index(() => {
                        function unset() {
                            onOpenChange(false);
                            setState({
                                delay: initialDelay,
                                currentId: null
                            });
                        }
                        if (!open && currentId === id) {
                            if (timeoutMs) {
                                const timeout = window.setTimeout(unset, timeoutMs);
                                return () => {
                                    clearTimeout(timeout);
                                };
                            } else {
                                unset();
                            }
                        }
                    }, [open, setState, currentId, id, onOpenChange, initialDelay, timeoutMs]);
                    index(() => {
                        if (open) {
                            setCurrentId(id);
                        }
                    }, [open, setCurrentId, id]);
                };

                function getAncestors(nodes, id) {
                    var _nodes$find;
                    let allAncestors = [];
                    let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;
                    while (currentParentId) {
                        const currentNode = nodes.find(node => node.id === currentParentId);
                        currentParentId = currentNode == null ? void 0 : currentNode.parentId;
                        if (currentNode) {
                            allAncestors = allAncestors.concat(currentNode);
                        }
                    }
                    return allAncestors;
                }

                function getChildren(nodes, id) {
                    let allChildren = nodes.filter(node => {
                        var _node$context;
                        return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);
                    });
                    let currentChildren = allChildren;
                    while (currentChildren.length) {
                        currentChildren = nodes.filter(node => {
                            var _currentChildren;
                            return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {
                                var _node$context2;
                                return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);
                            });
                        });
                        allChildren = allChildren.concat(currentChildren);
                    }
                    return allChildren;
                }

                function getDeepestNode(nodes, id) {
                    let deepestNodeId;
                    let maxDepth = -1;

                    function findDeepest(nodeId, depth) {
                        if (depth > maxDepth) {
                            deepestNodeId = nodeId;
                            maxDepth = depth;
                        }
                        const children = getChildren(nodes, nodeId);
                        children.forEach(child => {
                            findDeepest(child.id, depth + 1);
                        });
                    }
                    findDeepest(id, 0);
                    return nodes.find(node => node.id === deepestNodeId);
                }

                // Modified to add conditional `aria-hidden` support:
                // https://github.com/theKashey/aria-hidden/blob/9220c8f4a4fd35f63bee5510a9f41a37264382d4/src/index.ts
                let counterMap = /*#__PURE__*/ new WeakMap();
                let uncontrolledElementsSet = /*#__PURE__*/ new WeakSet();
                let markerMap = {};
                let lockCount = 0;
                const supportsInert = () => typeof HTMLElement !== 'undefined' && 'inert' in HTMLElement.prototype;
                const unwrapHost = node => node && (node.host || unwrapHost(node.parentNode));
                const correctElements = (parent, targets) => targets.map(target => {
                    if (parent.contains(target)) {
                        return target;
                    }
                    const correctedTarget = unwrapHost(target);
                    if (parent.contains(correctedTarget)) {
                        return correctedTarget;
                    }
                    return null;
                }).filter(x => x != null);

                function applyAttributeToOthers(uncorrectedAvoidElements, body, ariaHidden, inert) {
                    const markerName = 'data-floating-ui-inert';
                    const controlAttribute = inert ? 'inert' : ariaHidden ? 'aria-hidden' : null;
                    const avoidElements = correctElements(body, uncorrectedAvoidElements);
                    const elementsToKeep = new Set();
                    const elementsToStop = new Set(avoidElements);
                    const hiddenElements = [];
                    if (!markerMap[markerName]) {
                        markerMap[markerName] = new WeakMap();
                    }
                    const markerCounter = markerMap[markerName];
                    avoidElements.forEach(keep);
                    deep(body);
                    elementsToKeep.clear();

                    function keep(el) {
                        if (!el || elementsToKeep.has(el)) {
                            return;
                        }
                        elementsToKeep.add(el);
                        el.parentNode && keep(el.parentNode);
                    }

                    function deep(parent) {
                        if (!parent || elementsToStop.has(parent)) {
                            return;
                        }
                        Array.prototype.forEach.call(parent.children, node => {
                            if (elementsToKeep.has(node)) {
                                deep(node);
                            } else {
                                const attr = controlAttribute ? node.getAttribute(controlAttribute) : null;
                                const alreadyHidden = attr !== null && attr !== 'false';
                                const counterValue = (counterMap.get(node) || 0) + 1;
                                const markerValue = (markerCounter.get(node) || 0) + 1;
                                counterMap.set(node, counterValue);
                                markerCounter.set(node, markerValue);
                                hiddenElements.push(node);
                                if (counterValue === 1 && alreadyHidden) {
                                    uncontrolledElementsSet.add(node);
                                }
                                if (markerValue === 1) {
                                    node.setAttribute(markerName, '');
                                }
                                if (!alreadyHidden && controlAttribute) {
                                    node.setAttribute(controlAttribute, 'true');
                                }
                            }
                        });
                    }
                    lockCount++;
                    return () => {
                        hiddenElements.forEach(element => {
                            const counterValue = (counterMap.get(element) || 0) - 1;
                            const markerValue = (markerCounter.get(element) || 0) - 1;
                            counterMap.set(element, counterValue);
                            markerCounter.set(element, markerValue);
                            if (!counterValue) {
                                if (!uncontrolledElementsSet.has(element) && controlAttribute) {
                                    element.removeAttribute(controlAttribute);
                                }
                                uncontrolledElementsSet.delete(element);
                            }
                            if (!markerValue) {
                                element.removeAttribute(markerName);
                            }
                        });
                        lockCount--;
                        if (!lockCount) {
                            counterMap = new WeakMap();
                            counterMap = new WeakMap();
                            uncontrolledElementsSet = new WeakSet();
                            markerMap = {};
                        }
                    };
                }

                function markOthers(avoidElements, ariaHidden, inert) {
                    if (ariaHidden === void 0) {
                        ariaHidden = false;
                    }
                    if (inert === void 0) {
                        inert = false;
                    }
                    const body = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(avoidElements[0]).body;
                    return applyAttributeToOthers(avoidElements.concat(Array.from(body.querySelectorAll('[aria-live]'))), body, ariaHidden, inert);
                }

                const getTabbableOptions = () => ({
                    getShadowRoot: true,
                    displayCheck:
                        // JSDOM does not support the `tabbable` library. To solve this we can
                        // check if `ResizeObserver` is a real function (not polyfilled), which
                        // determines if the current environment is JSDOM-like.
                        typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'
                });

                function getTabbableIn(container, direction) {
                    const allTabbable = (0, tabbable__WEBPACK_IMPORTED_MODULE_8__.tabbable)(container, getTabbableOptions());
                    if (direction === 'prev') {
                        allTabbable.reverse();
                    }
                    const activeIndex = allTabbable.indexOf((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(container)));
                    const nextTabbableElements = allTabbable.slice(activeIndex + 1);
                    return nextTabbableElements[0];
                }

                function getNextTabbable() {
                    return getTabbableIn(document.body, 'next');
                }

                function getPreviousTabbable() {
                    return getTabbableIn(document.body, 'prev');
                }

                function isOutsideEvent(event, container) {
                    const containerElement = container || event.currentTarget;
                    const relatedTarget = event.relatedTarget;
                    return !relatedTarget || !(0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(containerElement, relatedTarget);
                }

                function disableFocusInside(container) {
                    const tabbableElements = (0, tabbable__WEBPACK_IMPORTED_MODULE_8__.tabbable)(container, getTabbableOptions());
                    tabbableElements.forEach(element => {
                        element.dataset.tabindex = element.getAttribute('tabindex') || '';
                        element.setAttribute('tabindex', '-1');
                    });
                }

                function enableFocusInside(container) {
                    const elements = container.querySelectorAll('[data-tabindex]');
                    elements.forEach(element => {
                        const tabindex = element.dataset.tabindex;
                        delete element.dataset.tabindex;
                        if (tabindex) {
                            element.setAttribute('tabindex', tabindex);
                        } else {
                            element.removeAttribute('tabindex');
                        }
                    });
                }

                // See Diego Haz's Sandbox for making this logic work well on Safari/iOS:
                // https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/FocusTrap.tsx

                const HIDDEN_STYLES = {
                    border: 0,
                    clip: 'rect(0 0 0 0)',
                    height: '1px',
                    margin: '-1px',
                    overflow: 'hidden',
                    padding: 0,
                    position: 'fixed',
                    whiteSpace: 'nowrap',
                    width: '1px',
                    top: 0,
                    left: 0
                };
                let timeoutId;

                function setActiveElementOnTab(event) {
                    if (event.key === 'Tab') {
                        event.target;
                        clearTimeout(timeoutId);
                    }
                }
                const FocusGuard = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function FocusGuard(props, ref) {
                    const [role, setRole] = react__WEBPACK_IMPORTED_MODULE_0__.useState();
                    index(() => {
                        if ((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isSafari)()) {
                            // Unlike other screen readers such as NVDA and JAWS, the virtual cursor
                            // on VoiceOver does trigger the onFocus event, so we can use the focus
                            // trap element. On Safari, only buttons trigger the onFocus event.
                            // NB: "group" role in the Sandbox no longer appears to work, must be a
                            // button role.
                            setRole('button');
                        }
                        document.addEventListener('keydown', setActiveElementOnTab);
                        return () => {
                            document.removeEventListener('keydown', setActiveElementOnTab);
                        };
                    }, []);
                    const restProps = {
                        ref,
                        tabIndex: 0,
                        // Role is only for VoiceOver
                        role,
                        'aria-hidden': role ? undefined : true,
                        [createAttribute('focus-guard')]: '',
                        style: HIDDEN_STYLES
                    };
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("span", _extends({}, props, restProps));
                });

                const PortalContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);

                function useFloatingPortalNode(_temp) {
                    let {
                        id,
                        root
                    } = _temp === void 0 ? {} : _temp;
                    const [portalNode, setPortalNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);
                    const uniqueId = useId();
                    const portalContext = usePortalContext();
                    const data = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        id,
                        root,
                        portalContext,
                        uniqueId
                    }), [id, root, portalContext, uniqueId]);
                    const dataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();
                    index(() => {
                        return () => {
                            portalNode == null ? void 0 : portalNode.remove();
                        };
                    }, [portalNode, data]);
                    index(() => {
                        if (dataRef.current === data) return;
                        dataRef.current = data;
                        const {
                            id,
                            root,
                            portalContext,
                            uniqueId
                        } = data;
                        const existingIdRoot = id ? document.getElementById(id) : null;
                        const attr = createAttribute('portal');
                        if (existingIdRoot) {
                            const subRoot = document.createElement('div');
                            subRoot.id = uniqueId;
                            subRoot.setAttribute(attr, '');
                            existingIdRoot.appendChild(subRoot);
                            setPortalNode(subRoot);
                        } else {
                            let container = root || (portalContext == null ? void 0 : portalContext.portalNode);
                            if (container && !(0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(container)) container = container.current;
                            container = container || document.body;
                            let idWrapper = null;
                            if (id) {
                                idWrapper = document.createElement('div');
                                idWrapper.id = id;
                                container.appendChild(idWrapper);
                            }
                            const subRoot = document.createElement('div');
                            subRoot.id = uniqueId;
                            subRoot.setAttribute(attr, '');
                            container = idWrapper || container;
                            container.appendChild(subRoot);
                            setPortalNode(subRoot);
                        }
                    }, [data]);
                    return portalNode;
                }
                /**
                 * Portals the floating element into a given container element — by default,
                 * outside of the app root and into the body.
                 * @see https://floating-ui.com/docs/FloatingPortal
                 */
                function FloatingPortal(_ref) {
                    let {
                        children,
                        id,
                        root = null,
                        preserveTabOrder = true
                    } = _ref;
                    const portalNode = useFloatingPortalNode({
                        id,
                        root
                    });
                    const [focusManagerState, setFocusManagerState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);
                    const beforeOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const afterOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const beforeInsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const afterInsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const shouldRenderGuards =
                        // The FocusManager and therefore floating element are currently open/
                        // rendered.
                        !!focusManagerState &&
                        // Guards are only for non-modal focus management.
                        !focusManagerState.modal &&
                        // Don't render if unmount is transitioning.
                        focusManagerState.open && preserveTabOrder && !!(root || portalNode);

                    // https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/TabbablePortal.tsx
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!portalNode || !preserveTabOrder || focusManagerState != null && focusManagerState.modal) {
                            return;
                        }

                        // Make sure elements inside the portal element are tabbable only when the
                        // portal has already been focused, either by tabbing into a focus trap
                        // element outside or using the mouse.
                        function onFocus(event) {
                            if (portalNode && isOutsideEvent(event)) {
                                const focusing = event.type === 'focusin';
                                const manageFocus = focusing ? enableFocusInside : disableFocusInside;
                                manageFocus(portalNode);
                            }
                        }
                        // Listen to the event on the capture phase so they run before the focus
                        // trap elements onFocus prop is called.
                        portalNode.addEventListener('focusin', onFocus, true);
                        portalNode.addEventListener('focusout', onFocus, true);
                        return () => {
                            portalNode.removeEventListener('focusin', onFocus, true);
                            portalNode.removeEventListener('focusout', onFocus, true);
                        };
                    }, [portalNode, preserveTabOrder, focusManagerState == null ? void 0 : focusManagerState.modal]);
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(PortalContext.Provider, {
                        value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                            preserveTabOrder,
                            beforeOutsideRef,
                            afterOutsideRef,
                            beforeInsideRef,
                            afterInsideRef,
                            portalNode,
                            setFocusManagerState
                        }), [preserveTabOrder, portalNode])
                    }, shouldRenderGuards && portalNode && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FocusGuard, {
                        "data-type": "outside",
                        ref: beforeOutsideRef,
                        onFocus: event => {
                            if (isOutsideEvent(event, portalNode)) {
                                var _beforeInsideRef$curr;
                                (_beforeInsideRef$curr = beforeInsideRef.current) == null ? void 0 : _beforeInsideRef$curr.focus();
                            } else {
                                const prevTabbable = getPreviousTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);
                                prevTabbable == null ? void 0 : prevTabbable.focus();
                            }
                        }
                    }), shouldRenderGuards && portalNode && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("span", {
                        "aria-owns": portalNode.id,
                        style: HIDDEN_STYLES
                    }), portalNode && /*#__PURE__*/ (0, react_dom__WEBPACK_IMPORTED_MODULE_5__.createPortal)(children, portalNode), shouldRenderGuards && portalNode && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FocusGuard, {
                        "data-type": "outside",
                        ref: afterOutsideRef,
                        onFocus: event => {
                            if (isOutsideEvent(event, portalNode)) {
                                var _afterInsideRef$curre;
                                (_afterInsideRef$curre = afterInsideRef.current) == null ? void 0 : _afterInsideRef$curre.focus();
                            } else {
                                const nextTabbable = getNextTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);
                                nextTabbable == null ? void 0 : nextTabbable.focus();
                                (focusManagerState == null ? void 0 : focusManagerState.closeOnFocusOut) && (focusManagerState == null ? void 0 : focusManagerState.onOpenChange(false, event.nativeEvent));
                            }
                        }
                    }));
                }
                const usePortalContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(PortalContext);

                const VisuallyHiddenDismiss = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function VisuallyHiddenDismiss(props, ref) {
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("button", _extends({}, props, {
                        type: "button",
                        ref: ref,
                        tabIndex: -1,
                        style: HIDDEN_STYLES
                    }));
                });
                /**
                 * Provides focus management for the floating element.
                 * @see https://floating-ui.com/docs/FloatingFocusManager
                 */
                function FloatingFocusManager(props) {
                    const {
                        context,
                        children,
                        disabled = false,
                        order = ['content'],
                        guards: _guards = true,
                        initialFocus = 0,
                        returnFocus = true,
                        modal = true,
                        visuallyHiddenDismiss = false,
                        closeOnFocusOut = true
                    } = props;
                    const {
                        open,
                        refs,
                        nodeId,
                        onOpenChange,
                        events,
                        dataRef,
                        elements: {
                            domReference,
                            floating
                        }
                    } = context;

                    // Force the guards to be rendered if the `inert` attribute is not supported.
                    const guards = supportsInert() ? _guards : true;
                    const orderRef = useLatestRef(order);
                    const initialFocusRef = useLatestRef(initialFocus);
                    const returnFocusRef = useLatestRef(returnFocus);
                    const tree = useFloatingTree();
                    const portalContext = usePortalContext();

                    // Controlled by `useListNavigation`.
                    const ignoreInitialFocus = typeof initialFocus === 'number' && initialFocus < 0;
                    const startDismissButtonRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const endDismissButtonRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const preventReturnFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const previouslyFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const isInsidePortal = portalContext != null;

                    // If the reference is a combobox and is typeable (e.g. input/textarea),
                    // there are different focus semantics. The guards should not be rendered, but
                    // aria-hidden should be applied to all nodes still. Further, the visually
                    // hidden dismiss button should only appear at the end of the list, not the
                    // start.
                    const isUntrappedTypeableCombobox = domReference && domReference.getAttribute('role') === 'combobox' && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isTypeableElement)(domReference) && ignoreInitialFocus;
                    const getTabbableContent = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(container) {
                        if (container === void 0) {
                            container = floating;
                        }
                        return container ? (0, tabbable__WEBPACK_IMPORTED_MODULE_8__.tabbable)(container, getTabbableOptions()) : [];
                    }, [floating]);
                    const getTabbableElements = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(container => {
                        const content = getTabbableContent(container);
                        return orderRef.current.map(type => {
                            if (domReference && type === 'reference') {
                                return domReference;
                            }
                            if (floating && type === 'floating') {
                                return floating;
                            }
                            return content;
                        }).filter(Boolean).flat();
                    }, [domReference, floating, orderRef, getTabbableContent]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (disabled || !modal) return;

                        function onKeyDown(event) {
                            if (event.key === 'Tab') {
                                // The focus guards have nothing to focus, so we need to stop the event.
                                if ((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(floating, (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating))) && getTabbableContent().length === 0 && !isUntrappedTypeableCombobox) {
                                    (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                }
                                const els = getTabbableElements();
                                const target = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getTarget)(event);
                                if (orderRef.current[0] === 'reference' && target === domReference) {
                                    (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                    if (event.shiftKey) {
                                        enqueueFocus(els[els.length - 1]);
                                    } else {
                                        enqueueFocus(els[1]);
                                    }
                                }
                                if (orderRef.current[1] === 'floating' && target === floating && event.shiftKey) {
                                    (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                    enqueueFocus(els[0]);
                                }
                            }
                        }
                        const doc = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating);
                        doc.addEventListener('keydown', onKeyDown);
                        return () => {
                            doc.removeEventListener('keydown', onKeyDown);
                        };
                    }, [disabled, domReference, floating, modal, orderRef, refs, isUntrappedTypeableCombobox, getTabbableContent, getTabbableElements]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (disabled || !closeOnFocusOut) return;

                        // In Safari, buttons lose focus when pressing them.
                        function handlePointerDown() {
                            isPointerDownRef.current = true;
                            setTimeout(() => {
                                isPointerDownRef.current = false;
                            });
                        }

                        function handleFocusOutside(event) {
                            const relatedTarget = event.relatedTarget;
                            queueMicrotask(() => {
                                const movedToUnrelatedNode = !((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(domReference, relatedTarget) || (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(floating, relatedTarget) || (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(relatedTarget, floating) || (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(portalContext == null ? void 0 : portalContext.portalNode, relatedTarget) || relatedTarget != null && relatedTarget.hasAttribute(createAttribute('focus-guard')) || tree && (getChildren(tree.nodesRef.current, nodeId).find(node => {
                                    var _node$context, _node$context2;
                                    return (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)((_node$context = node.context) == null ? void 0 : _node$context.elements.floating, relatedTarget) || (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)((_node$context2 = node.context) == null ? void 0 : _node$context2.elements.domReference, relatedTarget);
                                }) || getAncestors(tree.nodesRef.current, nodeId).find(node => {
                                    var _node$context3, _node$context4;
                                    return ((_node$context3 = node.context) == null ? void 0 : _node$context3.elements.floating) === relatedTarget || ((_node$context4 = node.context) == null ? void 0 : _node$context4.elements.domReference) === relatedTarget;
                                })));

                                // Focus did not move inside the floating tree, and there are no tabbable
                                // portal guards to handle closing.
                                if (relatedTarget && movedToUnrelatedNode && !isPointerDownRef.current &&
                                    // Fix React 18 Strict Mode returnFocus due to double rendering.
                                    relatedTarget !== previouslyFocusedElementRef.current) {
                                    preventReturnFocusRef.current = true;
                                    onOpenChange(false, event);
                                }
                            });
                        }
                        if (floating && (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(domReference)) {
                            domReference.addEventListener('focusout', handleFocusOutside);
                            domReference.addEventListener('pointerdown', handlePointerDown);
                            !modal && floating.addEventListener('focusout', handleFocusOutside);
                            return () => {
                                domReference.removeEventListener('focusout', handleFocusOutside);
                                domReference.removeEventListener('pointerdown', handlePointerDown);
                                !modal && floating.removeEventListener('focusout', handleFocusOutside);
                            };
                        }
                    }, [disabled, domReference, floating, modal, nodeId, tree, portalContext, onOpenChange, closeOnFocusOut]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        var _portalContext$portal;
                        if (disabled) return;

                        // Don't hide portals nested within the parent portal.
                        const portalNodes = Array.from((portalContext == null ? void 0 : (_portalContext$portal = portalContext.portalNode) == null ? void 0 : _portalContext$portal.querySelectorAll("[" + createAttribute('portal') + "]")) || []);
                        if (floating) {
                            const insideElements = [floating, ...portalNodes, startDismissButtonRef.current, endDismissButtonRef.current, orderRef.current.includes('reference') || isUntrappedTypeableCombobox ? domReference : null].filter(x => x != null);
                            const cleanup = modal ? markOthers(insideElements, guards, !guards) : markOthers(insideElements);
                            return () => {
                                cleanup();
                            };
                        }
                    }, [disabled, domReference, floating, modal, orderRef, portalContext, isUntrappedTypeableCombobox, guards]);
                    index(() => {
                        if (disabled || !floating) return;
                        const doc = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating);
                        const previouslyFocusedElement = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)(doc);

                        // Wait for any layout effect state setters to execute to set `tabIndex`.
                        queueMicrotask(() => {
                            const focusableElements = getTabbableElements(floating);
                            const initialFocusValue = initialFocusRef.current;
                            const elToFocus = (typeof initialFocusValue === 'number' ? focusableElements[initialFocusValue] : initialFocusValue.current) || floating;
                            const focusAlreadyInsideFloatingEl = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(floating, previouslyFocusedElement);
                            if (!ignoreInitialFocus && !focusAlreadyInsideFloatingEl && open) {
                                enqueueFocus(elToFocus, {
                                    preventScroll: elToFocus === floating
                                });
                            }
                        });
                    }, [disabled, open, floating, ignoreInitialFocus, getTabbableElements, initialFocusRef]);
                    index(() => {
                        if (disabled || !floating) return;
                        let preventReturnFocusScroll = false;
                        const doc = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating);
                        const previouslyFocusedElement = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)(doc);
                        const contextData = dataRef.current;
                        previouslyFocusedElementRef.current = previouslyFocusedElement;

                        // Dismissing via outside press should always ignore `returnFocus` to
                        // prevent unwanted scrolling.
                        function onDismiss(payload) {
                            if (payload.type === 'escapeKey' && refs.domReference.current) {
                                previouslyFocusedElementRef.current = refs.domReference.current;
                            }
                            if (['referencePress', 'escapeKey'].includes(payload.type)) {
                                return;
                            }
                            const returnFocus = payload.data.returnFocus;
                            if (typeof returnFocus === 'object') {
                                preventReturnFocusRef.current = false;
                                preventReturnFocusScroll = returnFocus.preventScroll;
                            } else {
                                preventReturnFocusRef.current = !returnFocus;
                            }
                        }
                        events.on('dismiss', onDismiss);
                        return () => {
                            events.off('dismiss', onDismiss);
                            const activeEl = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)(doc);
                            const shouldFocusReference = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(floating, activeEl) || tree && getChildren(tree.nodesRef.current, nodeId).some(node => {
                                var _node$context5;
                                return (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)((_node$context5 = node.context) == null ? void 0 : _node$context5.elements.floating, activeEl);
                            }) || contextData.openEvent && ['click', 'mousedown'].includes(contextData.openEvent.type);
                            if (shouldFocusReference && refs.domReference.current) {
                                previouslyFocusedElementRef.current = refs.domReference.current;
                            }
                            if (
                                // eslint-disable-next-line react-hooks/exhaustive-deps
                                returnFocusRef.current && (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(previouslyFocusedElementRef.current) && !preventReturnFocusRef.current) {
                                enqueueFocus(previouslyFocusedElementRef.current, {
                                    // When dismissing nested floating elements, by the time the rAF has
                                    // executed, the menus will all have been unmounted. When they try
                                    // to get focused, the calls get ignored — leaving the root
                                    // reference focused as desired.
                                    cancelPrevious: false,
                                    preventScroll: preventReturnFocusScroll
                                });
                            }
                        };
                    }, [disabled, floating, returnFocusRef, dataRef, refs, events, tree, nodeId]);

                    // Synchronize the `context` & `modal` value to the FloatingPortal context.
                    // It will decide whether or not it needs to render its own guards.
                    index(() => {
                        if (disabled || !portalContext) return;
                        portalContext.setFocusManagerState({
                            modal,
                            closeOnFocusOut,
                            open,
                            onOpenChange,
                            refs
                        });
                        return () => {
                            portalContext.setFocusManagerState(null);
                        };
                    }, [disabled, portalContext, modal, open, onOpenChange, refs, closeOnFocusOut]);
                    index(() => {
                        if (disabled) return;
                        if (floating && typeof MutationObserver === 'function' && !ignoreInitialFocus) {
                            const handleMutation = () => {
                                const tabIndex = floating.getAttribute('tabindex');
                                if (orderRef.current.includes('floating') || (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating)) !== refs.domReference.current && getTabbableContent().length === 0) {
                                    if (tabIndex !== '0') {
                                        floating.setAttribute('tabindex', '0');
                                    }
                                } else if (tabIndex !== '-1') {
                                    floating.setAttribute('tabindex', '-1');
                                }
                            };
                            handleMutation();
                            const observer = new MutationObserver(handleMutation);
                            observer.observe(floating, {
                                childList: true,
                                subtree: true,
                                attributes: true
                            });
                            return () => {
                                observer.disconnect();
                            };
                        }
                    }, [disabled, floating, refs, orderRef, getTabbableContent, ignoreInitialFocus]);

                    function renderDismissButton(location) {
                        if (disabled || !visuallyHiddenDismiss || !modal) {
                            return null;
                        }
                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(VisuallyHiddenDismiss, {
                            ref: location === 'start' ? startDismissButtonRef : endDismissButtonRef,
                            onClick: event => onOpenChange(false, event.nativeEvent)
                        }, typeof visuallyHiddenDismiss === 'string' ? visuallyHiddenDismiss : 'Dismiss');
                    }
                    const shouldRenderGuards = !disabled && guards && !isUntrappedTypeableCombobox && (isInsidePortal || modal);
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, shouldRenderGuards && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FocusGuard, {
                        "data-type": "inside",
                        ref: portalContext == null ? void 0 : portalContext.beforeInsideRef,
                        onFocus: event => {
                            if (modal) {
                                const els = getTabbableElements();
                                enqueueFocus(order[0] === 'reference' ? els[0] : els[els.length - 1]);
                            } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {
                                preventReturnFocusRef.current = false;
                                if (isOutsideEvent(event, portalContext.portalNode)) {
                                    const nextTabbable = getNextTabbable() || domReference;
                                    nextTabbable == null ? void 0 : nextTabbable.focus();
                                } else {
                                    var _portalContext$before;
                                    (_portalContext$before = portalContext.beforeOutsideRef.current) == null ? void 0 : _portalContext$before.focus();
                                }
                            }
                        }
                    }), !isUntrappedTypeableCombobox && renderDismissButton('start'), children, renderDismissButton('end'), shouldRenderGuards && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FocusGuard, {
                        "data-type": "inside",
                        ref: portalContext == null ? void 0 : portalContext.afterInsideRef,
                        onFocus: event => {
                            if (modal) {
                                enqueueFocus(getTabbableElements()[0]);
                            } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {
                                if (closeOnFocusOut) {
                                    preventReturnFocusRef.current = true;
                                }
                                if (isOutsideEvent(event, portalContext.portalNode)) {
                                    const prevTabbable = getPreviousTabbable() || domReference;
                                    prevTabbable == null ? void 0 : prevTabbable.focus();
                                } else {
                                    var _portalContext$afterO;
                                    (_portalContext$afterO = portalContext.afterOutsideRef.current) == null ? void 0 : _portalContext$afterO.focus();
                                }
                            }
                        }
                    }));
                }

                const activeLocks = /*#__PURE__*/ new Set();

                /**
                 * Provides base styling for a fixed overlay element to dim content or block
                 * pointer events behind a floating element.
                 * It's a regular `<div>`, so it can be styled via any CSS solution you prefer.
                 * @see https://floating-ui.com/docs/FloatingOverlay
                 */
                const FloatingOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function FloatingOverlay(_ref, ref) {
                    let {
                        lockScroll = false,
                            ...rest
                    } = _ref;
                    const lockId = useId();
                    index(() => {
                        if (!lockScroll) return;
                        activeLocks.add(lockId);
                        const isIOS = /iP(hone|ad|od)|iOS/.test((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getPlatform)());
                        const bodyStyle = document.body.style;
                        // RTL <body> scrollbar
                        const scrollbarX = Math.round(document.documentElement.getBoundingClientRect().left) + document.documentElement.scrollLeft;
                        const paddingProp = scrollbarX ? 'paddingLeft' : 'paddingRight';
                        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
                        const scrollX = bodyStyle.left ? parseFloat(bodyStyle.left) : window.pageXOffset;
                        const scrollY = bodyStyle.top ? parseFloat(bodyStyle.top) : window.pageYOffset;
                        bodyStyle.overflow = 'hidden';
                        if (scrollbarWidth) {
                            bodyStyle[paddingProp] = scrollbarWidth + "px";
                        }

                        // Only iOS doesn't respect `overflow: hidden` on document.body, and this
                        // technique has fewer side effects.
                        if (isIOS) {
                            var _window$visualViewpor, _window$visualViewpor2;
                            // iOS 12 does not support `visualViewport`.
                            const offsetLeft = ((_window$visualViewpor = window.visualViewport) == null ? void 0 : _window$visualViewpor.offsetLeft) || 0;
                            const offsetTop = ((_window$visualViewpor2 = window.visualViewport) == null ? void 0 : _window$visualViewpor2.offsetTop) || 0;
                            Object.assign(bodyStyle, {
                                position: 'fixed',
                                top: -(scrollY - Math.floor(offsetTop)) + "px",
                                left: -(scrollX - Math.floor(offsetLeft)) + "px",
                                right: '0'
                            });
                        }
                        return () => {
                            activeLocks.delete(lockId);
                            if (activeLocks.size === 0) {
                                Object.assign(bodyStyle, {
                                    overflow: '',
                                    [paddingProp]: ''
                                });
                                if (isIOS) {
                                    Object.assign(bodyStyle, {
                                        position: '',
                                        top: '',
                                        left: '',
                                        right: ''
                                    });
                                    window.scrollTo(scrollX, scrollY);
                                }
                            }
                        };
                    }, [lockId, lockScroll]);
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", _extends({
                        ref: ref
                    }, rest, {
                        style: {
                            position: 'fixed',
                            overflow: 'auto',
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0,
                            ...rest.style
                        }
                    }));
                });

                function isButtonTarget(event) {
                    return (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(event.target) && event.target.tagName === 'BUTTON';
                }

                function isSpaceIgnored(element) {
                    return (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isTypeableElement)(element);
                }
                /**
                 * Opens or closes the floating element when clicking the reference element.
                 * @see https://floating-ui.com/docs/useClick
                 */
                function useClick(context, props) {
                    if (props === void 0) {
                        props = {};
                    }
                    const {
                        open,
                        onOpenChange,
                        dataRef,
                        elements: {
                            domReference
                        }
                    } = context;
                    const {
                        enabled = true,
                            event: eventOption = 'click',
                            toggle = true,
                            ignoreMouse = false,
                            keyboardHandlers = true
                    } = props;
                    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();
                    const didKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (!enabled) return {};
                        return {
                            reference: {
                                onPointerDown(event) {
                                    pointerTypeRef.current = event.pointerType;
                                },
                                onMouseDown(event) {
                                    // Ignore all buttons except for the "main" button.
                                    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/button
                                    if (event.button !== 0) {
                                        return;
                                    }
                                    if ((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isMouseLikePointerType)(pointerTypeRef.current, true) && ignoreMouse) {
                                        return;
                                    }
                                    if (eventOption === 'click') {
                                        return;
                                    }
                                    if (open && toggle && (dataRef.current.openEvent ? dataRef.current.openEvent.type === 'mousedown' : true)) {
                                        onOpenChange(false, event.nativeEvent);
                                    } else {
                                        // Prevent stealing focus from the floating element
                                        event.preventDefault();
                                        onOpenChange(true, event.nativeEvent);
                                    }
                                },
                                onClick(event) {
                                    if (eventOption === 'mousedown' && pointerTypeRef.current) {
                                        pointerTypeRef.current = undefined;
                                        return;
                                    }
                                    if ((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isMouseLikePointerType)(pointerTypeRef.current, true) && ignoreMouse) {
                                        return;
                                    }
                                    if (open && toggle && (dataRef.current.openEvent ? dataRef.current.openEvent.type === 'click' : true)) {
                                        onOpenChange(false, event.nativeEvent);
                                    } else {
                                        onOpenChange(true, event.nativeEvent);
                                    }
                                },
                                onKeyDown(event) {
                                    pointerTypeRef.current = undefined;
                                    if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event)) {
                                        return;
                                    }
                                    if (event.key === ' ' && !isSpaceIgnored(domReference)) {
                                        // Prevent scrolling
                                        event.preventDefault();
                                        didKeyDownRef.current = true;
                                    }
                                    if (event.key === 'Enter') {
                                        if (open && toggle) {
                                            onOpenChange(false, event.nativeEvent);
                                        } else {
                                            onOpenChange(true, event.nativeEvent);
                                        }
                                    }
                                },
                                onKeyUp(event) {
                                    if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event) || isSpaceIgnored(domReference)) {
                                        return;
                                    }
                                    if (event.key === ' ' && didKeyDownRef.current) {
                                        didKeyDownRef.current = false;
                                        if (open && toggle) {
                                            onOpenChange(false, event.nativeEvent);
                                        } else {
                                            onOpenChange(true, event.nativeEvent);
                                        }
                                    }
                                }
                            }
                        };
                    }, [enabled, dataRef, eventOption, ignoreMouse, keyboardHandlers, domReference, toggle, open, onOpenChange]);
                }

                // `toString()` prevents bundlers from trying to `import { useInsertionEffect } from 'react'`
                const useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[ /*#__PURE__*/ 'useInsertionEffect'.toString()];
                const useSafeInsertionEffect = useInsertionEffect || (fn => fn());

                function useEffectEvent(callback) {
                    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {
                        if (true) {
                            throw new Error('Cannot call an event handler while rendering.');
                        }
                    });
                    useSafeInsertionEffect(() => {
                        ref.current = callback;
                    });
                    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function() {
                        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
                            args[_key] = arguments[_key];
                        }
                        return ref.current == null ? void 0 : ref.current(...args);
                    }, []);
                }

                function createVirtualElement(domRef, data) {
                    let offsetX = null;
                    let offsetY = null;
                    let isAutoUpdateEvent = false;
                    return {
                        contextElement: domRef.current || undefined,
                        getBoundingClientRect() {
                            var _domRef$current, _data$dataRef$current;
                            const domRect = ((_domRef$current = domRef.current) == null ? void 0 : _domRef$current.getBoundingClientRect()) || {
                                width: 0,
                                height: 0,
                                x: 0,
                                y: 0
                            };
                            const isXAxis = data.axis === 'x' || data.axis === 'both';
                            const isYAxis = data.axis === 'y' || data.axis === 'both';
                            const canTrackCursorOnAutoUpdate = ['mouseenter', 'mousemove'].includes(((_data$dataRef$current = data.dataRef.current.openEvent) == null ? void 0 : _data$dataRef$current.type) || '') && data.pointerType !== 'touch';
                            let width = domRect.width;
                            let height = domRect.height;
                            let x = domRect.x;
                            let y = domRect.y;
                            if (offsetX == null && data.x && isXAxis) {
                                offsetX = domRect.x - data.x;
                            }
                            if (offsetY == null && data.y && isYAxis) {
                                offsetY = domRect.y - data.y;
                            }
                            x -= offsetX || 0;
                            y -= offsetY || 0;
                            width = 0;
                            height = 0;
                            if (!isAutoUpdateEvent || canTrackCursorOnAutoUpdate) {
                                width = data.axis === 'y' ? domRect.width : 0;
                                height = data.axis === 'x' ? domRect.height : 0;
                                x = isXAxis && data.x != null ? data.x : x;
                                y = isYAxis && data.y != null ? data.y : y;
                            } else if (isAutoUpdateEvent && !canTrackCursorOnAutoUpdate) {
                                height = data.axis === 'x' ? domRect.height : height;
                                width = data.axis === 'y' ? domRect.width : width;
                            }
                            isAutoUpdateEvent = true;
                            return {
                                width,
                                height,
                                x,
                                y,
                                top: y,
                                right: x + width,
                                bottom: y + height,
                                left: x
                            };
                        }
                    };
                }

                function isMouseBasedEvent(event) {
                    return event != null && event.clientX != null;
                }
                /**
                 * Positions the floating element relative to a client point (in the viewport),
                 * such as the mouse position. By default, it follows the mouse cursor.
                 * @see https://floating-ui.com/docs/useClientPoint
                 */
                function useClientPoint(context, props) {
                    if (props === void 0) {
                        props = {};
                    }
                    const {
                        open,
                        refs,
                        dataRef,
                        elements: {
                            floating
                        }
                    } = context;
                    const {
                        enabled = true,
                            axis = 'both',
                            x = null,
                            y = null
                    } = props;
                    const initialRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const cleanupListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const [pointerType, setPointerType] = react__WEBPACK_IMPORTED_MODULE_0__.useState();
                    const [reactive, setReactive] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);
                    const setReference = useEffectEvent((x, y) => {
                        if (initialRef.current) return;

                        // Prevent setting if the open event was not a mouse-like one
                        // (e.g. focus to open, then hover over the reference element).
                        // Only apply if the event exists.
                        if (dataRef.current.openEvent && !isMouseBasedEvent(dataRef.current.openEvent)) {
                            return;
                        }
                        refs.setPositionReference(createVirtualElement(refs.domReference, {
                            x,
                            y,
                            axis,
                            dataRef,
                            pointerType
                        }));
                    });
                    const handleReferenceEnterOrMove = useEffectEvent(event => {
                        if (x != null || y != null) return;
                        if (!open) {
                            setReference(event.clientX, event.clientY);
                        } else if (!cleanupListenerRef.current) {
                            // If there's no cleanup, there's no listener, but we want to ensure
                            // we add the listener if the cursor landed on the floating element and
                            // then back on the reference (i.e. it's interactive).
                            setReactive([]);
                        }
                    });

                    // If the pointer is a mouse-like pointer, we want to continue following the
                    // mouse even if the floating element is transitioning out. On touch
                    // devices, this is undesirable because the floating element will move to
                    // the dismissal touch point.
                    const openCheck = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isMouseLikePointerType)(pointerType) ? floating : open;
                    const addListener = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {
                        // Explicitly specified `x`/`y` coordinates shouldn't add a listener.
                        if (!openCheck || !enabled || x != null || y != null) return;
                        const win = (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.getWindow)(refs.floating.current);

                        function handleMouseMove(event) {
                            const target = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getTarget)(event);
                            if (!(0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(refs.floating.current, target)) {
                                setReference(event.clientX, event.clientY);
                            } else {
                                win.removeEventListener('mousemove', handleMouseMove);
                                cleanupListenerRef.current = null;
                            }
                        }
                        if (!dataRef.current.openEvent || isMouseBasedEvent(dataRef.current.openEvent)) {
                            win.addEventListener('mousemove', handleMouseMove);
                            const cleanup = () => {
                                win.removeEventListener('mousemove', handleMouseMove);
                                cleanupListenerRef.current = null;
                            };
                            cleanupListenerRef.current = cleanup;
                            return cleanup;
                        }
                        refs.setPositionReference(refs.domReference.current);
                    }, [dataRef, enabled, openCheck, refs, setReference, x, y]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        return addListener();
                    }, [addListener, reactive]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (enabled && !floating) {
                            initialRef.current = false;
                        }
                    }, [enabled, floating]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!enabled && open) {
                            initialRef.current = true;
                        }
                    }, [enabled, open]);
                    index(() => {
                        if (enabled && (x != null || y != null)) {
                            initialRef.current = false;
                            setReference(x, y);
                        }
                    }, [enabled, x, y, setReference]);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (!enabled) return {};

                        function setPointerTypeRef(_ref) {
                            let {
                                pointerType
                            } = _ref;
                            setPointerType(pointerType);
                        }
                        return {
                            reference: {
                                onPointerDown: setPointerTypeRef,
                                onPointerEnter: setPointerTypeRef,
                                onMouseMove: handleReferenceEnterOrMove,
                                onMouseEnter: handleReferenceEnterOrMove
                            }
                        };
                    }, [enabled, handleReferenceEnterOrMove]);
                }

                const bubbleHandlerKeys = {
                    pointerdown: 'onPointerDown',
                    mousedown: 'onMouseDown',
                    click: 'onClick'
                };
                const captureHandlerKeys = {
                    pointerdown: 'onPointerDownCapture',
                    mousedown: 'onMouseDownCapture',
                    click: 'onClickCapture'
                };
                const normalizeBubblesProp = bubbles => {
                    var _bubbles$escapeKey, _bubbles$outsidePress;
                    return {
                        escapeKeyBubbles: typeof bubbles === 'boolean' ? bubbles : (_bubbles$escapeKey = bubbles == null ? void 0 : bubbles.escapeKey) != null ? _bubbles$escapeKey : false,
                        outsidePressBubbles: typeof bubbles === 'boolean' ? bubbles : (_bubbles$outsidePress = bubbles == null ? void 0 : bubbles.outsidePress) != null ? _bubbles$outsidePress : true
                    };
                };
                /**
                 * Closes the floating element when a dismissal is requested — by default, when
                 * the user presses the `escape` key or outside of the floating element.
                 * @see https://floating-ui.com/docs/useDismiss
                 */
                function useDismiss(context, props) {
                    if (props === void 0) {
                        props = {};
                    }
                    const {
                        open,
                        onOpenChange,
                        events,
                        nodeId,
                        elements: {
                            reference,
                            domReference,
                            floating
                        },
                        dataRef
                    } = context;
                    const {
                        enabled = true,
                            escapeKey = true,
                            outsidePress: unstable_outsidePress = true,
                            outsidePressEvent = 'pointerdown',
                            referencePress = false,
                            referencePressEvent = 'pointerdown',
                            ancestorScroll = false,
                            bubbles
                    } = props;
                    const tree = useFloatingTree();
                    const nested = useFloatingParentNodeId() != null;
                    const outsidePressFn = useEffectEvent(typeof unstable_outsidePress === 'function' ? unstable_outsidePress : () => false);
                    const outsidePress = typeof unstable_outsidePress === 'function' ? outsidePressFn : unstable_outsidePress;
                    const insideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const {
                        escapeKeyBubbles,
                        outsidePressBubbles
                    } = normalizeBubblesProp(bubbles);
                    const closeOnEscapeKeyDown = useEffectEvent(event => {
                        if (!open || !enabled || !escapeKey || event.key !== 'Escape') {
                            return;
                        }
                        const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];
                        if (!escapeKeyBubbles) {
                            event.stopPropagation();
                            if (children.length > 0) {
                                let shouldDismiss = true;
                                children.forEach(child => {
                                    var _child$context;
                                    if ((_child$context = child.context) != null && _child$context.open && !child.context.dataRef.current.__escapeKeyBubbles) {
                                        shouldDismiss = false;
                                        return;
                                    }
                                });
                                if (!shouldDismiss) {
                                    return;
                                }
                            }
                        }
                        events.emit('dismiss', {
                            type: 'escapeKey',
                            data: {
                                returnFocus: {
                                    preventScroll: false
                                }
                            }
                        });
                        onOpenChange(false, (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isReactEvent)(event) ? event.nativeEvent : event);
                    });
                    const closeOnPressOutside = useEffectEvent(event => {
                        // Given developers can stop the propagation of the synthetic event,
                        // we can only be confident with a positive value.
                        const insideReactTree = insideReactTreeRef.current;
                        insideReactTreeRef.current = false;
                        if (insideReactTree) {
                            return;
                        }
                        if (typeof outsidePress === 'function' && !outsidePress(event)) {
                            return;
                        }
                        const target = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getTarget)(event);
                        const inertSelector = "[" + createAttribute('inert') + "]";
                        const markers = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating).querySelectorAll(inertSelector);
                        let targetRootAncestor = (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(target) ? target : null;
                        while (targetRootAncestor && !(0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isLastTraversableNode)(targetRootAncestor)) {
                            const nextParent = (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.getParentNode)(targetRootAncestor);
                            if (nextParent === (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating).body || !(0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(nextParent)) {
                                break;
                            } else {
                                targetRootAncestor = nextParent;
                            }
                        }

                        // Check if the click occurred on a third-party element injected after the
                        // floating element rendered.
                        if (markers.length && (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(target) && !(0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isRootElement)(target) &&
                            // Clicked on a direct ancestor (e.g. FloatingOverlay).
                            !(0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(target, floating) &&
                            // If the target root element contains none of the markers, then the
                            // element was injected after the floating element rendered.
                            Array.from(markers).every(marker => !(0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(targetRootAncestor, marker))) {
                            return;
                        }

                        // Check if the click occurred on the scrollbar
                        if ((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(target) && floating) {
                            // In Firefox, `target.scrollWidth > target.clientWidth` for inline
                            // elements.
                            const canScrollX = target.clientWidth > 0 && target.scrollWidth > target.clientWidth;
                            const canScrollY = target.clientHeight > 0 && target.scrollHeight > target.clientHeight;
                            let xCond = canScrollY && event.offsetX > target.clientWidth;

                            // In some browsers it is possible to change the <body> (or window)
                            // scrollbar to the left side, but is very rare and is difficult to
                            // check for. Plus, for modal dialogs with backdrops, it is more
                            // important that the backdrop is checked but not so much the window.
                            if (canScrollY) {
                                const isRTL = (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.getComputedStyle)(target).direction === 'rtl';
                                if (isRTL) {
                                    xCond = event.offsetX <= target.offsetWidth - target.clientWidth;
                                }
                            }
                            if (xCond || canScrollX && event.offsetY > target.clientHeight) {
                                return;
                            }
                        }
                        const targetIsInsideChildren = tree && getChildren(tree.nodesRef.current, nodeId).some(node => {
                            var _node$context;
                            return (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isEventTargetWithin)(event, (_node$context = node.context) == null ? void 0 : _node$context.elements.floating);
                        });
                        if ((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isEventTargetWithin)(event, floating) || (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isEventTargetWithin)(event, domReference) || targetIsInsideChildren) {
                            return;
                        }
                        const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];
                        if (children.length > 0) {
                            let shouldDismiss = true;
                            children.forEach(child => {
                                var _child$context2;
                                if ((_child$context2 = child.context) != null && _child$context2.open && !child.context.dataRef.current.__outsidePressBubbles) {
                                    shouldDismiss = false;
                                    return;
                                }
                            });
                            if (!shouldDismiss) {
                                return;
                            }
                        }
                        events.emit('dismiss', {
                            type: 'outsidePress',
                            data: {
                                returnFocus: nested ? {
                                    preventScroll: true
                                } : (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isVirtualClick)(event) || (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isVirtualPointerEvent)(event)
                            }
                        });
                        onOpenChange(false, event);
                    });
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!open || !enabled) {
                            return;
                        }
                        dataRef.current.__escapeKeyBubbles = escapeKeyBubbles;
                        dataRef.current.__outsidePressBubbles = outsidePressBubbles;

                        function onScroll(event) {
                            onOpenChange(false, event);
                        }
                        const doc = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating);
                        escapeKey && doc.addEventListener('keydown', closeOnEscapeKeyDown);
                        outsidePress && doc.addEventListener(outsidePressEvent, closeOnPressOutside);
                        let ancestors = [];
                        if (ancestorScroll) {
                            if ((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(domReference)) {
                                ancestors = (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.getOverflowAncestors)(domReference);
                            }
                            if ((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(floating)) {
                                ancestors = ancestors.concat((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.getOverflowAncestors)(floating));
                            }
                            if (!(0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(reference) && reference && reference.contextElement) {
                                ancestors = ancestors.concat((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.getOverflowAncestors)(reference.contextElement));
                            }
                        }

                        // Ignore the visual viewport for scrolling dismissal (allow pinch-zoom)
                        ancestors = ancestors.filter(ancestor => {
                            var _doc$defaultView;
                            return ancestor !== ((_doc$defaultView = doc.defaultView) == null ? void 0 : _doc$defaultView.visualViewport);
                        });
                        ancestors.forEach(ancestor => {
                            ancestor.addEventListener('scroll', onScroll, {
                                passive: true
                            });
                        });
                        return () => {
                            escapeKey && doc.removeEventListener('keydown', closeOnEscapeKeyDown);
                            outsidePress && doc.removeEventListener(outsidePressEvent, closeOnPressOutside);
                            ancestors.forEach(ancestor => {
                                ancestor.removeEventListener('scroll', onScroll);
                            });
                        };
                    }, [dataRef, floating, domReference, reference, escapeKey, outsidePress, outsidePressEvent, open, onOpenChange, ancestorScroll, enabled, escapeKeyBubbles, outsidePressBubbles, closeOnEscapeKeyDown, closeOnPressOutside]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        insideReactTreeRef.current = false;
                    }, [outsidePress, outsidePressEvent]);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (!enabled) {
                            return {};
                        }
                        return {
                            reference: {
                                onKeyDown: closeOnEscapeKeyDown,
                                [bubbleHandlerKeys[referencePressEvent]]: event => {
                                    if (referencePress) {
                                        events.emit('dismiss', {
                                            type: 'referencePress',
                                            data: {
                                                returnFocus: false
                                            }
                                        });
                                        onOpenChange(false, event.nativeEvent);
                                    }
                                }
                            },
                            floating: {
                                onKeyDown: closeOnEscapeKeyDown,
                                [captureHandlerKeys[outsidePressEvent]]: () => {
                                    insideReactTreeRef.current = true;
                                }
                            }
                        };
                    }, [enabled, events, referencePress, outsidePressEvent, referencePressEvent, onOpenChange, closeOnEscapeKeyDown]);
                }

                let devMessageSet;
                if (true) {
                    devMessageSet = /*#__PURE__*/ new Set();
                }

                /**
                 * Provides data to position a floating element and context to add interactions.
                 * @see https://floating-ui.com/docs/react
                 */
                function useFloating(options) {
                    var _options$elements2;
                    if (options === void 0) {
                        options = {};
                    }
                    const {
                        open = false,
                            onOpenChange: unstable_onOpenChange,
                            nodeId
                    } = options;
                    if (true) {
                        var _options$elements;
                        const err = 'Floating UI: Cannot pass a virtual element to the ' + '`elements.reference` option, as it must be a real DOM element. ' + 'Use `refs.setPositionReference` instead.';
                        if ((_options$elements = options.elements) != null && _options$elements.reference && !(0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(options.elements.reference)) {
                            var _devMessageSet;
                            if (!((_devMessageSet = devMessageSet) != null && _devMessageSet.has(err))) {
                                var _devMessageSet2;
                                (_devMessageSet2 = devMessageSet) == null ? void 0 : _devMessageSet2.add(err);
                                console.error(err);
                            }
                        }
                    }
                    const [_domReference, setDomReference] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);
                    const domReference = ((_options$elements2 = options.elements) == null ? void 0 : _options$elements2.reference) || _domReference;
                    const position = (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_1__.useFloating)(options);
                    const tree = useFloatingTree();
                    const onOpenChange = useEffectEvent((open, event) => {
                        if (open) {
                            dataRef.current.openEvent = event;
                        }
                        unstable_onOpenChange == null ? void 0 : unstable_onOpenChange(open, event);
                    });
                    const domReferenceRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const dataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});
                    const events = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => createPubSub())[0];
                    const floatingId = useId();
                    const setPositionReference = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {
                        const positionReference = (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(node) ? {
                            getBoundingClientRect: () => node.getBoundingClientRect(),
                            contextElement: node
                        } : node;
                        position.refs.setReference(positionReference);
                    }, [position.refs]);
                    const setReference = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {
                        if ((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(node) || node === null) {
                            domReferenceRef.current = node;
                            setDomReference(node);
                        }

                        // Backwards-compatibility for passing a virtual element to `reference`
                        // after it has set the DOM reference.
                        if ((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(position.refs.reference.current) || position.refs.reference.current === null ||
                            // Don't allow setting virtual elements using the old technique back to
                            // `null` to support `positionReference` + an unstable `reference`
                            // callback ref.
                            node !== null && !(0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(node)) {
                            position.refs.setReference(node);
                        }
                    }, [position.refs]);
                    const refs = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        ...position.refs,
                        setReference,
                        setPositionReference,
                        domReference: domReferenceRef
                    }), [position.refs, setReference, setPositionReference]);
                    const elements = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        ...position.elements,
                        domReference: domReference
                    }), [position.elements, domReference]);
                    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        ...position,
                        refs,
                        elements,
                        dataRef,
                        nodeId,
                        floatingId,
                        events,
                        open,
                        onOpenChange
                    }), [position, nodeId, floatingId, events, open, onOpenChange, refs, elements]);
                    index(() => {
                        const node = tree == null ? void 0 : tree.nodesRef.current.find(node => node.id === nodeId);
                        if (node) {
                            node.context = context;
                        }
                    });
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        ...position,
                        context,
                        refs,
                        elements
                    }), [position, refs, elements, context]);
                }

                /**
                 * Opens the floating element while the reference element has focus, like CSS
                 * `:focus`.
                 * @see https://floating-ui.com/docs/useFocus
                 */
                function useFocus(context, props) {
                    if (props === void 0) {
                        props = {};
                    }
                    const {
                        open,
                        onOpenChange,
                        dataRef,
                        events,
                        refs,
                        elements: {
                            floating,
                            domReference
                        }
                    } = context;
                    const {
                        enabled = true,
                            keyboardOnly = true
                    } = props;
                    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef('');
                    const blockFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const timeoutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!enabled) {
                            return;
                        }
                        const doc = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating);
                        const win = doc.defaultView || window;

                        // If the reference was focused and the user left the tab/window, and the
                        // floating element was not open, the focus should be blocked when they
                        // return to the tab/window.
                        function onBlur() {
                            if (!open && (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(domReference) && domReference === (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(domReference))) {
                                blockFocusRef.current = true;
                            }
                        }
                        win.addEventListener('blur', onBlur);
                        return () => {
                            win.removeEventListener('blur', onBlur);
                        };
                    }, [floating, domReference, open, enabled]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!enabled) {
                            return;
                        }

                        function onDismiss(payload) {
                            if (payload.type === 'referencePress' || payload.type === 'escapeKey') {
                                blockFocusRef.current = true;
                            }
                        }
                        events.on('dismiss', onDismiss);
                        return () => {
                            events.off('dismiss', onDismiss);
                        };
                    }, [events, enabled]);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        return () => {
                            clearTimeout(timeoutRef.current);
                        };
                    }, []);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (!enabled) {
                            return {};
                        }
                        return {
                            reference: {
                                onPointerDown(_ref) {
                                    let {
                                        pointerType
                                    } = _ref;
                                    pointerTypeRef.current = pointerType;
                                    blockFocusRef.current = !!(pointerType && keyboardOnly);
                                },
                                onMouseLeave() {
                                    blockFocusRef.current = false;
                                },
                                onFocus(event) {
                                    var _dataRef$current$open;
                                    if (blockFocusRef.current) {
                                        return;
                                    }

                                    // Dismiss with click should ignore the subsequent `focus` trigger,
                                    // but only if the click originated inside the reference element.
                                    if (event.type === 'focus' && ((_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type) === 'mousedown' && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isEventTargetWithin)(dataRef.current.openEvent, domReference)) {
                                        return;
                                    }
                                    onOpenChange(true, event.nativeEvent);
                                },
                                onBlur(event) {
                                    blockFocusRef.current = false;
                                    const relatedTarget = event.relatedTarget;

                                    // Hit the non-modal focus management portal guard. Focus will be
                                    // moved into the floating element immediately after.
                                    const movedToFocusGuard = (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(relatedTarget) && relatedTarget.hasAttribute(createAttribute('focus-guard')) && relatedTarget.getAttribute('data-type') === 'outside';

                                    // Wait for the window blur listener to fire.
                                    timeoutRef.current = setTimeout(() => {
                                        // When focusing the reference element (e.g. regular click), then
                                        // clicking into the floating element, prevent it from hiding.
                                        // Note: it must be focusable, e.g. `tabindex="-1"`.
                                        if ((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(refs.floating.current, relatedTarget) || (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(domReference, relatedTarget) || movedToFocusGuard) {
                                            return;
                                        }
                                        onOpenChange(false, event.nativeEvent);
                                    });
                                }
                            }
                        };
                    }, [enabled, keyboardOnly, domReference, refs, dataRef, onOpenChange]);
                }

                function mergeProps(userProps, propsList, elementKey) {
                    const map = new Map();
                    return {
                        ...(elementKey === 'floating' && {
                            tabIndex: -1
                        }),
                        ...userProps,
                        ...propsList.map(value => value ? value[elementKey] : null).concat(userProps).reduce((acc, props) => {
                            if (!props) {
                                return acc;
                            }
                            Object.entries(props).forEach(_ref => {
                                let [key, value] = _ref;
                                if (key.indexOf('on') === 0) {
                                    if (!map.has(key)) {
                                        map.set(key, []);
                                    }
                                    if (typeof value === 'function') {
                                        var _map$get;
                                        (_map$get = map.get(key)) == null ? void 0 : _map$get.push(value);
                                        acc[key] = function() {
                                            var _map$get2;
                                            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
                                                args[_key] = arguments[_key];
                                            }
                                            return (_map$get2 = map.get(key)) == null ? void 0 : _map$get2.map(fn => fn(...args)).find(val => val !== undefined);
                                        };
                                    }
                                } else {
                                    acc[key] = value;
                                }
                            });
                            return acc;
                        }, {})
                    };
                }

                /**
                 * Merges an array of interaction hooks' props into prop getters, allowing
                 * event handler functions to be composed together without overwriting one
                 * another.
                 * @see https://floating-ui.com/docs/react#interaction-hooks
                 */
                function useInteractions(propsList) {
                    if (propsList === void 0) {
                        propsList = [];
                    }
                    // The dependencies are a dynamic array, so we can't use the linter's
                    // suggestion to add it to the deps array.
                    const deps = propsList;
                    const getReferenceProps = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(userProps => mergeProps(userProps, propsList, 'reference'),
                        // eslint-disable-next-line react-hooks/exhaustive-deps
                        deps);
                    const getFloatingProps = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(userProps => mergeProps(userProps, propsList, 'floating'),
                        // eslint-disable-next-line react-hooks/exhaustive-deps
                        deps);
                    const getItemProps = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(userProps => mergeProps(userProps, propsList, 'item'),
                        // Granularly check for `item` changes, because the `getItemProps` getter
                        // should be as referentially stable as possible since it may be passed as
                        // a prop to many components. All `item` key values must therefore be
                        // memoized.
                        // eslint-disable-next-line react-hooks/exhaustive-deps
                        propsList.map(key => key == null ? void 0 : key.item));
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        getReferenceProps,
                        getFloatingProps,
                        getItemProps
                    }), [getReferenceProps, getFloatingProps, getItemProps]);
                }

                let isPreventScrollSupported = false;

                function doSwitch(orientation, vertical, horizontal) {
                    switch (orientation) {
                        case 'vertical':
                            return vertical;
                        case 'horizontal':
                            return horizontal;
                        default:
                            return vertical || horizontal;
                    }
                }

                function isMainOrientationKey(key, orientation) {
                    const vertical = key === ARROW_UP || key === ARROW_DOWN;
                    const horizontal = key === ARROW_LEFT || key === ARROW_RIGHT;
                    return doSwitch(orientation, vertical, horizontal);
                }

                function isMainOrientationToEndKey(key, orientation, rtl) {
                    const vertical = key === ARROW_DOWN;
                    const horizontal = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;
                    return doSwitch(orientation, vertical, horizontal) || key === 'Enter' || key == ' ' || key === '';
                }

                function isCrossOrientationOpenKey(key, orientation, rtl) {
                    const vertical = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;
                    const horizontal = key === ARROW_DOWN;
                    return doSwitch(orientation, vertical, horizontal);
                }

                function isCrossOrientationCloseKey(key, orientation, rtl) {
                    const vertical = rtl ? key === ARROW_RIGHT : key === ARROW_LEFT;
                    const horizontal = key === ARROW_UP;
                    return doSwitch(orientation, vertical, horizontal);
                }
                /**
                 * Adds arrow key-based navigation of a list of items, either using real DOM
                 * focus or virtual focus.
                 * @see https://floating-ui.com/docs/useListNavigation
                 */
                function useListNavigation(context, props) {
                    const {
                        open,
                        onOpenChange,
                        refs,
                        elements: {
                            domReference,
                            floating
                        }
                    } = context;
                    const {
                        listRef,
                        activeIndex,
                        onNavigate: unstable_onNavigate = () => {},
                        enabled = true,
                        selectedIndex = null,
                        allowEscape = false,
                        loop = false,
                        nested = false,
                        rtl = false,
                        virtual = false,
                        focusItemOnOpen = 'auto',
                        focusItemOnHover = true,
                        openOnArrowKeyDown = true,
                        disabledIndices = undefined,
                        orientation = 'vertical',
                        cols = 1,
                        scrollItemIntoView = true,
                        virtualItemRef
                    } = props;
                    if (true) {
                        if (allowEscape) {
                            if (!loop) {
                                console.warn(['Floating UI: `useListNavigation` looping must be enabled to allow', 'escaping.'].join(' '));
                            }
                            if (!virtual) {
                                console.warn(['Floating UI: `useListNavigation` must be virtual to allow', 'escaping.'].join(' '));
                            }
                        }
                        if (orientation === 'vertical' && cols > 1) {
                            console.warn(['Floating UI: In grid list navigation mode (`cols` > 1), the', '`orientation` should be either "horizontal" or "both".'].join(' '));
                        }
                    }
                    const parentId = useFloatingParentNodeId();
                    const tree = useFloatingTree();
                    const onNavigate = useEffectEvent(unstable_onNavigate);
                    const focusItemOnOpenRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(focusItemOnOpen);
                    const indexRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(selectedIndex != null ? selectedIndex : -1);
                    const keyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const isPointerModalityRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);
                    const previousOnNavigateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onNavigate);
                    const previousMountedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!!floating);
                    const forceSyncFocus = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const forceScrollIntoViewRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const disabledIndicesRef = useLatestRef(disabledIndices);
                    const latestOpenRef = useLatestRef(open);
                    const scrollItemIntoViewRef = useLatestRef(scrollItemIntoView);
                    const [activeId, setActiveId] = react__WEBPACK_IMPORTED_MODULE_0__.useState();
                    const [virtualId, setVirtualId] = react__WEBPACK_IMPORTED_MODULE_0__.useState();
                    const focusItem = useEffectEvent(function(listRef, indexRef, forceScrollIntoView) {
                        if (forceScrollIntoView === void 0) {
                            forceScrollIntoView = false;
                        }
                        const item = listRef.current[indexRef.current];
                        if (!item) return;
                        if (virtual) {
                            setActiveId(item.id);
                            tree == null ? void 0 : tree.events.emit('virtualfocus', item);
                            if (virtualItemRef) {
                                virtualItemRef.current = item;
                            }
                        } else {
                            enqueueFocus(item, {
                                preventScroll: true,
                                // Mac Safari does not move the virtual cursor unless the focus call
                                // is sync. However, for the very first focus call, we need to wait
                                // for the position to be ready in order to prevent unwanted
                                // scrolling. This means the virtual cursor will not move to the first
                                // item when first opening the floating element, but will on
                                // subsequent calls. `preventScroll` is supported in modern Safari,
                                // so we can use that instead.
                                // iOS Safari must be async or the first item will not be focused.
                                sync: (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isMac)() && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isSafari)() ? isPreventScrollSupported || forceSyncFocus.current : false
                            });
                        }
                        requestAnimationFrame(() => {
                            const scrollIntoViewOptions = scrollItemIntoViewRef.current;
                            const shouldScrollIntoView = scrollIntoViewOptions && item && (forceScrollIntoView || !isPointerModalityRef.current);
                            if (shouldScrollIntoView) {
                                // JSDOM doesn't support `.scrollIntoView()` but it's widely supported
                                // by all browsers.
                                item.scrollIntoView == null ? void 0 : item.scrollIntoView(typeof scrollIntoViewOptions === 'boolean' ? {
                                    block: 'nearest',
                                    inline: 'nearest'
                                } : scrollIntoViewOptions);
                            }
                        });
                    });
                    index(() => {
                        document.createElement('div').focus({
                            get preventScroll() {
                                isPreventScrollSupported = true;
                                return false;
                            }
                        });
                    }, []);

                    // Sync `selectedIndex` to be the `activeIndex` upon opening the floating
                    // element. Also, reset `activeIndex` upon closing the floating element.
                    index(() => {
                        if (!enabled) {
                            return;
                        }
                        if (open && floating) {
                            if (focusItemOnOpenRef.current && selectedIndex != null) {
                                // Regardless of the pointer modality, we want to ensure the selected
                                // item comes into view when the floating element is opened.
                                forceScrollIntoViewRef.current = true;
                                onNavigate(selectedIndex);
                            }
                        } else if (previousMountedRef.current) {
                            // Since the user can specify `onNavigate` conditionally
                            // (onNavigate: open ? setActiveIndex : setSelectedIndex),
                            // we store and call the previous function.
                            indexRef.current = -1;
                            previousOnNavigateRef.current(null);
                        }
                    }, [enabled, open, floating, selectedIndex, onNavigate]);

                    // Sync `activeIndex` to be the focused item while the floating element is
                    // open.
                    index(() => {
                        if (!enabled) {
                            return;
                        }
                        if (open && floating) {
                            if (activeIndex == null) {
                                forceSyncFocus.current = false;
                                if (selectedIndex != null) {
                                    return;
                                }

                                // Reset while the floating element was open (e.g. the list changed).
                                if (previousMountedRef.current) {
                                    indexRef.current = -1;
                                    focusItem(listRef, indexRef);
                                }

                                // Initial sync.
                                if (!previousMountedRef.current && focusItemOnOpenRef.current && (keyRef.current != null || focusItemOnOpenRef.current === true && keyRef.current == null)) {
                                    let runs = 0;
                                    const waitForListPopulated = () => {
                                        if (listRef.current[0] == null) {
                                            // Avoid letting the browser paint if possible on the first try,
                                            // otherwise use rAF. Don't try more than twice, since something
                                            // is wrong otherwise.
                                            if (runs < 2) {
                                                const scheduler = runs ? requestAnimationFrame : queueMicrotask;
                                                scheduler(waitForListPopulated);
                                            }
                                            runs++;
                                        } else {
                                            indexRef.current = keyRef.current == null || isMainOrientationToEndKey(keyRef.current, orientation, rtl) || nested ? getMinIndex(listRef, disabledIndicesRef.current) : getMaxIndex(listRef, disabledIndicesRef.current);
                                            keyRef.current = null;
                                            onNavigate(indexRef.current);
                                        }
                                    };
                                    waitForListPopulated();
                                }
                            } else if (!isIndexOutOfBounds(listRef, activeIndex)) {
                                indexRef.current = activeIndex;
                                focusItem(listRef, indexRef, forceScrollIntoViewRef.current);
                                forceScrollIntoViewRef.current = false;
                            }
                        }
                    }, [enabled, open, floating, activeIndex, selectedIndex, nested, listRef, orientation, rtl, onNavigate, focusItem, disabledIndicesRef]);

                    // Ensure the parent floating element has focus when a nested child closes
                    // to allow arrow key navigation to work after the pointer leaves the child.
                    index(() => {
                        var _nodes$find, _nodes$find$context;
                        if (!enabled || floating || !tree || virtual || !previousMountedRef.current) {
                            return;
                        }
                        const nodes = tree.nodesRef.current;
                        const parent = (_nodes$find = nodes.find(node => node.id === parentId)) == null ? void 0 : (_nodes$find$context = _nodes$find.context) == null ? void 0 : _nodes$find$context.elements.floating;
                        const activeEl = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getDocument)(floating));
                        const treeContainsActiveEl = nodes.some(node => node.context && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(node.context.elements.floating, activeEl));
                        if (parent && !treeContainsActiveEl && isPointerModalityRef.current) {
                            parent.focus({
                                preventScroll: true
                            });
                        }
                    }, [enabled, floating, tree, parentId, virtual]);
                    index(() => {
                        if (!enabled || !tree || !virtual || parentId) return;

                        function handleVirtualFocus(item) {
                            setVirtualId(item.id);
                            if (virtualItemRef) {
                                virtualItemRef.current = item;
                            }
                        }
                        tree.events.on('virtualfocus', handleVirtualFocus);
                        return () => {
                            tree.events.off('virtualfocus', handleVirtualFocus);
                        };
                    }, [enabled, tree, virtual, parentId, virtualItemRef]);
                    index(() => {
                        previousOnNavigateRef.current = onNavigate;
                        previousMountedRef.current = !!floating;
                    });
                    index(() => {
                        if (!open) {
                            keyRef.current = null;
                        }
                    }, [open]);
                    const hasActiveIndex = activeIndex != null;
                    const item = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        function syncCurrentTarget(currentTarget) {
                            if (!open) return;
                            const index = listRef.current.indexOf(currentTarget);
                            if (index !== -1) {
                                onNavigate(index);
                            }
                        }
                        const props = {
                            onFocus(_ref) {
                                let {
                                    currentTarget
                                } = _ref;
                                syncCurrentTarget(currentTarget);
                            },
                            onClick: _ref2 => {
                                let {
                                    currentTarget
                                } = _ref2;
                                return currentTarget.focus({
                                    preventScroll: true
                                });
                            },
                            // Safari
                            ...(focusItemOnHover && {
                                onMouseMove(_ref3) {
                                    let {
                                        currentTarget
                                    } = _ref3;
                                    syncCurrentTarget(currentTarget);
                                },
                                onPointerLeave(_ref4) {
                                    let {
                                        pointerType
                                    } = _ref4;
                                    if (!isPointerModalityRef.current || pointerType === 'touch') {
                                        return;
                                    }
                                    indexRef.current = -1;
                                    focusItem(listRef, indexRef);
                                    onNavigate(null);
                                    if (!virtual) {
                                        enqueueFocus(refs.floating.current, {
                                            preventScroll: true
                                        });
                                    }
                                }
                            })
                        };
                        return props;
                    }, [open, refs, focusItem, focusItemOnHover, listRef, onNavigate, virtual]);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (!enabled) {
                            return {};
                        }
                        const disabledIndices = disabledIndicesRef.current;

                        function onKeyDown(event) {
                            isPointerModalityRef.current = false;
                            forceSyncFocus.current = true;

                            // If the floating element is animating out, ignore navigation. Otherwise,
                            // the `activeIndex` gets set to 0 despite not being open so the next time
                            // the user ArrowDowns, the first item won't be focused.
                            if (!latestOpenRef.current && event.currentTarget === refs.floating.current) {
                                return;
                            }
                            if (nested && isCrossOrientationCloseKey(event.key, orientation, rtl)) {
                                (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                onOpenChange(false, event.nativeEvent);
                                if ((0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(domReference) && !virtual) {
                                    domReference.focus();
                                }
                                return;
                            }
                            const currentIndex = indexRef.current;
                            const minIndex = getMinIndex(listRef, disabledIndices);
                            const maxIndex = getMaxIndex(listRef, disabledIndices);
                            if (event.key === 'Home') {
                                (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                indexRef.current = minIndex;
                                onNavigate(indexRef.current);
                            }
                            if (event.key === 'End') {
                                (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                indexRef.current = maxIndex;
                                onNavigate(indexRef.current);
                            }

                            // Grid navigation.
                            if (cols > 1) {
                                indexRef.current = getGridNavigatedIndex(listRef, {
                                    event,
                                    orientation,
                                    loop,
                                    cols,
                                    disabledIndices,
                                    minIndex,
                                    maxIndex,
                                    prevIndex: indexRef.current,
                                    stopEvent: true
                                });
                                onNavigate(indexRef.current);
                                if (orientation === 'both') {
                                    return;
                                }
                            }
                            if (isMainOrientationKey(event.key, orientation)) {
                                (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);

                                // Reset the index if no item is focused.
                                if (open && !virtual && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.activeElement)(event.currentTarget.ownerDocument) === event.currentTarget) {
                                    indexRef.current = isMainOrientationToEndKey(event.key, orientation, rtl) ? minIndex : maxIndex;
                                    onNavigate(indexRef.current);
                                    return;
                                }
                                if (isMainOrientationToEndKey(event.key, orientation, rtl)) {
                                    if (loop) {
                                        indexRef.current = currentIndex >= maxIndex ? allowEscape && currentIndex !== listRef.current.length ? -1 : minIndex : findNonDisabledIndex(listRef, {
                                            startingIndex: currentIndex,
                                            disabledIndices
                                        });
                                    } else {
                                        indexRef.current = Math.min(maxIndex, findNonDisabledIndex(listRef, {
                                            startingIndex: currentIndex,
                                            disabledIndices
                                        }));
                                    }
                                } else {
                                    if (loop) {
                                        indexRef.current = currentIndex <= minIndex ? allowEscape && currentIndex !== -1 ? listRef.current.length : maxIndex : findNonDisabledIndex(listRef, {
                                            startingIndex: currentIndex,
                                            decrement: true,
                                            disabledIndices
                                        });
                                    } else {
                                        indexRef.current = Math.max(minIndex, findNonDisabledIndex(listRef, {
                                            startingIndex: currentIndex,
                                            decrement: true,
                                            disabledIndices
                                        }));
                                    }
                                }
                                if (isIndexOutOfBounds(listRef, indexRef.current)) {
                                    onNavigate(null);
                                } else {
                                    onNavigate(indexRef.current);
                                }
                            }
                        }

                        function checkVirtualMouse(event) {
                            if (focusItemOnOpen === 'auto' && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isVirtualClick)(event.nativeEvent)) {
                                focusItemOnOpenRef.current = true;
                            }
                        }

                        function checkVirtualPointer(event) {
                            // `pointerdown` fires first, reset the state then perform the checks.
                            focusItemOnOpenRef.current = focusItemOnOpen;
                            if (focusItemOnOpen === 'auto' && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.isVirtualPointerEvent)(event.nativeEvent)) {
                                focusItemOnOpenRef.current = true;
                            }
                        }
                        const ariaActiveDescendantProp = virtual && open && hasActiveIndex && {
                            'aria-activedescendant': virtualId || activeId
                        };
                        const activeItem = listRef.current.find(item => (item == null ? void 0 : item.id) === activeId);
                        return {
                            reference: {
                                ...ariaActiveDescendantProp,
                                onKeyDown(event) {
                                    isPointerModalityRef.current = false;
                                    const isArrowKey = event.key.indexOf('Arrow') === 0;
                                    const isCrossOpenKey = isCrossOrientationOpenKey(event.key, orientation, rtl);
                                    const isCrossCloseKey = isCrossOrientationCloseKey(event.key, orientation, rtl);
                                    const isMainKey = isMainOrientationKey(event.key, orientation);
                                    const isNavigationKey = (nested ? isCrossOpenKey : isMainKey) || event.key === 'Enter' || event.key.trim() === '';
                                    if (virtual && open) {
                                        const rootNode = tree == null ? void 0 : tree.nodesRef.current.find(node => node.parentId == null);
                                        const deepestNode = tree && rootNode ? getDeepestNode(tree.nodesRef.current, rootNode.id) : null;
                                        if (isArrowKey && deepestNode && virtualItemRef) {
                                            const eventObject = new KeyboardEvent('keydown', {
                                                key: event.key,
                                                bubbles: true
                                            });
                                            if (isCrossOpenKey || isCrossCloseKey) {
                                                var _deepestNode$context, _deepestNode$context2;
                                                const isCurrentTarget = ((_deepestNode$context = deepestNode.context) == null ? void 0 : _deepestNode$context.elements.domReference) === event.currentTarget;
                                                const dispatchItem = isCrossCloseKey && !isCurrentTarget ? (_deepestNode$context2 = deepestNode.context) == null ? void 0 : _deepestNode$context2.elements.domReference : isCrossOpenKey ? activeItem : null;
                                                if (dispatchItem) {
                                                    (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                                    dispatchItem.dispatchEvent(eventObject);
                                                    setVirtualId(undefined);
                                                }
                                            }
                                            if (isMainKey && deepestNode.context) {
                                                if (deepestNode.context.open && deepestNode.parentId && event.currentTarget !== deepestNode.context.elements.domReference) {
                                                    var _deepestNode$context$;
                                                    (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                                    (_deepestNode$context$ = deepestNode.context.elements.domReference) == null ? void 0 : _deepestNode$context$.dispatchEvent(eventObject);
                                                    return;
                                                }
                                            }
                                        }
                                        return onKeyDown(event);
                                    }

                                    // If a floating element should not open on arrow key down, avoid
                                    // setting `activeIndex` while it's closed.
                                    if (!open && !openOnArrowKeyDown && isArrowKey) {
                                        return;
                                    }
                                    if (isNavigationKey) {
                                        keyRef.current = nested && isMainKey ? null : event.key;
                                    }
                                    if (nested) {
                                        if (isCrossOpenKey) {
                                            (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                            if (open) {
                                                indexRef.current = getMinIndex(listRef, disabledIndices);
                                                onNavigate(indexRef.current);
                                            } else {
                                                onOpenChange(true, event.nativeEvent);
                                            }
                                        }
                                        return;
                                    }
                                    if (isMainKey) {
                                        if (selectedIndex != null) {
                                            indexRef.current = selectedIndex;
                                        }
                                        (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                        if (!open && openOnArrowKeyDown) {
                                            onOpenChange(true, event.nativeEvent);
                                        } else {
                                            onKeyDown(event);
                                        }
                                        if (open) {
                                            onNavigate(indexRef.current);
                                        }
                                    }
                                },
                                onFocus() {
                                    if (open) {
                                        onNavigate(null);
                                    }
                                },
                                onPointerDown: checkVirtualPointer,
                                onMouseDown: checkVirtualMouse,
                                onClick: checkVirtualMouse
                            },
                            floating: {
                                'aria-orientation': orientation === 'both' ? undefined : orientation,
                                ...ariaActiveDescendantProp,
                                onKeyDown,
                                onPointerMove() {
                                    isPointerModalityRef.current = true;
                                }
                            },
                            item
                        };
                    }, [domReference, refs, activeId, virtualId, disabledIndicesRef, latestOpenRef, listRef, enabled, orientation, rtl, virtual, open, hasActiveIndex, nested, selectedIndex, openOnArrowKeyDown, allowEscape, cols, loop, focusItemOnOpen, onNavigate, onOpenChange, item, tree, virtualItemRef]);
                }

                /**
                 * Adds base screen reader props to the reference and floating elements for a
                 * given floating element `role`.
                 * @see https://floating-ui.com/docs/useRole
                 */
                function useRole(context, props) {
                    if (props === void 0) {
                        props = {};
                    }
                    const {
                        open,
                        floatingId
                    } = context;
                    const {
                        enabled = true,
                            role = 'dialog'
                    } = props;
                    const referenceId = useId();
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        const floatingProps = {
                            id: floatingId,
                            role
                        };
                        if (!enabled) {
                            return {};
                        }
                        if (role === 'tooltip') {
                            return {
                                reference: {
                                    'aria-describedby': open ? floatingId : undefined
                                },
                                floating: floatingProps
                            };
                        }
                        return {
                            reference: {
                                'aria-expanded': open ? 'true' : 'false',
                                'aria-haspopup': role === 'alertdialog' ? 'dialog' : role,
                                'aria-controls': open ? floatingId : undefined,
                                ...(role === 'listbox' && {
                                    role: 'combobox'
                                }),
                                ...(role === 'menu' && {
                                    id: referenceId
                                })
                            },
                            floating: {
                                ...floatingProps,
                                ...(role === 'menu' && {
                                    'aria-labelledby': referenceId
                                })
                            }
                        };
                    }, [enabled, role, open, floatingId, referenceId]);
                }

                // Converts a JS style key like `backgroundColor` to a CSS transition-property
                // like `background-color`.
                const camelCaseToKebabCase = str => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? '-' : '') + $.toLowerCase());

                function execWithArgsOrReturn(valueOrFn, args) {
                    return typeof valueOrFn === 'function' ? valueOrFn(args) : valueOrFn;
                }

                function useDelayUnmount(open, durationMs) {
                    const [isMounted, setIsMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(open);
                    if (open && !isMounted) {
                        setIsMounted(true);
                    }
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!open) {
                            const timeout = setTimeout(() => setIsMounted(false), durationMs);
                            return () => clearTimeout(timeout);
                        }
                    }, [open, durationMs]);
                    return isMounted;
                }
                /**
                 * Provides a status string to apply CSS transitions to a floating element,
                 * correctly handling placement-aware transitions.
                 * @see https://floating-ui.com/docs/useTransition#usetransitionstatus
                 */
                function useTransitionStatus(context, props) {
                    if (props === void 0) {
                        props = {};
                    }
                    const {
                        open,
                        elements: {
                            floating
                        }
                    } = context;
                    const {
                        duration = 250
                    } = props;
                    const isNumberDuration = typeof duration === 'number';
                    const closeDuration = (isNumberDuration ? duration : duration.close) || 0;
                    const [initiated, setInitiated] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);
                    const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState('unmounted');
                    const isMounted = useDelayUnmount(open, closeDuration);

                    // `initiated` check prevents this `setState` call from breaking
                    // <FloatingPortal />. This call is necessary to ensure subsequent opens
                    // after the initial one allows the correct side animation to play when the
                    // placement has changed.
                    index(() => {
                        if (initiated && !isMounted) {
                            setStatus('unmounted');
                        }
                    }, [initiated, isMounted]);
                    index(() => {
                        if (!floating) return;
                        if (open) {
                            setStatus('initial');
                            const frame = requestAnimationFrame(() => {
                                setStatus('open');
                            });
                            return () => {
                                cancelAnimationFrame(frame);
                            };
                        } else {
                            setInitiated(true);
                            setStatus('close');
                        }
                    }, [open, floating]);
                    return {
                        isMounted,
                        status
                    };
                }
                /**
                 * Provides styles to apply CSS transitions to a floating element, correctly
                 * handling placement-aware transitions. Wrapper around `useTransitionStatus`.
                 * @see https://floating-ui.com/docs/useTransition#usetransitionstyles
                 */
                function useTransitionStyles(context, props) {
                    if (props === void 0) {
                        props = {};
                    }
                    const {
                        initial: unstable_initial = {
                            opacity: 0
                        },
                        open: unstable_open,
                        close: unstable_close,
                        common: unstable_common,
                        duration = 250
                    } = props;
                    const placement = context.placement;
                    const side = placement.split('-')[0];
                    const fnArgs = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({
                        side,
                        placement
                    }), [side, placement]);
                    const isNumberDuration = typeof duration === 'number';
                    const openDuration = (isNumberDuration ? duration : duration.open) || 0;
                    const closeDuration = (isNumberDuration ? duration : duration.close) || 0;
                    const [styles, setStyles] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => ({
                        ...execWithArgsOrReturn(unstable_common, fnArgs),
                        ...execWithArgsOrReturn(unstable_initial, fnArgs)
                    }));
                    const {
                        isMounted,
                        status
                    } = useTransitionStatus(context, {
                        duration
                    });
                    const initialRef = useLatestRef(unstable_initial);
                    const openRef = useLatestRef(unstable_open);
                    const closeRef = useLatestRef(unstable_close);
                    const commonRef = useLatestRef(unstable_common);
                    index(() => {
                        const initialStyles = execWithArgsOrReturn(initialRef.current, fnArgs);
                        const closeStyles = execWithArgsOrReturn(closeRef.current, fnArgs);
                        const commonStyles = execWithArgsOrReturn(commonRef.current, fnArgs);
                        const openStyles = execWithArgsOrReturn(openRef.current, fnArgs) || Object.keys(initialStyles).reduce((acc, key) => {
                            acc[key] = '';
                            return acc;
                        }, {});
                        if (status === 'initial') {
                            setStyles(styles => ({
                                transitionProperty: styles.transitionProperty,
                                ...commonStyles,
                                ...initialStyles
                            }));
                        }
                        if (status === 'open') {
                            setStyles({
                                transitionProperty: Object.keys(openStyles).map(camelCaseToKebabCase).join(','),
                                transitionDuration: openDuration + "ms",
                                ...commonStyles,
                                ...openStyles
                            });
                        }
                        if (status === 'close') {
                            const styles = closeStyles || initialStyles;
                            setStyles({
                                transitionProperty: Object.keys(styles).map(camelCaseToKebabCase).join(','),
                                transitionDuration: closeDuration + "ms",
                                ...commonStyles,
                                ...styles
                            });
                        }
                    }, [closeDuration, closeRef, initialRef, openRef, commonRef, openDuration, status, fnArgs]);
                    return {
                        isMounted,
                        styles
                    };
                }

                /**
                 * Provides a matching callback that can be used to focus an item as the user
                 * types, often used in tandem with `useListNavigation()`.
                 * @see https://floating-ui.com/docs/useTypeahead
                 */
                function useTypeahead(context, props) {
                    var _ref;
                    const {
                        open,
                        dataRef
                    } = context;
                    const {
                        listRef,
                        activeIndex,
                        onMatch: unstable_onMatch,
                        onTypingChange: unstable_onTypingChange,
                        enabled = true,
                        findMatch = null,
                        resetMs = 750,
                        ignoreKeys = [],
                        selectedIndex = null
                    } = props;
                    const timeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();
                    const stringRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef('');
                    const prevIndexRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef((_ref = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref : -1);
                    const matchIndexRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const onMatch = useEffectEvent(unstable_onMatch);
                    const onTypingChange = useEffectEvent(unstable_onTypingChange);
                    const findMatchRef = useLatestRef(findMatch);
                    const ignoreKeysRef = useLatestRef(ignoreKeys);
                    index(() => {
                        if (open) {
                            clearTimeout(timeoutIdRef.current);
                            matchIndexRef.current = null;
                            stringRef.current = '';
                        }
                    }, [open]);
                    index(() => {
                        // Sync arrow key navigation but not typeahead navigation.
                        if (open && stringRef.current === '') {
                            var _ref2;
                            prevIndexRef.current = (_ref2 = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref2 : -1;
                        }
                    }, [open, selectedIndex, activeIndex]);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (!enabled) {
                            return {};
                        }

                        function setTypingChange(value) {
                            if (value) {
                                if (!dataRef.current.typing) {
                                    dataRef.current.typing = value;
                                    onTypingChange(value);
                                }
                            } else {
                                if (dataRef.current.typing) {
                                    dataRef.current.typing = value;
                                    onTypingChange(value);
                                }
                            }
                        }

                        function getMatchingIndex(list, orderedList, string) {
                            const str = findMatchRef.current ? findMatchRef.current(orderedList, string) : orderedList.find(text => (text == null ? void 0 : text.toLocaleLowerCase().indexOf(string.toLocaleLowerCase())) === 0);
                            return str ? list.indexOf(str) : -1;
                        }

                        function onKeyDown(event) {
                            const listContent = listRef.current;
                            if (stringRef.current.length > 0 && stringRef.current[0] !== ' ') {
                                if (getMatchingIndex(listContent, listContent, stringRef.current) === -1) {
                                    setTypingChange(false);
                                } else if (event.key === ' ') {
                                    (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                }
                            }
                            if (listContent == null || ignoreKeysRef.current.includes(event.key) ||
                                // Character key.
                                event.key.length !== 1 ||
                                // Modifier key.
                                event.ctrlKey || event.metaKey || event.altKey) {
                                return;
                            }
                            if (open && event.key !== ' ') {
                                (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.stopEvent)(event);
                                setTypingChange(true);
                            }

                            // Bail out if the list contains a word like "llama" or "aaron". TODO:
                            // allow it in this case, too.
                            const allowRapidSuccessionOfFirstLetter = listContent.every(text => {
                                var _text$, _text$2;
                                return text ? ((_text$ = text[0]) == null ? void 0 : _text$.toLocaleLowerCase()) !== ((_text$2 = text[1]) == null ? void 0 : _text$2.toLocaleLowerCase()) : true;
                            });

                            // Allows the user to cycle through items that start with the same letter
                            // in rapid succession.
                            if (allowRapidSuccessionOfFirstLetter && stringRef.current === event.key) {
                                stringRef.current = '';
                                prevIndexRef.current = matchIndexRef.current;
                            }
                            stringRef.current += event.key;
                            clearTimeout(timeoutIdRef.current);
                            timeoutIdRef.current = setTimeout(() => {
                                stringRef.current = '';
                                prevIndexRef.current = matchIndexRef.current;
                                setTypingChange(false);
                            }, resetMs);
                            const prevIndex = prevIndexRef.current;
                            const index = getMatchingIndex(listContent, [...listContent.slice((prevIndex || 0) + 1), ...listContent.slice(0, (prevIndex || 0) + 1)], stringRef.current);
                            if (index !== -1) {
                                onMatch(index);
                                matchIndexRef.current = index;
                            } else if (event.key !== ' ') {
                                stringRef.current = '';
                                setTypingChange(false);
                            }
                        }
                        return {
                            reference: {
                                onKeyDown
                            },
                            floating: {
                                onKeyDown,
                                onKeyUp(event) {
                                    if (event.key === ' ') {
                                        setTypingChange(false);
                                    }
                                }
                            }
                        };
                    }, [enabled, open, dataRef, listRef, resetMs, ignoreKeysRef, findMatchRef, onMatch, onTypingChange]);
                }

                function getArgsWithCustomFloatingHeight(state, height) {
                    return {
                        ...state,
                        rects: {
                            ...state.rects,
                            floating: {
                                ...state.rects.floating,
                                height
                            }
                        }
                    };
                }
                /**
                 * Positions the floating element such that an inner element inside
                 * of it is anchored to the reference element.
                 * @see https://floating-ui.com/docs/inner
                 */
                const inner = props => ({
                    name: 'inner',
                    options: props,
                    async fn(state) {
                        const {
                            listRef,
                            overflowRef,
                            onFallbackChange,
                            offset: innerOffset = 0,
                            index = 0,
                            minItemsVisible = 4,
                            referenceOverflowThreshold = 0,
                            scrollRef,
                            ...detectOverflowOptions
                        } = props;
                        const {
                            rects,
                            elements: {
                                floating
                            }
                        } = state;
                        const item = listRef.current[index];
                        if (true) {
                            if (!state.placement.startsWith('bottom')) {
                                console.warn(['Floating UI: `placement` side must be "bottom" when using the', '`inner` middleware.'].join(' '));
                            }
                        }
                        if (!item) {
                            return {};
                        }
                        const nextArgs = {
                            ...state,
                            ...(await (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.offset)(-item.offsetTop - floating.clientTop - rects.reference.height / 2 - item.offsetHeight / 2 - innerOffset).fn(state))
                        };
                        const el = (scrollRef == null ? void 0 : scrollRef.current) || floating;
                        const overflow = await (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.detectOverflow)(getArgsWithCustomFloatingHeight(nextArgs, el.scrollHeight), detectOverflowOptions);
                        const refOverflow = await (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.detectOverflow)(nextArgs, {
                            ...detectOverflowOptions,
                            elementContext: 'reference'
                        });
                        const diffY = Math.max(0, overflow.top);
                        const nextY = nextArgs.y + diffY;
                        const maxHeight = Math.max(0, el.scrollHeight - diffY - Math.max(0, overflow.bottom));
                        el.style.maxHeight = maxHeight + "px";
                        el.scrollTop = diffY;

                        // There is not enough space, fallback to standard anchored positioning
                        if (onFallbackChange) {
                            if (el.offsetHeight < item.offsetHeight * Math.min(minItemsVisible, listRef.current.length - 1) - 1 || refOverflow.top >= -referenceOverflowThreshold || refOverflow.bottom >= -referenceOverflowThreshold) {
                                (0, react_dom__WEBPACK_IMPORTED_MODULE_5__.flushSync)(() => onFallbackChange(true));
                            } else {
                                (0, react_dom__WEBPACK_IMPORTED_MODULE_5__.flushSync)(() => onFallbackChange(false));
                            }
                        }
                        if (overflowRef) {
                            overflowRef.current = await (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.detectOverflow)(getArgsWithCustomFloatingHeight({
                                ...nextArgs,
                                y: nextY
                            }, el.offsetHeight), detectOverflowOptions);
                        }
                        return {
                            y: nextY
                        };
                    }
                });
                /**
                 * Changes the `inner` middleware's `offset` upon a `wheel` event to
                 * expand the floating element's height, revealing more list items.
                 * @see https://floating-ui.com/docs/inner
                 */
                function useInnerOffset(context, props) {
                    const {
                        open,
                        elements
                    } = context;
                    const {
                        enabled = true,
                            overflowRef,
                            scrollRef,
                            onChange: unstable_onChange
                    } = props;
                    const onChange = useEffectEvent(unstable_onChange);
                    const controlledScrollingRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
                    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    const initialOverflowRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
                    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {
                        if (!enabled) {
                            return;
                        }

                        function onWheel(e) {
                            if (e.ctrlKey || !el || overflowRef.current == null) {
                                return;
                            }
                            const dY = e.deltaY;
                            const isAtTop = overflowRef.current.top >= -0.5;
                            const isAtBottom = overflowRef.current.bottom >= -0.5;
                            const remainingScroll = el.scrollHeight - el.clientHeight;
                            const sign = dY < 0 ? -1 : 1;
                            const method = dY < 0 ? 'max' : 'min';
                            if (el.scrollHeight <= el.clientHeight) {
                                return;
                            }
                            if (!isAtTop && dY > 0 || !isAtBottom && dY < 0) {
                                e.preventDefault();
                                (0, react_dom__WEBPACK_IMPORTED_MODULE_5__.flushSync)(() => {
                                    onChange(d => d + Math[method](dY, remainingScroll * sign));
                                });
                            } else if (/firefox/i.test((0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getUserAgent)())) {
                                // Needed to propagate scrolling during momentum scrolling phase once
                                // it gets limited by the boundary. UX improvement, not critical.
                                el.scrollTop += dY;
                            }
                        }
                        const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;
                        if (open && el) {
                            el.addEventListener('wheel', onWheel);

                            // Wait for the position to be ready.
                            requestAnimationFrame(() => {
                                prevScrollTopRef.current = el.scrollTop;
                                if (overflowRef.current != null) {
                                    initialOverflowRef.current = {
                                        ...overflowRef.current
                                    };
                                }
                            });
                            return () => {
                                prevScrollTopRef.current = null;
                                initialOverflowRef.current = null;
                                el.removeEventListener('wheel', onWheel);
                            };
                        }
                    }, [enabled, open, elements.floating, overflowRef, scrollRef, onChange]);
                    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {
                        if (!enabled) {
                            return {};
                        }
                        return {
                            floating: {
                                onKeyDown() {
                                    controlledScrollingRef.current = true;
                                },
                                onWheel() {
                                    controlledScrollingRef.current = false;
                                },
                                onPointerMove() {
                                    controlledScrollingRef.current = false;
                                },
                                onScroll() {
                                    const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;
                                    if (!overflowRef.current || !el || !controlledScrollingRef.current) {
                                        return;
                                    }
                                    if (prevScrollTopRef.current !== null) {
                                        const scrollDiff = el.scrollTop - prevScrollTopRef.current;
                                        if (overflowRef.current.bottom < -0.5 && scrollDiff < -1 || overflowRef.current.top < -0.5 && scrollDiff > 1) {
                                            (0, react_dom__WEBPACK_IMPORTED_MODULE_5__.flushSync)(() => onChange(d => d + scrollDiff));
                                        }
                                    }

                                    // [Firefox] Wait for the height change to have been applied.
                                    requestAnimationFrame(() => {
                                        prevScrollTopRef.current = el.scrollTop;
                                    });
                                }
                            }
                        };
                    }, [enabled, overflowRef, elements.floating, scrollRef, onChange]);
                }

                function isPointInPolygon(point, polygon) {
                    const [x, y] = point;
                    let isInside = false;
                    const length = polygon.length;
                    for (let i = 0, j = length - 1; i < length; j = i++) {
                        const [xi, yi] = polygon[i] || [0, 0];
                        const [xj, yj] = polygon[j] || [0, 0];
                        const intersect = yi >= y !== yj >= y && x <= (xj - xi) * (y - yi) / (yj - yi) + xi;
                        if (intersect) {
                            isInside = !isInside;
                        }
                    }
                    return isInside;
                }

                function isInside(point, rect) {
                    return point[0] >= rect.x && point[0] <= rect.x + rect.width && point[1] >= rect.y && point[1] <= rect.y + rect.height;
                }
                /**
                 * Generates a safe polygon area that the user can traverse without closing the
                 * floating element once leaving the reference element.
                 * @see https://floating-ui.com/docs/useHover#safePolygon
                 */
                function safePolygon(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    const {
                        buffer = 0.5,
                            blockPointerEvents = false,
                            requireIntent = true
                    } = options;
                    let timeoutId;
                    let hasLanded = false;
                    let lastX = null;
                    let lastY = null;
                    let lastCursorTime = performance.now();

                    function getCursorSpeed(x, y) {
                        const currentTime = performance.now();
                        const elapsedTime = currentTime - lastCursorTime;
                        if (lastX === null || lastY === null || elapsedTime === 0) {
                            lastX = x;
                            lastY = y;
                            lastCursorTime = currentTime;
                            return null;
                        }
                        const deltaX = x - lastX;
                        const deltaY = y - lastY;
                        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                        const speed = distance / elapsedTime; // px / ms

                        lastX = x;
                        lastY = y;
                        lastCursorTime = currentTime;
                        return speed;
                    }
                    const fn = _ref => {
                        let {
                            x,
                            y,
                            placement,
                            elements,
                            onClose,
                            nodeId,
                            tree
                        } = _ref;
                        return function onMouseMove(event) {
                            function close() {
                                clearTimeout(timeoutId);
                                onClose();
                            }
                            clearTimeout(timeoutId);
                            if (!elements.domReference || !elements.floating || placement == null || x == null || y == null) {
                                return;
                            }
                            const {
                                clientX,
                                clientY
                            } = event;
                            const clientPoint = [clientX, clientY];
                            const target = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.getTarget)(event);
                            const isLeave = event.type === 'mouseleave';
                            const isOverFloatingEl = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(elements.floating, target);
                            const isOverReferenceEl = (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(elements.domReference, target);
                            const refRect = elements.domReference.getBoundingClientRect();
                            const rect = elements.floating.getBoundingClientRect();
                            const side = placement.split('-')[0];
                            const cursorLeaveFromRight = x > rect.right - rect.width / 2;
                            const cursorLeaveFromBottom = y > rect.bottom - rect.height / 2;
                            const isOverReferenceRect = isInside(clientPoint, refRect);
                            const isFloatingWider = rect.width > refRect.width;
                            const isFloatingTaller = rect.height > refRect.height;
                            const left = (isFloatingWider ? refRect : rect).left;
                            const right = (isFloatingWider ? refRect : rect).right;
                            const top = (isFloatingTaller ? refRect : rect).top;
                            const bottom = (isFloatingTaller ? refRect : rect).bottom;
                            if (isOverFloatingEl) {
                                hasLanded = true;
                                if (!isLeave) {
                                    return;
                                }
                            }
                            if (isOverReferenceEl) {
                                hasLanded = false;
                            }
                            if (isOverReferenceEl && !isLeave) {
                                hasLanded = true;
                                return;
                            }

                            // Prevent overlapping floating element from being stuck in an open-close
                            // loop: https://github.com/floating-ui/floating-ui/issues/1910
                            if (isLeave && (0, _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_4__.isElement)(event.relatedTarget) && (0, _floating_ui_utils_react__WEBPACK_IMPORTED_MODULE_6__.contains)(elements.floating, event.relatedTarget)) {
                                return;
                            }

                            // If any nested child is open, abort.
                            if (tree && getChildren(tree.nodesRef.current, nodeId).some(_ref2 => {
                                    let {
                                        context
                                    } = _ref2;
                                    return context == null ? void 0 : context.open;
                                })) {
                                return;
                            }

                            // If the pointer is leaving from the opposite side, the "buffer" logic
                            // creates a point where the floating element remains open, but should be
                            // ignored.
                            // A constant of 1 handles floating point rounding errors.
                            if (side === 'top' && y >= refRect.bottom - 1 || side === 'bottom' && y <= refRect.top + 1 || side === 'left' && x >= refRect.right - 1 || side === 'right' && x <= refRect.left + 1) {
                                return close();
                            }

                            // Ignore when the cursor is within the rectangular trough between the
                            // two elements. Since the triangle is created from the cursor point,
                            // which can start beyond the ref element's edge, traversing back and
                            // forth from the ref to the floating element can cause it to close. This
                            // ensures it always remains open in that case.
                            let rectPoly = [];
                            switch (side) {
                                case 'top':
                                    rectPoly = [
                                        [left, refRect.top + 1],
                                        [left, rect.bottom - 1],
                                        [right, rect.bottom - 1],
                                        [right, refRect.top + 1]
                                    ];
                                    break;
                                case 'bottom':
                                    rectPoly = [
                                        [left, rect.top + 1],
                                        [left, refRect.bottom - 1],
                                        [right, refRect.bottom - 1],
                                        [right, rect.top + 1]
                                    ];
                                    break;
                                case 'left':
                                    rectPoly = [
                                        [rect.right - 1, bottom],
                                        [rect.right - 1, top],
                                        [refRect.left + 1, top],
                                        [refRect.left + 1, bottom]
                                    ];
                                    break;
                                case 'right':
                                    rectPoly = [
                                        [refRect.right - 1, bottom],
                                        [refRect.right - 1, top],
                                        [rect.left + 1, top],
                                        [rect.left + 1, bottom]
                                    ];
                                    break;
                            }

                            function getPolygon(_ref3) {
                                let [x, y] = _ref3;
                                switch (side) {
                                    case 'top':
                                        {
                                            const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];
                                            const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];
                                            const commonPoints = [
                                                [rect.left, cursorLeaveFromRight ? rect.bottom - buffer : isFloatingWider ? rect.bottom - buffer : rect.top],
                                                [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.bottom - buffer : rect.top : rect.bottom - buffer]
                                            ];
                                            return [cursorPointOne, cursorPointTwo, ...commonPoints];
                                        }
                                    case 'bottom':
                                        {
                                            const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];
                                            const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];
                                            const commonPoints = [
                                                [rect.left, cursorLeaveFromRight ? rect.top + buffer : isFloatingWider ? rect.top + buffer : rect.bottom],
                                                [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.top + buffer : rect.bottom : rect.top + buffer]
                                            ];
                                            return [cursorPointOne, cursorPointTwo, ...commonPoints];
                                        }
                                    case 'left':
                                        {
                                            const cursorPointOne = [x + buffer + 1, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];
                                            const cursorPointTwo = [x + buffer + 1, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];
                                            const commonPoints = [
                                                [cursorLeaveFromBottom ? rect.right - buffer : isFloatingTaller ? rect.right - buffer : rect.left, rect.top],
                                                [cursorLeaveFromBottom ? isFloatingTaller ? rect.right - buffer : rect.left : rect.right - buffer, rect.bottom]
                                            ];
                                            return [...commonPoints, cursorPointOne, cursorPointTwo];
                                        }
                                    case 'right':
                                        {
                                            const cursorPointOne = [x - buffer, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];
                                            const cursorPointTwo = [x - buffer, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];
                                            const commonPoints = [
                                                [cursorLeaveFromBottom ? rect.left + buffer : isFloatingTaller ? rect.left + buffer : rect.right, rect.top],
                                                [cursorLeaveFromBottom ? isFloatingTaller ? rect.left + buffer : rect.right : rect.left + buffer, rect.bottom]
                                            ];
                                            return [cursorPointOne, cursorPointTwo, ...commonPoints];
                                        }
                                }
                            }
                            if (isPointInPolygon([clientX, clientY], rectPoly)) {
                                return;
                            } else if (hasLanded && !isOverReferenceRect) {
                                return close();
                            }
                            if (!isLeave && requireIntent) {
                                const cursorSpeed = getCursorSpeed(event.clientX, event.clientY);
                                const cursorSpeedThreshold = 0.1;
                                if (cursorSpeed !== null && cursorSpeed < cursorSpeedThreshold) {
                                    return close();
                                }
                            }
                            if (!isPointInPolygon([clientX, clientY], getPolygon([x, y]))) {
                                close();
                            } else if (!hasLanded && requireIntent) {
                                timeoutId = window.setTimeout(close, 40);
                            }
                        };
                    };
                    fn.__options = {
                        blockPointerEvents
                    };
                    return fn;
                }




                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    alignments: () => ( /* binding */ alignments),
                    /* harmony export */
                    clamp: () => ( /* binding */ clamp),
                    /* harmony export */
                    createCoords: () => ( /* binding */ createCoords),
                    /* harmony export */
                    evaluate: () => ( /* binding */ evaluate),
                    /* harmony export */
                    expandPaddingObject: () => ( /* binding */ expandPaddingObject),
                    /* harmony export */
                    floor: () => ( /* binding */ floor),
                    /* harmony export */
                    getAlignment: () => ( /* binding */ getAlignment),
                    /* harmony export */
                    getAlignmentAxis: () => ( /* binding */ getAlignmentAxis),
                    /* harmony export */
                    getAlignmentSides: () => ( /* binding */ getAlignmentSides),
                    /* harmony export */
                    getAxisLength: () => ( /* binding */ getAxisLength),
                    /* harmony export */
                    getExpandedPlacements: () => ( /* binding */ getExpandedPlacements),
                    /* harmony export */
                    getOppositeAlignmentPlacement: () => ( /* binding */ getOppositeAlignmentPlacement),
                    /* harmony export */
                    getOppositeAxis: () => ( /* binding */ getOppositeAxis),
                    /* harmony export */
                    getOppositeAxisPlacements: () => ( /* binding */ getOppositeAxisPlacements),
                    /* harmony export */
                    getOppositePlacement: () => ( /* binding */ getOppositePlacement),
                    /* harmony export */
                    getPaddingObject: () => ( /* binding */ getPaddingObject),
                    /* harmony export */
                    getSide: () => ( /* binding */ getSide),
                    /* harmony export */
                    getSideAxis: () => ( /* binding */ getSideAxis),
                    /* harmony export */
                    max: () => ( /* binding */ max),
                    /* harmony export */
                    min: () => ( /* binding */ min),
                    /* harmony export */
                    placements: () => ( /* binding */ placements),
                    /* harmony export */
                    rectToClientRect: () => ( /* binding */ rectToClientRect),
                    /* harmony export */
                    round: () => ( /* binding */ round),
                    /* harmony export */
                    sides: () => ( /* binding */ sides)
                    /* harmony export */
                });
                const sides = ['top', 'right', 'bottom', 'left'];
                const alignments = ['start', 'end'];
                const placements = /*#__PURE__*/ sides.reduce((acc, side) => acc.concat(side, side + "-" + alignments[0], side + "-" + alignments[1]), []);
                const min = Math.min;
                const max = Math.max;
                const round = Math.round;
                const floor = Math.floor;
                const createCoords = v => ({
                    x: v,
                    y: v
                });
                const oppositeSideMap = {
                    left: 'right',
                    right: 'left',
                    bottom: 'top',
                    top: 'bottom'
                };
                const oppositeAlignmentMap = {
                    start: 'end',
                    end: 'start'
                };

                function clamp(start, value, end) {
                    return max(start, min(value, end));
                }

                function evaluate(value, param) {
                    return typeof value === 'function' ? value(param) : value;
                }

                function getSide(placement) {
                    return placement.split('-')[0];
                }

                function getAlignment(placement) {
                    return placement.split('-')[1];
                }

                function getOppositeAxis(axis) {
                    return axis === 'x' ? 'y' : 'x';
                }

                function getAxisLength(axis) {
                    return axis === 'y' ? 'height' : 'width';
                }

                function getSideAxis(placement) {
                    return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';
                }

                function getAlignmentAxis(placement) {
                    return getOppositeAxis(getSideAxis(placement));
                }

                function getAlignmentSides(placement, rects, rtl) {
                    if (rtl === void 0) {
                        rtl = false;
                    }
                    const alignment = getAlignment(placement);
                    const alignmentAxis = getAlignmentAxis(placement);
                    const length = getAxisLength(alignmentAxis);
                    let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';
                    if (rects.reference[length] > rects.floating[length]) {
                        mainAlignmentSide = getOppositePlacement(mainAlignmentSide);
                    }
                    return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];
                }

                function getExpandedPlacements(placement) {
                    const oppositePlacement = getOppositePlacement(placement);
                    return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];
                }

                function getOppositeAlignmentPlacement(placement) {
                    return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);
                }

                function getSideList(side, isStart, rtl) {
                    const lr = ['left', 'right'];
                    const rl = ['right', 'left'];
                    const tb = ['top', 'bottom'];
                    const bt = ['bottom', 'top'];
                    switch (side) {
                        case 'top':
                        case 'bottom':
                            if (rtl) return isStart ? rl : lr;
                            return isStart ? lr : rl;
                        case 'left':
                        case 'right':
                            return isStart ? tb : bt;
                        default:
                            return [];
                    }
                }

                function getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {
                    const alignment = getAlignment(placement);
                    let list = getSideList(getSide(placement), direction === 'start', rtl);
                    if (alignment) {
                        list = list.map(side => side + "-" + alignment);
                        if (flipAlignment) {
                            list = list.concat(list.map(getOppositeAlignmentPlacement));
                        }
                    }
                    return list;
                }

                function getOppositePlacement(placement) {
                    return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);
                }

                function expandPaddingObject(padding) {
                    return {
                        top: 0,
                        right: 0,
                        bottom: 0,
                        left: 0,
                        ...padding
                    };
                }

                function getPaddingObject(padding) {
                    return typeof padding !== 'number' ? expandPaddingObject(padding) : {
                        top: padding,
                        right: padding,
                        bottom: padding,
                        left: padding
                    };
                }

                function rectToClientRect(rect) {
                    return {
                        ...rect,
                        top: rect.y,
                        left: rect.x,
                        right: rect.x + rect.width,
                        bottom: rect.y + rect.height
                    };
                }




                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/utils/dom/dist/floating-ui.utils.dom.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    getComputedStyle: () => ( /* binding */ getComputedStyle),
                    /* harmony export */
                    getContainingBlock: () => ( /* binding */ getContainingBlock),
                    /* harmony export */
                    getDocumentElement: () => ( /* binding */ getDocumentElement),
                    /* harmony export */
                    getNearestOverflowAncestor: () => ( /* binding */ getNearestOverflowAncestor),
                    /* harmony export */
                    getNodeName: () => ( /* binding */ getNodeName),
                    /* harmony export */
                    getNodeScroll: () => ( /* binding */ getNodeScroll),
                    /* harmony export */
                    getOverflowAncestors: () => ( /* binding */ getOverflowAncestors),
                    /* harmony export */
                    getParentNode: () => ( /* binding */ getParentNode),
                    /* harmony export */
                    getWindow: () => ( /* binding */ getWindow),
                    /* harmony export */
                    isContainingBlock: () => ( /* binding */ isContainingBlock),
                    /* harmony export */
                    isElement: () => ( /* binding */ isElement),
                    /* harmony export */
                    isHTMLElement: () => ( /* binding */ isHTMLElement),
                    /* harmony export */
                    isLastTraversableNode: () => ( /* binding */ isLastTraversableNode),
                    /* harmony export */
                    isNode: () => ( /* binding */ isNode),
                    /* harmony export */
                    isOverflowElement: () => ( /* binding */ isOverflowElement),
                    /* harmony export */
                    isShadowRoot: () => ( /* binding */ isShadowRoot),
                    /* harmony export */
                    isTableElement: () => ( /* binding */ isTableElement),
                    /* harmony export */
                    isWebKit: () => ( /* binding */ isWebKit)
                    /* harmony export */
                });

                function getNodeName(node) {
                    if (isNode(node)) {
                        return (node.nodeName || '').toLowerCase();
                    }
                    // Mocked nodes in testing environments may not be instances of Node. By
                    // returning `#document` an infinite loop won't occur.
                    // https://github.com/floating-ui/floating-ui/issues/2317
                    return '#document';
                }

                function getWindow(node) {
                    var _node$ownerDocument;
                    return (node == null ? void 0 : (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;
                }

                function getDocumentElement(node) {
                    var _ref;
                    return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;
                }

                function isNode(value) {
                    return value instanceof Node || value instanceof getWindow(value).Node;
                }

                function isElement(value) {
                    return value instanceof Element || value instanceof getWindow(value).Element;
                }

                function isHTMLElement(value) {
                    return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;
                }

                function isShadowRoot(value) {
                    // Browsers without `ShadowRoot` support.
                    if (typeof ShadowRoot === 'undefined') {
                        return false;
                    }
                    return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;
                }

                function isOverflowElement(element) {
                    const {
                        overflow,
                        overflowX,
                        overflowY,
                        display
                    } = getComputedStyle(element);
                    return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);
                }

                function isTableElement(element) {
                    return ['table', 'td', 'th'].includes(getNodeName(element));
                }

                function isContainingBlock(element) {
                    const webkit = isWebKit();
                    const css = getComputedStyle(element);

                    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block
                    return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));
                }

                function getContainingBlock(element) {
                    let currentNode = getParentNode(element);
                    while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {
                        if (isContainingBlock(currentNode)) {
                            return currentNode;
                        } else {
                            currentNode = getParentNode(currentNode);
                        }
                    }
                    return null;
                }

                function isWebKit() {
                    if (typeof CSS === 'undefined' || !CSS.supports) return false;
                    return CSS.supports('-webkit-backdrop-filter', 'none');
                }

                function isLastTraversableNode(node) {
                    return ['html', 'body', '#document'].includes(getNodeName(node));
                }

                function getComputedStyle(element) {
                    return getWindow(element).getComputedStyle(element);
                }

                function getNodeScroll(element) {
                    if (isElement(element)) {
                        return {
                            scrollLeft: element.scrollLeft,
                            scrollTop: element.scrollTop
                        };
                    }
                    return {
                        scrollLeft: element.pageXOffset,
                        scrollTop: element.pageYOffset
                    };
                }

                function getParentNode(node) {
                    if (getNodeName(node) === 'html') {
                        return node;
                    }
                    const result =
                        // Step into the shadow DOM of the parent of a slotted node.
                        node.assignedSlot ||
                        // DOM Element detected.
                        node.parentNode ||
                        // ShadowRoot detected.
                        isShadowRoot(node) && node.host ||
                        // Fallback.
                        getDocumentElement(node);
                    return isShadowRoot(result) ? result.host : result;
                }

                function getNearestOverflowAncestor(node) {
                    const parentNode = getParentNode(node);
                    if (isLastTraversableNode(parentNode)) {
                        return node.ownerDocument ? node.ownerDocument.body : node.body;
                    }
                    if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {
                        return parentNode;
                    }
                    return getNearestOverflowAncestor(parentNode);
                }

                function getOverflowAncestors(node, list) {
                    var _node$ownerDocument2;
                    if (list === void 0) {
                        list = [];
                    }
                    const scrollableAncestor = getNearestOverflowAncestor(node);
                    const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);
                    const win = getWindow(scrollableAncestor);
                    if (isBody) {
                        return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], win.frameElement ? getOverflowAncestors(win.frameElement) : []);
                    }
                    return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor));
                }




                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/utils/react/dist/floating-ui.utils.react.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    TYPEABLE_SELECTOR: () => ( /* binding */ TYPEABLE_SELECTOR),
                    /* harmony export */
                    activeElement: () => ( /* binding */ activeElement),
                    /* harmony export */
                    contains: () => ( /* binding */ contains),
                    /* harmony export */
                    getDocument: () => ( /* binding */ getDocument),
                    /* harmony export */
                    getPlatform: () => ( /* binding */ getPlatform),
                    /* harmony export */
                    getTarget: () => ( /* binding */ getTarget),
                    /* harmony export */
                    getUserAgent: () => ( /* binding */ getUserAgent),
                    /* harmony export */
                    isEventTargetWithin: () => ( /* binding */ isEventTargetWithin),
                    /* harmony export */
                    isMac: () => ( /* binding */ isMac),
                    /* harmony export */
                    isMouseLikePointerType: () => ( /* binding */ isMouseLikePointerType),
                    /* harmony export */
                    isReactEvent: () => ( /* binding */ isReactEvent),
                    /* harmony export */
                    isRootElement: () => ( /* binding */ isRootElement),
                    /* harmony export */
                    isSafari: () => ( /* binding */ isSafari),
                    /* harmony export */
                    isTypeableElement: () => ( /* binding */ isTypeableElement),
                    /* harmony export */
                    isVirtualClick: () => ( /* binding */ isVirtualClick),
                    /* harmony export */
                    isVirtualPointerEvent: () => ( /* binding */ isVirtualPointerEvent),
                    /* harmony export */
                    stopEvent: () => ( /* binding */ stopEvent)
                    /* harmony export */
                });
                /* harmony import */
                var _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@floating-ui/utils/dom/dist/floating-ui.utils.dom.mjs");


                function activeElement(doc) {
                    let activeElement = doc.activeElement;
                    while (((_activeElement = activeElement) == null ? void 0 : (_activeElement$shadow = _activeElement.shadowRoot) == null ? void 0 : _activeElement$shadow.activeElement) != null) {
                        var _activeElement, _activeElement$shadow;
                        activeElement = activeElement.shadowRoot.activeElement;
                    }
                    return activeElement;
                }

                function contains(parent, child) {
                    if (!parent || !child) {
                        return false;
                    }
                    const rootNode = child.getRootNode && child.getRootNode();

                    // First, attempt with faster native method
                    if (parent.contains(child)) {
                        return true;
                    }

                    // then fallback to custom implementation with Shadow DOM support
                    if (rootNode && (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isShadowRoot)(rootNode)) {
                        let next = child;
                        while (next) {
                            if (parent === next) {
                                return true;
                            }
                            // @ts-ignore
                            next = next.parentNode || next.host;
                        }
                    }

                    // Give up, the result is false
                    return false;
                }
                // Avoid Chrome DevTools blue warning.
                function getPlatform() {
                    const uaData = navigator.userAgentData;
                    if (uaData != null && uaData.platform) {
                        return uaData.platform;
                    }
                    return navigator.platform;
                }

                function getUserAgent() {
                    const uaData = navigator.userAgentData;
                    if (uaData && Array.isArray(uaData.brands)) {
                        return uaData.brands.map(_ref => {
                            let {
                                brand,
                                version
                            } = _ref;
                            return brand + "/" + version;
                        }).join(' ');
                    }
                    return navigator.userAgent;
                }

                // License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts
                function isVirtualClick(event) {
                    if (event.mozInputSource === 0 && event.isTrusted) {
                        return true;
                    }
                    const androidRe = /Android/i;
                    if ((androidRe.test(getPlatform()) || androidRe.test(getUserAgent())) && event.pointerType) {
                        return event.type === 'click' && event.buttons === 1;
                    }
                    return event.detail === 0 && !event.pointerType;
                }

                function isVirtualPointerEvent(event) {
                    return event.width === 0 && event.height === 0 || event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType !== 'mouse' ||
                        // iOS VoiceOver returns 0.333• for width/height.
                        event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0;
                }

                function isSafari() {
                    // Chrome DevTools does not complain about navigator.vendor
                    return /apple/i.test(navigator.vendor);
                }

                function isMac() {
                    return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;
                }

                function isMouseLikePointerType(pointerType, strict) {
                    // On some Linux machines with Chromium, mouse inputs return a `pointerType`
                    // of "pen": https://github.com/floating-ui/floating-ui/issues/2015
                    const values = ['mouse', 'pen'];
                    if (!strict) {
                        values.push('', undefined);
                    }
                    return values.includes(pointerType);
                }

                function isReactEvent(event) {
                    return 'nativeEvent' in event;
                }

                function isRootElement(element) {
                    return element.matches('html,body');
                }

                function getDocument(node) {
                    return (node == null ? void 0 : node.ownerDocument) || document;
                }

                function isEventTargetWithin(event, node) {
                    if (node == null) {
                        return false;
                    }
                    if ('composedPath' in event) {
                        return event.composedPath().includes(node);
                    }

                    // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't
                    const e = event;
                    return e.target != null && node.contains(e.target);
                }

                function getTarget(event) {
                    if ('composedPath' in event) {
                        return event.composedPath()[0];
                    }

                    // TS thinks `event` is of type never as it assumes all browsers support
                    // `composedPath()`, but browsers without shadow DOM don't.
                    return event.target;
                }
                const TYPEABLE_SELECTOR = "input:not([type='hidden']):not([disabled])," + "[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";

                function isTypeableElement(element) {
                    return (0, _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) && element.matches(TYPEABLE_SELECTOR);
                }

                function stopEvent(event) {
                    event.preventDefault();
                    event.stopPropagation();
                }




                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_floating-ui_react_dist_floating-ui_react_mjs.37f57fa1e22b2e54.js.map