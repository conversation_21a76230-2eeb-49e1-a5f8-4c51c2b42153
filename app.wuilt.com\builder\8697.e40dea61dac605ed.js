(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [8697], {
        8697: (e, t) => {
            function n(e) {
                return function(t) {
                    var n = t.dispatch,
                        r = t.getState;
                    return function(t) {
                        return function(u) {
                            return "function" == typeof u ? u(n, r, e) : t(u)
                        }
                    }
                }
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var r = n();
            r.withExtraArgument = n;
            var u = r;
            t.default = u
        }
    }
]);
//# sourceMappingURL=8697.e40dea61dac605ed.js.map