(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["node_modules_dnd-kit_modifiers_dist_modifiers_esm_js-_48600"], {

        /***/
        "../../node_modules/@dnd-kit/modifiers/dist/modifiers.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    createSnapModifier: () => ( /* binding */ createSnapModifier),
                    /* harmony export */
                    restrictToFirstScrollableAncestor: () => ( /* binding */ restrictToFirstScrollableAncestor),
                    /* harmony export */
                    restrictToHorizontalAxis: () => ( /* binding */ restrictToHorizontalAxis),
                    /* harmony export */
                    restrictToParentElement: () => ( /* binding */ restrictToParentElement),
                    /* harmony export */
                    restrictToVerticalAxis: () => ( /* binding */ restrictToVerticalAxis),
                    /* harmony export */
                    restrictToWindowEdges: () => ( /* binding */ restrictToWindowEdges),
                    /* harmony export */
                    snapCenterToCursor: () => ( /* binding */ snapCenterToCursor)
                    /* harmony export */
                });
                /* harmony import */
                var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/@dnd-kit/utilities/@dnd-kit/utilities");
                /* harmony import */
                var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_0__);


                function createSnapModifier(gridSize) {
                    return _ref => {
                        let {
                            transform
                        } = _ref;
                        return { ...transform,
                            x: Math.ceil(transform.x / gridSize) * gridSize,
                            y: Math.ceil(transform.y / gridSize) * gridSize
                        };
                    };
                }

                const restrictToHorizontalAxis = _ref => {
                    let {
                        transform
                    } = _ref;
                    return { ...transform,
                        y: 0
                    };
                };

                function restrictToBoundingRect(transform, rect, boundingRect) {
                    const value = { ...transform
                    };

                    if (rect.top + transform.y <= boundingRect.top) {
                        value.y = boundingRect.top - rect.top;
                    } else if (rect.bottom + transform.y >= boundingRect.top + boundingRect.height) {
                        value.y = boundingRect.top + boundingRect.height - rect.bottom;
                    }

                    if (rect.left + transform.x <= boundingRect.left) {
                        value.x = boundingRect.left - rect.left;
                    } else if (rect.right + transform.x >= boundingRect.left + boundingRect.width) {
                        value.x = boundingRect.left + boundingRect.width - rect.right;
                    }

                    return value;
                }

                const restrictToParentElement = _ref => {
                    let {
                        containerNodeRect,
                        draggingNodeRect,
                        transform
                    } = _ref;

                    if (!draggingNodeRect || !containerNodeRect) {
                        return transform;
                    }

                    return restrictToBoundingRect(transform, draggingNodeRect, containerNodeRect);
                };

                const restrictToFirstScrollableAncestor = _ref => {
                    let {
                        draggingNodeRect,
                        transform,
                        scrollableAncestorRects
                    } = _ref;
                    const firstScrollableAncestorRect = scrollableAncestorRects[0];

                    if (!draggingNodeRect || !firstScrollableAncestorRect) {
                        return transform;
                    }

                    return restrictToBoundingRect(transform, draggingNodeRect, firstScrollableAncestorRect);
                };

                const restrictToVerticalAxis = _ref => {
                    let {
                        transform
                    } = _ref;
                    return { ...transform,
                        x: 0
                    };
                };

                const restrictToWindowEdges = _ref => {
                    let {
                        transform,
                        draggingNodeRect,
                        windowRect
                    } = _ref;

                    if (!draggingNodeRect || !windowRect) {
                        return transform;
                    }

                    return restrictToBoundingRect(transform, draggingNodeRect, windowRect);
                };

                const snapCenterToCursor = _ref => {
                    let {
                        activatorEvent,
                        draggingNodeRect,
                        transform
                    } = _ref;

                    if (draggingNodeRect && activatorEvent) {
                        const activatorCoordinates = (0, _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_0__.getEventCoordinates)(activatorEvent);

                        if (!activatorCoordinates) {
                            return transform;
                        }

                        const offsetX = activatorCoordinates.x - draggingNodeRect.left;
                        const offsetY = activatorCoordinates.y - draggingNodeRect.top;
                        return { ...transform,
                            x: transform.x + offsetX - draggingNodeRect.width / 2,
                            y: transform.y + offsetY - draggingNodeRect.height / 2
                        };
                    }

                    return transform;
                };




                /***/
            })

    }
])
//# sourceMappingURL=node_modules_dnd-kit_modifiers_dist_modifiers_esm_js-_48600.bd82db19a5fe9879.js.map