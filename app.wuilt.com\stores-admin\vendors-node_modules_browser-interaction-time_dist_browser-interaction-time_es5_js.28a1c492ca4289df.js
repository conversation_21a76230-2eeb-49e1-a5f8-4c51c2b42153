(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_browser-interaction-time_dist_browser-interaction-time_es5_js"], {

        /***/
        "../../node_modules/browser-interaction-time/dist/browser-interaction-time.es5.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /*! *****************************************************************************
                Copyright (c) Microsoft Corporation. All rights reserved.
                Licensed under the Apache License, Version 2.0 (the "License"); you may not use
                this file except in compliance with the License. You may obtain a copy of the
                License at http://www.apache.org/licenses/LICENSE-2.0

                THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
                KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
                WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
                MERCHANTABLITY OR NON-INFRINGEMENT.

                See the Apache Version 2.0 License for specific language governing permissions
                and limitations under the License.
                ***************************************************************************** */

                var __assign = function() {
                    __assign = Object.assign || function __assign(t) {
                        for (var s, i = 1, n = arguments.length; i < n; i++) {
                            s = arguments[i];
                            for (var p in s)
                                if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
                        }
                        return t;
                    };
                    return __assign.apply(this, arguments);
                };

                /**
                 * Checks if `value` is the
                 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
                 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
                 * @example
                 *
                 * _.isObject({});
                 * // => true
                 *
                 * _.isObject([1, 2, 3]);
                 * // => true
                 *
                 * _.isObject(_.noop);
                 * // => true
                 *
                 * _.isObject(null);
                 * // => false
                 */
                function isObject(value) {
                    var type = typeof value;
                    return value != null && (type == 'object' || type == 'function');
                }

                var isObject_1 = isObject;

                var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof __webpack_require__.g !== 'undefined' ? __webpack_require__.g : typeof self !== 'undefined' ? self : {};

                /** Detect free variable `global` from Node.js. */
                var freeGlobal = typeof commonjsGlobal == 'object' && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;

                var _freeGlobal = freeGlobal;

                /** Detect free variable `self`. */
                var freeSelf = typeof self == 'object' && self && self.Object === Object && self;

                /** Used as a reference to the global object. */
                var root = _freeGlobal || freeSelf || Function('return this')();

                var _root = root;

                /**
                 * Gets the timestamp of the number of milliseconds that have elapsed since
                 * the Unix epoch (1 January 1970 00:00:00 UTC).
                 *
                 * @static
                 * @memberOf _
                 * @since 2.4.0
                 * @category Date
                 * @returns {number} Returns the timestamp.
                 * @example
                 *
                 * _.defer(function(stamp) {
                 *   console.log(_.now() - stamp);
                 * }, _.now());
                 * // => Logs the number of milliseconds it took for the deferred invocation.
                 */
                var now = function() {
                    return _root.Date.now();
                };

                var now_1 = now;

                /** Built-in value references. */
                var Symbol = _root.Symbol;

                var _Symbol = Symbol;

                /** Used for built-in method references. */
                var objectProto = Object.prototype;

                /** Used to check objects for own properties. */
                var hasOwnProperty = objectProto.hasOwnProperty;

                /**
                 * Used to resolve the
                 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
                 * of values.
                 */
                var nativeObjectToString = objectProto.toString;

                /** Built-in value references. */
                var symToStringTag = _Symbol ? _Symbol.toStringTag : undefined;

                /**
                 * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.
                 *
                 * @private
                 * @param {*} value The value to query.
                 * @returns {string} Returns the raw `toStringTag`.
                 */
                function getRawTag(value) {
                    var isOwn = hasOwnProperty.call(value, symToStringTag),
                        tag = value[symToStringTag];

                    try {
                        value[symToStringTag] = undefined;
                        var unmasked = true;
                    } catch (e) {}

                    var result = nativeObjectToString.call(value);
                    if (unmasked) {
                        if (isOwn) {
                            value[symToStringTag] = tag;
                        } else {
                            delete value[symToStringTag];
                        }
                    }
                    return result;
                }

                var _getRawTag = getRawTag;

                /** Used for built-in method references. */
                var objectProto$1 = Object.prototype;

                /**
                 * Used to resolve the
                 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
                 * of values.
                 */
                var nativeObjectToString$1 = objectProto$1.toString;

                /**
                 * Converts `value` to a string using `Object.prototype.toString`.
                 *
                 * @private
                 * @param {*} value The value to convert.
                 * @returns {string} Returns the converted string.
                 */
                function objectToString(value) {
                    return nativeObjectToString$1.call(value);
                }

                var _objectToString = objectToString;

                /** `Object#toString` result references. */
                var nullTag = '[object Null]',
                    undefinedTag = '[object Undefined]';

                /** Built-in value references. */
                var symToStringTag$1 = _Symbol ? _Symbol.toStringTag : undefined;

                /**
                 * The base implementation of `getTag` without fallbacks for buggy environments.
                 *
                 * @private
                 * @param {*} value The value to query.
                 * @returns {string} Returns the `toStringTag`.
                 */
                function baseGetTag(value) {
                    if (value == null) {
                        return value === undefined ? undefinedTag : nullTag;
                    }
                    return (symToStringTag$1 && symToStringTag$1 in Object(value)) ?
                        _getRawTag(value) :
                        _objectToString(value);
                }

                var _baseGetTag = baseGetTag;

                /**
                 * Checks if `value` is object-like. A value is object-like if it's not `null`
                 * and has a `typeof` result of "object".
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
                 * @example
                 *
                 * _.isObjectLike({});
                 * // => true
                 *
                 * _.isObjectLike([1, 2, 3]);
                 * // => true
                 *
                 * _.isObjectLike(_.noop);
                 * // => false
                 *
                 * _.isObjectLike(null);
                 * // => false
                 */
                function isObjectLike(value) {
                    return value != null && typeof value == 'object';
                }

                var isObjectLike_1 = isObjectLike;

                /** `Object#toString` result references. */
                var symbolTag = '[object Symbol]';

                /**
                 * Checks if `value` is classified as a `Symbol` primitive or object.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
                 * @example
                 *
                 * _.isSymbol(Symbol.iterator);
                 * // => true
                 *
                 * _.isSymbol('abc');
                 * // => false
                 */
                function isSymbol(value) {
                    return typeof value == 'symbol' ||
                        (isObjectLike_1(value) && _baseGetTag(value) == symbolTag);
                }

                var isSymbol_1 = isSymbol;

                /** Used as references for various `Number` constants. */
                var NAN = 0 / 0;

                /** Used to match leading and trailing whitespace. */
                var reTrim = /^\s+|\s+$/g;

                /** Used to detect bad signed hexadecimal string values. */
                var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;

                /** Used to detect binary string values. */
                var reIsBinary = /^0b[01]+$/i;

                /** Used to detect octal string values. */
                var reIsOctal = /^0o[0-7]+$/i;

                /** Built-in method references without a dependency on `root`. */
                var freeParseInt = parseInt;

                /**
                 * Converts `value` to a number.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to process.
                 * @returns {number} Returns the number.
                 * @example
                 *
                 * _.toNumber(3.2);
                 * // => 3.2
                 *
                 * _.toNumber(Number.MIN_VALUE);
                 * // => 5e-324
                 *
                 * _.toNumber(Infinity);
                 * // => Infinity
                 *
                 * _.toNumber('3.2');
                 * // => 3.2
                 */
                function toNumber(value) {
                    if (typeof value == 'number') {
                        return value;
                    }
                    if (isSymbol_1(value)) {
                        return NAN;
                    }
                    if (isObject_1(value)) {
                        var other = typeof value.valueOf == 'function' ? value.valueOf() : value;
                        value = isObject_1(other) ? (other + '') : other;
                    }
                    if (typeof value != 'string') {
                        return value === 0 ? value : +value;
                    }
                    value = value.replace(reTrim, '');
                    var isBinary = reIsBinary.test(value);
                    return (isBinary || reIsOctal.test(value)) ?
                        freeParseInt(value.slice(2), isBinary ? 2 : 8) :
                        (reIsBadHex.test(value) ? NAN : +value);
                }

                var toNumber_1 = toNumber;

                /** Error message constants. */
                var FUNC_ERROR_TEXT = 'Expected a function';

                /* Built-in method references for those with the same name as other `lodash` methods. */
                var nativeMax = Math.max,
                    nativeMin = Math.min;

                /**
                 * Creates a debounced function that delays invoking `func` until after `wait`
                 * milliseconds have elapsed since the last time the debounced function was
                 * invoked. The debounced function comes with a `cancel` method to cancel
                 * delayed `func` invocations and a `flush` method to immediately invoke them.
                 * Provide `options` to indicate whether `func` should be invoked on the
                 * leading and/or trailing edge of the `wait` timeout. The `func` is invoked
                 * with the last arguments provided to the debounced function. Subsequent
                 * calls to the debounced function return the result of the last `func`
                 * invocation.
                 *
                 * **Note:** If `leading` and `trailing` options are `true`, `func` is
                 * invoked on the trailing edge of the timeout only if the debounced function
                 * is invoked more than once during the `wait` timeout.
                 *
                 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
                 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
                 *
                 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
                 * for details over the differences between `_.debounce` and `_.throttle`.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Function
                 * @param {Function} func The function to debounce.
                 * @param {number} [wait=0] The number of milliseconds to delay.
                 * @param {Object} [options={}] The options object.
                 * @param {boolean} [options.leading=false]
                 *  Specify invoking on the leading edge of the timeout.
                 * @param {number} [options.maxWait]
                 *  The maximum time `func` is allowed to be delayed before it's invoked.
                 * @param {boolean} [options.trailing=true]
                 *  Specify invoking on the trailing edge of the timeout.
                 * @returns {Function} Returns the new debounced function.
                 * @example
                 *
                 * // Avoid costly calculations while the window size is in flux.
                 * jQuery(window).on('resize', _.debounce(calculateLayout, 150));
                 *
                 * // Invoke `sendMail` when clicked, debouncing subsequent calls.
                 * jQuery(element).on('click', _.debounce(sendMail, 300, {
                 *   'leading': true,
                 *   'trailing': false
                 * }));
                 *
                 * // Ensure `batchLog` is invoked once after 1 second of debounced calls.
                 * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });
                 * var source = new EventSource('/stream');
                 * jQuery(source).on('message', debounced);
                 *
                 * // Cancel the trailing debounced invocation.
                 * jQuery(window).on('popstate', debounced.cancel);
                 */
                function debounce(func, wait, options) {
                    var lastArgs,
                        lastThis,
                        maxWait,
                        result,
                        timerId,
                        lastCallTime,
                        lastInvokeTime = 0,
                        leading = false,
                        maxing = false,
                        trailing = true;

                    if (typeof func != 'function') {
                        throw new TypeError(FUNC_ERROR_TEXT);
                    }
                    wait = toNumber_1(wait) || 0;
                    if (isObject_1(options)) {
                        leading = !!options.leading;
                        maxing = 'maxWait' in options;
                        maxWait = maxing ? nativeMax(toNumber_1(options.maxWait) || 0, wait) : maxWait;
                        trailing = 'trailing' in options ? !!options.trailing : trailing;
                    }

                    function invokeFunc(time) {
                        var args = lastArgs,
                            thisArg = lastThis;

                        lastArgs = lastThis = undefined;
                        lastInvokeTime = time;
                        result = func.apply(thisArg, args);
                        return result;
                    }

                    function leadingEdge(time) {
                        // Reset any `maxWait` timer.
                        lastInvokeTime = time;
                        // Start the timer for the trailing edge.
                        timerId = setTimeout(timerExpired, wait);
                        // Invoke the leading edge.
                        return leading ? invokeFunc(time) : result;
                    }

                    function remainingWait(time) {
                        var timeSinceLastCall = time - lastCallTime,
                            timeSinceLastInvoke = time - lastInvokeTime,
                            timeWaiting = wait - timeSinceLastCall;

                        return maxing ?
                            nativeMin(timeWaiting, maxWait - timeSinceLastInvoke) :
                            timeWaiting;
                    }

                    function shouldInvoke(time) {
                        var timeSinceLastCall = time - lastCallTime,
                            timeSinceLastInvoke = time - lastInvokeTime;

                        // Either this is the first call, activity has stopped and we're at the
                        // trailing edge, the system time has gone backwards and we're treating
                        // it as the trailing edge, or we've hit the `maxWait` limit.
                        return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||
                            (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));
                    }

                    function timerExpired() {
                        var time = now_1();
                        if (shouldInvoke(time)) {
                            return trailingEdge(time);
                        }
                        // Restart the timer.
                        timerId = setTimeout(timerExpired, remainingWait(time));
                    }

                    function trailingEdge(time) {
                        timerId = undefined;

                        // Only invoke if we have `lastArgs` which means `func` has been
                        // debounced at least once.
                        if (trailing && lastArgs) {
                            return invokeFunc(time);
                        }
                        lastArgs = lastThis = undefined;
                        return result;
                    }

                    function cancel() {
                        if (timerId !== undefined) {
                            clearTimeout(timerId);
                        }
                        lastInvokeTime = 0;
                        lastArgs = lastCallTime = lastThis = timerId = undefined;
                    }

                    function flush() {
                        return timerId === undefined ? result : trailingEdge(now_1());
                    }

                    function debounced() {
                        var time = now_1(),
                            isInvoking = shouldInvoke(time);

                        lastArgs = arguments;
                        lastThis = this;
                        lastCallTime = time;

                        if (isInvoking) {
                            if (timerId === undefined) {
                                return leadingEdge(lastCallTime);
                            }
                            if (maxing) {
                                // Handle invocations in a tight loop.
                                timerId = setTimeout(timerExpired, wait);
                                return invokeFunc(lastCallTime);
                            }
                        }
                        if (timerId === undefined) {
                            timerId = setTimeout(timerExpired, wait);
                        }
                        return result;
                    }
                    debounced.cancel = cancel;
                    debounced.flush = flush;
                    return debounced;
                }

                var debounce_1 = debounce;

                /** Error message constants. */
                var FUNC_ERROR_TEXT$1 = 'Expected a function';

                /**
                 * Creates a throttled function that only invokes `func` at most once per
                 * every `wait` milliseconds. The throttled function comes with a `cancel`
                 * method to cancel delayed `func` invocations and a `flush` method to
                 * immediately invoke them. Provide `options` to indicate whether `func`
                 * should be invoked on the leading and/or trailing edge of the `wait`
                 * timeout. The `func` is invoked with the last arguments provided to the
                 * throttled function. Subsequent calls to the throttled function return the
                 * result of the last `func` invocation.
                 *
                 * **Note:** If `leading` and `trailing` options are `true`, `func` is
                 * invoked on the trailing edge of the timeout only if the throttled function
                 * is invoked more than once during the `wait` timeout.
                 *
                 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
                 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
                 *
                 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
                 * for details over the differences between `_.throttle` and `_.debounce`.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Function
                 * @param {Function} func The function to throttle.
                 * @param {number} [wait=0] The number of milliseconds to throttle invocations to.
                 * @param {Object} [options={}] The options object.
                 * @param {boolean} [options.leading=true]
                 *  Specify invoking on the leading edge of the timeout.
                 * @param {boolean} [options.trailing=true]
                 *  Specify invoking on the trailing edge of the timeout.
                 * @returns {Function} Returns the new throttled function.
                 * @example
                 *
                 * // Avoid excessively updating the position while scrolling.
                 * jQuery(window).on('scroll', _.throttle(updatePosition, 100));
                 *
                 * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.
                 * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });
                 * jQuery(element).on('click', throttled);
                 *
                 * // Cancel the trailing throttled invocation.
                 * jQuery(window).on('popstate', throttled.cancel);
                 */
                function throttle(func, wait, options) {
                    var leading = true,
                        trailing = true;

                    if (typeof func != 'function') {
                        throw new TypeError(FUNC_ERROR_TEXT$1);
                    }
                    if (isObject_1(options)) {
                        leading = 'leading' in options ? !!options.leading : leading;
                        trailing = 'trailing' in options ? !!options.trailing : trailing;
                    }
                    return debounce_1(func, wait, {
                        'leading': leading,
                        'maxWait': wait,
                        'trailing': trailing
                    });
                }

                var throttle_1 = throttle;

                var windowIdleEvents = ['scroll', 'resize'];
                var documentIdleEvents = [
                    'wheel',
                    'keydown',
                    'keyup',
                    'mousedown',
                    'mousemove',
                    'touchstart',
                    'touchmove',
                    'click',
                    'contextmenu',
                ];
                var BrowserInteractionTime = /** @class */ (function() {
                    function BrowserInteractionTime(_a) {
                        var _b = _a.timeIntervalEllapsedCallbacks,
                            timeIntervalEllapsedCallbacks = _b === void 0 ? [] : _b,
                            _c = _a.absoluteTimeEllapsedCallbacks,
                            absoluteTimeEllapsedCallbacks = _c === void 0 ? [] : _c,
                            _d = _a.checkCallbacksIntervalMs,
                            checkCallbacksIntervalMs = _d === void 0 ? 100 : _d,
                            _e = _a.browserTabInactiveCallbacks,
                            browserTabInactiveCallbacks = _e === void 0 ? [] : _e,
                            _f = _a.idleCallbacks,
                            idleCallbacks = _f === void 0 ? [] : _f,
                            _g = _a.stopTimerOnTabchange,
                            stopTimerOnTabchange = _g === void 0 ? true : _g,
                            _h = _a.activeCallbacks,
                            activeCallbacks = _h === void 0 ? [] : _h,
                            _j = _a.browserTabActiveCallbacks,
                            browserTabActiveCallbacks = _j === void 0 ? [] : _j,
                            _k = _a.idleTimeoutMs,
                            idleTimeoutMs = _k === void 0 ? 3000 : _k;
                        var _this = this;
                        this.onBrowserTabInactive = function() {
                            // if running pause timer
                            if (_this.isRunning() && _this.stopTimerOnTabchange) {
                                _this.stopTimer();
                            }
                            _this.browserTabInactiveCallbacks.forEach(function(fn) {
                                return fn(_this.getTimeInMilliseconds());
                            });
                        };
                        this.onBrowserTabActive = function() {
                            // if not running start timer
                            if (!_this.isRunning()) {
                                _this.startTimer();
                            }
                            _this.browserTabActiveCallbacks.forEach(function(fn) {
                                return fn(_this.getTimeInMilliseconds());
                            });
                        };
                        this.onBrowserActiveChange = function() {
                            if (document.visibilityState === 'visible') {
                                _this.onBrowserTabActive();
                            } else {
                                _this.onBrowserTabInactive();
                            }
                        };
                        this.onTimePassed = function() {
                            // check all callbacks time and if passed execute callback
                            _this.absoluteTimeEllapsedCallbacks.forEach(function(_a, index) {
                                var callback = _a.callback,
                                    pending = _a.pending,
                                    timeInMilliseconds = _a.timeInMilliseconds;
                                if (pending && timeInMilliseconds <= _this.getTimeInMilliseconds()) {
                                    callback(_this.getTimeInMilliseconds());
                                    _this.absoluteTimeEllapsedCallbacks[index].pending = false;
                                }
                            });
                            _this.timeIntervalEllapsedCallbacks.forEach(function(_a, index) {
                                var callback = _a.callback,
                                    timeInMilliseconds = _a.timeInMilliseconds,
                                    multiplier = _a.multiplier;
                                if (timeInMilliseconds <= _this.getTimeInMilliseconds()) {
                                    callback(_this.getTimeInMilliseconds());
                                    _this.timeIntervalEllapsedCallbacks[index].timeInMilliseconds =
                                        multiplier(timeInMilliseconds);
                                }
                            });
                            if (_this.currentIdleTimeMs >= _this.idleTimeoutMs && _this.isRunning()) {
                                _this.idle = true;
                                _this.stopTimer();
                                _this.idleCallbacks.forEach(function(fn) {
                                    return fn(_this.getTimeInMilliseconds());
                                });
                            } else {
                                _this.currentIdleTimeMs += _this.checkCallbacksIntervalMs;
                            }
                        };
                        this.resetIdleTime = function() {
                            if (_this.idle) {
                                _this.startTimer();
                            }
                            _this.activeCallbacks.forEach(function(fn) {
                                return fn(_this.getTimeInMilliseconds());
                            });
                            _this.idle = false;
                            _this.currentIdleTimeMs = 0;
                        };
                        this.registerEventListeners = function() {
                            var documentListenerOptions = {
                                passive: true
                            };
                            var windowListenerOptions = __assign({}, documentListenerOptions, {
                                capture: true
                            });
                            document.addEventListener('visibilitychange', _this.onBrowserActiveChange);
                            var throttleResetIdleTime = throttle_1(_this.resetIdleTime, 2000, {
                                leading: true,
                                trailing: false,
                            });
                            windowIdleEvents.forEach(function(event) {
                                window.addEventListener(event, throttleResetIdleTime, windowListenerOptions);
                            });
                            documentIdleEvents.forEach(function(event) {
                                return document.addEventListener(event, throttleResetIdleTime, documentListenerOptions);
                            });
                        };
                        this.unregisterEventListeners = function() {
                            document.removeEventListener('visibilitychange', _this.onBrowserActiveChange);
                            windowIdleEvents.forEach(function(event) {
                                return window.removeEventListener(event, _this.resetIdleTime);
                            });
                            documentIdleEvents.forEach(function(event) {
                                return document.removeEventListener(event, _this.resetIdleTime);
                            });
                        };
                        this.checkCallbacksOnInterval = function() {
                            _this.checkCallbackIntervalId = window.setInterval(function() {
                                _this.onTimePassed();
                            }, _this.checkCallbacksIntervalMs);
                        };
                        this.startTimer = function() {
                            if (!_this.checkCallbackIntervalId) {
                                _this.checkCallbacksOnInterval();
                            }
                            var last = _this.times[_this.times.length - 1];
                            if (last && last.stop === null) {
                                return;
                            }
                            _this.times.push({
                                start: performance.now(),
                                stop: null,
                            });
                            _this.running = true;
                        };
                        this.stopTimer = function() {
                            if (!_this.times.length) {
                                return;
                            }
                            _this.times[_this.times.length - 1].stop = performance.now();
                            _this.running = false;
                        };
                        this.addTimeIntervalEllapsedCallback = function(timeIntervalEllapsedCallback) {
                            _this.timeIntervalEllapsedCallbacks.push(timeIntervalEllapsedCallback);
                        };
                        this.addAbsoluteTimeEllapsedCallback = function(absoluteTimeEllapsedCallback) {
                            _this.absoluteTimeEllapsedCallbacks.push(absoluteTimeEllapsedCallback);
                        };
                        this.addBrowserTabInactiveCallback = function(browserTabInactiveCallback) {
                            _this.browserTabInactiveCallbacks.push(browserTabInactiveCallback);
                        };
                        this.addBrowserTabActiveCallback = function(browserTabActiveCallback) {
                            _this.browserTabActiveCallbacks.push(browserTabActiveCallback);
                        };
                        this.addIdleCallback = function(inactiveCallback) {
                            _this.idleCallbacks.push(inactiveCallback);
                        };
                        this.addActiveCallback = function(activeCallback) {
                            _this.activeCallbacks.push(activeCallback);
                        };
                        this.getTimeInMilliseconds = function() {
                            return _this.times.reduce(function(acc, current) {
                                if (current.stop) {
                                    acc = acc + (current.stop - current.start);
                                } else {
                                    acc = acc + (performance.now() - current.start);
                                }
                                return acc;
                            }, 0);
                        };
                        this.isRunning = function() {
                            return _this.running;
                        };
                        this.isIdle = function() {
                            return _this.idle;
                        };
                        this.reset = function() {
                            _this.times = [];
                        };
                        this.destroy = function() {
                            _this.unregisterEventListeners();
                            if (_this.checkCallbackIntervalId) {
                                window.clearInterval(_this.checkCallbackIntervalId);
                            }
                        };
                        this.running = false;
                        this.times = [];
                        this.idle = false;
                        this.currentIdleTimeMs = 0;
                        this.marks = {};
                        this.measures = {};
                        this.stopTimerOnTabchange = stopTimerOnTabchange;
                        this.browserTabActiveCallbacks = browserTabActiveCallbacks;
                        this.browserTabInactiveCallbacks = browserTabInactiveCallbacks;
                        this.checkCallbacksIntervalMs = checkCallbacksIntervalMs;
                        this.idleTimeoutMs = idleTimeoutMs;
                        this.timeIntervalEllapsedCallbacks = timeIntervalEllapsedCallbacks;
                        this.absoluteTimeEllapsedCallbacks = absoluteTimeEllapsedCallbacks;
                        this.idleCallbacks = idleCallbacks;
                        this.activeCallbacks = activeCallbacks;
                        this.registerEventListeners();
                    }
                    BrowserInteractionTime.prototype.mark = function(key) {
                        if (!this.marks[key]) {
                            this.marks[key] = [];
                        }
                        this.marks[key].push({
                            time: this.getTimeInMilliseconds()
                        });
                    };
                    BrowserInteractionTime.prototype.getMarks = function(name) {
                        if (this.marks[name].length < 1) {
                            return;
                        }
                        return this.marks[name];
                    };
                    BrowserInteractionTime.prototype.measure = function(name, startMarkName, endMarkName) {
                        var startMarks = this.marks[startMarkName];
                        var startMark = startMarks[startMarks.length - 1];
                        var endMarks = this.marks[endMarkName];
                        var endMark = endMarks[endMarks.length - 1];
                        if (!this.measures[name]) {
                            this.measures[name] = [];
                        }
                        this.measures[name].push({
                            name: name,
                            startTime: startMark.time,
                            duration: endMark.time - startMark.time,
                        });
                    };
                    BrowserInteractionTime.prototype.getMeasures = function(name) {
                        if (!this.measures[name] && this.measures[name].length < 1) {
                            return;
                        }
                        return this.measures[name];
                    };
                    return BrowserInteractionTime;
                }());

                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (BrowserInteractionTime);


                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_browser-interaction-time_dist_browser-interaction-time_es5_js.28a1c492ca4289df.js.map