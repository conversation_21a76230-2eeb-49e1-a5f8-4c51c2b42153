(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [5253], {
        15253: r => {
            r.exports = function(r) {
                function n(t) {
                    var e, i, f = {};
                    if (!t) return t;
                    if (Array.isArray(t)) return t.map(n);
                    for (var o in t) t.hasOwnProperty(o) && (e = "function" != typeof r, 1 === r.length ? e = !r(o) : (i = t[o], e = !r(o, i = t[o], t)), e && (f[o] = i || t[o]));
                    return f
                }
                var t, e;
                return ("string" == typeof r || r instanceof String) && (t = r, r = function(r) {
                    return r === t
                }), Array.isArray(r) && (e = r, r = function(r) {
                    return e.indexOf(r) >= 0
                }), void 0 !== arguments[1] ? n(arguments[1]) : n
            }
        }
    }
]);
//# sourceMappingURL=5253.4a99f091c9005040.js.map