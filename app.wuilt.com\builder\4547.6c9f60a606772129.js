(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [4547, 419], {
        90419: (e, n, i) => {
            i.r(n), i.d(n, {
                ColorAndThemeSelector: () => a.H,
                ColorPicker: () => k.z,
                Link: () => u.r,
                useBreakpoints: () => r,
                useChakraBreakpoints: () => s.x,
                useClickOutside: () => d.O
            });
            var o = i(37900),
                t = i(1546);
            const r = () => {
                const [e, n] = (0, o.useState)("undefined" != typeof window ? window.innerWidth : 0);
                (0, o.useEffect)((() => {
                    const e = () => n(window.innerWidth);
                    if ("undefined" != typeof window) return window.addEventListener("resize", e), () => window.removeEventListener("resize", e)
                }), []);
                const i = e >= 320 && e <= 767,
                    r = e >= 768 && e <= 1025,
                    s = e >= 1026 && e <= 1440;
                return {
                    isMobile: i,
                    isTablet: r,
                    isDesktop: s,
                    breakpoint: i ? t.Uo.Mobile : r ? t.Uo.Tablet : s ? t.Uo.Desktop : e < 320 ? t.Uo.Mobile : t.Uo.Desktop,
                    breakpointWidth: e
                }
            };
            var s = i(54434),
                d = i(89597),
                u = i(56798),
                k = i(26862),
                a = i(71834)
        },
        15878: (e, n, i) => {
            function o(e, n) {
                if (null == e) return {};
                var i, o, t = {},
                    r = Object.keys(e);
                for (o = 0; o < r.length; o++) i = r[o], n.indexOf(i) >= 0 || (t[i] = e[i]);
                return t
            }
            i.d(n, {
                Z: () => o
            })
        }
    }
]);
//# sourceMappingURL=4547.6c9f60a606772129.js.map