(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [4285], {
        71834: (e, t, a) => {
            a.d(t, {
                H: () => b
            });
            var n = a(15878),
                r = a(37900),
                o = a.n(r),
                d = a(2547),
                l = a(26897),
                i = a(68264),
                s = a(89597),
                u = a(40690),
                c = a(7242),
                p = a(26862);
            const m = ["colorSelectorRef", "theme", "colorTypeAndValue", "onChangeColor"];

            function g() {
                return g = Object.assign ? Object.assign.bind() : function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var a = arguments[t];
                        for (var n in a) Object.prototype.hasOwnProperty.call(a, n) && (e[n] = a[n])
                    }
                    return e
                }, g.apply(this, arguments)
            }
            const b = e => {
                let {
                    colorSelectorRef: t,
                    theme: a,
                    colorTypeAndValue: r,
                    onChangeColor: b
                } = e, h = (0, n.Z)(e, m);
                const f = o().useRef(null),
                    x = o().useRef(null);
                (0, s.O)([x, f], (() => {
                    S()
                })), o().useEffect((() => {
                    t && x.current && (t.current = x.current)
                }), [x.current]);
                const E = null == a ? void 0 : a.colors,
                    y = {
                        brand: [null == E ? void 0 : E.brand1, null == E ? void 0 : E.brand2, null == E ? void 0 : E.brand3, null == E ? void 0 : E.brand4],
                        secondary: [null == E ? void 0 : E.secondary1, null == E ? void 0 : E.secondary2, null == E ? void 0 : E.secondary3, null == E ? void 0 : E.secondary4],
                        base: [null == E ? void 0 : E.base1, null == E ? void 0 : E.base2, null == E ? void 0 : E.base3, null == E ? void 0 : E.base4, null == E ? void 0 : E.base5],
                        accent: [null == E ? void 0 : E.accent1, null == E ? void 0 : E.accent2]
                    },
                    {
                        onOpen: M,
                        onClose: v,
                        isOpen: C
                    } = (0, d.useDisclosure)();

                function S() {
                    v()
                }
                return o().createElement(d.Popover, g({
                    isLazy: !0,
                    isOpen: C,
                    onOpen: function() {
                        M()
                    },
                    onClose: S,
                    styleConfig: {
                        baseStyle: {
                            popper: {
                                zIndex: 9999
                            }
                        }
                    },
                    placement: "bottom-start"
                }, h), o().createElement(d.PopoverTrigger, null, o().createElement(d.Stack, {
                    direction: "row",
                    gap: "6px",
                    width: "full",
                    borderRadius: "8px",
                    border: "1px solid",
                    borderColor: "gray.300",
                    py: "6px",
                    paddingInlineStart: "6px",
                    paddingInlineEnd: "8px",
                    minHeight: "40px",
                    alignItems: "center",
                    cursor: "pointer",
                    ref: f
                }, o().createElement(d.Box, {
                    width: "24px",
                    height: "24px",
                    borderRadius: "6px",
                    backgroundColor: r.value,
                    border: "1px solid",
                    borderColor: "gray.200"
                }), o().createElement(d.Text, {
                    variant: "textSm",
                    fontWeight: "medium",
                    color: "gray.700"
                }, r.type === i.Mr.Theme ? c.ny[null == r ? void 0 : r.themeColorKey] : r.value))), o().createElement(d.Portal, null, o().createElement(d.PopoverContent, {
                    ref: x,
                    width: "240px",
                    boxShadow: "lg",
                    borderRadius: "12px",
                    backgroundColor: "white",
                    border: "1px solid var(--chakra-colors-gray-200)",
                    onClick: e => {
                        e.stopPropagation()
                    }
                }, o().createElement(d.PopoverHeader, {
                    padding: "8px",
                    bgColor: "gray.25",
                    borderColor: "gray.200",
                    borderRadius: "12px",
                    borderBottom: "1px solid var(--chakra-colors-gray-200)"
                }, o().createElement(d.Flex, {
                    gap: "12px",
                    width: "full",
                    alignItems: "center",
                    justifyContent: "space-between"
                }, o().createElement(u.v, {
                    size: "sm",
                    variant: "tertiary",
                    defaultIndex: r.type === i.Mr.Custom ? 0 : 1,
                    panelPadding: "16px 0px",
                    tabs: [o().createElement(d.Text, {
                        key: "color-tab-1",
                        variant: "text2Xs",
                        fontWeight: "semibold"
                    }, o().createElement(l.FormattedMessage, {
                        defaultMessage: "Custom",
                        id: "Sjo1P4"
                    })), o().createElement(o().Fragment, null, a ? o().createElement(d.Text, {
                        key: "color-tab-0",
                        variant: "text2Xs",
                        fontWeight: "semibold"
                    }, o().createElement(l.FormattedMessage, {
                        defaultMessage: "Theme",
                        id: "Pe0ogR"
                    })) : null)],
                    panels: [o().createElement(p.z, {
                        key: "custom-color-panel-0",
                        value: r.value,
                        onChange: e => b({
                            type: i.Mr.Custom,
                            value: e,
                            themeColorKey: void 0
                        })
                    }), o().createElement(o().Fragment, null, a ? o().createElement(d.VStack, {
                        key: "theme-color-panel-0"
                    }, Object.entries(y).map((([e, t]) => o().createElement(d.Flex, {
                        key: `theme-${e}`,
                        gap: "6px",
                        width: "full",
                        alignItems: "center",
                        justifyContent: "space-between"
                    }, o().createElement(d.Text, {
                        textAlign: "start",
                        variant: "textXs",
                        fontWeight: "medium",
                        color: "gray.500"
                    }, c.v6[e]), o().createElement(d.Flex, {
                        gap: "4px",
                        alignItems: "center"
                    }, t.map(((t, a) => o().createElement(d.Box, {
                        key: `theme-color-${t}`,
                        width: "24px",
                        height: "24px",
                        borderRadius: "6px",
                        backgroundColor: t,
                        cursor: "pointer",
                        outline: t === (null == r ? void 0 : r.value) ? "2px solid var(--chakra-colors-primary-600)" : void 0,
                        border: t !== (null == r ? void 0 : r.value) ? "1px solid rgba(0, 0, 0, 0.1)" : void 0,
                        onClick: () => {
                            b({
                                type: i.Mr.Theme,
                                value: t,
                                themeColorKey: `${e.toLowerCase()}${a+1}`
                            })
                        }
                    })))))))) : null)]
                }))))))
            }
        },
        50470: (e, t, a) => {
            a.d(t, {
                z: () => u
            });
            var n = a(37900),
                r = a.n(n),
                o = a(2547),
                d = a(86696),
                l = a(16144),
                i = a.n(l),
                s = a(62923);
            const u = ({
                value: e,
                onChange: t
            }) => {
                const [a, l] = (0, n.useState)({
                    hex: i()(e).hex(),
                    alpha: Math.round(100 * i()(e).alpha())
                }), u = (e, t) => {
                    l((a => Object.assign({}, a, {
                        [e]: t
                    })))
                };
                return r().createElement(o.Box, {
                    zIndex: "popover",
                    w: "full",
                    h: "full",
                    position: "relative",
                    __css: {
                        "& .react-colorful__saturation": {
                            borderRadius: "4px"
                        },
                        "& .react-colorful__hue": {
                            height: "10px",
                            borderRadius: "10px",
                            marginTop: "8px",
                            border: "1px solid rgba(0, 0, 0, 0.1)"
                        },
                        "& .react-colorful__alpha": {
                            height: "10px",
                            borderRadius: "10px",
                            marginTop: "8px",
                            border: "1px solid rgba(0, 0, 0, 0.1)"
                        },
                        "& .react-colorful__pointer": {
                            width: "12px",
                            height: "12px",
                            backgroundColor: "white",
                            border: "2px solid white",
                            borderRadius: "50%"
                        }
                    }
                }, r().createElement(d.HexAlphaColorPicker, {
                    style: {
                        width: "100%"
                    },
                    color: i()(a.hex).alpha(a.alpha / 100).hexa().toUpperCase(),
                    onChange: e => {
                        l({
                            hex: i()(e).hex(),
                            alpha: Math.round(100 * i()(e).alpha())
                        })
                    },
                    onMouseUp: () => {
                        t(i()(a.hex).alpha(a.alpha / 100).hexa().toUpperCase())
                    }
                }), r().createElement(o.InputGroup, {
                    mt: "5px",
                    outline: "1px solid",
                    borderRadius: "4px",
                    outlineColor: "gray.300",
                    _hover: {
                        outlineColor: "gray.400"
                    },
                    _focusWithin: {
                        outlineWidth: "2px",
                        outlineColor: "primary.600"
                    }
                }, r().createElement(s.DebounceInput, {
                    flex: "5",
                    color: "gray.800",
                    padding: "6px",
                    fontSize: "10px",
                    height: "fit-content",
                    lightHeight: "14px",
                    letterSpacing: "-0.2px",
                    textTransform: "uppercase",
                    borderInlineEnd: "1px solid",
                    borderRadius: "4px",
                    borderColor: "gray.300",
                    style: {
                        borderStartEndRadius: "0px",
                        borderEndEndRadius: "0px"
                    },
                    outline: "none",
                    _hover: {
                        outline: "none",
                        borderColor: "gray.300"
                    },
                    _focus: {
                        outline: "none",
                        borderColor: "gray.300"
                    },
                    element: o.Input,
                    debounceTimeout: 1e3,
                    value: a.hex,
                    maxLength: 7,
                    onBeforeInput: e => {
                        const t = e.data;
                        if (null === t) return;
                        const a = e.target,
                            {
                                selectionStart: n,
                                selectionEnd: r,
                                value: o
                            } = a,
                            d = o.slice(0, n) + t + o.slice(r);
                        /^#([0-9a-fA-F]{0,6})?$/.test(d) || e.preventDefault()
                    },
                    onChange: e => {
                        const n = e.target.value;
                        7 === n.length && (u("hex", n), t(i()(n).alpha(a.alpha / 100).hexa().toUpperCase()))
                    }
                }), r().createElement(s.DebounceInput, {
                    flex: "2",
                    type: "number",
                    color: "gray.800",
                    padding: "6px",
                    fontSize: "10px",
                    height: "fit-content",
                    lightHeight: "14px",
                    letterSpacing: "-0.2px",
                    textTransform: "uppercase",
                    borderRadius: "4px",
                    style: {
                        borderStartStartRadius: "0px",
                        borderEndStartRadius: "0px"
                    },
                    outline: "none",
                    _hover: {
                        outline: "none"
                    },
                    _focus: {
                        outline: "none"
                    },
                    element: o.Input,
                    debounceTimeout: 1e3,
                    min: 1,
                    max: 100,
                    value: a.alpha,
                    onChange: e => {
                        var n;
                        u("alpha", Math.round(Number(null != (n = e.target.value) ? n : 1))), t(i()(a.hex).alpha(a.alpha / 100).hexa().toUpperCase())
                    }
                }), r().createElement(o.InputRightElement, {
                    padding: "6px",
                    height: "fit-content",
                    width: "fit-content"
                }, r().createElement(o.Text, {
                    variant: "textXs",
                    color: "gray.500"
                }, "%"))))
            }
        },
        26862: (e, t, a) => {
            a.d(t, {
                z: () => n.z
            });
            var n = a(50470)
        },
        56798: (e, t, a) => {
            a.d(t, {
                r: () => u
            });
            var n = a(15878),
                r = a(37900),
                o = a.n(r),
                d = a(86866),
                l = a(2547);
            const i = ["to", "href", "children"];

            function s() {
                return s = Object.assign ? Object.assign.bind() : function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var a = arguments[t];
                        for (var n in a) Object.prototype.hasOwnProperty.call(a, n) && (e[n] = a[n])
                    }
                    return e
                }, s.apply(this, arguments)
            }
            const u = (0, l.forwardRef)(((e, t) => {
                let {
                    to: a,
                    href: r,
                    children: u
                } = e, c = (0, n.Z)(e, i);
                return o().createElement(l.Link, s({
                    ref: t,
                    to: null != a ? a : r,
                    as: d.NavLink,
                    _hover: {
                        textDecoration: "underline"
                    }
                }, c), u)
            }))
        },
        40690: (e, t, a) => {
            a.d(t, {
                v: () => i
            });
            var n = a(37900),
                r = a.n(n),
                o = a(2547),
                d = function(e) {
                    return e.Primary = "primary", e.Secondary = "secondary", e.Tertiary = "tertiary", e
                }(d || {}),
                l = function(e) {
                    return e.Small = "sm", e.Medium = "md", e
                }(l || {});
            const i = ({
                    size: e,
                    tabs: t,
                    panels: a,
                    variant: n = d.Primary,
                    defaultIndex: o = 0,
                    panelPadding: l,
                    onTabChange: i
                }) => n === d.Secondary ? r().createElement(u, {
                    tabs: t,
                    panels: a,
                    defaultIndex: o,
                    panelPadding: l,
                    onTabChange: i
                }) : n === d.Tertiary ? r().createElement(c, {
                    size: e,
                    tabs: t,
                    panels: a,
                    defaultIndex: o,
                    panelPadding: l,
                    onTabChange: i
                }) : r().createElement(s, {
                    tabs: t,
                    panels: a,
                    defaultIndex: o,
                    panelPadding: l,
                    onTabChange: i
                }),
                s = ({
                    tabs: e,
                    panels: t,
                    defaultIndex: a = 0,
                    panelPadding: n,
                    onTabChange: d
                }) => {
                    const [l, i] = r().useState(a);
                    return r().createElement(o.Tabs, {
                        isLazy: !0,
                        isFitted: !0,
                        minWidth: "180px",
                        defaultIndex: l,
                        onChange: function(e) {
                            i(e), null == d || d(e)
                        }
                    }, r().createElement(o.TabList, null, e && e.map(((e, t) => r().createElement(o.Tab, {
                        padding: "12px",
                        key: `tab-${t}`
                    }, e)))), r().createElement(o.TabPanels, {
                        padding: "0px"
                    }, t && t.map(((e, t) => r().createElement(o.TabPanel, {
                        key: `panel-${t}`,
                        padding: n || "16px !important"
                    }, e)))))
                },
                u = ({
                    tabs: e,
                    panels: t,
                    defaultIndex: a = 0,
                    panelPadding: n,
                    onTabChange: d
                }) => {
                    const [l, i] = r().useState(a);
                    return r().createElement(o.Tabs, {
                        isLazy: !0,
                        isFitted: !0,
                        minWidth: "180px",
                        variant: "unstyled",
                        width: "fit-content",
                        defaultIndex: l,
                        onChange: function(e) {
                            i(e), null == d || d(e)
                        }
                    }, r().createElement(o.TabList, {
                        border: "none",
                        borderRadius: "8px"
                    }, e && e.map(((t, a) => r().createElement(o.Tab, {
                        padding: "0px",
                        key: `secondary-tab-group-tab-${a}`,
                        _selected: {
                            border: "none"
                        }
                    }, r().createElement(o.Button, {
                        size: "sm",
                        width: "full",
                        height: "36px",
                        padding: "8px",
                        borderRadius: "0px",
                        marginInlineStart: 0 !== a ? "-1px" : void 0,
                        style: 0 === a ? {
                            borderStartStartRadius: "8px",
                            borderEndStartRadius: "8px"
                        } : a === e.length - 1 ? {
                            borderStartEndRadius: "8px",
                            borderEndEndRadius: "8px"
                        } : {
                            borderRadius: "0px"
                        },
                        variant: l === a ? "primary" : "secondaryGray",
                        borderColor: "gray.300",
                        _hover: {
                            borderColor: "gray.300"
                        },
                        _focus: {
                            borderColor: "gray.300"
                        }
                    }, t))))), r().createElement(o.TabPanels, {
                        padding: "0px"
                    }, t && t.map(((e, t) => r().createElement(o.TabPanel, {
                        key: `panel-${t}`,
                        padding: n || "16px !important"
                    }, e)))))
                },
                c = ({
                    size: e,
                    tabs: t,
                    panels: a,
                    defaultIndex: n = 0,
                    panelPadding: d,
                    onTabChange: i
                }) => {
                    const [s, u] = r().useState(n);
                    return r().createElement(o.Tabs, {
                        isLazy: !0,
                        isFitted: !0,
                        minWidth: "180px",
                        variant: "unstyled",
                        defaultIndex: s,
                        onChange: function(e) {
                            u(e), null == i || i(e)
                        }
                    }, r().createElement(o.TabList, {
                        gap: "2px",
                        border: "1px solid",
                        borderRadius: "8px",
                        borderColor: "gray.200",
                        backgroundColor: "gray.50"
                    }, t && t.map(((t, a) => r().createElement(o.Tab, {
                        padding: "0px",
                        key: `tab-${a}`,
                        _selected: {
                            border: "none"
                        }
                    }, r().createElement(o.Button, {
                        size: "sm",
                        width: "full",
                        height: "full",
                        borderRadius: "6px",
                        border: "none",
                        color: s === a ? "gray.800" : "gray.500",
                        outline: s === a ? "1px solid var(--chakra-colors-gray-300)" : void 0,
                        variant: s === a ? "secondaryGray" : "linkGray",
                        _hover: {
                            bgColor: s === a ? "white" : void 0
                        },
                        padding: e === l.Small ? "6px 8px" : "8px 12px"
                    }, t))))), r().createElement(o.TabPanels, {
                        padding: "0px"
                    }, a && a.map(((e, t) => r().createElement(o.TabPanel, {
                        key: `panel-${t}`,
                        padding: d || "16px !important"
                    }, e)))))
                }
        },
        7242: (e, t, a) => {
            a.d(t, {
                ee: () => i,
                ny: () => u,
                pu: () => l,
                v6: () => s
            });
            var n = a(37900),
                r = a.n(n),
                o = a(68264),
                d = a(26897);
            const l = {
                    [o.U1.Text]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Text",
                        id: "aA8bDw"
                    }),
                    [o.U1.Heading]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Heading",
                        id: "jT6/da"
                    }),
                    [o.U1.Button]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Button",
                        id: "KP63fg"
                    }),
                    [o.U1.Image]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Image",
                        id: "+0zv6g"
                    }),
                    [o.U1.AppStoreButton]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "App store button",
                        id: "imf6rn"
                    }),
                    [o.U1.SocialLinks]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Social icons",
                        id: "Oa6tkb"
                    }),
                    [o.U1.Form]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Form",
                        id: "baRFiF"
                    }),
                    [o.U1.Video]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Video",
                        id: "kBJUtE"
                    }),
                    [o.U1.Counter]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Count up",
                        id: "5IKcqz"
                    }),
                    [o.U1.Embed]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Embed",
                        id: "bp3HuS"
                    }),
                    [o.U1.Map]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Map",
                        id: "zuqMfZ"
                    })
                },
                i = {
                    [o.Hd.Text]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Text settings",
                        id: "8dw8Bg"
                    }),
                    [o.Hd.Button]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Button settings",
                        id: "7krETq"
                    }),
                    [o.Hd.Image]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Image settings",
                        id: "VNUpYp"
                    }),
                    [o.Hd.AppStoreButton]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "App store button",
                        id: "imf6rn"
                    }),
                    [o.Hd.SocialLinks]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Social icons settings",
                        id: "EsV1qu"
                    }),
                    [o.Hd.Form]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Form settings",
                        id: "C7h90Y"
                    }),
                    [o.Hd.Video]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Video settings",
                        id: "qFSzHO"
                    }),
                    [o.Hd.Counter]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Count up settings",
                        id: "IkNgzS"
                    }),
                    [o.Hd.Embed]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Embed settings",
                        id: "sKvU09"
                    }),
                    [o.Hd.Map]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Map settings",
                        id: "xQwoWr"
                    })
                },
                s = {
                    [o.HV.brand]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "BRAND",
                        id: "Sige6r"
                    }),
                    [o.HV.secondary]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "SECONDARY",
                        id: "bmf427"
                    }),
                    [o.HV.base]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "BASE",
                        id: "36a/d2"
                    }),
                    [o.HV.accent]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "ACCENT",
                        id: "1LQhdn"
                    })
                },
                u = {
                    [o.tP.brand1]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Brand 1",
                        id: "3dJBUT"
                    }),
                    [o.tP.brand2]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Brand 2",
                        id: "hW1msu"
                    }),
                    [o.tP.brand3]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Brand 3",
                        id: "2D3Xp6"
                    }),
                    [o.tP.brand4]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Brand 4",
                        id: "MMiDUm"
                    }),
                    [o.tP.secondary1]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Secondary 1",
                        id: "7XFckP"
                    }),
                    [o.tP.secondary2]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Secondary 2",
                        id: "JHZeDx"
                    }),
                    [o.tP.secondary3]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Secondary 3",
                        id: "rqNzPi"
                    }),
                    [o.tP.secondary4]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Secondary 4",
                        id: "aE8By1"
                    }),
                    [o.tP.base1]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Base 1",
                        id: "0ea3tj"
                    }),
                    [o.tP.base2]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Base 2",
                        id: "4QgpyJ"
                    }),
                    [o.tP.base3]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Base 3",
                        id: "kvz1Vo"
                    }),
                    [o.tP.base4]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Base 4",
                        id: "+N/hFW"
                    }),
                    [o.tP.base5]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Base 5",
                        id: "ocpHTy"
                    }),
                    [o.tP.accent1]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Accent 1",
                        id: "gkqpVi"
                    }),
                    [o.tP.accent2]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Accent 2",
                        id: "drE0wm"
                    }),
                    [o.tP.transparent]: r().createElement(d.FormattedMessage, {
                        defaultMessage: "Transparent",
                        id: "dF3WgL"
                    })
                }
        },
        54434: (e, t, a) => {
            a.d(t, {
                x: () => d
            });
            var n = a(37900),
                r = a(1546),
                o = a(2547);
            const d = () => {
                const [e] = (0, o.useMediaQuery)("(min-width: 320px) and (max-width: 767px)", {
                    ssr: !0,
                    fallback: !1
                }), [t] = (0, o.useMediaQuery)("(min-width: 768px) and (max-width: 1025px)", {
                    ssr: !0,
                    fallback: !1
                }), [a] = (0, o.useMediaQuery)("(min-width: 1026px) and (max-width: 1440px)", {
                    ssr: !0,
                    fallback: !1
                }), [d, l] = (0, n.useState)("undefined" != typeof window ? window.innerWidth : 0);
                return (0, n.useEffect)((() => {
                    const e = () => l(window.innerWidth);
                    if ("undefined" != typeof window) return window.addEventListener("resize", e), () => window.removeEventListener("resize", e)
                }), []), {
                    isMobile: e,
                    isTablet: t,
                    isDesktop: a,
                    breakpoint: e ? r.Uo.Mobile : t ? r.Uo.Tablet : a ? r.Uo.Desktop : d < 320 ? r.Uo.Mobile : r.Uo.Desktop,
                    breakpointWidth: d
                }
            }
        },
        89597: (e, t, a) => {
            a.d(t, {
                O: () => o
            });
            var n = a(37900),
                r = a.n(n);

            function o(e, t) {
                r().useEffect((() => {
                    function a(a) {
                        const n = a.target;
                        !e.every((e => {
                            var t;
                            return !(null != e && null != (t = e.current) && t.contains(n))
                        })) || null != n && n.closest(".pac-container") || t()
                    }
                    return document.addEventListener("mousedown", a), () => {
                        document.removeEventListener("mousedown", a)
                    }
                }), [e, t])
            }
        },
        49852: (e, t, a) => {
            a.d(t, {
                $J: () => i,
                Do: () => m,
                Hd: () => n,
                K8: () => s,
                Qv: () => b,
                UH: () => f,
                Ut: () => g,
                W7: () => h,
                Yj: () => c,
                fS: () => p,
                gp: () => r,
                mP: () => u,
                u2: () => l,
                u7: () => d,
                zo: () => o
            });
            let n = function(e) {
                    return e.Text = "Text", e.Button = "Button", e.Image = "Image", e.AppStoreButton = "AppStoreButton", e.SocialLinks = "SocialLinks", e.Form = "Form", e.Video = "Video", e.Counter = "Counter", e.Embed = "Embed", e.Map = "Map", e
                }({}),
                r = function(e) {
                    return e.Primary = "Primary", e.Secondary = "Secondary", e.Tertiary = "Tertiary", e
                }({}),
                o = function(e) {
                    return e.Fit = "Fit", e.Fill = "Fill", e
                }({}),
                d = function(e) {
                    return e.InternalLink = "InternalLink", e.ExternalLink = "ExternalLink", e.Email = "Email", e.Phone = "Phone", e.Button = "Button", e.Submit = "Submit", e
                }({}),
                l = function(e) {
                    return e.InternalLink = "InternalLink", e.ExternalLink = "ExternalLink", e.ScaleUp = "ScaleUp", e.DoNothing = "DoNothing", e
                }({}),
                i = function(e) {
                    return e.Small = "Small", e.Medium = "Medium", e.Large = "Large", e
                }({}),
                s = function(e) {
                    return e.Apple = "Apple", e.Google = "Google", e.Samsung = "Samsung", e.Huawei = "Huawei", e
                }({}),
                u = function(e) {
                    return e.Brand = "Brand", e.Black = "Black", e.White = "White", e
                }({}),
                c = function(e) {
                    return e.Behance = "behance", e.Discord = "discord", e.Dribbble = "dribbble", e.Facebook = "facebook", e.Messenger = "messenger", e.Github = "github", e.Instagram = "instagram", e.Linkedin = "linkedin", e.Pinterest = "pinterest", e.Skype = "skype", e.Telegram = "telegram", e.Tiktok = "tiktok", e.Twitter = "twitter", e.Whatsapp = "whatsapp", e.Youtube = "youtube", e.Snapchat = "snapchat", e
                }({}),
                p = function(e) {
                    return e.Text = "Text", e.Email = "Email", e.Phone = "Phone", e.TextArea = "TextArea", e.Link = "Link", e.Date = "Date", e.Checkbox = "Checkbox", e.Radio = "Radio", e.Select = "Select", e.Time = "Time", e.File = "File", e
                }({}),
                m = function(e) {
                    return e.Message = "Message", e.Redirect = "Redirect", e
                }({}),
                g = function(e) {
                    return e.Name = "name", e.UserName = "username", e.OrganizationTitle = "organization-title", e.Organization = "organization", e.StreetAddress = "street-address", e.AddressLine1 = "address-line1", e.AddressLine2 = "address-line2", e.AddressLine3 = "address-line3", e.Country = "country", e.CountryName = "country-name", e.PostalCode = "postal-code", e.Language = "language", e.Sex = "sex", e.Email = "email", e.Phone = "phone", e.Url = "url", e
                }({}),
                b = function(e) {
                    return e.Limited = "Limited", e.Unlimited = "Unlimited", e
                }({}),
                h = function(e) {
                    return e.Filled = "Filled", e.Outlined = "Outlined", e.Colored = "Colored", e
                }({}),
                f = function(e) {
                    return e.Standard = "standard", e.Dark = "dark", e.Light = "light", e.NaturalBlue = "naturalBlue", e.Retro = "retro", e
                }({})
        },
        1546: (e, t, a) => {
            a.d(t, {
                Eo: () => l,
                HV: () => m,
                MF: () => h,
                Mr: () => i,
                U1: () => n,
                Uo: () => b,
                W5: () => u,
                _S: () => s,
                am: () => o,
                dv: () => c,
                h2: () => p,
                l8: () => d,
                ow: () => f,
                tP: () => g,
                xd: () => r
            });
            let n = function(e) {
                    return e.Text = "Text", e.Heading = "Heading", e.Button = "Button", e.Image = "Image", e.AppStoreButton = "AppStoreButton", e.SocialLinks = "SocialLinks", e.Form = "Form", e.Video = "Video", e.Counter = "Counter", e.Embed = "Embed", e.Map = "Map", e
                }({}),
                r = function(e) {
                    return e.Top = "top", e.Center = "center", e.Bottom = "bottom", e
                }({}),
                o = function(e) {
                    return e.Start = "start", e.Center = "center", e.End = "end", e
                }({}),
                d = function(e) {
                    return e[e.None = 0] = "None", e[e.Small = 20] = "Small", e[e.Medium = 40] = "Medium", e[e.Large = 60] = "Large", e
                }({}),
                l = function(e) {
                    return e.Color = "Color", e.Image = "Image", e.Video = "Video", e
                }({}),
                i = function(e) {
                    return e.Theme = "Theme", e.Custom = "Custom", e.Gradient = "Gradient", e
                }({}),
                s = function(e) {
                    return e.LeftTop = "left top", e.CenterTop = "center top", e.RightTop = "right top", e.LeftCenter = "left center", e.CenterCenter = "center center", e.RightCenter = "right center", e.LeftBottom = "left bottom", e.CenterBottom = "center bottom", e.RightBottom = "right bottom", e
                }({}),
                u = function(e) {
                    return e.Repeat = "repeat", e.NoRepeat = "no-repeat", e
                }({}),
                c = function(e) {
                    return e.Fixed = "fixed", e.Scroll = "scroll", e
                }({}),
                p = function(e) {
                    return e.Cover = "cover", e.Contain = "contain", e
                }({}),
                m = function(e) {
                    return e.brand = "brand", e.secondary = "secondary", e.base = "base", e.accent = "accent", e
                }({}),
                g = function(e) {
                    return e.brand1 = "brand1", e.brand2 = "brand2", e.brand3 = "brand3", e.brand4 = "brand4", e.secondary1 = "secondary1", e.secondary2 = "secondary2", e.secondary3 = "secondary3", e.secondary4 = "secondary4", e.base1 = "base1", e.base2 = "base2", e.base3 = "base3", e.base4 = "base4", e.base5 = "base5", e.accent1 = "accent1", e.accent2 = "accent2", e.transparent = "transparent", e
                }({}),
                b = function(e) {
                    return e.Mobile = "mobile", e.Tablet = "tablet", e.Desktop = "desktop", e
                }({}),
                h = function(e) {
                    return e.Top = "top", e.Right = "right", e.Bottom = "bottom", e.Left = "left", e
                }({}),
                f = function(e) {
                    return e.Grid = "grid", e.Element = "element", e
                }({})
        },
        68264: (e, t, a) => {
            a.d(t, {
                $J: () => r.$J,
                Do: () => r.Do,
                Eo: () => n.Eo,
                HV: () => n.HV,
                Hd: () => r.Hd,
                K8: () => r.K8,
                MF: () => n.MF,
                Mr: () => n.Mr,
                Qv: () => r.Qv,
                U1: () => n.U1,
                UH: () => r.UH,
                Uo: () => n.Uo,
                Ut: () => r.Ut,
                W5: () => n.W5,
                W7: () => r.W7,
                Yj: () => r.Yj,
                _S: () => n._S,
                am: () => n.am,
                dv: () => n.dv,
                fS: () => r.fS,
                gp: () => r.gp,
                h2: () => n.h2,
                l8: () => n.l8,
                mP: () => r.mP,
                ow: () => n.ow,
                tP: () => n.tP,
                u2: () => r.u2,
                u7: () => r.u7,
                xd: () => n.xd,
                zo: () => r.zo
            });
            var n = a(1546),
                r = a(49852)
        }
    }
]);
//# sourceMappingURL=4285.0bb9fc07d0559483.js.map