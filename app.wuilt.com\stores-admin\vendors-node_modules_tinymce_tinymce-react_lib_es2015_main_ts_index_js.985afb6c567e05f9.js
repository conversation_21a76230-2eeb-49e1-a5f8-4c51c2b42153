(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_tinymce_tinymce-react_lib_es2015_main_ts_index_js"], {

        /***/
        "../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/ScriptLoader2.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    ScriptLoader: () => ( /* binding */ ScriptLoader)
                    /* harmony export */
                });
                /* harmony import */
                var _Utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/Utils.js");
                var __assign = (undefined && undefined.__assign) || function() {
                    __assign = Object.assign || function(t) {
                        for (var s, i = 1, n = arguments.length; i < n; i++) {
                            s = arguments[i];
                            for (var p in s)
                                if (Object.prototype.hasOwnProperty.call(s, p))
                                    t[p] = s[p];
                        }
                        return t;
                    };
                    return __assign.apply(this, arguments);
                };

                var injectScriptTag = function(doc, item, handler) {
                    var _a, _b;
                    var scriptTag = doc.createElement('script');
                    scriptTag.referrerPolicy = 'origin';
                    scriptTag.type = 'application/javascript';
                    scriptTag.id = item.id;
                    scriptTag.src = item.src;
                    scriptTag.async = (_a = item.async) !== null && _a !== void 0 ? _a : false;
                    scriptTag.defer = (_b = item.defer) !== null && _b !== void 0 ? _b : false;
                    var loadHandler = function() {
                        scriptTag.removeEventListener('load', loadHandler);
                        scriptTag.removeEventListener('error', errorHandler);
                        handler(item.src);
                    };
                    var errorHandler = function(err) {
                        scriptTag.removeEventListener('load', loadHandler);
                        scriptTag.removeEventListener('error', errorHandler);
                        handler(item.src, err);
                    };
                    scriptTag.addEventListener('load', loadHandler);
                    scriptTag.addEventListener('error', errorHandler);
                    if (doc.head) {
                        doc.head.appendChild(scriptTag);
                    }
                };
                var createDocumentScriptLoader = function(doc) {
                    var lookup = {};
                    var scriptLoadOrErrorHandler = function(src, err) {
                        var item = lookup[src];
                        item.done = true;
                        item.error = err;
                        for (var _i = 0, _a = item.handlers; _i < _a.length; _i++) {
                            var h = _a[_i];
                            h(src, err);
                        }
                        item.handlers = [];
                    };
                    var loadScripts = function(items, success, failure) {
                        // eslint-disable-next-line no-console
                        var failureOrLog = function(err) {
                            return failure !== undefined ? failure(err) : console.error(err);
                        };
                        if (items.length === 0) {
                            failureOrLog(new Error('At least one script must be provided'));
                            return;
                        }
                        var successCount = 0;
                        var failed = false;
                        var loaded = function(_src, err) {
                            if (failed) {
                                return;
                            }
                            if (err) {
                                failed = true;
                                failureOrLog(err);
                            } else if (++successCount === items.length) {
                                success();
                            }
                        };
                        for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {
                            var item = items_1[_i];
                            var existing = lookup[item.src];
                            if (existing) {
                                if (existing.done) {
                                    loaded(item.src, existing.error);
                                } else {
                                    existing.handlers.push(loaded);
                                }
                            } else {
                                // create a new entry
                                var id = (0, _Utils__WEBPACK_IMPORTED_MODULE_0__.uuid)('tiny-');
                                lookup[item.src] = {
                                    id: id,
                                    src: item.src,
                                    done: false,
                                    error: null,
                                    handlers: [loaded],
                                };
                                injectScriptTag(doc, __assign({
                                    id: id
                                }, item), scriptLoadOrErrorHandler);
                            }
                        }
                    };
                    var deleteScripts = function() {
                        var _a;
                        for (var _i = 0, _b = Object.values(lookup); _i < _b.length; _i++) {
                            var item = _b[_i];
                            var scriptTag = doc.getElementById(item.id);
                            if (scriptTag != null && scriptTag.tagName === 'SCRIPT') {
                                (_a = scriptTag.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(scriptTag);
                            }
                        }
                        lookup = {};
                    };
                    var getDocument = function() {
                        return doc;
                    };
                    return {
                        loadScripts: loadScripts,
                        deleteScripts: deleteScripts,
                        getDocument: getDocument
                    };
                };
                var createScriptLoader = function() {
                    var cache = [];
                    var getDocumentScriptLoader = function(doc) {
                        var loader = cache.find(function(l) {
                            return l.getDocument() === doc;
                        });
                        if (loader === undefined) {
                            loader = createDocumentScriptLoader(doc);
                            cache.push(loader);
                        }
                        return loader;
                    };
                    var loadList = function(doc, items, delay, success, failure) {
                        var doLoad = function() {
                            return getDocumentScriptLoader(doc).loadScripts(items, success, failure);
                        };
                        if (delay > 0) {
                            setTimeout(doLoad, delay);
                        } else {
                            doLoad();
                        }
                    };
                    var reinitialize = function() {
                        for (var loader = cache.pop(); loader != null; loader = cache.pop()) {
                            loader.deleteScripts();
                        }
                    };
                    return {
                        loadList: loadList,
                        reinitialize: reinitialize
                    };
                };
                var ScriptLoader = createScriptLoader();


                /***/
            }),

        /***/
        "../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/TinyMCE.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    getTinymce: () => ( /* binding */ getTinymce)
                    /* harmony export */
                });
                var getTinymce = function(view) {
                    var global = view;
                    return global && global.tinymce ? global.tinymce : null;
                };



                /***/
            }),

        /***/
        "../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/Utils.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    configHandlers: () => ( /* binding */ configHandlers),
                    /* harmony export */
                    configHandlers2: () => ( /* binding */ configHandlers2),
                    /* harmony export */
                    isBeforeInputEventAvailable: () => ( /* binding */ isBeforeInputEventAvailable),
                    /* harmony export */
                    isFunction: () => ( /* binding */ isFunction),
                    /* harmony export */
                    isInDoc: () => ( /* binding */ isInDoc),
                    /* harmony export */
                    isTextareaOrInput: () => ( /* binding */ isTextareaOrInput),
                    /* harmony export */
                    mergePlugins: () => ( /* binding */ mergePlugins),
                    /* harmony export */
                    setMode: () => ( /* binding */ setMode),
                    /* harmony export */
                    uuid: () => ( /* binding */ uuid)
                    /* harmony export */
                });
                /* harmony import */
                var _components_EditorPropTypes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/components/EditorPropTypes.js");

                var isFunction = function(x) {
                    return typeof x === 'function';
                };
                var isEventProp = function(name) {
                    return name in _components_EditorPropTypes__WEBPACK_IMPORTED_MODULE_0__.eventPropTypes;
                };
                var eventAttrToEventName = function(attrName) {
                    return attrName.substr(2);
                };
                var configHandlers2 = function(handlerLookup, on, off, adapter, prevProps, props, boundHandlers) {
                    var prevEventKeys = Object.keys(prevProps).filter(isEventProp);
                    var currEventKeys = Object.keys(props).filter(isEventProp);
                    var removedKeys = prevEventKeys.filter(function(key) {
                        return props[key] === undefined;
                    });
                    var addedKeys = currEventKeys.filter(function(key) {
                        return prevProps[key] === undefined;
                    });
                    removedKeys.forEach(function(key) {
                        // remove event handler
                        var eventName = eventAttrToEventName(key);
                        var wrappedHandler = boundHandlers[eventName];
                        off(eventName, wrappedHandler);
                        delete boundHandlers[eventName];
                    });
                    addedKeys.forEach(function(key) {
                        var wrappedHandler = adapter(handlerLookup, key);
                        var eventName = eventAttrToEventName(key);
                        boundHandlers[eventName] = wrappedHandler;
                        on(eventName, wrappedHandler);
                    });
                };
                var configHandlers = function(editor, prevProps, props, boundHandlers, lookup) {
                    return configHandlers2(lookup, editor.on.bind(editor), editor.off.bind(editor),
                        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                        function(handlerLookup, key) {
                            return function(e) {
                                var _a;
                                return (_a = handlerLookup(key)) === null || _a === void 0 ? void 0 : _a(e, editor);
                            };
                        }, prevProps, props, boundHandlers);
                };
                var unique = 0;
                var uuid = function(prefix) {
                    var time = Date.now();
                    var random = Math.floor(Math.random() * 1000000000);
                    unique++;
                    return prefix + '_' + random + unique + String(time);
                };
                var isTextareaOrInput = function(element) {
                    return element !== null && (element.tagName.toLowerCase() === 'textarea' || element.tagName.toLowerCase() === 'input');
                };
                var normalizePluginArray = function(plugins) {
                    if (typeof plugins === 'undefined' || plugins === '') {
                        return [];
                    }
                    return Array.isArray(plugins) ? plugins : plugins.split(' ');
                };
                // eslint-disable-next-line max-len
                var mergePlugins = function(initPlugins, inputPlugins) {
                    return normalizePluginArray(initPlugins).concat(normalizePluginArray(inputPlugins));
                };
                var isBeforeInputEventAvailable = function() {
                    return window.InputEvent && typeof InputEvent.prototype.getTargetRanges === 'function';
                };
                var isInDoc = function(elem) {
                    if (!('isConnected' in Node.prototype)) {
                        // Fallback for IE and old Edge
                        var current = elem;
                        var parent_1 = elem.parentNode;
                        while (parent_1 != null) {
                            current = parent_1;
                            parent_1 = current.parentNode;
                        }
                        return current === elem.ownerDocument;
                    }
                    return elem.isConnected;
                };
                var setMode = function(editor, mode) {
                    if (editor !== undefined) {
                        if (editor.mode != null && typeof editor.mode === 'object' && typeof editor.mode.set === 'function') {
                            editor.mode.set(mode);
                        } else { // support TinyMCE 4
                            editor.setMode(mode);
                        }
                    }
                };


                /***/
            }),

        /***/
        "../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/components/Editor.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    Editor: () => ( /* binding */ Editor)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?49ef");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _ScriptLoader2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/ScriptLoader2.js");
                /* harmony import */
                var _TinyMCE__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/TinyMCE.js");
                /* harmony import */
                var _Utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/Utils.js");
                /* harmony import */
                var _EditorPropTypes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/components/EditorPropTypes.js");
                var __extends = (undefined && undefined.__extends) || (function() {
                    var extendStatics = function(d, b) {
                        extendStatics = Object.setPrototypeOf ||
                            ({
                                    __proto__: []
                                }
                                instanceof Array && function(d, b) {
                                    d.__proto__ = b;
                                }) ||
                            function(d, b) {
                                for (var p in b)
                                    if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
                            };
                        return extendStatics(d, b);
                    };
                    return function(d, b) {
                        if (typeof b !== "function" && b !== null)
                            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
                        extendStatics(d, b);

                        function __() {
                            this.constructor = d;
                        }
                        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
                    };
                })();
                var __assign = (undefined && undefined.__assign) || function() {
                    __assign = Object.assign || function(t) {
                        for (var s, i = 1, n = arguments.length; i < n; i++) {
                            s = arguments[i];
                            for (var p in s)
                                if (Object.prototype.hasOwnProperty.call(s, p))
                                    t[p] = s[p];
                        }
                        return t;
                    };
                    return __assign.apply(this, arguments);
                };





                var Editor = /** @class */ (function(_super) {
                    __extends(Editor, _super);

                    function Editor(props) {
                        var _a, _b, _c;
                        var _this = _super.call(this, props) || this;
                        _this.rollbackTimer = undefined;
                        _this.valueCursor = undefined;
                        _this.rollbackChange = function() {
                            var editor = _this.editor;
                            var value = _this.props.value;
                            if (editor && value && value !== _this.currentContent) {
                                editor.undoManager.ignore(function() {
                                    editor.setContent(value);
                                    // only restore cursor on inline editors when they are focused
                                    // as otherwise it will cause a focus grab
                                    if (_this.valueCursor && (!_this.inline || editor.hasFocus())) {
                                        try {
                                            editor.selection.moveToBookmark(_this.valueCursor);
                                        } catch (e) { /* ignore */ }
                                    }
                                });
                            }
                            _this.rollbackTimer = undefined;
                        };
                        _this.handleBeforeInput = function(_evt) {
                            if (_this.props.value !== undefined && _this.props.value === _this.currentContent && _this.editor) {
                                if (!_this.inline || _this.editor.hasFocus()) {
                                    try {
                                        // getBookmark throws exceptions when the editor has not been focused
                                        // possibly only in inline mode but I'm not taking chances
                                        _this.valueCursor = _this.editor.selection.getBookmark(3);
                                    } catch (e) { /* ignore */ }
                                }
                            }
                        };
                        _this.handleBeforeInputSpecial = function(evt) {
                            if (evt.key === 'Enter' || evt.key === 'Backspace' || evt.key === 'Delete') {
                                _this.handleBeforeInput(evt);
                            }
                        };
                        _this.handleEditorChange = function(_evt) {
                            var editor = _this.editor;
                            if (editor && editor.initialized) {
                                var newContent = editor.getContent();
                                if (_this.props.value !== undefined && _this.props.value !== newContent && _this.props.rollback !== false) {
                                    // start a timer and revert to the value if not applied in time
                                    if (!_this.rollbackTimer) {
                                        _this.rollbackTimer = window.setTimeout(_this.rollbackChange, typeof _this.props.rollback === 'number' ? _this.props.rollback : 200);
                                    }
                                }
                                if (newContent !== _this.currentContent) {
                                    _this.currentContent = newContent;
                                    if ((0, _Utils__WEBPACK_IMPORTED_MODULE_3__.isFunction)(_this.props.onEditorChange)) {
                                        _this.props.onEditorChange(newContent, editor);
                                    }
                                }
                            }
                        };
                        _this.handleEditorChangeSpecial = function(evt) {
                            if (evt.key === 'Backspace' || evt.key === 'Delete') {
                                _this.handleEditorChange(evt);
                            }
                        };
                        _this.initialise = function(attempts) {
                            var _a, _b, _c;
                            if (attempts === void 0) {
                                attempts = 0;
                            }
                            var target = _this.elementRef.current;
                            if (!target) {
                                return; // Editor has been unmounted
                            }
                            if (!(0, _Utils__WEBPACK_IMPORTED_MODULE_3__.isInDoc)(target)) {
                                // this is probably someone trying to help by rendering us offscreen
                                // but we can't do that because the editor iframe must be in the document
                                // in order to have state
                                if (attempts === 0) {
                                    // we probably just need to wait for the current events to be processed
                                    setTimeout(function() {
                                        return _this.initialise(1);
                                    }, 1);
                                } else if (attempts < 100) {
                                    // wait for ten seconds, polling every tenth of a second
                                    setTimeout(function() {
                                        return _this.initialise(attempts + 1);
                                    }, 100);
                                } else {
                                    // give up, at this point it seems that more polling is unlikely to help
                                    throw new Error('tinymce can only be initialised when in a document');
                                }
                                return;
                            }
                            var tinymce = (0, _TinyMCE__WEBPACK_IMPORTED_MODULE_2__.getTinymce)(_this.view);
                            if (!tinymce) {
                                throw new Error('tinymce should have been loaded into global scope');
                            }
                            var finalInit = __assign(__assign({}, _this.props.init), {
                                selector: undefined,
                                target: target,
                                readonly: _this.props.disabled,
                                inline: _this.inline,
                                plugins: (0, _Utils__WEBPACK_IMPORTED_MODULE_3__.mergePlugins)((_a = _this.props.init) === null || _a === void 0 ? void 0 : _a.plugins, _this.props.plugins),
                                toolbar: (_b = _this.props.toolbar) !== null && _b !== void 0 ? _b : (_c = _this.props.init) === null || _c === void 0 ? void 0 : _c.toolbar,
                                setup: function(editor) {
                                    _this.editor = editor;
                                    _this.bindHandlers({});
                                    // When running in inline mode the editor gets the initial value
                                    // from the innerHTML of the element it is initialized on.
                                    // However we don't want to take on the responsibility of sanitizing
                                    // to remove XSS in the react integration so we have a chicken and egg
                                    // problem... We avoid it by sneaking in a set content before the first
                                    // "official" setContent and using TinyMCE to do the sanitization.
                                    if (_this.inline && !(0, _Utils__WEBPACK_IMPORTED_MODULE_3__.isTextareaOrInput)(target)) {
                                        editor.once('PostRender', function(_evt) {
                                            editor.setContent(_this.getInitialValue(), {
                                                no_events: true
                                            });
                                        });
                                    }
                                    if (_this.props.init && (0, _Utils__WEBPACK_IMPORTED_MODULE_3__.isFunction)(_this.props.init.setup)) {
                                        _this.props.init.setup(editor);
                                    }
                                },
                                init_instance_callback: function(editor) {
                                    var _a, _b;
                                    // check for changes that happened since tinymce.init() was called
                                    var initialValue = _this.getInitialValue();
                                    _this.currentContent = (_a = _this.currentContent) !== null && _a !== void 0 ? _a : editor.getContent();
                                    if (_this.currentContent !== initialValue) {
                                        _this.currentContent = initialValue;
                                        // same as resetContent in TinyMCE 5
                                        editor.setContent(initialValue);
                                        editor.undoManager.clear();
                                        editor.undoManager.add();
                                        editor.setDirty(false);
                                    }
                                    var disabled = (_b = _this.props.disabled) !== null && _b !== void 0 ? _b : false;
                                    (0, _Utils__WEBPACK_IMPORTED_MODULE_3__.setMode)(_this.editor, disabled ? 'readonly' : 'design');
                                    // ensure existing init_instance_callback is called
                                    if (_this.props.init && (0, _Utils__WEBPACK_IMPORTED_MODULE_3__.isFunction)(_this.props.init.init_instance_callback)) {
                                        _this.props.init.init_instance_callback(editor);
                                    }
                                }
                            });
                            if (!_this.inline) {
                                target.style.visibility = '';
                            }
                            if ((0, _Utils__WEBPACK_IMPORTED_MODULE_3__.isTextareaOrInput)(target)) {
                                target.value = _this.getInitialValue();
                            }
                            tinymce.init(finalInit);
                        };
                        _this.id = _this.props.id || (0, _Utils__WEBPACK_IMPORTED_MODULE_3__.uuid)('tiny-react');
                        _this.elementRef = react__WEBPACK_IMPORTED_MODULE_0__.createRef();
                        _this.inline = (_c = (_a = _this.props.inline) !== null && _a !== void 0 ? _a : (_b = _this.props.init) === null || _b === void 0 ? void 0 : _b.inline) !== null && _c !== void 0 ? _c : false;
                        _this.boundHandlers = {};
                        return _this;
                    }
                    Object.defineProperty(Editor.prototype, "view", {
                        get: function() {
                            var _a, _b;
                            return (_b = (_a = this.elementRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument.defaultView) !== null && _b !== void 0 ? _b : window;
                        },
                        enumerable: false,
                        configurable: true
                    });
                    Editor.prototype.componentDidUpdate = function(prevProps) {
                        var _this = this;
                        var _a, _b;
                        if (this.rollbackTimer) {
                            clearTimeout(this.rollbackTimer);
                            this.rollbackTimer = undefined;
                        }
                        if (this.editor) {
                            this.bindHandlers(prevProps);
                            if (this.editor.initialized) {
                                this.currentContent = (_a = this.currentContent) !== null && _a !== void 0 ? _a : this.editor.getContent();
                                if (typeof this.props.initialValue === 'string' && this.props.initialValue !== prevProps.initialValue) {
                                    // same as resetContent in TinyMCE 5
                                    this.editor.setContent(this.props.initialValue);
                                    this.editor.undoManager.clear();
                                    this.editor.undoManager.add();
                                    this.editor.setDirty(false);
                                } else if (typeof this.props.value === 'string' && this.props.value !== this.currentContent) {
                                    var localEditor_1 = this.editor;
                                    localEditor_1.undoManager.transact(function() {
                                        // inline editors grab focus when restoring selection
                                        // so we don't try to keep their selection unless they are currently focused
                                        var cursor;
                                        if (!_this.inline || localEditor_1.hasFocus()) {
                                            try {
                                                // getBookmark throws exceptions when the editor has not been focused
                                                // possibly only in inline mode but I'm not taking chances
                                                cursor = localEditor_1.selection.getBookmark(3);
                                            } catch (e) { /* ignore */ }
                                        }
                                        var valueCursor = _this.valueCursor;
                                        localEditor_1.setContent(_this.props.value);
                                        if (!_this.inline || localEditor_1.hasFocus()) {
                                            for (var _i = 0, _a = [cursor, valueCursor]; _i < _a.length; _i++) {
                                                var bookmark = _a[_i];
                                                if (bookmark) {
                                                    try {
                                                        localEditor_1.selection.moveToBookmark(bookmark);
                                                        _this.valueCursor = bookmark;
                                                        break;
                                                    } catch (e) { /* ignore */ }
                                                }
                                            }
                                        }
                                    });
                                }
                                if (this.props.disabled !== prevProps.disabled) {
                                    var disabled = (_b = this.props.disabled) !== null && _b !== void 0 ? _b : false;
                                    (0, _Utils__WEBPACK_IMPORTED_MODULE_3__.setMode)(this.editor, disabled ? 'readonly' : 'design');
                                }
                            }
                        }
                    };
                    Editor.prototype.componentDidMount = function() {
                        var _this = this;
                        var _a, _b, _c, _d, _e;
                        if ((0, _TinyMCE__WEBPACK_IMPORTED_MODULE_2__.getTinymce)(this.view) !== null) {
                            this.initialise();
                        } else if (Array.isArray(this.props.tinymceScriptSrc) && this.props.tinymceScriptSrc.length === 0) {
                            (_b = (_a = this.props).onScriptsLoadError) === null || _b === void 0 ? void 0 : _b.call(_a, new Error('No `tinymce` global is present but the `tinymceScriptSrc` prop was an empty array.'));
                        } else if ((_c = this.elementRef.current) === null || _c === void 0 ? void 0 : _c.ownerDocument) {
                            var successHandler = function() {
                                var _a, _b;
                                (_b = (_a = _this.props).onScriptsLoad) === null || _b === void 0 ? void 0 : _b.call(_a);
                                _this.initialise();
                            };
                            var errorHandler = function(err) {
                                var _a, _b;
                                (_b = (_a = _this.props).onScriptsLoadError) === null || _b === void 0 ? void 0 : _b.call(_a, err);
                            };
                            _ScriptLoader2__WEBPACK_IMPORTED_MODULE_1__.ScriptLoader.loadList(this.elementRef.current.ownerDocument, this.getScriptSources(), (_e = (_d = this.props.scriptLoading) === null || _d === void 0 ? void 0 : _d.delay) !== null && _e !== void 0 ? _e : 0, successHandler, errorHandler);
                        }
                    };
                    Editor.prototype.componentWillUnmount = function() {
                        var _this = this;
                        var editor = this.editor;
                        if (editor) {
                            editor.off(this.changeEvents(), this.handleEditorChange);
                            editor.off(this.beforeInputEvent(), this.handleBeforeInput);
                            editor.off('keypress', this.handleEditorChangeSpecial);
                            editor.off('keydown', this.handleBeforeInputSpecial);
                            editor.off('NewBlock', this.handleEditorChange);
                            Object.keys(this.boundHandlers).forEach(function(eventName) {
                                editor.off(eventName, _this.boundHandlers[eventName]);
                            });
                            this.boundHandlers = {};
                            editor.remove();
                            this.editor = undefined;
                        }
                    };
                    Editor.prototype.render = function() {
                        return this.inline ? this.renderInline() : this.renderIframe();
                    };
                    Editor.prototype.changeEvents = function() {
                        var _a, _b, _c;
                        var isIE = (_c = (_b = (_a = (0, _TinyMCE__WEBPACK_IMPORTED_MODULE_2__.getTinymce)(this.view)) === null || _a === void 0 ? void 0 : _a.Env) === null || _b === void 0 ? void 0 : _b.browser) === null || _c === void 0 ? void 0 : _c.isIE();
                        return (isIE ?
                            'change keyup compositionend setcontent CommentChange' :
                            'change input compositionend setcontent CommentChange');
                    };
                    Editor.prototype.beforeInputEvent = function() {
                        return (0, _Utils__WEBPACK_IMPORTED_MODULE_3__.isBeforeInputEventAvailable)() ? 'beforeinput SelectionChange' : 'SelectionChange';
                    };
                    Editor.prototype.renderInline = function() {
                        var _a = this.props.tagName,
                            tagName = _a === void 0 ? 'div' : _a;
                        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(tagName, {
                            ref: this.elementRef,
                            id: this.id
                        });
                    };
                    Editor.prototype.renderIframe = function() {
                        return react__WEBPACK_IMPORTED_MODULE_0__.createElement('textarea', {
                            ref: this.elementRef,
                            style: {
                                visibility: 'hidden'
                            },
                            name: this.props.textareaName,
                            id: this.id
                        });
                    };
                    Editor.prototype.getScriptSources = function() {
                        var _a, _b;
                        var async = (_a = this.props.scriptLoading) === null || _a === void 0 ? void 0 : _a.async;
                        var defer = (_b = this.props.scriptLoading) === null || _b === void 0 ? void 0 : _b.defer;
                        if (this.props.tinymceScriptSrc !== undefined) {
                            if (typeof this.props.tinymceScriptSrc === 'string') {
                                return [{
                                    src: this.props.tinymceScriptSrc,
                                    async: async,
                                    defer: defer
                                }];
                            }
                            // multiple scripts can be specified which allows for hybrid mode
                            return this.props.tinymceScriptSrc.map(function(item) {
                                if (typeof item === 'string') {
                                    // async does not make sense for multiple items unless
                                    // they are not dependent (which will be unlikely)
                                    return {
                                        src: item,
                                        async: async,
                                        defer: defer
                                    };
                                } else {
                                    return item;
                                }
                            });
                        }
                        // fallback to the cloud when the tinymceScriptSrc is not specified
                        var channel = this.props.cloudChannel;
                        var apiKey = this.props.apiKey ? this.props.apiKey : 'no-api-key';
                        var cloudTinyJs = "https://cdn.tiny.cloud/1/".concat(apiKey, "/tinymce/").concat(channel, "/tinymce.min.js");
                        return [{
                            src: cloudTinyJs,
                            async: async,
                            defer: defer
                        }];
                    };
                    Editor.prototype.getInitialValue = function() {
                        if (typeof this.props.initialValue === 'string') {
                            return this.props.initialValue;
                        } else if (typeof this.props.value === 'string') {
                            return this.props.value;
                        } else {
                            return '';
                        }
                    };
                    Editor.prototype.bindHandlers = function(prevProps) {
                        var _this = this;
                        if (this.editor !== undefined) {
                            // typescript chokes trying to understand the type of the lookup function
                            (0, _Utils__WEBPACK_IMPORTED_MODULE_3__.configHandlers)(this.editor, prevProps, this.props, this.boundHandlers, function(key) {
                                return _this.props[key];
                            });
                            // check if we should monitor editor changes
                            var isValueControlled = function(p) {
                                return p.onEditorChange !== undefined || p.value !== undefined;
                            };
                            var wasControlled = isValueControlled(prevProps);
                            var nowControlled = isValueControlled(this.props);
                            if (!wasControlled && nowControlled) {
                                this.editor.on(this.changeEvents(), this.handleEditorChange);
                                this.editor.on(this.beforeInputEvent(), this.handleBeforeInput);
                                this.editor.on('keydown', this.handleBeforeInputSpecial);
                                this.editor.on('keyup', this.handleEditorChangeSpecial);
                                this.editor.on('NewBlock', this.handleEditorChange);
                            } else if (wasControlled && !nowControlled) {
                                this.editor.off(this.changeEvents(), this.handleEditorChange);
                                this.editor.off(this.beforeInputEvent(), this.handleBeforeInput);
                                this.editor.off('keydown', this.handleBeforeInputSpecial);
                                this.editor.off('keyup', this.handleEditorChangeSpecial);
                                this.editor.off('NewBlock', this.handleEditorChange);
                            }
                        }
                    };
                    Editor.propTypes = _EditorPropTypes__WEBPACK_IMPORTED_MODULE_4__.EditorPropTypes;
                    Editor.defaultProps = {
                        cloudChannel: '6'
                    };
                    return Editor;
                }(react__WEBPACK_IMPORTED_MODULE_0__.Component));



                /***/
            }),

        /***/
        "../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/components/EditorPropTypes.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    EditorPropTypes: () => ( /* binding */ EditorPropTypes),
                    /* harmony export */
                    eventPropTypes: () => ( /* binding */ eventPropTypes)
                    /* harmony export */
                });
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/prop-types/prop-types");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_0__);
                var __assign = (undefined && undefined.__assign) || function() {
                    __assign = Object.assign || function(t) {
                        for (var s, i = 1, n = arguments.length; i < n; i++) {
                            s = arguments[i];
                            for (var p in s)
                                if (Object.prototype.hasOwnProperty.call(s, p))
                                    t[p] = s[p];
                        }
                        return t;
                    };
                    return __assign.apply(this, arguments);
                };

                var eventPropTypes = {
                    onActivate: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onAddUndo: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onBeforeAddUndo: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onBeforeExecCommand: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onBeforeGetContent: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onBeforeRenderUI: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onBeforeSetContent: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onBeforePaste: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onBlur: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onChange: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onClearUndos: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onClick: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onContextMenu: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onCommentChange: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onCopy: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onCut: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDblclick: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDeactivate: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDirty: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDrag: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDragDrop: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDragEnd: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDragGesture: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDragOver: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onDrop: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onExecCommand: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onFocus: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onFocusIn: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onFocusOut: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onGetContent: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onHide: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onInit: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onKeyDown: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onKeyPress: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onKeyUp: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onLoadContent: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onMouseDown: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onMouseEnter: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onMouseLeave: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onMouseMove: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onMouseOut: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onMouseOver: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onMouseUp: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onNodeChange: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onObjectResizeStart: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onObjectResized: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onObjectSelected: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onPaste: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onPostProcess: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onPostRender: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onPreProcess: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onProgressState: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onRedo: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onRemove: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onReset: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onSaveContent: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onSelectionChange: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onSetAttrib: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onSetContent: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onShow: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onSubmit: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onUndo: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onVisualAid: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onSkinLoadError: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onThemeLoadError: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onModelLoadError: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onPluginLoadError: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onIconsLoadError: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onLanguageLoadError: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onScriptsLoad: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    onScriptsLoadError: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                };
                var EditorPropTypes = __assign({
                    apiKey: prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                    id: prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                    inline: prop_types__WEBPACK_IMPORTED_MODULE_0__.bool,
                    init: prop_types__WEBPACK_IMPORTED_MODULE_0__.object,
                    initialValue: prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                    onEditorChange: prop_types__WEBPACK_IMPORTED_MODULE_0__.func,
                    value: prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                    tagName: prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                    cloudChannel: prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                    plugins: prop_types__WEBPACK_IMPORTED_MODULE_0__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_0__.string, prop_types__WEBPACK_IMPORTED_MODULE_0__.array]),
                    toolbar: prop_types__WEBPACK_IMPORTED_MODULE_0__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_0__.string, prop_types__WEBPACK_IMPORTED_MODULE_0__.array]),
                    disabled: prop_types__WEBPACK_IMPORTED_MODULE_0__.bool,
                    textareaName: prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                    tinymceScriptSrc: prop_types__WEBPACK_IMPORTED_MODULE_0__.oneOfType([
                        prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                        prop_types__WEBPACK_IMPORTED_MODULE_0__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_0__.string),
                        prop_types__WEBPACK_IMPORTED_MODULE_0__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_0__.shape({
                            src: prop_types__WEBPACK_IMPORTED_MODULE_0__.string,
                            async: prop_types__WEBPACK_IMPORTED_MODULE_0__.bool,
                            defer: prop_types__WEBPACK_IMPORTED_MODULE_0__.bool
                        }))
                    ]),
                    rollback: prop_types__WEBPACK_IMPORTED_MODULE_0__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_0__.number, prop_types__WEBPACK_IMPORTED_MODULE_0__.oneOf([false])]),
                    scriptLoading: prop_types__WEBPACK_IMPORTED_MODULE_0__.shape({
                        async: prop_types__WEBPACK_IMPORTED_MODULE_0__.bool,
                        defer: prop_types__WEBPACK_IMPORTED_MODULE_0__.bool,
                        delay: prop_types__WEBPACK_IMPORTED_MODULE_0__.number
                    })
                }, eventPropTypes);


                /***/
            }),

        /***/
        "../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    Editor: () => ( /* reexport safe */ _components_Editor__WEBPACK_IMPORTED_MODULE_0__.Editor)
                    /* harmony export */
                });
                /* harmony import */
                var _components_Editor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/components/Editor.js");




                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_tinymce_tinymce-react_lib_es2015_main_ts_index_js.985afb6c567e05f9.js.map