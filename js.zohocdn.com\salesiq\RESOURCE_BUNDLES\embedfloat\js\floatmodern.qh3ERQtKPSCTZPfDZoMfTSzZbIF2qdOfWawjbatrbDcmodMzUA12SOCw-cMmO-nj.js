(() => {
        var e, t, i = {
            1242: (e, t, i) => {
                "use strict";
                i.d(t, {
                    XI: () => p,
                    f$: () => g,
                    jl: () => y
                });
                var o = i(2220),
                    n = i(2819),
                    a = i(9808),
                    r = i(5075),
                    s = i(33),
                    l = i(7490),
                    c = i(7408),
                    d = i(2717);
                let m = {},
                    h = [];
                const p = {
                        accessAnyPage: "access_any_page_on_website",
                        clicked: "clicked",
                        customAction: "performs_custom_action",
                        restart: "restartchat"
                    },
                    u = "encryptedVisitorInfo",
                    f = "triggerDetails";
                let g = (e, t) => {
                        if (!(0, o.hk)("isTriggerWithoutTrackingEnabled")) return;
                        let {
                            cpage: i,
                            ptitle: r,
                            referer: m
                        } = (0, a.W)(), {
                            name: h,
                            email: f,
                            phone: g,
                            language: _,
                            info: y
                        } = o.visitorInfo, w = (0, s.L2)(u), k = o.embedDetails.lang;
                        "default" === k || _ || (_ = k);
                        let z = {
                            brand_id: o.embedDetails.widgetId,
                            action_type: e,
                            current_page_title: r,
                            current_page_url: i,
                            visitor_info: y,
                            ...t ? {} : {
                                encrypted_visitor_info: w
                            },
                            emailid: f,
                            phonenumber: g,
                            name: h,
                            language: _ || d.ZP.getLanguage(),
                            referer: m
                        };
                        if (e === p.customAction) {
                            if (!o.visitorInfo.customaction) return;
                            z.custom_action_name = o.visitorInfo.customaction.field
                        } else if ((0, l.$G)()) return;
                        (0, n.P)(z, "post").then(i => {
                            i.matched_rule_id ? (t && (0, c.e)("triggerapi"), v(i, e, t)) : t ? (0, c.e)("triggerapi", !0) : b(e, i)
                        }).catch(e => {
                            t && (0, c.e)("triggerapi", !0)
                        })
                    },
                    v = (e, t, i) => {
                        let n, a;
                        (0, s.Hc)(u, e.encrypted_visitor_info);
                        let r = Date.now(),
                            l = e.action;
                        l ? (n = ((e, t) => {
                            let i = e.params,
                                o = i.value;
                            return JSON.stringify({
                                sendername: i.sender_name,
                                time: t ? 1 : e.delay || 1,
                                message: o,
                                triggername: o
                            })
                        })(l, i), a = "invoke_js_api" === l.type ? "10" : "2", r += i ? 1 : l.delay || 1) : (n = ((e, t) => {
                            let i = e.params,
                                {
                                    attender: n,
                                    typing_delay: a,
                                    departments: r,
                                    department: s
                                } = i,
                                {
                                    aboutme: l,
                                    sender: c,
                                    dname: d,
                                    attender_imagefkey: m
                                } = n;
                            return {
                                typing_delay: a,
                                module: "BOT_MESSAGE",
                                aboutme: l,
                                mode: "BOT_MESSAGE",
                                lsid: o.embedDetails.lsid,
                                sender: c,
                                time: t ? 1 : e.delay || 1,
                                dname: d,
                                departments: r,
                                botid: n.attender,
                                department: s,
                                msglist: i.msg_list,
                                attender: n.attender,
                                attender_imagefkey: m
                            }
                        })(e, i), a = "16", r += i ? 1 : e.delay || 1);
                        let c = {
                            triggerid: e.matched_rule_id,
                            type: a,
                            value: n
                        };
                        "10" === a && (c.visitorinfo = e.visitorinfo);
                        let d = (0, s.L2)(f);
                        (!d || (d.timeToTrigger || 0) > r) && (c.timeToTrigger = r, c.actionType = t, (0, s.Hc)(f, c)), _(), y(c)
                    },
                    b = (e, t) => {
                        let i = (0, s.L2)(f);
                        if (i) {
                            if (e === p.accessAnyPage && i.actionType === p.accessAnyPage && i.triggerid && (i.millis || 0) + 6e5 >= Date.now()) {
                                let e = i.value;
                                "object" != typeof e && (e = JSON.parse(e)), i.timeToTrigger = Date.now() + e.time, _(), y(i)
                            }
                        } else(0, s.Hc)(u, t.encrypted_visitor_info)
                    },
                    _ = () => {
                        window.addEventListener("beforeunload", () => {
                            let e = (0, s.L2)(f);
                            e && e.triggerid && (e.millis = Date.now(), (0, s.Hc)(f, e))
                        })
                    },
                    y = (e, t, i) => {
                        if (!e.value) return;
                        t && (m = {});
                        let o = "object" == typeof e.value ? e.value : JSON.parse(e.value),
                            n = e.value = o,
                            {
                                triggerid: a
                            } = e,
                            r = m[a];
                        r ? (n.msglist = r.msglist, delete m[a]) : m[a] = n;
                        let s = n.time;
                        if (s > 0) return h.push(setTimeout(() => {
                            w(e, i)
                        }, s));
                        w(e, i)
                    },
                    w = (e, t) => {
                        let i = e.type;
                        switch (h.forEach(e => clearTimeout(e)), h = [], (0, s.Hc)(f, e.value), t || (e => {
                            let t = {
                                triggered_id: e.triggerid,
                                encrypted_visitor_info: (0, s.L2)(u)
                            };
                            "16" === e.type && (t.bot_id = e.value.botid), (0, n.P)(t, "put").then(e => {
                                (0, s.Hc)(u, e.encrypted_object)
                            })
                        })(e), i) {
                            case "2":
                                return (0, r.E)("trigger", e);
                            case "16":
                                return (0, r.E)("bottrigger", e);
                            case "10":
                                return (0, r.E)("invoke_js_api", e)
                        }
                    }
            },
            4396: (e, t, i) => {
                "use strict";
                i.d(t, {
                    T: () => n
                }), i(1181);
                const o = {
                    avuid: "avuid",
                    appname: "brandName",
                    app_id: "widgetId",
                    chat: "chatConfig",
                    call: {
                        callDetail: {
                            feedback: "feedback",
                            messages: "messages",
                            rating: "reaction",
                            waiting_time: "waitingTime"
                        }
                    },
                    RTL_supported_language: "isRTLLang",
                    call_status: "callStatus",
                    components: "components",
                    enabled: "enabled",
                    font: "font",
                    form_type: "formType",
                    departments: "chatDepartments",
                    call_departments: "callDepartments",
                    language: "lang",
                    company_name: "companyName",
                    status: "embedStatus",
                    resources: "resources",
                    resource: "resourceObj",
                    privacy: "privacy",
                    faq: "articlesConfig",
                    free_logo_url: "drivenBySiqLink",
                    form_fields: "formFields",
                    logo_url: "brandLogoUrl",
                    show_emojis: "showEmoji",
                    hide_when_offline: "hideOnOffline",
                    product_name: "productName",
                    call_recording: "hasCallRecording",
                    voice_notes_enabled: "showVoiceNote",
                    view_conversation: "viewConversations",
                    jwt_authentication: {
                        jwtConfig: {
                            enabled: "enabled",
                            secret_key: "secret_key"
                        }
                    },
                    read_receipt: "hasReadReceipt",
                    white_labelled: "isWhiteLabelled",
                    allow_chat_reopen: "hasReopenChat",
                    allow_multiple_live_chat: "allowParellelChat",
                    waiting_time: "wTime",
                    mail_transcript: "sendEmail",
                    share_file: "hasFileShare",
                    unique_name: "uniqueName",
                    is_business_hours_enabled: "isBHEnabled",
                    online_content: {
                        onlineContent: {
                            header: "header",
                            sub_header: "desc"
                        }
                    },
                    offline_content: {
                        offlineContent: {
                            header: "header",
                            sub_header: "desc"
                        }
                    },
                    message_actions: "messageActionConfig",
                    google_translate_attribution_url: "gtPolicyUrl",
                    notify_googletranslator_usage: "gtPolicyConfig",
                    geo_details: "geoDetails",
                    notify_cookies: {
                        cookieConfig: {
                            code: "code",
                            link_url: "url",
                            banner_content: "content"
                        }
                    },
                    cookie_preference: "cookiePreference",
                    notify_terms: {
                        chatTerms: {
                            code: "enabled",
                            url: "url"
                        }
                    },
                    credit_card_masking: {
                        ccMasking: {
                            enabled: "enabled",
                            consent_banner_content: "consentContent"
                        }
                    },
                    integration: "integrations",
                    share_screen: "hasScreenShare",
                    uts_server: "utsDomain",
                    widget: {
                        widget: {
                            float: {
                                float: {
                                    color: "color",
                                    custom_sticker: "customSticker",
                                    show_company_logo: "showCompanyLogo",
                                    hide_tooltip: "hideTooltip",
                                    hide_in_mobile_device: "hideInMobile",
                                    hide_close_button: "hideCloseBtn",
                                    position: "position",
                                    gravatar: "gravatar"
                                }
                            },
                            button: {
                                float: {
                                    color: "color",
                                    custom_sticker: "customSticker",
                                    hide_in_mobile_device: "hideInMobile"
                                }
                            }
                        }
                    },
                    version: "version",
                    lsid: "lsid",
                    modified_time: "lastModified",
                    i18nkeys: "i18nKeys",
                    company_info: {
                        companyLogo: {
                            logo_source: "url"
                        }
                    },
                    visitor_info: {
                        visitorInfo: {
                            country: "country"
                        }
                    },
                    dynamic_configurations: {
                        dynamicConf: {
                            only_call_enabled: "hasOnlyCall",
                            isbetaportal: "isBetaPortal",
                            rtcp_enabled: "useRTCPFlow",
                            waiting_banner_enabled: "hasWaitingBannerForBot",
                            disallow_ratings_overriden: "isRatingOverrideDisabled",
                            display_trigger_message: "isTriggerOnChatnowEnabled",
                            is_white_labelling_enabled: "isWhiteLabellingEnabled",
                            is_zoho_maps_v3_enabled: "isZohoV3Enabled",
                            slider_default_value_enabled: "isSliderDefaultValEnabled",
                            is_triggers_without_tracking_enabled: "isTriggerWithoutTrackingEnabled",
                            visitor_name_newflow: "isFirstNameFlowEnabled",
                            company_input_enhancment: "isCompanyInputEnhancement",
                            restrict_visitor_input: "isRestrictVisitorInputEnabled",
                            range_slider_text_input_supported: "isSliderHintEnabled",
                            bot_rendering_ux_revamp: "isEmbedFeatureConfEnabled",
                            init_retracking: "init_retracking",
                            cross_widget_navigation_enabled: "isCrossWidgetNavigationEnabled",
                            media_device_settings: "media_device_settings",
                            offline_voice_note_in_transcript: "offlineVoiceNoteInTranscriptEnabled",
                            mail_transcript_newflow: "isMailTranscriptNewFlow",
                            subresourceintegrity_enabled: "isSRIEnabled",
                            is_widget_drag_supported: "isWidgetDragEnabled",
                            embed_mobile_view: "isEmbedMobileView"
                        }
                    },
                    license_info: {
                        licenseDetail: {
                            audiocall: "hasAudioCall",
                            filesharing: "hasFileShare",
                            faqallowed: "hasFaq",
                            issiqscreenshare: "hasSiqScreenShare",
                            creditmask: "hasCreditMask",
                            assistallowed: "hasAssistAccess",
                            planid: "planId"
                        }
                    },
                    portal_config: {
                        portalConfig: {
                            name: "portalName",
                            screensharing: "screenSharing",
                            audiocall: "audiocall",
                            id: "soid",
                            isgdprenabled: "gdprEnabled",
                            chattranscript: "hasChatTranscript"
                        }
                    },
                    product_url: "productUrl",
                    public_domain: "serverURL",
                    upload_download_server: "udConfig",
                    siq_service_name: "serviceName",
                    cdn: "cdnConfig",
                    use_apache: "use_apache",
                    static_urls: {
                        editorUrl: {
                            ace_editor: "ace_editor"
                        }
                    },
                    media: "media",
                    wms_related_info: {
                        wmsConfig: {
                            js_static_domain: "js_domain",
                            public_domain: "domain",
                            js_static_server: "js_url",
                            all_sri_hash_value: "allSRIHash",
                            wmslite_js_sri_hash_value: "jsSRIHash",
                            product_code: "prdid"
                        }
                    },
                    rtcp_related_info: {
                        rtcpInfo: {
                            RTCPCFPARAMNAME: "rtcp_csrf_param",
                            chunck_common: "chunkCommon",
                            chunck_media: "chunkMedia",
                            chunck_vendors: "chunkVendors",
                            css_url: "cssUrls",
                            js_url: "jsUrls",
                            _RTCPCFCOOKIENAME: "rtcp_cookie",
                            prd: "rtcpPrd",
                            domain_url: "domainUrl"
                        }
                    },
                    recording: {
                        recordingDetails: {
                            call_consent_message: "audioConsent",
                            screenshare_consent_message: "screenConsent",
                            type: "type"
                        }
                    },
                    timezoneOffset: {
                        timezone_offset: {
                            portal: "portal",
                            server: "server",
                            user: "user"
                        }
                    },
                    homepage_configs: "homePageConfigs",
                    zmap: "mapConfig",
                    form_fields_auto_pick_enabled: "autoPickEnabled",
                    seasonal_theme: "seasonal_theme",
                    _zldp: "zldp"
                };
                let n = (e = {}, t = o, i) => {
                    let a = Object.keys(t),
                        r = i ? {} : {
                            callDepartments: []
                        };
                    return a.length ? (a.forEach(i => {
                        let o = e[i];
                        if (null != o)
                            if ("object" == typeof t[i]) {
                                let [e] = Object.keys(t[i]);
                                e && (r[e] = n(o, t[i][e], !0))
                            } else r[t[i]] = ["true", "false"].includes(o) ? "true" === o : o
                    }), r) : e
                }
            },
            8008: (e, t, i) => {
                "use strict";
                i.d(t, {
                    L0: () => r,
                    LB: () => n,
                    LG: () => h,
                    PE: () => s,
                    Yi: () => l,
                    _o: () => u,
                    dO: () => d,
                    fo: () => o,
                    oQ: () => a,
                    rv: () => p,
                    yK: () => m
                });
                const o = window._STATICURLS,
                    n = "https://" + o[5],
                    a = window.$zoho.salesiq,
                    r = window._SIQ_WC || a.widgetcode;
                a.widgetcode = r;
                const s = {
                        traditional: "general",
                        inline: "classic",
                        chat: "conversation"
                    },
                    l = ($ZSD("#zsiqscript")[0] || $ZSD("#siquts")[0]).src.split("://")[0];
                let c = e => {
                    if (e.parent !== e) try {
                        return c(e.parent)
                    } catch (t) {
                        return e.document
                    }
                    return e.document
                };
                const d = c(window),
                    m = (() => {
                        let e = "";
                        return e || (e = location.hostname.toString().replace(/^w{3}\./, ""), /^[a-zA-Z0-9-\.]+$/.test(e) || (e = "")), e ? l + "://" + e : ""
                    })(),
                    h = !!window._IS_SIGNATURE_CHAT,
                    p = window._IS_PREVIEW,
                    u = !!p && window.parent.IS_BOT_PREVIEW;
                window._IS_REVAMP = !0, delete window._SIQ_WC, delete window._IS_PREVIEW
            },
            5046: (e, t, i) => {
                "use strict";
                i.d(t, {
                    DP: () => h,
                    Nl: () => m,
                    Vm: () => c
                });
                var o = i(2220),
                    n = i(9636),
                    a = i(4912);
                let r = !1,
                    s = e => {
                        (0, o.xj)({
                            umsg_count: e
                        })
                    },
                    l = (e, t) => {
                        n.domContainer.float.append((0, a.Zi)(e, t)), n.domContainer.indicator = $ZSD("#zsiq-indicator")
                    },
                    c = e => {
                        e ? (e => {
                            parseInt(e) && (s(e), !r) && (n.domContainer.indicator ? n.domContainer.indicator.text(e) : l(e))
                        })(e) : d()
                    },
                    d = () => {
                        r || (s(), n.domContainer.indicator && n.domContainer.indicator.remove(), delete n.domContainer.indicator)
                    },
                    m = e => {
                        n.domContainer.indicator && n.domContainer.indicator[e ? "show" : "hide"]()
                    },
                    h = e => {
                        r = e, e ? (() => {
                            if (n.domContainer.indicator) return n.domContainer.indicator.html((0, a.rF)()).addClass("zsiq-indicator-anim");
                            l("", !0)
                        })() : (n.domContainer.indicator && n.domContainer.indicator.removeClass("zsiq-indicator-anim"), c(o.cb.umsg_count))
                    }
            },
            5075: (e, t, i) => {
                "use strict";
                i.d(t, {
                    E: () => h,
                    c: () => d
                });
                var o = i(9291),
                    n = i(7408),
                    a = i(2220),
                    r = i(5812),
                    s = i(2615),
                    l = i(7490),
                    c = i(1753);
                let d = {},
                    m = (e, t) => (0, o.R)(!1, !0).then(() => {
                        (0, n.e)(e, t)
                    }),
                    h = (e, t) => {
                        switch (e) {
                            case "updatequeued":
                                return (0, r.r2)();
                            case "utsupdate":
                                return d = { ...d,
                                    ...t
                                }, d.zldp = d._zldp || t._zldp, a.visitorInfo.uvid = d._zldt, a.visitorInfo.uuid = d.uuid, a.hG.forEach(e => {
                                    d[e] && (0, a.vK)(e, d[e])
                                }), void(0, c.zN)();
                            case "name":
                            case "email":
                                return void(0, a.vK)(e, t);
                            case "invoke_js_api":
                                return (e => {
                                    let {
                                        visitorinfo: t
                                    } = e;
                                    if (t)
                                        for (var i in t)
                                            if (t.hasOwnProperty(i)) {
                                                var o = t[i];
                                                isNaN(o) || (t[i] = parseInt(o))
                                            }(0, s.L)("visitor.trigger", {
                                                triggername: e.value.triggername,
                                                visitorinfo: e.visitorinfo
                                            })
                                })(t), void(0, r.sR)("notify_triggered", {
                                    id: t.trigger_id,
                                    type: t.type
                                });
                            case "trigger":
                            case "bottrigger":
                                if ((0, l.OT)() && "bottrigger" !== e) return;
                                return void m(e, t.value).then(() => {
                                    (0, r.sR)("notify_triggered", {
                                        id: t.trigger_id || t.triggerid,
                                        type: t.type
                                    })
                                });
                            case "proactivechat":
                                return void m(e, t);
                            case "isCampaignEnabled":
                                let {
                                    integrations: i
                                } = a.embedDetails;
                                return "zc" === t ? i.enabled_integrations && JSON.parse(i.enabled_integrations).includes(6) : i.mailchimp.enabled;
                            case "formUpdate":
                                return a.hG.forEach(e => {
                                    t[e] && (0, a.vK)(e, t[e])
                                }), void(0, c.zN)()
                        }
                    }
            },
            350: (e, t, i) => {
                "use strict";
                i.d(t, {
                    Kh: () => f,
                    ST: () => d,
                    Us: () => m,
                    aX: () => u,
                    h: () => g,
                    iO: () => p,
                    r2: () => h,
                    sR: () => c
                });
                var o = i(5075),
                    n = i(2220),
                    a = i(9808),
                    r = i(1242),
                    s = i(2717);
                let l, c = (e, t) => {
                        l && l.notify(e, t)
                    },
                    d = () => {
                        l && l.disable(), l = void 0
                    },
                    m = () => {
                        l && l.destroy(), l = void 0
                    },
                    h = e => {
                        if (n.visitorInfo.customaction) {
                            if (!l) {
                                let e = n.embedDetails.cookieConfig;
                                if (n.embedDetails.privacy && n.embedDetails.portalConfig.gdprEnabled) {
                                    let t = n.visitorInfo.cookieconsent,
                                        i = t && t.includes("analytics");
                                    (0 === e.code || i) && (0, r.f$)(r.XI.customAction)
                                } else(0, r.f$)(r.XI.customAction);
                                return
                            }
                            c("notify_custom_action", {
                                action: n.visitorInfo.customaction,
                                navdata: (0, a.W)()
                            }), delete n.visitorInfo.customaction
                        }
                    },
                    p = () => {
                        let e = n.visitorInfo.tracking;
                        var t;
                        l || "off" === e || !e && !n.embedDetails.components.includes("proactive") ? (0, r.f$)(r.XI.accessAnyPage) : (t = n.embedDetails, i.e(93).then(i.bind(i, 4196)).then(e => {
                            let i = {
                                brandName: t.uniqueName,
                                portalName: t.portalConfig.portalName,
                                vtsDomain: t.utsDomain,
                                embedLang: n.visitorInfo.language || ("default" === t.lang ? s.ZP.getLanguage() : t.lang),
                                name: n.Ol.name,
                                ...(0, n.hk)("isFirstNameFlowEnabled") ? {
                                    firstname: n.Ol.first_name,
                                    lastname: n.Ol.last_name,
                                    salutation: n.Ol.salutation
                                } : {},
                                phone: n.Ol.phone,
                                email: n.Ol.email,
                                cinfo: n.visitorInfo.info && JSON.stringify(n.visitorInfo.info),
                                e_email: n.visitorInfo.eemail,
                                e_name: n.visitorInfo.ename,
                                idleTime: n.visitorInfo.idletime,
                                ...(0, a.W)(),
                                initRetracking: t.init_retracking,
                                communicationHandler: o.E
                            };
                            l = new e.default(i)
                        }))
                    },
                    u = e => {
                        l && l.handleTrigger(e)
                    },
                    f = e => {
                        l && l.updateIdleTime(e)
                    },
                    g = () => {
                        !l && (0, r.f$)(r.XI.clicked)
                    }
            },
            5504: (e, t, i) => {
                "use strict";
                i.r(t), i.d(t, {
                    dispatchAnalytics: () => n
                }), i(1181);
                var o = i(2220);
                let n = (e, t) => {
                    let i = (o.embedDetails.integrations || {}).analytics;
                    if (!i) return;
                    let n = (e => {
                        let t = o.embedDetails.productName;
                        return {
                            "chatbutton.click": ["Button Clicked", `Click on the ${t} chat button.`],
                            "floatbutton.click": ["Button Clicked", `Click on the ${t} chat button.`],
                            "chat.close": ["Chat Closed", "Click on the Close (X) icon in the chat window."],
                            "floatwindow.minimize": ["Chat Minimized", `Click to minimize the ${t} chat window.`],
                            "chatbubble.close": ["Bubble Closed", "Click on the Close(X) icon on the chat bubble."],
                            "visitor.attend": ["Chat Connected", "Chat sessions with the visitors.", "waitingduration"],
                            "visitor.chatcomplete": ["Chat Ended", "Chat ended by the visitors.", "chatduration"],
                            "chat.file": ["File Transferred", "File transfers made by the visitors."],
                            "chat.print": ["Chat Printed", "Chat transcripts printed by the visitors."],
                            "chat.mail": ["Chat Mail sent", "Chat transcripts emailed by the visitor."],
                            "visitor.rating": ["Rating Submitted", "Rating submitted by visitors.", "rating"],
                            "visitor.feedback": ["Feedback Submitted", "Feedback messages submitted by visitors."],
                            "visitor.chattrigger": ["Chat Triggerred", "Auto and proactive chat initiated to the visitors."],
                            "visitor.triggerresponded": ["Trigger Responded", "Visitor responded to trigger or proactive chat."],
                            "visitor.chat": ["Chat Initiated", "Visitor initiated chats."],
                            "visitor.offline": ["Offline Message Submitted", "Offline messages submitted by the visitors."],
                            "visitor.missed": ["Chat Missed", `Chat missed by the ${t} users.`],
                            "chat.visitorend": ["Chat Ended by visitor", "Chats ended by the visitors."]
                        }[e]
                    })(e);
                    if (!n) return;
                    o.visitorInfo.blockAnalytics && n.splice(2, 1);
                    let [a, r, s] = n, {
                        productName: l
                    } = o.embedDetails;
                    a = l + " " + a;
                    let c = s && parseInt(t[s]),
                        d = s ? {
                            [s]: c
                        } : {},
                        m = window;
                    i.forEach(e => {
                        switch (e) {
                            case 1:
                                return void("function" == typeof m.gtag ? gtag("event", n[0], {
                                    event_category: l,
                                    event_label: r,
                                    value: c
                                }) : "function" == typeof m.ga ? ga("send", "event", l, n[0], r, c) : window._gaq && _gaq.push(["_trackEvent", l, n[0], r, c]));
                            case 2:
                                if (!m.clicky) return;
                                return clicky.log(a + (s ? "-" + c : ""));
                            case 3:
                                if (!m._kmq) return;
                                return _kmq.push(["record", a, d]);
                            case 4:
                                if (!m.optimizely) return;
                                return m.optimizely.push(["trackEvent", a, d]);
                            case 5:
                                if (!m.woopra) return;
                                return woopra.track(a, d);
                            case 6:
                                if (!m._paq) return;
                                return _paq.push(["trackEvent", l, n[0], s, c]);
                            case 7:
                                if (!m.mixpanel) return;
                                return mixpanel.track(a, d);
                            case 8:
                                if (!m._hsq) return;
                                return _hsq.push(["trackEvent", a, e]);
                            case 9:
                                if (!window.dataLayer) return;
                                dataLayer.push({
                                    event: l + " Events",
                                    category: l,
                                    action: a,
                                    label: r,
                                    value: c || 0
                                })
                        }
                    })
                }
            },
            4562: (e, t, i) => {
                "use strict";
                i.d(t, {
                    A0: () => s,
                    Hi: () => l,
                    ey: () => d,
                    gB: () => m,
                    qf: () => c
                });
                var o = i(9636),
                    n = i(2220);
                let a = {},
                    r = () => {
                        let e = o.domContainer.iframeWrap.getRect(),
                            t = o.domContainer.float.getRect(),
                            i = "bottom_left" === n.embedDetails.widget ? .float ? .position ? e.left : e.left + e.width - t.width;
                        o.domContainer.float.addStyle({
                            top: e.top + e.height + 15 + "px",
                            left: i + "px"
                        }), o.domContainer.float.removeStyle("bottom")
                    },
                    s = e => {
                        if (!e || !$zoho.salesiq.dragchatwindow) return;
                        let t = {},
                            i = o.domContainer.iframeWrap;
                        ["top", "left", "bottom", "right"].forEach(i => {
                            t[i] = e[i]
                        }), i.addStyle(t), i.attr("dragpos", "auto" !== e.left ? "left" : "right"), r(), i.removeStyle("transition")
                    },
                    l = (e = {}) => {
                        let t = $ZSD("#zsiq_float");
                        Object.keys(e).length ? t.addStyle(e) : ["top", "left"].forEach(e => {
                            t.removeStyle(e)
                        })
                    },
                    c = e => {
                        let t = e.split("#"),
                            i = o.domContainer.iframeWrap,
                            n = o.domContainer.float,
                            s = window.innerWidth,
                            l = window.innerHeight,
                            c = i.offsetLeft() - parseInt(t[0]),
                            d = i.offsetTop() - parseInt(t[1]),
                            m = i.offsetWidth(),
                            h = i.getRect(),
                            p = n.getRect().height + 15,
                            u = i.offsetHeight() + p;
                        c = c < 0 ? 0 : c, d = d < 0 ? 0 : d, c = c + m > s ? s - m : c, d = d + u > l ? l - u : d, a = {
                            top: d + "px",
                            left: c + "px",
                            right: "auto",
                            bottom: "auto"
                        }, i.attr("dragpos", "left"), c > s / 2 && (a.right = s - c - h.width + "px", a.left = "auto", i.attr("dragpos", "right")), l - (d + h.height + p) < d && (a.bottom = l - d - h.height + "px", a.top = "auto"), i.addStyle(Object.assign({
                            transition: "unset"
                        }, a)), r()
                    },
                    d = () => {
                        (0, n.xj)({
                            cw_dragposition: a
                        }), o.domContainer.iframeWrap.removeStyle("transition")
                    },
                    m = e => {
                        "transitionstart" !== e ? (r(), (() => {
                            let e = o.domContainer.iframeWrap.getRect(),
                                t = o.domContainer.float,
                                i = t.getRect();
                            e.top < 0 ? o.domContainer.iframeWrap.addStyle({
                                top: "0px",
                                bottom: "auto"
                            }) : (i.bottom > window.innerHeight && (t.addStyle({
                                bottom: "0px"
                            }), o.domContainer.iframeWrap.addStyle({
                                bottom: `${i.height+15}px`,
                                top: "auto"
                            }), t.removeStyle("top")), h())
                        })()) : h(!0)
                    },
                    h = e => {
                        n.cb.cw_dragposition && o.domContainer.float[e ? "addClass" : "removeClass"]("zsiq-v-hid")
                    }
            },
            7408: (e, t, i) => {
                "use strict";
                i.d(t, {
                    e: () => _
                });
                var o = i(5075),
                    n = i(9636),
                    a = i(1753),
                    r = i(2615),
                    s = i(4386),
                    l = i(6460),
                    c = i(9291),
                    d = i(4562),
                    m = i(5812),
                    h = i(5046),
                    p = i(2067),
                    u = i(8008),
                    f = i(1242),
                    g = i(2220),
                    v = i(1038),
                    b = i(6826);
                let _ = (e, t) => {
                    n.domContainer.iframe && n.domContainer.iframe.contentWindow.postMessage(JSON.stringify({
                        src: "zsiqfloat",
                        action: e,
                        data: t
                    }))
                };
                $ZSD(window).on("message", e => {
                    let t, u = e.data;
                    try {
                        t = "string" == typeof u ? JSON.parse(u) : u
                    } catch (e) {}
                    if (t) {
                        if ("zoho.salesiq.apimessage" === t.type && (e => {
                                let t = () => {
                                    ["name", "email", "contactnumber", "firstname", "lastname", "salutation"].forEach(t => {
                                        $zohosq.visitor[t](e.visitor[t])
                                    })
                                };
                                e.websiteredirection && window.addEventListener("unload", t), t()
                            })(t), "zoho.salesiq.gettrackingdetails" === t.type) {
                            if (!o.c._zldt) return;
                            e.source.postMessage(JSON.stringify({
                                type: "zoho.salesiq.trackingdetails",
                                trackingdetails: {
                                    uvid: o.c._zldt
                                }
                            }), e.origin)
                        }
                        var _, y;
                        "zsiqcwframe" === t.src && e.source === (n.domContainer.iframe && n.domContainer.iframe.contentWindow) && (_ = t, y = {
                            jsapicb: e => {
                                for (let t in e)(0, r.L)(t, e[t])
                            },
                            togglecwdimension: function(e) {
                                n.domContainer.iframeWrap[e.expand ? "addClass" : "removeClass"]("siqcw-exp-window")
                            },
                            block: function() {
                                [".siq_media", ".siqembed", "[data-id='zsalesiq']"].forEach(e => {
                                    $ZSD(e).remove()
                                })
                            },
                            umsgcount: h.Vm,
                            clientaction: e => {
                                (0, p.hZ)($zohosq.clientactions[e.clientaction_name], e)
                            },
                            triggerreopen: m.aX,
                            callindicator: h.DP,
                            openfilepreview(e) {
                                n.domContainer.iframeWrap[e ? "addClass" : "removeClass"]("attach-preview")
                            },
                            blinkTitle: s.W8,
                            stopTitleBlink: s.Fs,
                            layoutload() {
                                $ZSD("#iframe_loader").remove(), (0, g.n6)(), (0, l.Qu)()
                            },
                            onload() {
                                (0, a.zN)(), (0, l.t6)(), (0, l.dG)(), (0, p.hZ)($zohosq.onload)
                            },
                            updateCustomEvent(e) {
                                (0, m.sR)("notify_custom_event", e)
                            },
                            minimizecw: c.nG,
                            loadMediaFiles() {
                                i.e(192).then(i.bind(i, 7306))
                            },
                            dummy() {
                                i.e(983).then(i.bind(i, 2809))
                            },
                            cwdrag() {
                                (0, d.qf)(_.data)
                            },
                            upcwdragpos() {
                                (0, d.ey)()
                            },
                            handleTriggerReopen() {
                                (0, f.f$)(f.XI.restart, !0)
                            },
                            fetchLatestJWTHeader: v.d4,
                            updateminiview: b.Y
                        }, y[_.action] && y[_.action](_.data))
                    }
                }), window.$SIQDataHandler = {
                    getUTSData: () => o.c,
                    isJWTEnabled: () => (0, v._I)(),
                    isAccessTokenAvailable: () => (0, v.JC)()
                }, u._o && (window.IframeHandler = {
                    handleBotMessage(e) {
                        _("botmessage", e)
                    },
                    handleBotTrigger(e) {
                        _("bottrigger", e)
                    },
                    handleBotTyping(e) {
                        _("bottyping", e)
                    },
                    updatePreview(e, t) {
                        _("preview", {
                            data: e,
                            type: t
                        })
                    }
                })
            },
            6460: (e, t, i) => {
                "use strict";
                i.d(t, {
                    wk: () => w,
                    rq: () => v,
                    dG: () => y,
                    t6: () => _,
                    Qu: () => k,
                    dl: () => z,
                    VP: () => b
                }), i(1181);
                var o = i(2220),
                    n = i(8008),
                    a = i(9636),
                    r = i(2615),
                    s = i(4912);
                let l = () => {
                    if (!a.domContainer.iframe) return;
                    let e = document.documentElement.clientWidth < 555;
                    $ZSD(a.domContainer.iframe.contentDocument).find("html").attr("view", e ? "mobile" : "")
                };
                var c = i(5046);
                let d = !1,
                    m = !1,
                    h = [],
                    p = [],
                    u = () => {
                        let e = document.createElement("script");
                        return e.type = "text/javascript", e
                    },
                    f = (e, t, i) => {
                        let o = [],
                            n = "js" === t,
                            a = window._IS_DEV;
                        for (let t = 0; t < e.length; t++)
                            if (n) {
                                let n = u();
                                n.src = e[t], i && !a ? n.type = "module" : a || (n.type = "nomodule"), o.push(n)
                            } else {
                                let i = document.createElement("link");
                                i.href = e[t], i.rel = "stylesheet", i.type = "text/css", o.push(i)
                            }
                        return o
                    },
                    g = e => {
                        let {
                            udConfig: t,
                            cdnConfig: i,
                            dynamicConf: a
                        } = e, r = {
                            AVUID: o.Kz,
                            visitorInfo: o.visitorInfo,
                            ISSIGNATURECHAT: window._IS_SIGNATURE_CHAT,
                            _HASSRI: a.isSRIEnabled,
                            SIQSERVICENAME: window._SIQSERVICENAME,
                            IS_PREVIEW: n.rv,
                            IS_RTL: o.embedDetails.isRTLLang,
                            brandid: e.widgetId,
                            lsid: e.lsid,
                            sname: e.portalConfig.portalName,
                            soid: e.portalConfig.soid,
                            annonid: e.annonid,
                            sURL: e.serverURL,
                            schema: n.Yi,
                            nonce: n.oQ.nonce,
                            producturl: e.productUrl,
                            downloadserver: t.download_server,
                            uploadserver: t.upload_server,
                            siqservicename: e.serviceName,
                            useUDServer: t.enabled,
                            UDServerRevamp: t.ud_revamp,
                            siqUDServiceName: t.siq_ud_servicename,
                            mediafiles: i.media_file_hashes,
                            commonStaticUrl: i.common_static_url,
                            uapache: e.use_apache,
                            mediaserverurl: e.media.server,
                            wmsInfo: e.wmsConfig,
                            domain: n.yK,
                            ismobilepreview: window.IS_MOBILE_PREVIEW,
                            aceeditorurl: (e.editorUrl || {}).ace_editor,
                            customcss: (e => {
                                let t = (e = e || {}).hasOwnProperty("new_float_url") ? [e.new_float_file_name, e.new_float_url] : [e.file_name, e.url];
                                return {
                                    enabled: e.enabled,
                                    file_name: t[0],
                                    fpath: e.fpath || (t[1] ? `embedcss/${t[1].split("css_id=")[1]}_${o.embedDetails.lsid}` : "")
                                }
                            })(o.embedDetails.chatConfig.custom_css),
                            widget_code: n.L0,
                            language: o.visitorInfo.language,
                            rtcurls: window._NEW_MEDIARTC_URLS,
                            isNewFloat: !0,
                            zmap: e.mapConfig
                        }, s = u();
                        return s.textContent = "window._STATIC_URL='" + window._STATIC_URL + "';window._CONFVARIABLES=" + JSON.stringify(r) + ";", s
                    },
                    v = e => new Promise(t => {
                        if (d) return void(m ? t() : h.push(t));
                        d = !0;
                        let {
                            iframeWrap: i
                        } = a.domContainer, r = document.createElement("iframe");
                        r.setAttribute("aria-label", "SalesIQ Chat Window"), r.id = "siq_chatwindow", r.onload = () => {
                            let t = r.contentDocument;
                            t.open(), t.close(), r.contentWindow._IS_MINI_VIEW = e, (e => {
                                let t = [];
                                window.NEW_STATIC_URLS.forEach(e => {
                                    e = e.slice(), o.embedDetails.isRTLLang ? e.shift() : e.pop(), t.push(e)
                                });
                                let i = [g(o.embedDetails)].concat(f(t[0], "js")).concat(f(t[1], "js", !0)).concat(f(t[2], "css"));
                                (n.rv ? window.parent._IS_ONBOARDING : window._IS_ONBOARDING) && (o.embedDetails.components = o.embedDetails.components.filter(e => !["call", "screen_share"].includes(e))), window._SIQEMBEDDETAIL = { ...o.embedDetails
                                }, i.forEach(t => {
                                    let i = n.oQ.nonce || (document.getElementById("zsiqscript") || {}).nonce;
                                    i && t.setAttribute("nonce", i);
                                    var a = t.src || t.href;
                                    a && (0, o.hk)("isSRIEnabled") && (t.setAttribute("integrity", "sha384-" + a.split(".js")[0].split(".css")[0].slice(-64).replace(/-/g, "+").replace(/_/g, "/")), t.setAttribute("crossorigin", "anonymous")), e.head.appendChild(t)
                                })
                            })(t)
                        }, !e && i.append((0, s.yt)()), i[0].appendChild(r), a.domContainer.iframe = i.find("iframe")[0], h.push(() => {
                            t(), m = !0, $ZSD(r.contentDocument).find("html")[0].className += (0, s.MF)().join(" "), l(), $ZSD(window).on("resize", l), (0, c.Vm)(0)
                        })
                    }),
                    b = () => d = !1,
                    _ = () => {
                        h.forEach(e => e()), h = []
                    },
                    y = () => {
                        window.location.protocol.includes("https") && (navigator.userAgent.includes("Chrome") || navigator.userAgent.includes("Firefox")) && (0, r.L)("call.isavsupported", {})
                    },
                    w = e => {
                        p.push(e)
                    },
                    k = () => {
                        p.forEach(e => e()), p = []
                    },
                    z = () => d
            },
            9291: (e, t, i) => {
                "use strict";
                i.d(t, {
                    nG: () => z,
                    ZV: () => S,
                    R: () => k,
                    LT: () => x
                });
                var o = i(9636),
                    n = i(2220),
                    a = i(6460),
                    r = i(7408),
                    s = i(2615),
                    l = i(4386),
                    c = i(5812),
                    d = i(5046),
                    m = i(7490),
                    h = i(4912);
                let p = e => {
                        if ((0, m.CY)()) {
                            if (o.domContainer.float[e ? "removeClass" : "addClass"]("zsiq-custom-stickercont"), e) return o.domContainer.sticker && o.domContainer.sticker.remove(), void delete o.domContainer.sticker;
                            o.domContainer.float.append((0, h.Ti)()), o.domContainer.sticker = $ZSD("#zsiq_cus_sticker")
                        }
                    },
                    u = e => {
                        if ((0, m.F8)()) return e ? (o.domContainer.flogo && o.domContainer.flogo.remove(), void delete o.domContainer.flogo) : (o.domContainer.float.append((0, h.aj)()), void(o.domContainer.flogo = $ZSD("#zs_fl_logo")))
                    };
                var f = i(4562),
                    g = i(6826),
                    v = i(8008);
                let b = (e, t) => {
                        let i = e ? "addClass" : "removeClass";
                        o.domContainer.float[i]("zsiq-toggle"), (0, m.Kx)() && o.domContainer.float[i]("zsiq-show"), (0, g.Y)(t), o.domContainer.iframeWrap[i]("chat-iframe-open")
                    },
                    _ = e => {
                        (0, n.xj)({
                            cw_open: e
                        }, !0), (0, r.e)("iframestate", e)
                    },
                    y = e => {
                        o.domContainer.float && o.domContainer.float[e ? "removeClass" : "addClass"]("zsiq-hide")
                    },
                    w = (e, t) => {
                        let i = o.domContainer.float.find("#zs_fl_close"),
                            n = o.domContainer.float.find((0, m.CY)() ? "#zsiq_cus_sticker" : ".zsiq-chat-icn");
                        i.attr("tabindex", e ? "0" : "-1"), n.length && (n.attr("tabindex", e ? "-1" : "0"), !t && (e ? i[0] : n[0]).focus())
                    },
                    k = (e = !1, t = !1) => (n.visitorInfo.hideMinimizeButton && y(!1), (0, f.A0)(n.cb.cw_dragposition), t = t && (0, g.c)(), b(!0, t), p(!0), u(!0), (0, d.Nl)(!1), w(!0, e), (0, n.hk)("isWidgetDragEnabled") && (0, f.Hi)(), (0, a.rq)(t).then(() => {
                        (0, s.L)("chat.open"), _(!0)
                    })),
                    z = (e = !1) => {
                        !(0, m.Kx)() && n.visitorInfo.hideMinimizeButton && y(!0), b(!1), _(!1), p(!1), u(!1), (0, d.Nl)(!0), w(!1, e), (0, m.$G)() && $ZSD("#zs-fl-tip").remove(), (0, s.L)("chat.close"), (0, f.Hi)(n.yZ)
                    },
                    x = () => {
                        var e;
                        o.domContainer.floatWrap = $ZSD('[data-id="zsalesiq"]'), o.domContainer.float = $ZSD("#zsiq_float"), o.domContainer.iframeWrap = $ZSD("#zsiq_chat_wrap"), o.domContainer.indicator = (e = $ZSD("#zsiq-indicator"))[0] && e, $ZSD(document).on("click", l.Fs), $ZSD("#zs-tip-close").on("click", e => {
                            e.stopPropagation(), (0, n.xj)({
                                hideTip: !0
                            }), $ZSD("#zs-fl-tip").remove()
                        });
                        let t = "show" === (n.visitorInfo.floatwindowvisible || n.visitorInfo.chatwindowvisible);
                        if ((0, n.hk)("isWidgetDragEnabled") && !v.rv && (() => {
                                let e, t, i = o.domContainer.float[0],
                                    a = !1;
                                i.setAttribute("draggable", !0);
                                let r = (e, t) => {
                                        i.style.left = e, i.style.top = t, (0, n.xh)({
                                            top: t,
                                            left: e
                                        })
                                    },
                                    s = o => {
                                        if (n.Uy.cw_open) return;
                                        let s, l;
                                        o.preventDefault(), "touchmove" === o.type ? (s = o.touches[0].clientX - e, l = o.touches[0].clientY - t, a = !0) : (s = o.clientX - e, l = o.clientY - t);
                                        let c = 0,
                                            d = $ZSD("#zs-fl-tip")[0];
                                        d && (c = -d.offsetLeft), s = Math.min(Math.max(s, 0 + c), window.innerWidth - i.clientWidth), l = Math.min(Math.max(l, 0), window.innerHeight - i.clientHeight), r(s + "px", l + "px")
                                    },
                                    l = () => {
                                        let e = document.getElementById("ghostimage");
                                        e && document.body.removeChild(e)
                                    };
                                n.yZ && r(n.yZ.left, n.yZ.top), i.addEventListener("dragstart", o => {
                                    let n = document.createElement("div");
                                    n.id = "ghostimage", n.setAttribute("style", "position: absolute;height:40px; width: 40px;z-index: -1; opacity:0"), document.body.appendChild(n), o.dataTransfer.setDragImage(n, 0, 0);
                                    let a = i.getBoundingClientRect();
                                    e = o.clientX - a.left, t = o.clientY - a.top
                                }), document.addEventListener("dragover", s), i.addEventListener("dragend", l), i.addEventListener("touchmove", s), i.addEventListener("touchstart", o => {
                                    o.preventDefault(), a = !1;
                                    let n = i.getBoundingClientRect();
                                    e = o.touches[0].clientX - n.left, t = o.touches[0].clientY - n.top, setTimeout(function() {
                                        !a && i.click()
                                    }, 200)
                                }), i.addEventListener("touchend", () => {
                                    a = !1, l()
                                })
                            })(), n.Uy.cw_open && !(0, m.Kx)() || t) return k();
                        (0, m.$G)() && (0, a.rq)(), p(), u()
                    },
                    S = e => "hide-widget" === e.target.id ? (e.stopPropagation(), void $ZSD('[data-id="zsalesiq"]').remove()) : n.Uy.cw_open ? z("click" === e.type) : ((0, s.L)("chatbutton.click"), (0, a.dl)() || (0, c.h)(), k("click" === e.type), (0, c.sR)("notify_widget_clicked"))
            },
            2338: (e, t, i) => {
                "use strict";
                i.r(t), i.d(t, {
                    default: () => o
                }), i(1181);
                const o = (() => {
                        var e = "zoho_cookie",
                            t = ["essential", "performance", "analytics"],
                            i = ["click", "keypress"];

                        function o() {
                            try {
                                return window.top.localStorage ? window.top : window
                            } catch {
                                return window
                            }
                        }

                        function n(e, t = "") {
                            return !e.hasOwnProperty("openInNewTab") && (e.openInNewTab = !0), e.linkTitle && e.linkUrl ? `<a class="${t||"details"}" href="${e.linkUrl}" target="${e.openInNewTab?"__blank":"self"}" ref="noopener noreferrer" role="link">${e.linkTitle}</a>` : ""
                        }

                        function a(e) {
                            var t = "";
                            return Object.entries(e).forEach(([e, i]) => t += `${e}: ${i}; `), t
                        }

                        function r(e, t) {
                            return {
                                back: `<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="279" height="512" viewBox="0 0 279 512" class="zoho-back-icon" id="${t}" role="button" tabindex="0">\n            <title></title>\n            <g id="icomoon-ignore">\n            </g>\n            <path d="M3.5 43.8l200.7 212.2-200.7 212.1c-5.2 5.5-5.2 12.8 0 18.3l22.5 23.8c1.7 1.8 6.9 1.8 8.6 1.8s5.2 0 6.9-1.8l233.5-245.1c5.2-5.5 5.2-12.8 0-18.3l-231.7-243.2c-1.7-1.8-5.2-3.6-8.6-3.6-3.5 0-6.9 1.8-8.6 3.6l-22.6 22c-5.1 5.4-5.1 12.8 0 18.2v0z"></path>\n            </svg>`,
                                close: `<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="512" height="512" viewBox="0 0 512 512" class="zoho-close-icon" role="button" tabindex="0" id="${t}">\n            <title></title>\n            <g id="icomoon-ignore">\n            </g>\n            <path d="M9.2 502.2c12.8 12.8 35.2 12.8 51.2 0l195.2-195.2 195.2 195.2c12.8 12.8 35.2 12.8 51.2 0 12.8-12.8 12.8-35.2 0-51.2l-195.2-195.2 195.2-195.2c12.8-12.8 12.8-35.2 0-51.2-12.8-12.8-35.2-12.8-51.2 0l-195.2 195.2-195.2-195.2c-12.8-12.8-35.2-12.8-51.2 0-12.8 12.8-12.8 35.2 0 51.2l195.2 195.2-195.2 195.2c-12.8 12.8-12.8 35.2 0 51.2v0z"></path>\n            </svg>`
                            }[e]
                        }

                        function s() {
                            return o().cookie_consent[0] || {}
                        }

                        function l(e = !0) {
                            ["decline", "accept"].forEach((t, o) => {
                                var n = document.getElementById(t);
                                i.forEach(t => {
                                    n && n[e ? "addEventListener" : "removeEventListener"](t, e => {
                                        ("click" === e.type || 13 === e.keyCode) && f(!!o)
                                    })
                                })
                            })
                        }

                        function c(e) {
                            if ("click" === e.type || 13 === e.keyCode) {
                                var t = e.target.dataset.metaid,
                                    i = o().cookie_consent,
                                    n = [];
                                i.forEach(e => {
                                        var i = e.cookie_types;
                                        i && i[t] && i[t].meta && (n = n.concat(i[t].meta))
                                    }),
                                    function(e, t) {
                                        var i = s().cookie_types[e],
                                            o = `<div class="zoho_zpmain" data-id="zoho_cookie_preview" data-uid="cookie_meta">\n            ${r("close","hide")}\n            ${r("back","back")}\n            <div class="zoho_pLR30 zoho-cookiebody-div" id="cookie_meta">\n                <div class="zoho_zpsubmain zoho-zptabl" style="margin-top:40px;">\n                    <div class="zoho_zphdr">${i.title||""}</div>\n                    <div class="zoho_clr6">${i.description||""}</div>\n                </div>\n                <div class="zoho-cookiecont-div">\n                    ${function(e,t){var i="",o=s().tableButtons||[];t.forEach(e=>{i+=`<div class="zoho_tr">\n                        <div class="zoho_td">${e.name}</div>\n                        <div class="zoho_td zoho_wpro">${e.purpose}</div>\n                        <div class="zoho_td">${e.url}</div>\n                        <div class="zoho_td">${e.expiry}</div>\n                    </div>`});var n=` < div class = "zoho-footer " > \n < div class = "zoho-cbtn zoho-wbtn"
                                        data - id = "decline-cookie"
                                        data - selid = "${e}"
                                        role = "button"
                                        tabindex = "0" > $ {
                                            o[0].title || ""
                                        } < /div>\n                        <div class="zoho-cbtn" data-id="accept-cookie" data-selid="${e}" role="button" tabindex="0">${o[1].title||""}</div > \n < /div>`;return`<div class="zoho_table" tabindex="-1">\n                            <div class="zoho_th">\n                                <div class="zoho_td">Name</div > \n < div class = "zoho_td zoho_wpro" > Purpose < /div>\n                                <div class="zoho_td">URL</div > \n < div class = "zoho_td" > Expiry < /div>\n                            </div > \n $ {
                                            i
                                        }\
                                        n < /div>\n                        ${"essential"!==e?n:""}`}(e,t)}\n                </div > \n < /div>\n        </div > `;d(!0,o)}(t,n)}}function d(e,t){document.querySelector('[data-uid="cookie_container"]').classList[e?"add":"remove"]("zoho_hide");var i=document.getElementById("preference_container");if(e)i.insertAdjacentHTML("beforeend",t),m(!0);else{m(!1);var o=document.querySelector('[data-uid="cookie_meta"]');o&&o.remove()}}function m(e){["decline-cookie","accept-cookie"].forEach(t=>{var o=document.querySelector(` [data - id = "${t}"]
                                        `);i.forEach(t=>{o&&o[e?"addEventListener":"removeEventListener"](t,e=>{if("click"===e.type||13===e.keyCode){var t=e.target.dataset.id,i=e.target.dataset.selid;document.getElementById(i)["decline-cookie"===t?"removeAttribute":"setAttribute"]("checked",!0),p("accept-cookie"===t,i),d(!1)}})})}),function(e){["hide","back"].forEach(t=>{var o=document.getElementById(t);i.forEach(t=>{o&&o[e?"addEventListener":"removeEventListener"](t,e=>{"click"!==e.type&&13!==e.keyCode||("hide"===e.target.id?h(!1):d(!1))})})})}(e)}function h(e){var t=document.getElementById("preference_container");t&&t.classList[e?"remove":"add"]("zoho_hide")}function p(e,i){e?t.push(i):t.splice(t.indexOf(i),1)}function u(e){var t,o;!g()&&e?(function(){!function(){var e=s(),t=e.customCss||e.isRtl?".zoho_zpmain .details,.zoho_zpmain a{color:#0080ff}.zoho_prv_main *{-webkit-transition:.3s cubic-bezier(.25, .8, .25, 1);-moz-transition:.3s cubic-bezier(.25, .8, .25, 1);-o-transition:.3s cubic-bezier(.25, .8, .25, 1);transition:.3s cubic-bezier(.25, .8, .25, 1);transform-origin:center center}.zoho_prv_main,.zoho_prv_main *{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.zoho_prv_main{font-family:inherit;position:fixed;z-index:1111111111111111!important;width:100%;height:100%;top:0;right:0;background:rgba(0,0,0,.35);animation:.2s zohopreview;-webkit-animation:.2s zohopreview;-ms-animation:.2s zohopreview;-o-animation:.2s zohopreview;-moz-animation:.2s zohopreview}[data-id=zoho_cookie_preview]{display:flex;flex-direction:column;width:1060px;max-width:95%;padding:50px 0 0;overflow:hidden;text-align:right;transform:translateY(-50%);-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);top:50%;margin:auto;border-radius:10px;background-color:#fff;position:relative;cursor:default;max-height:80vh;color:#2f3138}.zoho-cbtn,.zoho-cookiecont-div .curp,.zoho-okbtn,.zoho_zpmain .details{cursor:pointer}.zoho-footer{background-color:#fff;height:80px;position:relative;border-top:1px solid #eee;justify-content:space-between;flex-shrink:0;width:100%}.zoho-cbtn,.zoho-footer{display:flex;align-items:center}.zoho-cbtn{height:38px;border-radius:6px;line-height:inherit;padding:0 13px;color:#fff;background-color:#06c;justify-content:center;font-size:15px;min-width:140px;position:absolute;left:110px;top:0;bottom:0;margin:auto}.zoho-cbtn:hover{background-color:#0082e5}@keyframes zohopreview{from{opacity:0}100%{opacity:1}}@-webkit-keyframes zohopreview{from{opacity:0}100%{opacity:1}}@-moz-keyframes zohopreview{from{opacity:0}100%{opacity:1}}@-o-keyframes zohopreview{from{opacity:0}100%{opacity:1}}@-ms-keyframes zohopreview{from{opacity:0}100%{opacity:1}}@keyframes fontscaleanim{0%{transform:scale(1)}50%{transform:scale(1.45);-webkit-transform:scale(1.45);-moz-transform:scale(1.45);-ms-transform:scale(1.45)}100%{transform:scale(1);-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);-o-transform:scale(1)}}@media only screen and (min-width:100px) and (max-width:555px){[data-id=zoho_cookie_preview]{padding:30px 20px 0;border-radius:5px;max-height:calc(100% - 20px)}.zoho-footer{padding:0 20px;width:calc(100% + 40px);right:-20px}.zoho-prev-content{padding:30px 0;max-height:none;height:calc(100% - 20px)}body #zoho-prev-header,body .zoho-prev-content{padding-right:0;padding-left:0}body .zoho-footer{padding-right:30px;padding-left:30px}body div#zoho-prev-header:before{width:100%;right:0}body .zoho-cbtn{left:30px}}div[data-id=zoho_cookie_preview].zoho_zpmain{padding-top:30px;font-size:14px;line-height:22px;width:700px}.notify-cookie div[data-id=zoho_cookie_preview].zoho_zpmain{width:800px}.zoho_pLR30{padding:0 30px}.zoho_zphdr{font-size:20px;font-weight:700;margin-bottom:10px}.zoho_zpsubmain{border-bottom:1px solid #d6d6d6;padding-bottom:25px;margin-bottom:30px}.zoho-cookie-pref{padding:0 30px;overflow:auto}.zoho_zpsubhdr{display:inline-flex;align-items:center;padding-right:40px;position:relative}.zoho_zpsubhdr input[type=checkbox]{margin:0 0 0 10px;position:absolute;right:10px}.zoho_zpsubhdr span{font-size:15px;color:#aaa}.zoho_zpsubhdr input[type=checkbox]:checked+span{color:#000}.zoho_zpsubdtl{color:#666;padding-right:40px;margin-top:5px}.zoho_zpsubmain:last-child{border-bottom:none;margin-bottom:10px}.zoho_zpmain .zoho-cbtn{position:static;margin:0 0 0 20px;width:160px;height:42px;border-radius:6px}.zoho_zpmain .zoho-footer{display:flex;align-items:center;justify-content:center}.zoho_zpmain .zoho-cbtn.zoho-wbtn{border:1px solid #aaa;background-color:#fff;color:#aaa;margin-left:20px}.zoho_zpmain #confirmmychoice{color:#888}.zoho_zpmain #confirmmychoice:hover{border-color:#888}.zoho_td,.zoho_th{border-bottom:1px solid #d6d6d6}.zoho_table{border:1px solid #d6d6d6;border-radius:10px;max-height:820px;overflow:auto;filter:blur(0px)}.zoho_th,.zoho_tr{display:table-row;vertical-align:middle;width:100%;background-color:#f4f4f4}.zoho_tr{background-color:#fff}.zoho_td.zoho_wpro{width:46%;flex-shrink:0}.zoho_td{width:20%;padding:5px 10px;word-break:break-word;display:table-cell;vertical-align:middle;border-left:1px solid #d6d6d6}.zoho_th{position:sticky;top:0}.zoho_zpsubmain.zoho-zptabl{border:none;margin:0}.zoho-cookiebody-div,.zoho-cookiecont-div{overflow:hidden;height:100%;display:flex;flex-direction:column}.zoho-cookiecont-div{overflow:auto;padding-bottom:30px}@media only screen and (max-width:500px){.zoho_zpmain{padding:10px}.zoho-cookiecont-div{padding:0 10px}.zoho_zpsubhdr input[type=checkbox]{right:0}.zoho_zpsubdtl,.zoho_zpsubhdr{padding-right:20px}.zoho_zpmain .zoho-cbtn{transform:scale(.8) translate(13px,0);margin:0;font-size:14px}.zoho_zpmain .zoho-cbtn.zoho-wbtn{margin-left:0;transform:scale(.8) translate(-6px,0)}[data-id=zoho_cookie_preview]{padding:30px 10px 0 4px}}.zoho-msgbanr{width:100%;background-color:rgba(46,67,82,.9);padding:20px 30px;color:#fff;font-size:15px;position:fixed;bottom:0;z-index:9999999999;text-align:center;right:0;display:table;table-layout:fixed;font-family:inherit;box-sizing:border-box}.zoho-msgbanr span{max-width:80%;margin-left:15px;text-align:right}.dib-mid,.zoho-clkoptn{display:inline-block;vertical-align:middle}.zoho-okbtn{padding:5px 15px;background-color:transparent;border-radius:6px;margin-left:15px;border:2px solid #fff}.zoho_hide{display:none}.zoho-back-icon,.zoho-close-icon{right:20px;transform:rotate(-180deg);padding:7px;position:absolute;line-height:normal;top:20px;cursor:pointer;font-size:18px;text-align:center;width:26px;height:26px;border-radius:26px;background-color:#f3f3f3;fill:#666}.zoho-close-icon{left:20px;right:auto}.lmore{color: #fff}":".zoho_zpmain .details,.zoho_zpmain a{color:#0080ff}.zoho_prv_main *{-webkit-transition:.3s cubic-bezier(.25, .8, .25, 1);-moz-transition:.3s cubic-bezier(.25, .8, .25, 1);-o-transition:.3s cubic-bezier(.25, .8, .25, 1);transition:.3s cubic-bezier(.25, .8, .25, 1);transform-origin:center center}.zoho_prv_main,.zoho_prv_main *{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.zoho_prv_main{font-family:inherit;position:fixed;z-index:1111111111111111!important;width:100%;height:100%;top:0;left:0;background:rgba(0,0,0,.35);animation:.2s zohopreview;-webkit-animation:.2s zohopreview;-ms-animation:.2s zohopreview;-o-animation:.2s zohopreview;-moz-animation:.2s zohopreview}[data-id=zoho_cookie_preview]{display:flex;flex-direction:column;width:1060px;max-width:95%;padding:50px 0 0;overflow:hidden;text-align:left;transform:translateY(-50%);-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);top:50%;margin:auto;border-radius:10px;background-color:#fff;position:relative;cursor:default;max-height:80vh;color:#2f3138}.zoho-cbtn,.zoho-cookiecont-div .curp,.zoho-okbtn,.zoho_zpmain .details{cursor:pointer}.zoho-footer{background-color:#fff;height:80px;position:relative;border-top:1px solid #eee;justify-content:space-between;flex-shrink:0;width:100%}.zoho-cbtn,.zoho-footer{display:flex;align-items:center}.zoho-cbtn{height:38px;border-radius:6px;line-height:inherit;padding:0 13px;color:#fff;background-color:#06c;justify-content:center;font-size:15px;min-width:140px;position:absolute;right:110px;top:0;bottom:0;margin:auto}.zoho-cbtn:hover{background-color:#0082e5}@keyframes zohopreview{from{opacity:0}100%{opacity:1}}@-webkit-keyframes zohopreview{from{opacity:0}100%{opacity:1}}@-moz-keyframes zohopreview{from{opacity:0}100%{opacity:1}}@-o-keyframes zohopreview{from{opacity:0}100%{opacity:1}}@-ms-keyframes zohopreview{from{opacity:0}100%{opacity:1}}@keyframes fontscaleanim{0%{transform:scale(1)}50%{transform:scale(1.45);-webkit-transform:scale(1.45);-moz-transform:scale(1.45);-ms-transform:scale(1.45)}100%{transform:scale(1);-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);-o-transform:scale(1)}}@media only screen and (min-width:100px) and (max-width:555px){[data-id=zoho_cookie_preview]{padding:30px 20px 0;border-radius:5px;max-height:calc(100% - 20px)}.zoho-footer{padding:0 20px;width:calc(100% + 40px);left:-20px}.zoho-prev-content{padding:30px 0;max-height:none;height:calc(100% - 20px)}body #zoho-prev-header,body .zoho-prev-content{padding-left:0;padding-right:0}body .zoho-footer{padding-left:30px;padding-right:30px}body div#zoho-prev-header:before{width:100%;left:0}body .zoho-cbtn{right:30px}}div[data-id=zoho_cookie_preview].zoho_zpmain{padding-top:30px;font-size:14px;line-height:22px;width:700px}.notify-cookie div[data-id=zoho_cookie_preview].zoho_zpmain{width:800px}.zoho_pLR30{padding:0 30px}.zoho_zphdr{font-size:20px;font-weight:700;margin-bottom:10px}.zoho_zpsubmain{border-bottom:1px solid #d6d6d6;padding-bottom:25px;margin-bottom:30px}.zoho-cookie-pref{padding:0 30px;overflow:auto}.zoho_zpsubhdr{display:inline-flex;align-items:center;padding-left:40px;position:relative}.zoho_zpsubhdr input[type=checkbox]{margin:0 10px 0 0;position:absolute;left:10px}.zoho_zpsubhdr span{font-size:15px;color:#aaa}.zoho_zpsubhdr input[type=checkbox]:checked+span{color:#000}.zoho_zpsubdtl{color:#666;padding-left:40px;margin-top:5px}.zoho_zpsubmain:last-child{border-bottom:none;margin-bottom:10px}.zoho_zpmain .zoho-cbtn{position:static;margin:0 20px 0 0;width:160px;height:42px;border-radius:6px}.zoho_zpmain .zoho-footer{display:flex;align-items:center;justify-content:center}.zoho_zpmain .zoho-cbtn.zoho-wbtn{border:1px solid #aaa;background-color:#fff;color:#aaa;margin-right:20px}.zoho_zpmain #confirmmychoice{color:#888}.zoho_zpmain #confirmmychoice:hover{border-color:#888}.zoho_td,.zoho_th{border-bottom:1px solid #d6d6d6}.zoho_table{border:1px solid #d6d6d6;border-radius:10px;max-height:820px;overflow:auto;filter:blur(0px)}.zoho_th,.zoho_tr{display:table-row;vertical-align:middle;width:100%;background-color:#f4f4f4}.zoho_tr{background-color:#fff}.zoho_td.zoho_wpro{width:46%;flex-shrink:0}.zoho_td{width:20%;padding:5px 10px;word-break:break-word;display:table-cell;vertical-align:middle;border-right:1px solid #d6d6d6}.zoho_th{position:sticky;top:0}.zoho_zpsubmain.zoho-zptabl{border:none;margin:0}.zoho-cookiebody-div,.zoho-cookiecont-div{overflow:hidden;height:100%;display:flex;flex-direction:column}.zoho-cookiecont-div{overflow:auto;padding-bottom:30px}@media only screen and (max-width:500px){.zoho_zpmain{padding:10px}.zoho-cookiecont-div{padding:0 10px}.zoho_zpsubhdr input[type=checkbox]{left:0}.zoho_zpsubdtl,.zoho_zpsubhdr{padding-left:20px}.zoho_zpmain .zoho-cbtn{transform:scale(.8) translate(-13px,0);margin:0;font-size:14px}.zoho_zpmain .zoho-cbtn.zoho-wbtn{margin-right:0;transform:scale(.8) translate(6px,0)}[data-id=zoho_cookie_preview]{padding:30px 4px 0 10px}}.zoho-msgbanr{width:100%;background-color:rgba(46,67,82,.9);padding:20px 30px;color:#fff;font-size:15px;position:fixed;bottom:0;z-index:9999999999;text-align:center;left:0;display:table;table-layout:fixed;font-family:inherit;box-sizing:border-box}.zoho-msgbanr span{max-width:80%;margin-right:15px;text-align:left}.dib-mid,.zoho-clkoptn{display:inline-block;vertical-align:middle}.zoho-okbtn{padding:5px 15px;background-color:transparent;border-radius:6px;margin-right:15px;border:2px solid #fff}.zoho_hide{display:none}.zoho-back-icon,.zoho-close-icon{left:20px;transform:rotate(180deg);padding:7px;position:absolute;line-height:normal;top:20px;cursor:pointer;font-size:18px;text-align:center;width:26px;height:26px;border-radius:26px;background-color:#f3f3f3;fill:#666}.zoho-close-icon{right:20px;left:auto}.lmore{color: #fff}",i=document.createElement("style");i.insertAdjacentHTML("beforeend",t),document.getElementsByTagName("body")[0].appendChild(i)}();var e=document.getElementsByTagName("body")[0],t=s(),i="",o="";!t.customHtml&&Object.entries(t.cookie_types).forEach(([e,t])=>{i+=function(e,t){return` < div class = "zoho_zpsubmain" > \n < label class = "zoho_zpsubhdr" > \n < input type = "checkbox"
                                        id = "${e}"
                                        checked = "true"
                                        $ {
                                            "essential" === e ? 'disabled=""' : ""
                                        } > < span > $ {
                                            t.title
                                        } < /span>\n                        </label > \n < div class = "zoho_zpsubdtl" > \n $ {
                                            t.description
                                        }\
                                        n $ {
                                            t.linkTitle ? `<span class="details" data-id="cookie_meta" data-metaid="${e}" role="button" tabindex="0">${t.linkTitle}</span>` : ""
                                        }\
                                        n < /div>\n                    </div > `}(e,t)}),t.preferenceButtons&&t.preferenceButtons.forEach((e,t)=>{o+=function(e,t){var i=e?"accept":"decline";return` < div class = "zoho-cbtn ${e?"
                                        ":"
                                        zoho - wbtn zoho_clr8 "}"
                                        style = "${a(t.style||{})}"
                                        id = "${i}"
                                        role = "button"
                                        tabindex = "0" > \n $ {
                                            t.title
                                        }\
                                        n < /div>`}(t,e)});let c=a(t.buttons[0].style||{}),d={};t.buttons[1]&&(d=a(t.buttons[1].style||{}));var m=t.customHtml||`<div class="gdbr-banner-cont" id="consent_container">\n\t\t\t<div id="gdpr_banner" class="zoho-msgbanr">\n\t\t\t\t<span class="dib-mid">\n\t\t\t\t\t${t.title}\n\t\t\t\t\t${n(t,"lmore gdpr_lmore_link")}\n\t\t\t\t</span > \n\ t\ t\ t\ t < div class = "zoho-clkoptn" > \n\ t\ t\ t\ t\ t$ {
                                            t.buttons[0] ? `<div class="zoho-okbtn dib-mid" style="${c}" id="accept_all" role="button" tabindex="0">${t.buttons[0].title}\n\t\t\t\t\t</div>` : ""
                                        }\
                                        n\ t\ t\ t\ t\ t$ {
                                            t.buttons[1] ? `<div class="zoho-okbtn dib-mid" style="${d}" id="preference" role="button" tabindex="0">${t.buttons[1].title}</div>` : ""
                                        }\
                                        n\ t\ t\ t\ t < /div>\n\t\t\t</div > \n\ t\ t\ t < div class = "zoho_prv_main notify-cookie zoho_hide"
                                        id = "preference_container" > \n\ t\ t\ t\ t < div class = "zoho_zpmain"
                                        data - id = "zoho_cookie_preview"
                                        data - uid = "cookie_container" > \n\ t\ t\ t\ t\ t$ {
                                            r("close", "close")
                                        }\
                                        n\ t\ t\ t\ t\ t < div class = "zoho-cookiecont-div"
                                        id = "zohocookieui" > \n\ t\ t\ t\ t\ t\ t < div class = "zoho_pLR30 zoho_zpsubmain" > \n\ t\ t\ t\ t\ t\ t < div class = "zoho_zphdr" > $ {
                                            t.header.title || ""
                                        } < /div>\n\t\t\t\t\t\t<div class="zoho_clr6" role="link">${t.header.description||""} ${n(t.header||{},"details cookie_policy_url")}</div > \n\ t\ t\ t\ t\ t\ t < /div>\n\t\t\t\t\t\t<div class="zoho-cookie-pref">\n\t\t\t\t\t\t${i}\n\t\t\t\t\t\t</div > \n\ t\ t\ t\ t\ t < /div>\n\t\t\t\t\t<div class="zoho-footer">\n\t\t\t\t\t\t${o}\n\t\t\t\t\t</div > \n\ t\ t\ t\ t < /div>\n\t\t\t</div > \n\ t\ t < /div>`;e.insertAdjacentHTML("beforeend",m),l()}(),function(){["accept_all","preference","close"].forEach(e=>{var t=document.getElementById(e);i.forEach(i=>{t&&t.addEventListener(i,t=>{if("click"===t.type||13===t.keyCode)switch(e){case"accept_all":return f(!0);case"preference":return d(!1),h(!0);case"close":return h(!1)}})})});let e=document.querySelectorAll('[data-id="cookie_meta"]');e&&Array.from(e).forEach(e=>{i.forEach(t=>e.addEventListener(t,c))})}(),(o=s()).cookie_types&&Object.keys(o.cookie_types).forEach(e=>{var t=document.getElementById(e);i.forEach(e=>{t&&t.addEventListener(e,e=>{if("click"===e.type||13===e.keyCode){var t=e.target.id,i=13===e.keyCode?!e.target.checked:e.target.checked;e.target.checked=i,e.target.setAttribute("checked",i),p(i,t)}})})})):(t=document.getElementById("consent_container"))&&(l(!1),t.remove())}function f(e){var i=s(),n=e?t:i.customHtml?[]:["essential"];u(!1),v("cookie_status",e),v("allowed_cookies",n),o().cookie_consent.forEach(t=>{t.handler&&t.handler(e,{selected_cookies:n})})}function g(){return null!=_("cookie_status")}function v(t,i){var n=b();n[t]=i,o().localStorage.setItem(e,JSON.stringify(n))}function b(){return JSON.parse(o().localStorage.getItem(e)||"{}")}function _(e){return b()[e]}return{showConsent:function(e={handler:()=>{}}){if(!g()){var t=o(),i=!!t.cookie_consent;i||(t.cookie_consent=[]),t.cookie_consent.push(e),!i&&u(!0)}},clearAll:function(){o().localStorage.removeItem(e)},isCookieSet:g,getSelectedCookies:function(){return _("allowed_cookies")||[]},setValue:v}})()},2660:(e,t,i)=>{"use strict";i.d(t,{L:()=>l,u:()=>c});var o=i(5287),n=i(6677),a=i(2220),r=i(2067),s=i(8321);const l={essential:[{name:"JSESSIONID",expiry:"When the browsing session ends",purpose:"This cookie is generated by servlet containers like Tomcat and used for session management for the HTTP protocol",url:"http_cookie"},{name:"'LS_CSRF_TOKEN'",expiry:"When the browsing session ends",purpose:"This cookie is used for security purposes in order to avoid Cross-Site Request Forgery, (CSRF) for the AJAX calls made by the visitor",url:"http_cookie"},{name:"'gdpr_'+<@screenname@>+'__donottrack'",expiry:"1 month",purpose:"This cookie stores the GDPR consent from the visitor - enable/disable tracking",url:"http_cookie"},{name:"e3de1f7d42(random checksum)",expiry:"When the browsing session ends",purpose:"This cookie is used for internal load balancing of SalesIQ servers",url:"http_cookie"},{name:"'fbpgsr_'+<@lsid@>",expiry:"Until cleared",purpose:"Used in the Facebook app to authenticate the SalesIQ API request",url:"http_cookie"},{name:"uesign",expiry:"1 Month",purpose:"This cookie is used to manage the security of the applications.",url:"http_cookie"}],performance:[{name:"isiframeenabled",expiry:"1 day",purpose:"This cookie is set when the Live Chat feature is disabled by proactive chats/trigger/JSAPI",url:"http_cookie"},{name:"'zld'+<@lsid@>+'Article_'+<@article_id@>",expiry:"1 day",purpose:"This cookie stores if an article/FAQ is liked or not by the visitor",url:"http_cookie"},{name:"'ZLD'+<@appid@>+'WAITING'",expiry:"Embed waiting time",purpose:"This cookie stores the waiting time of the chat time details for a chat",url:"http_cookie"},{name:"'ZLD'+<@appid@>+'WTIME'",expiry:"Embed waiting time",purpose:"This cookie stores the remaining waiting time details for a chat",url:"http_cookie"},{name:"cdn_status",expiry:"2 days",purpose:"This cookie stores the cdn status for getting the static files",url:"http_cookie"},{name:"<@chatid@>+'_translate'",expiry:"1 day",purpose:"This cookie stores whether translation is enabled or not",url:"http_cookie"},{name:"'gdpr_'+<@screenname@>+'_trackingconfig'",expiry:"1 Month",purpose:"This cookie stores GDPR consent configured in portal side to handle the visitor end widget accordingly with consent banners",url:"http_cookie"}],analytics:[{name:"<@screenname@>+'-'_zldp'",expiry:"2 years",purpose:"This cookie identifies the unique visitors for the website",url:"http_cookie"},{name:"<@screenname@>+'-'_zldt'",expiry:"1 day",purpose:"This cookie identifies unique visits for a visitor in the website",url:"http_cookie"},{name:"<@screenname@>+'-'_siqid'",expiry:"2 years",purpose:"This cookie helps you track users across all domains",url:"http_cookie"},{name:"<@screenname@>+'-'_uuid'",expiry:"2 years",purpose:"This cookie provides user id needed in REST API to get the data of the current user.",url:"http_cookie"},{name:"<@screenname@>+'-'mc_cid'",expiry:"1 day",purpose:"This cookie tracks Mailchimp details about the visitor via campaign id & member email's unique id",url:"http_cookie"},{name:"<@screenname@>+'-'mc_eid'",expiry:"1 day",purpose:"This cookie tracks Mailchimp details about the visitor via unique id & member email's unique id",url:"http_cookie"},{name:"_zldvfp",expiry:"6 months",purpose:"Used to identify visitors referred from email campaigns",url:"http_cookie"},{name:"'ZLD'+<@appid@>",expiry:"1 week",purpose:"This cookie stores the Live Chat attender details and visitor details",url:"http_cookie"},{name:"'ZLD'+<@lsid@>+'avuid'",expiry:"1 week",purpose:"This cookie stores the avuid unique id to denote a visitor",url:"http_cookie"},{name:"'ZLDTRIGGER'+<@appid@>",expiry:"1 week",purpose:"This cookie stores the trigger details when the visitor is contacted",url:"http_cookie"},{name:"ZLDPERSONALIZE",expiry:"180 seconds",purpose:"This cookie stores attender ID in cookie for personalized chat",url:"http_cookie"},{name:"'ZLD'+<@lsid@>+'tabowner'",expiry:"180 seconds",purpose:"This cookie stores WMS session ID",url:"http_cookie"},{name:"<@avuid@>+'_accesstime'",expiry:"1 day",purpose:"This cookie stores the last fetched conversation's end time",url:"http_cookie"}]},c=()=>{let{cookieConfig:e,cookiePreference:t}=a.embedDetails,i="custom"===t,o=a.embedDetails.privacy&&a.embedDetails.portalConfig.gdprEnabled;return!o||a.cb.cookies||i||0===e.code?i&&o?Promise.reject():Promise.resolve():d(e)};let d=e=>{let t=a.visitorInfo.trackbannerobj||{},c=t.policytextandlink||{},d=((0,n.K2)(c.link)?c.link:e.url)||"https:/ / www.zoho.com / salesiq / cookies - policy.html ";d=(0,r.Ae)(d);let m=o.k;return new Promise(n=>{i.e(720).then(i.bind(i,2338)).then(i=>{i.default.showConsent({title:(0,s.Z)(t.description||e.content)||(0,o.N)("
                                        gdpr.banner.notify "),linkTitle:(0,s.Z)(c.text)||m("
                                        cookie.learnmore "),linkUrl:d,buttons:1===e.code?[{title:(0,o.N)("
                                        gdpr.banner.button.ok ")}]:[{title:m("
                                        cookie.accept.all "),style:{"
                                        background - color ":"#
                                        fff ",color:"
                                        #333"}},{title:(0,o.N)("gdpr.banner.preferences")}],header:{title:m("cookie.your.preference"),description:m("cookie.your.preference.desc"),linkTitle:m("cookie.policy"),openInNewTab:!0,linkUrl:d},cookie_types:{essential:{title:m("cookie.essential"),description:m("cookie.essential.desc"),linkTitle:m("cookie.learnmore"),meta:l.essential},performance:{title:m("cookie.performance"),description:m("cookie.performance.desc"),linkTitle:m("cookie.learnmore"),meta:l.performance},analytics:{title:m("cookie.analytics"),description:m("cookie.analytics.desc"),linkTitle:m("cookie.learnmore"),meta:l.analytics}},preferenceButtons:[{title:m("cookie.use.essential")},{title:(0,o.N)("gdpr.banner.accept")}],tableButtons:[{title:t.declinebutton||(0,o.N)("gdpr.banner.decline")},{title:t.acceptbutton||(0,o.N)("gdpr.banner.accept")}],isRtl:!1,handler:(e,t)= > {
                                            if (!e) return;
                                            let i = t.selected_cookies;i.includes("analytics") && n(),
                                            (0, a.xj)({
                                                cookies: !0
                                            }),
                                            a.visitorInfo.cookieconsent = i
                                        }
                                    })
                        })
                })
        }
    }, 9636: (e, t, i) => {
        "use strict";
        i.d(t, {
            P: () => a,
            T: () => n,
            domContainer: () => o
        });
        let o = {},
            n = () => {
                Object.keys(o).forEach(e => o[e] ? .remove()), o = {}
            },
            a = () => !Object.keys(o).length
    }, 1199: () => {
        let e = (e, i) => "string" == typeof e ? new t(...(i || document).querySelectorAll(e)) : new t(e);
        class t extends Array {
            addClass(e) {
                this.each(t => {
                    t.classList.add(e)
                })
            }
            removeClass(e) {
                return this.each(t => {
                    t.classList.remove(e)
                }), this
            }
            toggleClass(e) {
                this.each(({
                    classList: t
                }) => {
                    t[t.contains(e) ? "remove" : "add"](e)
                })
            }
            each(e) {
                this.forEach((...t) => {
                    t[0] && e(...t)
                })
            }
            on(e, t, i) {
                this.each(o => {
                    o.addEventListener(e, e => {
                        "function" == typeof t ? t(e) : e.target.matches(t) && i(e)
                    })
                })
            }
            off(e, t) {
                this.each(i => i.removeEventListener(e, t))
            }
            attr(e, t) {
                return void 0 !== t ? this[0].setAttribute(e, t) : this[0].getAttribute(e)
            }
            html(e) {
                return this.forEach(t => {
                    t.innerHTML = e
                }), this
            }
            text(e) {
                this.each(t => {
                    t.innerText = e
                })
            }
            append(e) {
                return this[0].insertAdjacentHTML("beforeend", e), this
            }
            remove() {
                return this.forEach(e => {
                    e.parentNode.removeChild(e)
                }), this
            }
            find(t) {
                return e(t, this[0])
            }
            show() {
                this.removeClass("zsiq-hide")
            }
            hide() {
                this.addClass("zsiq-hide")
            }
            removeStyle(e) {
                this[0].style.removeProperty(e)
            }
            addStyle(e) {
                Object.keys(e).forEach(t => {
                    this[0].style[t] = e[t]
                })
            }
            offsetTop() {
                return this[0].offsetTop
            }
            offsetLeft() {
                return this[0].offsetLeft
            }
            getStyle(e) {
                return this[0].style[e]
            }
            getRect() {
                return this[0].getBoundingClientRect()
            }
            offsetWidth() {
                return this[0].offsetWidth
            }
            offsetHeight() {
                return this[0].offsetHeight
            }
        }
        window.$ZSD = function(t) {
            return e(t)
        }
    }, 5812: (e, t, i) => {
        "use strict";
        i.d(t, {
            iO: () => C.iO,
            Us: () => C.Us,
            ST: () => C.ST,
            aX: () => C.aX,
            h: () => C.h,
            r2: () => C.r2,
            sR: () => C.sR,
            Kh: () => C.Kh
        }), i(1199);
        var o = i(7408),
            n = (i(1181), i(8008)),
            a = i(2220),
            r = i(2115),
            s = i(6677),
            l = i(33),
            c = i(9636);
        let d = (e, t, i) => {
            let o = (0, a.vK)(e, t);
            return (0, a.dg)(e, t), a.visitorInfo[e] && i && ((e, t) => {
                (0, C.sR)("update_info", {
                    ["info" === e ? "cinfo" : e.replace("_", "")]: t
                })
            })(e, t), o
        };
        const m = { ...(0, r.g)("visitor.", [
                ["chat"],
                ["attend"],
                ["missed"],
                ["agentsoffline", "offline"],
                ["offlineMessage", "offline"],
                ["chatmessage", "chatmessage"],
                ["chatcomplete"],
                ["rating"],
                ["feedback"],
                ["idle"],
                ["active"],
                ["trigger"],
                ["triggeredchat"]
            ]),
            destroy(e) {
                let t = JSON.stringify({});
                (0, C.Us)(), localStorage.setItem("siqlsdb", t), localStorage.setItem(l.xl, t)
            },
            name: e => (d("last_name", e, !0), d("name", e, !0)),
            email: e => (0, s.vV)(e) && d("email", e, !0),
            contactnumber: e => (0, s.yf)(e) && d("phone", e, !0),
            firstname: e => d("first_name", e, !0),
            lastname: e => d("last_name", e, !0),
            salutation(e) {
                if (e) return e.endsWith(".") && (e = e.slice(0, -1)), d("salutation", e, !0)
            },
            id: e => (0, a.vK)("id", e),
            info: e => d("info", e, !0),
            question: e => (0, a.vK)("question", e),
            uniqueid: () => a.visitorInfo.uvid,
            uniqueuserid: () => a.visitorInfo.uuid,
            idleTime(e) {
                isNaN(e) || (e = parseFloat(e), a.visitorInfo.idletime = e, (0, C.Kh)(e))
            },
            pagetitle: e => d("pageTitle", e),
            cpage: e => d("cpage", e),
            referer: e => d("referrer", e),
            autopick: e => (e && (a.visitorInfo.autopick = e), a.visitorInfo.autopick),
            customaction(e, t) {
                (0, s.iS)(e) && (d("customaction", {
                    field: e,
                    value: t || {}
                }), (0, C.r2)())
            },
            getGeoDetails() {
                a.visitorInfo.getLocation = !0
            },
            handleinfo(e) {
                let {
                    name: t,
                    email: i,
                    phone: o
                } = e;
                t && (d("last_name", t, !0), d("name", t, !0)), i && (0, s.vV)(i) && d("email", i, !0), o && (0, s.yf)(o) && d("phone", o, !0)
            },
            ongoingchat() {
                let e = c.domContainer.iframe;
                return !!e && e.contentWindow.$Support.hasOngoingChat()
            },
            setlocation(e) {
                a.visitorInfo.address = e
            }
        };
        var h = i(9291),
            p = i(6460);
        const u = { ...(0, r.g)("call.", [
                ["isavsupported"],
                ["attended"],
                ["completed"],
                ["missed"],
                ["cancelled"]
            ]),
            initiatecall: e => (e && (a.visitorInfo.initiateCallOnClick = e), (0, p.dl)() && (0, o.e)("initiatecall"), a.visitorInfo.initiateCallOnClick),
            department: e => (e && (a.visitorInfo.call_department = e), a.visitorInfo.call_department),
            start() {
                (0, a.PL)() ? (0, o.e)("callstart") : ((0, p.wk)(() => {
                    (0, o.e)("callstart")
                }), (0, h.R)())
            }
        };
        var f = i(1038);
        const g = {
            logintoken(e) {
                (0, f.S8)(e)
            },
            logout() {
                (0, f.n1)()
            },
            jwttokengetter(e) {
                (0, f.TE)(e, "jwtRegenerator")
            },
            logoutcomplete: function(e) {
                (0, f.TE)(e, "logoutComplete")
            }
        };
        var v = i(1753);
        const b = { ...(0, r.g)("chat.", [
                    ["online"],
                    ["offline"],
                    ["waitinghandler"],
                    ["open"],
                    ["close"],
                    ["missed"]
                ]),
                theme(e) {
                    e && (a.visitorInfo.embedtheme = e)
                },
                messagehint: e => (0, a.vK)("messagehint", e),
                forward: e => (e && (a.visitorInfo.forward = e), a.visitorInfo.forward),
                attend(e) {
                    e && (0, v.bz)("visitor.attend", e)
                },
                complete(e) {
                    e ? (0, v.bz)("visitor.chatcomplete", e) : (0, o.e)("chatend")
                },
                continue (e) {
                    e ? e && (0, v.bz)("chat.continue", e) : setTimeout(() => (0, o.e)("showActiveChat"), 1e3)
                },
                transferchat(e) {
                    e && (0, v.bz)("agent.transferchat", e)
                },
                accepttransfer(e) {
                    e && (0, v.bz)("agent.accepttransfer", e)
                },
                restrictAnalyticsValue: function(e) {
                    return e && e.trim() && (a.visitorInfo.blockAnalytics = "true" === e), a.visitorInfo.blockAnalytics
                },
                enableCrossDomain: e => (e && e.trim() && (a.visitorInfo.enablecrossdomain = e), a.visitorInfo.enablecrossdomain),
                mode: e => ("click" === e && (a.visitorInfo.initiateChatOnClick = !0), a.visitorInfo.initiateChatOnClick),
                department: e => (0, a.vK)("department", e),
                defaultdepartment: function(e) {
                    return (0, a.vK)("defaultdepartment", e)
                },
                agentMessage(e) {
                    e && (0, v.bz)("visitor.chatmessage", e)
                },
                triggerMessage(e) {
                    e && (0, v.bz)("visitor.chattrigger", e)
                },
                agent: e => (e && (a.visitorInfo.agent = e), a.visitorInfo.agent),
                messages: e => (e && (a.visitorInfo.chatmessages = e), a.visitorInfo.chatmessages),
                systemmessages: e => (e && (a.visitorInfo.chatmessages = e), a.visitorInfo.chatmessages),
                start() {
                    (0, a.PL)() ? (0, o.e)("chatstart") : ((0, p.wk)(() => {
                        (0, o.e)("chatstart")
                    }), (0, h.R)())
                },
                waittime(e) {
                    e && (a.visitorInfo.waitime = e)
                },
                title: (e, t) => (e && (a.visitorInfo.title = e), t && (a.visitorInfo.titlestyle = t), a.visitorInfo.title),
                logo: (e, t) => (e && (a.visitorInfo.clogo = e), t && (a.visitorInfo.cwebsite = t), a.visitorInfo.clogo),
                floatingwindow() {}
            },
            _ = {
                closebutton(e) {
                    a.visitorInfo.closeicon = e
                },
                position: e => (e && (a.visitorInfo.floatposition = e), a.visitorInfo.floatposition),
                visible(e) {
                    if (e) {
                        a.visitorInfo.floatvisible = e;
                        let t = (e = !0) => {
                            let t = c.domContainer.float;
                            t && (t.removeClass(e ? "zsiq-hide" : "zsiq-show"), t.addClass(e ? "zsiq-show" : "zsiq-hide"))
                        };
                        ["show", "hide", "0"].includes(e) ? t("hide" !== e) : parseInt(e) && setTimeout(t, 1e3 * parseInt(e))
                    }
                    return a.visitorInfo.floatvisible
                },
                onlineicon: {
                    src() {}
                },
                offlineicon: {
                    src() {}
                },
                click() {},
                coin: {
                    hidetooltip() {
                        a.visitorInfo.hideTooltip = !0
                    }
                }
            },
            y = {
                texts: e => (e && (a.visitorInfo.buttontexts = e), a.visitorInfo.buttontexts),
                icon: e => (e && (a.visitorInfo.buttonicon = e), a.visitorInfo.buttonicon),
                visible: e => (e && (a.visitorInfo.buttonvisible = e, c.domContainer.float && c.domContainer.float[0].classList.add("show" === e ? "zsiq-show" : "zsiq-hide")), a.visitorInfo.buttonvisible),
                onlineicon: {
                    src: e => (e && (a.visitorInfo.buttononlineicon = e), a.visitorInfo.buttononlineicon)
                },
                offlineicon: {
                    src: e => (e && (a.visitorInfo.buttonofflineicon = e), a.visitorInfo.buttonofflineicon)
                },
                click(e) {
                    e && (0, v.bz)("chatbutton.click", e)
                },
                width: e => (e && (a.visitorInfo.bwidth = e), a.visitorInfo.bwidth)
            },
            w = {
                visible(e) {
                    if (e) {
                        a.visitorInfo.floatwindowvisible = e, parseInt(e) && (a.visitorInfo.floatwindowvisible = e, e = parseInt(e));
                        let t = function() {
                            (0, c.P)() || ("hide" === e ? (0, h.nG)() : (window.IS_BOT_TRIGGER_ON_JSAPI && ((0, C.h)(), (0, C.sR)("notify_widget_clicked")), (0, h.R)()))
                        };
                        ["show", "hide", 0].includes(e) ? t() : setTimeout(t, 1e3 * e)
                    }
                    return a.visitorInfo.floatwindowvisible
                },
                open(e) {
                    e ? (0, v.bz)("chat.open", e) : (0, h.R)()
                },
                close(e) {
                    e ? (0, v.bz)("chat.close", e) : (0, h.nG)()
                },
                minimize(e) {
                    e ? (0, v.bz)("floatwindow.minimize", e) : (0, h.nG)()
                },
                onlinetitle: e => (0, a.vK)("floatwindowonlinetitle", e),
                offlinetitle: e => (0, a.vK)("floatwindowofflinetitle", e),
                fields: e => (0, a.vK)("floatwindowfields", e),
                defaultview: e => (e && (a.visitorInfo.defaultview = e), a.visitorInfo.defaultview)
            };
        var k = i(2660),
            z = i(5075);
        window.$zohosq = n.oQ;
        let x = () => {
                (0, C.Us)(), (0, h.nG)(), (0, c.T)(), (0, p.VP)(), (0, a.Ck)(), localStorage.removeItem("siqlsdb"), localStorage.removeItem(l.xl), sessionStorage.removeItem(l.xl), localStorage.removeItem("zoho_cookie")
            },
            S = {
                jwttoken: g,
                clientactions: {},
                visitor: m,
                chat: b,
                call: u,
                article: {
                    content: e => (e && (a.visitorInfo.articlecontent = e), a.visitorInfo.articlecontent)
                },
                rating: {
                    visible(e) {
                        e && (a.visitorInfo.showRating = e)
                    }
                },
                feedback: {
                    visible(e) {
                        e && (a.visitorInfo.showFeedback = e)
                    }
                },
                integ: {
                    requestid: e => (e && (a.visitorInfo.requestid = e), a.visitorInfo.requestid)
                },
                chatwindow: {
                    visible: e => (e && (a.visitorInfo.chatwindowvisible = e, !(0, c.P)() && ("show" === e ? (0, h.R)() : (0, h.nG)())), a.visitorInfo.chatwindowvisible),
                    reload() {
                        (0, o.e)("chatwindowReload")
                    },
                    closebutton(e) {
                        a.visitorInfo.hideMinimizeButton = "hide" === e
                    },
                    open() {}
                },
                floatwindow: w,
                pastchat: {
                    visible(e) {
                        e && (a.visitorInfo.pastchatvisible = e)
                    }
                },
                field: {
                    clear(e) {
                        e && (a.visitorInfo.clearfield = e)
                    }
                },
                custom: {
                    html(e, t) {
                        a.visitorInfo.customhtml = [e, t]
                    }
                },
                customfield: {
                    add(e) {
                        let t = a.visitorInfo.customfield || [];
                        e && (t.push(e), a.visitorInfo.customfield = t)
                    },
                    clear(e) {
                        e && (a.visitorInfo.clearfield = e, a.visitorInfo.customfield = a.visitorInfo.customfield.filter(t => !e.includes(t.name)))
                    },
                    handleCallbacks(e) {
                        if (!e) return;
                        let t = a.visitorInfo.customfield.find(t => t.name === e.name);
                        t && t.callback && t.callback(e.val)
                    }
                },
                language: e => (e && (a.visitorInfo.language = e), a.visitorInfo.language),
                domain(e) {
                    if ($zoho.salesiq.domainAPIEnabled) {
                        let t = window.location.hostname;
                        e && -1 !== t.indexOf(e, t.length - e.length) && (a.visitorInfo.trackingdomain = e, n.oQ.tracking.domain(e))
                    } else n.oQ.tracking.domain(e)
                },
                reset() {
                    x(), i.e(337).then(i.bind(i, 337)).then(e => {
                        e.initiateEmbed()
                    })
                },
                privacy: {
                    chatbannercontent: e => (e && (a.visitorInfo.textbannerobj = e), a.visitorInfo.textbannerobj),
                    trackingbannercontent: e => (e && (a.visitorInfo.trackbannerobj = e), a.visitorInfo.trackbannerobj),
                    content: e => (e && (a.visitorInfo.chatprivacycontent = e.trim()), a.visitorInfo.chatprivacycontent),
                    getCookieList: () => k.L,
                    updateCookieConsent(e) {
                        e && "custom" === a.embedDetails.cookiePreference && (a.visitorInfo.cookieconsent = e, (0, C.iO)())
                    }
                },
                department: {
                    bannerText(e) {
                        if (!e) return;
                        let t = e.offline,
                            i = e.engaged;
                        t && (0, a.vK)("offlineBannerText", t), i && (0, a.vK)("engagedBannerText", i)
                    }
                },
                tracking: {
                    on() {
                        a.visitorInfo.tracking = "on", (0, C.iO)()
                    },
                    off() {
                        a.visitorInfo.tracking = "off", (0, C.ST)()
                    },
                    domain(e) {
                        let t = window.location.hostname;
                        e && -1 !== t.indexOf(e, t.length - e.length) && (a.visitorInfo.trackingdomain = e)
                    },
                    allowMultiTrigger(e) {
                        a.visitorInfo.mtrigger = !!e
                    },
                    getsiqid: () => z.c.siq_id
                },
                vanish() {
                    x(), ["$SIQDataHandler", "$ZSD", "$zohosq", "NEW_STATIC_URLS", "s", "state", "siqFloatJsonp", "t", "visitorInfo", "_IS_DEV", "_NEW_MEDIARTC_URLS", "_STATICURLS"].forEach(e => {
                        delete window[e]
                    }), delete window.$zoho.salesiq
                },
                getWidgetDetails() {
                    let e = a.embedDetails.portalConfig;
                    return {
                        soid: e.soid,
                        screenName: e.portalName
                    }
                },
                floatbutton: _,
                chatbutton: y,
                set(e, t) {
                    "home.widgets" === e && (a.visitorInfo.homeWidgets = t), (0, v.zN)()
                }
            },
            I = (e, t, i) => {
                for (let i in t) {
                    let o = t[i];
                    "object" != typeof o ? Object.defineProperty(e, i, {
                        get: () => o
                    }) : (e[i] = o, I(e[i], o, i))
                }
            };
        I(n.oQ, S), n.oQ.values = a.visitorInfo;
        var C = i(350);
        i(5504)
    }, 2115: (e, t, i) => {
        "use strict";
        i.d(t, {
            g: () => a,
            v: () => r
        }), i(1181);
        var o = i(1753),
            n = i(2220);
        let a = (e, t) => {
                let i = {};
                return t.forEach(([t, n]) => {
                    i[t] = function(i) {
                        "function" == typeof i && (0, o.bz)(e + (n || t), i)
                    }
                }), i
            },
            r = () => {
                let e = window.location.search,
                    t = window.location.hash;
                if (!e && t) {
                    let i = t.split("?");
                    i[1] && (e = i[1])
                }
                e && (e = e.replace(/^\?/g, "").split("&"), e.forEach(e => {
                    let [t, i] = e.split("=");
                    if (i = decodeURIComponent((i + "").replace(/\+/g, "%20")), i) switch (t) {
                        case "siq_eemail":
                            n.visitorInfo.eemail = i;
                            break;
                        case "siq_email":
                        case "om_email":
                            $zohosq.visitor.email(i);
                            break;
                        case "siq_ename":
                            return void(n.visitorInfo.ename = i);
                        case "siq_name":
                        case "om_name":
                            $zohosq.visitor.name(i)
                    }
                }), function() {
                    let e = location.href.split("?")[0],
                        t = location.search,
                        i = location.hash;
                    t && (t = t.replace(/^\?/g, ""), t = t.replace(/(^|&)siq_(name|email|ename|eemail)=[^&]*/g, ""), t = t.replace(/^&/g, ""), e += (t ? "?" + t : "") + i, window.history.replaceState(window.history.state, "", e))
                }())
            }
    }, 1753: (e, t, i) => {
        "use strict";
        i.d(t, {
            ZM: () => l,
            bz: () => s,
            zN: () => c
        });
        var o = i(7408),
            n = i(2220),
            a = i(2067);
        let r = {},
            s = (e, t) => {
                r[e] = t
            },
            l = (e, t) => {
                let i = "visitor.trigger" === e ? [t.triggername, t.visitorinfo] : (t && t.attenderemail && delete t.attenderemail, t && t.visitid ? [t.visitid, t] : [t]);
                (0, a.hZ)(r[e], ...i)
            },
            c = () => {
                (0, o.e)("jsapi", n.visitorInfo)
            }
    }, 1038: (e, t, i) => {
        "use strict";
        i.d(t, {
            d4: () => g,
            fh: () => v,
            S8: () => u,
            JC: () => b,
            _I: () => p,
            n1: () => _,
            TE: () => f
        }), i(1181);
        var o = i(2123),
            n = i(2220),
            a = i(33),
            r = i(7408);
        let s = null,
            l = null;
        const c = () => {
            l && (l(), s = null, l = null)
        };
        var d = i(350);
        const m = e => Date.now() > e,
            h = () => {
                let {
                    access_token: e
                } = n.Dh.accessToken || {};
                if (e) return Object.assign({
                    appID: n.embedDetails.widgetId
                }, {
                    accessToken: e
                })
            },
            p = () => (n.embedDetails.jwtConfig || {}).enabled && n.Dh.jwtToken,
            u = e => ((0, n.K_)("jwtToken", e.token), (e => {
                let t = (0, a.L2)("recentchatid"),
                    i = {
                        token: e.token,
                        app_id: n.embedDetails.widgetId
                    };
                return t && (i.conversation_id = t), o.Z.post(`/visitor/v2/${n.embedDetails.portalConfig.portalName}/jwtauthentication`, i)
            })(e).then(t => {
                var i;
                (e => {
                    let t = ["name", "email", "phone"];
                    for (let i of t) e[i] && (0, n.vK)(i, e[i])
                })((i = t).visitor), (0, n.K_)("accessToken", i), c(), !e.refetch && (0, r.e)("accessTokenReceived")
            }).catch(e => {
                6205 === e.code && n.Dh.jwtRegenerator && n.Dh.jwtRegenerator()
            })),
            f = (e, t) => {
                p() && (0, n.K_)(t, e)
            },
            g = async () => {
                let e = await (async () => {
                    if (!p()) return;
                    const e = n.Dh.accessToken || {};
                    return m(e.expiry) ? m(e.jwt_expiry) ? (n.Dh.jwtRegenerator && n.Dh.jwtRegenerator(), await (s || (s = new Promise((e, t) => {
                        l = e, setTimeout(() => {
                            c()
                        }, 5e3)
                    })), s), h()) : (await u({
                        token: n.Dh.jwtToken,
                        refetch: !0
                    }), h()) : h()
                })();
                (0, r.e)("latestJWTHeader", e)
            },
            v = e => {
                if (n.Dh.accessToken && e.match(/\/jwtauthentication(\/|$)/)) return h()
            },
            b = () => n.Dh.accessToken && n.Dh.accessToken.access_token,
            _ = () => {
                (() => {
                    let e = {
                        token: n.Dh.jwtToken,
                        app_id: n.embedDetails.widgetId
                    };
                    return o.Z.put(`/visitor/v2/${n.embedDetails.portalConfig.portalName}/jwtauthentication/logout`, e)
                })().then(() => {
                    (0, a.WY)(), (0, d.sR)("destroy"), n.Dh.logoutComplete && n.Dh.logoutComplete(), (0, n.sy)()
                })
            }
    }, 6826: (e, t, i) => {
        "use strict";
        i.d(t, {
            Y: () => s,
            c: () => r
        });
        var o = i(9636),
            n = i(8008),
            a = i(2220);
        const r = () => !a.Uy.cw_open && !n.LG && n.oQ.hasMiniView,
            s = e => {
                o.domContainer.iframeWrap[e ? "addClass" : "removeClass"]("zsiq-chat-minview")
            }
    }, 9808: (e, t, i) => {
        "use strict";
        i.d(t, {
            W: () => c
        });
        var o = i(2220),
            n = i(4386),
            a = i(350),
            r = i(8008);
        let s = e => e.substring(0, 3072),
            l = () => {
                (0, a.sR)("update_nav", c())
            };
        (e => {
            let t = e.pushState;
            e.pushState = function() {
                let i = t.apply(e, arguments);
                return l(), i
            }, $ZSD(window).on("hashchange", l)
        })(window.history);
        let c = () => {
            let e = r.dO;
            return {
                cpage: o.visitorInfo.cpage || s(e.location.href),
                ptitle: o.visitorInfo.pageTitle || n.tj || e.title,
                referer: o.visitorInfo.referrer || s(e.referrer),
                lsid: o.embedDetails.lsid
            }
        }
    }, 2615: (e, t, i) => {
        "use strict";
        i.d(t, {
            L: () => a
        });
        var o = i(1753),
            n = i(2220);
        let a = (e, t) => {
            (0, o.ZM)(e, t || ""), n.embedDetails.integrations.analytics && Promise.resolve().then(i.bind(i, 5504)).then(i => i.dispatchAnalytics(e, t))
        }
    }, 33: (e, t, i) => {
        "use strict";
        i.d(t, {
            E4: () => m,
            Hc: () => d,
            L2: () => l,
            WY: () => h,
            xl: () => n
        });
        var o = i(8008);
        const n = "siq_embed",
            a = {
                callDetail: ["departments"],
                chatDepartments: !0,
                callDepartments: !0
            },
            r = "ZSIQ_" + o.L0;
        let s = e => JSON.parse((e || localStorage).getItem(n) || "{}");
        const l = (e, t) => s(t) ? .[r] ? .[e];
        let c = (e, t) => t.setItem(n, JSON.stringify(e)),
            d = (e, t, i) => {
                let o = i ? sessionStorage : localStorage;
                if (window.isChatWindowResetted) return;
                let a = s(o);
                if (!a[r]) return (e => {
                    e[n] || (e[n] = JSON.stringify({})), e[n] = JSON.stringify({ ...JSON.parse(e[n]),
                        [r]: {}
                    })
                })(o), d(e, t, i);
                a[r][e] = t, c(a, o)
            };
        const m = (e, t = {}) => {
                Object.keys(a).forEach(e => {
                    let i = a[e];
                    Array.isArray(i) ? i.forEach(i => {
                        t[e] && (t[e] = { ...t[e]
                        }, delete t[e][i])
                    }) : i && delete t[e]
                }), d(e, t)
            },
            h = () => {
                [sessionStorage, localStorage].forEach(e => {
                    let t = s(e);
                    delete t[r], c(t, e)
                })
            }
    }, 2123: (e, t, i) => {
        "use strict";
        i.d(t, {
            Z: () => s
        });
        var o = i(8008),
            n = i(1038);
        let a = e => new Promise((t, i) => {
                e.onreadystatechange = function() {
                    if (4 === this.readyState)
                        if (204 === this.status) t();
                        else if (this.responseText) {
                        let e = JSON.parse(this.responseText);
                        200 === this.status ? t(e.data) : i(e.error)
                    }
                }
            }),
            r = (e, t, i) => {
                let r = new XMLHttpRequest,
                    s = (0, n.fh)(e);
                return e = o.LB + e, r.open(i, e, !0), r.setRequestHeader("x-siq-channel", o.LG ? "emailsignature" : "website"), s && (r.setRequestHeader("X-SIQ-APPID", s.appID), r.setRequestHeader("X-SIQ-ACCESSTOKEN", s.accessToken)), r.send(JSON.stringify(t)), a(r)
            };
        const s = {
            get(e, t) {
                let i = new XMLHttpRequest;
                return e = o.LB + e, t && (e += "?" + new URLSearchParams(t)), i.open("GET", e, !0), i.withCredentials = !0, i.setRequestHeader("x-siq-embed-version", 3), i.send(), a(i)
            },
            post: (e, t) => r(e, t, "POST"),
            put: (e, t) => r(e, t, "PUT")
        }
    }, 337: (e, t, i) => {
        "use strict";
        i.r(t), i.d(t, {
            handlePostEmbedDetails: () => y,
            initiateEmbed: () => v
        });
        var o = i(8008),
            n = i(2067),
            a = i(2220),
            r = i(2819),
            s = i(2115),
            l = (i(1181), i(6677)),
            c = i(350);
        const d = ["id", "name", "type", "siqatrib"],
            m = "siq_id",
            h = {
                name: {
                    key: "isNameSet",
                    val: ["name", "yourname", "subscribername", "contactname", "customername"]
                },
                firstname: {
                    val: ["first_name", "firstname", "first name", "fname"]
                },
                lastname: {
                    val: ["last_name", "lastname", "last name", "lname"]
                },
                email: {
                    key: "isEmailSet",
                    val: ["email", "emailid", "youremail", "contactemail", "subscriberemail", "mail", "customeremail", "emailaddress"],
                    validator: e => (0, l.vV)(e)
                },
                phone: {
                    key: "isPhoneSet",
                    val: ["phone", "pno", "phone no", "phoneno", "phno", "tel", "mobilenumber", "contactphone"],
                    validator: e => (0, l.yf)(e)
                },
                company: {
                    key: "isCompanySet",
                    val: ["company"]
                }
            },
            p = new class {
                constructor() {
                    this.isNameSet = !1, this.isEmailSet = !1, this.isPhoneSet = !1, this.isCompanySet = !1, this.forms = void 0, this.onSubmitObj = {}
                }
                hasElements(e) {
                    return !!e.length
                }
                handleForms() {
                    this.forms = Array.from(this.forms || document.forms), this.hasElements(this.forms) && this.includeFormSubmit(this.forms)
                }
                includeFormSubmit(e) {
                    e.forEach(e => {
                        const t = "autopick_" + Math.random().toString(36).substr(2, 10);
                        e.setAttribute(m, t), this.onSubmitObj[t] = e.onsubmit || (() => {}), e.onsubmit = e => this.onSubmitOverride(e)
                    })
                }
                getFuncName(e) {
                    return e && e.getAttribute(m)
                }
                onSubmitOverride(e) {
                    let t = e.target || e.srcElement;
                    if (t) {
                        this.autoPick(t);
                        let i = this.getFuncName(t);
                        i && !1 === this.onSubmitObj[i].call(t, e) && e.preventDefault && e.preventDefault()
                    }
                }
                autoPick(e) {
                    let t = this.getFormDetails(e),
                        i = Object.keys(t);
                    i.length && ((0, c.sR)("send_info", t), i.forEach(e => (0, a.vK)(e, t[e])))
                }
                getFormDetails(e) {
                    let t = {},
                        i = Array.from(e.getElementsByTagName("input"));
                    const o = ["firstname", "lastname"];
                    if (this.hasElements(i)) {
                        if (i.forEach(e => {
                                e.value && Object.keys(h).forEach(i => {
                                    if (!t[i]) {
                                        let o = this.getValue(i, e);
                                        o && (t[i] = o);
                                        let n = h[i].key;
                                        t[i] && n && (this[n] = !0)
                                    }
                                })
                            }), Object.entries(t).forEach(([e, i]) => {
                                if (!o.includes(e)) {
                                    let o = h[e].validator;
                                    o && !o(i) && (this[h[e].key] = !1, delete t[e])
                                }
                            }), t.firstname) {
                            let e = "";
                            e = t.firstname, t.lastname && (e += ` ${t.lastname}`, t.name = e), this.isNameSet = !0
                        }
                        o.forEach(e => delete t[e])
                    }
                    return t
                }
                getValue(e, t) {
                    let i = "";
                    if (this[h[e].key]) return;
                    let o = d.length;
                    for (let n = 0; n < o; n++) {
                        let o = t.getAttribute(d[n]);
                        if (o) {
                            o = this.formatAttr(o);
                            let n = h[e].val,
                                a = n.length;
                            for (let e = 0; e < a; e++)
                                if (o === n[e]) {
                                    i = t.value;
                                    break
                                }
                        }
                        if (i) break
                    }
                    return i
                }
                formatAttr(e) {
                    return e = e.toLowerCase().trim(), this.replaceSplChars(e)
                }
                replaceSplChars(e) {
                    return e.replace(/[_.-]/g, "")
                }
            };
        (() => {
            let e = e => {
                e.startsWith("https") || (e = o.Yi + "://" + e);
                let t = document.createElement("link");
                t.rel = "preconnect", t.href = e, document.head.appendChild(t)
            };
            o.fo.forEach(e), e(window._STATIC_URL)
        })();
        var u = i(2717),
            f = i(7490),
            g = i(9636);
        let v = () => {
                (0, a.eo)();
                let e = async e => {
                    g.domContainer.floatWrap || (b(), _(e), e.autoPickEnabled && p.handleForms())
                };
                (0, s.v)(), (0, n.hZ)(o.oQ.internalready), (0, n.hZ)(o.oQ.ready), (0, r.a)().then(() => {
                    if ("complete" === document.readyState) return e(a.embedDetails);
                    $ZSD(window).on("load", () => e(a.embedDetails))
                }).catch(e => {
                    b(e.code)
                })
            },
            b = e => {
                let t, i = {
                    outsideBusinessHours: !1,
                    hideWidgetOnMobile: !1,
                    operatorsBusy: !1,
                    brandDisabled: !1,
                    restrictedURI: !1,
                    allDepartmentsDisabled: !1
                };
                e ? (i.brandDisabled = 1406 === e, i.restrictedURI = 1432 === e, i.allDepartmentsDisabled = 1431 === e) : (i.outsideBusinessHours = a.embedDetails.isBHEnabled, i.hideWidgetOnMobile = !(!a.embedDetails.widget.float.hideInMobile || !u.ry), i.operatorsBusy = a.embedDetails.hideOnOffline && (0, f.re)(), t = "online" === a.embedDetails.embedStatus), (0, n.hZ)(o.oQ.afterReady, a.embedDetails.geoDetails || {}, {
                    widgetHideInfo: i,
                    status: t
                })
            },
            _ = () => {},
            y = e => {
                _ = e
            };
        v()
    }, 4912: (e, t, i) => {
        "use strict";
        i.d(t, {
            FM: () => h,
            MF: () => m,
            Ti: () => p,
            Zi: () => v,
            aj: () => f,
            rF: () => g,
            yt: () => b
        }), i(1637);
        var o = i(2220),
            n = i(5287),
            a = i(8321),
            r = i(2717),
            s = i(8364),
            l = i(8008),
            c = i(1070),
            d = i(7490);
        let m = () => {
                if (!r.ry) return [];
                let e = ["mobile"];
                return r.yZ.includes("android") && e.push("android"), r.yZ.includes("iphone") && e.push("iphone"), e
            },
            h = () => {
                let e = (0, d.Kx)() ? "zsiq-hide" : "",
                    t = "show" === o.visitorInfo.closeicon ? "" : "zsiq-hide",
                    i = (() => {
                        let e = {
                            bottom_left: "left-bottom",
                            topright: "right-top",
                            topleft: "left-top",
                            bottomright: "right-bottom",
                            bottomleft: "left-bottom",
                            left: "left",
                            right: "right",
                            top: "left-top",
                            bottom: "left-bottom",
                            left_middle: "left-middle",
                            left_bottom: "left-bottom",
                            left_top: "left-top"
                        }[o.visitorInfo.floatposition || o.embedDetails.widget.float.position || "bottomright"];
                        return e ? `position="${e}"` : ""
                    })(),
                    r = "",
                    c = (0, s.E)(o.embedDetails);
                parseInt(o.visitorInfo.floatvisible) && (e = "zsiq-hide");
                for (let e in c) r += `${e}=${c[e]}`;
                return `<div data-id="zsalesiq" ${i} ${l.LG?"class='zsiq-signature-chat'":""} theme=${(0,d.RW)()} ${r}><div id="zsiq_chat_wrap" class="${(()=>{let e=["chat-iframe-wrap","large"===o.embedDetails.chatConfig.size?"zsiq-large-size":"zsiq-medium-size"];return l._o&&e.push("bot-preview"),e.concat(m()).join(" ")})()}"><span class="seasonal-cw-bg"></span></div><div class="zsiq-float zsiq-flexM ${e}" role="button" aria-label="${(0,n.N)("customsticker.alt.text")}" id="zsiq_float"><span class="seasonal-float-img ${e}"></span>` + ((0, d.CY)() ? u() : ((0, d.F8)() ? "" : `<span class="${o._x?"siqico-call":"siqico-chat"} zsiq-chat-icn" aria-label="${(0,n.N)("customsticker.alt.text")}" role="button" tabindex="0"></span>`) + u() + `<em id="hide-widget" class="siqico-close zsiq-widget-close ${t}" role="button"></em>` + (() => {
                    if (o.embedDetails.widget.float.hideTooltip || o.visitorInfo.hideTooltip || o.cb.hideTip || (0, d.$G)()) return "";
                    let e = (() => {
                        let {
                            offlineContent: e,
                            onlineContent: t
                        } = o.embedDetails, {
                            buttontexts: i = [
                                [],
                                []
                            ]
                        } = o.visitorInfo;
                        return (0, d.re)() ? [i[1][0] || e.header || (0, n.N)("float.offline.maintitle"), i[1][1] || e.desc || (0, n.N)("float.offline.byline")] : [i[0][0] || t.header || (0, n.N)("float.online.maintitle"), i[0][1] || t.desc || (0, n.N)("float.online.byline")]
                    })();
                    return `<div class="zsiq-float-tooltip ${(0,o.hk)("isEmbedMobileView")?"mobile-view":""}" id="zs-fl-tip" role="tooltip"><div class="zsiq-clr3 zsiq-elips" data-zsqa="title">${(0,a.Z)(e[0])}</div><p class="zsiq-clr6 zsiq-m0 zsiq-mT3 zsiq-elips zsiq-tultip-desc" data-zsqa="subtitle">${(0,a.Z)(e[1])}</p>` + (o.embedDetails.widget.float.hideCloseBtn ? "" : '<em id="zs-tip-close" class="siqico-close zsiq-tultip-close" role="button" tabindex="0" aria-label="Close tooltip"></em>') + " </div>"
                })()) + (o.cb.umsg_count && parseInt(o.cb.umsg_count) ? v(o.cb.umsg_count) : "") + "</div></div>"
            },
            p = () => {
                let e = o.embedDetails.widget.float.customSticker,
                    t = (0, d.re)() && !e.offline.uploaded,
                    i = (0, n.N)("customsticker.alt.text");
                return `<img id="zsiq_cus_sticker" class="zsiq-hw-100 ${t?"grayscl":""}" src="${(0,c.P)(o.embedDetails.widget.float.customSticker)}" role="img" aria-label="${i}" alt="${i}" tabindex="0" />`
            },
            u = () => '<span class="siqico-close zsiq-close-icn" id="zs_fl_close" role="button" aria-label="Minimize live chat window" tabindex="-1"></span>',
            f = () => {
                let e = `${(0,n.N)("companylogo.alt.text")} / ${(0,n.N)("gravatar.alt.text")}`;
                return `<img id="zs_fl_logo" class="zsiq-hw-100 zsiq-bdr-rad100" src="${(0,d.uJ)(o.embedDetails.widget.float)}" role="img" alt="${e}" aria-label="${e}" />`
            },
            g = () => '<em class="siqico-call"></em>',
            v = (e, t) => {
                let i = ["zsiq-count-elem zsiq-flexM"];
                return o.Uy.cw_open && i.push("zsiq-hide"), t && i.push("zsiq-indicator-anim"), `<span id="zsiq-indicator" class="${i.join(" ")}">${e||g()}</span>`
            },
            b = () => '<div id="iframe_loader" class="chat-loader-cont">\n               <header class="chat-loader-hdr">\n                    <div class="zsiq-progress-load zsiq-load-circle"></div>\n                    <div class="zsiq-flexG">\n                         <div class="zsiq-progress-load zsiq-load-title"></div>\n                         <div class="zsiq-progress-load zsiq-load-desc zsiq-mT10"></div>\n                    </div>\n               </header>\n               <section class="zsiq-flexG chat-loader-sec zsiq-flexM">\n                    <div class="zsiq-circle-load"></div>\n               </section>\n               <footer class="chat-loader-ftr"></footer>\n          </div>'
    }, 2819: (e, t, i) => {
        "use strict";
        i.d(t, {
            P: () => c,
            a: () => l
        });
        var o = i(2123),
            n = i(8008),
            a = i(2717),
            r = i(4396),
            s = i(2220);
        let l = () => {
                let e = {
                    widgetcode: n.L0,
                    pagetitle: document.title.substring(0, 3e3),
                    current_domain: s.visitorInfo.trackingdomain || n.yK,
                    internal_channel_req: !0,
                    browser_language: a.ZP.getLanguage(),
                    is_signaturechat: n.LG
                };
                return s.Kz || (e.include_fields = "avuid"), Object.keys(s.embedDetails).length && (e.last_modified_time = s.embedDetails.lastModified, e.version = s.embedDetails.version), window._SIQ_WIDGET_LIVE_PREVIEW && (e.siq_widget_preview = !0), n.rv && (e.is_preview = !0), s.visitorInfo.language && (e.api_language = s.visitorInfo.language), s.visitorInfo.agent && (e.attender_email = s.visitorInfo.agent), s.visitorInfo.getLocation && !s.embedDetails.geoDetails && (e.include_fields = (e.include_fields || "") + ",geo_details"), o.Z.get("/visitor/v2/channels/website", e).then(e => ((0, s.S6)((0, r.T)(e)), e))
            },
            c = (e, t) => o.Z[t](`/visitor/v2/${s.embedDetails.portalConfig.portalName}/livevisitors/${s.Kz}/triggers`, e)
    }, 2220: (e, t, i) => {
        "use strict";
        i.d(t, {
            Kz: () => x,
            hG: () => w,
            _x: () => S,
            embedDetails: () => v,
            hk: () => R,
            PL: () => M,
            Ol: () => _,
            Dh: () => y,
            eo: () => E,
            cb: () => k,
            sy: () => P,
            Ck: () => N,
            Uy: () => z,
            S6: () => T,
            K_: () => $,
            dg: () => O,
            n6: () => A,
            vK: () => L,
            xh: () => j,
            xj: () => q,
            visitorInfo: () => b,
            yZ: () => C
        }), i(1181);
        var o = i(33),
            n = i(2067),
            a = i(8008);
        const r = e => {
                let t = JSON.parse(e);
                return {
                    enabled: !!t[0],
                    ...t[1]
                }
            },
            s = (e, t) => {
                let i = r(e);
                return {
                    message: i.msg,
                    ...t ? {
                        enabled: i.enabled
                    } : {
                        response: i.resmsg
                    }
                }
            },
            l = e => JSON.parse(e || "{}"),
            c = e => {
                let t = JSON.parse(e)[1];
                return {
                    onlineContent: {
                        header: t.online,
                        desc: t.online_byline
                    },
                    offlineContent: {
                        header: t.offline,
                        desc: t.offline_byline
                    }
                }
            };
        var d = i(1753),
            m = i(6677),
            h = i(1919);
        const p = "avuid",
            u = "embedDetails",
            f = "preferences",
            g = "siq_widget_position";
        let v = (0, o.L2)(u) || {},
            b = (0, o.L2)("vinfo") || {},
            _ = {},
            y = {};
        const w = ["name", "email", "phone", "first_name", "last_name", "salutation"];
        let k = (() => {
                let e = (0, h.F_)(f);
                return e ? JSON.parse(e) : (0, o.L2)(f) || {}
            })(),
            z = (0, o.L2)(f, sessionStorage) || {},
            x = (0, h.F_)(p) || (0, o.L2)(p) || "",
            S = !1,
            I = !1,
            C = (0, o.L2)(g, sessionStorage),
            E = () => {
                v = Object.assign((() => {
                    let e = l(localStorage.getItem("siqlsdb")),
                        t = l(e[`ZSIQ${a.L0}data`]);
                    if (!t.version) return {};
                    let {
                        embedobj: i,
                        widgetobj: o
                    } = t, n = i.einfo, d = n.props;
                    return {
                        avuid: e[`ZLD${n.lsid}avuid`],
                        enabled: !!o.islivechat,
                        formType: {
                            1: a.PE.inline,
                            2: a.PE.traditional,
                            3: a.PE.chat
                        }[r(d.formtype).val],
                        chatConfig: {
                            waiting_message: r(d.waitingmsg).msg,
                            agents_busy: s(d.busymsg),
                            agents_engaged: s(d.engagedmsg),
                            agents_offline: s(d.offlinemsg),
                            color: r(d.color).code,
                            mute_sound: r(d.icsound).enabled,
                            custom_css: (h = r(d.iscustomcss), {
                                enabled: h.enabled,
                                file_name: h.fname || h.pfname,
                                fpath: h.fpath
                            }),
                            print: r(d.icprint).enabled,
                            show_operator_image: r(d.icphoto).enabled,
                            feedback: s(d.feedback, !0),
                            reaction: r(d.reaction),
                            thanking_message: r(d.thanksmsg).msg,
                            show_company_logo: r(d.iclogo).enabled,
                            show_brand_logo: r(i.homepage_configs.show_brand_logo).enabled,
                            theme: {
                                1: "ribbon",
                                2: "crest",
                                3: "crayon",
                                4: "lloyd",
                                5: "airy",
                                6: "crown",
                                7: "connect",
                                8: "pattern",
                                9: "pattern_straight",
                                10: "pattern_curve"
                            }[i.theme]
                        },
                        callDetail: {
                            messages: d.call_messages,
                            feedback: s(d.call_feedback, !0),
                            reaction: r(d.call_reaction),
                            waitingTime: d.call_waiting_time
                        },
                        hasScreenShare: r(d.isscreenshare).enabled,
                        font: {
                            0: "Default System Font",
                            1: "Lato",
                            2: "Opensans",
                            3: "Oxygen",
                            4: "Roboto",
                            5: "Puvi"
                        }[r(o.font).default],
                        widgetId: n.embedid,
                        brandName: n.brandname,
                        viewConversations: r(d.isconversation).enabled,
                        hasReopenChat: n.reopen_enabled,
                        hasReadReceipt: i.read_receipt,
                        allowParellelChat: d.allow_multiple_live_chat,
                        homePageConfigs: i.homepage_configs,
                        wTime: n.waitingtime,
                        sendEmail: r(d.icmail).enabled,
                        drivenBySiqLink: i.freelogolink,
                        brandLogoUrl: i.logo_url,
                        resources: i.resources_config,
                        messageActionConfig: i.messageactions,
                        privacy: o.isgdprenabled,
                        showEmoji: r(d.showsmiley).enabled,
                        showVoiceNote: d.voice_notes_enabled,
                        gtPolicyUrl: i.gtattributionlink,
                        gtPolicyConfig: o.privacyconfig.notifygt,
                        utsDomain: o.utsserver,
                        productUrl: i.producturl,
                        chatTerms: (() => {
                            let e = o.privacyconfig.notify_terms;
                            return {
                                enabled: !!e.code,
                                url: e.url
                            }
                        })(),
                        ccMasking: (() => {
                            let e = d.credit_card_masking;
                            return {
                                enabled: e.enabled,
                                consentContent: e.consent_banner_content
                            }
                        })(),
                        cookieConfig: (() => {
                            let e = i.pinfo.pinfo;
                            return {
                                code: e.trackingprivacyconfig,
                                url: e.cookiepolicyurl,
                                content: e.trackingprivacystatement
                            }
                        })(),
                        cookiePreference: i.pinfo.cookiePreference,
                        share_screen: r(d.isscreenshare).enabled,
                        hasFileShare: r(d.icfile).enabled,
                        rtcpInfo: (() => {
                            let e = i.rtcp_info || {};
                            return {
                                rtcp_csrf_param: e.RTCPCFPARAMNAME,
                                chunkCommon: e.chunck_common,
                                chunkMedia: e.chunck_media,
                                chunkVendors: e.chunck_vendors,
                                cssUrls: e.css_url,
                                jsUrls: e.js_url,
                                rtcp_cookie: e._RTCPCFCOOKIENAME,
                                rtcpPrd: e.prd,
                                domainUrl: e.domain_url
                            }
                        })(),
                        recordingDetails: (m = n.embedstatus.call_recording, {
                            audioConsent: m.call_consent_message,
                            screenConsent: m.screenshare_consent_message,
                            type: m.type
                        }),
                        lsid: n.lsid,
                        productName: i.productname,
                        version: t.version,
                        lastModified: t.last_modified,
                        hasCallRecording: d.call_recording,
                        i18nKeys: {
                            chat_widget: o.i18nKeys,
                            chat_window: o.i18nChatwindowKeys
                        },
                        ...c(o.title),
                        hideOnOffline: !!o.hideoffline[0],
                        annonid: i.annonid,
                        serverURL: i.embedserverurl,
                        use_apache: i.uapache,
                        autoPickEnabled: o.autopick.isenabled,
                        widget: {
                            float: {
                                color: o.color,
                                customSticker: (e => {
                                    let t = e[1],
                                        i = e => e ? {
                                            file_name: e.pfname || e.fname,
                                            id: e.fpath && e.fpath.split("/")[1].split("_")[0]
                                        } : "";
                                    return {
                                        enabled: !e[0],
                                        offline: i(t.offline),
                                        online: i(t.online)
                                    }
                                })(JSON.parse(o.sticker)),
                                showCompanyLogo: !!o.clogo[0],
                                hideTooltip: o.hide_tooltip,
                                hideInMobile: !!o.mdevice_hide[0],
                                gravatar: (() => {
                                    let [e, t] = JSON.parse(o.gravatar);
                                    return {
                                        enabled: !!e,
                                        file_name: t.fname,
                                        id: t.fpath && t.fpath.split("/")[1].split("_")[0],
                                        no: t.no
                                    }
                                })(),
                                position: {
                                    1: "bottom_right",
                                    2: "bottom_middle",
                                    3: "bottom_left",
                                    4: "left_bottom",
                                    5: "left_middle",
                                    6: "left_top",
                                    7: "top_left",
                                    8: "top_middle",
                                    9: "top_right",
                                    10: "right_top",
                                    11: "right_middle",
                                    12: "right_bottom"
                                }[r(o.position).no]
                            }
                        }
                    };
                    var m, h
                })(), v), v.avuid && D(v.avuid)
            },
            T = e => {
                v.i18nKeys && (e.i18nKeys = { ...v.i18nKeys,
                    ...e.i18nKeys
                }), v = Object.assign(v, e), v.avuid && D(v.avuid), (0, o.E4)(u, { ...v
                }), (e => {
                    S = (0, n._F)(e) && !e.components.includes("chat")
                })(v)
            },
            q = (e, t) => {
                let i = t ? z : k;
                for (let t in e) void 0 !== e[t] ? i[t] = e[t] : delete i[t];
                $zoho.salesiq.domainAPIEnabled && b.trackingdomain && !t ? (0, h.K5)(f, JSON.stringify(i)) : (0, o.Hc)(f, i, t)
            },
            D = e => {
                e && (x = e, $zoho.salesiq.domainAPIEnabled && b.trackingdomain ? (0, h.K5)(p, e, {
                    days: 7,
                    hours: 24
                }) : (0, o.Hc)(p, e))
            },
            L = (e, t) => {
                if (!(t = t && ("string" == typeof t ? (0, m.iS)(t) : t))) return b[e];
                if (b[e] = t, (0, d.zN)(), !w.includes(e)) return b[e];
                let i = {};
                return w.forEach(e => b[e] && (i[e] = b[e])), (0, o.Hc)("vinfo", i), b[e]
            },
            O = (e, t) => {
                w.includes(e) && (_[e] = t)
            },
            $ = (e, t) => {
                t && (y[e] = t)
            },
            P = () => y = {},
            N = () => {
                v = {}, a.oQ.values = b = {}, x = "", k = {}
            },
            R = e => v.dynamicConf[e],
            A = () => I = !0,
            M = () => I,
            j = e => {
                (0, o.Hc)(g, e, !0), C = e
            }
    }, 1637: (e, t, i) => {
        "use strict";
        var o = i(3379),
            n = i.n(o),
            a = i(7795),
            r = i.n(a),
            s = i(569),
            l = i.n(s),
            c = i(3565),
            d = i.n(c),
            m = i(9216),
            h = i.n(m),
            p = i(4589),
            u = i.n(p),
            f = i(4809),
            g = i.n(f),
            v = {};
        v.styleTagTransform = u(), v.setAttributes = d(), v.insert = l().bind(null, "head"), v.domAPI = r(), v.insertStyleElement = h(), n()(g(), v), g() && g().locals && g().locals;
        var b = i(6605),
            _ = i.n(b),
            y = {};
        y.styleTagTransform = u(), y.setAttributes = d(), y.insert = l().bind(null, "head"), y.domAPI = r(), y.insertStyleElement = h(), n()(_(), y), _() && _().locals && _().locals;
        var w = i(6655),
            k = i.n(w),
            z = {};
        z.styleTagTransform = u(), z.setAttributes = d(), z.insert = l().bind(null, "head"), z.domAPI = r(), z.insertStyleElement = h(), n()(k(), z), k() && k().locals && k().locals;
        var x = i(3639),
            S = i.n(x),
            I = {};
        I.styleTagTransform = u(), I.setAttributes = d(), I.insert = l().bind(null, "head"), I.domAPI = r(), I.insertStyleElement = h(), n()(S(), I), S() && S().locals && S().locals;
        var C = i(185),
            E = i.n(C),
            T = {};
        T.styleTagTransform = u(), T.setAttributes = d(), T.insert = l().bind(null, "head"), T.domAPI = r(), T.insertStyleElement = h(), n()(E(), T), E() && E().locals && E().locals;
        var q = i(2888),
            D = i.n(q),
            L = {};
        L.styleTagTransform = u(), L.setAttributes = d(), L.insert = l().bind(null, "head"), L.domAPI = r(), L.insertStyleElement = h(), n()(D(), L), D() && D().locals && D().locals
    }, 7490: (e, t, i) => {
        "use strict";
        i.d(t, {
            uJ: () => u,
            RW: () => m,
            F8: () => h,
            $G: () => p,
            OT: () => l,
            CY: () => s,
            Kx: () => d,
            re: () => c
        });
        var o = i(2220),
            n = i(1070),
            a = i(33),
            r = i(2717);
        let s = () => o.embedDetails.widget.float.customSticker.enabled,
            l = () => "offline" === o.embedDetails.embedStatus,
            c = () => (o._x || l()) && !o.embedDetails.callStatus,
            d = () => {
                let e = o.visitorInfo.floatvisible;
                return "show" !== e && ("hide" === e || !o.embedDetails.enabled || !o.embedDetails.components.includes("chat") && !o.embedDetails.components.includes("call") || o.embedDetails.isBHEnabled || o.embedDetails.widget.float.hideInMobile && r.ry || o.embedDetails.hideOnOffline && c())
            },
            m = () => {
                let e = o.embedDetails.chatConfig.theme;
                return [1, 7, 12, 29].includes(o.embedDetails.licenseDetail.planId) || e.includes("pattern") ? "pattern" : e
            },
            h = () => !s() && (o.embedDetails.widget.float.gravatar || {}).enabled,
            p = () => !!(0, a.L2)("ongoingchats"),
            u = e => {
                let t = e.gravatar || {};
                return t.enabled ? (0, n.c)({
                    name: t.file_name,
                    id: t.id,
                    type: "fgravatar"
                }) : o.visitorInfo.clogo || o.embedDetails.companyLogo.url
            }
    }, 1070: (e, t, i) => {
        "use strict";
        i.d(t, {
            c: () => r,
            P: () => s
        });
        const o = e => {
            let t = ["d"],
                i = t => e[`x-siq-${t}`];
            return ["soid", "module", "parentid"].forEach(e => {
                "parentid" == e ? t.push(i(e) || i("conversation" == i("module") ? "type" : "resourceid") || i("type")) : t.push(i(e))
            }), t.join("_")
        };
        var n = i(2220),
            a = i(7490);
        const r = e => {
                let {
                    udConfig: t
                } = n.embedDetails;
                return i = {
                    DOWNLOAD_SERVER_URL: t.download_server + "/public",
                    SIQ_UD_SERVICE_NAME: t.siq_ud_servicename,
                    public: !0
                }, a = t.ud_revamp ? {
                    "x-siq-soid": n.embedDetails.portalConfig.soid,
                    "x-siq-module": "brands",
                    "x-siq-type": e.type,
                    "x-siq-parentid": n.embedDetails.widgetId,
                    "x-siq-resourceid": e.id,
                    "x-siq-filename": e.name,
                    "x-siq-mode": "view"
                } : {
                    "x-siq-soid": n.embedDetails.portalConfig.soid,
                    "x-siq-ispreview": !1,
                    "x-siq-lsid": n.embedDetails.lsid,
                    "x-siq-filetype": e.type,
                    "x-siq-pfname": e.name,
                    "x-siq-downloadtype": "default"
                }, i.DOWNLOAD_SERVER_URL + "/" + i.SIQ_UD_SERVICE_NAME + "/download/" + o(a) + "?x-cli-msg=" + encodeURIComponent(JSON.stringify(a));
                var i, a
            },
            s = e => {
                let t = (0, a.re)() ? "offline" : "online",
                    i = e[t];
                return i.uploaded || (i = e.online, t = "online"), r({
                    id: i.id + "_" + n.embedDetails.lsid,
                    name: i.file_name,
                    type: "fsticker_" + t
                })
            }
    }, 5287: (e, t, i) => {
        "use strict";
        i.d(t, {
            N: () => n,
            k: () => a
        });
        var o = i(2220);
        let n = e => o.embedDetails.i18nKeys.chat_widget[e],
            a = e => o.embedDetails.i18nKeys.chat_window[e]
    }, 2067: (e, t, i) => {
        "use strict";
        i.d(t, {
            Ae: () => a,
            _F: () => n,
            hZ: () => o
        });
        let o = (e, ...t) => {
                if ("function" == typeof e) try {
                    e.call(void 0, ...t)
                } catch (e) {}
            },
            n = e => e.components.includes("call") && (e.homePageConfigs.conversation_mode.includes("call") || !e.components.includes("chat")) && !!e.callDepartments.length,
            a = e => e.includes("http") ? e : "http://" + e
    }, 4386: (e, t, i) => {
        "use strict";
        i.d(t, {
            Fs: () => s,
            W8: () => r,
            tj: () => n
        });
        var o = i(8008);
        let n, a, r = e => {
                n = n || o.dO.title, clearInterval(a), a = setInterval(() => {
                    o.dO.title = o.dO.title === e ? n : e
                }, 500)
            },
            s = () => {
                n && (o.dO.title = n), clearInterval(a)
            }
    }, 6677: (e, t, i) => {
        "use strict";
        i.d(t, {
            K2: () => r,
            iS: () => a,
            vV: () => n,
            yf: () => o
        });
        let o = e => a(e) && /^[+0-9A-Za-z():.\-\[\] ]{1,30}$/.test(e),
            n = e => a(e) && /^(\w([\w\-\.\+\'\/]*)@([\w\-\.]*)(\.[a-zA-Z]{2,22}(\.[a-zA-Z]{2}){0,2}))$/.test(e),
            a = e => e && e.trim(),
            r = (e = "") => a(e) && /^(((http|https):\/\/(www.){0,1})|www.){1}[a-zA-Z0-9]+[-a-zA-Z0-9@:/%_+.~#?&/=]*[^.]$/.test(e)
    }, 4196: (e, t, i) => {
        "use strict";
        i.r(t), i.d(t, {
            default: () => V
        }), i(1181);
        const o = (e, t = {}) => {
                let i = new Event(e);
                i.detail = t, window.dispatchEvent(i)
            },
            n = e => {
                o("dolog", {
                    msg: e
                })
            },
            a = () => {
                [{
                    name: "activate",
                    event: e => {}
                }, {
                    name: "install",
                    event: e => {}
                }, {
                    name: "fetch",
                    event: e => {}
                }].forEach(e => self.addEventListener(e.name, e.event))
            };
        class r {
            constructor() {
                this.registration = void 0, this.registerWorker()
            }
            async registerWorker() {
                if ("serviceWorker" in navigator) try {
                    let e = URL.createObjectURL(new Blob(["( " + a + " )()"]), {
                        type: "javascript"
                    });
                    this.registration = await navigator.serviceWorker.register(e, {
                        scope: "/"
                    }), registration.installing ? n("Service worker installing") : registration.waiting ? n("Service worker installed") : registration.active && n("Service worker active")
                } catch (e) {
                    n(`Registration failed with ${e}`)
                }
            }
        }
        let s = {
            brandName: void 0,
            lsid: void 0,
            idleTimeout: 0,
            inactivityTimeout: 36e5,
            domainCookie: void 0,
            initRetracking: !1,
            communicationHandler: () => {},
            triggers: void 0,
            token: void 0
        };
        window.state = s;
        const l = s,
            c = "utsdb",
            d = ["zldt", "con_id", "zldp", "avuid"];
        let m = "sessionStorage",
            h = (e, t = m) => {
                let i = window[t].getItem(e) || "";
                return i ? JSON.parse(i) : {}
            };
        var p = i(1919),
            u = i(2220);
        class f {
            constructor() {
                this.init()
            }
            init() {
                this.utsDB = h(c) || {}
            }
            getValue(e = "") {
                let t = Object.keys(this.utsDB).filter(t => t.includes(e)) ? .[0];
                return $zoho.salesiq.domainAPIEnabled && u.visitorInfo.trackingdomain && d.includes(e) ? (0, p.F_)(t || e) : this.utsDB[t] || ""
            }
            setValue(e = "", t) {
                $zoho.salesiq.domainAPIEnabled && u.visitorInfo.trackingdomain && d.includes(e) ? (0, p.K5)(e, t) : ((e, t, i) => {
                    ((e, t, i, o = m) => {
                        let n = h(e, o);
                        n[t] = i, window[o].setItem(e, JSON.stringify(n))
                    })(e, t, i)
                })(c, e, t), this.init()
            }
        }
        const g = (e = "") => ({
            _zldp: `${e}-_zldp`,
            _zldt: `${e}-_zldt`
        });
        class v extends f {
            constructor() {
                super()
            }
            notify({
                operation: e = "",
                params: t = {}
            }) {
                if ("ack" === e) return this.acknowledge()
            }
            acknowledge() {
                let e = new f,
                    t = g(l.brandName)._zldt,
                    i = (0, p.F_)(t) || e.getValue(t);
                i && this.sendMsg({
                    opr: "ack",
                    uvid: i
                })
            }
            getTime() {
                return "" + (new Date).getTime()
            }
            sendMsg(e) {
                "object" == typeof e && (e.event_time || (e.event_time = this.getTime()), W.send(e))
            }
        }
        const b = "sentMessages",
            _ = "sequence_no";
        class y extends v {
            constructor(e) {
                super(), this.sendMessage = e
            }
            updateSequenceNumber(e) {
                this.setValue(_, e)
            }
            getSequenceNumber() {
                return this.getValue(_) || 0
            }
            initCustomEvents() {
                window.addEventListener("afterMsgAckReceived", e => this.afterMsgAckReceived(e))
            }
            afterMsgAckReceived({
                detail: e
            }) {
                this.removeMessage(e.sseqno)
            }
            getMessages() {
                return this.getValue(b) || {}
            }
            setMessages(e) {
                this.setValue(b, e)
            }
            storeMessage(e, t) {
                if (e < 0) return;
                let i = this.getMessages();
                i[e] = t, this.setMessages(i)
            }
            removeMessage(e = "") {
                let t = this.getMessages();
                delete t[e], this.setMessages(t)
            }
            dispatchMessage(e, t) {
                this.storeMessage(e, t), this.sendMessage(t)
            }
            resendMsgs(e = [], t) {
                if (!e.length) return;
                let i = this.getMessages();
                e.forEach((e, o) => {
                    let n = { ...i[e] || {}
                    };
                    n.seqno && (n.seqno = "" + (parseInt(t) + o + 1), this.dispatchMessage(parseInt(n.seqno), n), this.removeMessage(e))
                })
            }
        }
        const w = (e, t) => {
                let i;
                return function() {
                    const o = this,
                        n = arguments;
                    clearTimeout(i), i = setTimeout(() => e.apply(o, n), t)
                }
            },
            k = () => {
                localStorage.removeItem(c), sessionStorage.removeItem(c), ["_zldp", "_zldt", "sid", "siq_ref", "siq_name", "siq_email", "siq_phone"].forEach(e => {
                    (0, p.f6)(l.brandName + "-" + e)
                })
            },
            z = (e, t = !0) => {
                let i = {};
                t ? (i.data = { ...e,
                    event_time: "" + (new Date).getTime()
                }, i.seqno = W.getIncSeqNumber()) : i = { ...e
                }, W.dispatcher.storeMessage(i.seqno, i), W.send(i, !1)
            },
            x = (e, t = "action") => {
                let i = {
                    opr: t,
                    type: "" + e.type
                };
                return ["triggered_id", "action_type"].forEach(t => {
                    e[t] && (i[t] = e[t])
                }), i
            },
            S = e => {
                let t = {},
                    i = {};
                return ["name", "email", "phone", "company", "cinfo", "firstname", "lastname", "salutation"].forEach(o => {
                    "company" === o ? i.company = e[o] : e[o] && (t[o] = e[o])
                }), t
            },
            I = (e, t) => {
                switch (e) {
                    case "notify_window_opened":
                        return void z(x(t));
                    case "notify_widget_clicked":
                        return (() => {
                            let e = {
                                data: x({
                                    type: "1"
                                }),
                                seqno: W.getIncSeqNumber()
                            };
                            z(e, !1)
                        })();
                    case "update_nav":
                        return (e => {
                            z({
                                opr: "nav",
                                navdata: e
                            })
                        })(t);
                    case "notify_custom_action":
                        return (e => {
                            z({
                                opr: "customaction",
                                ...e
                            })
                        })(t);
                    case "notify_custom_event":
                        return (e => {
                            z({
                                opr: "customevent",
                                data: e
                            })
                        })(t);
                    case "send_info":
                        return (e => {
                            let t = {
                                opr: "updateguessinfo",
                                detail: S(e)
                            };
                            e.company && (t.cinfo = JSON.stringify({
                                company: e.company
                            })), z(t)
                        })(t);
                    case "destroy":
                        return k();
                    case "update_info":
                        return (e => {
                            let t = {
                                data: {
                                    opr: "updateinfo",
                                    event_time: "" + (new Date).getTime(),
                                    ...S(e)
                                },
                                seqno: W.getIncSeqNumber()
                            };
                            z(t, !1)
                        })(t);
                    case "toggle_tracking_state":
                        return t ? W.reconnect() : W.destroy();
                    case "notify_triggered":
                        return void z(x({
                            triggered_id: (i = t).id,
                            action_type: i.type,
                            type: 7
                        }));
                    case "notify_campaign_identifier":
                        return void z({
                            opr: "identifybycampaign",
                            campaigndata: t
                        });
                    case "update_idle":
                        return void z({
                            opr: "status",
                            status: t ? "4" : "1"
                        });
                    default:
                        return
                }
                var i
            };
        var C = i(2615);
        const E = ["mousemove", "mousedown", "keypress", " DOMMouseScroll", "mousewheel", "touchmove", "MSPointerMove"],
            T = new class {
                constructor() {
                    this.isEventsAttached = !1, this.isTabFocused = !0, this.interval = void 0, this.lastActiveTime = this.getTime(), this.visibleStatus = "visible", this.isIdle = !1, this.idleTimer = void 0
                }
                init(e = () => {}) {
                    this.isEventsAttached || (this.eventCallback = e, this.addEvents(), this.resetInterval(), this.resetIdleTimer())
                }
                addEvents() {
                    if (this.isEventsAttached) return;
                    document.addEventListener("visibilitychange", () => this.handleChange("visible" === document.visibilityState));
                    let e = w(() => {
                        l.initRetracking && this.eventCallback(), this.resetInterval.bind(this)
                    }, 200);
                    E.forEach(t => {
                        document.addEventListener(t, e)
                    }), this.isEventsAttached = !0
                }
                handleChange(e) {
                    this.isTabFocused = e
                }
                startInterval() {
                    this.interval = setInterval(() => this.setInactiveState, l.inactivityTimeout)
                }
                resetInterval() {
                    clearInterval(this.interval), this.setActiveState()
                }
                setActiveState() {
                    this.lastActiveTime = this.getTime(), this.startInterval(), this.isIdle && (this.isIdle = !1, this.updateIdleState(), this.resetIdleTimer(), (0, C.L)("visitor.active", !0))
                }
                resetIdleTimer() {
                    clearTimeout(this.idleTimer), this.idleTimer = setTimeout(this.setIdleState.bind(this), l.idleTimeout)
                }
                setIdleState() {
                    this.isIdle = !0, this.updateIdleState(), (0, C.L)("visitor.idle", !0)
                }
                updateIdleState() {
                    I("update_idle", this.isIdle)
                }
                setInactiveState() {
                    this.getTime() - this.lastActiveTime > l.inactivityTimeout && this.toggleVisibleStatus()
                }
                toggleVisibleStatus(e = "hidden") {
                    this.visibleStatus = e
                }
                getTime() {
                    return Date.now()
                }
            },
            q = (e = {}) => {
                Object.entries(g(l.brandName)).forEach(([t, i]) => {
                    let o;
                    e.setExpiry && (o = {
                        _zldp: {
                            years: 2
                        },
                        _zldt: {
                            hours: 24
                        }
                    }), e[t] && (0, p.K5)(i, e[t], o ? o[t] : "")
                }), ((e = {}) => {
                    let t = new f;
                    ["sid", "_zldt"].forEach(i => {
                        e[i] && t.setValue(l.brandName + `-${i}`, e[i])
                    })
                })(e)
            };
        let D = new f;
        const L = new class {
            constructor() {
                this.queryParams = new URLSearchParams(window.location.search)
            }
            identify() {
                if (l.communicationHandler("isCampaignEnabled", "zc")) {
                    let e = this.getSearchParam("zc_cid");
                    e && this.zohoCampaignHandler(e)
                }
                if (l.communicationHandler("isCampaignEnabled", "mailchimp")) {
                    let e = this.getSearchParam("mc_cid"),
                        t = this.getSearchParam("mc_eid");
                    e && t && this.mailchimpHandler(e, t)
                }
            }
            zohoCampaignHandler(e) {
                let t = this.getZohoCampaignData();
                if (t) return l.communicationHandler("campaign_data", t);
                (0, p.K5)(`${l.lsid}_${e}`, e), this.send({
                    zc_cid: e
                })
            }
            mailchimpHandler(e, t) {
                let i = this.getMailChimpData();
                if (i) return l.communicationHandler("campaign_data", i);
                (0, p.K5)(`${l.lsid}_${e}`, e), (0, p.K5)(`${l.lsid}_${t}`, t), this.send({
                    mc_cid: e,
                    mc_eid: t
                })
            }
            storeZohoCampaignData(e) {
                (0, p.K5)(`${l.lsid}_zohocampaign`, e), l.communicationHandler("campaign_data", e)
            }
            getZohoCampaignData() {
                return (0, p.F_)(`${l.lsid}_zohocampaign`)
            }
            storeMailChimpData(e) {
                (0, p.K5)(`${l.lsid}_mailchimp`), l.communicationHandler("campaign_data", e)
            }
            getMailChimpData() {
                return (0, p.F_)(`${l.lsid}_mailchimp`)
            }
            getSearchParam(e) {
                return this.queryParams.get(e)
            }
            send(e) {
                I("notify_campaign_identifier", e), l.communicationHandler("campaign_identified", e)
            }
        };
        var O = i(1242);
        const $ = new class {
            parseTime(e) {
                let t = String(e).toLowerCase(),
                    i = parseInt(t);
                return t.includes("hour") ? 36e5 * i : t.includes("minute") ? 6e4 * i : t.includes("second") ? 1e3 * i : void 0
            }
            handle(e, t) {
                if (!e.value) return;
                let i = e.value = JSON.parse(e.value),
                    o = (t => t ? i.time ? parseInt(i.time - e.time) : 0 : this.parseTime(i.time) - e.time)("BOT_MESSAGE" === i.mode);
                e.value.time = o, (0, O.jl)(e, t, !0)
            }
        };
        let P = {};
        const N = (e = {}) => {
                e.triggers && ["token", "triggers"].forEach(t => {
                    let i = e[t];
                    i && (l[t] = i), "triggers" === t && $.handle(i)
                })
            },
            R = e => {
                let t = {};
                [{
                    key: "name"
                }, {
                    key: "email",
                    sub_key: "isemailset"
                }].forEach(i => {
                    let {
                        key: o,
                        sub_key: n = ""
                    } = i, a = e[o];
                    (a && e[n] || a && !i.hasOwnProperty("sub_key")) && (t[o] = a)
                })
            },
            A = () => {
                window.isReconnect = !1
            },
            M = () => {
                l.communicationHandler("updatequeued"), W.clearQueue()
            },
            j = e => {
                let t = e.data;
                if (!t) return;
                if ("," === t) o("reInitSocket");
                else if ("//1//" === t) return;
                let i, n = JSON.parse(t || "{}");
                i = Array.isArray(n) ? n[0] : n;
                let {
                    d: a,
                    o: r
                } = i;
                if (a) {
                    let {
                        firstname: e,
                        lastname: t
                    } = a;
                    e && (a.first_name = e, delete a.firstname), t && (a.last_name = t, delete a.lastname)
                }
                switch ("" + r) {
                    case "-1":
                        return o("updateConnectionStatus", "terminated"), o("terminateSocket");
                    case "0":
                        return A(), N(a), R(a), l.communicationHandler("utsupdate", a), M(), L.identify(), o("updateConnectionStatus", "connected");
                    case "1":
                        return A(), N(a), ((e = {}) => {
                            P = { ...P,
                                ...e
                            }
                        })(a), L.identify(), R(a), l.communicationHandler("utsupdate", a), M(), o("establishReconnection"), q(a);
                    case "2":
                        return A(), o("updateConnectionStatus", "connecting"), q({ ...a,
                            setExpiry: !0
                        }), l.communicationHandler("utsupdate", a), o("notify", {
                            operation: "ack"
                        });
                    case "3":
                    case "5":
                    case "6":
                    case "7":
                    case "10":
                    case "11":
                        return;
                    case "4":
                        return q(a), o("afterMsgAckReceived", a);
                    case "9":
                        return o("pingMe");
                    case "100":
                        return l.communicationHandler("proactivechat", a);
                    case "101":
                        return ((e = {}) => {
                            e.triggers ? N(e) : ((e = {}) => {
                                e.action && (["mailchimp", "zohocampaign", "edata"].forEach(t => {
                                    if (e.action === t) {
                                        let i = JSON.stringify({
                                            email: e.email,
                                            name: e.name
                                        });
                                        D.setValue(t, i)
                                    }
                                }), ["name", "email"].forEach(t => {
                                    e[t] && l.communicationHandler(t, e[t])
                                }))
                            })(e)
                        })(a);
                    case "102":
                        return N(a)
                }
            },
            W = new class extends r {
                constructor() {
                    super(), this.dbHelper = new f, this.dispatcher = new y(this.send.bind(this)), this.seqno = -1, this.socketParams = {}, this.isSocketOpened = !1, this.connectionStatus = "", this.interval = void 0, this.queue = [], this.initCustomEvents()
                }
                createSocketConnection(e, t = {}) {
                    this.socketParams = {
                        url: e,
                        params: t
                    }, this.initSocket()
                }
                initSocket() {
                    this.websocket && (this.socketParams.params = window._getSIQUTSParam()), this.websocket = new WebSocket(`wss://${this.socketParams.url}/watchws?${new URLSearchParams(this.socketParams.params).toString()}`), this.initEvents(), this.initNetworkEvents(), delete this.socketParams.params.recon
                }
                initEvents() {
                    [{
                        event: "onopen",
                        handler: e => this.onOpen(e)
                    }, {
                        event: "onmessage",
                        handler: j
                    }, {
                        event: "onclose",
                        handler: e => {
                            this.socketParams.params = { ...window._getSIQUTSParam()
                            }, this.socketParams.params.recon = !0
                        }
                    }].map(e => this.websocket[e.event] = e.handler)
                }
                initNetworkEvents(e = !0) {
                    ["offline", "online", "beforeunload"].forEach(t => {
                        window[e ? "addEventListener" : "removeEventListener"](t, e => this[`${t}Handler`](e))
                    })
                }
                onlineHandler(e) {
                    n("Came online."), this.reconnect()
                }
                offlineHandler(e) {
                    n("Went Offline.")
                }
                beforeunloadHandler(e) {
                    n("Tab closed or refreshed.")
                }
                onOpen(e) {
                    this.isSocketOpened || (this.isSocketOpened = !0, this.makeMeAlive(), this.afterOpen()), n("WebSocket opened successfully!")
                }
                afterOpen() {
                    T.init(() => {
                        "terminated" === this.connectionStatus && this.reconnect()
                    })
                }
                isSocketOpen() {
                    return this.websocket.OPEN === this.websocket.readyState
                }
                getSequenceNumber(e) {
                    return "object" == typeof e ? .data ? this.seqno + 1 : this.seqno
                }
                getIncSeqNumber() {
                    return this.seqno = parseInt(this.seqno) + 1, this.seqno
                }
                send(e, t = !0) {
                    if (!this.isSocketOpen()) return this.queue.push(e);
                    t && (this.seqno = this.getSequenceNumber(e)), this.websocket.send("string" == typeof e ? e : JSON.stringify(e))
                }
                clearQueue() {
                    this.queue.forEach(e => {
                        this.send(e)
                    }), this.queue = []
                }
                makeMeAlive() {
                    this.interval = setInterval(() => {
                        this.send({
                            data: "-",
                            seqno: this.seqno
                        })
                    }, 1e4)
                }
                reconnect() {
                    this.isSocketOpened && (window.isReconnect = !0, this.destroy(), this.initSocket())
                }
                onclose() {
                    this.websocket.close()
                }
                destroy() {
                    clearInterval(this.interval), this.isSocketOpened = !1, this.onclose(), this.initNetworkEvents(!1)
                }
                doLog(e, t) {
                    if (e.detail.msg) return;
                    let i = {
                        opr: "dolog",
                        logdata: e.detail.msg
                    };
                    this.dispatcher.dispatchMessage(null != t ? t : this.getSequenceNumber(), i)
                }
                getTime() {
                    return "" + (new Date).getTime()
                }
                initCustomEvents() {
                    [{
                        name: "reInitSocket",
                        func: this.reconnect
                    }, {
                        name: "sendMessage",
                        func: e => this.dispatcher.dispatchMessage(this.getSequenceNumber(e), e)
                    }, {
                        name: "updateConnectionStatus",
                        func: ({
                            detail: e
                        }) => {
                            ["connected"].includes(e) && this.doLog({
                                detail: {
                                    msg: ` isLocalstorage: true isSessionStorage: true Tab status - ${this.getTime()} - visible`
                                }
                            }, 0), this.connectionStatus = e
                        }
                    }, {
                        name: "pingMe",
                        func: () => {
                            clearInterval(this.interval), this.makeMeAlive()
                        }
                    }, {
                        name: "terminateSocket",
                        func: () => {
                            clearInterval(this.interval), this.onclose()
                        }
                    }, {
                        name: "afterMsgAckReceived",
                        func: e => {
                            let {
                                sseqno: t,
                                seqno: i,
                                missedseq: o
                            } = e.detail;
                            this.seqno = t || i || this.seqno, i ? this.dispatcher.afterMsgAckReceived({
                                detail: {
                                    sseqno: i
                                }
                            }) : t && (this.dispatcher.updateSequenceNumber(t), o && this.dispatcher.resendMsgs(JSON.parse(o), parseInt(t)))
                        }
                    }, {
                        name: "notify",
                        func: e => this.dispatcher.notify(e.detail || {})
                    }, {
                        name: "establishReconnection",
                        func: e => {
                            this.dispatcher.dispatchMessage(this.getSequenceNumber(), "--1--")
                        }
                    }, {
                        name: "dolog",
                        func: this.doLog
                    }].forEach(e => window.addEventListener(e.name, e.func))
                }
            };
        class Z {
            constructor(e = "", t = {}) {
                if (!e) return n("Domain not sent from UTS.js");
                W.createSocketConnection(e, t)
            }
            connect() {}
        }
        const F = ["siq_name", "siq_email", "siq_ename", "siq_eemail"],
            H = ["name", "email"],
            U = new class extends f {
                constructor() {
                    super(), this.queryParams = new URLSearchParams(window.location.search), this.setOnCookie()
                }
                getReplacedKey(e) {
                    return e.replace("siq_", "").replace("siq_e", "")
                }
                getParams() {
                    let e = {};
                    return F.forEach(t => {
                        let i = this.getSearchParam(t);
                        i && (e[this.getReplacedKey(t)] = i)
                    }), e
                }
                getQueryParams() {
                    return this.queryParams.size ? this.getParams() : this.getFromCookie()
                }
                setOnCookie() {
                    let e = this.getParams();
                    Object.keys(e).forEach(t => {
                        H.forEach(i => {
                            t.includes(i) && (0, p.K5)(`${l.lsid}_${i}`, e[i])
                        })
                    })
                }
                getFromCookie() {
                    let e = {};
                    return H.forEach(t => {
                        let i = (0, p.F_)(`${l.lsid}_${t}`);
                        i && (e[t] = i)
                    }), e
                }
                getSearchParam(e) {
                    return this.queryParams.get(e)
                }
            };
        let B = () => (new Date).toTimeString().split(" ") || [];
        class V extends f {
            constructor(e) {
                super(), e.notify = I, Object.entries(e).forEach(([e, t]) => t && (this[e] = t)), this.params = {
                    "x-e": this.brandName,
                    "x-s": this.portalName,
                    resolution: window.screen.width + "x" + window.screen.height,
                    lang_embed: this.embedLang
                }, window.isReconnect && (this.params.recon = !0), ["name", "email", "cinfo", "phone", "ptitle", "cpage", "referer", "lsid", "widget_code", "cdnuts", "e_email", "e_name", "firstname", "lastname", "salutation"].forEach(t => {
                    e[t] && (this.params[t] = e[t])
                }), this.initParams(), window._getSIQUTSParam = this.getParams.bind(this)
            }
            initParams() {
                this.setParams()
            }
            getParams() {
                return ["con_id", "connection_count", "referer", "_zldp", "_zldt", "localtime", "gmttime", "x-sid", "idleTime"].forEach(e => {
                    switch (e) {
                        case "con_id":
                            return this.setConnectionId();
                        case "connection_count":
                            return this.setConnectionCount();
                        case "referer":
                            return this.setReferrer();
                        case "_zldp":
                            return this.setZlParam("_zldp");
                        case "_zldt":
                            return this.setZlParam("_zldt");
                        case "localtime":
                            return this.params[e] = (() => {
                                let e = B();
                                return e.length ? e.slice(1) ? .join(" ") : ""
                            })();
                        case "gmttime":
                            return this.params[e] = (() => {
                                let e = B();
                                return e.length ? e.slice(1, 2) ? .[0] : ""
                            })();
                        case "x-sid":
                            let t = this.getValue(`${this.brandName}-sid`);
                            return t && (this.params[e] = t);
                        case "idleTime":
                            return this.updateIdleTime(this.idleTime)
                    }
                }), Object.keys(l).forEach(e => {
                    this[e] && (l[e] = this[e])
                }), this.params
            }
            setParams() {
                this.getParams(), this.params = { ...this.params,
                    ...U.getQueryParams()
                }, this.connection = new Z(this.vtsDomain, this.params)
            }
            setConnectionId() {
                let e = "con_id",
                    t = this.getValue(e);
                t || (t = (new Date).getTime(), this.setValue(e, t)), this.params[e] = t
            }
            setConnectionCount() {
                let e = "connection_count",
                    t = this.getValue(e);
                t = t ? parseInt(t) + 1 : 1, this.setValue(e, t), this.params[e] = t
            }
            setReferrer() {
                this.referer && (this.params.docref = this.referer)
            }
            setZlParam(e) {
                let t = (0, p.F_)(`${this.brandName}-${e}`);
                t && (this.params[e] = t)
            }
            handleTrigger(e) {
                $.handle(e, !0)
            }
            updateIdleTime(e) {
                l.idleTimeout = 60 * (e || 15) * 1e3, T.resetIdleTimer()
            }
            disable() {
                W.destroy()
            }
            destroy() {
                k()
            }
        }
    }, 1919: (e, t, i) => {
        "use strict";
        i.d(t, {
            F_: () => n,
            K5: () => r,
            f6: () => a
        }), i(1181);
        var o = i(2220);
        const n = e => new Map(document.cookie.split("; ").map(e => e.split(/=(.*)/s))).get(e),
            a = e => {
                r(e, "", -1)
            },
            r = (e, t, i) => {
                let n = -1 === i ? i : "";
                i && !n && (n = s(i));
                let a = `${e}=${t};`,
                    r = o.visitorInfo.trackingdomain;
                if (r && (a = `${a}path=/;domain=${r};`), n) {
                    let e = new Date;
                    e.setTime(e.getTime() + n), a += `expires=${e.toGMTString()};`
                }
                document.cookie = a
            },
            s = (e = {}) => {
                if (!Object.keys(e).length) return;
                let {
                    years: t = 0,
                    days: i = 1,
                    hours: o = 1,
                    minutes: n = 60,
                    seconds: a = 60,
                    milliseconds: r = 1e3
                } = e;
                return t ? (i = 365, o = 24) : t = 1, Date.now() + t * i * o * n * a * r
            }
    }, 8364: (e, t, i) => {
        "use strict";
        i.d(t, {
            E: () => o
        });
        let o = ({
            seasonal_theme: e = {}
        }) => {
            let t = new Date;
            t.setHours(0, 0, 0, 0);
            let i = t.getTime(),
                o = {};
            if (e.enabled && e.themes) {
                let {
                    start_time: t = "",
                    expiry_time: n = "",
                    type: a
                } = e.themes.filter(e => e.enabled)[0] || {};
                n && parseInt(t) <= i && parseInt(n) >= i && (o["seasonal-type"] = a)
            }
            return o
        }
    }, 2717: (e, t, i) => {
        "use strict";
        i.d(t, {
            ry: () => d,
            yZ: () => r,
            ZP: () => m
        }), i(1181);
        const o = ["en", "ar", "da", "de", "el", "es", "fr", "ga", "he", "hu", "it", "iw", "ja", "ko", "nb", "nl", "pl", "pt", "pt_PT", "ro", "ru", "sv", "th", "tr", "zh", "hr", "cs", "sk", "sl", "vi", "hy", "ka", "pt_PT", "zh_TW", "bg", "fa_IR", "fi", "pa", "hi", "or", "gu", "kn", "ml", "mr", "ta", "te", "bn", "kk", "uz", "id", "ca", "fil", "uk"],
            n = {
                zh_hant: "zh_TW",
                zh_tw: "zh_TW"
            },
            a = navigator,
            r = a.userAgent.toLowerCase(),
            s = (/(wince|win|mac|linux|sunos|solaris|iphone|ipad|ipod|nokia|webos|blackberry|s60|widerweb|symbian|nintendo|bada|kfot|playstation)/.exec(a.platform.toLowerCase()) || [])[0].replace("sunos", "solaris").replace("widerweb", "motorola").replace("s60", "symbian").replace("kfot", "kindle"),
            l = /edg/.test(r) ? "edge" : /firefox|fxios/.test(r) ? "firefox" : /opr/.test(r) ? "opera" : /chrome|chromium|crios/.test(r) ? "chrome" : /safari/.test(r) ? "safari" : "",
            c = (/(chrome)[ \/]([\w.]+)/.exec(r) || /(webkit)[ \/]([\w.]+)/.exec(r) || /(opera)(?:.*version|)[ \/]([\w.]+)/.exec(r) || /(msie) ([\w.]+)/.exec(r) || r.indexOf("compatible") < 0 && /(mozilla)(?:.*? rv:([\w.]+)|)/.exec(r) || [])[2] || "0",
            d = !!/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB10|IEMobile|Opera Mini/i.test(r) || window.IS_MOBILE_PREVIEW,
            m = (/Android/i.test(r) || /|iPhone|iPad|iPod/i.test(r), (() => {
                const e = {
                    cookie_enabled: a.cookieEnabled,
                    java_enabled: !1,
                    color_code: screen.colorDepth + "",
                    platform: a.platform,
                    local_time_zone: (new Date).toTimeString().split(" ").slice(1).join(" ")
                };
                return {
                    getInfo: () => {
                        let t = window.parent.document,
                            i = window.screen;
                        return { ...e,
                            screen: i.width + " * " + i.height,
                            page_title: t.title,
                            current_page: t.location.href,
                            referrer: t.referrer
                        }
                    },
                    getFullInfo() {
                        return { ...this.getInfo(),
                            version: c,
                            os: s,
                            browser_name: l || "other"
                        }
                    },
                    getLanguage: e => {
                        let t = e || document.documentElement.lang || "",
                            i = a.language || a.userLanguage || "";
                        for (let e of [t, i]) {
                            if (!e || "zxx" === e) continue;
                            e = e.replace(/-/g, "_");
                            let t = e.split("_")[0].toLowerCase();
                            return o.includes(e) ? e : n[e] ? n[e] : o.includes(t) ? t : "en"
                        }
                    }
                }
            })())
    }, 8321: (e, t, i) => {
        "use strict";
        i.d(t, {
            Z: () => o
        });
        const o = e => (Array.isArray(e) && (e = e.join(",")), e && (e = (e = e.replace(/&#39;|&#x27;/g, "'").replace(/&quot;/g, '"').replace(/&gt;/g, ">").replace(/&lt;/g, "<").replace(/&amp;/g, "&").replace(/&#x2F;/g, "/")).replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#x27;").replace(/\//g, "&#x2F;")), e)
    }, 4963: e => {
        e.exports = function(e) {
            if ("function" != typeof e) throw TypeError(e + " is not a function!");
            return e
        }
    }, 7722: (e, t, i) => {
        var o = i(6314)("unscopables"),
            n = Array.prototype;
        null == n[o] && i(7728)(n, o, {}), e.exports = function(e) {
            n[o][e] = !0
        }
    }, 7007: (e, t, i) => {
        var o = i(5286);
        e.exports = function(e) {
            if (!o(e)) throw TypeError(e + " is not an object!");
            return e
        }
    }, 9315: (e, t, i) => {
        var o = i(2110),
            n = i(875),
            a = i(2337);
        e.exports = function(e) {
            return function(t, i, r) {
                var s, l = o(t),
                    c = n(l.length),
                    d = a(r, c);
                if (e && i != i) {
                    for (; c > d;)
                        if ((s = l[d++]) != s) return !0
                } else
                    for (; c > d; d++)
                        if ((e || d in l) && l[d] === i) return e || d || 0;
                return !e && -1
            }
        }
    }, 2032: e => {
        var t = {}.toString;
        e.exports = function(e) {
            return t.call(e).slice(8, -1)
        }
    }, 5645: e => {
        var t = e.exports = {
            version: "2.6.12"
        };
        "number" == typeof __e && (__e = t)
    }, 741: (e, t, i) => {
        var o = i(4963);
        e.exports = function(e, t, i) {
            if (o(e), void 0 === t) return e;
            switch (i) {
                case 1:
                    return function(i) {
                        return e.call(t, i)
                    };
                case 2:
                    return function(i, o) {
                        return e.call(t, i, o)
                    };
                case 3:
                    return function(i, o, n) {
                        return e.call(t, i, o, n)
                    }
            }
            return function() {
                return e.apply(t, arguments)
            }
        }
    }, 1355: e => {
        e.exports = function(e) {
            if (null == e) throw TypeError("Can't call method on  " + e);
            return e
        }
    }, 7057: (e, t, i) => {
        e.exports = !i(4253)(function() {
            return 7 != Object.defineProperty({}, "a", {
                get: function() {
                    return 7
                }
            }).a
        })
    }, 2457: (e, t, i) => {
        var o = i(5286),
            n = i(3816).document,
            a = o(n) && o(n.createElement);
        e.exports = function(e) {
            return a ? n.createElement(e) : {}
        }
    }, 4430: e => {
        e.exports = "constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")
    }, 2985: (e, t, i) => {
        var o = i(3816),
            n = i(5645),
            a = i(7728),
            r = i(3415),
            s = i(741),
            l = "prototype",
            c = function(e, t, i) {
                var d, m, h, p, u = e & c.F,
                    f = e & c.G,
                    g = e & c.S,
                    v = e & c.P,
                    b = e & c.B,
                    _ = f ? o : g ? o[t] || (o[t] = {}) : (o[t] || {})[l],
                    y = f ? n : n[t] || (n[t] = {}),
                    w = y[l] || (y[l] = {});
                for (d in f && (i = t), i) h = ((m = !u && _ && void 0 !== _[d]) ? _ : i)[d], p = b && m ? s(h, o) : v && "function" == typeof h ? s(Function.call, h) : h, _ && r(_, d, h, e & c.U), y[d] != h && a(y, d, p), v && w[d] != h && (w[d] = h)
            };
        o.core = n, c.F = 1, c.G = 2, c.S = 4, c.P = 8, c.B = 16, c.W = 32, c.U = 64, c.R = 128, e.exports = c
    }, 4253: e => {
        e.exports = function(e) {
            try {
                return !!e()
            } catch (e) {
                return !0
            }
        }
    }, 18: (e, t, i) => {
        e.exports = i(3825)("native-function-to-string", Function.toString)
    }, 3816: e => {
        var t = e.exports = "undefined" != typeof window && window.Math == Math ? window : "undefined" != typeof self && self.Math == Math ? self : Function("return this")();
        "number" == typeof __g && (__g = t)
    }, 9181: e => {
        var t = {}.hasOwnProperty;
        e.exports = function(e, i) {
            return t.call(e, i)
        }
    }, 7728: (e, t, i) => {
        var o = i(9275),
            n = i(681);
        e.exports = i(7057) ? function(e, t, i) {
            return o.f(e, t, n(1, i))
        } : function(e, t, i) {
            return e[t] = i, e
        }
    }, 639: (e, t, i) => {
        var o = i(3816).document;
        e.exports = o && o.documentElement
    }, 1734: (e, t, i) => {
        e.exports = !i(7057) && !i(4253)(function() {
            return 7 != Object.defineProperty(i(2457)("div"), "a", {
                get: function() {
                    return 7
                }
            }).a
        })
    }, 9797: (e, t, i) => {
        var o = i(2032);
        e.exports = Object("z").propertyIsEnumerable(0) ? Object : function(e) {
            return "String" == o(e) ? e.split("") : Object(e)
        }
    }, 5286: e => {
        e.exports = function(e) {
            return "object" == typeof e ? null !== e : "function" == typeof e
        }
    }, 9988: (e, t, i) => {
        "use strict";
        var o = i(2503),
            n = i(681),
            a = i(2943),
            r = {};
        i(7728)(r, i(6314)("iterator"), function() {
            return this
        }), e.exports = function(e, t, i) {
            e.prototype = o(r, {
                next: n(1, i)
            }), a(e, t + " Iterator")
        }
    }, 2923: (e, t, i) => {
        "use strict";
        var o = i(4461),
            n = i(2985),
            a = i(3415),
            r = i(7728),
            s = i(7234),
            l = i(9988),
            c = i(2943),
            d = i(468),
            m = i(6314)("iterator"),
            h = !([].keys && "next" in [].keys()),
            p = "keys",
            u = "values",
            f = function() {
                return this
            };
        e.exports = function(e, t, i, g, v, b, _) {
            l(i, t, g);
            var y, w, k, z = function(e) {
                    if (!h && e in C) return C[e];
                    switch (e) {
                        case p:
                        case u:
                            return function() {
                                return new i(this, e)
                            }
                    }
                    return function() {
                        return new i(this, e)
                    }
                },
                x = t + " Iterator",
                S = v == u,
                I = !1,
                C = e.prototype,
                E = C[m] || C["@@iterator"] || v && C[v],
                T = E || z(v),
                q = v ? S ? z("entries") : T : void 0,
                D = "Array" == t && C.entries || E;
            if (D && (k = d(D.call(new e))) !== Object.prototype && k.next && (c(k, x, !0), o || "function" == typeof k[m] || r(k, m, f)), S && E && E.name !== u && (I = !0, T = function() {
                    return E.call(this)
                }), o && !_ || !h && !I && C[m] || r(C, m, T), s[t] = T, s[x] = f, v)
                if (y = {
                        values: S ? T : z(u),
                        keys: b ? T : z(p),
                        entries: q
                    }, _)
                    for (w in y) w in C || a(C, w, y[w]);
                else n(n.P + n.F * (h || I), t, y);
            return y
        }
    }, 5436: e => {
        e.exports = function(e, t) {
            return {
                value: t,
                done: !!e
            }
        }
    }, 7234: e => {
        e.exports = {}
    }, 4461: e => {
        e.exports = !1
    }, 2503: (e, t, i) => {
        var o = i(7007),
            n = i(5588),
            a = i(4430),
            r = i(9335)("IE_PROTO"),
            s = function() {},
            l = "prototype",
            c = function() {
                var e, t = i(2457)("iframe"),
                    o = a.length;
                for (t.style.display = "none", i(639).appendChild(t), t.src = "javascript:", (e = t.contentWindow.document).open(), e.write("<script>document.F=Object<\/script>"), e.close(), c = e.F; o--;) delete c[l][a[o]];
                return c()
            };
        e.exports = Object.create || function(e, t) {
            var i;
            return null !== e ? (s[l] = o(e), i = new s, s[l] = null, i[r] = e) : i = c(), void 0 === t ? i : n(i, t)
        }
    }, 9275: (e, t, i) => {
        var o = i(7007),
            n = i(1734),
            a = i(1689),
            r = Object.defineProperty;
        t.f = i(7057) ? Object.defineProperty : function(e, t, i) {
            if (o(e), t = a(t, !0), o(i), n) try {
                return r(e, t, i)
            } catch (e) {}
            if ("get" in i || "set" in i) throw TypeError("Accessors not supported!");
            return "value" in i && (e[t] = i.value), e
        }
    }, 5588: (e, t, i) => {
        var o = i(9275),
            n = i(7007),
            a = i(7184);
        e.exports = i(7057) ? Object.defineProperties : function(e, t) {
            n(e);
            for (var i, r = a(t), s = r.length, l = 0; s > l;) o.f(e, i = r[l++], t[i]);
            return e
        }
    }, 468: (e, t, i) => {
        var o = i(9181),
            n = i(508),
            a = i(9335)("IE_PROTO"),
            r = Object.prototype;
        e.exports = Object.getPrototypeOf || function(e) {
            return e = n(e), o(e, a) ? e[a] : "function" == typeof e.constructor && e instanceof e.constructor ? e.constructor.prototype : e instanceof Object ? r : null
        }
    }, 189: (e, t, i) => {
        var o = i(9181),
            n = i(2110),
            a = i(9315)(!1),
            r = i(9335)("IE_PROTO");
        e.exports = function(e, t) {
            var i, s = n(e),
                l = 0,
                c = [];
            for (i in s) i != r && o(s, i) && c.push(i);
            for (; t.length > l;) o(s, i = t[l++]) && (~a(c, i) || c.push(i));
            return c
        }
    }, 7184: (e, t, i) => {
        var o = i(189),
            n = i(4430);
        e.exports = Object.keys || function(e) {
            return o(e, n)
        }
    }, 681: e => {
        e.exports = function(e, t) {
            return {
                enumerable: !(1 & e),
                configurable: !(2 & e),
                writable: !(4 & e),
                value: t
            }
        }
    }, 3415: (e, t, i) => {
        var o = i(3816),
            n = i(7728),
            a = i(9181),
            r = i(3953)("src"),
            s = i(18),
            l = "toString",
            c = ("" + s).split(l);
        i(5645).inspectSource = function(e) {
            return s.call(e)
        }, (e.exports = function(e, t, i, s) {
            var l = "function" == typeof i;
            l && (a(i, "name") || n(i, "name", t)), e[t] !== i && (l && (a(i, r) || n(i, r, e[t] ? "" + e[t] : c.join(String(t)))), e === o ? e[t] = i : s ? e[t] ? e[t] = i : n(e, t, i) : (delete e[t], n(e, t, i)))
        })(Function.prototype, l, function() {
            return "function" == typeof this && this[r] || s.call(this)
        })
    }, 2943: (e, t, i) => {
        var o = i(9275).f,
            n = i(9181),
            a = i(6314)("toStringTag");
        e.exports = function(e, t, i) {
            e && !n(e = i ? e : e.prototype, a) && o(e, a, {
                configurable: !0,
                value: t
            })
        }
    }, 9335: (e, t, i) => {
        var o = i(3825)("keys"),
            n = i(3953);
        e.exports = function(e) {
            return o[e] || (o[e] = n(e))
        }
    }, 3825: (e, t, i) => {
        var o = i(5645),
            n = i(3816),
            a = "__core-js_shared__",
            r = n[a] || (n[a] = {});
        (e.exports = function(e, t) {
            return r[e] || (r[e] = void 0 !== t ? t : {})
        })("versions", []).push({
            version: o.version,
            mode: i(4461) ? "pure" : "global",
            copyright: "© 2020 Denis Pushkarev (zloirock.ru)"
        })
    }, 2337: (e, t, i) => {
        var o = i(1467),
            n = Math.max,
            a = Math.min;
        e.exports = function(e, t) {
            return (e = o(e)) < 0 ? n(e + t, 0) : a(e, t)
        }
    }, 1467: e => {
        var t = Math.ceil,
            i = Math.floor;
        e.exports = function(e) {
            return isNaN(e = +e) ? 0 : (e > 0 ? i : t)(e)
        }
    }, 2110: (e, t, i) => {
        var o = i(9797),
            n = i(1355);
        e.exports = function(e) {
            return o(n(e))
        }
    }, 875: (e, t, i) => {
        var o = i(1467),
            n = Math.min;
        e.exports = function(e) {
            return e > 0 ? n(o(e), 9007199254740991) : 0
        }
    }, 508: (e, t, i) => {
        var o = i(1355);
        e.exports = function(e) {
            return Object(o(e))
        }
    }, 1689: (e, t, i) => {
        var o = i(5286);
        e.exports = function(e, t) {
            if (!o(e)) return e;
            var i, n;
            if (t && "function" == typeof(i = e.toString) && !o(n = i.call(e))) return n;
            if ("function" == typeof(i = e.valueOf) && !o(n = i.call(e))) return n;
            if (!t && "function" == typeof(i = e.toString) && !o(n = i.call(e))) return n;
            throw TypeError("Can't convert object to primitive value")
        }
    }, 3953: e => {
        var t = 0,
            i = Math.random();
        e.exports = function(e) {
            return "Symbol(".concat(void 0 === e ? "" : e, ")_", (++t + i).toString(36))
        }
    }, 6314: (e, t, i) => {
        var o = i(3825)("wks"),
            n = i(3953),
            a = i(3816).Symbol,
            r = "function" == typeof a;
        (e.exports = function(e) {
            return o[e] || (o[e] = r && a[e] || (r ? a : n)("Symbol." + e))
        }).store = o
    }, 6997: (e, t, i) => {
        "use strict";
        var o = i(7722),
            n = i(5436),
            a = i(7234),
            r = i(2110);
        e.exports = i(2923)(Array, "Array", function(e, t) {
            this._t = r(e), this._i = 0, this._k = t
        }, function() {
            var e = this._t,
                t = this._k,
                i = this._i++;
            return !e || i >= e.length ? (this._t = void 0, n(1)) : n(0, "keys" == t ? i : "values" == t ? e[i] : [i, e[i]])
        }, "values"), a.Arguments = a.Array, o("keys"), o("values"), o("entries")
    }, 1181: (e, t, i) => {
        for (var o = i(6997), n = i(7184), a = i(3415), r = i(3816), s = i(7728), l = i(7234), c = i(6314), d = c("iterator"), m = c("toStringTag"), h = l.Array, p = {
                CSSRuleList: !0,
                CSSStyleDeclaration: !1,
                CSSValueList: !1,
                ClientRectList: !1,
                DOMRectList: !1,
                DOMStringList: !1,
                DOMTokenList: !0,
                DataTransferItemList: !1,
                FileList: !1,
                HTMLAllCollection: !1,
                HTMLCollection: !1,
                HTMLFormElement: !1,
                HTMLSelectElement: !1,
                MediaList: !0,
                MimeTypeArray: !1,
                NamedNodeMap: !1,
                NodeList: !0,
                PaintRequestList: !1,
                Plugin: !1,
                PluginArray: !1,
                SVGLengthList: !1,
                SVGNumberList: !1,
                SVGPathSegList: !1,
                SVGPointList: !1,
                SVGStringList: !1,
                SVGTransformList: !1,
                SourceBufferList: !1,
                StyleSheetList: !0,
                TextTrackCueList: !1,
                TextTrackList: !1,
                TouchList: !1
            }, u = n(p), f = 0; f < u.length; f++) {
            var g, v = u[f],
                b = p[v],
                _ = r[v],
                y = _ && _.prototype;
            if (y && (y[d] || s(y, d, h), y[m] || s(y, m, v), l[v] = h, b))
                for (g in o) y[g] || a(y, g, o[g], !0)
        }
    }, 3639: () => {}, 185: () => {}, 2888: () => {}, 6655: () => {}, 6605: () => {}, 4809: () => {}, 3379: e => {
        "use strict";
        var t = [];

        function i(e) {
            for (var i = -1, o = 0; o < t.length; o++)
                if (t[o].identifier === e) {
                    i = o;
                    break
                }
            return i
        }

        function o(e, o) {
            for (var a = {}, r = [], s = 0; s < e.length; s++) {
                var l = e[s],
                    c = o.base ? l[0] + o.base : l[0],
                    d = a[c] || 0,
                    m = "".concat(c, " ").concat(d);
                a[c] = d + 1;
                var h = i(m),
                    p = {
                        css: l[1],
                        media: l[2],
                        sourceMap: l[3],
                        supports: l[4],
                        layer: l[5]
                    };
                if (-1 !== h) t[h].references++, t[h].updater(p);
                else {
                    var u = n(p, o);
                    o.byIndex = s, t.splice(s, 0, {
                        identifier: m,
                        updater: u,
                        references: 1
                    })
                }
                r.push(m)
            }
            return r
        }

        function n(e, t) {
            var i = t.domAPI(t);
            return i.update(e),
                function(t) {
                    if (t) {
                        if (t.css === e.css && t.media === e.media && t.sourceMap === e.sourceMap && t.supports === e.supports && t.layer === e.layer) return;
                        i.update(e = t)
                    } else i.remove()
                }
        }
        e.exports = function(e, n) {
            var a = o(e = e || [], n = n || {});
            return function(e) {
                e = e || [];
                for (var r = 0; r < a.length; r++) {
                    var s = i(a[r]);
                    t[s].references--
                }
                for (var l = o(e, n), c = 0; c < a.length; c++) {
                    var d = i(a[c]);
                    0 === t[d].references && (t[d].updater(), t.splice(d, 1))
                }
                a = l
            }
        }
    }, 569: e => {
        "use strict";
        var t = {};
        e.exports = function(e, i) {
            var o = function(e) {
                if (void 0 === t[e]) {
                    var i = document.querySelector(e);
                    if (window.HTMLIFrameElement && i instanceof window.HTMLIFrameElement) try {
                        i = i.contentDocument.head
                    } catch (e) {
                        i = null
                    }
                    t[e] = i
                }
                return t[e]
            }(e);
            if (!o) throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");
            o.appendChild(i)
        }
    }, 9216: e => {
        "use strict";
        e.exports = function(e) {
            var t = document.createElement("style");
            return e.setAttributes(t, e.attributes), e.insert(t, e.options), t
        }
    }, 3565: (e, t, i) => {
        "use strict";
        e.exports = function(e) {
            var t = i.nc;
            t && e.setAttribute("nonce", t)
        }
    }, 7795: e => {
        "use strict";
        e.exports = function(e) {
            if ("undefined" == typeof document) return {
                update: function() {},
                remove: function() {}
            };
            var t = e.insertStyleElement(e);
            return {
                update: function(i) {
                    ! function(e, t, i) {
                        var o = "";
                        i.supports && (o += "@supports (".concat(i.supports, ") {")), i.media && (o += "@media ".concat(i.media, " {"));
                        var n = void 0 !== i.layer;
                        n && (o += "@layer".concat(i.layer.length > 0 ? " ".concat(i.layer) : "", " {")), o += i.css, n && (o += "}"), i.media && (o += "}"), i.supports && (o += "}");
                        var a = i.sourceMap;
                        a && "undefined" != typeof btoa && (o += "\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a)))), " */")), t.styleTagTransform(o, e, t.options)
                    }(t, e, i)
                },
                remove: function() {
                    ! function(e) {
                        if (null === e.parentNode) return !1;
                        e.parentNode.removeChild(e)
                    }(t)
                }
            }
        }
    }, 4589: e => {
        "use strict";
        e.exports = function(e, t) {
            if (t.styleSheet) t.styleSheet.cssText = e;
            else {
                for (; t.firstChild;) t.removeChild(t.firstChild);
                t.appendChild(document.createTextNode(e))
            }
        }
    }
}, o = {};

function n(e) {
    var t = o[e];
    if (void 0 !== t) return t.exports;
    var a = o[e] = {
        exports: {}
    };
    return i[e](a, a.exports, n), a.exports
}
n.m = i, n.n = e => {
var t = e && e.__esModule ? () => e.default : () => e;
return n.d(t, {
    a: t
}), t
}, n.d = (e, t) => {
for (var i in t) n.o(t, i) && !n.o(e, i) && Object.defineProperty(e, i, {
    enumerable: !0,
    get: t[i]
})
}, n.f = {}, n.e = e => Promise.all(Object.keys(n.f).reduce((t, i) => (n.f[i](e, t), t), [])), n.u = e => "js/" + ({
93: "vts",
192: "float-media",
720: "cookieHandler",
983: "float-dummy"
}[e] || e) + "~modern." + {
93: "04fe3f24871d1c2bbe69",
192: "KDkeX2IGLjQMDOub_UFb6sUcfNQdyA2fUTrIZjXWTbTqH1wHnpD6uvuVFxTPD7rU",
337: "1adb6de12a3ec886573d",
720: "2e1b80ebacca9161afd4",
983: "Icq73p1DfcHT-omC7CQJOc5MM87PSxS6CKbChfco73dmG7N3UToUkWaOznziMXhP"
}[e] + ".js", n.miniCssF = e => "css/" + {
192: "float-media",
983: "float-dummy"
}[e] + "." + {
192: "4D2I6c4KZpMj8DRN3F1gwko1RFfDGaKcTylBpSaQ6jeN-wbVFZ9cL4JcFElUbG6L",
983: "AOAdKIwQ2sY_ZQyZ8ceUVII-to7gyromlLT7tsLYxfjbsx4NM4ZZckN9_fCRTWGp"
}[e] + ".css", n.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t), e = {}, t = "float:", n.l = (i, o, a, r) => {
if (e[i]) e[i].push(o);
else {
    var s, l;
    if (void 0 !== a)
        for (var c = document.getElementsByTagName("script"), d = 0; d < c.length; d++) {
            var m = c[d];
            if (m.getAttribute("src") == i || m.getAttribute("data-webpack") == t + a) {
                s = m;
                break
            }
        }
    s || (l = !0, (s = document.createElement("script")).charset = "utf-8", s.timeout = 120, n.nc && s.setAttribute("nonce", n.nc), s.setAttribute("data-webpack", t + a), s.src = i), e[i] = [o];
    var h = (t, o) => {
            s.onerror = s.onload = null, clearTimeout(p);
            var n = e[i];
            if (delete e[i], s.parentNode && s.parentNode.removeChild(s), n && n.forEach(e => e(o)), t) return t(o)
        },
        p = setTimeout(h.bind(null, void 0, {
            type: "timeout",
            target: s
        }), 12e4);
    s.onerror = h.bind(null, s.onerror), s.onload = h.bind(null, s.onload), l && document.head.appendChild(s)
}
}, n.r = e => {
"undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
    value: "Module"
}), Object.defineProperty(e, "__esModule", {
    value: !0
})
}, n.p = _STATIC_URL + "/salesiq/RESOURCE_BUNDLES/embedfloat/", (() => {
if ("undefined" != typeof document) {
    var e = {
        787: 0,
        93: 0,
        337: 0,
        720: 0
    };
    n.f.miniCss = (t, i) => {
        e[t] ? i.push(e[t]) : 0 !== e[t] && {
            192: 1,
            983: 1
        }[t] && i.push(e[t] = (e => new Promise((t, i) => {
            var o = n.miniCssF(e),
                a = n.p + o;
            if (((e, t) => {
                    for (var i = document.getElementsByTagName("link"), o = 0; o < i.length; o++) {
                        var n = (r = i[o]).getAttribute("data-href") || r.getAttribute("href");
                        if ("stylesheet" === r.rel && (n === e || n === t)) return r
                    }
                    var a = document.getElementsByTagName("style");
                    for (o = 0; o < a.length; o++) {
                        var r;
                        if ((n = (r = a[o]).getAttribute("data-href")) === e || n === t) return r
                    }
                })(o, a)) return t();
            ((e, t, i, o, a) => {
                var r = document.createElement("link");
                r.rel = "stylesheet", r.type = "text/css", n.nc && (r.nonce = n.nc), r.onerror = r.onload = i => {
                    if (r.onerror = r.onload = null, "load" === i.type) o();
                    else {
                        var n = i && i.type,
                            s = i && i.target && i.target.href || t,
                            l = new Error("Loading CSS chunk " + e + " failed.\n(" + n + ": " + s + ")");
                        l.name = "ChunkLoadError", l.code = "CSS_CHUNK_LOAD_FAILED", l.type = n, l.request = s, r.parentNode && r.parentNode.removeChild(r), a(l)
                    }
                }, r.href = t, document.head.appendChild(r)
            })(e, a, 0, t, i)
        }))(t).then(() => {
            e[t] = 0
        }, i => {
            throw delete e[t], i
        }))
    }
}
})(), (() => {
var e = {
    787: 0,
    93: 0,
    337: 0,
    720: 0
};
n.f.j = (t, i) => {
    var o = n.o(e, t) ? e[t] : void 0;
    if (0 !== o)
        if (o) i.push(o[2]);
        else {
            var a = new Promise((i, n) => o = e[t] = [i, n]);
            i.push(o[2] = a);
            var r = n.p + n.u(t),
                s = new Error;
            n.l(r, i => {
                if (n.o(e, t) && (0 !== (o = e[t]) && (e[t] = void 0), o)) {
                    var a = i && ("load" === i.type ? "missing" : i.type),
                        r = i && i.target && i.target.src;
                    s.message = "Loading chunk " + t + " failed.\n(" + a + ": " + r + ")", s.name = "ChunkLoadError", s.type = a, s.request = r, o[1](s)
                }
            }, "chunk-" + t, t)
        }
};
var t = (t, i) => {
        var o, a, [r, s, l] = i,
            c = 0;
        if (r.some(t => 0 !== e[t])) {
            for (o in s) n.o(s, o) && (n.m[o] = s[o]);
            l && l(n)
        }
        for (t && t(i); c < r.length; c++) a = r[c], n.o(e, a) && e[a] && e[a][0](), e[a] = 0
    },
    i = self.siqFloatJsonp = self.siqFloatJsonp || [];
i.forEach(t.bind(null, 0)), i.push = t.bind(null, i.push.bind(i))
})(), n.nc = void 0, (() => {
"use strict";
var e = n(5812),
    t = n(337),
    i = (n(1181), n(4912)),
    o = n(9291),
    a = n(2220),
    r = n(4562),
    s = n(2717),
    l = n(8008);
n(4196), n(2338);
var c = n(2660);
(0, t.handlePostEmbedDetails)(() => {
    $ZSD("body").append((0, i.FM)()), $ZSD("#zsiq_float").on("click", o.ZV), $ZSD("#zsiq_float").on("keydown", e => {
        "Enter" === e.key && (0, o.ZV)(e)
    }), s.ry || l.LG || !$zoho.salesiq.dragchatwindow || ["transitionstart", "transitionend"].forEach(e => {
        $ZSD("#zsiq_chat_wrap").on(e, function() {
            (e => {
                a.cb.cw_dragposition && a.Uy.cw_open && (0, r.gB)(e)
            })(e)
        })
    }), (() => {
        let {
            chatConfig: e
        } = a.embedDetails, t = a.visitorInfo.embedtheme || e.color;
        document.documentElement.style.cssText += ["--zsiqf-custom-bg-color"].reduce((e, i) => e + `${i}:${t};`, "")
    })(), (() => {
        if (!a.visitorInfo.customhtml) return;
        let [e, t] = a.visitorInfo.customhtml, i = $ZSD(`#${e}`);
        if (!i) return;
        let {
            embedStatus: n
        } = a.embedDetails, r = t[`${n}.html`], s = t[`${n}.click`];
        r && (i[0].innerHTML = r, i.on("click", s || o.R))
    })(), (0, o.LT)(), l.LG && (0, o.R)(), (0, c.u)().then(e.iO).catch(() => {})
})
})()
})();