(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [1503], {
        1503: (e, o, r) => {
            r.r(o), r.d(o, {
                AddColumnIcon: () => pe,
                AddIcon: () => Q,
                AlertCircleIcon: () => $o,
                AlignBottom01Icon: () => io,
                AlignHorizontalCenter01Icon: () => $e,
                AlignLeft01Icon: () => eo,
                AlignRight01Icon: () => oo,
                AlignTopArrow01Icon: () => to,
                AlignVerticalCenter01Icon: () => ro,
                AngleIcon: () => fo,
                AnimationStyleIcon: () => Le,
                AppStoreIcon: () => lo,
                ArrowDownIcon: () => j,
                ArrowLeftIcon: () => k,
                ArrowRightIcon: () => A,
                ArrowUpIcon: () => u,
                ArrowUpRightIcon: () => w,
                BlankIcon: () => Ho,
                BottomLeftCornerIcon: () => me,
                BottomRightCornerIcon: () => Ze,
                BucketPaintIcon: () => ye,
                CalendarOutlineIcon: () => K,
                CheckSquareIcon: () => $,
                ChevronLeftIcon: () => T,
                ChevronRightIcon: () => z,
                ChevronSelectorHorizontalIcon: () => ke,
                ChevronSelectorVerticalIcon: () => ao,
                ChevronSmallDown: () => G,
                ChevronSmallUp: () => F,
                ClockIcon: () => oe,
                CloneIcon: () => Te,
                CloseIcon: () => he,
                CodeCircle03Icon: () => Yo,
                CodeTextIcon: () => Ko,
                ColorDropIcon: () => je,
                ColorPickerIcon: () => wo,
                ColorsIcon: () => we,
                Copy03Icon: () => Xe,
                CopyIcon: () => f,
                CornerRadiusBottomLeftIcon: () => Qo,
                CornerRadiusBottomRightIcon: () => Ro,
                CornerRadiusIcon: () => Do,
                CornerRadiusTopLeftIcon: () => _o,
                CornerRadiusTopRightIcon: () => Eo,
                CornersIcon: () => Ve,
                CountUpIcon: () => Co,
                CursorButtonIcon: () => Me,
                DashedSquareIcon: () => _e,
                DeleteIcon: () => no,
                DesktopCrossIcon: () => bo,
                DesktopIcon: () => To,
                DoListIcon: () => Oo,
                DomainIcon: () => M,
                DotsMenuIcon: () => ko,
                DragHandleIcon: () => y,
                DropdownMenuIcon: () => re,
                DropdownMenuItemCheckMarkIcon: () => yo,
                DuplicateIcon: () => ae,
                EditIcon: () => He,
                EmailIcon: () => _,
                EnvelopeIcon: () => q,
                ExpandIcon: () => P,
                ExternalLinkIcon: () => W,
                EyeIcon: () => Go,
                FacebookGrayScaleIcon: () => ne,
                FacebookIcon: () => g,
                FacebookOutlineIcon: () => ho,
                File04Icon: () => Bo,
                FileIcon: () => Se,
                FlipBackwardIcon: () => ve,
                FloppyIcon: () => ze,
                GarbageIcon: () => Ce,
                GlobeIcon: () => Pe,
                GridDotSquareIcon: () => Lo,
                GridGapIcon: () => Fe,
                HeartIcon: () => Ao,
                HelpCircleIcon: () => Vo,
                ImageIcon: () => ue,
                ImagePictureAddPlusIcon: () => mo,
                ImagePlusIcon: () => go,
                InfoCircleIcon: () => zo,
                InfoIcon: () => ie,
                LargeAppStoreButtonIcon: () => Ue,
                Link01Icon: () => X,
                Link04Icon: () => Y,
                LinkExternal02Icon: () => Wo,
                LinkedInIcon: () => H,
                Mail01Icon: () => Io,
                MapPinSquareIcon: () => er,
                MediumAppStoreButtonIcon: () => be,
                MobileCrossIcon: () => Jo,
                MobileIcon: () => No,
                MobilePhoneIcon: () => N,
                MonitorIcon: () => S,
                MoreHorizIcon: () => xe,
                MoveBackwardIcon: () => Ke,
                MoveForwardIcon: () => Ye,
                NetworkDocumentIcon: () => D,
                ObjectStarSquare: () => Fo,
                PaddingBottomIcon: () => jo,
                PaddingTopIcon: () => uo,
                PenIcon: () => xo,
                PhoneIcon: () => E,
                PillIcon: () => Qe,
                PinIcon: () => Je,
                PlayVideoHandIcon: () => co,
                PlusCircleIcon: () => Ae,
                PlusIcon: () => so,
                RadioIcon: () => ee,
                RoundedIcon: () => Re,
                Rows01Icon: () => po,
                RowsIcon: () => ce,
                SVG: () => p,
                Scale02Icon: () => Zo,
                Settings02Icon: () => Ne,
                SettingsGeneralIcon: () => b,
                SettingsIcon: () => qe,
                SharpIcon: () => Ee,
                SliderControlIcon: () => I,
                SmartphoneIcon: () => O,
                SquareIcon: () => De,
                StopIcon: () => We,
                SwitchHorizontal02Icon: () => Mo,
                TabletCrossIcon: () => qo,
                TabletIcon: () => Uo,
                TelegramIcon: () => B,
                TextH2Icon: () => Xo,
                TextIcon: () => ge,
                TextInputIcon: () => fe,
                ThemeEmptyStateIcon: () => So,
                ThreeDots: () => Ge,
                TickIcon: () => m,
                TopLeftCornerIcon: () => Be,
                TopRightCornerIcon: () => Ie,
                TransparentIcon: () => Oe,
                Trash01Icon: () => vo,
                TrashIcon: () => R,
                TwitchIcon: () => le,
                TwitterIcon: () => v,
                Type01Icon: () => U,
                TypeSquareIcon: () => J,
                TypeWarningIcon: () => or,
                UploadCloudIcon: () => te,
                UserIcon: () => L,
                UserMinusIcon: () => Z,
                VimeoIcon: () => se,
                WhatsAppIcon: () => V,
                YoutubeIcon: () => de,
                ZapIcon: () => Po
            });
            var t = r(43188),
                i = r(2547),
                n = Object.defineProperty,
                s = Object.defineProperties,
                l = Object.getOwnPropertyDescriptors,
                d = Object.getOwnPropertySymbols,
                h = Object.prototype.hasOwnProperty,
                c = Object.prototype.propertyIsEnumerable,
                C = (e, o, r) => o in e ? n(e, o, {
                    enumerable: !0,
                    configurable: !0,
                    writable: !0,
                    value: r
                }) : e[o] = r,
                x = (e, o) => {
                    for (var r in o || (o = {})) h.call(o, r) && C(e, r, o[r]);
                    if (d)
                        for (var r of d(o)) c.call(o, r) && C(e, r, o[r]);
                    return e
                },
                a = (e, o) => s(e, l(o));

            function p(e) {
                return (0, t.jsx)(i.Icon, a(x({}, e), {
                    boxSize: e.size || e.boxSize || "20px"
                }))
            }
            const A = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M4.16602 10H15.8327M15.8327 10L9.99935 4.16669M15.8327 10L9.99935 15.8334",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                k = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M15.8334 10H4.16675M4.16675 10L10.0001 15.8333M4.16675 10L10.0001 4.16666",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                u = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M10.5363 17.1047V4.89539M10.5363 4.89539L4.43164 11M10.5363 4.89539L16.6409 11",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                j = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M9.99984 4.16675V15.8334M9.99984 15.8334L15.8332 10.0001M9.99984 15.8334L4.1665 10.0001",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                w = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M5.83337 14.1663L14.1667 5.83301M14.1667 5.83301H5.83337M14.1667 5.83301V14.1663",
                        stroke: "#1D2939",
                        strokeWidth: "1.66667",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                L = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 22 22",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("g", {
                        id: "Group",
                        children: (0, t.jsxs)("g", {
                            id: "Group_2",
                            children: [(0, t.jsx)("path", {
                                id: "Path",
                                d: "M14.9995 5.2C16.6995 6.9 16.6995 9.6 14.9995 11.2C13.2995 12.8 10.5995 12.9 8.9995 11.2C7.3995 9.5 7.2995 6.8 8.9995 5.2C10.6995 3.6 13.2995 3.6 14.9995 5.2",
                                stroke: "currentColor",
                                strokeWidth: "1.5",
                                strokeLinecap: "round",
                                strokeLinejoin: "round"
                            }), (0, t.jsx)("path", {
                                id: "Path_2",
                                d: "M4 20C4 17.5 6 15.5 8.5 15.5H11.1",
                                stroke: "currentColor",
                                strokeWidth: "1.5",
                                strokeLinecap: "round",
                                strokeLinejoin: "round"
                            }), (0, t.jsx)("path", {
                                id: "Path_3",
                                d: "M19 17.6V16.5C19 15.7 18.3 15 17.5 15C16.7 15 16 15.7 16 16.5V17.6",
                                stroke: "currentColor",
                                strokeWidth: "1.5",
                                strokeLinecap: "round",
                                strokeLinejoin: "round"
                            }), (0, t.jsx)("path", {
                                id: "Path_4",
                                fillRule: "evenodd",
                                clipRule: "evenodd",
                                d: "M19 17.6H16C15.4 17.6 15 18 15 18.6V20C15 20.6 15.4 21 16 21H19C19.6 21 20 20.6 20 20V18.6C20 18 19.6 17.6 19 17.6Z",
                                stroke: "currentColor",
                                strokeWidth: "1.5",
                                strokeLinecap: "round",
                                strokeLinejoin: "round"
                            })]
                        })
                    })
                })),
                M = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 16 16",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M1.79084 10.4301L3.06297 9.69568C3.13188 9.6559 3.21274 9.64208 3.29094 9.65672L5.79394 10.1252C5.99964 10.1637 6.18953 10.0053 6.1886 9.79604L6.17885 7.60295C6.17858 7.54336 6.1943 7.4848 6.22435 7.43334L7.4875 5.27079C7.55324 5.15824 7.54735 5.0177 7.47242 4.91105L5.34562 1.88377M12.6665 3.23935C8.99981 5.00001 10.9997 7.33334 11.6665 7.66667C12.9179 8.29226 14.6581 8.33331 14.6581 8.33331C14.6635 8.22288 14.6663 8.11175 14.6663 7.99998C14.6663 4.31808 11.6816 1.33331 7.99967 1.33331C4.31778 1.33331 1.33301 4.31808 1.33301 7.99998C1.33301 11.6819 4.31778 14.6666 7.99967 14.6666C8.11145 14.6666 8.22258 14.6639 8.33301 14.6585M11.1715 14.6265L9.06034 9.06065L14.6262 11.1718L12.1581 12.1584L11.1715 14.6265Z",
                        stroke: "currentColor",
                        strokeWidth: "1.33333",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                f = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("rect", {
                        x: "2.5",
                        y: "5.83301",
                        width: "11.6667",
                        height: "11.6667",
                        rx: "3",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M5.83301 3.33334C6.36603 2.79891 7.0901 2.499 7.8449 2.5H14.9997C16.3804 2.5 17.4997 3.61929 17.4997 5V12.1547C17.5007 12.9096 17.2008 13.6336 16.6663 14.1667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                g = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("rect", {
                        width: "24",
                        height: "24",
                        rx: "12",
                        fill: "#1877F2"
                    }), (0, t.jsx)("path", {
                        d: "M16.6711 15.4688L17.2031 12H13.875V9.75C13.875 8.80078 14.3391 7.875 15.8297 7.875H17.3438V4.92188C17.3438 4.92188 15.9703 4.6875 14.6578 4.6875C11.9156 4.6875 10.125 6.34922 10.125 9.35625V12H7.07812V15.4688H10.125V23.8547C10.7367 23.9508 11.3625 24 12 24C12.6375 24 13.2633 23.9508 13.875 23.8547V15.4688H16.6711Z",
                        fill: "white"
                    })]
                })),
                v = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("rect", {
                        width: "24",
                        height: "24",
                        rx: "12",
                        fill: "black"
                    }), (0, t.jsx)("path", {
                        d: "M16.096 5.64258H18.1201L13.698 10.6967L18.9002 17.5742H14.8269L11.6366 13.403L7.98616 17.5742H5.96085L10.6907 12.1682L5.7002 5.64258H9.87686L12.7606 9.45518L16.096 5.64258ZM15.3856 16.3626H16.5071L9.26743 6.79046H8.06387L15.3856 16.3626Z",
                        fill: "white"
                    })]
                })),
                H = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z",
                        fill: "#0077B5"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M8.65899 7.41135C8.65899 8.1959 8.06844 8.82367 7.1202 8.82367H7.10282C6.18984 8.82367 5.59961 8.1959 5.59961 7.41135C5.59961 6.6102 6.20781 6 7.13813 6C8.06844 6 8.64134 6.6102 8.65899 7.41135ZM8.47988 9.93892V18.1098H5.76042V9.93892H8.47988ZM18.2872 18.1098L18.2873 13.4249C18.2873 10.9151 16.9457 9.74706 15.1562 9.74706C13.7123 9.74706 13.0659 10.5401 12.7049 11.0965V9.93914H9.98516C10.021 10.7059 9.98516 18.11 9.98516 18.11H12.7049V13.5467C12.7049 13.3025 12.7226 13.0589 12.7945 12.8841C12.991 12.3962 13.4386 11.8911 14.1899 11.8911C15.1743 11.8911 15.5678 12.6404 15.5678 13.7384V18.1098H18.2872Z",
                        fill: "white"
                    })]
                })),
                V = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("rect", {
                        width: "24",
                        height: "24",
                        rx: "12",
                        fill: "#20B038"
                    }), (0, t.jsx)("path", {
                        d: "M4.7207 18.9819L5.74964 15.2455C5.11351 14.1488 4.7798 12.9068 4.78327 11.6371C4.78327 7.65856 8.03693 4.42383 12.031 4.42383C13.9707 4.42383 15.7922 5.17456 17.1583 6.53765C18.5279 7.90074 19.2822 9.71357 19.2787 11.6406C19.2787 15.6191 16.0251 18.8539 12.0275 18.8539H12.024C10.8109 18.8539 9.61857 18.5494 8.55835 17.9751L4.7207 18.9819ZM8.74258 16.6708L8.96158 16.8023C9.88623 17.3489 10.9464 17.6361 12.0275 17.6395H12.031C15.3507 17.6395 18.0551 14.9514 18.0551 11.644C18.0551 10.0422 17.4294 8.5373 16.2927 7.40255C15.156 6.2678 13.6404 5.64507 12.031 5.64507C8.7113 5.64161 6.00687 8.32973 6.00687 11.6371C6.00687 12.7684 6.3232 13.872 6.92804 14.8269L7.07056 15.0552L6.46224 17.2659L8.74258 16.6708Z",
                        fill: "white"
                    }), (0, t.jsx)("path", {
                        d: "M4.7207 18.9819L5.74964 15.2455C5.11351 14.1488 4.7798 12.9068 4.78327 11.6371C4.78327 7.65856 8.03693 4.42383 12.031 4.42383C13.9707 4.42383 15.7922 5.17456 17.1583 6.53765C18.5279 7.90074 19.2822 9.71357 19.2787 11.6406C19.2787 15.6191 16.0251 18.8539 12.0275 18.8539H12.024C10.8109 18.8539 9.61857 18.5494 8.55835 17.9751L4.7207 18.9819ZM8.74258 16.6708L8.96158 16.8023C9.88623 17.3489 10.9464 17.6361 12.0275 17.6395H12.031C15.3507 17.6395 18.0551 14.9514 18.0551 11.644C18.0551 10.0422 17.4294 8.5373 16.2927 7.40255C15.156 6.2678 13.6404 5.64507 12.031 5.64507C8.7113 5.64161 6.00687 8.32973 6.00687 11.6371C6.00687 12.7684 6.3232 13.872 6.92804 14.8269L7.07056 15.0552L6.46224 17.2659L8.74258 16.6708Z",
                        fill: "white"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M10.2198 8.62049C10.0842 8.3195 9.94168 8.31258 9.81306 8.30912C9.70878 8.30566 9.58711 8.30566 9.46545 8.30566C9.34378 8.30566 9.14912 8.35064 8.98226 8.53054C8.81541 8.71044 8.34961 9.14635 8.34961 10.0355C8.34961 10.9211 8.99965 11.7791 9.09002 11.9002C9.1804 12.0213 10.3449 13.8999 12.1838 14.6229C13.7133 15.2249 14.0261 15.1038 14.3564 15.0727C14.6866 15.0415 15.427 14.6367 15.58 14.2147C15.7294 13.7926 15.7294 13.4328 15.6842 13.3567C15.639 13.2806 15.5174 13.2356 15.3366 13.1457C15.1559 13.0557 14.266 12.6198 14.0991 12.5575C13.9323 12.4987 13.8106 12.4676 13.6924 12.6475C13.5708 12.8274 13.2231 13.2321 13.1189 13.3532C13.0146 13.4743 12.9068 13.4882 12.7261 13.3982C12.5453 13.3083 11.9613 13.118 11.2696 12.5022C10.7308 12.0247 10.3658 11.4332 10.2615 11.2533C10.1572 11.0734 10.2511 10.9765 10.3414 10.8865C10.4214 10.807 10.5222 10.6755 10.6126 10.5717C10.7029 10.4679 10.7342 10.3918 10.7933 10.2707C10.8524 10.1496 10.8246 10.0458 10.7794 9.9559C10.7342 9.86941 10.3797 8.97683 10.2198 8.62049Z",
                        fill: "white"
                    })]
                })),
                B = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("rect", {
                        width: "24",
                        height: "24",
                        rx: "12",
                        fill: "url(#paint0_linear_7428_178505)"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M5.43202 11.873C8.93026 10.3488 11.263 9.34403 12.4302 8.85856C15.7627 7.47245 16.4551 7.23167 16.9065 7.22372C17.0058 7.22197 17.2277 7.24657 17.3715 7.36323C17.4929 7.46174 17.5263 7.59481 17.5423 7.68821C17.5583 7.7816 17.5782 7.99436 17.5623 8.16061C17.3818 10.0581 16.6003 14.6628 16.2028 16.788C16.0346 17.6872 15.7034 17.9888 15.3827 18.0183C14.6859 18.0824 14.1567 17.5577 13.4817 17.1153C12.4256 16.423 11.8289 15.992 10.8037 15.3164C9.61896 14.5357 10.387 14.1066 11.0622 13.4053C11.2389 13.2217 14.3093 10.429 14.3687 10.1756C14.3762 10.144 14.3831 10.0258 14.3129 9.96348C14.2427 9.90111 14.1392 9.92244 14.0644 9.9394C13.9585 9.96344 12.2713 11.0786 9.00276 13.285C8.52385 13.6138 8.09007 13.7741 7.70141 13.7657C7.27295 13.7564 6.44876 13.5234 5.83606 13.3242C5.08456 13.08 4.48728 12.9508 4.53929 12.5359C4.56638 12.3198 4.86395 12.0989 5.43202 11.873Z",
                        fill: "white"
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsxs)("linearGradient", {
                            id: "paint0_linear_7428_178505",
                            x1: "12",
                            y1: "0",
                            x2: "12",
                            y2: "23.822",
                            gradientUnits: "userSpaceOnUse",
                            children: [(0, t.jsx)("stop", {
                                stopColor: "#2AABEE"
                            }), (0, t.jsx)("stop", {
                                offset: "1",
                                stopColor: "#229ED9"
                            })]
                        })
                    })]
                })),
                I = e => (0, t.jsxs)(p, a(x({
                    width: "28",
                    height: "28",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M10 14L8 12L10 10",
                        stroke: "currentColor",
                        strokeWidth: "1.152",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M14 14L16 12L14 10",
                        stroke: "currentColor",
                        strokeWidth: "1.152",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                m = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 14 10",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M1.58301 4.16634L5.74967 8.33301L13.2497 0.833008",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        alignmentBaseline: "central"
                    })
                })),
                Z = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M14.9993 16.6667V15.8333C14.9993 13.9924 13.507 12.5 11.666 12.5H4.99935C3.1584 12.5 1.66602 13.9924 1.66602 15.8333V16.6667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("circle", {
                        cx: "8.33333",
                        cy: "5.83333",
                        r: "3.33333",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M17.5 9.16667H15",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                W = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M17.5 7.50001L17.5 2.50001M17.5 2.50001H12.5M17.5 2.50001L10 10M8.33333 2.5H6.5C5.09987 2.5 4.3998 2.5 3.86502 2.77248C3.39462 3.01217 3.01217 3.39462 2.77248 3.86502C2.5 4.3998 2.5 5.09987 2.5 6.5V13.5C2.5 14.9001 2.5 15.6002 2.77248 16.135C3.01217 16.6054 3.39462 16.9878 3.86502 17.2275C4.3998 17.5 5.09987 17.5 6.5 17.5H13.5C14.9001 17.5 15.6002 17.5 16.135 17.2275C16.6054 16.9878 16.9878 16.6054 17.2275 16.135C17.5 15.6002 17.5 14.9001 17.5 13.5V11.6667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                D = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 16 20",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M3 0C1.905 0 1 .905 1 2v12c0 1.095.905 2 2 2h4v2H0v2h16v-2H9v-2h4c1.095 0 2-.905 2-2V4.049L10.998 0H3zm0 2h7v3h3v9H3V2z",
                        fillRule: "nonzero"
                    })
                })),
                _ = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M10 0C4.489 0 0 4.489 0 10s4.489 10 10 10h5v-2h-5c-4.43 0-8-3.57-8-8s3.57-8 8-8 8 3.57 8 8v1.5c0 .84-.66 1.5-1.5 1.5s-1.5-.66-1.5-1.5V10c0-2.75-2.25-5-5-5s-5 2.25-5 5 2.25 5 5 5a4.975 4.975 0 0 0 3.6-1.553A3.504 3.504 0 0 0 16.5 15c1.921 0 3.5-1.579 3.5-3.5V10c0-5.511-4.489-10-10-10zm0 7c1.669 0 3 1.331 3 3s-1.331 3-3 3-3-1.331-3-3 1.331-3 3-3z",
                        fillRule: "nonzero"
                    })
                })),
                E = e => (0, t.jsx)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M6.98356 7.37779C7.56356 8.58581 8.35422 9.71801 9.35553 10.7193C10.3568 11.7206 11.4891 12.5113 12.6971 13.0913C12.801 13.1412 12.8529 13.1661 12.9187 13.1853C13.1523 13.2534 13.4392 13.2045 13.637 13.0628C13.6927 13.0229 13.7403 12.9753 13.8356 12.88C14.1269 12.5887 14.2726 12.443 14.4191 12.3478C14.9715 11.9886 15.6837 11.9886 16.2361 12.3478C16.3825 12.443 16.5282 12.5887 16.8196 12.88L16.9819 13.0424C17.4248 13.4853 17.6462 13.7067 17.7665 13.9446C18.0058 14.4175 18.0058 14.9761 17.7665 15.449C17.6462 15.6869 17.4248 15.9083 16.9819 16.3512L16.8506 16.4825C16.4092 16.9239 16.1886 17.1446 15.8885 17.3131C15.5556 17.5001 15.0385 17.6346 14.6567 17.6334C14.3126 17.6324 14.0774 17.5657 13.607 17.4322C11.0792 16.7147 8.69387 15.361 6.70388 13.371C4.7139 11.381 3.36017 8.99569 2.6427 6.46786C2.50919 5.99749 2.44244 5.7623 2.44141 5.41818C2.44028 5.03633 2.57475 4.51925 2.76176 4.18633C2.9303 3.88631 3.15098 3.66563 3.59233 3.22428L3.72369 3.09292C4.16656 2.65005 4.388 2.42861 4.62581 2.30833C5.09878 2.0691 5.65734 2.0691 6.1303 2.30832C6.36812 2.42861 6.58955 2.65005 7.03242 3.09291L7.19481 3.25531C7.48615 3.54665 7.63182 3.69231 7.72706 3.8388C8.08622 4.3912 8.08622 5.10336 7.72706 5.65576C7.63182 5.80225 7.48615 5.94791 7.19481 6.23925C7.09955 6.33451 7.05192 6.38214 7.01206 6.43782C6.87038 6.63568 6.82146 6.92256 6.88957 7.15619C6.90873 7.22193 6.93367 7.27389 6.98356 7.37779Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                R = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M13.3333 5.00033V4.33366C13.3333 3.40024 13.3333 2.93353 13.1517 2.57701C12.9919 2.2634 12.7369 2.00844 12.4233 1.84865C12.0668 1.66699 11.6001 1.66699 10.6667 1.66699H9.33333C8.39991 1.66699 7.9332 1.66699 7.57668 1.84865C7.26308 2.00844 7.00811 2.2634 6.84832 2.57701C6.66667 2.93353 6.66667 3.40024 6.66667 4.33366V5.00033M8.33333 9.58366V13.7503M11.6667 9.58366V13.7503M2.5 5.00033H17.5M15.8333 5.00033V14.3337C15.8333 15.7338 15.8333 16.4339 15.5608 16.9686C15.3212 17.439 14.9387 17.8215 14.4683 18.0612C13.9335 18.3337 13.2335 18.3337 11.8333 18.3337H8.16667C6.76654 18.3337 6.06647 18.3337 5.53169 18.0612C5.06129 17.8215 4.67883 17.439 4.43915 16.9686C4.16667 16.4339 4.16667 15.7338 4.16667 14.3337V5.00033",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                Q = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M0 0h24v24H0V0z",
                        fill: "none"
                    }), (0, t.jsx)("path", {
                        d: "M18 13h-5v5c0 .55-.45 1-1 1s-1-.45-1-1v-5H6c-.55 0-1-.45-1-1s.45-1 1-1h5V6c0-.55.45-1 1-1s1 .45 1 1v5h5c.55 0 1 .45 1 1s-.45 1-1 1z"
                    })]
                })),
                y = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M12.5 2.5a1.666 1.666 0 1 0 0 3.333 1.666 1.666 0 0 0 0-3.333Zm0 5.833a1.666 1.666 0 1 0 0 3.333 1.666 1.666 0 0 0 0-3.333Zm0 5.834a1.667 1.667 0 1 0 0 3.333 1.667 1.667 0 0 0 0-3.333ZM7.5 2.5a1.667 1.667 0 1 0 0 3.333 1.667 1.667 0 0 0 0-3.333Zm0 5.833a1.667 1.667 0 1 0 0 3.334 1.667 1.667 0 0 0 0-3.334Zm0 5.834a1.667 1.667 0 1 0 0 3.333 1.667 1.667 0 0 0 0-3.333Z"
                    })
                })),
                S = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 511.999 511.999",
                    fill: "currentColor"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M481.091 27.937H30.909C13.866 27.937 0 41.803 0 58.846v310.819c0 17.043 13.866 30.909 30.909 30.909h154.26v22.49c0 20.617-16.774 37.391-37.391 37.391h-33.997c-6.518 0-11.803 5.284-11.803 11.803 0 6.519 5.285 11.803 11.803 11.803h284.436c6.518 0 11.803-5.284 11.803-11.803 0-6.519-5.285-11.803-11.803-11.803h-33.998c-20.617 0-37.391-16.774-37.391-37.391v-22.489h154.26c17.043 0 30.91-13.866 30.91-30.909V58.846c.002-17.043-13.864-30.909-30.907-30.909zM195.92 460.457c8.046-10.336 12.857-23.308 12.857-37.391v-22.49h94.447v22.49c0 14.083 4.811 27.056 12.857 37.391H195.92zm292.474-90.791c0 4.027-3.276 7.304-7.304 7.304H30.909c-4.027 0-7.304-3.276-7.304-7.304v-62.033h464.789v62.033zm0-85.64H23.606V58.846c0-4.027 3.276-7.304 7.304-7.304h450.18c4.027 0 7.305 3.276 7.305 7.304v225.18z"
                    }), (0, t.jsx)("circle", {
                        cx: "256.003",
                        cy: "342.305",
                        r: "12.738"
                    }), (0, t.jsx)("path", {
                        d: "M276.238 109.254c-4.61-4.609-12.081-4.609-16.693 0l-83.414 83.414c-4.609 4.609-4.609 12.083 0 16.693a11.767 11.767 0 0 0 8.347 3.457c3.022 0 6.041-1.152 8.346-3.457l83.414-83.414c4.609-4.609 4.609-12.083 0-16.693zM325.678 157.593c-4.608-4.609-12.079-4.609-16.692-.001l-33.23 33.228c-4.609 4.61-4.609 12.084 0 16.693a11.765 11.765 0 0 0 8.346 3.457c3.02 0 6.041-1.152 8.346-3.457l33.23-33.228c4.609-4.609 4.609-12.083 0-16.692z"
                    })]
                })),
                O = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 10 16",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M8 .667H2c-.92 0-1.667.746-1.667 1.666v11.334c0 .92.747 1.666 1.667 1.666h6c.92 0 1.666-.746 1.666-1.666V2.333C9.666 1.413 8.92.667 8 .667ZM5 14.083a.75.75 0 1 1-.002-1.499.75.75 0 0 1 .002 1.5ZM8.333 12H1.666V2.667h6.667V12Z"
                    })
                })),
                P = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M11.7.612c-.165.072-.483.374-1.782 1.688C8.91 3.319 8.309 3.957 8.263 4.056a.696.696 0 0 0 .087.704c.288.39.674.533.993.367.086-.045.526-.452.977-.904.45-.453.832-.823.849-.823.016 0 .034.962.04 2.137.011 2.087.013 2.14.093 2.248.191.254.502.354.907.29a.672.672 0 0 0 .489-.29c.08-.108.082-.161.093-2.248.006-1.175.024-2.137.04-2.137.017 0 .399.37.849.823.451.452.891.859.977.904.319.166.705.023.993-.367a.696.696 0 0 0 .087-.704c-.046-.099-.647-.737-1.655-1.756C12.783.986 12.465.684 12.3.612a1.013 1.013 0 0 0-.3-.088c-.055 0-.19.039-.3.088M4.04 8.267c-.077.038-.86.781-1.74 1.651C.985 11.218.684 11.535.612 11.7c-.049.11-.088.245-.088.3 0 .055.039.19.088.3.072.165.374.483 1.688 1.782 1.019 1.008 1.657 1.609 1.756 1.655.222.103.49.07.704-.087.39-.288.533-.674.367-.993-.045-.086-.452-.526-.904-.977-.453-.45-.823-.832-.823-.849 0-.016.962-.034 2.137-.04 2.087-.011 2.14-.013 2.248-.093a.672.672 0 0 0 .29-.489c.064-.405-.036-.716-.29-.907-.108-.08-.161-.082-2.248-.093-1.175-.006-2.137-.024-2.137-.04 0-.017.37-.399.823-.849.452-.451.859-.891.904-.977.166-.32.023-.705-.369-.993-.21-.155-.505-.189-.718-.083m15.329-.003a1.431 1.431 0 0 0-.505.512.649.649 0 0 0 .013.576c.043.081.448.517.9.968.453.45.823.832.823.849 0 .016-.962.034-2.137.04-2.087.011-2.14.013-2.248.093-.257.193-.357.499-.292.897.038.235.115.366.292.499.108.08.161.082 2.248.093 1.175.006 2.137.024 2.137.04 0 .017-.37.399-.823.849-.452.451-.859.891-.904.977-.167.321-.023.705.373.996.148.11.205.127.414.127.21 0 .266-.017.417-.128.096-.071.87-.821 1.721-1.667 1.452-1.444 1.551-1.552 1.623-1.762.074-.219.074-.227 0-.446-.072-.21-.171-.318-1.623-1.762-.851-.846-1.624-1.595-1.717-1.664-.21-.154-.508-.191-.712-.087m-7.609 7.659a.7.7 0 0 0-.458.292c-.08.108-.082.161-.093 2.248-.006 1.175-.024 2.137-.04 2.137-.017 0-.399-.37-.849-.823-.451-.452-.891-.859-.977-.904-.321-.167-.705-.023-.996.373-.11.148-.127.205-.127.414 0 .21.017.266.128.417.071.096.821.87 1.667 1.721 1.444 1.452 1.552 1.551 1.762 1.623.219.074.227.074.446 0 .21-.072.318-.171 1.762-1.623.846-.851 1.596-1.625 1.667-1.721.111-.151.128-.207.128-.417 0-.209-.017-.266-.127-.414-.291-.396-.675-.54-.996-.373-.086.045-.526.452-.977.904-.45.453-.833.823-.85.823-.016 0-.03-.935-.03-2.077 0-1.371-.014-2.116-.043-2.19-.124-.33-.541-.501-.997-.41"
                    })
                })),
                z = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fill: "currentColor",
                        fillRule: "evenodd",
                        d: "M8 16a.999.999 0 0 1-.707-1.707L11.586 10 7.293 5.707a.999.999 0 1 1 1.414-1.414l5 5a.999.999 0 0 1 0 1.414l-5 5A.997.997 0 0 1 8 16"
                    })
                })),
                F = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 12 12",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M2.5 8L6 4.5L9.5 8",
                        stroke: "#667085"
                    })
                })),
                G = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 12 12",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M9.5 4L6 7.5L2.5 4",
                        stroke: "#667085"
                    })
                })),
                T = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fill: "currentColor",
                        fillRule: "evenodd",
                        d: "M12 16a.997.997 0 0 1-.707-.293l-5-5a.999.999 0 0 1 0-1.414l5-5a.999.999 0 1 1 1.414 1.414L8.414 10l4.293 4.293A.999.999 0 0 1 12 16"
                    })
                })),
                b = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 1 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsxs)("g", {
                        fill: "none",
                        fillRule: "evenodd",
                        children: [(0, t.jsx)("path", {
                            d: "M0 0h20v20H0z"
                        }), (0, t.jsx)("path", {
                            fill: "currentColor",
                            fillRule: "nonzero",
                            d: "m7.9 1-.442 2.271a7.165 7.165 0 0 0-2.02 1.162l-2.181-.752-2.102 3.638 1.747 1.52A7.194 7.194 0 0 0 2.8 10c0 .368.036.75.102 1.162v.002l-1.747 1.518 2.102 3.637 2.18-.75c.597.49 1.28.88 2.021 1.16L7.9 19h4.202l.44-2.271a7.127 7.127 0 0 0 2.02-1.162l2.182.752 2.1-3.637-1.745-1.52c.067-.412.102-.795.102-1.162 0-.367-.035-.748-.102-1.16v-.002l1.747-1.52-2.102-3.637-2.18.75a7.179 7.179 0 0 0-2.021-1.16L12.1 1H7.899zm1.483 1.8h1.234l.35 1.8.938.355a5.349 5.349 0 0 1 1.516.868l.777.637 1.73-.594.616 1.067-1.381 1.202.158.99v.001c.055.338.079.62.079.874 0 .253-.024.536-.08.874l-.16.99 1.383 1.202-.617 1.068-1.728-.596-.78.639c-.44.362-.947.655-1.513.868h-.001l-.939.355-.35 1.8H9.383l-.35-1.8-.938-.355a5.349 5.349 0 0 1-1.516-.868l-.777-.637-1.73.594-.616-1.067 1.383-1.204-.16-.986v-.002c-.054-.339-.079-.622-.079-.875s.024-.536.08-.874l.16-.99-1.384-1.202.617-1.068 1.73.596.776-.639c.441-.362.95-.655 1.516-.868l.938-.355.35-1.8zM10 6.4c-1.977 0-3.6 1.623-3.6 3.6s1.623 3.6 3.6 3.6 3.6-1.623 3.6-3.6-1.623-3.6-3.6-3.6zm0 1.8c1 0 1.8.8 1.8 1.8s-.8 1.8-1.8 1.8S8.2 11 8.2 10 9 8.2 10 8.2z"
                        })]
                    })
                })),
                U = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M3.33398 5.8335C3.33398 5.05693 3.33398 4.66864 3.46085 4.36236C3.63001 3.95398 3.95447 3.62952 4.36285 3.46036C4.66913 3.3335 5.05742 3.3335 5.83398 3.3335H14.1673C14.9439 3.3335 15.3322 3.3335 15.6385 3.46036C16.0468 3.62952 16.3713 3.95398 16.5405 4.36236C16.6673 4.66864 16.6673 5.05693 16.6673 5.8335M7.50065 16.6668H12.5007M10.0007 3.3335V16.6668",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                q = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fill: "currentColor",
                        d: "M6.802 3.002c-1.68 0-2.661-.052-3.617.435a4 4 0 0 0-1.748 1.748c-.29.569-.373 1.203-.406 1.931-.002.025-.002.05-.002.076-.019.48-.027.96-.027 1.61v6.397c0 1.68-.052 2.66.435 3.616a4 4 0 0 0 1.748 1.748c.956.487 1.937.435 3.617.435h10.396c1.68 0 2.661.052 3.617-.435a3.999 3.999 0 0 0 1.748-1.748c.487-.956.435-1.936.435-3.616V8.8c0-.645-.009-1.12-.027-1.597a1 1 0 0 0-.008-.445c-.047-.588-.152-1.088-.4-1.574a4 4 0 0 0-1.748-1.748c-.956-.487-1.937-.435-3.617-.435H6.802Zm0 2h10.396c1.68 0 2.382.051 2.71.218.375.192.68.496.872.873.031.062.017.419.041.511l-7.56 5.292c-.66.463-.912.606-1.019.633-.159.04-.325.04-.484 0-.107-.027-.358-.17-1.02-.633L3.18 6.604c.024-.092.01-.45.041-.511.192-.377.497-.681.873-.873C4.42 5.053 5.122 5 6.802 5Zm-3.8 3.918 6.59 4.615c.66.462 1.07.78 1.681.933.477.12.977.12 1.454 0 .61-.152 1.02-.47 1.68-.933L21 8.92V15.2c0 1.68-.052 2.38-.219 2.708a1.995 1.995 0 0 1-.873.873c-.327.167-1.029.219-2.709.219H6.802c-1.68 0-2.382-.052-2.71-.219a1.995 1.995 0 0 1-.872-.873c-.167-.327-.219-1.028-.219-2.708V8.92Z"
                    })
                })),
                N = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fill: "currentColor",
                        clipRule: "evenodd",
                        d: "M6.8.833h6.397c.44 0 .819 0 ***********.027.658.086.973.247a2.5 2.5 0 0 1 1.093 1.093c.16.315.22.643.247.972.025.312.025.691.025 1.13V15.7c0 .44 0 .818-.025 1.13-.027.33-.087.657-.247.973a2.5 2.5 0 0 1-1.093 1.092c-.315.161-.643.22-.972.247-.312.026-.691.026-1.13.026H6.8c-.44 0-.818 0-1.13-.026-.33-.027-.658-.086-.973-.247a2.5 2.5 0 0 1-1.092-1.092c-.161-.316-.22-.643-.247-.973-.026-.312-.026-.69-.026-1.13V4.3c0-.439 0-.818.026-1.13.026-.33.086-.657.247-.972a2.5 2.5 0 0 1 1.092-1.093c.315-.16.643-.22.973-.247.312-.026.69-.026 1.13-.026ZM5.805 2.52c-.226.019-.31.05-.351.071a.833.833 0 0 0-.364.364c-.021.041-.053.125-.071.352-.02.236-.02.546-.02 1.026v11.334c0 .48 0 .79.02 *************.05.31.07.351.08.157.208.284.365.364.**************.351.071.236.02.547.02 1.027.02h6.333c.48 0 .791 0 1.027-.02.227-.018.31-.05.352-.07a.833.833 0 0 0 .364-.365c.02-.041.052-.125.07-.351.02-.236.02-.547.02-1.027V4.333c0-.48 0-.79-.02-1.026-.018-.227-.05-.31-.07-.352a.833.833 0 0 0-.364-.364c-.042-.021-.125-.052-.352-.07-.236-.02-.546-.02-1.027-.02H6.832c-.48 0-.79 0-1.027.02ZM8.75 14.584a1.25 1.25 0 1 1 2.5 0 1.25 1.25 0 0 1-2.5 0Z"
                    })
                })),
                J = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fill: "currentColor",
                        clipRule: "evenodd",
                        d: "M6.467 1.667h7.069c.67 0 1.224 0 1.675.036.469.039.9.12 1.304.327.627.32 1.137.83 1.456 1.457.206.404.288.835.327 1.303.037.451.037 1.005.037 1.675v7.07c0 .67 0 1.224-.037 1.675-.038.468-.12.899-.327 1.303a3.334 3.334 0 0 1-1.456 1.457c-.405.206-.835.288-1.304.326-.45.037-1.005.037-1.675.037h-7.07c-.67 0-1.224 0-1.675-.037-.468-.038-.899-.12-1.303-.326a3.334 3.334 0 0 1-1.457-1.457c-.206-.404-.288-.835-.326-1.303-.037-.451-.037-1.005-.037-1.676V6.465c0-.67 0-1.224.037-1.675.038-.468.12-.899.326-1.303.32-.628.83-1.138 1.457-1.457.404-.206.835-.288 1.303-.327.451-.037 1.005-.037 1.676-.036Zm-1.54 1.697c-.365.03-.552.084-.682.15-.314.16-.569.416-.729.73-.066.13-.12.316-.15.682-.03.375-.031.86-.031 1.574v7c0 .714 0 1.199.03 1.574.03.365.085.552.151.683.16.313.415.568.729.728.13.066.317.12.682.15.375.03.86.031 1.574.031h7c.714 0 1.2 0 1.574-.03.366-.03.553-.085.683-.151.314-.16.569-.415.728-.728.067-.13.12-.318.15-.683.031-.375.032-.86.032-1.574v-7c0-.714 0-1.2-.031-1.574-.03-.366-.084-.552-.15-.683a1.667 1.667 0 0 0-.729-.728c-.13-.067-.317-.12-.683-.15-.375-.031-.86-.032-1.574-.032h-7c-.714 0-1.199 0-1.574.031Zm.908 2.47c0-.461.373-.834.833-.834h6.667a.833.833 0 1 1 0 1.667h-2.5v7.5a.833.833 0 0 1-1.667 0v-7.5h-2.5a.833.833 0 0 1-.833-.834Z"
                    })
                })),
                X = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M10.5883 15.3034L9.40982 16.4819C7.78264 18.1091 5.14445 18.1091 3.51726 16.4819C1.89008 14.8547 1.89008 12.2165 3.51726 10.5893L4.69577 9.4108M15.3024 10.5893L16.4809 9.4108C18.1081 7.78361 18.1081 5.14542 16.4809 3.51824C14.8537 1.89106 12.2155 1.89106 10.5883 3.51824L9.40982 4.69675M7.08241 12.9167L12.9157 7.08337",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                Y = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 10",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fill: "currentColor",
                        clipRule: "evenodd",
                        d: "M5.832 1.667a3.333 3.333 0 1 0 0 6.666h1.667A3.333 3.333 0 0 0 10.832 5a.833.833 0 0 1 1.667 0 5 5 0 0 1-5 5H5.832a5 5 0 0 1 0-10h.417a.833.833 0 0 1 0 1.667h-.417Z"
                    }), (0, t.jsx)("path", {
                        fill: "currentColor",
                        clipRule: "evenodd",
                        d: "M14.165 1.667H12.5A3.333 3.333 0 0 0 9.165 5 .833.833 0 1 1 7.5 5a5 5 0 0 1 5-5h1.666a5 5 0 0 1 0 10h-.416a.833.833 0 0 1 0-1.667h.416a3.333 3.333 0 0 0 0-6.666Z"
                    })]
                })),
                K = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fill: "currentColor",
                        clipRule: "evenodd",
                        d: "M8 1a1 1 0 0 1 1 1v1h6V2a1 1 0 1 1 2 0v1.002c.476.003.891.013 1.252.042.562.046 1.079.145 1.564.392a4 4 0 0 1 1.748 1.748c.247.485.346 1.002.392 1.564C22 7.29 22 7.954 22 8.758v8.483c0 .805 0 1.47-.044 2.01-.046.563-.145 1.08-.392 1.565a4 4 0 0 1-1.748 1.748c-.485.247-1.002.346-1.564.392-.541.044-1.206.044-2.01.044H7.758c-.805 0-1.47 0-2.01-.044-.563-.046-1.08-.145-1.565-.392a4 4 0 0 1-1.748-1.748c-.247-.485-.346-1.002-.392-1.564C2 18.71 2 18.046 2 17.242V8.758c0-.805 0-1.47.044-2.01.046-.563.145-1.08.392-1.565a4 4 0 0 1 1.748-1.748c.485-.247 1.002-.346 1.564-.392.361-.03.777-.04 1.252-.042V2a1 1 0 0 1 1-1ZM7 5.002c-.446.003-.795.012-1.089.036-.438.035-.663.1-.819.18a2 2 0 0 0-.874.874c-.08.156-.145.38-.18.819C4 7.361 4 7.943 4 8.8V9h16v-.2c0-.857 0-1.439-.038-1.889-.035-.438-.1-.663-.18-.819a2 2 0 0 0-.874-.874c-.156-.08-.38-.145-.819-.18A15.155 15.155 0 0 0 17 5.002V6a1 1 0 1 1-2 0V5H9v1a1 1 0 1 1-2 0v-.998ZM20 11H4v6.2c0 .857 0 1.439.038 1.889.035.438.1.663.18.819a2 2 0 0 0 .874.874c.*************.819.18C6.361 21 6.943 21 7.8 21h8.4c.857 0 1.439 0 1.889-.038.438-.035.663-.1.819-.18a2 2 0 0 0 .874-.874c.08-.156.145-.38.18-.819.037-.45.038-1.032.038-1.889V11Z"
                    })
                })),
                $ = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fill: "currentColor",
                        clipRule: "evenodd",
                        d: "M6.467 1.667h7.069c.67 0 1.224 0 1.675.036.469.039.9.12 1.304.327.627.32 1.137.83 1.456 1.457.206.404.288.835.327 1.303.037.451.037 1.005.037 1.675v7.07c0 .67 0 1.224-.037 1.675-.038.468-.12.899-.327 1.303a3.334 3.334 0 0 1-1.456 1.457c-.405.206-.835.288-1.304.326-.45.037-1.005.037-1.675.037h-7.07c-.67 0-1.224 0-1.675-.037-.468-.038-.899-.12-1.303-.326a3.334 3.334 0 0 1-1.457-1.457c-.206-.404-.288-.835-.326-1.303-.037-.451-.037-1.005-.037-1.676V6.465c0-.67 0-1.224.037-1.675.038-.468.12-.899.326-1.303.32-.628.83-1.138 1.457-1.457.404-.206.835-.288 1.303-.327.451-.037 1.005-.037 1.676-.036Zm-1.54 1.697c-.365.03-.552.084-.682.15-.314.16-.569.416-.729.73-.066.13-.12.316-.15.682-.03.375-.031.86-.031 1.574v7c0 .714 0 1.199.03 1.574.03.365.085.552.151.683.16.313.415.568.729.728.13.066.317.12.682.15.375.03.86.031 1.574.031h7c.714 0 1.2 0 1.574-.03.366-.03.553-.085.683-.151.314-.16.569-.415.728-.728.067-.13.12-.318.15-.683.031-.375.032-.86.032-1.574v-7c0-.714 0-1.2-.031-1.574-.03-.366-.084-.552-.15-.683a1.667 1.667 0 0 0-.729-.728c-.13-.067-.317-.12-.683-.15-.375-.031-.86-.032-1.574-.032h-7c-.714 0-1.199 0-1.574.031Zm9.414 3.547a.833.833 0 0 1 0 1.178l-5 5a.833.833 0 0 1-1.179 0l-2.5-2.5a.833.833 0 0 1 1.179-1.178l1.91 1.91 4.411-4.41a.833.833 0 0 1 1.179 0Z"
                    })
                })),
                ee = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        clipPath: "url(#prefix__a)",
                        fill: "currentColor",
                        children: [(0, t.jsx)("path", {
                            fillRule: "evenodd",
                            clipRule: "evenodd",
                            d: "M10.003 2.5a7.5 7.5 0 1 0 0 15 7.5 7.5 0 0 0 0-15ZM.836 10a9.167 9.167 0 1 1 18.333 0A9.167 9.167 0 0 1 .836 10Z"
                        }), (0, t.jsx)("path", {
                            d: "M13 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "prefix__a",
                            children: (0, t.jsx)("path", {
                                fill: "#fff",
                                d: "M0 0h20v20H0z"
                            })
                        })
                    })]
                })),
                oe = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        clipPath: "url(#prefix__a)",
                        children: [(0, t.jsx)("mask", {
                            id: "prefix__b",
                            maskUnits: "userSpaceOnUse",
                            x: "0",
                            y: "0",
                            width: "20",
                            height: "20",
                            style: {
                                maskType: "luminance"
                            },
                            children: (0, t.jsx)("path", {
                                d: "M20 0H0v20h20V0Z",
                                fill: "#fff"
                            })
                        }), (0, t.jsx)("g", {
                            mask: "url(#prefix__b)",
                            children: (0, t.jsx)("path", {
                                d: "M10 .834C4.947.834.834 4.947.834 10S4.947 19.166 10 19.166s9.166-4.113 9.166-9.166S15.053.834 10 .834ZM10 2.5c4.152 0 7.5 3.348 7.5 7.5s-3.348 7.5-7.5 7.5A7.487 7.487 0 0 1 2.5 10c0-4.152 3.348-7.5 7.5-7.5Zm0 1.666A.833.833 0 0 0 9.166 5v5a.833.833 0 0 0 .461.746l3.334 1.666a.833.833 0 0 0 .746-1.49l-2.873-1.438V5A.833.833 0 0 0 10 4.166Z"
                            })
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "prefix__a",
                            children: (0, t.jsx)("path", {
                                fill: "#fff",
                                d: "M0 0h20v20H0z"
                            })
                        })
                    })]
                })),
                re = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fill: "currentColor",
                        d: "M5 10.084a.75.75 0 1 0 0 1.5h5a.75.75 0 1 0 0-1.5H5ZM5 13.416a.75.75 0 1 0 0 1.5h5a.75.75 0 1 0 0-1.5H5ZM15.832 4.063a.75.75 0 0 0-.531.224c-.231.172-.403.41-.403.715.002.51.426.936.936.936s.938-.428.938-.938c0-.303-.175-.54-.405-.713a.75.75 0 0 0-.535-.224Z"
                    }), (0, t.jsx)("path", {
                        fill: "currentColor",
                        clipRule: "evenodd",
                        d: "M3.334 1.75A2.429 2.429 0 0 0 .916 4.166v11.668a2.429 2.429 0 0 0 2.418 2.416h8.332a2.429 2.429 0 0 0 2.418-2.416V2.5a.75.75 0 0 0-.75-.75h-10Zm0 1.5h9.25v12.584a.905.905 0 0 1-.918.916H3.334a.905.905 0 0 1-.918-.916V4.166c0-.516.402-.916.918-.916Z"
                    }), (0, t.jsx)("path", {
                        fill: "currentColor",
                        d: "M13.334 1.75a.75.75 0 1 0 0 1.5h3.332c.516 0 .918.4.918.916v1.668a.905.905 0 0 1-.918.916h-15a.75.75 0 1 0 0 1.5h15a2.429 2.429 0 0 0 2.418-2.416V4.166a2.429 2.429 0 0 0-2.418-2.416h-3.332Z"
                    })]
                })),
                te = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M10 1.666c-2.689 0-4.897 1.86-5.566 4.348-2.044.462-3.6 2.227-3.6 4.402 0 1.583.807 2.99 2.035 3.813a.835.835 0 0 0 .928-1.387A2.906 2.906 0 0 1 2.5 10.416a2.908 2.908 0 0 1 2.643-2.904.833.833 0 0 0 .746-.694A4.16 4.16 0 0 1 10 3.334a4.16 4.16 0 0 1 4.111 3.484.833.833 0 0 0 .746.694 2.908 2.908 0 0 1 2.643 2.904 2.906 2.906 0 0 1-1.297 2.426.832.832 0 0 0-.228 1.156.833.833 0 0 0 1.156.23 4.588 4.588 0 0 0 2.035-3.812c0-2.175-1.556-3.94-3.6-4.402C14.898 3.527 12.69 1.666 10 1.666Zm0 7.5a.833.833 0 0 0-.59.244l-3.332 3.334a.834.834 0 0 0 1.178 1.178l1.91-1.908V17.5a.834.834 0 1 0 1.668 0v-5.486l1.91 1.908a.834.834 0 0 0 1.178-1.178L10.59 9.41a.833.833 0 0 0-.59-.244Z"
                    })
                })),
                ie = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M9.247.868A9.346 9.346 0 0 0 5 2.325 9.623 9.623 0 0 0 2.325 5 9.384 9.384 0 0 0 .927 8.717c-.103.67-.103 1.896 0 2.566.173 1.118.537 2.209 1.043 3.124.441.797.883 1.38 1.563 2.061 1.122 1.124 2.326 1.843 3.875 2.312 2.547.772 5.397.347 7.637-1.137 2.264-1.501 3.708-3.852 4.059-6.61.058-.45.058-1.616 0-2.066-.276-2.167-1.215-4.073-2.725-5.535A9.193 9.193 0 0 0 9.247.868m1.903 1.713a7.468 7.468 0 0 1 4.151 2.118 7.47 7.47 0 0 1 2.123 4.184c.08.511.08 1.723 0 2.234a7.47 7.47 0 0 1-2.123 4.184 7.47 7.47 0 0 1-4.184 2.123c-.511.08-1.723.08-2.234 0a7.62 7.62 0 0 1-2.776-1.007c-.95-.565-1.956-1.57-2.517-2.513-.701-1.179-1.034-2.304-1.08-3.655a7.294 7.294 0 0 1 .945-3.916A7.513 7.513 0 0 1 9.083 2.55c.471-.06 1.602-.043 2.067.031M9.698 5.879c-.36.097-.514.333-.514.788 0 .489.179.726.611.807.407.075.797-.068.945-.349.109-.206.109-.711 0-.917-.162-.307-.606-.447-1.042-.329m0 3.331c-.215.061-.347.166-.437.349l-.078.158v3.9l.079.16c.099.201.264.313.533.363.375.07.732-.044.898-.287.134-.196.143-.355.133-2.288-.008-1.615-.016-1.863-.063-1.965a.758.758 0 0 0-.359-.357c-.159-.067-.525-.084-.706-.033"
                    })
                })),
                ne = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M12 1.44C6.168 1.44 1.44 6.168 1.44 12c0 5.294 3.9 9.666 8.982 10.43v-7.631H7.809v-2.776h2.613v-1.847c0-3.058 1.49-4.4 4.031-4.4 1.218 0 1.861.09 2.166.13V8.33h-1.734c-1.079 0-1.456 1.023-1.456 2.176v1.517h3.163l-.43 2.776H13.43v7.653c5.155-.7 9.13-5.106 9.13-10.452 0-5.832-4.727-10.56-10.56-10.56Z"
                    })
                })),
                se = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M19.68 2.4c-3.259-.004-5.055 1.822-6.16 5.381.578-.214 1.158-.478 1.683-.478 1.104 0 1.471.41 1.314 1.686-.053.797-.368 2.083-1.314 3.57-.947 1.434-1.77 1.84-2.243 1.84-.578 0-1.016-.83-1.489-3.061-.157-.637-.525-2.072-.947-4.78-.367-2.495-1.59-3.837-3.324-3.678-.735.052-1.614.737-3.086 2.012-1.052.956-2.05 1.646-3.154 2.602l1.052 1.348c.997-.69 1.577-.956 1.734-.956.735 0 1.418 1.168 2.102 3.559.578 2.177 1.208 4.354 1.787 6.53.892 2.391 1.997 3.56 3.259 3.56 2.102 0 4.623-1.911 7.62-5.841 2.942-3.719 4.421-6.797 4.526-8.974.105-2.867-1.048-4.268-3.36-4.32Z"
                    })
                })),
                le = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M2.55.48.96 4.71v15.93h5.28v2.88h3.555l2.88-2.88h4.32l6.045-6.03V.48H2.55Zm2.73 2.4h15.36v10.56l-2.88 2.88H12L9.12 19.2v-2.88H5.28V2.88ZM9.6 6.24v6.72h2.88V6.24H9.6Zm4.8 0v6.72h2.88V6.24H14.4Z"
                    })
                })),
                de = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M21.551 6.96c-.191-1.056-1.102-1.825-2.16-2.065-1.582-.335-4.511-.575-7.68-.575-3.167 0-6.142.24-7.727.575-1.055.24-1.968.96-2.16 2.065-.193 1.2-.384 2.88-.384 5.04s.191 3.84.431 5.04c.193 1.055 1.105 1.824 2.16 2.064 1.68.336 4.56.576 7.729.576 3.169 0 6.049-.24 7.729-.576 1.055-.24 1.967-.96 2.16-2.064.19-1.2.43-2.929.48-5.04-.098-2.16-.338-3.84-.578-5.04ZM9.12 15.36V8.64L14.976 12 9.12 15.36Z"
                    })
                })),
                he = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        d: "m11.414 10 4.293-4.293a.999.999 0 1 0-1.414-1.414L10 8.586 5.707 4.293a.999.999 0 1 0-1.414 1.414L8.586 10l-4.293 4.293a1 1 0 0 0 1.414 1.414L10 11.414l4.293 4.293a.997.997 0 0 0 1.414 0 .999.999 0 0 0 0-1.414L11.414 10z"
                    })
                })),
                ce = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 12 12",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M2 2c-.547 0-1 .453-1 1v6c0 .547.453 1 1 1h8c.546 0 1-.453 1-1V3c0-.547-.454-1-1-1H2Zm0 1h8v2.5H2V3Zm0 3.5h8V9H2V6.5Z"
                    })
                })),
                Ce = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 486.4 486.4",
                    fill: "currentColor"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M446 70H344.8V53.5c0-29.5-24-53.5-53.5-53.5h-96.2c-29.5 0-53.5 24-53.5 53.5V70H40.4c-7.5 0-13.5 6-13.5 13.5S32.9 97 40.4 97h24.4v317.2c0 39.8 32.4 72.2 72.2 72.2h212.4c39.8 0 72.2-32.4 72.2-72.2V97H446c7.5 0 13.5-6 13.5-13.5S453.5 70 446 70zM168.6 53.5c0-14.6 11.9-26.5 26.5-26.5h96.2c14.6 0 26.5 11.9 26.5 26.5V70H168.6V53.5zm226 360.7c0 24.9-20.3 45.2-45.2 45.2H137c-24.9 0-45.2-20.3-45.2-45.2V97h302.9v317.2h-.1z"
                    }), (0, t.jsx)("path", {
                        d: "M243.2 411c7.5 0 13.5-6 13.5-13.5V158.9c0-7.5-6-13.5-13.5-13.5s-13.5 6-13.5 13.5v238.5c0 7.5 6 13.6 13.5 13.6zM155.1 396.1c7.5 0 13.5-6 13.5-13.5V173.7c0-7.5-6-13.5-13.5-13.5s-13.5 6-13.5 13.5v208.9c0 7.5 6.1 13.5 13.5 13.5zM331.3 396.1c7.5 0 13.5-6 13.5-13.5V173.7c0-7.5-6-13.5-13.5-13.5s-13.5 6-13.5 13.5v208.9c0 7.5 6 13.5 13.5 13.5z"
                    }), " "]
                })),
                xe = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M0 0h24v24H0z",
                        fill: "none"
                    }), (0, t.jsx)("path", {
                        d: "M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
                    }), " "]
                })),
                ae = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 16 16",
                    fill: "currentColor",
                    preserveAspectRatio: "xMidYMid meet"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#prefix__a)",
                        children: (0, t.jsx)("path", {
                            d: "M6.455.616c-.751.089-1.32.474-1.63 1.104-.204.415-.238.692-.238 1.937v.925L3.5 4.594c-1.164.014-1.301.028-1.633.162a1.836 1.836 0 0 0-.666.445c-.3.3-.467.614-.556 1.048C.604 6.446.6 6.794.6 10s.004 3.554.045 3.751c.089.434.256.748.556 *********.614.467 1.048.556.197.041.545.045 3.751.045s3.554-.004 3.751-.045a1.93 1.93 0 0 0 1.048-.556c.212-.212.338-.399.445-.666.134-.332.148-.47.162-1.631l.012-1.084 1.084-.012c1.161-.014 1.299-.028 1.631-.162.267-.107.454-.233.666-.445.3-.3.467-.614.556-1.048.041-.197.045-.545.045-3.751s-.004-3.554-.045-3.751a1.93 1.93 0 0 0-.556-1.048 1.957 1.957 0 0 0-1.02-.554C13.608.608 13.21.603 10.12.598 8.213.595 6.564.603 6.455.616m6.912 1.489c.259.027.386.104.476.287l.064.128.007 3.284c.005 1.854-.002 3.39-.016 3.527-.03.298-.098.418-.29.512-.127.062-.139.063-1.16.073l-1.031.01-.011-1.757c-.012-1.896-.015-1.939-.162-2.302a1.836 1.836 0 0 0-.445-.666 1.836 1.836 0 0 0-.666-.445c-.363-.147-.406-.15-2.302-.162l-1.757-.011.01-1.031c.01-1.021.011-1.033.073-1.16.086-.177.222-.263.451-.286.256-.026 6.511-.027 6.759-.001m-4 4c.259.027.386.104.476.287l.064.128v6.96l-.064.128c-.09.183-.217.26-.476.287-.114.012-1.629.022-3.367.022s-3.253-.01-3.367-.022c-.259-.027-.386-.104-.476-.287l-.064-.128V6.52l.064-.128c.086-.177.222-.263.451-.286.256-.026 6.511-.027 6.759-.001"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "prefix__a",
                            children: (0, t.jsx)("path", {
                                fill: "#fff",
                                d: "M0 0h16v16H0z"
                            })
                        })
                    })]
                })),
                pe = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        clipPath: "url(#prefix__a)",
                        children: [(0, t.jsx)("path", {
                            d: "M10 6.2c0-1.12 0-1.68-.218-2.108a2 2 0 0 0-.874-.874C8.48 3 7.92 3 6.8 3h-.6c-1.12 0-1.68 0-2.108.218a2 2 0 0 0-.874.874C3 4.52 3 5.08 3 6.2v11.6c0 1.12 0 1.68.218 2.108a2 2 0 0 0 .874.874C4.52 21 5.08 21 6.2 21h.6c1.12 0 1.68 0 2.108-.218a2 2 0 0 0 .874-.874C10 19.48 10 18.92 10 17.8V6.2Z",
                            stroke: "#1D2939",
                            strokeWidth: "2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            fill: "none"
                        }), (0, t.jsx)("path", {
                            d: "M21 6.2c0-1.12 0-1.68-.218-2.108a2 2 0 0 0-.874-.874C19.48 3 18.92 3 17.8 3h-.6c-1.12 0-1.68 0-2.108.218a2 2 0 0 0-.874.874C14 4.52 14 5.08 14 6.2v11.6c0 1.12 0 1.68.218 2.108a2 2 0 0 0 .874.874C15.52 21 16.08 21 17.2 21h.6c1.12 0 1.68 0 2.108-.218a2 2 0 0 0 .874-.874C21 19.48 21 18.92 21 17.8V6.2Z",
                            stroke: "#D0D5DD",
                            strokeWidth: "2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            fill: "none"
                        }), (0, t.jsxs)("g", {
                            clipPath: "url(#prefix__b)",
                            children: [(0, t.jsx)("path", {
                                d: "M23.333 12a3.333 3.333 0 1 1-6.667 0 3.333 3.333 0 0 1 6.667 0Z",
                                fill: "#15B79E"
                            }), (0, t.jsx)("path", {
                                d: "M19.667 10.5h.666v3h-.666v-3Z",
                                fill: "#fff"
                            }), (0, t.jsx)("path", {
                                d: "M18.5 11.667h3v.666h-3v-.666Z",
                                fill: "#fff"
                            })]
                        })]
                    }), (0, t.jsxs)("defs", {
                        children: [(0, t.jsx)("clipPath", {
                            id: "prefix__a",
                            children: (0, t.jsx)("path", {
                                fill: "#fff",
                                transform: "rotate(-90 12 12)",
                                d: "M0 0h24v24H0z"
                            })
                        }), (0, t.jsx)("clipPath", {
                            id: "prefix__b",
                            children: (0, t.jsx)("path", {
                                fill: "#fff",
                                transform: "translate(16 8)",
                                d: "M0 0h8v8H0z"
                            })
                        })]
                    })]
                })),
                Ae = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#prefix__a)",
                        children: (0, t.jsx)("path", {
                            fillRule: "evenodd",
                            clipRule: "evenodd",
                            d: "M10 2.667a7.333 7.333 0 1 0 0 14.666 7.333 7.333 0 0 0 0-14.666ZM.667 10a9.333 9.333 0 1 1 18.666 0A9.333 9.333 0 0 1 .667 10ZM10 5.667a1 1 0 0 1 1 1V9h2.333a1 1 0 1 1 0 2H11v2.333a1 1 0 1 1-2 0V11H6.667a1 1 0 1 1 0-2H9V6.667a1 1 0 0 1 1-1Z"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "prefix__a",
                            children: (0, t.jsx)("path", {
                                d: "M0 0h20v20H0z"
                            })
                        })
                    })]
                })),
                ke = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M7.2 5.036c-.115.052-.666.583-2.373 2.291C2.464 9.689 2.468 9.684 2.468 10c0 .316-.004.311 2.359 2.673 1.707 1.708 2.258 2.239 2.373 2.291.305.138.591.066.877-.22.288-.288.36-.575.218-.875-.054-.113-.547-.629-1.924-2.011L4.519 10l1.852-1.858c1.377-1.382 1.87-1.898 1.924-2.011.142-.3.07-.587-.218-.875-.286-.286-.572-.358-.877-.22m5.016-.007c-.172.078-.442.348-.52.52a.724.724 0 0 0 .007.584c.051.114.515.599 1.923 2.009L15.481 10l-1.855 1.858c-1.408 1.41-1.872 1.895-1.923 2.009-.139.305-.067.597.216.88.285.285.577.357.879.215.115-.054.695-.614 2.372-2.292 2.35-2.35 2.363-2.364 2.363-2.67 0-.306-.013-.32-2.363-2.67-1.677-1.678-2.257-2.238-2.372-2.292a.7.7 0 0 0-.582-.009"
                    })
                })),
                ue = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M5.632 1.667h8.736c.67 0 1.224 0 1.675.037.469.038.9.12 1.304.326.627.32 1.137.83 1.456 1.457.207.404.289.835.327 1.303.037.451.037 1.005.037 1.676v7.069c0 .67 0 1.224-.037 1.675-.038.468-.12.899-.326 1.303a3.334 3.334 0 0 1-1.457 1.457c-.405.206-.835.288-1.304.327-.45.036-1.004.036-1.675.036H5.632c-.67 0-1.224 0-1.675-.036-.468-.039-.899-.12-1.303-.327a3.333 3.333 0 0 1-1.457-1.457C.99 16.11.909 15.678.87 15.21c-.037-.451-.037-1.005-.037-1.676V6.466c0-.671 0-1.225.037-1.676.039-.468.12-.899.327-1.303.32-.627.83-1.137 1.457-1.457.404-.206.835-.288 1.303-.326.451-.037 1.005-.037 1.675-.037ZM17.5 10.488V6.5c0-.714 0-1.199-.031-1.574-.03-.365-.084-.552-.15-.683a1.666 1.666 0 0 0-.729-.728c-.13-.066-.317-.12-.682-.15-.375-.03-.86-.032-1.574-.032H5.667c-.714 0-1.2.001-1.574.032-.366.03-.552.084-.683.15-.313.16-.568.415-.728.728-.067.13-.12.318-.15.683-.031.375-.032.86-.032 1.574v7c0 .714 0 1.2.031 1.574.03.366.084.552.15.683.134.26.333.481.577.64l5.229-5.229c.149-.149.293-.293.426-.406.146-.124.33-.257.572-.335a1.668 1.668 0 0 1 1.03 0c.242.078.426.211.572.335.133.113.278.258.427.406l.153.154 1.82-1.82c.149-.15.293-.294.426-.407.146-.123.33-.256.572-.335a1.667 1.667 0 0 1 1.03 0c.242.079.426.212.572.335.133.113.278.258.426.407l.987.986Zm-6.422 2.601-.724-.723a6.842 6.842 0 0 0-.345-.333L10 12.025l-.008.008c-.075.063-.17.158-.345.333l-4.301 4.3h9.308l-3.576-3.577Zm5.664 3.308L12.845 12.5l1.802-1.801a6.842 6.842 0 0 1 .353-.34l.009.007c.074.063.17.158.345.333l2.146 2.146v.655c0 .714 0 1.2-.031 1.574-.03.366-.084.552-.15.683a1.666 1.666 0 0 1-.577.64ZM6.667 6.667a.833.833 0 1 0 0 1.666.833.833 0 0 0 0-1.666Zm-2.5.833a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Z"
                    })
                })),
                je = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M10.581 1.07 10 1.667l-.581-.597a.833.833 0 0 1 1.162 0ZM10 2.843A70.668 70.668 0 0 0 7.897 5.06a39.392 39.392 0 0 0-2.034 2.41c-.619.808-1.07 1.508-1.273 2.01a5.833 5.833 0 1 0 10.82 0c-.203-.502-.654-1.202-1.273-2.01a39.388 39.388 0 0 0-2.034-2.41A70.205 70.205 0 0 0 10 2.843ZM9.419 1.07l.581.597.581-.597.017.015.045.045c.**************.17.168a71.901 71.901 0 0 1 2.525 2.643 41.045 41.045 0 0 1 2.123 2.516c.633.828 1.202 1.676 1.493 2.397a7.5 7.5 0 1 1-13.909 0c.292-.72.861-1.57 1.494-2.397a41.027 41.027 0 0 1 2.123-2.516A71.874 71.874 0 0 1 9.357 1.13l.045-.045.017-.015Z"
                    })
                })),
                we = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M12 2a10 10 0 0 0-9.975 10.713C2.392 18.002 7.005 22 12.307 22H13a2 2 0 0 0 2-2v-3a2 2 0 0 1 2-2h3a2 2 0 0 0 2-2v-.693c0-5.301-3.997-9.915-9.285-10.282C12.475 2.008 12.236 2 12 2zm0 2c.19 0 .381.007.574.02C16.738 4.309 20 7.949 20 12.307V13h-3c-2.206 0-4 1.794-4 4v3h-.693c-4.358 0-8-3.263-8.287-7.426a7.94 7.94 0 0 1 2.128-6.03A7.924 7.924 0 0 1 12 4zm.5 1A1.5 1.5 0 0 0 11 6.5 1.5 1.5 0 0 0 12.5 8 1.5 1.5 0 0 0 14 6.5 1.5 1.5 0 0 0 12.5 5zm-4 1A1.5 1.5 0 0 0 7 7.5 1.5 1.5 0 0 0 8.5 9 1.5 1.5 0 0 0 10 7.5 1.5 1.5 0 0 0 8.5 6zm8 1A1.5 1.5 0 0 0 15 8.5a1.5 1.5 0 0 0 1.5 1.5A1.5 1.5 0 0 0 18 8.5 1.5 1.5 0 0 0 16.5 7zm-10 3A1.5 1.5 0 0 0 5 11.5 1.5 1.5 0 0 0 6.5 13 1.5 1.5 0 0 0 8 11.5 1.5 1.5 0 0 0 6.5 10zm3.5 4a2 2 0 0 0-2 2 2 2 0 0 0 2 2 2 2 0 0 0 2-2 2 2 0 0 0-2-2z"
                    })
                })),
                Le = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 21",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M3.534.871c-.234.039-.412.158-.508.337-.072.134-.077.185-.086.932l-.011.789-.789.011c-.747.009-.798.014-.932.086-.272.145-.403.503-.336.924.039.249.153.426.336.524.134.072.185.077.932.086l.789.011.011.789c.009.747.014.798.086.932.098.183.275.297.524.336.421.067.779-.064.924-.336.072-.134.077-.185.086-.932l.011-.789.789-.011c.747-.009.798-.014.932-.086.183-.098.297-.275.336-.524.067-.421-.064-.779-.336-.924-.134-.072-.185-.077-.932-.086l-.789-.011-.011-.789c-.009-.747-.014-.798-.086-.932C4.356.986 4.198.903 3.8.851a1.229 1.229 0 0 0-.266.02m7.011.865c-.341.131-.363.172-1.025 1.897-1.244 3.24-1.251 3.255-1.525 3.529s-.284.278-3.522 1.522c-.849.327-1.61.635-1.69.686-.276.175-.359.525-.215.909.135.361.102.345 2.465 1.252 2.484.954 2.714 1.055 2.962 1.305.276.279.245.207 1.376 3.146.402 1.044.768 1.962.814 2.041.173.295.535.389.927.242.362-.135.345-.102 1.252-2.465.95-2.476 1.054-2.712 1.306-2.964.251-.251.487-.355 2.957-1.303 1.16-.445 2.177-.853 2.26-.905.269-.171.353-.53.212-.907-.135-.361-.103-.345-2.466-1.252-1.164-.447-2.266-.883-2.45-.969-.438-.205-.638-.401-.836-.817-.078-.165-.511-1.259-.962-2.431-.451-1.173-.857-2.196-.903-2.275-.173-.294-.547-.391-.937-.241m.753 4.256c.62 1.616.783 1.924 1.257 2.377.444.424.72.565 2.287 1.167C15.479 9.78 16 9.989 16 10c0 .011-.521.22-1.158.464-1.616.621-1.924.783-2.377 1.257-.425.444-.566.72-1.167 2.287-.245.637-.454 1.159-.465 1.159-.011 0-.22-.522-.464-1.159-.601-1.567-.742-1.843-1.167-2.287-.453-.474-.761-.636-2.377-1.257-.637-.244-1.158-.453-1.158-.464 0-.011.521-.22 1.158-.464 1.567-.602 1.843-.743 2.287-1.167.474-.453.637-.761 1.257-2.377.244-.637.453-1.159.464-1.159.011 0 .22.522.465 1.159m-7.764 7.379c-.234.039-.412.158-.508.337-.072.134-.077.185-.086.932l-.011.789-.789.011c-.747.009-.798.014-.932.086-.272.145-.403.503-.336.924.039.249.153.426.336.524.134.072.185.077.932.086l.789.011.011.789c.009.747.014.798.086.932.098.183.275.297.524.336.421.067.779-.064.924-.336.072-.134.077-.185.086-.932l.011-.789.789-.011c.747-.009.798-.014.932-.086.183-.098.297-.275.336-.524.067-.421-.064-.779-.336-.924-.134-.072-.185-.077-.932-.086l-.789-.011-.011-.789c-.009-.747-.014-.798-.086-.932-.118-.222-.276-.305-.674-.357a1.229 1.229 0 0 0-.266.02",
                        fillRule: "evenodd"
                    })
                })),
                Me = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M11.3827 15.4348H14.3332C14.6037 15.4348 14.8475 15.2713 14.9502 15.0211C15.0529 14.7708 14.9943 14.4833 14.8018 14.2932L9.46755 9.02683C9.27615 8.83786 8.98996 8.78235 8.74179 8.88607C8.49361 8.9898 8.33203 9.23244 8.33203 9.50142V16.8347C8.33203 17.1031 8.49298 17.3454 8.74045 17.4495C8.98792 17.5535 9.27366 17.4991 9.4655 17.3113L11.3827 15.4348Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M5.83277 10.8334H4.16541C3.24455 10.8334 2.49805 10.0869 2.49805 9.16603V4.16394C2.49805 3.24308 3.24455 2.49658 4.16541 2.49658H15.8369C16.7578 2.49658 17.5043 3.24308 17.5043 4.16394V9.16603C17.5043 10.0869 16.7578 10.8334 15.8369 10.8334H15.0033",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                fe = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 21",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M11.894 2.546a.71.71 0 0 0-.537.704c0 .354.232.64.58.714.091.02.466.036.831.036h.665V17.495l-.758.011c-.72.01-.767.015-.92.09-.533.262-.531 1.046.002 1.309l.16.078h2.233c2.456 0 2.381.006 2.6-.207a.665.665 0 0 0 .221-.526.665.665 0 0 0-.207-.514c-.203-.203-.268-.217-1.106-.23l-.758-.011v-1.828l.592-.001c.937-.002 1.581-.05 1.896-.142a2.359 2.359 0 0 0 1.503-1.395c.156-.421.159-.485.159-3.379s-.003-2.958-.159-3.379a2.406 2.406 0 0 0-.976-1.164c-.484-.287-.734-.332-2.023-.362l-.992-.022V4.005l.758-.011c.838-.013.903-.027 1.106-.23a.665.665 0 0 0 .207-.514.665.665 0 0 0-.207-.514c-.229-.229-.115-.219-2.531-.226-1.685-.005-2.219.003-2.339.036M3.333 5.867c-.656.038-1.035.164-1.464.487-.511.384-.823.949-.901 1.632-.047.407-.046 5.176.001 5.548.044.354.165.719.325.981.255.418.758.825 1.215.982.435.15.528.153 4.624.153h3.884l.136-.073c.554-.296.552-1.022-.003-1.322l-.133-.072-3.967-.016c-3.775-.016-3.974-.02-4.109-.079-.179-.079-.427-.352-.482-.533-.061-.201-.061-5.409 0-5.61.055-.181.303-.454.482-.533.135-.059.334-.063 4.109-.079l3.967-.016.136-.073c.554-.297.552-1.022-.003-1.322l-.133-.072-3.634-.004c-1.998-.002-3.82.008-4.05.021m13.726 1.546a.962.962 0 0 1 .479.562c.036.14.045.7.045 2.775 0 2.934.007 2.842-.236 3.113-.25.278-.281.284-1.439.297l-1.008.011V7.329l1.008.011c.892.01 1.025.018 1.151.073",
                        fillRule: "evenodd"
                    })
                })),
                ge = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 21",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M3.33331 2.5V4.16667V5.83333H4.99998V4.16667H9.16665V15.8333H7.49998V17.5H9.16665H10.8333H12.5V15.8333H10.8333V4.16667H15V5.83333H16.6666V3.33333V2.5H3.33331Z"
                    })
                })),
                ve = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M2.5 7.99984H13.75C15.8211 7.99984 17.5 9.67877 17.5 11.7498C17.5 13.8209 15.8211 15.4998 13.75 15.4998H10M2.5 7.99984L5.83333 4.6665M2.5 7.99984L5.83333 11.3332",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                He = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M4.41018 13.0157L13.0152 4.41074C13.3402 4.08574 13.8677 4.08574 14.1927 4.41074L15.5893 5.80741C15.9143 6.13241 15.9143 6.65991 15.5893 6.98491L6.98352 15.5891C6.82768 15.7457 6.61602 15.8332 6.39518 15.8332H4.16602V13.6041C4.16602 13.3832 4.25352 13.1716 4.41018 13.0157Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M11.459 5.9668L14.034 8.5418",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Ve = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 20 21",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M18.0269 2.30303L18.0269 2.30302L18.0262 2.30233C17.4856 1.76176 16.7578 1.45 15.9477 1.45H13.7411C13.4088 1.45 13.1395 1.71937 13.1395 2.05165C13.1395 2.38393 13.4088 2.65329 13.7411 2.65329H15.9273C16.4035 2.65329 16.8405 2.85171 17.1586 3.16986C17.4768 3.48802 17.6752 3.92502 17.6752 4.40125V6.77128C17.6752 7.10356 17.9446 7.37293 18.2769 7.37293C18.6091 7.37293 18.8785 7.10356 18.8785 6.77128V4.38082C18.8785 3.5699 18.5458 2.84268 18.0269 2.30303Z",
                        fill: "currentColor",
                        stroke: "currentColor",
                        strokeWidth: "0.1"
                    }), (0, t.jsx)("path", {
                        d: "M1.97742 18.6767L1.97736 18.6768L1.9795 18.6787C2.53912 19.1969 3.26599 19.5297 4.07703 19.5297H6.26318C6.59546 19.5297 6.86483 19.2604 6.86483 18.9281C6.86483 18.5958 6.59546 18.3265 6.26318 18.3265H4.07703C3.60081 18.3265 3.1638 18.128 2.84564 17.8099C2.52749 17.4917 2.32907 17.0547 2.32907 16.5785V14.2289C2.32907 13.8966 2.05971 13.6272 1.72743 13.6272C1.39515 13.6272 1.12578 13.8966 1.12578 14.2289V16.5989C1.12578 17.4098 1.45853 18.1371 1.97742 18.6767Z",
                        fill: "currentColor",
                        stroke: "currentColor",
                        strokeWidth: "0.1"
                    }), (0, t.jsx)("path", {
                        d: "M18.005 18.6985L18.0057 18.6978C18.5463 18.1573 18.8581 17.4295 18.8581 16.6194V14.2289C18.8581 13.8966 18.5887 13.6272 18.2564 13.6272C17.9241 13.6272 17.6548 13.8966 17.6548 14.2289V16.5989C17.6548 17.0752 17.4564 17.5122 17.1382 17.8303C16.8201 18.1485 16.383 18.3469 15.9068 18.3469H13.7411C13.4088 18.3469 13.1395 18.6162 13.1395 18.9485C13.1395 19.2808 13.4088 19.5502 13.7411 19.5502H15.9273C16.7382 19.5502 17.4654 19.2174 18.005 18.6985Z",
                        fill: "currentColor",
                        stroke: "currentColor",
                        strokeWidth: "0.1"
                    }), (0, t.jsx)("path", {
                        d: "M1.99834 2.30164L1.99833 2.30164L1.99764 2.30233C1.45708 2.84289 1.14531 3.5707 1.14531 4.38082V6.75085C1.14531 7.08313 1.41468 7.3525 1.74696 7.3525C2.07924 7.3525 2.3486 7.08313 2.3486 6.75085V4.38082C2.3486 3.90459 2.54702 3.46759 2.86517 3.14943C3.18333 2.83127 3.62034 2.63286 4.09656 2.63286H6.29293C6.61957 2.63286 6.88436 2.36807 6.88436 2.04143C6.88436 1.71479 6.61957 1.45 6.29293 1.45H4.07613C3.26522 1.45 2.53799 1.78275 1.99834 2.30164Z",
                        fill: "currentColor",
                        stroke: "currentColor",
                        strokeWidth: "0.1"
                    })]
                })),
                Be = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 21",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M16 4.52835C2.77249 4.52837 4.03225 3.33116 4.03225 16.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round"
                    })
                })),
                Ie = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 21",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M4 4.52835C17.2275 4.52837 15.9677 3.33116 15.9677 16.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round"
                    })
                })),
                me = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 21",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M16 16.4716C2.77249 16.4716 4.03225 17.6688 4.03225 4.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round"
                    })
                })),
                Ze = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 20 21",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M4 16.4716C17.2275 16.4716 15.9677 17.6688 15.9677 4.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round"
                    })
                })),
                We = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 21 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M15.8059 4.69678L5.19922 15.3034L15.8059 4.69678Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M10.502 2.5V2.5C6.35945 2.5 3.00195 5.8575 3.00195 10V10C3.00195 14.1425 6.35945 17.5 10.502 17.5V17.5C14.6445 17.5 18.002 14.1425 18.002 10V10C18.002 5.8575 14.6445 2.5 10.502 2.5Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                De = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 21 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M3.00195 6.5C3.00195 5.09987 3.00195 4.3998 3.27444 3.86502C3.51412 3.39462 3.89657 3.01217 4.36698 2.77248C4.90176 2.5 5.60182 2.5 7.00195 2.5H14.002C15.4021 2.5 16.1021 2.5 16.6369 2.77248C17.1073 3.01217 17.4898 3.39462 17.7295 3.86502C18.002 4.3998 18.002 5.09987 18.002 6.5V13.5C18.002 14.9001 18.002 15.6002 17.7295 16.135C17.4898 16.6054 17.1073 16.9878 16.6369 17.2275C16.1021 17.5 15.4021 17.5 14.002 17.5H7.00195C5.60182 17.5 4.90176 17.5 4.36698 17.2275C3.89657 16.9878 3.51412 16.6054 3.27444 16.135C3.00195 15.6002 3.00195 14.9001 3.00195 13.5V6.5Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                _e = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 21 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("mask", {
                        id: "path-1-inside-1_9947_9193",
                        fill: "currentColor",
                        children: (0, t.jsx)("path", {
                            d: "M2.50195 3H17.502V18H2.50195V3Z"
                        })
                    }), (0, t.jsx)("path", {
                        d: "M17.502 16.5H2.50195V19.5H17.502V16.5Z",
                        fill: "currentColor",
                        mask: "url(#path-1-inside-1_9947_9193)"
                    }), (0, t.jsx)("mask", {
                        id: "path-3-inside-2_9947_9193",
                        fill: "currentColor",
                        children: (0, t.jsx)("path", {
                            d: "M2.50195 3H17.502V18H2.50195V3Z"
                        })
                    }), (0, t.jsx)("path", {
                        d: "M2.50195 3V2.25C2.08774 2.25 1.75195 2.58579 1.75195 3H2.50195ZM17.502 3H18.252C18.252 2.58579 17.9162 2.25 17.502 2.25V3ZM1.75195 3V4.875H3.25195V3H1.75195ZM1.75195 8.625V12.375H3.25195V8.625H1.75195ZM1.75195 16.125V18H3.25195V16.125H1.75195ZM18.252 18V16.125H16.752V18H18.252ZM18.252 12.375V8.625H16.752V12.375H18.252ZM18.252 4.875V3H16.752V4.875H18.252ZM17.502 2.25H15.627V3.75H17.502V2.25ZM11.877 2.25H8.12695V3.75H11.877V2.25ZM4.37695 2.25H2.50195V3.75H4.37695V2.25ZM2.50195 3V1.5C1.67353 1.5 1.00195 2.17157 1.00195 3L2.50195 3ZM17.502 3H19.002C19.002 2.17157 18.3304 1.5 17.502 1.5V3ZM1.00195 3V4.875H4.00195V3H1.00195ZM1.00195 8.625V12.375H4.00195V8.625H1.00195ZM1.00195 16.125V18H4.00195V16.125H1.00195ZM19.002 18V16.125H16.002V18H19.002ZM19.002 12.375V8.625H16.002V12.375H19.002ZM19.002 4.875V3H16.002V4.875H19.002ZM17.502 1.5H15.627V4.5H17.502V1.5ZM11.877 1.5H8.12695V4.5H11.877V1.5ZM4.37695 1.5H2.50195V4.5H4.37695V1.5Z",
                        fill: "currentColor",
                        mask: "url(#path-3-inside-2_9947_9193)"
                    })]
                })),
                Ee = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 65 30",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        filter: "url(#filter0_d_9947_9127)",
                        children: [(0, t.jsx)("rect", {
                            x: "2.50195",
                            y: "1",
                            width: "60",
                            height: "26",
                            fill: "white"
                        }), (0, t.jsx)("rect", {
                            x: "3.00195",
                            y: "1.5",
                            width: "59",
                            height: "25",
                            stroke: "#D0D5DD"
                        }), (0, t.jsx)("mask", {
                            id: "path-3-inside-1_9947_9127",
                            fill: "white",
                            children: (0, t.jsx)("path", {
                                d: "M2.50195 1H16.502V15H2.50195V1Z"
                            })
                        }), (0, t.jsx)("path", {
                            d: "M2.50195 1H16.502V15H2.50195V1Z",
                            fill: "white"
                        }), (0, t.jsx)("path", {
                            d: "M2.50195 1V-1H0.501953V1H2.50195ZM2.50195 3H16.502V-1H2.50195V3ZM4.50195 15V1H0.501953V15H4.50195Z",
                            fill: "#98A2B3",
                            mask: "url(#path-3-inside-1_9947_9127)"
                        }), (0, t.jsx)("rect", {
                            x: "15.502",
                            y: "11",
                            width: "34",
                            height: "6",
                            rx: "3",
                            fill: "#EAECF0"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsxs)("filter", {
                            id: "filter0_d_9947_9127",
                            x: "0.501953",
                            y: "0",
                            width: "64",
                            height: "30",
                            filterUnits: "userSpaceOnUse",
                            colorInterpolationFilters: "sRGB",
                            children: [(0, t.jsx)("feFlood", {
                                floodOpacity: "0",
                                result: "BackgroundImageFix"
                            }), (0, t.jsx)("feColorMatrix", { in: "SourceAlpha",
                                type: "matrix",
                                values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                                result: "hardAlpha"
                            }), (0, t.jsx)("feOffset", {
                                dy: "1"
                            }), (0, t.jsx)("feGaussianBlur", {
                                stdDeviation: "1"
                            }), (0, t.jsx)("feColorMatrix", {
                                type: "matrix",
                                values: "0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in2: "BackgroundImageFix",
                                result: "effect1_dropShadow_9947_9127"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in: "SourceGraphic",
                                in2: "effect1_dropShadow_9947_9127",
                                result: "shape"
                            })]
                        })
                    })]
                })),
                Re = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 65 30",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        filter: "url(#filter0_d_9947_9133)",
                        children: [(0, t.jsx)("rect", {
                            x: "2.50195",
                            y: "1",
                            width: "60",
                            height: "26",
                            rx: "6",
                            fill: "white"
                        }), (0, t.jsx)("rect", {
                            x: "3.00195",
                            y: "1.5",
                            width: "59",
                            height: "25",
                            rx: "5.5",
                            stroke: "#D0D5DD"
                        }), (0, t.jsx)("mask", {
                            id: "path-3-inside-1_9947_9133",
                            fill: "white",
                            children: (0, t.jsx)("path", {
                                d: "M2.50195 7C2.50195 3.68629 5.18824 1 8.50195 1H16.502V15H2.50195V7Z"
                            })
                        }), (0, t.jsx)("path", {
                            d: "M2.50195 7C2.50195 3.68629 5.18824 1 8.50195 1H16.502V15H2.50195V7Z",
                            fill: "white"
                        }), (0, t.jsx)("path", {
                            d: "M0.501953 7C0.501953 2.58172 4.08368 -1 8.50195 -1H16.502V3H8.50195C6.29281 3 4.50195 4.79086 4.50195 7H0.501953ZM16.502 15H2.50195H16.502ZM0.501953 15V7C0.501953 2.58172 4.08368 -1 8.50195 -1V3C6.29281 3 4.50195 4.79086 4.50195 7V15H0.501953ZM16.502 1V15V1Z",
                            fill: "#98A2B3",
                            mask: "url(#path-3-inside-1_9947_9133)"
                        }), (0, t.jsx)("rect", {
                            x: "15.502",
                            y: "11",
                            width: "34",
                            height: "6",
                            rx: "3",
                            fill: "#EAECF0"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsxs)("filter", {
                            id: "filter0_d_9947_9133",
                            x: "0.501953",
                            y: "0",
                            width: "64",
                            height: "30",
                            filterUnits: "userSpaceOnUse",
                            colorInterpolationFilters: "sRGB",
                            children: [(0, t.jsx)("feFlood", {
                                floodOpacity: "0",
                                result: "BackgroundImageFix"
                            }), (0, t.jsx)("feColorMatrix", { in: "SourceAlpha",
                                type: "matrix",
                                values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                                result: "hardAlpha"
                            }), (0, t.jsx)("feOffset", {
                                dy: "1"
                            }), (0, t.jsx)("feGaussianBlur", {
                                stdDeviation: "1"
                            }), (0, t.jsx)("feColorMatrix", {
                                type: "matrix",
                                values: "0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in2: "BackgroundImageFix",
                                result: "effect1_dropShadow_9947_9133"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in: "SourceGraphic",
                                in2: "effect1_dropShadow_9947_9133",
                                result: "shape"
                            })]
                        })
                    })]
                })),
                Qe = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 65 30",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        filter: "url(#filter0_d_9947_9139)",
                        children: [(0, t.jsx)("rect", {
                            x: "2.50195",
                            y: "1",
                            width: "60",
                            height: "26",
                            rx: "13",
                            fill: "white"
                        }), (0, t.jsx)("rect", {
                            x: "3.00195",
                            y: "1.5",
                            width: "59",
                            height: "25",
                            rx: "12.5",
                            stroke: "#D0D5DD"
                        }), (0, t.jsx)("mask", {
                            id: "path-3-inside-1_9947_9139",
                            fill: "white",
                            children: (0, t.jsx)("path", {
                                d: "M2.50195 14C2.50195 6.8203 8.32225 1 15.502 1H16.502V27H15.502C8.32225 27 2.50195 21.1797 2.50195 14Z"
                            })
                        }), (0, t.jsx)("path", {
                            d: "M2.50195 14C2.50195 6.8203 8.32225 1 15.502 1H16.502V27H15.502C8.32225 27 2.50195 21.1797 2.50195 14Z",
                            fill: "white"
                        }), (0, t.jsx)("path", {
                            d: "M0.501953 14C0.501953 5.71573 7.21768 -1 15.502 -1H16.502V3H15.502C9.42682 3 4.50195 7.92487 4.50195 14H0.501953ZM16.502 29H15.502C7.21768 29 0.501953 22.2843 0.501953 14H4.50195C4.50195 20.0751 9.42682 25 15.502 25H16.502V29ZM15.502 29C7.21768 29 0.501953 22.2843 0.501953 14C0.501953 5.71573 7.21768 -1 15.502 -1V3C9.42682 3 4.50195 7.92487 4.50195 14C4.50195 20.0751 9.42682 25 15.502 25V29ZM16.502 1V27V1Z",
                            fill: "#98A2B3",
                            mask: "url(#path-3-inside-1_9947_9139)"
                        }), (0, t.jsx)("rect", {
                            x: "15.502",
                            y: "11",
                            width: "34",
                            height: "6",
                            rx: "3",
                            fill: "#EAECF0"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsxs)("filter", {
                            id: "filter0_d_9947_9139",
                            x: "0.501953",
                            y: "0",
                            width: "64",
                            height: "30",
                            filterUnits: "userSpaceOnUse",
                            colorInterpolationFilters: "sRGB",
                            children: [(0, t.jsx)("feFlood", {
                                floodOpacity: "0",
                                result: "BackgroundImageFix"
                            }), (0, t.jsx)("feColorMatrix", { in: "SourceAlpha",
                                type: "matrix",
                                values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                                result: "hardAlpha"
                            }), (0, t.jsx)("feOffset", {
                                dy: "1"
                            }), (0, t.jsx)("feGaussianBlur", {
                                stdDeviation: "1"
                            }), (0, t.jsx)("feColorMatrix", {
                                type: "matrix",
                                values: "0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in2: "BackgroundImageFix",
                                result: "effect1_dropShadow_9947_9139"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in: "SourceGraphic",
                                in2: "effect1_dropShadow_9947_9139",
                                result: "shape"
                            })]
                        })
                    })]
                })),
                ye = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 24 25",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M7.258 16.8651L3.516 13.1231C2.689 12.2961 2.689 10.9561 3.516 10.1291L9.15 4.49512L15.886 11.2311L10.252 16.8651C9.425 17.6911 8.084 17.6911 7.258 16.8651Z",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M18.8831 14.2212C18.8831 14.2212 16.7661 16.5182 16.7661 17.9262C16.7661 19.0902 17.7191 20.0432 18.8831 20.0432C20.0471 20.0432 21.0001 19.0902 21.0001 17.9252C21.0001 16.5182 18.8831 14.2212 18.8831 14.2212V14.2212Z",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M9.15006 4.49721L8.06006 3.38721",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M15.8899 11.2273H2.92993",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M3.41992 21.8872H11.9999",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Se = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M11.6654 1.89154V5.33366C11.6654 5.80037 11.6654 6.03372 11.7562 6.21198C11.8361 6.36879 11.9636 6.49627 12.1204 6.57616C12.2986 6.66699 12.532 6.66699 12.9987 6.66699H16.4408M16.6654 8.32385V14.3337C16.6654 15.7338 16.6654 16.4339 16.3929 16.9686C16.1532 17.439 15.7707 17.8215 15.3003 18.0612C14.7656 18.3337 14.0655 18.3337 12.6654 18.3337H7.33203C5.9319 18.3337 5.23183 18.3337 4.69705 18.0612C4.22665 17.8215 3.8442 17.439 3.60451 16.9686C3.33203 16.4339 3.33203 15.7338 3.33203 14.3337V5.66699C3.33203 4.26686 3.33203 3.5668 3.60451 3.03202C3.8442 2.56161 4.22665 2.17916 4.69705 1.93948C5.23183 1.66699 5.9319 1.66699 7.33203 1.66699H10.0085C10.62 1.66699 10.9257 1.66699 11.2134 1.73607C11.4685 1.79731 11.7124 1.89832 11.9361 2.03539C12.1884 2.19 12.4046 2.40619 12.8369 2.83857L15.4938 5.49542C15.9262 5.9278 16.1424 6.14399 16.297 6.39628C16.434 6.61996 16.535 6.86382 16.5963 7.11891C16.6654 7.40663 16.6654 7.71237 16.6654 8.32385Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                Oe = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 32 32",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg",
                    xmlnsXlink: "http://www.w3.org/1999/xlink"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        clipPath: "url(#clip0_10125_21245)",
                        children: [(0, t.jsx)("rect", {
                            width: "32",
                            height: "32",
                            rx: "8",
                            fill: "white"
                        }), (0, t.jsx)("rect", {
                            width: "32",
                            height: "32",
                            rx: "8",
                            fill: "url(#pattern0_10125_21245)",
                            fillOpacity: "0.8"
                        }), (0, t.jsx)("line", {
                            x1: "32.7071",
                            y1: "-0.292893",
                            x2: "0.707105",
                            y2: "31.7071",
                            stroke: "#FF3D3D",
                            strokeWidth: "2"
                        })]
                    }), (0, t.jsx)("rect", {
                        x: "0.5",
                        y: "0.5",
                        width: "31",
                        height: "31",
                        rx: "7.5",
                        stroke: "black",
                        strokeOpacity: "0.1"
                    }), (0, t.jsxs)("defs", {
                        children: [(0, t.jsx)("pattern", {
                            id: "pattern0_10125_21245",
                            patternContentUnits: "objectBoundingBox",
                            width: "1",
                            height: "1",
                            children: (0, t.jsx)("use", {
                                xlinkHref: "#image0_10125_21245",
                                transform: "scale(0.00390625)"
                            })
                        }), (0, t.jsx)("clipPath", {
                            id: "clip0_10125_21245",
                            children: (0, t.jsx)("rect", {
                                width: "32",
                                height: "32",
                                rx: "8",
                                fill: "white"
                            })
                        }), (0, t.jsx)("image", {
                            id: "image0_10125_21245",
                            width: "256",
                            height: "256",
                            xlinkHref: "data:image/png;base64,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"
                        })]
                    })]
                })),
                Pe = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "12",
                    height: "13",
                    viewBox: "0 0 12 13",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#clip0_10454_18210)",
                        children: (0, t.jsx)("path", {
                            d: "M1 6.5H11M1 6.5C1 9.26142 3.23858 11.5 6 11.5M1 6.5C1 3.73858 3.23858 1.5 6 1.5M11 6.5C11 9.26142 8.76142 11.5 6 11.5M11 6.5C11 3.73858 8.76142 1.5 6 1.5M6 1.5C7.25064 2.86918 7.96138 4.64602 8 6.5C7.96138 8.35398 7.25064 10.1308 6 11.5M6 1.5C4.74936 2.86918 4.03862 4.64602 4 6.5C4.03862 8.35398 4.74936 10.1308 6 11.5",
                            stroke: "#15B79E",
                            strokeWidth: "1.2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_10454_18210",
                            children: (0, t.jsx)("rect", {
                                width: "12",
                                height: "12",
                                fill: "white",
                                transform: "translate(0 0.5)"
                            })
                        })
                    })]
                })),
                ze = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "24",
                    height: "24",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M16.1639 3H5.00689C3.89689 3 2.99889 3.904 3.00689 5.015L3.11089 19.015C3.11889 20.114 4.01189 21 5.11089 21H18.9919C20.0969 21 20.9919 20.105 20.9919 19V7.828C20.9919 7.298 20.7809 6.789 20.4059 6.414L17.5779 3.586C17.2029 3.211 16.6949 3 16.1639 3Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M15.9927 3V6.909C15.9927 7.461 15.5447 7.909 14.9927 7.909H8.99268C8.44068 7.909 7.99268 7.461 7.99268 6.909V3",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M7 21V13.286C7 12.576 7.576 12 8.286 12H15.715C16.424 12 17 12.576 17 13.286V21",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Fe = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        clipRule: "evenodd",
                        d: "M10 3H3v7h7V3zM5 8V5h3v3H5zM19 3h-7v7h7V3zm-5 5V5h3v3h-3zM3 12h7v7H3v-7zm2 2v3h3v-3H5zM19 12h-7v7h7v-7zm-5 5v-3h3v3h-3z",
                        fillRule: "evenodd"
                    })
                })),
                Ge = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M12.8 11a1.8 1.8 0 11-3.6 0 1.8 1.8 0 013.6 0zM4.8 11a1.8 1.8 0 11-3.6 0 1.8 1.8 0 013.6 0zM19 12.8a1.8 1.8 0 100-3.6 1.8 1.8 0 000 3.6z"
                    })
                })),
                Te = e => (0, t.jsx)(p, a(x({
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, e), {
                    children: (0, t.jsx)("path", {
                        clipRule: "evenodd",
                        d: "M2 16V2h14v4h4v14H6v-4H2zM4 4h10v10H4V4zm4 12v2h10V8h-2v8H8z",
                        fillRule: "evenodd"
                    })
                })),
                be = e => (0, t.jsxs)("svg", {
                    width: "66",
                    height: "32",
                    viewBox: "0 0 66 32",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg",
                    children: [(0, t.jsxs)("g", {
                        filter: "url(#filter0_d_10725_41401)",
                        children: [(0, t.jsx)("rect", {
                            x: "2",
                            y: "1",
                            width: "62",
                            height: "28",
                            rx: "4",
                            fill: "white"
                        }), (0, t.jsx)("rect", {
                            x: "2.5",
                            y: "1.5",
                            width: "61",
                            height: "27",
                            rx: "3.5",
                            stroke: "#EAECF0"
                        }), (0, t.jsx)("rect", {
                            x: "8",
                            y: "9",
                            width: "12",
                            height: "12",
                            rx: "4",
                            fill: "#D0D5DD"
                        }), (0, t.jsx)("rect", {
                            x: "22",
                            y: "15",
                            width: "36",
                            height: "6",
                            rx: "1",
                            fill: "#EAECF0"
                        }), (0, t.jsx)("rect", {
                            x: "22",
                            y: "9",
                            width: "27",
                            height: "4",
                            rx: "1",
                            fill: "#EAECF0"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsxs)("filter", {
                            id: "filter0_d_10725_41401",
                            x: "0",
                            y: "0",
                            width: "66",
                            height: "32",
                            filterUnits: "userSpaceOnUse",
                            colorInterpolationFilters: "sRGB",
                            children: [(0, t.jsx)("feFlood", {
                                floodOpacity: "0",
                                result: "BackgroundImageFix"
                            }), (0, t.jsx)("feColorMatrix", { in: "SourceAlpha",
                                type: "matrix",
                                values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                                result: "hardAlpha"
                            }), (0, t.jsx)("feOffset", {
                                dy: "1"
                            }), (0, t.jsx)("feGaussianBlur", {
                                stdDeviation: "1"
                            }), (0, t.jsx)("feColorMatrix", {
                                type: "matrix",
                                values: "0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in2: "BackgroundImageFix",
                                result: "effect1_dropShadow_10725_41401"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in: "SourceGraphic",
                                in2: "effect1_dropShadow_10725_41401",
                                result: "shape"
                            })]
                        })
                    })]
                }),
                Ue = e => (0, t.jsxs)("svg", {
                    width: "77",
                    height: "36",
                    viewBox: "0 0 77 36",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg",
                    children: [(0, t.jsxs)("g", {
                        filter: "url(#filter0_d_10725_41359)",
                        children: [(0, t.jsx)("rect", {
                            x: "2.33203",
                            y: "1",
                            width: "72",
                            height: "32",
                            rx: "4",
                            fill: "white"
                        }), (0, t.jsx)("rect", {
                            x: "2.83203",
                            y: "1.5",
                            width: "71",
                            height: "31",
                            rx: "3.5",
                            stroke: "#EAECF0"
                        }), (0, t.jsx)("rect", {
                            x: "10.332",
                            y: "8",
                            width: "18",
                            height: "18",
                            rx: "4",
                            fill: "#D0D5DD"
                        }), (0, t.jsx)("rect", {
                            x: "30.332",
                            y: "16",
                            width: "36",
                            height: "10",
                            rx: "2",
                            fill: "#EAECF0"
                        }), (0, t.jsx)("rect", {
                            x: "30.332",
                            y: "8",
                            width: "24",
                            height: "6",
                            rx: "2",
                            fill: "#EAECF0"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsxs)("filter", {
                            id: "filter0_d_10725_41359",
                            x: "0.332031",
                            y: "0",
                            width: "76",
                            height: "36",
                            filterUnits: "userSpaceOnUse",
                            colorInterpolationFilters: "sRGB",
                            children: [(0, t.jsx)("feFlood", {
                                floodOpacity: "0",
                                result: "BackgroundImageFix"
                            }), (0, t.jsx)("feColorMatrix", { in: "SourceAlpha",
                                type: "matrix",
                                values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                                result: "hardAlpha"
                            }), (0, t.jsx)("feOffset", {
                                dy: "1"
                            }), (0, t.jsx)("feGaussianBlur", {
                                stdDeviation: "1"
                            }), (0, t.jsx)("feColorMatrix", {
                                type: "matrix",
                                values: "0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in2: "BackgroundImageFix",
                                result: "effect1_dropShadow_10725_41359"
                            }), (0, t.jsx)("feBlend", {
                                mode: "normal",
                                in: "SourceGraphic",
                                in2: "effect1_dropShadow_10725_41359",
                                result: "shape"
                            })]
                        })
                    })]
                }),
                qe = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M10.6665 2.49658C11.0183 2.49658 11.3321 2.71744 11.451 3.04848L11.8745 4.22814C11.9417 4.41364 12.0722 4.56951 12.243 4.66832L13.4994 5.39362C13.67 5.49206 13.8699 5.52691 14.0638 5.492L15.2976 5.2694C15.6441 5.2064 15.9928 5.3679 16.1688 5.6729L16.8358 6.82338C17.0123 7.12756 16.9789 7.50978 16.7524 7.77878L15.9429 8.73585C15.8154 8.88634 15.7454 9.07716 15.7453 9.2744V10.725C15.7454 10.9222 15.8154 11.1131 15.9429 11.2636L16.7524 12.2206C16.9789 12.4896 17.0123 12.8719 16.8358 13.176L16.1688 14.3265C15.9929 14.6312 15.6446 14.7926 15.2984 14.73L14.0646 14.5074C13.8708 14.4725 13.6708 14.5074 13.5002 14.6058L12.2438 15.3311C12.0731 15.4299 11.9426 15.5858 11.8754 15.7713L11.4518 16.9509C11.3329 17.2823 11.0186 17.5031 10.6665 17.5028H9.33263C8.98089 17.5028 8.66702 17.282 8.54813 16.9509L8.12462 15.7713C8.05742 15.586 7.9273 15.4302 7.75697 15.3311L6.49978 14.6058C6.32918 14.5074 6.12923 14.4725 5.93538 14.5074L4.70153 14.73C4.35506 14.793 4.00637 14.6315 3.83034 14.3265L3.16339 13.176C2.98689 12.8719 3.02024 12.4896 3.24676 12.2206L4.05626 11.2636C4.18376 11.1131 4.25377 10.9222 4.25385 10.725V9.2744C4.25377 9.07716 4.18376 8.88634 4.05626 8.73585L3.2551 7.77878C3.02858 7.50978 2.99523 7.12756 3.17173 6.82338L3.83867 5.6729C4.01459 5.36823 4.36284 5.20678 4.70904 5.2694L5.94288 5.492C6.13673 5.52691 6.33668 5.49206 6.50728 5.39362L7.76447 4.66832C7.93481 4.56919 8.06493 4.4134 8.13213 4.22814L8.55564 3.04848C8.67358 2.72001 8.98364 2.49978 9.33263 2.49658H10.6665Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("circle", {
                        cx: "9.99965",
                        cy: "9.99965",
                        r: "2.29262",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Ne = e => (0, t.jsxs)(p, a(x({
                    width: "21px",
                    height: "20px",
                    viewBox: "0 0 21 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M7.88552 16.1424L8.37255 17.2378C8.51734 17.5639 8.75362 17.8409 9.05274 18.0353C9.35187 18.2298 9.70098 18.3332 10.0577 18.3332C10.4145 18.3332 10.7636 18.2298 11.0627 18.0353C11.3619 17.8409 11.5981 17.5639 11.7429 17.2378L12.23 16.1424C12.4033 15.7538 12.695 15.4297 13.0633 15.2165C13.4339 15.0027 13.8627 14.9116 14.2883 14.9563L15.48 15.0832C15.8347 15.1207 16.1927 15.0545 16.5105 14.8926C16.8284 14.7307 17.0924 14.4801 17.2707 14.1711C17.4492 13.8623 17.5342 13.5084 17.5155 13.1522C17.4967 12.7961 17.375 12.453 17.1651 12.1647L16.4596 11.1952C16.2084 10.8474 16.0741 10.4289 16.0763 9.99984C16.0762 9.57199 16.2117 9.15513 16.4633 8.8091L17.1688 7.83965C17.3787 7.5513 17.5004 7.20823 17.5192 6.85207C17.5379 6.4959 17.4529 6.14195 17.2744 5.83317C17.0962 5.5242 16.8321 5.27358 16.5142 5.11169C16.1964 4.94981 15.8384 4.88361 15.4837 4.92113L14.292 5.04799C13.8664 5.09268 13.4377 5.00161 13.067 4.7878C12.6979 4.57338 12.4062 4.24764 12.2337 3.85725L11.7429 2.76187C11.5981 2.43581 11.3619 2.15877 11.0627 1.96434C10.7636 1.76991 10.4145 1.66645 10.0577 1.6665C9.70098 1.66645 9.35187 1.76991 9.05274 1.96434C8.75362 2.15877 8.51734 2.43581 8.37255 2.76187L7.88552 3.85725C7.71296 4.24764 7.42124 4.57338 7.05218 4.7878C6.68153 5.00161 6.25274 5.09268 5.82718 5.04799L4.63181 4.92113C4.27709 4.88361 3.9191 4.94981 3.60124 5.11169C3.28339 5.27358 3.01933 5.5242 2.84107 5.83317C2.66259 6.14195 2.57756 6.4959 2.5963 6.85207C2.61503 7.20823 2.73673 7.5513 2.94663 7.83965L3.65218 8.8091C3.9038 9.15513 4.0393 9.57199 4.03922 9.99984C4.0393 10.4277 3.9038 10.8445 3.65218 11.1906L2.94663 12.16C2.73673 12.4484 2.61503 12.7914 2.5963 13.1476C2.57756 13.5038 2.66259 13.8577 2.84107 14.1665C3.0195 14.4753 3.2836 14.7258 3.6014 14.8877C3.91921 15.0495 4.27711 15.1158 4.63181 15.0785L5.82348 14.9517C6.24904 14.907 6.67783 14.9981 7.04848 15.2119C7.41892 15.4257 7.71199 15.7515 7.88552 16.1424Z",
                        stroke: "currentColor",
                        strokeWidth: "1.66667",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M10.0562 12.4998C11.437 12.4998 12.5562 11.3805 12.5562 9.99984C12.5562 8.61913 11.437 7.49984 10.0562 7.49984C8.67554 7.49984 7.55625 8.61913 7.55625 9.99984C7.55625 11.3805 8.67554 12.4998 10.0562 12.4998Z",
                        stroke: "currentColor",
                        strokeWidth: "1.66667",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Je = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M6.97967 13.0138L2.26562 17.7279M9.74441 5.53507L8.44367 6.83581C8.33757 6.94191 8.28452 6.99496 8.22407 7.03712C8.17042 7.07453 8.11255 7.10551 8.05166 7.12939C7.98306 7.1563 7.90949 7.17101 7.76235 7.20044L4.70863 7.81118C3.91504 7.9699 3.51825 8.04926 3.33262 8.25847C3.1709 8.44073 3.09705 8.68464 3.13051 8.92599C3.16891 9.20304 3.45505 9.48917 4.02731 10.0614L9.93209 15.9662C10.5044 16.5385 10.7905 16.8246 11.0675 16.863C11.3089 16.8965 11.5528 16.8226 11.7351 16.6609C11.9443 16.4753 12.0236 16.0785 12.1823 15.2849L12.7931 12.2312C12.8225 12.084 12.8372 12.0105 12.8641 11.9419C12.888 11.881 12.919 11.8231 12.9564 11.7695C12.9986 11.709 13.0516 11.656 13.1577 11.5499L14.4585 10.2491C14.5263 10.1813 14.5602 10.1474 14.5975 10.1177C14.6306 10.0914 14.6657 10.0677 14.7024 10.0467C14.7438 10.0231 14.7879 10.0042 14.876 9.9664L16.9547 9.07555C17.5611 8.81566 17.8643 8.68572 18.002 8.47573C18.1225 8.2921 18.1656 8.06835 18.1219 7.85312C18.072 7.607 17.8388 7.37374 17.3723 6.90722L13.0863 2.62127C12.6198 2.15475 12.3865 1.92149 12.1404 1.8716C11.9252 1.82797 11.7014 1.87106 11.5178 1.9915C11.3078 2.12923 11.1779 2.43244 10.918 3.03886L10.0271 5.11749C9.98934 5.20567 9.97044 5.24976 9.94682 5.2911C9.92584 5.32783 9.90209 5.36291 9.87578 5.39603C9.84617 5.43331 9.81225 5.46723 9.74441 5.53507Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                Xe = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#clip0_11103_658)",
                        children: (0, t.jsx)("path", {
                            d: "M6.66797 6.6665V4.33317C6.66797 3.39975 6.66797 2.93304 6.84962 2.57652C7.00941 2.26292 7.26438 2.00795 7.57798 1.84816C7.9345 1.6665 8.40121 1.6665 9.33464 1.6665H15.668C16.6014 1.6665 17.0681 1.6665 17.4246 1.84816C17.7382 2.00795 17.9932 2.26292 18.153 2.57652C18.3346 2.93304 18.3346 3.39975 18.3346 4.33317V10.6665C18.3346 11.5999 18.3346 12.0666 18.153 12.4232C17.9932 12.7368 17.7382 12.9917 17.4246 13.1515C17.0681 13.3332 16.6014 13.3332 15.668 13.3332H13.3346M4.33464 18.3332H10.668C11.6014 18.3332 12.0681 18.3332 12.4246 18.1515C12.7382 17.9917 12.9932 17.7368 13.153 17.4232C13.3346 17.0666 13.3346 16.5999 13.3346 15.6665V9.33317C13.3346 8.39975 13.3346 7.93304 13.153 7.57652C12.9932 7.26292 12.7382 7.00795 12.4246 6.84816C12.0681 6.6665 11.6014 6.6665 10.668 6.6665H4.33464C3.40121 6.6665 2.9345 6.6665 2.57798 6.84816C2.26438 7.00795 2.00941 7.26292 1.84962 7.57652C1.66797 7.93304 1.66797 8.39975 1.66797 9.33317V15.6665C1.66797 16.5999 1.66797 17.0666 1.84962 17.4232C2.00941 17.7368 2.26438 17.9917 2.57798 18.1515C2.9345 18.3332 3.40121 18.3332 4.33464 18.3332Z",
                            stroke: "currentColor",
                            strokeWidth: "1.5",
                            strokeLinecap: "round",
                            strokeLinejoin: "round"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_11103_658",
                            children: (0, t.jsx)("rect", {
                                width: "20",
                                height: "20",
                                fill: "white"
                            })
                        })
                    })]
                })),
                Ye = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        clipPath: "url(#clip0_9185_51110)",
                        children: [(0, t.jsx)("path", {
                            fillRule: "evenodd",
                            clipRule: "evenodd",
                            d: "M5.89062 12.6445V13.5348H6.64062H7.39062V12.6445H5.89062ZM12.5898 7.39258H13.5151V6.64258V5.89258H12.5898V7.39258ZM5.89062 15.5917H6.64062H7.39062V16.6201C7.39062 17.1724 7.83834 17.6201 8.39062 17.6201H9.41553V18.3701V19.1201H8.39062C7.00991 19.1201 5.89062 18.0008 5.89062 16.6201V15.5917ZM11.4653 18.3701V17.6201H13.5151V18.3701V19.1201H11.4653V18.3701ZM15.5649 18.3701V17.6201H16.5898C17.1421 17.6201 17.5898 17.1724 17.5898 16.6201V15.5917H18.3398H19.0898V16.6201C19.0898 18.0008 17.9706 19.1201 16.5898 19.1201H15.5649V18.3701ZM18.3398 13.5348H17.5898V11.4779H18.3398H19.0898V13.5348H18.3398ZM18.3398 9.42102H17.5898V8.39258C17.5898 7.84029 17.1421 7.39258 16.5898 7.39258H15.5649V6.64258V5.89258H16.5898C17.9706 5.89258 19.0898 7.01187 19.0898 8.39258V9.42102H18.3398Z",
                            fill: "#98A2B3"
                        }), (0, t.jsx)("rect", {
                            x: "1.64062",
                            y: "1.66699",
                            width: "11.6992",
                            height: "11.7275",
                            rx: "1.75",
                            stroke: "#344054",
                            strokeWidth: "1.5"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_9185_51110",
                            children: (0, t.jsx)("rect", {
                                width: "20",
                                height: "20",
                                fill: "white"
                            })
                        })
                    })]
                })),
                Ke = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsxs)("g", {
                        clipPath: "url(#clip0_9185_51118)",
                        children: [(0, t.jsx)("rect", {
                            x: "1.64844",
                            y: "1.64844",
                            width: "11.6992",
                            height: "11.7275",
                            rx: "1.75",
                            stroke: "#98A2B3",
                            strokeWidth: "1.5",
                            strokeDasharray: "3 3 3 3"
                        }), (0, t.jsx)("path", {
                            fillRule: "evenodd",
                            clipRule: "evenodd",
                            d: "M5.89844 15.0322V16.6016C5.89844 17.9823 7.01773 19.1016 8.39844 19.1016H16.5977C17.9784 19.1016 19.0977 17.9823 19.0977 16.6016V8.37402C19.0977 6.99331 17.9784 5.87402 16.5977 5.87402H15.0215V7.37402H16.5977C17.1499 7.37402 17.5977 7.82174 17.5977 8.37402V16.6016C17.5977 17.1538 17.1499 17.6016 16.5977 17.6016H8.39844C7.84615 17.6016 7.39844 17.1538 7.39844 16.6016V15.0322H5.89844Z",
                            fill: "#344054"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_9185_51118",
                            children: (0, t.jsx)("rect", {
                                width: "20",
                                height: "20",
                                fill: "white"
                            })
                        })
                    })]
                })),
                $e = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "24px",
                    height: "24px",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M12 3V21M22 12H15.5M15.5 12L19.5 16M15.5 12L19.5 8M2 12H8.5M8.5 12L4.5 16M8.5 12L4.5 8",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                eo = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "24px",
                    height: "24px",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M3 3V21M21 12H7M7 12L14 19M7 12L14 5",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                oo = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "24px",
                    height: "24px",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M21 21V3M3 12H17M17 12L10 5M17 12L10 19",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                ro = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "24px",
                    height: "24px",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M3 12H21M12 2V8.5M12 8.5L16 4.5M12 8.5L8 4.5M12 22V15.5M12 15.5L16 19.5M12 15.5L8 19.5",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                to = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "24px",
                    height: "24px",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M21 3H3M12 21V7M12 7L5 14M12 7L19 14",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                io = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "24px",
                    height: "24px",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M3 21H21M12 3V17M12 17L19 10M12 17L5 10",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                no = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "21px",
                    height: "21px",
                    viewBox: "0 0 21 21",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M15.5324 5.2915L4.92578 15.8982L15.5324 5.2915Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M10.2285 3.09473V3.09473C6.08602 3.09473 2.72852 6.45223 2.72852 10.5947V10.5947C2.72852 14.7372 6.08602 18.0947 10.2285 18.0947V18.0947C14.371 18.0947 17.7285 14.7372 17.7285 10.5947V10.5947C17.7285 6.45223 14.371 3.09473 10.2285 3.09473Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                so = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M9.99935 4.1665V15.8332M4.16602 9.99984H15.8327",
                        stroke: "currentColor",
                        strokeWidth: "1.66667",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                lo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M6.25 2.5H13.7533C15.8225 2.5 17.5 4.1775 17.5 6.24667V13.7542C17.5 15.8225 15.8225 17.5 13.7533 17.5H6.24667C4.1775 17.5 2.5 15.8225 2.5 13.7533V6.25C2.5 4.17917 4.17917 2.5 6.25 2.5V2.5Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M6.5666 13.2833L6.29993 13.75",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M13.6996 13.75L10.9746 8.9917",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M9.1582 5.83337L9.99987 7.30004",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M10.842 5.83337L7.54199 11.5917",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M10.5163 11.5917H5.83301",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M14.1663 11.5917H12.458",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                ho = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M6.25 2.5H13.7533C15.8225 2.5 17.5 4.1775 17.5 6.24667V13.7542C17.5 15.8225 15.8225 17.5 13.7533 17.5H6.24667C4.1775 17.5 2.5 15.8225 2.5 13.7533V6.25C2.5 4.17917 4.17917 2.5 6.25 2.5V2.5Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M9.25 10.7502H13.75",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M13.75 7H12.9625C11.7408 7 10.75 7.99083 10.75 9.2125V10V17.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                co = e => (0, t.jsxs)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M7.41211 8.54107V5.62567C7.41212 5.47647 7.4919 5.33866 7.62128 5.26435C7.75066 5.19004 7.90989 5.19057 8.03878 5.26574L10.5387 6.72344C10.6667 6.7981 10.7454 6.93516 10.7454 7.08337C10.7454 7.23158 10.6667 7.36864 10.5387 7.4433L8.03875 8.901C7.90987 8.97616 7.75064 8.97669 7.62127 8.90238C7.4919 8.82807 7.41213 8.69027 7.41211 8.54107Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M7.49935 12.4998H4.16602C2.7853 12.4998 1.66602 11.3805 1.66602 9.99984V4.1665C1.66602 2.78579 2.7853 1.6665 4.16602 1.6665H13.3327C14.7134 1.6665 15.8327 2.78579 15.8327 4.1665V6.6665",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M12.5 14.1663L12.0121 13.6784C11.7911 13.4574 11.4913 13.3332 11.1787 13.3332C10.8661 13.3332 10.5663 13.4574 10.3452 13.6784V13.6784C9.94798 14.0756 9.88633 14.6981 10.1979 15.1655L11.8146 17.5909C12.1237 18.0546 12.6442 18.3332 13.2015 18.3332H16.3649C17.2054 18.3332 17.9144 17.7072 18.0187 16.8732L18.3203 14.4591C18.4314 13.5698 17.8193 12.7518 16.9348 12.6076L15 12.2921V10.4165C15 9.72615 14.4403 9.1665 13.75 9.1665V9.1665C13.0596 9.1665 12.5 9.72615 12.5 10.4165V14.1663Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Co = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("rect", {
                        x: "2",
                        y: "2",
                        width: "16",
                        height: "16",
                        rx: "1.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5"
                    }), (0, t.jsxs)("g", {
                        clipPath: "url(#clip0_9126_45546)",
                        children: [(0, t.jsx)("path", {
                            d: "M9.35566 7.61591V11.9119C9.35566 13.3279 8.70766 14.0959 7.33966 14.0959C6.04366 14.0959 5.37166 13.3279 5.37166 11.9839V11.7679H6.61966V12.0679C6.61966 12.6679 6.88366 12.8959 7.30366 12.8959C7.77166 12.8959 8.03566 12.6679 8.03566 11.8639V10.3279C7.80766 10.8079 7.39966 11.0719 6.81166 11.0719C5.83966 11.0719 5.34766 10.3879 5.34766 9.15191V7.61591C5.34766 6.27191 6.05566 5.50391 7.35166 5.50391C8.64766 5.50391 9.35566 6.27191 9.35566 7.61591ZM7.35166 9.87191C7.77166 9.87191 8.03566 9.64391 8.03566 9.04391V7.53191C8.03566 6.93191 7.77166 6.71591 7.35166 6.71591C6.93166 6.71591 6.66766 6.93191 6.66766 7.53191V9.04391C6.66766 9.64391 6.93166 9.87191 7.35166 9.87191Z",
                            fill: "currentColor"
                        }), (0, t.jsx)("path", {
                            d: "M10.168 8.4761V7.2761L12.568 1.6001H14.008V7.2761H14.632V8.4761H14.008V10.0001H12.712V8.4761H10.168ZM11.38 7.2761H12.712V4.1201L11.38 7.2761Z",
                            fill: "#98A2B3"
                        }), (0, t.jsx)("path", {
                            d: "M10.4199 16.2881H11.6679V17.0681C11.6679 17.6681 11.9319 17.8841 12.3519 17.8841C12.7719 17.8841 13.0359 17.6681 13.0359 17.0681V15.2201C13.0359 14.6201 12.7719 14.3921 12.3519 14.3921C11.9319 14.3921 11.6679 14.6201 11.6679 15.2201V15.4721H10.4199L10.6599 10.6001H14.1399V11.8001H11.8479L11.7399 13.8041C11.9799 13.4081 12.3639 13.1921 12.8919 13.1921C13.8639 13.1921 14.3559 13.8761 14.3559 15.1121V16.9841C14.3559 18.3281 13.6839 19.0961 12.3879 19.0961C11.0919 19.0961 10.4199 18.3281 10.4199 16.9841V16.2881Z",
                            fill: "currentColor"
                        })]
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_9126_45546",
                            children: (0, t.jsx)("rect", {
                                width: "10",
                                height: "12",
                                fill: "white",
                                transform: "translate(5 4)"
                            })
                        })
                    })]
                })),
                xo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M4.41018 13.0157L13.0152 4.41074C13.3402 4.08574 13.8677 4.08574 14.1927 4.41074L15.5893 5.80741C15.9143 6.13241 15.9143 6.65991 15.5893 6.98491L6.98352 15.5891C6.82768 15.7457 6.61602 15.8332 6.39518 15.8332H4.16602V13.6041C4.16602 13.3832 4.25352 13.1716 4.41018 13.0157Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M11.459 5.9668L14.034 8.5418",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                ao = e => (0, t.jsx)(p, a(x({
                    width: "24px",
                    height: "24px",
                    viewBox: "0 0 24 24",
                    xmlns: "http://www.w3.org/2000/svg",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M7 15L12 20L17 15M7 9L12 4L17 9",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                po = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M14.8333 8.33333C15.7668 8.33333 16.2335 8.33333 16.59 8.15168C16.9036 7.99189 17.1586 7.73692 17.3183 7.42332C17.5 7.0668 17.5 6.60009 17.5 5.66667V5.16667C17.5 4.23325 17.5 3.76654 17.3183 3.41002C17.1586 3.09641 16.9036 2.84145 16.59 2.68166C16.2335 2.5 15.7668 2.5 14.8333 2.5L5.16667 2.5C4.23325 2.5 3.76654 2.5 3.41002 2.68166C3.09641 2.84144 2.84144 3.09641 2.68166 3.41002C2.5 3.76654 2.5 4.23325 2.5 5.16667L2.5 5.66667C2.5 6.60009 2.5 7.0668 2.68166 7.42332C2.84144 7.73692 3.09641 7.99189 3.41002 8.15168C3.76654 8.33333 4.23325 8.33333 5.16667 8.33333L14.8333 8.33333Z",
                        stroke: "currentColor",
                        strokeWidth: "1.66667",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M14.8333 17.5C15.7668 17.5 16.2335 17.5 16.59 17.3183C16.9036 17.1586 17.1586 16.9036 17.3183 16.59C17.5 16.2335 17.5 15.7668 17.5 14.8333V14.3333C17.5 13.3999 17.5 12.9332 17.3183 12.5767C17.1586 12.2631 16.9036 12.0081 16.59 11.8483C16.2335 11.6667 15.7668 11.6667 14.8333 11.6667L5.16667 11.6667C4.23325 11.6667 3.76654 11.6667 3.41002 11.8483C3.09641 12.0081 2.84144 12.2631 2.68166 12.5767C2.5 12.9332 2.5 13.3999 2.5 14.3333L2.5 14.8333C2.5 15.7668 2.5 16.2335 2.68166 16.59C2.84144 16.9036 3.09641 17.1586 3.41002 17.3183C3.76654 17.5 4.23325 17.5 5.16667 17.5H14.8333Z",
                        stroke: "currentColor",
                        strokeWidth: "1.66667",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Ao = e => (0, t.jsxs)(p, a(x({
                    width: "21px",
                    height: "20px",
                    viewBox: "0 0 21 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#clip0_11525_7444)",
                        children: (0, t.jsx)("path", {
                            fillRule: "evenodd",
                            clipRule: "evenodd",
                            d: "M10.0503 4.27985C8.38416 2.332 5.60578 1.80804 3.51824 3.59168C1.43069 5.37532 1.1368 8.35748 2.77615 10.467C4.13917 12.2209 8.26413 15.9201 9.61607 17.1174C9.76733 17.2513 9.84295 17.3183 9.93117 17.3446C10.0082 17.3676 10.0924 17.3676 10.1694 17.3446C10.2576 17.3183 10.3332 17.2513 10.4845 17.1174C11.8364 15.9201 15.9614 12.2209 17.3244 10.467C18.9638 8.35748 18.7058 5.35656 16.5823 3.59168C14.4589 1.8268 11.7164 2.332 10.0503 4.27985Z",
                            stroke: "currentColor",
                            strokeWidth: "1.66667",
                            strokeLinecap: "round",
                            strokeLinejoin: "round"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_11525_7444",
                            children: (0, t.jsx)("rect", {
                                width: "20",
                                height: "20",
                                fill: "white",
                                transform: "translate(0.0566406)"
                            })
                        })
                    })]
                })),
                ko = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "21px",
                    viewBox: "0 0 20 21",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M15.4196 10.5001C15.4196 10.7303 15.233 10.9169 15.0028 10.9169C14.7726 10.9169 14.5859 10.7303 14.5859 10.5001C14.5859 10.2699 14.7726 10.0833 15.0028 10.0833C15.233 10.0833 15.4196 10.2699 15.4196 10.5001",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M10.4177 10.5001C10.4177 10.7303 10.231 10.9169 10.0008 10.9169C9.77061 10.9169 9.58398 10.7303 9.58398 10.5001C9.58398 10.2699 9.77061 10.0833 10.0008 10.0833C10.231 10.0833 10.4177 10.2699 10.4177 10.5001",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M5.41571 10.5001C5.41571 10.7303 5.22909 10.9169 4.99887 10.9169C4.76866 10.9169 4.58203 10.7303 4.58203 10.5001C4.58203 10.2699 4.76866 10.0833 4.99887 10.0833C5.22909 10.0833 5.41571 10.2699 5.41571 10.5001",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                uo = e => (0, t.jsx)(p, a(x({
                    width: "16px",
                    height: "17px",
                    viewBox: "0 0 16 17",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M14.4004 3.12758L8.00039 3.12758M1.60039 3.12758L8.00039 3.12758M8.00039 3.12758L8.00039 10.4583M8.00039 10.4583L5.20039 7.65832M8.00039 10.4583L10.8004 7.65832M8.82218 14.1003C8.74249 14.1003 8.24112 14.1003 8.00039 14.1003L7.1288 14.1003M3.39339 14.1003C3.3137 14.1003 2.81232 14.1003 2.5716 14.1003L1.7 14.1003M14.3008 14.1003C14.2211 14.1003 13.7197 14.1003 13.479 14.1003L12.6074 14.1003",
                        stroke: "currentColor",
                        strokeWidth: "1.2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                jo = e => (0, t.jsx)(p, a(x({
                    width: "16px",
                    height: "17px",
                    viewBox: "0 0 16 17",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M1.59961 13.8724H7.99961M14.3996 13.8724H7.99961M7.99961 13.8724V6.54168M7.99961 6.54168L10.7996 9.34168M7.99961 6.54168L5.19961 9.34168M7.17782 2.89966C7.25751 2.89966 7.75888 2.89966 7.99961 2.89966H8.8712M12.6066 2.89966C12.6863 2.89966 13.1877 2.89966 13.4284 2.89966H14.3M1.69922 2.89966C1.77891 2.89966 2.28028 2.89966 2.52101 2.89966H3.39261",
                        stroke: "currentColor",
                        strokeWidth: "1.2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                wo = e => (0, t.jsxs)(p, a(x({
                    width: "17px",
                    height: "17px",
                    viewBox: "0 0 17 17",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M6.34833 11.139C5.86687 10.6575 5.86687 9.87691 6.34833 9.39544L9.8826 5.86118C10.3641 5.37971 11.1447 5.37971 11.6261 5.86118V5.86118C12.1076 6.34264 12.1076 7.12325 11.6261 7.60471L8.09187 11.139C7.6104 11.6204 6.8298 11.6204 6.34833 11.139V11.139Z",
                        stroke: "currentColor",
                        strokeWidth: "1.2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M10.8203 9.66663L7.82031 6.66663",
                        stroke: "currentColor",
                        strokeWidth: "1.2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("rect", {
                        x: "3.00781",
                        y: "2.5",
                        width: "12",
                        height: "12",
                        rx: "3.33333",
                        stroke: "currentColor",
                        strokeWidth: "1.2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Lo = e => (0, t.jsxs)(p, a(x({
                    width: "17px",
                    height: "17px",
                    viewBox: "0 0 17 17",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("rect", {
                        x: "2.46094",
                        y: "2.5",
                        width: "12",
                        height: "12",
                        rx: "2.66667",
                        stroke: "currentColor",
                        strokeWidth: "1.2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("rect", {
                        x: "3.79688",
                        y: "3.83533",
                        width: "9.32945",
                        height: "9.32945",
                        rx: "2",
                        fill: "url(#paint0_linear_11711_2930)"
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsxs)("linearGradient", {
                            id: "paint0_linear_11711_2930",
                            x1: "8.4616",
                            y1: "3.83533",
                            x2: "8.4616",
                            y2: "13.1648",
                            gradientUnits: "userSpaceOnUse",
                            children: [(0, t.jsx)("stop", {
                                offset: "0.114016",
                                stopColor: "currentColor"
                            }), (0, t.jsx)("stop", {
                                offset: "0.782949",
                                stopColor: "currentColor",
                                stopOpacity: "0"
                            })]
                        })
                    })]
                })),
                Mo = e => (0, t.jsx)(p, a(x({
                    width: "21px",
                    height: "20px",
                    viewBox: "0 0 21 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M4.30469 14.1667H17.638M17.638 14.1667L14.3047 10.8333M17.638 14.1667L14.3047 17.5M17.638 5.83333H4.30469M4.30469 5.83333L7.63802 2.5M4.30469 5.83333L7.63802 9.16667",
                        stroke: "currentColor",
                        strokeWidth: "1.35",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                fo = e => (0, t.jsx)(p, a(x({
                    width: "16px",
                    height: "16px",
                    viewBox: "0 0 16 16",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M8.29206 2.83024L7.01339 5.10291C7.06121 5.10821 7.10722 5.12416 7.14806 5.14958C7.53771 5.39472 7.90932 5.66741 8.26006 5.96558C8.32743 6.02286 8.36928 6.10457 8.3764 6.19271C8.38353 6.28085 8.35535 6.36821 8.29806 6.43558C8.24077 6.50294 8.15907 6.54479 8.07093 6.55192C7.98279 6.55904 7.89543 6.53086 7.82806 6.47358C7.50123 6.19624 7.15526 5.94228 6.79273 5.71358C6.76191 5.69274 6.73485 5.66682 6.71273 5.63691L2.57139 13.0002H10.9901C10.9794 12.6539 10.9489 12.3084 10.8987 11.9656C10.8889 11.8799 10.9127 11.7937 10.9652 11.7252C11.0176 11.6566 11.0946 11.6111 11.1799 11.5982C11.2652 11.5852 11.3522 11.6058 11.4226 11.6557C11.493 11.7056 11.5413 11.7808 11.5574 11.8656C11.6129 12.2415 11.6463 12.6204 11.6574 13.0002H14.0014C14.0898 13.0002 14.1746 13.0354 14.2371 13.0979C14.2996 13.1604 14.3347 13.2452 14.3347 13.3336C14.3347 13.422 14.2996 13.5068 14.2371 13.5693C14.1746 13.6318 14.0898 13.6669 14.0014 13.6669H2.00139C1.9432 13.6669 1.88601 13.6517 1.83553 13.6228C1.78504 13.5938 1.74301 13.5522 1.71361 13.5019C1.68422 13.4517 1.66849 13.3947 1.66798 13.3365C1.66748 13.2783 1.68221 13.221 1.71073 13.1702L7.71073 2.50358C7.73218 2.46541 7.76093 2.43183 7.79536 2.40478C7.82978 2.37772 7.8692 2.35771 7.91136 2.34588C7.95351 2.33406 7.99759 2.33066 8.04106 2.33586C8.08454 2.34107 8.12656 2.35479 8.16473 2.37624C8.2029 2.39769 8.23647 2.42645 8.26353 2.46087C8.29059 2.4953 8.3106 2.53471 8.32242 2.57687C8.33425 2.61903 8.33765 2.6631 8.33244 2.70658C8.32723 2.75005 8.31351 2.79207 8.29206 2.83024ZM10.6141 10.7136C10.6402 10.7976 10.6985 10.8679 10.7762 10.9091C10.8539 10.9503 10.9448 10.9592 11.029 10.9336C11.1133 10.9081 11.184 10.8503 11.2257 10.7729C11.2675 10.6955 11.277 10.6046 11.2521 10.5202C11.1179 10.0796 10.9524 9.64921 10.7567 9.23224C10.7395 9.19062 10.7141 9.15291 10.6819 9.1214C10.6497 9.08989 10.6114 9.06523 10.5695 9.0489C10.5275 9.03258 10.4826 9.02494 10.4376 9.02644C10.3926 9.02793 10.3483 9.03854 10.3075 9.05761C10.2667 9.07668 10.2302 9.10383 10.2002 9.13741C10.1702 9.17098 10.1473 9.2103 10.1329 9.25298C10.1184 9.29565 10.1128 9.34081 10.1164 9.38571C10.1199 9.43062 10.1325 9.47434 10.1534 9.51424C10.3354 9.90274 10.4893 10.3031 10.6141 10.7136ZM9.24473 6.93091C9.18612 6.86461 9.10356 6.8243 9.01523 6.81886C8.92691 6.81342 8.84003 6.8433 8.77373 6.90191C8.70742 6.96052 8.66712 7.04307 8.66168 7.1314C8.65624 7.21973 8.68612 7.30661 8.74473 7.37291C9.02892 7.69404 9.29002 8.03489 9.52606 8.39291C9.54943 8.43086 9.58017 8.46374 9.61647 8.4896C9.65276 8.51546 9.69388 8.53378 9.73738 8.54348C9.78088 8.55317 9.82588 8.55405 9.86973 8.54605C9.91357 8.53805 9.95536 8.52134 9.99264 8.49691C10.0299 8.47247 10.0619 8.44081 10.0867 8.4038C10.1116 8.36679 10.1287 8.32517 10.1372 8.28142C10.1456 8.23766 10.1452 8.19265 10.136 8.14905C10.1268 8.10545 10.1089 8.06414 10.0834 8.02758C9.83022 7.64258 9.54995 7.2761 9.24473 6.93091Z",
                        fill: "currentColor"
                    })
                })),
                go = e => (0, t.jsx)(p, a(x({
                    width: "21px",
                    height: "20px",
                    viewBox: "0 0 21 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M10.9167 2.50001H7C5.59987 2.50001 4.8998 2.50001 4.36502 2.77249C3.89462 3.01217 3.51217 3.39462 3.27248 3.86503C3 4.39981 3 5.09987 3 6.5V13.5C3 14.9001 3 15.6002 3.27248 16.135C3.51217 16.6054 3.89462 16.9878 4.36502 17.2275C4.8998 17.5 5.59987 17.5 7 17.5H14.6667C15.4416 17.5 15.8291 17.5 16.147 17.4148C17.0098 17.1837 17.6836 16.5098 17.9148 15.6471C18 15.3291 18 14.9416 18 14.1667M16.3333 6.66667V1.66667M13.8333 4.16667H18.8333M9.25 7.08334C9.25 8.00381 8.50381 8.75 7.58333 8.75C6.66286 8.75 5.91667 8.00381 5.91667 7.08334C5.91667 6.16286 6.66286 5.41667 7.58333 5.41667C8.50381 5.41667 9.25 6.16286 9.25 7.08334ZM12.9917 9.93179L5.94262 16.34C5.54614 16.7005 5.34789 16.8807 5.33036 17.0368C5.31516 17.1722 5.36704 17.3064 5.46932 17.3963C5.58732 17.5 5.85523 17.5 6.39107 17.5H14.2133C15.4126 17.5 16.0123 17.5 16.4833 17.2985C17.0745 17.0456 17.5456 16.5745 17.7985 15.9833C18 15.5123 18 14.9126 18 13.7133C18 13.3098 18 13.108 17.9559 12.9201C17.9005 12.684 17.7941 12.4628 17.6444 12.272C17.5252 12.1202 17.3677 11.9941 17.0526 11.7421L14.7215 9.87722C14.4062 9.62493 14.2485 9.49878 14.0748 9.45426C13.9218 9.41502 13.7607 9.42011 13.6104 9.46891C13.44 9.52429 13.2905 9.66013 12.9917 9.93179Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                vo = e => (0, t.jsx)(p, a(x({
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M13.3333 5.00033V4.33366C13.3333 3.40024 13.3333 2.93353 13.1517 2.57701C12.9919 2.2634 12.7369 2.00844 12.4233 1.84865C12.0668 1.66699 11.6001 1.66699 10.6667 1.66699H9.33333C8.39991 1.66699 7.9332 1.66699 7.57668 1.84865C7.26308 2.00844 7.00811 2.2634 6.84832 2.57701C6.66667 2.93353 6.66667 3.40024 6.66667 4.33366V5.00033M8.33333 9.58366V13.7503M11.6667 9.58366V13.7503M2.5 5.00033H17.5M15.8333 5.00033V14.3337C15.8333 15.7338 15.8333 16.4339 15.5608 16.9686C15.3212 17.439 14.9387 17.8215 14.4683 18.0612C13.9335 18.3337 13.2335 18.3337 11.8333 18.3337H8.16667C6.76654 18.3337 6.06647 18.3337 5.53169 18.0612C5.06129 17.8215 4.67883 17.439 4.43915 16.9686C4.16667 16.4339 4.16667 15.7338 4.16667 14.3337V5.00033",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                Ho = e => (0, t.jsx)(p, a(x({
                    width: "21px",
                    height: "20px",
                    viewBox: "0 0 21 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M2.55811 6.45142L6.95142 2.05811C7.19556 1.81397 7.59106 1.81397 7.83521 2.05811C8.07935 2.30226 8.07935 2.69776 7.83521 2.94191L3.4419 7.33522C3.31983 7.45728 3.15991 7.51832 3.00001 7.51832C2.8401 7.51832 2.68018 7.45729 2.55811 7.33522C2.31397 7.09107 2.31396 6.69557 2.55811 6.45142ZM13.1387 2.9419C13.3829 2.69776 13.3829 2.30225 13.1387 2.05811C12.8946 1.81396 12.4991 1.81396 12.255 2.05811L2.55811 11.755C2.31396 11.9991 2.31396 12.3946 2.55811 12.6388C2.68017 12.7608 2.84009 12.8219 3 12.8219C3.15991 12.8219 3.31982 12.7608 3.44189 12.6388L13.1387 2.9419ZM17.692 2.80803C17.4478 2.56389 17.0523 2.56389 16.8082 2.80803L3.30802 16.3082C3.06388 16.5523 3.06388 16.9478 3.30802 17.192C3.43009 17.314 3.59001 17.3751 3.74991 17.3751C3.90982 17.3751 4.06974 17.3141 4.19181 17.192L17.692 3.69182C17.9361 3.44768 17.9361 3.05217 17.692 2.80803ZM17.5581 7.36126L7.86125 17.0581C7.61711 17.3023 7.61711 17.6978 7.86125 17.9419C7.98331 18.064 8.14323 18.125 8.30314 18.125C8.46305 18.125 8.62296 18.064 8.74503 17.9419L18.4419 8.24505C18.686 8.00091 18.686 7.6054 18.4419 7.36126C18.1977 7.11712 17.8022 7.11712 17.5581 7.36126ZM17.5581 12.6648L13.1648 17.0581C12.9207 17.3023 12.9207 17.6978 13.1648 17.9419C13.2869 18.064 13.4468 18.125 13.6067 18.125C13.7666 18.125 13.9265 18.064 14.0486 17.9419L18.4419 13.5486C18.686 13.3045 18.686 12.9089 18.4419 12.6648C18.1977 12.4207 17.8022 12.4207 17.5581 12.6648Z",
                        fill: "currentColor"
                    })
                })),
                Vo = e => (0, t.jsxs)(p, a(x({
                    width: "16px",
                    height: "16px",
                    viewBox: "0 0 16 16",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#clip0_11747_12111)",
                        children: (0, t.jsx)("path", {
                            d: "M6.06065 6.00004C6.21739 5.55449 6.52675 5.17878 6.93395 4.93946C7.34116 4.70015 7.81991 4.61267 8.28544 4.69252C8.75096 4.77236 9.1732 5.01439 9.47737 5.37573C9.78154 5.73706 9.94802 6.19439 9.94732 6.66671C9.94732 8.00004 7.94732 8.66671 7.94732 8.66671M8.00065 11.3334H8.00732M14.6673 8.00004C14.6673 11.6819 11.6826 14.6667 8.00065 14.6667C4.31875 14.6667 1.33398 11.6819 1.33398 8.00004C1.33398 4.31814 4.31875 1.33337 8.00065 1.33337C11.6826 1.33337 14.6673 4.31814 14.6673 8.00004Z",
                            stroke: "currentColor",
                            strokeWidth: "1.33333",
                            strokeLinecap: "round",
                            strokeLinejoin: "round"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_11747_12111",
                            children: (0, t.jsx)("rect", {
                                width: "16",
                                height: "16",
                                fill: "white"
                            })
                        })
                    })]
                })),
                Bo = e => (0, t.jsx)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M11.6654 1.8913V5.33342C11.6654 5.80013 11.6654 6.03348 11.7562 6.21174C11.8361 6.36854 11.9636 6.49603 12.1204 6.57592C12.2986 6.66675 12.532 6.66675 12.9987 6.66675H16.4408M16.6654 8.3236V14.3334C16.6654 15.7335 16.6654 16.4336 16.3929 16.9684C16.1532 17.4388 15.7707 17.8212 15.3003 18.0609C14.7656 18.3334 14.0655 18.3334 12.6654 18.3334H7.33203C5.9319 18.3334 5.23183 18.3334 4.69705 18.0609C4.22665 17.8212 3.8442 17.4388 3.60451 16.9684C3.33203 16.4336 3.33203 15.7335 3.33203 14.3334V5.66675C3.33203 4.26662 3.33203 3.56655 3.60451 3.03177C3.8442 2.56137 4.22665 2.17892 4.69705 1.93923C5.23183 1.66675 5.9319 1.66675 7.33203 1.66675H10.0085C10.62 1.66675 10.9257 1.66675 11.2134 1.73582C11.4685 1.79707 11.7124 1.89808 11.9361 2.03515C12.1884 2.18975 12.4046 2.40594 12.8369 2.83832L15.4938 5.49517C15.9262 5.92755 16.1424 6.14374 16.297 6.39604C16.434 6.61972 16.535 6.86358 16.5963 7.11867C16.6654 7.40639 16.6654 7.71213 16.6654 8.3236Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                Io = e => (0, t.jsx)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M1.66797 5.83325L8.47207 10.5961C9.02304 10.9818 9.29853 11.1746 9.59819 11.2493C9.86288 11.3153 10.1397 11.3153 10.4044 11.2493C10.7041 11.1746 10.9796 10.9818 11.5305 10.5961L18.3346 5.83325M5.66797 16.6666H14.3346C15.7348 16.6666 16.4348 16.6666 16.9696 16.3941C17.44 16.1544 17.8225 15.772 18.0622 15.3016C18.3346 14.7668 18.3346 14.0667 18.3346 12.6666V7.33325C18.3346 5.93312 18.3346 5.23306 18.0622 4.69828C17.8225 4.22787 17.44 3.84542 16.9696 3.60574C16.4348 3.33325 15.7348 3.33325 14.3346 3.33325H5.66797C4.26784 3.33325 3.56777 3.33325 3.03299 3.60574C2.56259 3.84542 2.18014 4.22787 1.94045 4.69828C1.66797 5.23306 1.66797 5.93312 1.66797 7.33325V12.6666C1.66797 14.0667 1.66797 14.7668 1.94045 15.3016C2.18014 15.772 2.56259 16.1544 3.03299 16.3941C3.56777 16.6666 4.26784 16.6666 5.66797 16.6666Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                mo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "21px",
                    viewBox: "0 0 20 21",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M2.5 13.8334C6.94444 10.5 13.0556 10.5 17.5 13.8334",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M6.51949 7.01986L6.51986 7.01949C6.60111 6.93845 6.73265 6.93857 6.81375 7.01977C6.89484 7.10096 6.8948 7.23251 6.81366 7.31366C6.73251 7.3948 6.60096 7.39484 6.51977 7.31375C6.43857 7.23265 6.43845 7.10111 6.51949 7.01986",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M15.4167 6.74996V3.41663",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M13.75 5.08329H17.0833",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M10.8333 3H6.66667C4.36548 3 2.5 4.86548 2.5 7.16667V13.8333C2.5 16.1345 4.36548 18 6.66667 18H13.3333C15.6345 18 17.5 16.1345 17.5 13.8333V9.66667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Zo = e => (0, t.jsx)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M13.3333 17.5H13.5C14.9001 17.5 15.6002 17.5 16.135 17.2275C16.6054 16.9878 16.9878 16.6054 17.2275 16.135C17.5 15.6002 17.5 14.9001 17.5 13.5V6.5C17.5 5.09987 17.5 4.3998 17.2275 3.86502C16.9878 3.39462 16.6054 3.01217 16.135 2.77248C15.6002 2.5 14.9001 2.5 13.5 2.5H6.5C5.09987 2.5 4.3998 2.5 3.86502 2.77248C3.39462 3.01217 3.01217 3.39462 2.77248 3.86502C2.5 4.3998 2.5 5.09987 2.5 6.5V6.66667M9.58333 10.4167L14.1667 5.83333M14.1667 5.83333H10M14.1667 5.83333V10M5.16667 17.5H7.33333C8.26675 17.5 8.73346 17.5 9.08998 17.3183C9.40359 17.1586 9.65855 16.9036 9.81834 16.59C10 16.2335 10 15.7668 10 14.8333V12.6667C10 11.7332 10 11.2665 9.81834 10.91C9.65855 10.5964 9.40359 10.3414 9.08998 10.1817C8.73346 10 8.26675 10 7.33333 10H5.16667C4.23325 10 3.76654 10 3.41002 10.1817C3.09641 10.3414 2.84144 10.5964 2.68166 10.91C2.5 11.2665 2.5 11.7332 2.5 12.6667V14.8333C2.5 15.7668 2.5 16.2335 2.68166 16.59C2.84144 16.9036 3.09641 17.1586 3.41002 17.3183C3.76654 17.5 4.23325 17.5 5.16667 17.5Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                Wo = e => (0, t.jsx)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: (0, t.jsx)("path", {
                        d: "M17.5 7.50001L17.5 2.50001M17.5 2.50001H12.5M17.5 2.50001L10 10M8.33333 2.5H6.5C5.09987 2.5 4.3998 2.5 3.86502 2.77248C3.39462 3.01217 3.01217 3.39462 2.77248 3.86502C2.5 4.3998 2.5 5.09987 2.5 6.5V13.5C2.5 14.9001 2.5 15.6002 2.77248 16.135C3.01217 16.6054 3.39462 16.9878 3.86502 17.2275C4.3998 17.5 5.09987 17.5 6.5 17.5H13.5C14.9001 17.5 15.6002 17.5 16.135 17.2275C16.6054 16.9878 16.9878 16.6054 17.2275 16.135C17.5 15.6002 17.5 14.9001 17.5 13.5V11.6667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })
                })),
                Do = e => (0, t.jsxs)(p, a(x({
                    width: "18px",
                    height: "18px",
                    viewBox: "0 0 18 18",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M11.668 1C17.0013 0.999985 17.0013 1.59259 17.0013 6.33333",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33203 1C0.998698 0.999985 0.998698 1.59259 0.998698 6.33333",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M11.668 17C17.0013 17 17.0013 16.4074 17.0013 11.6667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33203 17C0.998698 17 0.998698 16.4074 0.998698 11.6667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    })]
                })),
                _o = e => (0, t.jsxs)(p, a(x({
                    width: "18px",
                    height: "18px",
                    viewBox: "0 0 18 18",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M11.666 1C16.9993 0.999985 16.9993 1.59259 16.9993 6.33333",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33398 1C1.00065 0.999985 1.00065 1.59259 1.00065 6.33333",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M11.666 17C16.9993 17 16.9993 16.4074 16.9993 11.6667",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33398 17C1.00065 17 1.00065 16.4074 1.00065 11.6667",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    })]
                })),
                Eo = e => (0, t.jsxs)(p, a(x({
                    width: "18px",
                    height: "18px",
                    viewBox: "0 0 18 18",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M11.666 1C16.9993 0.999985 16.9993 1.59259 16.9993 6.33333",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33398 1C1.00065 0.999985 1.00065 1.59259 1.00065 6.33333",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M11.666 17C16.9993 17 16.9993 16.4074 16.9993 11.6667",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33398 17C1.00065 17 1.00065 16.4074 1.00065 11.6667",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    })]
                })),
                Ro = e => (0, t.jsxs)(p, a(x({
                    width: "18px",
                    height: "18px",
                    viewBox: "0 0 18 18",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M11.666 1C16.9993 0.999985 16.9993 1.59259 16.9993 6.33333",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33398 1C1.00065 0.999985 1.00065 1.59259 1.00065 6.33333",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M11.666 17C16.9993 17 16.9993 16.4074 16.9993 11.6667",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33398 17C1.00065 17 1.00065 16.4074 1.00065 11.6667",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    })]
                })),
                Qo = e => (0, t.jsxs)(p, a(x({
                    width: "18px",
                    height: "18px",
                    viewBox: "0 0 18 18",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M11.666 1C16.9993 0.999985 16.9993 1.59259 16.9993 6.33333",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33398 1C1.00065 0.999985 1.00065 1.59259 1.00065 6.33333",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M11.666 17C16.9993 17 16.9993 16.4074 16.9993 11.6667",
                        stroke: "#D0D5DD",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    }), (0, t.jsx)("path", {
                        d: "M6.33398 17C1.00065 17 1.00065 16.4074 1.00065 11.6667",
                        stroke: "#344054",
                        strokeWidth: "1.5",
                        strokeLinecap: "square"
                    })]
                })),
                yo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M15 5.83333V4.16667C15.0001 3.7246 14.8246 3.30061 14.512 2.98802C14.1994 2.67543 13.7754 2.49988 13.3333 2.5H4.16667C3.7246 2.49988 3.30061 2.67543 2.98802 2.98802C2.67543 3.30061 2.49988 3.7246 2.5 4.16667V8.33333C2.49988 8.7754 2.67543 9.19939 2.98802 9.51198C3.30061 9.82457 3.7246 10.0001 4.16667 10H5.83333",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M15.832 17.4999H7.4987C6.57822 17.4999 5.83203 16.7537 5.83203 15.8333V7.49992C5.83203 6.57944 6.57822 5.83325 7.4987 5.83325H15.832C16.7525 5.83325 17.4987 6.57944 17.4987 7.49992V15.8333C17.4987 16.7537 16.7525 17.4999 15.832 17.4999Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M12.918 13.3334H14.5846",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M8.75 13.253L9.30314 13.75L10.4167 12.75",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M12.832 9.99992H14.4987",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M8.66406 9.91975L9.21721 10.4167L10.3307 9.41675",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                So = e => (0, t.jsxs)(p, a(x({
                    viewBox: "0 0 80 80",
                    fill: "none"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M57.3229 15.4951H9.87362C7.18199 15.4951 5 17.6771 5 20.3687V59.6317C5 62.3234 7.18199 64.5054 9.87362 64.5054H57.3229C60.0145 64.5054 62.1965 62.3234 62.1965 59.6317V20.3687C62.1965 17.6771 60.0145 15.4951 57.3229 15.4951Z",
                        fill: "#EAECF0"
                    }), (0, t.jsx)("path", {
                        d: "M45.0073 49.6967C46.3244 46.4576 50.7147 41.3498 56.6541 36.4172C64.8323 29.6182 72.8479 25.7758 74.5421 27.8158C76.2363 29.8559 70.988 37.0216 62.8019 43.82C56.8704 48.7533 51.0274 52.1294 47.6173 52.8307L45.0073 49.6967Z",
                        fill: "#0E9384"
                    }), (0, t.jsx)("path", {
                        d: "M47.6167 52.8319L45.0067 49.6979C45.8692 47.5927 48.0381 44.6788 51.0956 41.5214L51.1665 41.528C55.3011 39.7814 59.2586 44.5817 56.7925 48.3023L56.7866 48.3654C53.0993 50.7731 49.8388 52.3464 47.6167 52.8319Z",
                        fill: "#107569"
                    }), (0, t.jsx)("path", {
                        d: "M44.0999 59.5861C42.6979 60.7512 41.0262 61.5461 39.2376 61.8983C37.4491 62.2504 35.6008 62.1484 33.8618 61.6017C33.6816 60.0891 33.8199 58.5556 34.2679 57.0996C34.7158 55.6437 35.4634 54.2976 36.4627 53.1479C37.462 51.9981 38.6908 51.0702 40.0701 50.4238C41.4494 49.7773 42.9487 49.4267 44.4717 49.3943C44.9727 49.3885 45.4687 49.4944 45.9236 49.7045C46.3785 49.9145 46.7809 50.2233 47.1014 50.6084C47.4219 50.9935 47.6526 51.4453 47.7765 51.9308C47.9005 52.4162 47.9146 52.9232 47.8179 53.4149C47.3161 55.8348 46.0047 58.0115 44.0999 59.5861Z",
                        fill: "#D0D5DD"
                    }), (0, t.jsx)("path", {
                        d: "M33.8609 61.6001C33.6452 59.801 33.8813 57.9766 34.5479 56.2917C35.2145 54.6068 36.2905 53.1145 37.6786 51.9499C37.8648 52.1075 38.0375 52.2803 38.1949 52.4666C38.613 52.9696 38.9177 53.5569 39.0882 54.1884C39.2587 54.8198 39.291 55.4806 39.183 56.1257C39.1612 56.3336 39.1844 56.5438 39.2511 56.7419C39.3177 56.94 39.4263 57.1215 39.5693 57.2739C40.2842 58.1347 39.7837 59.6775 38.5245 61.3487C38.3482 61.5836 38.1192 61.7738 37.856 61.904C37.5928 62.0342 37.3026 62.1007 37.0089 62.0983C35.9407 62.0891 34.8798 61.9212 33.8609 61.6001Z",
                        fill: "#107569"
                    }), (0, t.jsx)("path", {
                        d: "M57.3229 15.4951H9.87362C8.58105 15.4951 7.34143 16.0086 6.42745 16.9226C5.51347 17.8365 5 19.0762 5 20.3687V22.6447H62.1965V20.3687C62.1965 19.7287 62.0704 19.095 61.8255 18.5037C61.5806 17.9124 61.2216 17.3751 60.769 16.9226C60.3165 16.47 59.7792 16.111 59.1879 15.8661C58.5966 15.6212 57.9629 15.4951 57.3229 15.4951Z",
                        fill: "#107569"
                    }), (0, t.jsx)("path", {
                        d: "M12.0174 18.3306H10.5518V19.7962H12.0174V18.3306Z",
                        fill: "#0E9384"
                    }), (0, t.jsx)("path", {
                        d: "M15.0321 18.3306H13.5664V19.7962H15.0321V18.3306Z",
                        fill: "#0E9384"
                    }), (0, t.jsx)("path", {
                        d: "M18.037 18.3306H16.5713V19.7962H18.037V18.3306Z",
                        fill: "#0E9384"
                    }), (0, t.jsx)("path", {
                        d: "M62.1965 22.6445H5V24.4081H62.1965V22.6445Z",
                        fill: "#0E9384"
                    }), (0, t.jsx)("path", {
                        d: "M62.1965 27.7327H8.32455C7.44282 27.7327 6.59721 27.3825 5.97374 26.759C5.35026 26.1355 5 25.2899 5 24.4082H62.1965V27.7327Z",
                        fill: "#D0D5DD"
                    })]
                })),
                Oo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M9.16797 9.99992H17.5013",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M5.34479 8.82162C5.99563 9.47245 5.99563 10.5275 5.34479 11.1783C4.69396 11.8291 3.63896 11.8291 2.98813 11.1783C2.33729 10.5275 2.33729 9.47245 2.98813 8.82162C3.63896 8.17079 4.69396 8.17079 5.34479 8.82162",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M9.16797 4.16667H17.5013",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M2.51953 3.75667L3.89703 4.99667L6.66953 2.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M9.16797 15.8334H17.5013",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M5.34479 14.6551C5.99563 15.306 5.99563 16.361 5.34479 17.0118C4.69396 17.6626 3.63896 17.6626 2.98813 17.0118C2.33729 16.361 2.33729 15.306 2.98813 14.6551C3.63896 14.0043 4.69396 14.0043 5.34479 14.6551",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Po = e => (0, t.jsxs)(p, a(x({
                    width: "14px",
                    height: "14px",
                    viewBox: "0 0 14 14",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M7.58431 1.16675L2.38883 7.40133C2.18536 7.6455 2.08362 7.76758 2.08206 7.87068C2.08071 7.96032 2.12065 8.04559 2.19038 8.10194C2.27058 8.16675 2.4295 8.16675 2.74733 8.16675H7.00098L6.41764 12.8334L11.6131 6.59883C11.8166 6.35467 11.9183 6.23259 11.9199 6.12948C11.9212 6.03985 11.8813 5.95457 11.8116 5.89823C11.7314 5.83342 11.5725 5.83342 11.2546 5.83342H7.00098L7.58431 1.16675Z",
                        stroke: "url(#paint0_linear_12264_34741)",
                        strokeWidth: "1.05",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsxs)("linearGradient", {
                            id: "paint0_linear_12264_34741",
                            x1: "2.84709",
                            y1: "8.61231",
                            x2: "9.63054",
                            y2: "6.73288",
                            gradientUnits: "userSpaceOnUse",
                            children: [(0, t.jsx)("stop", {
                                stopColor: "#FF2C77"
                            }), (0, t.jsx)("stop", {
                                offset: "1",
                                stopColor: "#6723A5"
                            })]
                        })
                    })]
                })),
                zo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#clip0_12264_34732)",
                        children: (0, t.jsx)("path", {
                            d: "M10.0013 13.3334V10.0001M10.0013 6.66675H10.0096M18.3346 10.0001C18.3346 14.6025 14.6037 18.3334 10.0013 18.3334C5.39893 18.3334 1.66797 14.6025 1.66797 10.0001C1.66797 5.39771 5.39893 1.66675 10.0013 1.66675C14.6037 1.66675 18.3346 5.39771 18.3346 10.0001Z",
                            stroke: "currentColor",
                            strokeWidth: "1.66667",
                            strokeLinecap: "round",
                            strokeLinejoin: "round"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_12264_34732",
                            children: (0, t.jsx)("rect", {
                                width: "20",
                                height: "20",
                                fill: "white"
                            })
                        })
                    })]
                })),
                Fo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M10.0007 13.3345C10.0007 11.4928 11.4937 9.99976 13.3355 9.99976C11.4937 9.99976 10.0007 8.50676 10.0007 6.66504C10.0007 8.50676 8.50773 9.99976 6.66602 9.99976C8.50773 9.99976 10.0007 11.4928 10.0007 13.3345Z",
                        stroke: "currentColor",
                        strokeWidth: "1.25",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M17.5043 6.66498V13.3344C17.5043 15.6366 15.638 17.5028 13.3359 17.5028H6.66645C4.3643 17.5028 2.49805 15.6366 2.49805 13.3344V6.66498C2.49805 4.36284 4.3643 2.49658 6.66645 2.49658H11.6685",
                        stroke: "currentColor",
                        strokeWidth: "1.25",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M14.168 3.33041H17.5027",
                        stroke: "currentColor",
                        strokeWidth: "1.25",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M15.8368 1.66309V4.99781",
                        stroke: "currentColor",
                        strokeWidth: "1.25",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Go = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M11.7684 8.23337C12.7442 9.2092 12.7442 10.7934 11.7684 11.7709C10.7926 12.7467 9.2084 12.7467 8.2309 11.7709C7.25507 10.795 7.25507 9.21087 8.2309 8.23337C9.2084 7.25587 10.7917 7.25587 11.7684 8.23337",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M2.5 9.99996C2.5 9.45079 2.62667 8.90746 2.87167 8.40663V8.40663C4.13417 5.82579 6.92417 4.16663 10 4.16663C13.0758 4.16663 15.8658 5.82579 17.1283 8.40663V8.40663C17.3733 8.90746 17.5 9.45079 17.5 9.99996C17.5 10.5491 17.3733 11.0925 17.1283 11.5933V11.5933C15.8658 14.1741 13.0758 15.8333 10 15.8333C6.92417 15.8333 4.13417 14.1741 2.87167 11.5933V11.5933C2.62667 11.0925 2.5 10.5491 2.5 9.99996Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                To = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M13.3337 17.5H6.66699",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("rect", {
                        x: "1.66699",
                        y: "2.5",
                        width: "16.6667",
                        height: "11.6667",
                        rx: "2",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M11.667 14.1666V15C11.667 16.3807 12.2266 17.5 12.917 17.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M8.33301 14.1666V15C8.33301 16.3807 7.77337 17.5 7.08301 17.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                bo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M13.3334 17.5002H6.66675",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M1.66675 3.21875V12.1667C1.66675 13.2712 2.56218 14.1667 3.66675 14.1667H13.3487M6.16281 2.5H16.3334C17.438 2.5 18.3334 3.39543 18.3334 4.5V14.1667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M11.6667 14.1665V14.9998C11.6667 16.3805 12.2264 17.4998 12.9167 17.4998",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M8.33325 14.1665V14.9998C8.33325 16.3805 7.77361 17.4998 7.08325 17.4998",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M1.02734 2.4082L19.1338 15.0992",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Uo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("rect", {
                        x: "1.66699",
                        y: "3.33337",
                        width: "16.6667",
                        height: "13.3333",
                        rx: "2",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M7.5 14.5833H12.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                qo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M15.8333 15.9226V4.16667C15.8333 3.24619 15.0871 2.5 14.1667 2.5H7.5",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M8.33325 15.0002H11.6666",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M4.16675 7.57666V15.8332C4.16675 16.7537 4.91294 17.4999 5.83341 17.4999H12.5001",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M2.47461 2.80273L17.186 17.514",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                No = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M8.33301 3.33337V4.83337C8.33301 5.10921 8.51967 5.33337 8.74967 5.33337H11.2497C11.4797 5.33337 11.6663 5.10921 11.6663 4.83337V3.33337",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M7.49967 3.33337H12.4997C13.4205 3.33337 14.1663 4.07921 14.1663 5.00004V15C14.1663 15.9209 13.4205 16.6667 12.4997 16.6667H7.49967C6.57884 16.6667 5.83301 15.9209 5.83301 15V5.00004C5.83301 4.07921 6.57884 3.33337 7.49967 3.33337Z",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Jo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M3.54175 3.5415L16.4584 16.4582",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M5.83325 9.1665V14.9998C5.83325 15.9207 6.57909 16.6665 7.49992 16.6665H12.4999C13.4208 16.6665 14.1666 15.9207 14.1666 14.9998V14.1665",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M5.83325 5.8335V5.00016C5.83325 4.07933 6.57909 3.3335 7.49992 3.3335H12.4999C13.4208 3.3335 14.1666 4.07933 14.1666 5.00016V10.8335",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M8.33325 3.5415V5.0415C8.33325 5.31734 8.51992 5.5415 8.74992 5.5415H11.2499C11.4799 5.5415 11.6666 5.31734 11.6666 5.0415V3.5415",
                        stroke: "currentColor",
                        strokeWidth: "1.2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Xo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M14.167 11.6667C14.167 11.2065 14.5401 10.8334 15.0003 10.8334H15.5124C15.9531 10.8334 16.3443 11.1154 16.4837 11.5334V11.5334C16.5974 11.8746 16.5238 12.2504 16.2897 12.5235L14.167 15H16.667",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M4.16667 5V15",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M10.8337 5V15",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M4.16699 10H10.8337",
                        stroke: "currentColor",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                Yo = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#clip0_12632_6368)",
                        children: (0, t.jsx)("path", {
                            d: "M12.917 12.5L15.417 9.99996L12.917 7.49996M7.08366 7.49996L4.58366 9.99996L7.08366 12.5M10.8337 5.83329L9.16699 14.1666M18.3337 9.99996C18.3337 14.6023 14.6027 18.3333 10.0003 18.3333C5.39795 18.3333 1.66699 14.6023 1.66699 9.99996C1.66699 5.39759 5.39795 1.66663 10.0003 1.66663C14.6027 1.66663 18.3337 5.39759 18.3337 9.99996Z",
                            stroke: "currentColor",
                            strokeWidth: "1.5",
                            strokeLinecap: "round",
                            strokeLinejoin: "round"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_12632_6368",
                            children: (0, t.jsx)("rect", {
                                width: "20",
                                height: "20",
                                fill: "white"
                            })
                        })
                    })]
                })),
                Ko = e => (0, t.jsxs)(p, a(x({
                    width: "32px",
                    height: "32px",
                    viewBox: "0 0 32 32",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M18.3072 5.62939L13.6924 26.3701",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M23.7778 10.8149L28.963 16.0001L23.7778 21.1853",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M8.2223 21.1853L3.03711 16.0001L8.2223 10.8149",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                $o = e => (0, t.jsxs)(p, a(x({
                    width: "16px",
                    height: "16px",
                    viewBox: "0 0 16 16",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("g", {
                        clipPath: "url(#clip0_12652_12461)",
                        children: (0, t.jsx)("path", {
                            d: "M7.99943 5.3335V8.00016M7.99943 10.6668H8.0061M14.6661 8.00016C14.6661 11.6821 11.6813 14.6668 7.99943 14.6668C4.31753 14.6668 1.33276 11.6821 1.33276 8.00016C1.33276 4.31826 4.31753 1.3335 7.99943 1.3335C11.6813 1.3335 14.6661 4.31826 14.6661 8.00016Z",
                            stroke: "currentColor",
                            strokeWidth: "1.2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round"
                        })
                    }), (0, t.jsx)("defs", {
                        children: (0, t.jsx)("clipPath", {
                            id: "clip0_12652_12461",
                            children: (0, t.jsx)("rect", {
                                width: "16",
                                height: "16",
                                fill: "white"
                            })
                        })
                    }), " "]
                })),
                er = e => (0, t.jsxs)(p, a(x({
                    width: "20px",
                    height: "20px",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M17.5 6.66667V13.3333C17.5 15.6345 15.6345 17.5 13.3333 17.5H6.66667C4.36548 17.5 2.5 15.6345 2.5 13.3333V6.66667C2.5 4.36548 4.36548 2.5 6.66667 2.5H8.33333",
                        stroke: "#667085",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M3.33334 4.16663L15.8333 16.6666",
                        stroke: "#667085",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M3.72091 16.279L9.58333 10.4166",
                        stroke: "#667085",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M13.75 7.5V5",
                        stroke: "#667085",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("ellipse", {
                        cx: "13.75",
                        cy: "2.91671",
                        rx: "2.08333",
                        ry: "2.08333",
                        stroke: "#667085",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    })]
                })),
                or = e => (0, t.jsxs)(p, a(x({
                    width: "32px",
                    height: "24px",
                    viewBox: "0 0 32 24",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, e), {
                    children: [(0, t.jsx)("path", {
                        d: "M4 7C4 6.06812 4 5.60218 4.15224 5.23463C4.35523 4.74458 4.74458 4.35523 5.23463 4.15224C5.60218 4 6.06812 4 7 4H17C17.9319 4 18.3978 4 18.7654 4.15224C19.2554 4.35523 19.6448 4.74458 19.8478 5.23463C20 5.60218 20 6.06812 20 7M9 20H15M12 4V20",
                        stroke: "currentColor",
                        strokeWidth: "2",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }), (0, t.jsx)("path", {
                        d: "M23.513 9.40223C23.8233 9.26443 24.1774 9.26429 24.4876 9.40223C24.7271 9.50886 24.8832 9.6966 24.9915 9.8495C25.1015 10.0049 25.2196 10.2103 25.3499 10.4354L30.2855 18.9589C30.4163 19.1849 30.5354 19.391 30.6155 19.5643C30.6944 19.7348 30.7805 19.9641 30.7532 20.2255C30.7179 20.5637 30.54 20.8711 30.265 21.0712C30.0524 21.2257 29.8113 21.2669 29.6243 21.2841C29.4342 21.3015 29.196 21.3007 28.9349 21.3007H19.0648C18.8039 21.3007 18.5663 21.3015 18.3763 21.2841C18.1893 21.2669 17.9473 21.2256 17.7347 21.0712C17.4598 20.8711 17.2827 20.5636 17.2474 20.2255C17.2201 19.9641 17.3053 19.7348 17.3841 19.5643C17.4643 19.391 17.5842 19.185 17.7152 18.9589L22.6497 10.4354C22.7801 10.2102 22.899 10.0049 23.0091 9.8495C23.1175 9.69654 23.2733 9.5088 23.513 9.40223ZM23.9993 18.0009C23.6681 18.0009 23.3999 18.2693 23.3997 18.6005C23.3997 18.9318 23.668 19.2011 23.9993 19.2011H24.0052L24.1263 19.1884C24.3996 19.1324 24.6058 18.8904 24.6058 18.6005C24.6056 18.3107 24.3996 18.0685 24.1263 18.0126L24.0052 18.0009H23.9993ZM23.9993 13.2011C23.668 13.2011 23.3998 13.4694 23.3997 13.8007V16.2011C23.3999 16.5323 23.6681 16.8007 23.9993 16.8007C24.3306 16.8007 24.5987 16.5323 24.5989 16.2011V13.8007C24.5988 13.4694 24.3306 13.2011 23.9993 13.2011Z",
                        fill: "currentColor"
                    })]
                }))
        }
    }
]);
//# sourceMappingURL=1503.35308869da5e42fc.js.map