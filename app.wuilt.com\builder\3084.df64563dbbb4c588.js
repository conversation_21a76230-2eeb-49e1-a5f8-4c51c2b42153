(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [3084], {
        58247: (_, e, l) => {
            l.d(e, {
                O: () => r,
                y: () => i
            });
            let i = function(_) {
                    return _.MANUAL = "MANUAL", _.AUTOMATIC = "AUTOMATIC", _
                }({}),
                r = function(_) {
                    return _.CREATING = "CREATING", _.RESTORING = "RESTORING", _
                }({})
        },
        37484: (_, e, l) => {
            l.d(e, {
                Z: () => n
            });
            var i = l(63552),
                r = l(26897),
                t = l(5862),
                d = l.n(t),
                s = l(74499);
            let o;
            const n = ({
                    message: _
                }) => (0, s.BX)(i.<PERSON>ack, {
                    align: "center",
                    pt: "10px",
                    children: [(0, s.tZ)(m, {
                        children: (0, s.tZ)(i.<PERSON>Circle<PERSON><PERSON>, {
                            className: "check-circle-icon",
                            color: "primary"
                        })
                    }), (0, s.BX)(i.<PERSON><PERSON>, {
                        spacing: "condensed",
                        align: "center",
                        children: [(0, s.tZ)(i.Heading, {
                            fontSize: "md2",
                            children: (0, s.tZ)(r.FormattedMessage, {
                                defaultMessage: "Completed",
                                id: "95stPq"
                            })
                        }), (0, s.tZ)(i.Text, {
                            fontSize: "sm",
                            children: _
                        })]
                    })]
                }),
                m = d()(i.Box)(o || (o = (_ => _)
                    `
  .check-circle-icon {
    width: 72px;
    height: 72px;
  }
`))
        },
        34418: (_, e, l) => {
            l.d(e, {
                Z: () => x
            });
            var i = l(37900),
                r = l(63552),
                t = l(26897),
                d = l(58247),
                s = l(44486),
                o = l(75675),
                n = l(37484),
                m = l(74499);
            const x = ({
                show: _,
                onClose: e,
                manualBackupsList: l,
                createSiteBackup: x,
                backupName: a,
                setBackupName: c
            }) => {
                const g = 5 === (null == l ? void 0 : l.length),
                    [G, p] = (0, i.useState)(""),
                    u = (0, s.useSelector)((_ => (0, o.Xw)(_))),
                    f = u.isLoading,
                    h = u.isCompleted,
                    y = (0, s.useDispatch)();
                return (0, m.tZ)(r.Modal, {
                    modalWidth: "small",
                    show: _,
                    onClose: () => {
                        p(""), e(), y(o.eX.saveSiteBackupsCompleted())
                    },
                    children: (0, m.tZ)(r.Modal.Body, {
                        children: f ? (0, m.BX)(m.HY, {
                            children: [(0, m.tZ)(r.Heading, {
                                fontSize: "md2",
                                fontWeight: "semiBold",
                                children: (0, m.tZ)(t.FormattedMessage, {
                                    defaultMessage: "Save Backup",
                                    id: "f8IHpR"
                                })
                            }), (0, m.BX)(r.Stack, {
                                align: "center",
                                pt: "30px",
                                children: [(0, m.tZ)(r.Spinner, {
                                    size: "large"
                                }), (0, m.tZ)(r.Text, {
                                    children: (0, m.tZ)(t.FormattedMessage, {
                                        defaultMessage: "Creating website backup...",
                                        id: "6ByHL0"
                                    })
                                })]
                            })]
                        }) : h ? (0, m.BX)(r.Stack, {
                            children: [(0, m.tZ)(r.Heading, {
                                fontSize: "md2",
                                fontWeight: "semiBold",
                                children: (0, m.tZ)(t.FormattedMessage, {
                                    defaultMessage: "Save Backup",
                                    id: "f8IHpR"
                                })
                            }), (0, m.tZ)(n.Z, {
                                message: (0, m.tZ)(t.FormattedMessage, {
                                    defaultMessage: "Your website backup is now complete.",
                                    id: "5CPgeq"
                                })
                            })]
                        }) : (0, m.BX)(r.Stack, {
                            children: [(0, m.tZ)(r.Stack, {
                                width: "30px",
                                height: "30px",
                                p: "18px",
                                borderRadius: "10px",
                                border: `1px solid ${r.theme.palette.cloud.darkActive}`,
                                align: "center",
                                justify: "center",
                                children: (0, m.tZ)(r.BackupClockIcon, {
                                    color: "info"
                                })
                            }), (0, m.BX)(r.Stack, {
                                spacing: "tight",
                                children: [(0, m.tZ)(r.Heading, {
                                    fontSize: "md2",
                                    fontWeight: "semiBold",
                                    children: (0, m.tZ)(t.FormattedMessage, {
                                        defaultMessage: "Save Backup",
                                        id: "f8IHpR"
                                    })
                                }), (0, m.tZ)(r.Text, {
                                    fontSize: "sm",
                                    mt: "10px",
                                    children: (0, m.tZ)(t.FormattedMessage, {
                                        defaultMessage: "Would you like to save a backup of the current version?",
                                        id: "eCYIqU"
                                    })
                                })]
                            }), g && (0, m.BX)(r.Stack, {
                                bgColor: "warning",
                                p: "10px",
                                border: `1px solid ${r.theme.palette.orange.lightActive}`,
                                borderRadius: "12px",
                                children: [(0, m.BX)(r.Stack, {
                                    spacing: "tight",
                                    direction: "row",
                                    children: [(0, m.tZ)(r.Stack, {
                                        width: "fit-content",
                                        height: "fit-content",
                                        p: "5px",
                                        borderRadius: "50%",
                                        align: "center",
                                        justify: "center",
                                        border: `1px solid ${r.theme.palette.orange.lightHover}`,
                                        children: (0, m.tZ)(r.Stack, {
                                            width: "fit-content",
                                            height: "fit-content",
                                            p: "5px",
                                            borderRadius: "50%",
                                            align: "center",
                                            justify: "center",
                                            border: `1px solid ${r.theme.palette.orange.lightActive}`,
                                            children: (0, m.tZ)(r.OutlinedDangerIcon, {
                                                color: "danger",
                                                size: "xl"
                                            })
                                        })
                                    }), (0, m.BX)(r.Stack, {
                                        spacing: "tight",
                                        pt: "10px",
                                        children: [(0, m.tZ)(r.Text, {
                                            fontSize: "sm",
                                            fontWeight: "500",
                                            color: "darkOrange",
                                            children: (0, m.tZ)(t.FormattedMessage, {
                                                defaultMessage: "You've reached the backup limit",
                                                id: "0z/4AH"
                                            })
                                        }), (0, m.tZ)(r.Text, {
                                            color: "darkOrange",
                                            children: (0, m.tZ)(t.FormattedMessage, {
                                                defaultMessage: "Select one of the existing 5 backups to replace with the new one.",
                                                id: "udyG6l"
                                            })
                                        })]
                                    })]
                                }), (0, m.BX)(r.Stack, {
                                    spacing: "tight",
                                    pb: "10px",
                                    children: [(0, m.tZ)(r.Label, {
                                        fontSize: "sm",
                                        fontWeight: "medium",
                                        children: (0, m.tZ)(t.FormattedMessage, {
                                            defaultMessage: "Select backup to replace",
                                            id: "yB1T6B"
                                        })
                                    }), (0, m.tZ)(r.Select, {
                                        options: null == l || null == l.map ? void 0 : l.map((_ => ({
                                            value: null == _ ? void 0 : _._id,
                                            label: null == _ ? void 0 : _.data.backup_title
                                        }))),
                                        onChange: _ => p(null == _ ? void 0 : _.value)
                                    })]
                                })]
                            }), (0, m.BX)(r.Stack, {
                                spacing: "tight",
                                pb: "10px",
                                children: [(0, m.tZ)(r.Label, {
                                    fontSize: "sm",
                                    children: (0, m.tZ)(t.FormattedMessage, {
                                        defaultMessage: "Name",
                                        id: "HAlOn1"
                                    })
                                }), (0, m.tZ)(r.InputField, {
                                    value: a,
                                    onChange: _ => c(_.target.value)
                                })]
                            }), (0, m.BX)(r.Stack, {
                                direction: "row",
                                justify: "between",
                                children: [(0, m.tZ)(r.Button, {
                                    color: "white",
                                    onClick: () => {
                                        e()
                                    },
                                    children: (0, m.tZ)(t.FormattedMessage, {
                                        defaultMessage: "Cancel",
                                        id: "47FYwb"
                                    })
                                }), (0, m.tZ)(r.Button, {
                                    disabled: !a || !G && g,
                                    onClick: async () => {
                                        await x(d.y.MANUAL, a, G, d.O.CREATING)
                                    },
                                    children: g ? (0, m.tZ)(t.FormattedMessage, {
                                        defaultMessage: "Replace & Save",
                                        id: "CAK65+"
                                    }) : (0, m.tZ)(t.FormattedMessage, {
                                        defaultMessage: "Save",
                                        id: "jvo0vs"
                                    })
                                })]
                            })]
                        })
                    })
                })
            }
        },
        19614: (_, e, l) => {
            l.d(e, {
                Z: () => c
            });
            var i = l(63552),
                r = l(26897),
                t = l(44486),
                d = l(58247),
                s = l(80108),
                o = l(75675),
                n = l(37044),
                m = l(37900),
                x = l(77686),
                a = function(_) {
                    return _[_["Site backup 1"] = 0] = "Site backup 1", _[_["Site backup 2"] = 1] = "Site backup 2", _[_["Site backup 3"] = 2] = "Site backup 3", _[_["Site backup 4"] = 3] = "Site backup 4", _[_["Site backup 5"] = 4] = "Site backup 5", _
                }(a || {});
            const c = _ => {
                var e, l;
                const c = (0, r.useIntl)(),
                    {
                        addToast: g
                    } = (0, i.useToaster)(),
                    G = (0, t.useDispatch)(),
                    {
                        pushEvent: p
                    } = (0, x.useTracking)(),
                    [u, f] = (0, m.useState)(""),
                    h = (0, t.useSelector)((_ => {
                        var e;
                        return null == (e = (0, o.Xw)(_)) ? void 0 : e.backups
                    })),
                    y = (0, t.useSelector)((_ => (0, n.jx)(_))),
                    b = null == (e = h || y) ? void 0 : e.filter((_ => {
                        var e;
                        return (null == _ || null == (e = _.data) ? void 0 : e.backup_type) === d.y.MANUAL
                    })),
                    w = a[b.length] || a[b.length - 1];
                return {
                    createSiteBackup: async (e, l, i, r) => {
                        try {
                            G(o.eX.saveSiteBackupsRequest(d.O.CREATING));
                            const t = new Date,
                                n = await (0, s.F$)({
                                    siteId: _,
                                    type: e,
                                    title: l,
                                    date: t.toISOString(),
                                    id: i
                                });
                            return G(o.eX.saveSiteBackups(n)), r === d.O.CREATING && G(o.eX.saveSiteBackupsSucceeded()), p("Site backup created", {
                                siteId: _
                            }), g({
                                content: c.formatMessage({
                                    id: "9XQRQ3",
                                    defaultMessage: "Your website backup created successfully"
                                }),
                                appearance: "success"
                            }), n
                        } catch (_) {
                            g({
                                content: c.formatMessage({
                                    id: "rPijV8",
                                    defaultMessage: "Oops, An Error occurred. please try again"
                                }),
                                appearance: "error"
                            })
                        }
                    },
                    manualBackupsList: b,
                    autoBackup: null == (l = h || y) ? void 0 : l.find((_ => {
                        var e;
                        return (null == _ || null == (e = _.data) ? void 0 : e.backup_type) === d.y.AUTOMATIC
                    })),
                    setDefaultBackupName: () => {
                        f(w)
                    },
                    setBackupName: f,
                    backupName: u
                }
            }
        },
        47307: (_, e, l) => {
            l.d(e, {
                Z: () => s
            }), l(37900);
            var i = l(77686),
                r = l(63552),
                t = l(26897),
                d = l(74499);
            const s = function({
                isUnderMaintenance: _,
                toggleUnderMaintenance: e,
                siteId: l
            }) {
                const s = (0, t.useIntl)(),
                    {
                        pushEvent: o
                    } = (0, i.useTracking)(),
                    {
                        addToast: n
                    } = (0, r.useToaster)();
                return (0, d.tZ)(r.ToggleButton, {
                    hideIcons: !0,
                    value: _,
                    onChange: _ => {
                        e(l), _ ? (o("maintenance mode enabled", {
                            siteId: l
                        }), n({
                            content: s.formatMessage({
                                defaultMessage: "Maintenance mode activated, Publish to apply",
                                id: "KIe7aI"
                            }),
                            appearance: "success"
                        })) : (o("maintenance mode disabled", {
                            siteId: l
                        }), n({
                            content: s.formatMessage({
                                defaultMessage: "Maintenance mode deactivated, Publish to apply",
                                id: "C+P+/2"
                            }),
                            appearance: "success"
                        }))
                    }
                })
            }
        },
        8352: (_, e, l) => {
            l.d(e, {
                Z: () => f
            });
            var i = l(37900),
                r = l.n(i),
                t = l(23147),
                d = l.n(t),
                s = l(84848),
                o = l(29822),
                n = l.n(o);
            const m = "Button_prefix__P7M4N";
            var x = l(63231),
                a = l.n(x);
            const c = {
                button: "ButtonLayout_button__szCQ5",
                transparent: "ButtonLayout_transparent__TJKE6",
                "close-standard": "ButtonLayout_close-standard__1XCP5",
                "close-dark": "ButtonLayout_close-dark__CIC1j",
                "close-transparent": "ButtonLayout_close-transparent__cAMIU",
                "icon-greybackground": "ButtonLayout_icon-greybackground__tV2bb",
                "icon-standard": "ButtonLayout_icon-standard__pEQGn",
                "icon-standardsecondary": "ButtonLayout_icon-standardsecondary__OJTdV",
                "icon-white": "ButtonLayout_icon-white__U-Qe0",
                "icon-whitesecondary": "ButtonLayout_icon-whitesecondary__shahu",
                inner: "ButtonLayout_inner__vVjxR",
                heightsmall: "ButtonLayout_heightsmall__XXHXt",
                heightlarge: "ButtonLayout_heightlarge__FiZCS",
                "heightx-large": "ButtonLayout_heightx-large__0K3Mp",
                fullGreen: "ButtonLayout_fullGreen__8Znfi",
                fullWhite: "ButtonLayout_fullWhite__pYhWL",
                fullKohly: "ButtonLayout_fullKohly__-h68g",
                fullTransparent: "ButtonLayout_fullTransparent__7J7+D",
                hover: "ButtonLayout_hover__ZbRuW",
                dashedGreen: "ButtonLayout_dashedGreen__691eF",
                fullTransparentIconOnDark: "ButtonLayout_fullTransparentIconOnDark__t8C3x",
                fullTransparentOnDark: "ButtonLayout_fullTransparentOnDark__NF3xa",
                fullGold: "ButtonLayout_fullGold__4kuUp",
                emptyGold: "ButtonLayout_emptyGold__nTtxs",
                emptyGolden: "ButtonLayout_emptyGolden__wu8Gq",
                editorBar: "ButtonLayout_editorBar__RWXF8",
                editorBarIcon: "ButtonLayout_editorBarIcon__lcd46",
                active: "ButtonLayout_active__bIndz",
                yellowGradient: "ButtonLayout_yellowGradient__OabQU",
                yellowGradientHover: "ButtonLayout_yellowGradientHover__LpOeI",
                yellowGradientActive: "ButtonLayout_yellowGradientActive__jGY6P",
                blueGradient: "ButtonLayout_blueGradient__vb01I",
                fullblue: "ButtonLayout_fullblue__embNk",
                login: "ButtonLayout_login__uoqZP",
                fullpurple: "ButtonLayout_fullpurple__3VXmM",
                fullred: "ButtonLayout_fullred__phnqJ",
                emptyWhite: "ButtonLayout_emptyWhite__1JXSZ",
                emptyGreen: "ButtonLayout_emptyGreen__cl0Mr",
                emptyRed: "ButtonLayout_emptyRed__2PUKY",
                emptyblue: "ButtonLayout_emptyblue__IB4sI",
                emptylogin: "ButtonLayout_emptylogin__exEgE",
                emptypurple: "ButtonLayout_emptypurple__XkZJT",
                transparentblue: "ButtonLayout_transparentblue__1LboR",
                emptybluesecondary: "ButtonLayout_emptybluesecondary__N5sw+",
                whiteblueprimary: "ButtonLayout_whiteblueprimary__M9aVk",
                whiteblue: "ButtonLayout_whiteblue__kwoQk",
                whitebluesecondary: "ButtonLayout_whitebluesecondary__8ddUX",
                emptyred: "ButtonLayout_emptyred__5LA7P",
                disabled: "ButtonLayout_disabled__lICC8",
                fullgreen: "ButtonLayout_fullgreen__+AuAv",
                emptygreen: "ButtonLayout_emptygreen__-azim",
                plainGreen: "ButtonLayout_plainGreen__R97tS",
                plainRed: "ButtonLayout_plainRed__rZZch"
            };
            var g = l(74499);
            const G = _ => {
                const {
                    theme: e,
                    hover: l,
                    active: i,
                    disabled: t,
                    height: d,
                    children: s,
                    matchParent: o
                } = _, n = a()({
                    [c.button]: !0,
                    [c[e]]: !0,
                    [c.hover]: l,
                    [c.active]: i,
                    [c.disabled]: t,
                    [c[`height${d}`]]: "medium" !== d
                }, s.props.className), m = Object.assign({}, s.props.style, {
                    height: d,
                    display: "inline-block"
                });
                return o && (m.width = "100%"), 1 === r().Children.count(s) ? r().cloneElement(s, {
                    className: n,
                    style: m
                }, (0, g.tZ)("div", {
                    className: c.inner,
                    children: s.props.children
                })) : s
            };
            G.defaultProps = {
                height: "medium",
                theme: "fullGreen"
            }, G.propTypes = {
                active: d().bool,
                disabled: d().bool,
                height: d().oneOf(["small", "medium", "large", "x-large"]),
                hover: d().bool,
                matchParent: d().bool,
                theme: d().oneOf(["transparent", "fullTransparent", "fullTransparentOnDark", "fullTransparentIconOnDark", "fullGreen", "fullWhite", "fullKohly", "editorBar", "editorBarIcon", "emptyGold", "emptyRed", "emptyGreen", "emptyWhite", "yellowGradient", "yellowGradientActive", "yellowGradientHover", "plainGreen", "plainRed", "fullred"])
            }, G.displayName = "ButtonLayout";
            const p = (0, i.memo)(G);
            class u extends i.PureComponent {
                constructor(..._) {
                    super(..._), this.addIcon = (_, e, l) => {
                        const {
                            iconSize: i
                        } = this.props, t = i || ("small" === l ? "8px" : "medium" === l ? "12px" : "16px"), d = _ === m ? "btn-prefix" : "btn-suffix";
                        return e ? (0, g.tZ)("div", {
                            className: _,
                            "data-hook": d,
                            children: r().cloneElement(e, {
                                size: t
                            })
                        }) : null
                    }, this.addPrefix = () => {
                        const {
                            prefixIcon: _,
                            height: e
                        } = this.props;
                        return this.addIcon(m, _, e)
                    }, this.addSuffix = () => {
                        const {
                            suffixIcon: _,
                            height: e
                        } = this.props;
                        return this.addIcon("Button_suffix__O7s1g", _, e)
                    }
                }
                render() {
                    const {
                        style: _,
                        to: e,
                        route: l,
                        disabled: i,
                        onClick: r,
                        children: t,
                        type: d,
                        className: o,
                        onMouseEnter: m,
                        onMouseLeave: x
                    } = this.props, a = n()(["id", "style", "onClick", "prefixIcon", "suffixIcon", "type"], this.props), c = l ? s.Link : "button";
                    return (0, g.tZ)(p, Object.assign({}, a, {
                        children: l ? (0, g.BX)(s.Link, {
                            to: e,
                            style: _,
                            className: o,
                            disabled: i,
                            type: d,
                            onMouseEnter: m,
                            onMouseLeave: x,
                            children: [this.addPrefix(), t, this.addSuffix()]
                        }) : (0, g.BX)(c, {
                            onClick: r,
                            style: _,
                            className: o,
                            disabled: i,
                            type: d,
                            onMouseEnter: m,
                            onMouseLeave: x,
                            children: [this.addPrefix(), t, this.addSuffix()]
                        })
                    }))
                }
            }
            u.propTypes = Object.assign({}, p.propTypes, {
                children: t.node,
                id: t.string,
                prefixIcon: t.node,
                suffixIcon: t.node,
                type: t.string,
                onClick: t.func,
                onMouseEnter: t.func,
                onMouseLeave: t.func
            }), u.defaultProps = p.defaultProps, u.displayName = "Button";
            const f = u
        },
        97149: (_, e, l) => {
            l.d(e, {
                $: () => d
            });
            var i = l(2547),
                r = (l(37900), l(74499));
            const t = {
                    success: {
                        bgColor: "success.100",
                        borderColor: "success.50"
                    },
                    brand: {
                        bgColor: "brand.100",
                        borderColor: "brand.50"
                    },
                    danger: {
                        bgColor: "error.100",
                        borderColor: "error.50"
                    }
                },
                d = ({
                    children: _,
                    theme: e = "brand"
                }) => (0, r.tZ)(i.Center, {
                    backgroundColor: t[e].bgColor,
                    border: "8px solid",
                    borderColor: t[e].borderColor,
                    borderRadius: "100px",
                    width: "fit-content",
                    padding: "6px",
                    children: _
                })
        },
        83688: (_, e, l) => {
            l.d(e, {
                Y: () => m
            });
            var i = l(63552),
                r = l(26897);
            const t = (0, l(19672).createSelector)([_ => _.api], (_ => null == _ ? void 0 : _.isApiFailed));
            var d = l(44486),
                s = l(37900),
                o = l(74499);
            const n = {
                    fontWeight: 600,
                    textDecoration: "underline",
                    cursor: "pointer"
                },
                m = () => {
                    const _ = (0, d.useSelector)(t),
                        e = (0, r.useIntl)(),
                        {
                            addToast: l
                        } = (0, i.useToaster)();
                    return (0, s.useEffect)((() => {
                        _ && l({
                            content: (0, o.tZ)(i.Text, {
                                color: "black",
                                children: e.formatMessage({
                                    id: "YJJzf0",
                                    defaultMessage: "Something went wrong, please try again or <b>refresh</b> the browser"
                                }, {
                                    b: (..._) => (0, o.tZ)("b", {
                                        onClick: () => window.location.reload(),
                                        style: n,
                                        children: _
                                    })
                                })
                            }),
                            appearance: "error",
                            autoDismiss: !1
                        })
                    }), [_])
                }
        },
        34539: (_, e, l) => {
            l.d(e, {
                Z: () => z
            });
            var i = l(37900),
                r = l(26897),
                t = l(91484),
                d = l(44486),
                s = l(84848),
                o = l(77686),
                n = l(94557),
                m = l(35335),
                x = l(2547),
                a = l(92560),
                c = l(74499);
            const g = ({
                text: _
            }) => {
                const {
                    onCopy: e,
                    hasCopied: l
                } = (0, x.useClipboard)(_);
                return (0, c.tZ)(x.Stack, {
                    direction: "row",
                    alignItems: "center",
                    justify: "center",
                    border: "1px solid",
                    borderColor: "gray.300",
                    borderRadius: "8px",
                    minW: "40px",
                    h: "40px",
                    px: "4px",
                    cursor: "pointer",
                    onClick: () => {
                        e()
                    },
                    children: l ? (0, c.tZ)(a.TickIcon, {
                        size: "15px",
                        color: "gray.600"
                    }) : (0, c.tZ)(a.CopyIcon, {
                        size: "20px",
                        color: "gray.600"
                    })
                })
            };
            var G = l(68914);
            const p = function({
                    domainType: _,
                    domain: e
                }) {
                    const l = (0, i.useRef)(null),
                        t = (0, i.useRef)(null);
                    (0, i.useEffect)((() => {
                        d()
                    }), []);
                    const d = () => {
                            const _ = t.current.querySelector("canvas");
                            _ && (l.current.href = _.toDataURL(), l.current.download = e + "-QR.png")
                        },
                        {
                            pushEvent: s
                        } = (0, o.useTracking)(),
                        n = [{
                            name: "facebook",
                            url: `https://www.facebook.com/sharer/sharer.php?u=${"SUBDOMAIN"===_?"https":"http"}://${e}`,
                            Icon: a.FacebookIcon
                        }, {
                            name: "twitter",
                            url: `https://twitter.com/intent/tweet?url=${"SUBDOMAIN"===_?"https":"http"}://${e}`,
                            Icon: a.TwitterIcon
                        }, {
                            name: "linkedin",
                            url: `https://www.linkedin.com/sharing/share-offsite/?url=${"SUBDOMAIN"===_?"https":"http"}://${e}`,
                            Icon: a.LinkedInIcon
                        }, {
                            name: "whatsApp",
                            url: `https://api.whatsapp.com/send?text=${"SUBDOMAIN"===_?"https":"http"}://${e}`,
                            Icon: a.WhatsAppIcon
                        }, {
                            name: "telegram",
                            url: `https://telegram.me/share/url?url=${"SUBDOMAIN"===_?"https":"http"}://${e}`,
                            Icon: a.TelegramIcon
                        }];
                    return (0, c.BX)(x.Stack, {
                        alignItems: "center",
                        direction: {
                            lg: "row",
                            base: "column"
                        },
                        minH: "88px",
                        bg: "gray.50",
                        spacing: "12px",
                        borderRadius: "8px",
                        mt: "12px",
                        p: "12px",
                        divider: (0, c.tZ)(x.StackDivider, {
                            margin: "12px",
                            borderColor: "gray.200"
                        }),
                        children: [(0, c.BX)(x.Stack, {
                            direction: "row",
                            gap: "8px",
                            alignSelf: "self-start",
                            align: "center",
                            textAlign: "start",
                            children: [(0, c.BX)(x.Stack, {
                                align: "center",
                                alignSelf: "center",
                                justify: "center",
                                bg: "white",
                                w: "64px",
                                h: "64px",
                                flexShrink: "0",
                                position: "relative",
                                children: [(0, c.tZ)(x.Box, {
                                    position: "absolute",
                                    top: "0",
                                    left: "0",
                                    w: "16px",
                                    h: "16px",
                                    borderTopWidth: "1px",
                                    borderLeftWidth: "1px",
                                    borderColor: "gray.400",
                                    borderTopLeftRadius: "4px"
                                }), (0, c.tZ)(x.Box, {
                                    position: "absolute",
                                    top: "0",
                                    right: "0",
                                    w: "16px",
                                    h: "16px",
                                    borderTopWidth: "1px",
                                    borderRightWidth: "1px",
                                    borderColor: "gray.400",
                                    borderTopRightRadius: "4px"
                                }), (0, c.tZ)(x.Box, {
                                    position: "absolute",
                                    bottom: "0",
                                    left: "0",
                                    w: "16px",
                                    h: "16px",
                                    borderBottomWidth: "1px",
                                    borderLeftWidth: "1px",
                                    borderColor: "gray.400",
                                    borderBottomLeftRadius: "4px"
                                }), (0, c.tZ)(x.Box, {
                                    position: "absolute",
                                    bottom: "0",
                                    right: "0",
                                    w: "16px",
                                    h: "16px",
                                    borderBottomWidth: "1px",
                                    borderRightWidth: "1px",
                                    borderColor: "gray.400",
                                    borderBottomRightRadius: "4px"
                                }), (0, c.tZ)(G.QRCodeSVG, {
                                    value: `${"SUBDOMAIN"===_?"https":"http"}://${e}`,
                                    size: 52
                                }), (0, c.tZ)(x.Box, {
                                    display: "none",
                                    ref: t,
                                    className: "HpQrcode",
                                    children: (0, c.tZ)(G.QRCodeCanvas, {
                                        value: `${"SUBDOMAIN"===_?"https":"http"}://${e}`,
                                        size: 180
                                    })
                                })]
                            }), (0, c.BX)(x.Box, {
                                children: [(0, c.tZ)(x.Text, {
                                    fontSize: "12px",
                                    mb: "0",
                                    fontWeight: "600",
                                    color: "gray.900",
                                    children: (0, c.tZ)(r.FormattedMessage, {
                                        defaultMessage: "Website QR Code",
                                        id: "JXGRci"
                                    })
                                }), (0, c.BX)(x.Box, {
                                    mt: "4px",
                                    lineHeight: "1",
                                    children: [(0, c.tZ)(x.Text, {
                                        as: "span",
                                        mb: "0",
                                        fontSize: "12px",
                                        fontWeight: "400",
                                        color: "gray.600",
                                        children: (0, c.tZ)(r.FormattedMessage, {
                                            defaultMessage: "Scan QR code for instant access to your website on mobile, or",
                                            id: "/lSTGx"
                                        })
                                    }), (0, c.tZ)(x.Link, {
                                        _hover: {
                                            textDecoration: "none"
                                        },
                                        fontSize: "12px",
                                        display: "inline",
                                        p: "0",
                                        m: "0",
                                        fontWeight: "400",
                                        mx: "3px",
                                        color: "primary.600",
                                        cursor: "pointer",
                                        ref: l,
                                        children: (0, c.tZ)(r.FormattedMessage, {
                                            defaultMessage: "download QR",
                                            id: "5R+KZw"
                                        })
                                    })]
                                })]
                            })]
                        }), (0, c.BX)(x.Box, {
                            textAlign: "start",
                            alignSelf: "self-start",
                            children: [(0, c.tZ)(x.Text, {
                                as: "span",
                                display: "block",
                                fontSize: "12px",
                                fontWeight: "500",
                                color: "gray.600",
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    defaultMessage: "Share website",
                                    id: "vBTIBo"
                                })
                            }), (0, c.tZ)(x.Stack, {
                                mt: "8px",
                                align: "center",
                                gap: "6px",
                                direction: "row",
                                children: n.map((({
                                    name: _,
                                    url: e,
                                    Icon: l
                                }) => (0, c.tZ)(x.Link, {
                                    _hover: {
                                        textDecoration: "none"
                                    },
                                    cursor: "pointer",
                                    rel: "noopener noreferrer",
                                    target: "_blank",
                                    href: e,
                                    onClick: () => s(`shared website by ${_}`),
                                    children: (0, c.tZ)(l, {
                                        size: "24px"
                                    })
                                }, _)))
                            })]
                        })]
                    })
                },
                u = ({
                    domain: _,
                    domainType: e
                }) => (0, c.BX)(x.Box, {
                    textAlign: "center",
                    width: "100%",
                    p: "20px",
                    pb: "0px",
                    children: [(0, c.tZ)(x.Stack, {
                        display: "inline-flex",
                        margin: "0 0 4px 0",
                        justify: "center",
                        align: "center",
                        height: "100px",
                        width: "100px",
                        lineHeight: "85px",
                        fontSize: "50px",
                        borderRadius: "50%",
                        border: "2px solid",
                        borderColor: "primary.600",
                        color: "primary.600",
                        textAlign: "center",
                        children: (0, c.tZ)(m.Tick, {})
                    }), (0, c.tZ)(x.Heading, {
                        mb: "6px",
                        mt: "4px",
                        fontSize: "24px",
                        fontWeight: "600",
                        color: "gray.800",
                        children: (0, c.tZ)(r.FormattedMessage, {
                            id: "lOzDTt",
                            defaultMessage: "Congratulations!"
                        })
                    }), (0, c.tZ)(x.Text, {
                        as: "p",
                        fontSize: "14px",
                        fontWeight: "400",
                        color: "gray.500",
                        width: "90%",
                        margin: "0 auto 16px auto",
                        children: (0, c.tZ)(r.FormattedMessage, {
                            id: "UZNNq6",
                            defaultMessage: "Your website has been successfully published."
                        })
                    }), (0, c.BX)(x.Stack, {
                        direction: "row",
                        align: "center",
                        gap: "8px",
                        children: [(0, c.tZ)(x.Stack, {
                            direction: "row",
                            align: "center",
                            justify: "space-between",
                            width: "100%",
                            textAlign: "center",
                            borderRadius: "8px",
                            px: "12px",
                            color: "gray.500",
                            m: "auto",
                            bg: "gray.50",
                            border: "1px solid",
                            borderColor: "gray.300",
                            height: "40px",
                            cursor: "pointer",
                            children: (0, c.tZ)(x.Link, {
                                _hover: {
                                    textDecoration: "none"
                                },
                                py: "8px",
                                width: "100%",
                                height: "100%",
                                color: "gray.500",
                                fontWeight: "400",
                                fontSize: "16px",
                                textAlign: "start",
                                textDecorationLine: "none",
                                href: `${"SUBDOMAIN"===e?"https":"http"}://${_}`,
                                rel: "noopener noreferrer",
                                target: "_blank",
                                children: _
                            })
                        }), (0, c.tZ)(g, {
                            text: `${"SUBDOMAIN"===e?"https":"http"}://${_}`
                        })]
                    }), (0, c.tZ)(p, {
                        domain: _,
                        domainType: e
                    })]
                });
            var f = l(35618),
                h = l(63231),
                y = l.n(h);
            const b = {
                container: "Grid_container__2YVf2",
                "container-fluid": "Grid_container-fluid__1uxM4",
                "container-xxl": "Grid_container-xxl__z6OjV",
                "container-xl": "Grid_container-xl__iLLWF",
                "container-lg": "Grid_container-lg__6z-E3",
                "container-md": "Grid_container-md__xUqcI",
                "container-sm": "Grid_container-sm__NWVbt",
                row: "Grid_row__vjkhO",
                col: "Grid_col__HA-Cq",
                "row-cols-auto": "Grid_row-cols-auto__xQgbh",
                "row-cols-1": "Grid_row-cols-1__oDurG",
                "row-cols-2": "Grid_row-cols-2__6ILcR",
                "row-cols-3": "Grid_row-cols-3__fs62G",
                "row-cols-4": "Grid_row-cols-4__FPKAq",
                "row-cols-5": "Grid_row-cols-5__yFC0K",
                "row-cols-6": "Grid_row-cols-6__FelrO",
                "col-auto": "Grid_col-auto__5oe3j",
                "col-1": "Grid_col-1__GNPgH",
                "col-2": "Grid_col-2__xx2NX",
                "col-3": "Grid_col-3__Wo9Sm",
                "col-4": "Grid_col-4__KsCFz",
                "col-5": "Grid_col-5__yeRpi",
                "col-6": "Grid_col-6__4cg07",
                "col-7": "Grid_col-7__dRtfJ",
                "col-8": "Grid_col-8__iKjEC",
                "col-9": "Grid_col-9__K-F0W",
                "col-10": "Grid_col-10__hqYFE",
                "col-11": "Grid_col-11__uGPbp",
                "col-12": "Grid_col-12__R9B83",
                "offset-1": "Grid_offset-1__zsvQn",
                "offset-2": "Grid_offset-2__4Owmw",
                "offset-3": "Grid_offset-3__A8M1v",
                "offset-4": "Grid_offset-4__CYDPJ",
                "offset-5": "Grid_offset-5__TS0QI",
                "offset-6": "Grid_offset-6__LJcuK",
                "offset-7": "Grid_offset-7__zT-xf",
                "offset-8": "Grid_offset-8__Upj-g",
                "offset-9": "Grid_offset-9__TuRJx",
                "offset-10": "Grid_offset-10__znpCL",
                "offset-11": "Grid_offset-11__VaBtn",
                "g-0": "Grid_g-0__SuBtO",
                "gx-0": "Grid_gx-0__gDwIj",
                "gy-0": "Grid_gy-0__e-Y9o",
                "g-1": "Grid_g-1__Z0pSy",
                "gx-1": "Grid_gx-1__1Vh0f",
                "gy-1": "Grid_gy-1__8iYrO",
                "g-2": "Grid_g-2__agPO0",
                "gx-2": "Grid_gx-2__zNQEJ",
                "gy-2": "Grid_gy-2__H-HZi",
                "g-3": "Grid_g-3__-kDro",
                "gx-3": "Grid_gx-3__kLoCA",
                "gy-3": "Grid_gy-3__dsxXO",
                "g-4": "Grid_g-4__wtZ4g",
                "gx-4": "Grid_gx-4__FVx7b",
                "gy-4": "Grid_gy-4__YgqTg",
                "g-5": "Grid_g-5__LTAid",
                "gx-5": "Grid_gx-5__a2gzx",
                "gy-5": "Grid_gy-5__ORgws",
                "col-sm": "Grid_col-sm__MF9Ia",
                "row-cols-sm-auto": "Grid_row-cols-sm-auto__hiQOV",
                "row-cols-sm-1": "Grid_row-cols-sm-1__-7TNv",
                "row-cols-sm-2": "Grid_row-cols-sm-2__9ldJ0",
                "row-cols-sm-3": "Grid_row-cols-sm-3__mhvkz",
                "row-cols-sm-4": "Grid_row-cols-sm-4__X+jmX",
                "row-cols-sm-5": "Grid_row-cols-sm-5__9mbvU",
                "row-cols-sm-6": "Grid_row-cols-sm-6__p--9G",
                "col-sm-auto": "Grid_col-sm-auto__4fiFI",
                "col-sm-1": "Grid_col-sm-1__o1zjw",
                "col-sm-2": "Grid_col-sm-2__TNEDN",
                "col-sm-3": "Grid_col-sm-3__kkmBq",
                "col-sm-4": "Grid_col-sm-4__O+p0V",
                "col-sm-5": "Grid_col-sm-5__hDQuz",
                "col-sm-6": "Grid_col-sm-6__x9b5t",
                "col-sm-7": "Grid_col-sm-7__5Yubp",
                "col-sm-8": "Grid_col-sm-8__Xaky0",
                "col-sm-9": "Grid_col-sm-9__W7CEM",
                "col-sm-10": "Grid_col-sm-10__AQchf",
                "col-sm-11": "Grid_col-sm-11__hOwt6",
                "col-sm-12": "Grid_col-sm-12__oY-GZ",
                "offset-sm-0": "Grid_offset-sm-0__Z-Sir",
                "offset-sm-1": "Grid_offset-sm-1__Y9OCx",
                "offset-sm-2": "Grid_offset-sm-2__lKrVI",
                "offset-sm-3": "Grid_offset-sm-3__sZHy6",
                "offset-sm-4": "Grid_offset-sm-4__S4r10",
                "offset-sm-5": "Grid_offset-sm-5__xpZEr",
                "offset-sm-6": "Grid_offset-sm-6__Pi+t4",
                "offset-sm-7": "Grid_offset-sm-7__bPqiW",
                "offset-sm-8": "Grid_offset-sm-8__Bzh5U",
                "offset-sm-9": "Grid_offset-sm-9__fD-CZ",
                "offset-sm-10": "Grid_offset-sm-10__axwd3",
                "offset-sm-11": "Grid_offset-sm-11__edjlS",
                "g-sm-0": "Grid_g-sm-0__+Syiv",
                "gx-sm-0": "Grid_gx-sm-0__165uu",
                "gy-sm-0": "Grid_gy-sm-0__HNYBB",
                "g-sm-1": "Grid_g-sm-1__6Q63k",
                "gx-sm-1": "Grid_gx-sm-1__DSUZa",
                "gy-sm-1": "Grid_gy-sm-1__uqwcN",
                "g-sm-2": "Grid_g-sm-2__HyNJn",
                "gx-sm-2": "Grid_gx-sm-2__7BRIc",
                "gy-sm-2": "Grid_gy-sm-2__uRSZd",
                "g-sm-3": "Grid_g-sm-3__G2JOy",
                "gx-sm-3": "Grid_gx-sm-3__PgcoB",
                "gy-sm-3": "Grid_gy-sm-3__CBpG4",
                "g-sm-4": "Grid_g-sm-4__zBC9r",
                "gx-sm-4": "Grid_gx-sm-4__3T8sv",
                "gy-sm-4": "Grid_gy-sm-4__1gu5v",
                "g-sm-5": "Grid_g-sm-5__zUPy9",
                "gx-sm-5": "Grid_gx-sm-5__OlC+1",
                "gy-sm-5": "Grid_gy-sm-5__RVECS",
                "col-md": "Grid_col-md__8mKsi",
                "row-cols-md-auto": "Grid_row-cols-md-auto__9jv1M",
                "row-cols-md-1": "Grid_row-cols-md-1__WQ-rD",
                "row-cols-md-2": "Grid_row-cols-md-2__Ym-hF",
                "row-cols-md-3": "Grid_row-cols-md-3__oPU4a",
                "row-cols-md-4": "Grid_row-cols-md-4__b2RgU",
                "row-cols-md-5": "Grid_row-cols-md-5__YMUlp",
                "row-cols-md-6": "Grid_row-cols-md-6__R-dzf",
                "col-md-auto": "Grid_col-md-auto__J6rkk",
                "col-md-1": "Grid_col-md-1__XWRGd",
                "col-md-2": "Grid_col-md-2__esOIt",
                "col-md-3": "Grid_col-md-3__g09aX",
                "col-md-4": "Grid_col-md-4__kek4y",
                "col-md-5": "Grid_col-md-5__ZzWK+",
                "col-md-6": "Grid_col-md-6__Vr1Qk",
                "col-md-7": "Grid_col-md-7__78hjR",
                "col-md-8": "Grid_col-md-8__YPK-3",
                "col-md-9": "Grid_col-md-9__3b3c+",
                "col-md-10": "Grid_col-md-10__EkvIu",
                "col-md-11": "Grid_col-md-11__ZZbnK",
                "col-md-12": "Grid_col-md-12__IpmqY",
                "offset-md-0": "Grid_offset-md-0__X0nNT",
                "offset-md-1": "Grid_offset-md-1__TMZTs",
                "offset-md-2": "Grid_offset-md-2__-hFQA",
                "offset-md-3": "Grid_offset-md-3__ZJl8c",
                "offset-md-4": "Grid_offset-md-4__qLB+x",
                "offset-md-5": "Grid_offset-md-5__NYqBr",
                "offset-md-6": "Grid_offset-md-6__7XeWg",
                "offset-md-7": "Grid_offset-md-7__f78bp",
                "offset-md-8": "Grid_offset-md-8__ofWNk",
                "offset-md-9": "Grid_offset-md-9__T0SEQ",
                "offset-md-10": "Grid_offset-md-10__S2f0l",
                "offset-md-11": "Grid_offset-md-11__fuTZO",
                "g-md-0": "Grid_g-md-0__OJnTr",
                "gx-md-0": "Grid_gx-md-0__p-2UJ",
                "gy-md-0": "Grid_gy-md-0__IRAuk",
                "g-md-1": "Grid_g-md-1__KePI7",
                "gx-md-1": "Grid_gx-md-1__etVtZ",
                "gy-md-1": "Grid_gy-md-1__RHLPN",
                "g-md-2": "Grid_g-md-2__whCSE",
                "gx-md-2": "Grid_gx-md-2__RgC6f",
                "gy-md-2": "Grid_gy-md-2__xMxaZ",
                "g-md-3": "Grid_g-md-3__HoW+c",
                "gx-md-3": "Grid_gx-md-3__zBSnB",
                "gy-md-3": "Grid_gy-md-3__1xZlK",
                "g-md-4": "Grid_g-md-4__hlatq",
                "gx-md-4": "Grid_gx-md-4__IR252",
                "gy-md-4": "Grid_gy-md-4__bsB7Z",
                "g-md-5": "Grid_g-md-5__56uXy",
                "gx-md-5": "Grid_gx-md-5__siJ-R",
                "gy-md-5": "Grid_gy-md-5__grWs9",
                "col-lg": "Grid_col-lg__oInOA",
                "row-cols-lg-auto": "Grid_row-cols-lg-auto__v08g6",
                "row-cols-lg-1": "Grid_row-cols-lg-1__PidBK",
                "row-cols-lg-2": "Grid_row-cols-lg-2__sBP4D",
                "row-cols-lg-3": "Grid_row-cols-lg-3__EqWid",
                "row-cols-lg-4": "Grid_row-cols-lg-4__SnA3N",
                "row-cols-lg-5": "Grid_row-cols-lg-5__EqFRX",
                "row-cols-lg-6": "Grid_row-cols-lg-6__4mLIi",
                "col-lg-auto": "Grid_col-lg-auto__mJRJk",
                "col-lg-1": "Grid_col-lg-1__tO6-N",
                "col-lg-2": "Grid_col-lg-2__Qs+4i",
                "col-lg-3": "Grid_col-lg-3__sVVGg",
                "col-lg-4": "Grid_col-lg-4__C-nEF",
                "col-lg-5": "Grid_col-lg-5__LKN5t",
                "col-lg-6": "Grid_col-lg-6__Dkc3B",
                "col-lg-7": "Grid_col-lg-7__1tmaB",
                "col-lg-8": "Grid_col-lg-8__-QkbM",
                "col-lg-9": "Grid_col-lg-9__KIQ4E",
                "col-lg-10": "Grid_col-lg-10__YtjDd",
                "col-lg-11": "Grid_col-lg-11__sgSyc",
                "col-lg-12": "Grid_col-lg-12__PXeNX",
                "offset-lg-0": "Grid_offset-lg-0__OQ9Lb",
                "offset-lg-1": "Grid_offset-lg-1__8Ms-s",
                "offset-lg-2": "Grid_offset-lg-2__ebq7c",
                "offset-lg-3": "Grid_offset-lg-3__j1PaZ",
                "offset-lg-4": "Grid_offset-lg-4__9nWM0",
                "offset-lg-5": "Grid_offset-lg-5__8EVIv",
                "offset-lg-6": "Grid_offset-lg-6__M8UHB",
                "offset-lg-7": "Grid_offset-lg-7__wMgW9",
                "offset-lg-8": "Grid_offset-lg-8__eTwiW",
                "offset-lg-9": "Grid_offset-lg-9__dTMgF",
                "offset-lg-10": "Grid_offset-lg-10__DpR7W",
                "offset-lg-11": "Grid_offset-lg-11__XgFeh",
                "g-lg-0": "Grid_g-lg-0__04r0B",
                "gx-lg-0": "Grid_gx-lg-0__mAPWs",
                "gy-lg-0": "Grid_gy-lg-0__07z84",
                "g-lg-1": "Grid_g-lg-1__fxWu2",
                "gx-lg-1": "Grid_gx-lg-1__Wrglb",
                "gy-lg-1": "Grid_gy-lg-1__ZIP8j",
                "g-lg-2": "Grid_g-lg-2__NV9vH",
                "gx-lg-2": "Grid_gx-lg-2__k6QMf",
                "gy-lg-2": "Grid_gy-lg-2__PAZJb",
                "g-lg-3": "Grid_g-lg-3__SXDNN",
                "gx-lg-3": "Grid_gx-lg-3__9r4KD",
                "gy-lg-3": "Grid_gy-lg-3__wzQfM",
                "g-lg-4": "Grid_g-lg-4__yT8p8",
                "gx-lg-4": "Grid_gx-lg-4__8rhfk",
                "gy-lg-4": "Grid_gy-lg-4__cm8oE",
                "g-lg-5": "Grid_g-lg-5__MLbVv",
                "gx-lg-5": "Grid_gx-lg-5__R15z2",
                "gy-lg-5": "Grid_gy-lg-5__0rQ2S",
                "col-xl": "Grid_col-xl__WeN8j",
                "row-cols-xl-auto": "Grid_row-cols-xl-auto__iDqdn",
                "row-cols-xl-1": "Grid_row-cols-xl-1__GWAth",
                "row-cols-xl-2": "Grid_row-cols-xl-2__AnQZq",
                "row-cols-xl-3": "Grid_row-cols-xl-3__aHR0r",
                "row-cols-xl-4": "Grid_row-cols-xl-4__tsNjr",
                "row-cols-xl-5": "Grid_row-cols-xl-5__7A2bP",
                "row-cols-xl-6": "Grid_row-cols-xl-6__CfkNa",
                "col-xl-auto": "Grid_col-xl-auto__nicse",
                "col-xl-1": "Grid_col-xl-1__k3vQX",
                "col-xl-2": "Grid_col-xl-2__Azbpd",
                "col-xl-3": "Grid_col-xl-3__hZl9c",
                "col-xl-4": "Grid_col-xl-4__rf8b7",
                "col-xl-5": "Grid_col-xl-5__8u+iD",
                "col-xl-6": "Grid_col-xl-6__+QIz4",
                "col-xl-7": "Grid_col-xl-7__xvD8Z",
                "col-xl-8": "Grid_col-xl-8__iRHAj",
                "col-xl-9": "Grid_col-xl-9__tttms",
                "col-xl-10": "Grid_col-xl-10__t0EZL",
                "col-xl-11": "Grid_col-xl-11__yDZNp",
                "col-xl-12": "Grid_col-xl-12__pMAEj",
                "offset-xl-0": "Grid_offset-xl-0__2gv6H",
                "offset-xl-1": "Grid_offset-xl-1__jg2R-",
                "offset-xl-2": "Grid_offset-xl-2__+O3Ns",
                "offset-xl-3": "Grid_offset-xl-3__OZCcF",
                "offset-xl-4": "Grid_offset-xl-4__+Qs9I",
                "offset-xl-5": "Grid_offset-xl-5__ThHoP",
                "offset-xl-6": "Grid_offset-xl-6__zqFyU",
                "offset-xl-7": "Grid_offset-xl-7__T525j",
                "offset-xl-8": "Grid_offset-xl-8__7y9ML",
                "offset-xl-9": "Grid_offset-xl-9__ou826",
                "offset-xl-10": "Grid_offset-xl-10__b56Hz",
                "offset-xl-11": "Grid_offset-xl-11__K-Owa",
                "g-xl-0": "Grid_g-xl-0__MBjDj",
                "gx-xl-0": "Grid_gx-xl-0__iV4G8",
                "gy-xl-0": "Grid_gy-xl-0__znqmD",
                "g-xl-1": "Grid_g-xl-1__NfhYZ",
                "gx-xl-1": "Grid_gx-xl-1__QvQyS",
                "gy-xl-1": "Grid_gy-xl-1__2XqAJ",
                "g-xl-2": "Grid_g-xl-2__KcAFq",
                "gx-xl-2": "Grid_gx-xl-2__B2UB8",
                "gy-xl-2": "Grid_gy-xl-2__hIZpz",
                "g-xl-3": "Grid_g-xl-3__VHzHn",
                "gx-xl-3": "Grid_gx-xl-3__yqQWk",
                "gy-xl-3": "Grid_gy-xl-3__pzKMh",
                "g-xl-4": "Grid_g-xl-4__7F3oC",
                "gx-xl-4": "Grid_gx-xl-4__r42Io",
                "gy-xl-4": "Grid_gy-xl-4__qk5XA",
                "g-xl-5": "Grid_g-xl-5__cFhOT",
                "gx-xl-5": "Grid_gx-xl-5__TRE1F",
                "gy-xl-5": "Grid_gy-xl-5__XyRJp",
                "col-xxl": "Grid_col-xxl__-SF3N",
                "row-cols-xxl-auto": "Grid_row-cols-xxl-auto__UKFwp",
                "row-cols-xxl-1": "Grid_row-cols-xxl-1__tQopT",
                "row-cols-xxl-2": "Grid_row-cols-xxl-2__eKnDn",
                "row-cols-xxl-3": "Grid_row-cols-xxl-3__mIOFi",
                "row-cols-xxl-4": "Grid_row-cols-xxl-4__Vov-t",
                "row-cols-xxl-5": "Grid_row-cols-xxl-5__N39Rs",
                "row-cols-xxl-6": "Grid_row-cols-xxl-6__SmE3u",
                "col-xxl-auto": "Grid_col-xxl-auto__ANCAL",
                "col-xxl-1": "Grid_col-xxl-1__4P9if",
                "col-xxl-2": "Grid_col-xxl-2__hOlub",
                "col-xxl-3": "Grid_col-xxl-3__e4Rb9",
                "col-xxl-4": "Grid_col-xxl-4__pWXpO",
                "col-xxl-5": "Grid_col-xxl-5__6Bn5-",
                "col-xxl-6": "Grid_col-xxl-6__-mMsn",
                "col-xxl-7": "Grid_col-xxl-7__XU0ys",
                "col-xxl-8": "Grid_col-xxl-8__45u18",
                "col-xxl-9": "Grid_col-xxl-9__9+YL+",
                "col-xxl-10": "Grid_col-xxl-10__54MWm",
                "col-xxl-11": "Grid_col-xxl-11__6VekQ",
                "col-xxl-12": "Grid_col-xxl-12__LkwDv",
                "offset-xxl-0": "Grid_offset-xxl-0__p-60y",
                "offset-xxl-1": "Grid_offset-xxl-1__IPI+d",
                "offset-xxl-2": "Grid_offset-xxl-2__QO4Xh",
                "offset-xxl-3": "Grid_offset-xxl-3__4oWvf",
                "offset-xxl-4": "Grid_offset-xxl-4__ScGVh",
                "offset-xxl-5": "Grid_offset-xxl-5__p6l7V",
                "offset-xxl-6": "Grid_offset-xxl-6__GXjP2",
                "offset-xxl-7": "Grid_offset-xxl-7__LZH8G",
                "offset-xxl-8": "Grid_offset-xxl-8__SnOyn",
                "offset-xxl-9": "Grid_offset-xxl-9__i3UxV",
                "offset-xxl-10": "Grid_offset-xxl-10__OznhZ",
                "offset-xxl-11": "Grid_offset-xxl-11__Zau+o",
                "g-xxl-0": "Grid_g-xxl-0__DJlx6",
                "gx-xxl-0": "Grid_gx-xxl-0__v-VM8",
                "gy-xxl-0": "Grid_gy-xxl-0__ykeDV",
                "g-xxl-1": "Grid_g-xxl-1__zgXII",
                "gx-xxl-1": "Grid_gx-xxl-1__eYtsf",
                "gy-xxl-1": "Grid_gy-xxl-1__Z9Rpm",
                "g-xxl-2": "Grid_g-xxl-2__RwY4d",
                "gx-xxl-2": "Grid_gx-xxl-2__QB1MG",
                "gy-xxl-2": "Grid_gy-xxl-2__omHlO",
                "g-xxl-3": "Grid_g-xxl-3__SpDa6",
                "gx-xxl-3": "Grid_gx-xxl-3__Rgr8U",
                "gy-xxl-3": "Grid_gy-xxl-3__ILvHL",
                "g-xxl-4": "Grid_g-xxl-4__xfMEN",
                "gx-xxl-4": "Grid_gx-xxl-4__GN4zJ",
                "gy-xxl-4": "Grid_gy-xxl-4__BVbki",
                "g-xxl-5": "Grid_g-xxl-5__LnGBu",
                "gx-xxl-5": "Grid_gx-xxl-5__Mojal",
                "gy-xxl-5": "Grid_gy-xxl-5__SACbO",
                "d-inline": "Grid_d-inline__+P1-A",
                "d-inline-block": "Grid_d-inline-block__fy16B",
                "d-block": "Grid_d-block__Gyo0r",
                "d-grid": "Grid_d-grid__GYyIb",
                "d-inline-grid": "Grid_d-inline-grid__LTlbm",
                "d-table": "Grid_d-table__4i6bj",
                "d-table-row": "Grid_d-table-row__RBTYd",
                "d-table-cell": "Grid_d-table-cell__82TkP",
                "d-flex": "Grid_d-flex__HxWsl",
                "d-inline-flex": "Grid_d-inline-flex__mLaH1",
                "d-none": "Grid_d-none__mk7EM",
                "flex-fill": "Grid_flex-fill__JYqNt",
                "flex-row": "Grid_flex-row__rouKH",
                "flex-column": "Grid_flex-column__moG-7",
                "flex-row-reverse": "Grid_flex-row-reverse__0lT32",
                "flex-column-reverse": "Grid_flex-column-reverse__bXlhW",
                "flex-grow-0": "Grid_flex-grow-0__9UPgn",
                "flex-grow-1": "Grid_flex-grow-1__gH4dY",
                "flex-shrink-0": "Grid_flex-shrink-0__RrLzL",
                "flex-shrink-1": "Grid_flex-shrink-1__+m44L",
                "flex-wrap": "Grid_flex-wrap__I67X1",
                "flex-nowrap": "Grid_flex-nowrap__NaDmv",
                "flex-wrap-reverse": "Grid_flex-wrap-reverse__YGCyt",
                "justify-content-start": "Grid_justify-content-start__moZM8",
                "justify-content-end": "Grid_justify-content-end__OBigE",
                "justify-content-center": "Grid_justify-content-center__sJ8pH",
                "justify-content-between": "Grid_justify-content-between__dabTL",
                "justify-content-around": "Grid_justify-content-around__DZxwN",
                "justify-content-evenly": "Grid_justify-content-evenly__ES-Rq",
                "align-items-start": "Grid_align-items-start__EPopL",
                "align-items-end": "Grid_align-items-end__H6NVQ",
                "align-items-center": "Grid_align-items-center__qFnd1",
                "align-items-baseline": "Grid_align-items-baseline__h-DWM",
                "align-items-stretch": "Grid_align-items-stretch__V0IQQ",
                "align-content-start": "Grid_align-content-start__nOeB-",
                "align-content-end": "Grid_align-content-end__jmS-K",
                "align-content-center": "Grid_align-content-center__Zgfnj",
                "align-content-between": "Grid_align-content-between__JqQTN",
                "align-content-around": "Grid_align-content-around__GUlyP",
                "align-content-stretch": "Grid_align-content-stretch__c4rfA",
                "align-self-auto": "Grid_align-self-auto__Fx2gp",
                "align-self-start": "Grid_align-self-start__8c6Tg",
                "align-self-end": "Grid_align-self-end__+TJiO",
                "align-self-center": "Grid_align-self-center__8U03J",
                "align-self-baseline": "Grid_align-self-baseline__-kCar",
                "align-self-stretch": "Grid_align-self-stretch__l+YbZ",
                "order-first": "Grid_order-first__uNLdp",
                "order-0": "Grid_order-0__U2cNY",
                "order-1": "Grid_order-1__zx6dP",
                "order-2": "Grid_order-2__dsloX",
                "order-3": "Grid_order-3__r-Q61",
                "order-4": "Grid_order-4__41hJg",
                "order-5": "Grid_order-5__XJaSo",
                "order-last": "Grid_order-last__nD+ys",
                "m-0": "Grid_m-0__LW4IX",
                "m-1": "Grid_m-1__QimDB",
                "m-2": "Grid_m-2__sLlSK",
                "m-3": "Grid_m-3__R-oh5",
                "m-4": "Grid_m-4__BGTqo",
                "m-5": "Grid_m-5__qqTGE",
                "m-auto": "Grid_m-auto__x0et-",
                "mx-0": "Grid_mx-0__cRNAa",
                "mx-1": "Grid_mx-1__H29KX",
                "mx-2": "Grid_mx-2__lXgKc",
                "mx-3": "Grid_mx-3__xC9-O",
                "mx-4": "Grid_mx-4__EESwz",
                "mx-5": "Grid_mx-5__4SxPe",
                "mx-auto": "Grid_mx-auto__R+9Y4",
                "my-0": "Grid_my-0__OoUTG",
                "my-1": "Grid_my-1__vD8Sq",
                "my-2": "Grid_my-2__Z-AIa",
                "my-3": "Grid_my-3__08zfm",
                "my-4": "Grid_my-4__35z4I",
                "my-5": "Grid_my-5__alTFb",
                "my-auto": "Grid_my-auto__rlgZk",
                "mt-0": "Grid_mt-0__H55tq",
                "mt-1": "Grid_mt-1__2Nv2J",
                "mt-2": "Grid_mt-2__X11gn",
                "mt-3": "Grid_mt-3__FgzAg",
                "mt-4": "Grid_mt-4__aat2L",
                "mt-5": "Grid_mt-5__ZwiC4",
                "mt-auto": "Grid_mt-auto__pBMBA",
                "me-0": "Grid_me-0__ojSYp",
                "me-1": "Grid_me-1__Pe2gj",
                "me-2": "Grid_me-2__q9EO-",
                "me-3": "Grid_me-3__Nl+TC",
                "me-4": "Grid_me-4__Wo6bY",
                "me-5": "Grid_me-5__ush60",
                "me-auto": "Grid_me-auto__h8SEl",
                "mb-0": "Grid_mb-0__vVwng",
                "mb-1": "Grid_mb-1__A1yW-",
                "mb-2": "Grid_mb-2__US3+h",
                "mb-3": "Grid_mb-3__rgh54",
                "mb-4": "Grid_mb-4__elbV0",
                "mb-5": "Grid_mb-5__BPMSD",
                "mb-auto": "Grid_mb-auto__hdZKF",
                "ms-0": "Grid_ms-0__QVMdF",
                "ms-1": "Grid_ms-1__kjJEF",
                "ms-2": "Grid_ms-2__d6-Wu",
                "ms-3": "Grid_ms-3__yf-uK",
                "ms-4": "Grid_ms-4__GnDAg",
                "ms-5": "Grid_ms-5__RE1E2",
                "ms-auto": "Grid_ms-auto__BX6Vq",
                "p-0": "Grid_p-0__JpZfZ",
                "p-1": "Grid_p-1__uBzR9",
                "p-2": "Grid_p-2__ezySq",
                "p-3": "Grid_p-3__NUlM7",
                "p-4": "Grid_p-4__8sEV-",
                "p-5": "Grid_p-5__zrmHl",
                "px-0": "Grid_px-0__Bp8Dn",
                "px-1": "Grid_px-1__WAT4w",
                "px-2": "Grid_px-2__coljq",
                "px-3": "Grid_px-3__gnp1z",
                "px-4": "Grid_px-4__JyCkW",
                "px-5": "Grid_px-5__EjwWW",
                "py-0": "Grid_py-0__wcx-1",
                "py-1": "Grid_py-1__tqXt9",
                "py-2": "Grid_py-2__v+Vux",
                "py-3": "Grid_py-3__dr4dv",
                "py-4": "Grid_py-4__L+hCJ",
                "py-5": "Grid_py-5__eI+lJ",
                "pt-0": "Grid_pt-0__xWAYx",
                "pt-1": "Grid_pt-1__O978T",
                "pt-2": "Grid_pt-2__+BVAC",
                "pt-3": "Grid_pt-3__bJLOJ",
                "pt-4": "Grid_pt-4__RzQK1",
                "pt-5": "Grid_pt-5__UQqP0",
                "pe-0": "Grid_pe-0__+Y+fl",
                "pe-1": "Grid_pe-1__XK38T",
                "pe-2": "Grid_pe-2__loP3s",
                "pe-3": "Grid_pe-3__7ZwjH",
                "pe-4": "Grid_pe-4__TSxoe",
                "pe-5": "Grid_pe-5__tvN4g",
                "pb-0": "Grid_pb-0__hfOIr",
                "pb-1": "Grid_pb-1__YJJar",
                "pb-2": "Grid_pb-2__zCunI",
                "pb-3": "Grid_pb-3__94KPh",
                "pb-4": "Grid_pb-4__FnbW+",
                "pb-5": "Grid_pb-5__3yWcY",
                "ps-0": "Grid_ps-0__cMpeJ",
                "ps-1": "Grid_ps-1__LjM9s",
                "ps-2": "Grid_ps-2__W5fXp",
                "ps-3": "Grid_ps-3__PXaUC",
                "ps-4": "Grid_ps-4__ZuixI",
                "ps-5": "Grid_ps-5__-hJy+",
                "d-sm-inline": "Grid_d-sm-inline__DezqA",
                "d-sm-inline-block": "Grid_d-sm-inline-block__ciYVS",
                "d-sm-block": "Grid_d-sm-block__G-XhG",
                "d-sm-grid": "Grid_d-sm-grid__TFvdt",
                "d-sm-inline-grid": "Grid_d-sm-inline-grid__OS9Lw",
                "d-sm-table": "Grid_d-sm-table__pN9pB",
                "d-sm-table-row": "Grid_d-sm-table-row__GT5d+",
                "d-sm-table-cell": "Grid_d-sm-table-cell__lDlDL",
                "d-sm-flex": "Grid_d-sm-flex__tTH8f",
                "d-sm-inline-flex": "Grid_d-sm-inline-flex__1wNVm",
                "d-sm-none": "Grid_d-sm-none__GKfZb",
                "flex-sm-fill": "Grid_flex-sm-fill__0CqKF",
                "flex-sm-row": "Grid_flex-sm-row__mu41-",
                "flex-sm-column": "Grid_flex-sm-column__uDIpc",
                "flex-sm-row-reverse": "Grid_flex-sm-row-reverse__arg71",
                "flex-sm-column-reverse": "Grid_flex-sm-column-reverse__j0Ey4",
                "flex-sm-grow-0": "Grid_flex-sm-grow-0__86H8s",
                "flex-sm-grow-1": "Grid_flex-sm-grow-1__Ndeew",
                "flex-sm-shrink-0": "Grid_flex-sm-shrink-0__w+mY4",
                "flex-sm-shrink-1": "Grid_flex-sm-shrink-1__j5Qgr",
                "flex-sm-wrap": "Grid_flex-sm-wrap__3x3Af",
                "flex-sm-nowrap": "Grid_flex-sm-nowrap__BRdue",
                "flex-sm-wrap-reverse": "Grid_flex-sm-wrap-reverse__cOKMC",
                "justify-content-sm-start": "Grid_justify-content-sm-start__iSQKN",
                "justify-content-sm-end": "Grid_justify-content-sm-end__rLGi5",
                "justify-content-sm-center": "Grid_justify-content-sm-center__FeYxU",
                "justify-content-sm-between": "Grid_justify-content-sm-between__dPtan",
                "justify-content-sm-around": "Grid_justify-content-sm-around__IQAqG",
                "justify-content-sm-evenly": "Grid_justify-content-sm-evenly__gevDR",
                "align-items-sm-start": "Grid_align-items-sm-start__xvINf",
                "align-items-sm-end": "Grid_align-items-sm-end__P87Go",
                "align-items-sm-center": "Grid_align-items-sm-center__kgN+D",
                "align-items-sm-baseline": "Grid_align-items-sm-baseline__uBlEK",
                "align-items-sm-stretch": "Grid_align-items-sm-stretch__V-pqq",
                "align-content-sm-start": "Grid_align-content-sm-start__io4QX",
                "align-content-sm-end": "Grid_align-content-sm-end__9UvKS",
                "align-content-sm-center": "Grid_align-content-sm-center__vbZpq",
                "align-content-sm-between": "Grid_align-content-sm-between__qMw6P",
                "align-content-sm-around": "Grid_align-content-sm-around__4MqwA",
                "align-content-sm-stretch": "Grid_align-content-sm-stretch__L7tnj",
                "align-self-sm-auto": "Grid_align-self-sm-auto__fqYPB",
                "align-self-sm-start": "Grid_align-self-sm-start__YaqaA",
                "align-self-sm-end": "Grid_align-self-sm-end__ePsdF",
                "align-self-sm-center": "Grid_align-self-sm-center__JXM5s",
                "align-self-sm-baseline": "Grid_align-self-sm-baseline__gnKQY",
                "align-self-sm-stretch": "Grid_align-self-sm-stretch__GppNf",
                "order-sm-first": "Grid_order-sm-first__4NYnK",
                "order-sm-0": "Grid_order-sm-0__5KV58",
                "order-sm-1": "Grid_order-sm-1__GpLTt",
                "order-sm-2": "Grid_order-sm-2__qPsn5",
                "order-sm-3": "Grid_order-sm-3__yXcTp",
                "order-sm-4": "Grid_order-sm-4__RagRM",
                "order-sm-5": "Grid_order-sm-5__+Z42P",
                "order-sm-last": "Grid_order-sm-last__5dvzg",
                "m-sm-0": "Grid_m-sm-0__zHaSK",
                "m-sm-1": "Grid_m-sm-1__k1r8z",
                "m-sm-2": "Grid_m-sm-2__zmWwY",
                "m-sm-3": "Grid_m-sm-3__oTlsL",
                "m-sm-4": "Grid_m-sm-4__z3z99",
                "m-sm-5": "Grid_m-sm-5__56oLI",
                "m-sm-auto": "Grid_m-sm-auto__wkAjv",
                "mx-sm-0": "Grid_mx-sm-0__fQqLx",
                "mx-sm-1": "Grid_mx-sm-1__AkUkb",
                "mx-sm-2": "Grid_mx-sm-2__Sdlf7",
                "mx-sm-3": "Grid_mx-sm-3__KyhTY",
                "mx-sm-4": "Grid_mx-sm-4__LL4Vh",
                "mx-sm-5": "Grid_mx-sm-5__5hQSh",
                "mx-sm-auto": "Grid_mx-sm-auto__M6ZOb",
                "my-sm-0": "Grid_my-sm-0__v7jnM",
                "my-sm-1": "Grid_my-sm-1__IiHqC",
                "my-sm-2": "Grid_my-sm-2__kdOjI",
                "my-sm-3": "Grid_my-sm-3__orKpc",
                "my-sm-4": "Grid_my-sm-4__n7JQI",
                "my-sm-5": "Grid_my-sm-5__IlS43",
                "my-sm-auto": "Grid_my-sm-auto__GjPey",
                "mt-sm-0": "Grid_mt-sm-0__KMpjQ",
                "mt-sm-1": "Grid_mt-sm-1__mLuUm",
                "mt-sm-2": "Grid_mt-sm-2__tJ8Fg",
                "mt-sm-3": "Grid_mt-sm-3__D2h-X",
                "mt-sm-4": "Grid_mt-sm-4__bS2DF",
                "mt-sm-5": "Grid_mt-sm-5__xhvOR",
                "mt-sm-auto": "Grid_mt-sm-auto__Vd1VA",
                "me-sm-0": "Grid_me-sm-0__Vj37o",
                "me-sm-1": "Grid_me-sm-1__p66fV",
                "me-sm-2": "Grid_me-sm-2__lIfIc",
                "me-sm-3": "Grid_me-sm-3__Vss3E",
                "me-sm-4": "Grid_me-sm-4__E0CHy",
                "me-sm-5": "Grid_me-sm-5__fcKzK",
                "me-sm-auto": "Grid_me-sm-auto__83dpJ",
                "mb-sm-0": "Grid_mb-sm-0__QUKnv",
                "mb-sm-1": "Grid_mb-sm-1__V8KhB",
                "mb-sm-2": "Grid_mb-sm-2__el5qu",
                "mb-sm-3": "Grid_mb-sm-3__yq-pL",
                "mb-sm-4": "Grid_mb-sm-4__iJ-R4",
                "mb-sm-5": "Grid_mb-sm-5__nm8xR",
                "mb-sm-auto": "Grid_mb-sm-auto__zPg+e",
                "ms-sm-0": "Grid_ms-sm-0__L9s5r",
                "ms-sm-1": "Grid_ms-sm-1__uzPCL",
                "ms-sm-2": "Grid_ms-sm-2__pQZqi",
                "ms-sm-3": "Grid_ms-sm-3__J4GuP",
                "ms-sm-4": "Grid_ms-sm-4__4I4UX",
                "ms-sm-5": "Grid_ms-sm-5__zwnW-",
                "ms-sm-auto": "Grid_ms-sm-auto__YpUoK",
                "p-sm-0": "Grid_p-sm-0__kS4th",
                "p-sm-1": "Grid_p-sm-1__gDopm",
                "p-sm-2": "Grid_p-sm-2__5QybZ",
                "p-sm-3": "Grid_p-sm-3__GJmKh",
                "p-sm-4": "Grid_p-sm-4__awcDM",
                "p-sm-5": "Grid_p-sm-5__TCpYA",
                "px-sm-0": "Grid_px-sm-0__-gN0Z",
                "px-sm-1": "Grid_px-sm-1__30SfI",
                "px-sm-2": "Grid_px-sm-2__9Hqcv",
                "px-sm-3": "Grid_px-sm-3__8Nay+",
                "px-sm-4": "Grid_px-sm-4__VZyaS",
                "px-sm-5": "Grid_px-sm-5__ZOTMB",
                "py-sm-0": "Grid_py-sm-0__wEkix",
                "py-sm-1": "Grid_py-sm-1__zEZ9p",
                "py-sm-2": "Grid_py-sm-2__Xk8C6",
                "py-sm-3": "Grid_py-sm-3__6re1-",
                "py-sm-4": "Grid_py-sm-4__6rCho",
                "py-sm-5": "Grid_py-sm-5__4efhB",
                "pt-sm-0": "Grid_pt-sm-0__dqWd4",
                "pt-sm-1": "Grid_pt-sm-1__dk+zM",
                "pt-sm-2": "Grid_pt-sm-2__5c19c",
                "pt-sm-3": "Grid_pt-sm-3__BV+vv",
                "pt-sm-4": "Grid_pt-sm-4__GADSt",
                "pt-sm-5": "Grid_pt-sm-5__OYOuF",
                "pe-sm-0": "Grid_pe-sm-0__MgLt+",
                "pe-sm-1": "Grid_pe-sm-1__+2pVr",
                "pe-sm-2": "Grid_pe-sm-2__1jTSP",
                "pe-sm-3": "Grid_pe-sm-3__t0Npw",
                "pe-sm-4": "Grid_pe-sm-4__HrUWe",
                "pe-sm-5": "Grid_pe-sm-5__uqgfQ",
                "pb-sm-0": "Grid_pb-sm-0__TnEEW",
                "pb-sm-1": "Grid_pb-sm-1__XLYq3",
                "pb-sm-2": "Grid_pb-sm-2__oK6Q5",
                "pb-sm-3": "Grid_pb-sm-3__4BfeQ",
                "pb-sm-4": "Grid_pb-sm-4__z8Isd",
                "pb-sm-5": "Grid_pb-sm-5__tndBP",
                "ps-sm-0": "Grid_ps-sm-0__YWjdZ",
                "ps-sm-1": "Grid_ps-sm-1__7Ucdn",
                "ps-sm-2": "Grid_ps-sm-2__LhCVu",
                "ps-sm-3": "Grid_ps-sm-3__HuYHd",
                "ps-sm-4": "Grid_ps-sm-4__mYvYO",
                "ps-sm-5": "Grid_ps-sm-5__L6+3t",
                "d-md-inline": "Grid_d-md-inline__2NmTF",
                "d-md-inline-block": "Grid_d-md-inline-block__jCyZg",
                "d-md-block": "Grid_d-md-block__AFg3b",
                "d-md-grid": "Grid_d-md-grid__Qu-ic",
                "d-md-inline-grid": "Grid_d-md-inline-grid__Y3umC",
                "d-md-table": "Grid_d-md-table__qXNvS",
                "d-md-table-row": "Grid_d-md-table-row__udxzf",
                "d-md-table-cell": "Grid_d-md-table-cell__DxoIl",
                "d-md-flex": "Grid_d-md-flex__I12T7",
                "d-md-inline-flex": "Grid_d-md-inline-flex__4KBSf",
                "d-md-none": "Grid_d-md-none__EgoeY",
                "flex-md-fill": "Grid_flex-md-fill__Haqn7",
                "flex-md-row": "Grid_flex-md-row__V2Mhl",
                "flex-md-column": "Grid_flex-md-column__H193z",
                "flex-md-row-reverse": "Grid_flex-md-row-reverse__LdoRG",
                "flex-md-column-reverse": "Grid_flex-md-column-reverse__65VR4",
                "flex-md-grow-0": "Grid_flex-md-grow-0__sxcf2",
                "flex-md-grow-1": "Grid_flex-md-grow-1__O+brS",
                "flex-md-shrink-0": "Grid_flex-md-shrink-0__jTSOA",
                "flex-md-shrink-1": "Grid_flex-md-shrink-1__XYkFg",
                "flex-md-wrap": "Grid_flex-md-wrap__BuOI6",
                "flex-md-nowrap": "Grid_flex-md-nowrap__Tgc5Q",
                "flex-md-wrap-reverse": "Grid_flex-md-wrap-reverse__OYzzW",
                "justify-content-md-start": "Grid_justify-content-md-start__WMJTl",
                "justify-content-md-end": "Grid_justify-content-md-end__l69kE",
                "justify-content-md-center": "Grid_justify-content-md-center__KV3Vd",
                "justify-content-md-between": "Grid_justify-content-md-between__Ov6Wz",
                "justify-content-md-around": "Grid_justify-content-md-around__jr1JM",
                "justify-content-md-evenly": "Grid_justify-content-md-evenly__v91yN",
                "align-items-md-start": "Grid_align-items-md-start__uG0lo",
                "align-items-md-end": "Grid_align-items-md-end__Ai72i",
                "align-items-md-center": "Grid_align-items-md-center__ZDysS",
                "align-items-md-baseline": "Grid_align-items-md-baseline__+0QL3",
                "align-items-md-stretch": "Grid_align-items-md-stretch__fwsxf",
                "align-content-md-start": "Grid_align-content-md-start__u+TpV",
                "align-content-md-end": "Grid_align-content-md-end__gwncL",
                "align-content-md-center": "Grid_align-content-md-center__5iPm2",
                "align-content-md-between": "Grid_align-content-md-between__b6WsB",
                "align-content-md-around": "Grid_align-content-md-around__tvLHn",
                "align-content-md-stretch": "Grid_align-content-md-stretch__Jo2mN",
                "align-self-md-auto": "Grid_align-self-md-auto__fkNep",
                "align-self-md-start": "Grid_align-self-md-start__O8+65",
                "align-self-md-end": "Grid_align-self-md-end__rrodV",
                "align-self-md-center": "Grid_align-self-md-center__WjDz7",
                "align-self-md-baseline": "Grid_align-self-md-baseline__jlRUz",
                "align-self-md-stretch": "Grid_align-self-md-stretch__YZL3e",
                "order-md-first": "Grid_order-md-first__xAfbQ",
                "order-md-0": "Grid_order-md-0__KtI5q",
                "order-md-1": "Grid_order-md-1__dFnYn",
                "order-md-2": "Grid_order-md-2__6sBTn",
                "order-md-3": "Grid_order-md-3__RtXwg",
                "order-md-4": "Grid_order-md-4__MaH9N",
                "order-md-5": "Grid_order-md-5__H8cMS",
                "order-md-last": "Grid_order-md-last__AaE98",
                "m-md-0": "Grid_m-md-0__ie9FA",
                "m-md-1": "Grid_m-md-1__rfDHu",
                "m-md-2": "Grid_m-md-2__-sZQq",
                "m-md-3": "Grid_m-md-3__b2JTd",
                "m-md-4": "Grid_m-md-4__6F8eo",
                "m-md-5": "Grid_m-md-5__0nwz+",
                "m-md-auto": "Grid_m-md-auto__h9Xdu",
                "mx-md-0": "Grid_mx-md-0__SRBw4",
                "mx-md-1": "Grid_mx-md-1__q5aDk",
                "mx-md-2": "Grid_mx-md-2__6TJI9",
                "mx-md-3": "Grid_mx-md-3__tTCk3",
                "mx-md-4": "Grid_mx-md-4__QH050",
                "mx-md-5": "Grid_mx-md-5__BgLdQ",
                "mx-md-auto": "Grid_mx-md-auto__Ek6If",
                "my-md-0": "Grid_my-md-0__o2g3l",
                "my-md-1": "Grid_my-md-1__GLhdS",
                "my-md-2": "Grid_my-md-2__k+87e",
                "my-md-3": "Grid_my-md-3__uAHju",
                "my-md-4": "Grid_my-md-4__2TiMZ",
                "my-md-5": "Grid_my-md-5__AywAV",
                "my-md-auto": "Grid_my-md-auto__oUbDY",
                "mt-md-0": "Grid_mt-md-0__1aRud",
                "mt-md-1": "Grid_mt-md-1__qiOoP",
                "mt-md-2": "Grid_mt-md-2__czT64",
                "mt-md-3": "Grid_mt-md-3__yUhNJ",
                "mt-md-4": "Grid_mt-md-4__+6rV-",
                "mt-md-5": "Grid_mt-md-5__Svvjq",
                "mt-md-auto": "Grid_mt-md-auto__29Q3D",
                "me-md-0": "Grid_me-md-0__C-TQj",
                "me-md-1": "Grid_me-md-1__l-gkL",
                "me-md-2": "Grid_me-md-2__DZP6E",
                "me-md-3": "Grid_me-md-3__Lh-iY",
                "me-md-4": "Grid_me-md-4__wsNZT",
                "me-md-5": "Grid_me-md-5__Rq9y3",
                "me-md-auto": "Grid_me-md-auto__A3n6I",
                "mb-md-0": "Grid_mb-md-0__dvlDC",
                "mb-md-1": "Grid_mb-md-1__yQJdF",
                "mb-md-2": "Grid_mb-md-2__-0fZU",
                "mb-md-3": "Grid_mb-md-3__yWA5t",
                "mb-md-4": "Grid_mb-md-4__j0Ljx",
                "mb-md-5": "Grid_mb-md-5__04zYt",
                "mb-md-auto": "Grid_mb-md-auto__BOoVJ",
                "ms-md-0": "Grid_ms-md-0__LTSbP",
                "ms-md-1": "Grid_ms-md-1__3VI0a",
                "ms-md-2": "Grid_ms-md-2__03pIi",
                "ms-md-3": "Grid_ms-md-3__SXh8V",
                "ms-md-4": "Grid_ms-md-4__+3hVd",
                "ms-md-5": "Grid_ms-md-5__wWr14",
                "ms-md-auto": "Grid_ms-md-auto__jGbLG",
                "p-md-0": "Grid_p-md-0__AonbN",
                "p-md-1": "Grid_p-md-1__NJBTh",
                "p-md-2": "Grid_p-md-2__-VXXC",
                "p-md-3": "Grid_p-md-3__ZWPVh",
                "p-md-4": "Grid_p-md-4__b4Ek0",
                "p-md-5": "Grid_p-md-5__1VEfC",
                "px-md-0": "Grid_px-md-0__tsZ4u",
                "px-md-1": "Grid_px-md-1__1g6PW",
                "px-md-2": "Grid_px-md-2__gvvxn",
                "px-md-3": "Grid_px-md-3__vAqsK",
                "px-md-4": "Grid_px-md-4__+DeOb",
                "px-md-5": "Grid_px-md-5__fy6Ic",
                "py-md-0": "Grid_py-md-0__JJfjD",
                "py-md-1": "Grid_py-md-1__ZLXTe",
                "py-md-2": "Grid_py-md-2__sLCNP",
                "py-md-3": "Grid_py-md-3__Fx3B0",
                "py-md-4": "Grid_py-md-4__abqef",
                "py-md-5": "Grid_py-md-5__ULPBp",
                "pt-md-0": "Grid_pt-md-0__3uSqg",
                "pt-md-1": "Grid_pt-md-1__RXWey",
                "pt-md-2": "Grid_pt-md-2__PRgqZ",
                "pt-md-3": "Grid_pt-md-3__4gMpY",
                "pt-md-4": "Grid_pt-md-4__rQBY9",
                "pt-md-5": "Grid_pt-md-5__vAZ+w",
                "pe-md-0": "Grid_pe-md-0__cDC0I",
                "pe-md-1": "Grid_pe-md-1__jfo+q",
                "pe-md-2": "Grid_pe-md-2__cLXcn",
                "pe-md-3": "Grid_pe-md-3__xX-zg",
                "pe-md-4": "Grid_pe-md-4__XmT-b",
                "pe-md-5": "Grid_pe-md-5__vtNJX",
                "pb-md-0": "Grid_pb-md-0__wmfh1",
                "pb-md-1": "Grid_pb-md-1__TkKSI",
                "pb-md-2": "Grid_pb-md-2__x+P4g",
                "pb-md-3": "Grid_pb-md-3__qb3Ot",
                "pb-md-4": "Grid_pb-md-4__53Z4U",
                "pb-md-5": "Grid_pb-md-5__6RjLH",
                "ps-md-0": "Grid_ps-md-0__oZE+k",
                "ps-md-1": "Grid_ps-md-1__f8psD",
                "ps-md-2": "Grid_ps-md-2__9dwpc",
                "ps-md-3": "Grid_ps-md-3__8nwAM",
                "ps-md-4": "Grid_ps-md-4__3YKXS",
                "ps-md-5": "Grid_ps-md-5__IMrbz",
                "d-lg-inline": "Grid_d-lg-inline__daVI0",
                "d-lg-inline-block": "Grid_d-lg-inline-block__Dyp6W",
                "d-lg-block": "Grid_d-lg-block__pKSei",
                "d-lg-grid": "Grid_d-lg-grid__5WUil",
                "d-lg-inline-grid": "Grid_d-lg-inline-grid__xzvu9",
                "d-lg-table": "Grid_d-lg-table__3oazZ",
                "d-lg-table-row": "Grid_d-lg-table-row__kH4ga",
                "d-lg-table-cell": "Grid_d-lg-table-cell__GObY6",
                "d-lg-flex": "Grid_d-lg-flex__CCk8s",
                "d-lg-inline-flex": "Grid_d-lg-inline-flex__2kpXl",
                "d-lg-none": "Grid_d-lg-none__S1NOS",
                "flex-lg-fill": "Grid_flex-lg-fill__2VdfW",
                "flex-lg-row": "Grid_flex-lg-row__gNLOB",
                "flex-lg-column": "Grid_flex-lg-column__+DrxR",
                "flex-lg-row-reverse": "Grid_flex-lg-row-reverse__iJznJ",
                "flex-lg-column-reverse": "Grid_flex-lg-column-reverse__PMAyx",
                "flex-lg-grow-0": "Grid_flex-lg-grow-0__Gcsft",
                "flex-lg-grow-1": "Grid_flex-lg-grow-1__CwnIc",
                "flex-lg-shrink-0": "Grid_flex-lg-shrink-0__g6s3f",
                "flex-lg-shrink-1": "Grid_flex-lg-shrink-1__J-YgF",
                "flex-lg-wrap": "Grid_flex-lg-wrap__XHgVS",
                "flex-lg-nowrap": "Grid_flex-lg-nowrap__u6UaN",
                "flex-lg-wrap-reverse": "Grid_flex-lg-wrap-reverse__lbJbd",
                "justify-content-lg-start": "Grid_justify-content-lg-start__uh-Zs",
                "justify-content-lg-end": "Grid_justify-content-lg-end__zS7aH",
                "justify-content-lg-center": "Grid_justify-content-lg-center__qF6d8",
                "justify-content-lg-between": "Grid_justify-content-lg-between__YE-hh",
                "justify-content-lg-around": "Grid_justify-content-lg-around__+3+zO",
                "justify-content-lg-evenly": "Grid_justify-content-lg-evenly__0ky3l",
                "align-items-lg-start": "Grid_align-items-lg-start__PRoso",
                "align-items-lg-end": "Grid_align-items-lg-end__J9KKG",
                "align-items-lg-center": "Grid_align-items-lg-center__flyoS",
                "align-items-lg-baseline": "Grid_align-items-lg-baseline__34MKb",
                "align-items-lg-stretch": "Grid_align-items-lg-stretch__nfRC-",
                "align-content-lg-start": "Grid_align-content-lg-start__RWJXs",
                "align-content-lg-end": "Grid_align-content-lg-end__8Nf9f",
                "align-content-lg-center": "Grid_align-content-lg-center__vViXo",
                "align-content-lg-between": "Grid_align-content-lg-between__pCHzs",
                "align-content-lg-around": "Grid_align-content-lg-around__VOpJp",
                "align-content-lg-stretch": "Grid_align-content-lg-stretch__km1J1",
                "align-self-lg-auto": "Grid_align-self-lg-auto__GPtlt",
                "align-self-lg-start": "Grid_align-self-lg-start__HP12R",
                "align-self-lg-end": "Grid_align-self-lg-end__70u4P",
                "align-self-lg-center": "Grid_align-self-lg-center__tlahl",
                "align-self-lg-baseline": "Grid_align-self-lg-baseline__DdqQg",
                "align-self-lg-stretch": "Grid_align-self-lg-stretch__O87y0",
                "order-lg-first": "Grid_order-lg-first__tMxBg",
                "order-lg-0": "Grid_order-lg-0__ndhK9",
                "order-lg-1": "Grid_order-lg-1__e0r2g",
                "order-lg-2": "Grid_order-lg-2__V-Oo2",
                "order-lg-3": "Grid_order-lg-3__NXlfN",
                "order-lg-4": "Grid_order-lg-4__I3iUH",
                "order-lg-5": "Grid_order-lg-5__DkTqf",
                "order-lg-last": "Grid_order-lg-last__YyXq+",
                "m-lg-0": "Grid_m-lg-0__vAhUY",
                "m-lg-1": "Grid_m-lg-1__4++1A",
                "m-lg-2": "Grid_m-lg-2__uvxG2",
                "m-lg-3": "Grid_m-lg-3__xOFvY",
                "m-lg-4": "Grid_m-lg-4__h9L2N",
                "m-lg-5": "Grid_m-lg-5__PFcNG",
                "m-lg-auto": "Grid_m-lg-auto__ulttC",
                "mx-lg-0": "Grid_mx-lg-0__4Nn5i",
                "mx-lg-1": "Grid_mx-lg-1__5JW1D",
                "mx-lg-2": "Grid_mx-lg-2__GsvFS",
                "mx-lg-3": "Grid_mx-lg-3__tF4yR",
                "mx-lg-4": "Grid_mx-lg-4__ASRfh",
                "mx-lg-5": "Grid_mx-lg-5__SBMYa",
                "mx-lg-auto": "Grid_mx-lg-auto__jRLFX",
                "my-lg-0": "Grid_my-lg-0__ALGre",
                "my-lg-1": "Grid_my-lg-1__EeR4T",
                "my-lg-2": "Grid_my-lg-2__0BVmR",
                "my-lg-3": "Grid_my-lg-3__pOVUX",
                "my-lg-4": "Grid_my-lg-4__mCcB6",
                "my-lg-5": "Grid_my-lg-5__zvLBA",
                "my-lg-auto": "Grid_my-lg-auto__648aD",
                "mt-lg-0": "Grid_mt-lg-0__f6wdy",
                "mt-lg-1": "Grid_mt-lg-1__wOaLu",
                "mt-lg-2": "Grid_mt-lg-2__syOI7",
                "mt-lg-3": "Grid_mt-lg-3__fMG98",
                "mt-lg-4": "Grid_mt-lg-4__V0+M2",
                "mt-lg-5": "Grid_mt-lg-5__xSsAx",
                "mt-lg-auto": "Grid_mt-lg-auto__RjFFz",
                "me-lg-0": "Grid_me-lg-0__uKfjN",
                "me-lg-1": "Grid_me-lg-1__LEM8B",
                "me-lg-2": "Grid_me-lg-2__utgX1",
                "me-lg-3": "Grid_me-lg-3__TMn8s",
                "me-lg-4": "Grid_me-lg-4__8qADv",
                "me-lg-5": "Grid_me-lg-5__mv76w",
                "me-lg-auto": "Grid_me-lg-auto__hoL8r",
                "mb-lg-0": "Grid_mb-lg-0__AC4pj",
                "mb-lg-1": "Grid_mb-lg-1__MQ9C1",
                "mb-lg-2": "Grid_mb-lg-2__2WIAE",
                "mb-lg-3": "Grid_mb-lg-3__4E01k",
                "mb-lg-4": "Grid_mb-lg-4__zUOMK",
                "mb-lg-5": "Grid_mb-lg-5__ND-+u",
                "mb-lg-auto": "Grid_mb-lg-auto__ncQAC",
                "ms-lg-0": "Grid_ms-lg-0__y1foG",
                "ms-lg-1": "Grid_ms-lg-1__BMgnB",
                "ms-lg-2": "Grid_ms-lg-2__nHKk5",
                "ms-lg-3": "Grid_ms-lg-3__fT95u",
                "ms-lg-4": "Grid_ms-lg-4__Ul21G",
                "ms-lg-5": "Grid_ms-lg-5__q-V3v",
                "ms-lg-auto": "Grid_ms-lg-auto__JWi55",
                "p-lg-0": "Grid_p-lg-0__8w+wO",
                "p-lg-1": "Grid_p-lg-1__kl2xL",
                "p-lg-2": "Grid_p-lg-2__lHeCq",
                "p-lg-3": "Grid_p-lg-3__RkYkO",
                "p-lg-4": "Grid_p-lg-4__NygHZ",
                "p-lg-5": "Grid_p-lg-5__8IpUx",
                "px-lg-0": "Grid_px-lg-0__j0B8f",
                "px-lg-1": "Grid_px-lg-1__MaYG+",
                "px-lg-2": "Grid_px-lg-2__kXRxA",
                "px-lg-3": "Grid_px-lg-3__84pDi",
                "px-lg-4": "Grid_px-lg-4__8K6Oo",
                "px-lg-5": "Grid_px-lg-5__ysmgw",
                "py-lg-0": "Grid_py-lg-0__yxEZu",
                "py-lg-1": "Grid_py-lg-1__urv3L",
                "py-lg-2": "Grid_py-lg-2__w0EYA",
                "py-lg-3": "Grid_py-lg-3__sH+fP",
                "py-lg-4": "Grid_py-lg-4__8aNg7",
                "py-lg-5": "Grid_py-lg-5__8Hvi2",
                "pt-lg-0": "Grid_pt-lg-0__fVvYU",
                "pt-lg-1": "Grid_pt-lg-1__zi0Xe",
                "pt-lg-2": "Grid_pt-lg-2__5IE1C",
                "pt-lg-3": "Grid_pt-lg-3__-edeW",
                "pt-lg-4": "Grid_pt-lg-4__wyOs1",
                "pt-lg-5": "Grid_pt-lg-5__qEPBp",
                "pe-lg-0": "Grid_pe-lg-0__ev4J7",
                "pe-lg-1": "Grid_pe-lg-1__ygp85",
                "pe-lg-2": "Grid_pe-lg-2__KcvYi",
                "pe-lg-3": "Grid_pe-lg-3__n9m7S",
                "pe-lg-4": "Grid_pe-lg-4__9dQvo",
                "pe-lg-5": "Grid_pe-lg-5__docUA",
                "pb-lg-0": "Grid_pb-lg-0__oy1S2",
                "pb-lg-1": "Grid_pb-lg-1__syM7e",
                "pb-lg-2": "Grid_pb-lg-2__Gsp7u",
                "pb-lg-3": "Grid_pb-lg-3__RVPvm",
                "pb-lg-4": "Grid_pb-lg-4__Ig8zd",
                "pb-lg-5": "Grid_pb-lg-5__HfX77",
                "ps-lg-0": "Grid_ps-lg-0__BBKRy",
                "ps-lg-1": "Grid_ps-lg-1__HBJhI",
                "ps-lg-2": "Grid_ps-lg-2__86nE8",
                "ps-lg-3": "Grid_ps-lg-3__NtRov",
                "ps-lg-4": "Grid_ps-lg-4__mLqfd",
                "ps-lg-5": "Grid_ps-lg-5__z-6vL",
                "d-xl-inline": "Grid_d-xl-inline__GF1w+",
                "d-xl-inline-block": "Grid_d-xl-inline-block__kgxPi",
                "d-xl-block": "Grid_d-xl-block__3Zws6",
                "d-xl-grid": "Grid_d-xl-grid__KlbCm",
                "d-xl-inline-grid": "Grid_d-xl-inline-grid__q8JFC",
                "d-xl-table": "Grid_d-xl-table__qYhV1",
                "d-xl-table-row": "Grid_d-xl-table-row__k4SyS",
                "d-xl-table-cell": "Grid_d-xl-table-cell__6-MlO",
                "d-xl-flex": "Grid_d-xl-flex__6IQfI",
                "d-xl-inline-flex": "Grid_d-xl-inline-flex__oNvYi",
                "d-xl-none": "Grid_d-xl-none__Ess5u",
                "flex-xl-fill": "Grid_flex-xl-fill__5LYvu",
                "flex-xl-row": "Grid_flex-xl-row__OHe2K",
                "flex-xl-column": "Grid_flex-xl-column__eJHeH",
                "flex-xl-row-reverse": "Grid_flex-xl-row-reverse__lWN6d",
                "flex-xl-column-reverse": "Grid_flex-xl-column-reverse__ugsLB",
                "flex-xl-grow-0": "Grid_flex-xl-grow-0__QxaiL",
                "flex-xl-grow-1": "Grid_flex-xl-grow-1__0XrD+",
                "flex-xl-shrink-0": "Grid_flex-xl-shrink-0__LehC9",
                "flex-xl-shrink-1": "Grid_flex-xl-shrink-1__T25-F",
                "flex-xl-wrap": "Grid_flex-xl-wrap__mfB0O",
                "flex-xl-nowrap": "Grid_flex-xl-nowrap__3WyjT",
                "flex-xl-wrap-reverse": "Grid_flex-xl-wrap-reverse__ho77Q",
                "justify-content-xl-start": "Grid_justify-content-xl-start__AOHVV",
                "justify-content-xl-end": "Grid_justify-content-xl-end__1ZS4f",
                "justify-content-xl-center": "Grid_justify-content-xl-center__fSXJt",
                "justify-content-xl-between": "Grid_justify-content-xl-between__8Mfqt",
                "justify-content-xl-around": "Grid_justify-content-xl-around__G1HBt",
                "justify-content-xl-evenly": "Grid_justify-content-xl-evenly__qb0e7",
                "align-items-xl-start": "Grid_align-items-xl-start__0F7IC",
                "align-items-xl-end": "Grid_align-items-xl-end__Is1vO",
                "align-items-xl-center": "Grid_align-items-xl-center__65iIH",
                "align-items-xl-baseline": "Grid_align-items-xl-baseline__Y-e3M",
                "align-items-xl-stretch": "Grid_align-items-xl-stretch__X8udv",
                "align-content-xl-start": "Grid_align-content-xl-start__UyUMa",
                "align-content-xl-end": "Grid_align-content-xl-end__yxv-7",
                "align-content-xl-center": "Grid_align-content-xl-center__ssHPm",
                "align-content-xl-between": "Grid_align-content-xl-between__OsQET",
                "align-content-xl-around": "Grid_align-content-xl-around__EavxQ",
                "align-content-xl-stretch": "Grid_align-content-xl-stretch__bLLCW",
                "align-self-xl-auto": "Grid_align-self-xl-auto__emIOP",
                "align-self-xl-start": "Grid_align-self-xl-start__+79RP",
                "align-self-xl-end": "Grid_align-self-xl-end__1DEmk",
                "align-self-xl-center": "Grid_align-self-xl-center__TqOpL",
                "align-self-xl-baseline": "Grid_align-self-xl-baseline__gNWM4",
                "align-self-xl-stretch": "Grid_align-self-xl-stretch__R+ZIX",
                "order-xl-first": "Grid_order-xl-first__Bh260",
                "order-xl-0": "Grid_order-xl-0__kANvO",
                "order-xl-1": "Grid_order-xl-1__DoP+h",
                "order-xl-2": "Grid_order-xl-2__MrAmh",
                "order-xl-3": "Grid_order-xl-3__MyDdw",
                "order-xl-4": "Grid_order-xl-4__kmOVX",
                "order-xl-5": "Grid_order-xl-5__7eOgm",
                "order-xl-last": "Grid_order-xl-last__xhHnO",
                "m-xl-0": "Grid_m-xl-0__1IGOT",
                "m-xl-1": "Grid_m-xl-1__Ms-Gl",
                "m-xl-2": "Grid_m-xl-2__QlpaA",
                "m-xl-3": "Grid_m-xl-3__D4guU",
                "m-xl-4": "Grid_m-xl-4__YyZUl",
                "m-xl-5": "Grid_m-xl-5__U9evo",
                "m-xl-auto": "Grid_m-xl-auto__-SEiE",
                "mx-xl-0": "Grid_mx-xl-0__hORrc",
                "mx-xl-1": "Grid_mx-xl-1__5MXX4",
                "mx-xl-2": "Grid_mx-xl-2__Gq6-p",
                "mx-xl-3": "Grid_mx-xl-3__IFGKH",
                "mx-xl-4": "Grid_mx-xl-4__jiDFq",
                "mx-xl-5": "Grid_mx-xl-5__sQfSX",
                "mx-xl-auto": "Grid_mx-xl-auto__Bptgh",
                "my-xl-0": "Grid_my-xl-0__-4344",
                "my-xl-1": "Grid_my-xl-1__UMkuR",
                "my-xl-2": "Grid_my-xl-2__ZxeFb",
                "my-xl-3": "Grid_my-xl-3__ISiRl",
                "my-xl-4": "Grid_my-xl-4__3hkFs",
                "my-xl-5": "Grid_my-xl-5__9FMmw",
                "my-xl-auto": "Grid_my-xl-auto__2eE5O",
                "mt-xl-0": "Grid_mt-xl-0__Gytfl",
                "mt-xl-1": "Grid_mt-xl-1__txc1p",
                "mt-xl-2": "Grid_mt-xl-2__QUEf+",
                "mt-xl-3": "Grid_mt-xl-3__MXjDN",
                "mt-xl-4": "Grid_mt-xl-4__am1Bd",
                "mt-xl-5": "Grid_mt-xl-5__eUgN4",
                "mt-xl-auto": "Grid_mt-xl-auto__-6L2t",
                "me-xl-0": "Grid_me-xl-0__fYrJ-",
                "me-xl-1": "Grid_me-xl-1__J1sm0",
                "me-xl-2": "Grid_me-xl-2__xKKSF",
                "me-xl-3": "Grid_me-xl-3__L3W7G",
                "me-xl-4": "Grid_me-xl-4__9jm9U",
                "me-xl-5": "Grid_me-xl-5__FzHMg",
                "me-xl-auto": "Grid_me-xl-auto__4oBQY",
                "mb-xl-0": "Grid_mb-xl-0__xJjVX",
                "mb-xl-1": "Grid_mb-xl-1__eDLjZ",
                "mb-xl-2": "Grid_mb-xl-2__3w5+L",
                "mb-xl-3": "Grid_mb-xl-3__mM8kG",
                "mb-xl-4": "Grid_mb-xl-4__LsXND",
                "mb-xl-5": "Grid_mb-xl-5__3sDx0",
                "mb-xl-auto": "Grid_mb-xl-auto__BkgAz",
                "ms-xl-0": "Grid_ms-xl-0__ySGcw",
                "ms-xl-1": "Grid_ms-xl-1__zGiwu",
                "ms-xl-2": "Grid_ms-xl-2__j69E3",
                "ms-xl-3": "Grid_ms-xl-3__L0hjK",
                "ms-xl-4": "Grid_ms-xl-4__+Tttk",
                "ms-xl-5": "Grid_ms-xl-5__DA-p2",
                "ms-xl-auto": "Grid_ms-xl-auto__vjCwG",
                "p-xl-0": "Grid_p-xl-0__azkgm",
                "p-xl-1": "Grid_p-xl-1__x9Nox",
                "p-xl-2": "Grid_p-xl-2__IZ--M",
                "p-xl-3": "Grid_p-xl-3__BRnBD",
                "p-xl-4": "Grid_p-xl-4__P9YBj",
                "p-xl-5": "Grid_p-xl-5__eDx3t",
                "px-xl-0": "Grid_px-xl-0__7sGCK",
                "px-xl-1": "Grid_px-xl-1__7Qv2S",
                "px-xl-2": "Grid_px-xl-2__2g4o+",
                "px-xl-3": "Grid_px-xl-3__vfseo",
                "px-xl-4": "Grid_px-xl-4__PIMuI",
                "px-xl-5": "Grid_px-xl-5__EAhCO",
                "py-xl-0": "Grid_py-xl-0__fOSWq",
                "py-xl-1": "Grid_py-xl-1__oqLmw",
                "py-xl-2": "Grid_py-xl-2__VzMbY",
                "py-xl-3": "Grid_py-xl-3__smfre",
                "py-xl-4": "Grid_py-xl-4__pEa+N",
                "py-xl-5": "Grid_py-xl-5__rZoWa",
                "pt-xl-0": "Grid_pt-xl-0__iWr+Z",
                "pt-xl-1": "Grid_pt-xl-1__1P8Bc",
                "pt-xl-2": "Grid_pt-xl-2__9YFXh",
                "pt-xl-3": "Grid_pt-xl-3__JGuL4",
                "pt-xl-4": "Grid_pt-xl-4__YazEs",
                "pt-xl-5": "Grid_pt-xl-5__I20X+",
                "pe-xl-0": "Grid_pe-xl-0__Z1hJM",
                "pe-xl-1": "Grid_pe-xl-1__BKw6p",
                "pe-xl-2": "Grid_pe-xl-2__sMqcg",
                "pe-xl-3": "Grid_pe-xl-3__TYoGO",
                "pe-xl-4": "Grid_pe-xl-4__xepT1",
                "pe-xl-5": "Grid_pe-xl-5__CABNy",
                "pb-xl-0": "Grid_pb-xl-0__9ETPh",
                "pb-xl-1": "Grid_pb-xl-1__MLxR+",
                "pb-xl-2": "Grid_pb-xl-2__YCm6+",
                "pb-xl-3": "Grid_pb-xl-3__K+A6P",
                "pb-xl-4": "Grid_pb-xl-4__Q5MP0",
                "pb-xl-5": "Grid_pb-xl-5__GwMRL",
                "ps-xl-0": "Grid_ps-xl-0__KsbLO",
                "ps-xl-1": "Grid_ps-xl-1__JXQoX",
                "ps-xl-2": "Grid_ps-xl-2__Tc6i2",
                "ps-xl-3": "Grid_ps-xl-3__WRre6",
                "ps-xl-4": "Grid_ps-xl-4__cxQww",
                "ps-xl-5": "Grid_ps-xl-5__cVPFG",
                "d-xxl-inline": "Grid_d-xxl-inline__p0t1D",
                "d-xxl-inline-block": "Grid_d-xxl-inline-block__dqs9e",
                "d-xxl-block": "Grid_d-xxl-block__33CeF",
                "d-xxl-grid": "Grid_d-xxl-grid__qgvVH",
                "d-xxl-inline-grid": "Grid_d-xxl-inline-grid__YQmHJ",
                "d-xxl-table": "Grid_d-xxl-table__cvtS9",
                "d-xxl-table-row": "Grid_d-xxl-table-row__WjDg3",
                "d-xxl-table-cell": "Grid_d-xxl-table-cell__s2nXW",
                "d-xxl-flex": "Grid_d-xxl-flex__r1f9Q",
                "d-xxl-inline-flex": "Grid_d-xxl-inline-flex__h6nHU",
                "d-xxl-none": "Grid_d-xxl-none__jHQWw",
                "flex-xxl-fill": "Grid_flex-xxl-fill__CJYcw",
                "flex-xxl-row": "Grid_flex-xxl-row__IfAeP",
                "flex-xxl-column": "Grid_flex-xxl-column__oyDif",
                "flex-xxl-row-reverse": "Grid_flex-xxl-row-reverse__fLCNV",
                "flex-xxl-column-reverse": "Grid_flex-xxl-column-reverse__6wN7i",
                "flex-xxl-grow-0": "Grid_flex-xxl-grow-0__TtA38",
                "flex-xxl-grow-1": "Grid_flex-xxl-grow-1__lUsA2",
                "flex-xxl-shrink-0": "Grid_flex-xxl-shrink-0__XzTO1",
                "flex-xxl-shrink-1": "Grid_flex-xxl-shrink-1__k+cMR",
                "flex-xxl-wrap": "Grid_flex-xxl-wrap__edWor",
                "flex-xxl-nowrap": "Grid_flex-xxl-nowrap__Oe0UD",
                "flex-xxl-wrap-reverse": "Grid_flex-xxl-wrap-reverse__L8mod",
                "justify-content-xxl-start": "Grid_justify-content-xxl-start__W2u-O",
                "justify-content-xxl-end": "Grid_justify-content-xxl-end__vxeIM",
                "justify-content-xxl-center": "Grid_justify-content-xxl-center__gc-8-",
                "justify-content-xxl-between": "Grid_justify-content-xxl-between__R4bK8",
                "justify-content-xxl-around": "Grid_justify-content-xxl-around__Y+MMH",
                "justify-content-xxl-evenly": "Grid_justify-content-xxl-evenly__j43HK",
                "align-items-xxl-start": "Grid_align-items-xxl-start__pR9xT",
                "align-items-xxl-end": "Grid_align-items-xxl-end__J+tIM",
                "align-items-xxl-center": "Grid_align-items-xxl-center__cixIB",
                "align-items-xxl-baseline": "Grid_align-items-xxl-baseline__SZJqM",
                "align-items-xxl-stretch": "Grid_align-items-xxl-stretch__dAU-W",
                "align-content-xxl-start": "Grid_align-content-xxl-start__wQaYG",
                "align-content-xxl-end": "Grid_align-content-xxl-end__Gez2f",
                "align-content-xxl-center": "Grid_align-content-xxl-center__QsvBX",
                "align-content-xxl-between": "Grid_align-content-xxl-between__itWTf",
                "align-content-xxl-around": "Grid_align-content-xxl-around__DWwZW",
                "align-content-xxl-stretch": "Grid_align-content-xxl-stretch__AXSjq",
                "align-self-xxl-auto": "Grid_align-self-xxl-auto__+zkCt",
                "align-self-xxl-start": "Grid_align-self-xxl-start__6qf+5",
                "align-self-xxl-end": "Grid_align-self-xxl-end__XwVwc",
                "align-self-xxl-center": "Grid_align-self-xxl-center__wAZRN",
                "align-self-xxl-baseline": "Grid_align-self-xxl-baseline__GofEm",
                "align-self-xxl-stretch": "Grid_align-self-xxl-stretch__o4Itp",
                "order-xxl-first": "Grid_order-xxl-first__ra+oe",
                "order-xxl-0": "Grid_order-xxl-0__0HLIl",
                "order-xxl-1": "Grid_order-xxl-1__otaB0",
                "order-xxl-2": "Grid_order-xxl-2__ZlZLF",
                "order-xxl-3": "Grid_order-xxl-3__Q5f8r",
                "order-xxl-4": "Grid_order-xxl-4__HUf-V",
                "order-xxl-5": "Grid_order-xxl-5__uwgZ1",
                "order-xxl-last": "Grid_order-xxl-last__uo-2x",
                "m-xxl-0": "Grid_m-xxl-0__KVCN9",
                "m-xxl-1": "Grid_m-xxl-1__dvilJ",
                "m-xxl-2": "Grid_m-xxl-2__BspkD",
                "m-xxl-3": "Grid_m-xxl-3__oOWOu",
                "m-xxl-4": "Grid_m-xxl-4__wyUbe",
                "m-xxl-5": "Grid_m-xxl-5__fKjeo",
                "m-xxl-auto": "Grid_m-xxl-auto__kqn7-",
                "mx-xxl-0": "Grid_mx-xxl-0__d7h4Q",
                "mx-xxl-1": "Grid_mx-xxl-1__UL2eJ",
                "mx-xxl-2": "Grid_mx-xxl-2__A5jND",
                "mx-xxl-3": "Grid_mx-xxl-3__fMfyZ",
                "mx-xxl-4": "Grid_mx-xxl-4__79eXF",
                "mx-xxl-5": "Grid_mx-xxl-5__UziWY",
                "mx-xxl-auto": "Grid_mx-xxl-auto__-IB5G",
                "my-xxl-0": "Grid_my-xxl-0__smA9L",
                "my-xxl-1": "Grid_my-xxl-1__2mOds",
                "my-xxl-2": "Grid_my-xxl-2__nxW7y",
                "my-xxl-3": "Grid_my-xxl-3__ogtY8",
                "my-xxl-4": "Grid_my-xxl-4__zv52B",
                "my-xxl-5": "Grid_my-xxl-5__At8zy",
                "my-xxl-auto": "Grid_my-xxl-auto__YaZTO",
                "mt-xxl-0": "Grid_mt-xxl-0__JlhT8",
                "mt-xxl-1": "Grid_mt-xxl-1__Cuxws",
                "mt-xxl-2": "Grid_mt-xxl-2__2hmTQ",
                "mt-xxl-3": "Grid_mt-xxl-3__C-UD2",
                "mt-xxl-4": "Grid_mt-xxl-4__DuM37",
                "mt-xxl-5": "Grid_mt-xxl-5__i6SNl",
                "mt-xxl-auto": "Grid_mt-xxl-auto__5y4bn",
                "me-xxl-0": "Grid_me-xxl-0__R8SLn",
                "me-xxl-1": "Grid_me-xxl-1__H6WZl",
                "me-xxl-2": "Grid_me-xxl-2__seLZI",
                "me-xxl-3": "Grid_me-xxl-3__W42Xg",
                "me-xxl-4": "Grid_me-xxl-4__PQTWK",
                "me-xxl-5": "Grid_me-xxl-5__83GMX",
                "me-xxl-auto": "Grid_me-xxl-auto__VYWJy",
                "mb-xxl-0": "Grid_mb-xxl-0__l+kkm",
                "mb-xxl-1": "Grid_mb-xxl-1__4X4MW",
                "mb-xxl-2": "Grid_mb-xxl-2__jkArY",
                "mb-xxl-3": "Grid_mb-xxl-3__+RZ-K",
                "mb-xxl-4": "Grid_mb-xxl-4__Oz465",
                "mb-xxl-5": "Grid_mb-xxl-5__GZH0T",
                "mb-xxl-auto": "Grid_mb-xxl-auto__29axl",
                "ms-xxl-0": "Grid_ms-xxl-0__jEsrG",
                "ms-xxl-1": "Grid_ms-xxl-1__OLyoK",
                "ms-xxl-2": "Grid_ms-xxl-2__rEZze",
                "ms-xxl-3": "Grid_ms-xxl-3__RJ3Wb",
                "ms-xxl-4": "Grid_ms-xxl-4__CRGH0",
                "ms-xxl-5": "Grid_ms-xxl-5__-IrQr",
                "ms-xxl-auto": "Grid_ms-xxl-auto__0Fswx",
                "p-xxl-0": "Grid_p-xxl-0__4HAWQ",
                "p-xxl-1": "Grid_p-xxl-1__Y0nl6",
                "p-xxl-2": "Grid_p-xxl-2__DZmHH",
                "p-xxl-3": "Grid_p-xxl-3__TdkHI",
                "p-xxl-4": "Grid_p-xxl-4__Lvney",
                "p-xxl-5": "Grid_p-xxl-5__UlYWV",
                "px-xxl-0": "Grid_px-xxl-0__plIej",
                "px-xxl-1": "Grid_px-xxl-1__7Ho82",
                "px-xxl-2": "Grid_px-xxl-2__83Zmi",
                "px-xxl-3": "Grid_px-xxl-3__HLDiE",
                "px-xxl-4": "Grid_px-xxl-4__+l0Or",
                "px-xxl-5": "Grid_px-xxl-5__kEuNZ",
                "py-xxl-0": "Grid_py-xxl-0__OvOKi",
                "py-xxl-1": "Grid_py-xxl-1__m0Yoz",
                "py-xxl-2": "Grid_py-xxl-2__41Grz",
                "py-xxl-3": "Grid_py-xxl-3__evVAF",
                "py-xxl-4": "Grid_py-xxl-4__5qCgl",
                "py-xxl-5": "Grid_py-xxl-5__w+ouK",
                "pt-xxl-0": "Grid_pt-xxl-0__AELsz",
                "pt-xxl-1": "Grid_pt-xxl-1__z1bQm",
                "pt-xxl-2": "Grid_pt-xxl-2__rHLyW",
                "pt-xxl-3": "Grid_pt-xxl-3__eyQWN",
                "pt-xxl-4": "Grid_pt-xxl-4__jE5W0",
                "pt-xxl-5": "Grid_pt-xxl-5__5xs7X",
                "pe-xxl-0": "Grid_pe-xxl-0__9+Bg1",
                "pe-xxl-1": "Grid_pe-xxl-1__9Q8Dv",
                "pe-xxl-2": "Grid_pe-xxl-2__mJJ+D",
                "pe-xxl-3": "Grid_pe-xxl-3__2FUEs",
                "pe-xxl-4": "Grid_pe-xxl-4__q9Ym0",
                "pe-xxl-5": "Grid_pe-xxl-5__ePQNO",
                "pb-xxl-0": "Grid_pb-xxl-0__NzpSs",
                "pb-xxl-1": "Grid_pb-xxl-1__z0ZzO",
                "pb-xxl-2": "Grid_pb-xxl-2__0jCpR",
                "pb-xxl-3": "Grid_pb-xxl-3__WtI6m",
                "pb-xxl-4": "Grid_pb-xxl-4__2lngJ",
                "pb-xxl-5": "Grid_pb-xxl-5__c6G+G",
                "ps-xxl-0": "Grid_ps-xxl-0__oVgSp",
                "ps-xxl-1": "Grid_ps-xxl-1__u1hUF",
                "ps-xxl-2": "Grid_ps-xxl-2__hoGSF",
                "ps-xxl-3": "Grid_ps-xxl-3__kxPTe",
                "ps-xxl-4": "Grid_ps-xxl-4__xWl1z",
                "ps-xxl-5": "Grid_ps-xxl-5__ft0dq",
                "d-print-inline": "Grid_d-print-inline__e-dBE",
                "d-print-inline-block": "Grid_d-print-inline-block__IPGyT",
                "d-print-block": "Grid_d-print-block__A2Er5",
                "d-print-grid": "Grid_d-print-grid__ZD+oZ",
                "d-print-inline-grid": "Grid_d-print-inline-grid__85aO5",
                "d-print-table": "Grid_d-print-table__YDq4R",
                "d-print-table-row": "Grid_d-print-table-row__IIxb6",
                "d-print-table-cell": "Grid_d-print-table-cell__bSn-7",
                "d-print-flex": "Grid_d-print-flex__AMnjA",
                "d-print-inline-flex": "Grid_d-print-inline-flex__prLz5",
                "d-print-none": "Grid_d-print-none__pn0TT"
            };

            function w(_) {
                if (_) {
                    const e = Number(_);
                    return Number.isInteger(e) && e > 0 && e <= 12
                }
                return !1
            }
            const Z = ({
                    children: _
                }) => (0, c.tZ)("div", {
                    className: b.container,
                    children: _
                }),
                v = ({
                    className: _,
                    children: e
                }) => (0, c.tZ)("div", {
                    className: y()(b.row, _),
                    children: e
                }),
                k = ({
                    className: _,
                    span: e,
                    xs: l,
                    sm: i,
                    md: r,
                    lg: t,
                    xl: d,
                    children: s
                }) => {
                    const o = y()(_, b.col, {
                        [b[`col-${e}`]]: w(e)
                    }, {
                        [b[`col-${l}`]]: w(l)
                    }, {
                        [b[`col-sm-${i}`]]: w(i)
                    }, {
                        [b[`col-md-${r}`]]: w(r)
                    }, {
                        [b[`col-lg-${t}`]]: w(t)
                    }, {
                        [b[`col-xl-${d}`]]: w(d)
                    });
                    return (0, c.tZ)("div", {
                        className: o,
                        children: s
                    })
                },
                B = "PublishPopup_buttonCol__AT+G2",
                M = ({
                    headerContent: _,
                    content: e,
                    expandAreaContent: l,
                    isActive: i,
                    onChange: r
                }) => (0, c.BX)("div", {
                    className: y()("SelectCard_card__41Jvi", {
                        "SelectCard_--isActive__0HNaF": i
                    }),
                    onClick: r,
                    children: [(0, c.tZ)("div", {
                        className: "SelectCard_cardHeader__zZgFy",
                        children: _
                    }), e && (0, c.tZ)("div", {
                        className: "SelectCard_body__5BKRk",
                        children: e
                    }), l && (0, c.tZ)("div", {
                        className: "SelectCard_expandArea__BfMdO",
                        children: l
                    })]
                }),
                S = ({
                    onChange: _,
                    style: e,
                    width: l,
                    className: i,
                    value: r,
                    postfix: t
                }) => (0, c.BX)("div", {
                    className: "TextInput_inputContainer__E9Q8V",
                    style: Object.assign({}, e, {
                        width: l
                    }),
                    children: [(0, c.tZ)("input", {
                        value: r,
                        onChange: _,
                        className: y()("TextInput_input__IOodk", i)
                    }), t && (0, c.tZ)("div", {
                        className: "TextInput_postfix__JkSTy",
                        children: t
                    })]
                }),
                C = ({
                    className: _,
                    size: e
                }) => (0, c.BX)("svg", {
                    className: _,
                    width: e,
                    height: e,
                    viewBox: "0 0 512.001 512.001",
                    children: [(0, c.tZ)("path", {
                        style: {
                            fill: "#FFC600"
                        },
                        d: "M231.168,122.58l-20.167,10l-37.953-10c-33.85,0-61.29-27.44-61.29-61.29l0,0 c0-33.85,27.44-61.29,61.29-61.29h58.12L231.168,122.58L231.168,122.58z"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#FFE92F"
                        },
                        d: "M340.933,122.58l19.568,10l22.93-10c24.751,0,44.816-20.065,44.816-44.816l0,0 c0-24.751-20.065-44.816-44.816-44.816h-42.498V122.58z"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#FFD82F"
                        },
                        d: "M294.571,122.58l27.93,10l15.727-10c24.111,0,43.657-19.546,43.657-43.657v-2.318 c0-24.111-19.546-43.657-43.657-43.657l0,0c-24.111,0-43.657,19.546-43.657,43.657l-10,14.895L294.571,122.58z"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#F4AF00"
                        },
                        d: "M294.571,122.58l-35.571,10l-24.134-10c-32.974,0-59.705-26.731-59.705-59.705v-3.17 C175.162,26.731,201.892,0,234.866,0l0,0c32.974,0,59.705,26.731,59.705,59.705V122.58z"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#7A6CF7"
                        },
                        d: "M315.986,512H32.012c-10.234,0-18.53-8.296-18.53-18.53V141.127c0-10.234,8.296-18.53,18.53-18.53 h283.974V512z"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#6959E2"
                        },
                        d: "M252.302,122.597v105.751c0,131.896-106.923,238.82-238.82,238.82l0,0v26.303 c0,10.234,8.296,18.53,18.53,18.53h241.881c11.42,0,20.678-9.258,20.678-20.678V146.55c0-13.229-10.724-23.953-23.953-23.953 H252.302z"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#837AF4"
                        },
                        d: "M479.99,512H313.714c-10.572,0-19.143-8.57-19.143-19.143v-347.11c0-12.785,10.365-23.15,23.15-23.15 h162.268c10.234,0,18.53,8.296,18.53,18.53V493.47C498.519,503.704,490.223,512,479.99,512z"
                    }), (0, c.BX)("g", {
                        children: [(0, c.tZ)("circle", {
                            style: {
                                fill: "#F288E8"
                            },
                            cx: "57.936",
                            cy: "208.34",
                            r: "21.362"
                        }), (0, c.tZ)("circle", {
                            style: {
                                fill: "#F288E8"
                            },
                            cx: "116.741",
                            cy: "354.82",
                            r: "21.362"
                        }), (0, c.tZ)("circle", {
                            style: {
                                fill: "#F288E8"
                            },
                            cx: "228.751",
                            cy: "193.03",
                            r: "21.362"
                        }), (0, c.tZ)("circle", {
                            style: {
                                fill: "#F288E8"
                            },
                            cx: "344.261",
                            cy: "165.61",
                            r: "21.362"
                        }), (0, c.tZ)("circle", {
                            style: {
                                fill: "#F288E8"
                            },
                            cx: "440.041",
                            cy: "248.91",
                            r: "21.362"
                        }), (0, c.tZ)("circle", {
                            style: {
                                fill: "#F288E8"
                            },
                            cx: "365.621",
                            cy: "396.47",
                            r: "21.362"
                        }), (0, c.tZ)("circle", {
                            style: {
                                fill: "#F288E8"
                            },
                            cx: "228.751",
                            cy: "450.22",
                            r: "21.362"
                        }), (0, c.tZ)("circle", {
                            style: {
                                fill: "#F288E8"
                            },
                            cx: "52.67",
                            cy: "464.59",
                            r: "21.362"
                        })]
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#7A6CF7"
                        },
                        d: "M479.99,122.597h-15.883v174.524c0,93.631-75.903,169.535-169.535,169.535l0,0v25.804 c0,10.792,8.749,19.541,19.541,19.541H479.99c10.234,0,18.53-8.296,18.53-18.53V141.127 C498.519,130.893,490.223,122.597,479.99,122.597z"
                    }), (0, c.tZ)("rect", {
                        x: "119.151",
                        y: "122.6",
                        style: {
                            fill: "#FFC600"
                        },
                        width: "56.01",
                        height: "389.4"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#F4AF00"
                        },
                        d: "M119.155,442.556V512h56.006V404.1C158.504,419.432,139.662,432.421,119.155,442.556z"
                    }), (0, c.tZ)("rect", {
                        x: "375.941",
                        y: "122.6",
                        style: {
                            fill: "#FFE92F"
                        },
                        width: "56.01",
                        height: "389.4"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#FFD82F"
                        },
                        d: "M375.939,445.882V512h56.006V396.472C417.213,416.808,398.081,433.745,375.939,445.882z"
                    }), (0, c.tZ)("path", {
                        style: {
                            fill: "#E29100"
                        },
                        d: "M175.162,59.705v3.17c0,15.773,6.127,30.107,16.117,40.78c9.99-10.673,16.117-25.007,16.117-40.78 v-3.17c0-15.773-6.127-30.107-16.117-40.78C181.288,29.598,175.162,43.932,175.162,59.705z"
                    })]
                });
            var L = l(34080);
            const I = {
                    container: "UpgradeBanner_container__HIYP7",
                    buttonUpgradeText: "UpgradeBanner_buttonUpgradeText__x8Gc0",
                    text: "UpgradeBanner_text__r5jgH"
                },
                N = ({
                    onUpgrade: _
                }) => (0, c.BX)("div", {
                    className: I.container,
                    children: [(0, c.tZ)(m.Premium, {
                        size: 50
                    }), (0, c.BX)("div", {
                        className: I.text,
                        children: [(0, c.tZ)("h3", {
                            className: I.upgradeTitle,
                            children: (0, c.tZ)(r.FormattedMessage, {
                                id: "YYU0VW",
                                defaultMessage: "Upgrade to Premium"
                            })
                        }), (0, c.tZ)("p", {
                            className: I.upgradeSubtitle,
                            children: (0, c.tZ)(r.FormattedMessage, {
                                id: "Uxpyx9",
                                defaultMessage: "Free domain and many more."
                            })
                        })]
                    }), (0, c.tZ)(L.Z, {
                        onClick: _,
                        theme: "fullWhite",
                        height: "large",
                        children: (0, c.tZ)("span", {
                            className: I.buttonUpgradeText,
                            children: (0, c.tZ)(r.FormattedMessage, {
                                id: "oLrxqt",
                                defaultMessage: "UPGRADE TO PREMIUM"
                            })
                        })
                    })]
                }),
                j = (0, i.memo)(N);
            var T = l(63552);
            const D = (0, f.withState)("subDomain", "updateSubDomain", "")((0, f.withState)("domainType", "updateDomainType", "wuiltSubDomain")((({
                    siteId: _,
                    haveFreeDomain: e,
                    isPremium: l,
                    freeSubDomain: t,
                    domainType: d,
                    domain: o,
                    subDomain: n,
                    updateSubDomain: m,
                    updateDomainType: x,
                    handleRegisterSubDomain: a,
                    handlePublish: g
                }) => {
                    const G = (0, s.useNavigate)();
                    return (0, c.BX)("div", {
                        style: {
                            width: "100%"
                        },
                        children: [l && (0, c.BX)(i.Fragment, {
                            children: [(0, c.tZ)(M, {
                                id: "purchase",
                                onChange: () => x("purchase"),
                                isActive: "purchase" === d,
                                headerContent: (0, c.tZ)(Z, {
                                    children: (0, c.BX)(v, {
                                        children: [(0, c.BX)(k, {
                                            span: "12",
                                            lg: "8",
                                            children: [(0, c.tZ)(T.Heading, {
                                                variant: "h1",
                                                children: (0, c.tZ)(r.FormattedMessage, {
                                                    id: "eMco4Q",
                                                    defaultMessage: "Purchase a New Domain"
                                                })
                                            }), (0, c.tZ)("p", {
                                                children: (0, c.tZ)(r.FormattedMessage, {
                                                    id: "oC8axN",
                                                    defaultMessage: "Just pick a great name address for your next big thing"
                                                })
                                            })]
                                        }), (0, c.tZ)(k, {
                                            lg: "4",
                                            className: B,
                                            children: (0, c.tZ)(T.Button, {
                                                to: `/site/${_}/purchase-domain?source=publish`,
                                                children: (0, c.tZ)(r.FormattedMessage, {
                                                    id: "QXZw1G",
                                                    defaultMessage: "Purchase Domain"
                                                })
                                            })
                                        })]
                                    })
                                }),
                                expandAreaContent: e ? (0, c.tZ)(Z, {
                                    children: (0, c.BX)(v, {
                                        className: "PublishPopup_freeDomainRow__zmC4E",
                                        children: [(0, c.tZ)(k, {
                                            className: "PublishPopup_giftCol__vDpuG",
                                            span: "1",
                                            children: (0, c.tZ)(C, {})
                                        }), (0, c.tZ)(k, {})]
                                    })
                                }) : void 0
                            }), (0, c.tZ)(M, {
                                id: "outDomain",
                                onChange: () => x("outDomain"),
                                isActive: "outDomain" === d,
                                headerContent: (0, c.tZ)(Z, {
                                    children: (0, c.BX)(v, {
                                        children: [(0, c.BX)(k, {
                                            span: "12",
                                            lg: "8",
                                            children: [(0, c.tZ)(T.Heading, {
                                                variant: "h1",
                                                children: (0, c.tZ)(r.FormattedMessage, {
                                                    id: "Mo6WH7",
                                                    defaultMessage: "Use a Domain Your Already Own"
                                                })
                                            }), (0, c.tZ)("p", {
                                                children: (0, c.tZ)(r.FormattedMessage, {
                                                    id: "R62vZl",
                                                    defaultMessage: "Sync your domain to your website"
                                                })
                                            })]
                                        }), (0, c.tZ)(k, {
                                            lg: "4",
                                            className: B,
                                            children: (0, c.tZ)(T.Button, {
                                                outlined: !0,
                                                to: `/site/${_}/connect-domain?source=publish`,
                                                children: (0, c.tZ)(r.FormattedMessage, {
                                                    id: "r4j5so",
                                                    defaultMessage: "Connect Domain"
                                                })
                                            })
                                        })]
                                    })
                                })
                            })]
                        }), (0, c.tZ)(M, {
                            id: "wuiltSubDomain",
                            onChange: () => x("wuiltSubDomain"),
                            isActive: "wuiltSubDomain" === d,
                            headerContent: (0, c.BX)(Z, {
                                children: [(0, c.tZ)(v, {
                                    children: (0, c.BX)(k, {
                                        span: "12",
                                        children: [(0, c.tZ)(T.Heading, {
                                            variant: "h1",
                                            children: (0, c.tZ)(r.FormattedMessage, {
                                                id: "/Xsc9z",
                                                defaultMessage: "Use a Free Wuilt Subdomain"
                                            })
                                        }), (0, c.tZ)("p", {
                                            children: (0, c.tZ)(r.FormattedMessage, {
                                                id: "hkhKn7",
                                                defaultMessage: "For example, you can make it businessname.wuiltsite.com"
                                            })
                                        }), (0, c.tZ)("br", {})]
                                    })
                                }), (0, c.tZ)(v, {
                                    children: (0, c.tZ)(k, {
                                        span: "12",
                                        children: (0, c.tZ)("div", {
                                            className: "PublishPopup_domainWrapper__Cyl+P",
                                            children: (0, c.tZ)(S, {
                                                disabled: !!o,
                                                width: "100%",
                                                type: "text",
                                                onChange: ({
                                                    target: {
                                                        value: _
                                                    }
                                                }) => {
                                                    _.match(/^(www\.)?[A-Za-z1-9]*$/g) && m(_)
                                                },
                                                value: o || n,
                                                name: "sub-domain",
                                                postfix: (0, c.BX)("span", {
                                                    children: [".", t]
                                                })
                                            })
                                        })
                                    })
                                })]
                            }),
                            expandAreaContent: (0, c.tZ)("div", {
                                className: "PublishPopup_actions__5JimV",
                                children: (0, c.tZ)(T.Button, {
                                    onClick: () => {
                                        o ? g() : a(n)
                                    },
                                    children: (0, c.tZ)(r.FormattedMessage, {
                                        id: "syEQFE",
                                        defaultMessage: "Publish"
                                    })
                                })
                            })
                        }), !l && (0, c.tZ)(j, {
                            onUpgrade: () => G(`/upgrade/${_}`)
                        })]
                    })
                }))),
                A = _ => (0, c.tZ)(m.Icon, Object.assign({
                    viewBox: "0 0 30 30"
                }, _, {
                    children: (0, c.tZ)("g", {
                        id: "Publishing",
                        stroke: "none",
                        strokeWidth: "1",
                        fill: "none",
                        fillRule: "evenodd",
                        children: (0, c.tZ)("g", {
                            id: "Publishing---Trial-User---Publish-2nd-Time-Copy-7",
                            transform: "translate(-1059.000000, -462.000000)",
                            fill: "#0e9384",
                            children: (0, c.tZ)("g", {
                                id: "Group-6",
                                transform: "translate(316.000000, 352.000000)",
                                children: (0, c.tZ)("g", {
                                    id: "Group-7",
                                    children: (0, c.tZ)("g", {
                                        id: "Group-10",
                                        transform: "translate(0.000000, 75.000000)",
                                        children: (0, c.BX)("g", {
                                            id: "001-export",
                                            transform: "translate(743.000000, 35.000000)",
                                            children: [(0, c.tZ)("path", {
                                                d: "M20.7245397,2.13319994 L26.3545139,2.13319994 L13.4833352,15.0070304 C13.0665305,15.4237196 13.0665305,16.101617 13.4833352,16.5183062 C13.6886271,16.7235412 13.9623496,16.8292683 14.2360721,16.8292683 C14.5097946,16.8292683 14.7835171,16.7235412 14.988809,16.5183062 L27.8662087,3.64447569 L27.8662087,9.27288951 C27.8662087,9.86371748 28.345223,10.3425991 28.9299938,10.3425991 C29.5209856,10.3425991 30,9.86371748 30,9.27288951 L30,1.06970959 C30,0.478881618 29.5209856,0 28.9299938,0 L20.7245397,0 C20.1335479,0 19.6545335,0.478881618 19.6545335,1.06970959 C19.6607545,1.65431832 20.1335479,2.13319994 20.7245397,2.13319994 Z",
                                                id: "Path"
                                            }), (0, c.tZ)("path", {
                                                d: "M6.29871455,30 L23.7012855,30 C27.1781269,30 30,27.1725826 30,23.7025704 L30,15.5079559 C30,14.9265606 29.5286676,14.4614443 28.9471536,14.4614443 C28.3656397,14.4614443 27.9004285,14.9326805 27.9004285,15.5079559 L27.9004285,23.6964504 C27.9004285,26.0097919 26.015099,27.8947368 23.7012855,27.8947368 L6.29871455,27.8947368 C3.98490104,27.8947368 2.09957152,26.0097919 2.09957152,23.6964504 L2.09957152,9.87760098 L2.09957152,6.30354957 C2.09957152,3.99020808 3.98490104,2.10526316 6.29871455,2.10526316 L14.4215466,2.10526316 C15.0030606,2.10526316 15.4682718,1.63402693 15.4682718,1.05263158 C15.4682718,0.47123623 14.9969394,0 14.4215466,0 L6.29871455,0 C2.82187309,0 0,2.82741738 0,6.29742962 L0,9.87148103 L0,23.6964504 C0,27.1725826 2.82799429,30 6.29871455,30 Z",
                                                id: "Path"
                                            })]
                                        })
                                    })
                                })
                            })
                        })
                    })
                })),
                F = function({
                    siteId: _,
                    onPublish: e,
                    isPremium: l,
                    isSubDomain: t,
                    domainName: d
                }) {
                    const o = (0, s.useNavigate)();
                    return (0, c.BX)(i.Fragment, {
                        children: [(0, c.BX)("div", {
                            className: "RegisteredDomainPublish_container__ykbUC",
                            children: [(0, c.tZ)(T.Heading, {
                                variant: "h1",
                                className: "RegisteredDomainPublish_title__WHVgs",
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    id: "L/HSbn",
                                    defaultMessage: "Your website is going to be published on this domain"
                                })
                            }), (0, c.tZ)("p", {
                                className: "RegisteredDomainPublish_subtitle__3EhkS",
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    id: "nBmbWH",
                                    defaultMessage: "you can visit your website by clicking the link down below"
                                })
                            }), (0, c.BX)("a", {
                                className: "RegisteredDomainPublish_domainContainer__y3zWV",
                                href: `${t?"https":"http"}://${d.join("")}`,
                                rel: "noopener noreferrer",
                                target: "_blank",
                                children: [(0, c.tZ)("span", {
                                    className: "RegisteredDomainPublish_subDomain__1346l",
                                    children: d[0]
                                }), d[1], (0, c.tZ)(A, {
                                    className: "RegisteredDomainPublish_icon__T+QYQ",
                                    size: 25
                                })]
                            }), (0, c.tZ)(T.Button, {
                                onClick: e,
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    id: "LVKoRy",
                                    defaultMessage: "PUBLISH NOW"
                                })
                            }), (0, c.tZ)(s.Link, {
                                className: "RegisteredDomainPublish_domainSettings__RNSvL",
                                to: `/site/${_}/settings`,
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    id: "anENTF",
                                    defaultMessage: "Want to publish on another domain?"
                                })
                            })]
                        }), !l && (0, c.tZ)(j, {
                            onUpgrade: () => o(`/upgrade/${_}`)
                        })]
                    })
                };
            var O = l(72415),
                P = l(37044),
                R = l(19614),
                X = l(34418);
            const W = "wuiltweb.com",
                z = (0, d.connect)(((_, e) => {
                    var l, i;
                    const r = null == e ? void 0 : e.siteId,
                        t = (0, P.$8)(_),
                        d = null == _ ? void 0 : _.siteSettings;
                    return {
                        isOpen: !!r,
                        siteId: r,
                        isPremium: 1 !== ((null == t || null == (l = t.plan) ? void 0 : l.id) || (null == d ? void 0 : d.planId)),
                        siteDomainData: (null == t ? void 0 : t.domain_name) || (null == d || null == (i = d.domain) ? void 0 : i.data)
                    }
                }), {
                    publishSuccessed: (_, e, l) => ({
                        type: "publish/PUPLISH_SUCCESSED",
                        siteId: _,
                        name: e,
                        domain: l
                    }),
                    updateSiteDomain: O.gi
                })((function(_) {
                    const {
                        siteId: e,
                        isOpen: l,
                        siteDomainData: d,
                        isPremium: m
                    } = _, [x, a] = (0, i.useState)({
                        isPublishing: !1,
                        purchasedImages: !1,
                        isPublished: !1,
                        isError: !1
                    }), [g, G] = (0, i.useState)(!1), {
                        createSiteBackup: p,
                        manualBackupsList: f,
                        setDefaultBackupName: h,
                        setBackupName: y,
                        backupName: b
                    } = (0, R.Z)(e), {
                        isPublishing: w,
                        isPublished: Z,
                        isError: v
                    } = x, k = null == d ? void 0 : d.domain_name, B = d && "SUBDOMAIN" !== d.type ? k : `${k}.${W}`, M = (0, s.useNavigate)(), {
                        pushEvent: S
                    } = (0, o.useTracking)(), C = () => {
                        a((_ => Object.assign({}, _, {
                            isPublishing: !0
                        }))), L()
                    }, L = () => {
                        const {
                            siteId: e,
                            publishSuccessed: l
                        } = _;
                        a((_ => Object.assign({}, _, {
                                isPublishing: !0
                            }))),
                            function(_) {
                                return (0, t.a)(`${_}/publish-website/v2`, {}, "post")
                            }(e).then((_ => {
                                if (_.message.indexOf("published") < 0) throw new Error("Couldn't publish");
                                S("Site published", {
                                    siteId: e,
                                    domain: B
                                }), l(e), a((_ => Object.assign({}, _, {
                                    isPublished: !0,
                                    isPublishing: !1
                                })))
                            })).catch((() => {
                                a((_ => Object.assign({}, _, {
                                    isError: !0,
                                    isPublishing: !1
                                })))
                            }))
                    }, I = () => {
                        const {
                            purchasedImages: _
                        } = x;
                        _ || (M("../"), setTimeout((() => {
                            a((_ => Object.assign({}, _, {
                                isPublishing: !1,
                                haveShutterstockImages: !1,
                                purchasedImages: !1,
                                isPublished: !1,
                                isError: !1
                            })))
                        }), 500))
                    };
                    return w ? (0, c.BX)(T.Modal, {
                        show: l,
                        modalWidth: "medium",
                        onClose: I,
                        children: [(0, c.tZ)(T.Modal.Header, {
                            children: (0, c.tZ)(T.Heading, {
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    id: "PYkM0b",
                                    defaultMessage: "Publishing ..."
                                })
                            })
                        }), (0, c.tZ)(T.Modal.Body, {
                            children: (0, c.tZ)("div", {
                                style: {
                                    display: "flex",
                                    height: "100%",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    textAlign: "center"
                                },
                                children: (0, c.tZ)("div", {
                                    children: (0, c.tZ)(n.Z, {})
                                })
                            })
                        })]
                    }) : Z ? (0, c.BX)(T.Modal, {
                        modalWidth: "medium",
                        show: l,
                        onClose: I,
                        children: [(0, c.tZ)(T.Modal.Header, {
                            children: (0, c.tZ)(T.Heading, {
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    id: "D4cMWG",
                                    defaultMessage: "Publish Your Website"
                                })
                            })
                        }), (0, c.BX)(T.Modal.Body, {
                            p: "0",
                            children: [(0, c.tZ)(u, {
                                domain: B,
                                domainType: d.type
                            }), (0, c.tZ)(T.Divider, {
                                mt: "20px",
                                mb: "12px"
                            }), (0, c.tZ)(T.Stack, {
                                align: "center",
                                pb: "20px",
                                children: (0, c.tZ)(T.Button, {
                                    plain: !0,
                                    compact: !0,
                                    prefixIcon: (0, c.tZ)(T.FloppySaveIcon, {
                                        customColor: "#667085"
                                    }),
                                    onClick: () => {
                                        G(!0), h(), a((_ => Object.assign({}, _, {
                                            isPublished: !1
                                        })))
                                    },
                                    children: (0, c.tZ)(T.Text, {
                                        fontSize: "sm",
                                        fontWeight: "500",
                                        children: (0, c.tZ)(r.FormattedMessage, {
                                            defaultMessage: "Save current site version",
                                            id: "ixos9Y"
                                        })
                                    })
                                })
                            })]
                        })]
                    }) : g ? (0, c.tZ)(X.Z, {
                        show: g,
                        onClose: () => {
                            G(!1), I()
                        },
                        createSiteBackup: p,
                        manualBackupsList: f,
                        backupName: b,
                        setBackupName: y
                    }) : v ? (0, c.BX)(T.Modal, {
                        show: l,
                        modalWidth: "medium",
                        onClose: I,
                        children: [(0, c.tZ)(T.Modal.Header, {
                            children: (0, c.tZ)(T.Heading, {
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    defaultMessage: "Error while publishing your website",
                                    id: "bC3PUy"
                                })
                            })
                        }), (0, c.tZ)(T.Modal.Body, {
                            children: (0, c.tZ)(T.Heading, {
                                variant: "h4",
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    defaultMessage: "Error",
                                    id: "KN7zKn"
                                })
                            })
                        })]
                    }) : d ? (0, c.BX)(T.Modal, {
                        show: l,
                        modalWidth: "medium",
                        onClose: I,
                        children: [(0, c.tZ)(T.Modal.Header, {
                            children: (0, c.tZ)(T.Heading, {
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    id: "D4cMWG",
                                    defaultMessage: "Publish Your Website"
                                })
                            })
                        }), (0, c.tZ)(T.Modal.Body, {
                            children: (0, c.tZ)(F, {
                                siteId: e,
                                onPublish: C,
                                isPremium: m,
                                isSubDomain: "SUBDOMAIN" === d.type,
                                domainName: (_ => {
                                    const e = _.indexOf(".");
                                    return -1 === e ? [_, `.${W}`] : [_.slice(0, e), _.slice(e, _.length)]
                                })(d.domain_name)
                            })
                        })]
                    }) : (0, c.BX)(T.Modal, {
                        modalWidth: "medium",
                        show: l,
                        onClose: I,
                        children: [(0, c.tZ)(T.Modal.Header, {
                            children: (0, c.tZ)(T.Heading, {
                                children: (0, c.tZ)(r.FormattedMessage, {
                                    id: "D4cMWG",
                                    defaultMessage: "Publish Your Website"
                                })
                            })
                        }), (0, c.tZ)(T.Modal.Body, {
                            children: (0, c.tZ)(D, {
                                isPremium: m,
                                siteId: e,
                                freeSubDomain: W,
                                handlePublish: C,
                                handleRegisterSubDomain: async e => {
                                    const {
                                        updateSiteDomain: l,
                                        siteId: i
                                    } = _;
                                    try {
                                        await (r = e, (0, t.a)("subdomain/availability", {
                                            subdomain_name: r
                                        }, "post").then((_ => _.available))) ? (await
                                            function(_, e) {
                                                let l = e.toLowerCase();
                                                return l = l.startsWith("www.") ? l.replace("www.", "") : l, (0, t.a)("subdomain/availability", {
                                                    subdomain_name: l
                                                }, "post").then((_ => {
                                                    if (!1 === _.available) throw alert("not avaliable"), new Error("not available");
                                                    return _.subdomain
                                                })).then((() => (0, t.a)(`subdomain/${_}/register`, {
                                                    subdomain_name: l
                                                }, "post")))
                                            }(i, e), l(i, {
                                                domain_name: e,
                                                type: "SUBDOMAIN"
                                            }), C()) : alert("not available")
                                    } catch (_) {}
                                    var r
                                },
                                domainData: d,
                                domain: d && "SUBDOMAIN" === d.type ? d.domain_name : ""
                            })
                        })]
                    })
                }))
        },
        34080: (_, e, l) => {
            l.d(e, {
                Z: () => i.Z
            });
            var i = l(8352)
        },
        94557: (_, e, l) => {
            l.d(e, {
                Z: () => r
            }), l(37900);
            var i = l(74499);
            const r = ({
                size: _,
                color: e
            }) => (0, i.tZ)("div", {
                style: {
                    width: _ || "20px",
                    height: _ || "20px",
                    borderBottom: e ? `3.2px solid ${e}` : "3.2px solid #0e9384",
                    borderRight: e ? `3.2px solid ${e}` : "3.2px solid #0e9384"
                },
                className: "Spinner_circle__N3Z3x"
            })
        }
    }
]);
//# sourceMappingURL=3084.df64563dbbb4c588.js.map