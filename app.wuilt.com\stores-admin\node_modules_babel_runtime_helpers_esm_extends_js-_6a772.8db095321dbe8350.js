(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["node_modules_babel_runtime_helpers_esm_extends_js-_6a772"], {

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/extends.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _extends)
                    /* harmony export */
                });

                function _extends() {
                    _extends = Object.assign ? Object.assign.bind() : function(target) {
                        for (var i = 1; i < arguments.length; i++) {
                            var source = arguments[i];
                            for (var key in source) {
                                if (Object.prototype.hasOwnProperty.call(source, key)) {
                                    target[key] = source[key];
                                }
                            }
                        }
                        return target;
                    };
                    return _extends.apply(this, arguments);
                }

                /***/
            })

    }
])
//# sourceMappingURL=node_modules_babel_runtime_helpers_esm_extends_js-_6a772.8db095321dbe8350.js.map