.Selectable_container__aI7Ln {
    position: relative
}

.Selectable_active__i2rx6 {
    outline: 1px solid #0e9384
}

.Slider_bar__1tyhq {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 6px;
    border-radius: 10%;
    margin-right: auto;
    background-color: #d8d8d8;
    position: relative;
    border-radius: 10px
}

.Slider_bar__1tyhq .Slider_handle__TQ3dD {
    position: absolute;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #f5f5f5;
    transform: translateX(-50%)
}

.uiRtl .Slider_bar__1tyhq .Slider_handle__TQ3dD {
    transform: translateX(50%)
}

.Slider_bar__1tyhq.Slider_dark__UoLjY {
    background-color: rgba(31, 43, 57, .4)
}

.Slider_bar__1tyhq.Slider_dark__UoLjY .Slider_handle__TQ3dD {
    background-color: #1f2b39
}

.PopupCropper_btn__3OMU1 {
    font-weight: 800;
    border-radius: 9999px;
    text-transform: uppercase;
    color: #fff;
    padding: 4px 10px;
    font-size: 11px;
    margin: 0 2px;
    outline: none;
    cursor: pointer
}

.PopupCropper_btn__3OMU1:focus,
.PopupCropper_btn__3OMU1:active {
    outline: none
}

.PopupCropper_cropSettings__MshWc {
    left: 50%;
    position: absolute;
    z-index: 1000;
    display: flex;
    user-select: none;
    justify-content: space-between;
    align-items: center;
    background-color: #353f4b;
    width: 330px;
    height: 45px;
    bottom: 0;
    transform: translate(-50%, 50%);
    box-shadow: 0 2px 3px 1px rgba(2, 18, 43, .35);
    border-right: .6px solid rgba(179, 190, 202, .6);
    border-radius: 5px
}

.PopupCropper_cropSettings__MshWc .PopupCropper_cropIcon__0SCAN {
    display: flex;
    padding: 0 10px;
    height: 100%;
    justify-content: center;
    align-items: center
}

.PopupCropper_cropSettings__MshWc .PopupCropper_cropIcon__0SCAN img {
    width: 22px;
    height: 22px
}

.uiRtl .PopupCropper_cropSettings__MshWc .PopupCropper_cropIcon__0SCAN {
    border-left: .6px solid rgba(179, 190, 202, .6);
    border-right: none
}

.PopupCropper_cropSettings__MshWc .PopupCropper_cropBtns__sqUCQ {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px
}

.PopupCropper_cropSettings__MshWc .PopupCropper_cancelBtn__ZiVj\+ {
    box-shadow: 0 2px 3px 1px rgba(2, 18, 43, .11);
    background-color: #353f4b;
    border: 1px solid #fff
}

.PopupCropper_cropSettings__MshWc .PopupCropper_cropBtn__0S2Pc {
    box-shadow: 0 2px 3px 1px rgba(2, 18, 43, .11);
    background-color: #0e9384;
    border: 1px solid #0e9384
}

.PopupCropper_smallCropper__SehuN {
    margin-top: 10x;
    width: 208px;
    box-shadow: 0 3px 4px 0 rgba(0, 0, 0, .2);
    background-color: #fff;
    border-radius: 7px;
    position: absolute;
    z-index: 999999999
}

.PopupCropper_smallCropper__SehuN .PopupCropper_smallCropperHeader__O80m7 {
    display: flex;
    background-color: #1f2b39;
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
    padding: 10px 6px
}

.PopupCropper_smallCropper__SehuN .PopupCropper_smallCropperHeader__O80m7 img {
    margin-right: 5px;
    margin-left: 12px
}

.PopupCropper_smallCropper__SehuN .PopupCropper_smallCropperHeader__O80m7 span {
    width: 100%;
    text-transform: uppercase;
    text-align: left;
    font-size: 13px;
    font-weight: 600;
    color: #fff
}

.PopupCropper_smallCropper__SehuN .PopupCropper_smallCropperSettings__4\+YFG {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 30px
}

.PopupCropper_smallCropper__SehuN .PopupCropper_smallCropperSettings__4\+YFG .PopupCropper_smallCropperBtns__kuL0\+ {
    display: flex;
    justify-content: center
}

.PopupCropper_smallCropper__SehuN .PopupCropper_smallCropperSettings__4\+YFG .PopupCropper_smallCropperBtns__kuL0\+.PopupCropper_cancelBtn__ZiVj\+ {
    color: #000;
    background-color: #fff;
    border: 1px solid #000;
    padding: 5px 13px;
    margin: 0 5px
}

.PopupCropper_smallCropper__SehuN .PopupCropper_smallCropperSettings__4\+YFG .PopupCropper_smallCropperBtns__kuL0\+.PopupCropper_cropBtn__0S2Pc {
    color: #fff;
    background-color: #0e9384;
    border: 1px solid #0e9384;
    padding: 5px 13px;
    margin: 0 5px
}

.CropToolbar_btn__7hdcZ {
    font-weight: 800;
    border-radius: 9999px;
    text-transform: uppercase;
    color: #fff;
    padding: 4px 10px;
    font-size: 11px;
    margin: 0 2px;
    outline: none;
    cursor: pointer
}

.CropToolbar_btn__7hdcZ:focus,
.CropToolbar_btn__7hdcZ:active {
    outline: none
}

.CropToolbar_cropSettings__7iyWn {
    left: 50%;
    position: absolute;
    z-index: 1000;
    display: flex;
    user-select: none;
    justify-content: space-between;
    align-items: center;
    background-color: #353f4b;
    width: 100%;
    height: 45px;
    bottom: 20px;
    transform: translate(-50%, 50%);
    box-shadow: 0 2px 3px 1px rgba(2, 18, 43, .35);
    border-right: .6px solid rgba(179, 190, 202, .6);
    border-radius: 5px
}

.CropToolbar_cropIcon__lKtjI {
    display: flex;
    padding: 0 10px;
    height: 100%;
    justify-content: center;
    align-items: center
}

.CropToolbar_cropIcon__lKtjI img {
    width: 22px;
    height: 22px
}

.uiRtl .CropToolbar_cropIcon__lKtjI {
    border-left: .6px solid rgba(179, 190, 202, .6);
    border-right: none
}

.CropToolbar_cropBtns__SjuuF {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px
}

.CropToolbar_cancelBtn__pcAyi {
    box-shadow: 0 2px 3px 1px rgba(2, 18, 43, .11);
    background-color: #353f4b;
    border: 1px solid #fff
}

.CropToolbar_cropBtn__SMBBL {
    box-shadow: 0 2px 3px 1px rgba(2, 18, 43, .11);
    background-color: #0e9384;
    border: 1px solid #0e9384
}

.EditableItemBar_container__z9a63 {
    white-space: nowrap;
    direction: ltr;
    z-index: 150;
    top: 0;
    left: -1px;
    position: absolute;
    transform: translate(0, -100%);
    font-size: 16px;
    line-height: 25px;
    visibility: hidden
}

.EditableItemBar_container__z9a63.EditableItemBar_--visible__lSr3x {
    visibility: visible
}

.EditableItemBar_container__z9a63.EditableItemBar_--inner__Y6TjX {
    transform: translate(0, -1px)
}

.uiRtl .EditableItemBar_container__z9a63 {
    direction: rtl;
    top: 0;
    right: 0px;
    left: unset
}

.EditableItemBar_controlBar__fsn2T {
    position: relative;
    opacity: 0
}

.EditableItemBar_controlBar__fsn2T.EditableItemBar_--visible__lSr3x {
    opacity: 1
}

.EditableItemBar_innerStyle__iqMMV {
    display: flex;
    padding: 0;
    margin: 0px;
    border: 1px solid #009882;
    background-color: #fff;
    color: #000;
    position: relative;
    border-radius: 0px 0px 4px;
    align-content: center
}

.EditableItemBar_innerStyle__iqMMV .EditableItemBar_label__\+JcL3 {
    cursor: default;
    display: flex;
    align-content: center;
    flex-wrap: wrap;
    background-color: #009882;
    color: #fff;
    font-size: 10px;
    line-height: 10px;
    font-weight: 900;
    padding: 3px 4px
}

.EditableItemBar_innerStyle__iqMMV .EditableItemBar_label__\+JcL3 .EditableItemBar_svg__WLhgW {
    display: block
}

.EditableItemBar_innerStyle__iqMMV .EditableItemBar_label__\+JcL3 span {
    display: block
}

.EditableItemBar_innerStyle__iqMMV.EditableItemBar_--collection__tBXFJ .EditableItemBar_label__\+JcL3 {
    background-color: #ee46bc
}

.EditableItemBar_innerStyle__iqMMV .EditableItemBar_item__p-jSQ {
    padding: 3px 4px;
    cursor: pointer;
    border-right: 1px solid #009882;
    color: #000 !important;
    font-size: 10px;
    font-weight: 900;
    word-spacing: normal;
    letter-spacing: normal;
    line-height: 10px
}

.EditableItemBar_innerStyle__iqMMV .EditableItemBar_item__p-jSQ svg {
    display: block;
    min-width: 16px;
    min-height: 16px;
    max-width: 16px;
    max-height: 16px
}

.uiRtl .EditableItemBar_innerStyle__iqMMV .EditableItemBar_item__p-jSQ {
    border-right: none;
    border-left: 1px solid #009882
}

.EditableItemBar_innerStyle__iqMMV .EditableItemBar_item__p-jSQ.EditableItemBar_--last__WrfNc {
    border-right: none
}

.uiRtl .EditableItemBar_innerStyle__iqMMV .EditableItemBar_item__p-jSQ.EditableItemBar_--last__WrfNc {
    border-left: none
}

.EditableItemBar_innerStyle__iqMMV.EditableItemBar_--collection__tBXFJ {
    border: 1px solid #ee46bc
}

.EditableItemBar_innerStyle__iqMMV.EditableItemBar_--collection__tBXFJ .EditableItemBar_item__p-jSQ {
    border-right: 1px solid #ee46bc
}

.uiRtl .EditableItemBar_innerStyle__iqMMV.EditableItemBar_--collection__tBXFJ .EditableItemBar_item__p-jSQ {
    border-right: none;
    border-left: 1px solid #ee46bc
}

.EditableItemBar_innerStyle__iqMMV.EditableItemBar_--collection__tBXFJ .EditableItemBar_item__p-jSQ.EditableItemBar_--last__WrfNc {
    border-right: none
}

.uiRtl .EditableItemBar_innerStyle__iqMMV.EditableItemBar_--collection__tBXFJ .EditableItemBar_item__p-jSQ.EditableItemBar_--last__WrfNc {
    border-left: none
}

.SelectableWithControls_container__YKsnm {
    position: relative;
    cursor: pointer
}

.SelectableWithControls_active__2yj4o {
    outline: 1px solid #0e9384;
    box-shadow: 0px 0px 0px 1px #0e9384
}

.ExpandButton_sectionControlButtonContainer__1VXrK {
    position: relative;
    width: 40px;
    height: 40px;
    overflow: visible;
    font-size: 12px;
    pointer-events: all
}

.ExpandButton_controlButton__KQmjC {
    pointer-events: all;
    position: relative;
    transition: all .9s;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    width: 50px;
    line-height: 50px;
    height: 50px;
    border: 0 solid;
    background: #fff;
    box-shadow: 1px 1px 15px rgba(0, 0, 0, .3)
}

.ExpandButton_controlButton__KQmjC span {
    opacity: 0;
    display: none
}

@media screen and (min-width: 600px) {
    .ExpandButton_controlButton__KQmjC:hover {
        width: auto
    }
    .ExpandButton_controlButton__KQmjC:hover span {
        text-align: right;
        opacity: 1;
        display: block;
        padding: 0 10px
    }
    .ExpandButton_controlButton__KQmjC:hover i {
        padding: 0 5px
    }
}

.ExpandButton_cRtl__B87oe .ExpandButton_sectionControlButton__udrek.ExpandButton_--fromCenter__u8pmm {
    transform: translateX(-50%)
}

.ExpandButton_controlButton__KQmjC {
    position: relative;
    transition: all .9s;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    width: 50px;
    line-height: 50px;
    height: 50px;
    border: 0 solid;
    background: #fff;
    box-shadow: 1px 1px 15px rgba(0, 0, 0, .3)
}

.ExpandButton_sectionControlButton__udrek.ExpandButton_--fromCenter__u8pmm {
    left: 50%;
    transform: translateX(-50%)
}

.ExpandButton_controlButton__KQmjC span {
    opacity: 0;
    display: none
}

@media screen and (min-width: 600px) {
    .ExpandButton_controlButton__KQmjC:hover {
        width: auto
    }
}

.ExpandButton_controlButton__KQmjC:hover span {
    text-align: right;
    opacity: 1;
    display: block;
    padding: 0 10px
}

.ExpandButton_controlButton__KQmjC:hover i {
    padding: 0 5px
}

.ExpandButton_sectionControlButton__udrek span {
    text-align: right;
    width: 0;
    padding: 0 10px;
    height: 100%;
    display: none;
    overflow: hidden;
    transition: width .4s
}

.ExpandButton_cRtl__B87oe .ExpandButton_sectionControlButton__udrek span {
    text-align: left
}

.ExpandButton_sectionControlButton__udrek:hover span,
.ExpandButton_sectionControlButton__udrek:focus span {
    display: block;
    width: 250px
}

.ExpandButton_controlButton__icon__V\+EZw {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    width: 40px;
    line-height: 40px;
    height: 40px
}

.ExpandButton_sectionControlButton__udrek {
    overflow: hidden;
    position: absolute;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    width: 40px;
    line-height: 40px;
    height: 40px;
    border: 0 solid;
    background: #fff;
    box-shadow: 0 1px 6px 1px rgba(2, 18, 43, .25);
    transition: all .4s;
    flex-wrap: wrap
}

.ExpandButton_sectionControlButton__udrek:hover,
.ExpandButton_sectionControlButton__udrek:focus {
    outline: none
}

@media screen and (min-width: 600px) {
    .ExpandButton_sectionControlButton__udrek.ExpandButton_--expanded__8rlDw,
    .ExpandButton_sectionControlButton__udrek:hover,
    .ExpandButton_sectionControlButton__udrek:focus {
        width: 215px
    }
    .ExpandButton_sectionControlButton__udrek.ExpandButton_--expanded__8rlDw span,
    .ExpandButton_sectionControlButton__udrek:hover span,
    .ExpandButton_sectionControlButton__udrek:focus span {
        display: block;
        width: 250px
    }
}

.ExpandButton_sectionControlButton__udrek.ExpandButton_--expandToRight__VVjq- span {
    text-align: left
}

.uiRtl .ExpandButton_sectionControlButton__udrek.ExpandButton_--expandToRight__VVjq- span {
    text-align: right
}

.ExpandButton_sectionControlButton__udrek.ExpandButton_--iconToLeft__XR608.ExpandButton_--expandToLeft__-ADhT span {
    text-align: right
}

.uiRtl .ExpandButton_sectionControlButton__udrek.ExpandButton_--iconToLeft__XR608.ExpandButton_--expandToLeft__-ADhT span {
    text-align: left
}

.ExpandButton_sectionControlButton__udrek.ExpandButton_--expandToLeft__-ADhT.ExpandButton_--iconToRight__re2BC span {
    text-align: left
}

.uiRtl .ExpandButton_sectionControlButton__udrek.ExpandButton_--expandToLeft__-ADhT.ExpandButton_--iconToRight__re2BC span {
    text-align: right
}

.ExpandButton_sectionControlButton__udrek.ExpandButton_--fromCenter__u8pmm span {
    text-align: center !important
}

.Text_text__x2w7g strong,
.Text_text__x2w7g strong * {
    font-weight: bold !important
}

.Text_text__x2w7g em,
.Text_text__x2w7g em * {
    font-style: italic !important
}

.Text_text__x2w7g s,
.Text_text__x2w7g s * {
    text-decoration: line-through !important
}

.Text_text__x2w7g s span[style*="text-decoration: underline;"] {
    text-decoration: underline !important
}

.Text_text__x2w7g s,
.Text_text__x2w7g em,
.Text_text__x2w7g strong,
.Text_text__x2w7g span:not([style^=color]):not([style*=" color"]),
.Text_text__x2w7g span[style^=color] *:not([style^=color]):not([style*=" color"]),
.Text_text__x2w7g span[style*=" color"] *:not([style^=color]):not([style*=" color"]) {
    color: inherit !important
}

.Text_text__x2w7g s,
.Text_text__x2w7g em,
.Text_text__x2w7g strong,
.Text_text__x2w7g span:not([style*=font-size]),
.Text_text__x2w7g span[style*=font-size] *:not([style*=font-size]) {
    font-size: inherit !important
}

.EditorButton_hidden__OnP0f {
    opacity: .1 !important
}

.SlideShow_slider__5sFqU {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0
}

.SlideShow_slide__9s0qM {
    transition: all .3s;
    opacity: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden
}

.SlideShow_slide__9s0qM img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center
}

.SlideShow_slideActive__RHtuW {
    opacity: 1
}

.SlideShow_overlay__\+pUwv {
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.SlideShow_bulletsContainer__jdSuY {
    display: flex;
    z-index: 3;
    position: absolute;
    justify-content: center;
    align-items: center;
    z-index: 13;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%)
}

@media(max-width: 786px) {
    .SlideShow_bulletsContainer__jdSuY {
        display: none
    }
}

.SlideShow_bulletItem__fvVed {
    cursor: pointer;
    outline: 0 !important;
    margin: 3px;
    background: #fff;
    opacity: .5;
    border: 0px solid;
    border-radius: 99999px;
    padding: 0;
    width: 30px;
    height: 5px;
    display: inline-block
}

.SlideShow_bulletItemActive__CjUT9 {
    opacity: 1
}

.SlideShow_nav__8F9IO,
.SlideShow_navBack__XMkK4,
.SlideShow_navNext__yizsI {
    z-index: 3;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: #000;
    position: absolute;
    color: #fff;
    border-radius: 50%;
    bottom: 50%;
    transform: translateY(-50%);
    cursor: pointer
}

.SlideShow_navNext__yizsI {
    right: 40px
}

.SlideShow_navNext__yizsI:before {
    content: ">"
}

.SlideShow_navBack__XMkK4 {
    left: 40px
}

.SlideShow_navBack__XMkK4:before {
    content: "<"
}

.SlideShow_slideEditor__49F4q {
    position: absolute;
    bottom: 0px;
    left: 5px;
    z-index: 100
}

.uiRtl .SlideShow_slideEditor__49F4q {
    left: unset;
    right: 5px
}

.nav-link.NavMenu_menuItem__\+fQYu,
.NavMenu_menuItem__\+fQYu {
    display: flex
}

.rtl .NavMenu_menu__F9Eig .dropdown-menu {
    text-align: right
}

.NavMenu_levelIndent__gc85F {
    margin-inline-start: 10px;
    display: block
}

.NavMenu_pageName__TXQ2z {
    flex: 1 1 auto
}

.NavMenu_dropArrowContainer__LbEuA {
    padding: 0 10px
}

.NavMenu_dropArrow__ZZuBE {
    transition: transform ease-in-out .3s
}

@media screen and (min-width: 600px) {
    .dropdown-menu .NavMenu_dropArrow__ZZuBE {
        transform: rotate(-90deg)
    }
    .rtl .dropdown-menu .NavMenu_dropArrow__ZZuBE {
        transform: rotate(90deg)
    }
}

.NavMenu_dropArrow__ZZuBE.NavMenu_--active__cqjp4 {
    transform: rotate(180deg)
}

.NavMenu_editContainer__Y10OW {
    position: relative;
    width: 100%
}

.NavMenu_editContainer__Y10OW:hover>.NavMenu_menuContainer__O\+-Sx {
    outline: 1px solid #0e9384
}

.NavMenu_settingsContainer__6uRTD {
    position: relative
}

.EditorSocialLink_hidden__JtUOR {
    opacity: .1 !important
}

.DropDown_wrapper__1pWbj {
    display: flex;
    flex-direction: column
}

.DropDown_contentContainer__Rhq4V {
    opacity: 0;
    transition: opacity .2s ease;
    visibility: hidden;
    min-width: 100%;
    max-width: calc(100% + 34px);
    z-index: 6;
    position: absolute;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background: rgba(0, 0, 0, 0)
}

.DropDown_contentContainer__Rhq4V.DropDown_up__seQDl {
    padding-bottom: 10px;
    bottom: 0
}

.DropDown_contentContainer__Rhq4V.DropDown_down__7jPMq {
    padding-top: 10px;
    top: 0
}

.DropDown_contentContainer__Rhq4V.DropDown_shown__jZgSv {
    visibility: visible;
    opacity: 1
}

.DropDown_innerContentContainer__lt-Lr {
    padding: 0;
    background: #fff;
    border-radius: 4px;
    display: block;
    overflow: auto;
    border: 1px solid #e2e8f0
}

.DropDown_option__yXwTu {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #000;
    line-height: 2.5;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 5px 15px 5px 7px;
    border-bottom: 1px solid #e2e8f0
}

.rtl .DropDown_option__yXwTu {
    padding: 5px 7px 5px 15px
}

.DropDown_optionSelectedIndicator__yFWC1 {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    background: #0f0f0f;
    color: #fff;
    margin: 0px 8px 0px 0px
}

.rtl .DropDown_optionSelectedIndicator__yFWC1 {
    margin: 0px 0px 0px 8px
}

.DropDown_optionNotSelectedIndicator__xaYEG {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    width: 19px;
    height: 19px;
    color: #fff;
    border: 2px solid #94a3b8;
    margin: 0px 8px 0px 0px
}

.rtl .DropDown_optionNotSelectedIndicator__xaYEG {
    margin: 0px 0px 0px 8px
}

.LanguageSwitcher_styledFlex__cpTCY {
    display: flex;
    align-items: center
}

@media(max-width: 992px) {
    .LanguageSwitcher_styledFlex__cpTCY {
        width: 100%
    }
}

.LanguageSwitcher_wrapper__-0RGB {
    position: relative;
    margin: 0;
    justify-content: center;
    height: fit-content
}

@media(max-width: 992px) {
    .LanguageSwitcher_wrapper__-0RGB {
        width: 100%
    }
}

.LanguageSwitcher_languageName__gVinx,
.LanguageSwitcher_languageLink__83c9j {
    font-size: 16px
}

.LanguageSwitcher_languageName__gVinx {
    color: #667085
}

.rtl .LanguageSwitcher_languageName__gVinx {
    margin-left: 15px
}

.LanguageSwitcher_languageLink__83c9j {
    color: #667085
}

.LanguageSwitcher_divider__OG-t7 {
    width: 1px;
    background: #eaecf0;
    height: 18px;
    margin: 0px 10px;
    display: inline-block
}

@media(max-width: 992px) {
    .LanguageSwitcher_divider__OG-t7 {
        display: none
    }
}

.LanguageSwitcher_labelWrapper__0wvNI {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #000;
    padding: 10px 14px;
    height: 38px;
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer
}

.rtl .LanguageSwitcher_labelWrapper__0wvNI {
    padding-inline: 8px
}

@media(max-width: 992px) {
    .LanguageSwitcher_labelWrapper__0wvNI {
        width: 100%
    }
}

.LanguageSwitcher_dropArrow__ZeOXK {
    margin: 2px 0px 0px 10px
}

.LanguageSwitcher_dropArrow__ZeOXK.LanguageSwitcher_dropDirectionUp__PKI5Z {
    transform: rotate(180deg)
}

.rtl .LanguageSwitcher_dropArrow__ZeOXK {
    margin: 5px 15px 0px 0px
}

.LanguageSwitcher_languagesIcon__0yzxU {
    color: #94a3b8;
    margin: 3px 5px 0px 0px
}

.rtl .LanguageSwitcher_languagesIcon__0yzxU {
    margin: 5px 0px 0px 5px
}

.LanguageSwitcher_dropDown__D1jCF {
    display: block;
    position: absolute;
    bottom: 0;
    left: 0px;
    z-index: 999999;
    width: 120px
}

@media(max-width: 992px) {
    .LanguageSwitcher_dropDown__D1jCF {
        width: 100%
    }
}

.rtl .LanguageSwitcher_dropDown__D1jCF {
    right: 0px
}

.LanguageSwitcher_dropDown__D1jCF.LanguageSwitcher_dropDirectionUp__PKI5Z {
    top: 0;
    bottom: auto
}

.MobileMenu_menuContainer__5GaYs {
    color: #fff;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 999000;
    left: 0;
    bottom: 0;
    transition: transform ease-in-out .3s, opacity ease-in-out .1s;
    transform: translate(-100%, 0);
    opacity: 0
}

.rtl .MobileMenu_menuContainer__5GaYs {
    transform: translate(100%, 0)
}

.MobileMenu_menuContainer__5GaYs.MobileMenu_--open__ryGt8 {
    opacity: 1;
    transform: translate(0, 0)
}

.MobileMenu_defaultMenuStyle__rcWrw {
    background: #fff
}

.MobileMenu_brandBar__vLF8r {
    flex: 0 0 auto;
    min-height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 13px 30px;
    position: relative
}

.MobileMenu_brandBar__vLF8r::before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: .12;
    background-color: #fff
}

.MobileMenu_menuLinksContainer__Omzd- {
    flex: 1 1 auto;
    overflow: auto;
    min-height: 0
}

.MobileMenu_menuLinksContainer__Omzd-::-webkit-scrollbar {
    width: 6px;
    border-radius: 9px
}

.MobileMenu_menuLinksContainer__Omzd-::-webkit-scrollbar-thumb {
    background: rgba(127, 134, 142, .4666666667);
    border-radius: 9px
}

.MobileMenu_navMenu__vbk2c {
    list-style: none;
    padding: 0
}

.MobileMenu_navItem__Qr7lT {
    opacity: 1
}

.MobileMenu_navItem__Qr7lT .dropdown-menu {
    padding: 0;
    background: rgba(0, 0, 0, 0);
    border: none;
    position: initial;
    float: none
}

.MobileMenu_navItem__Qr7lT:hover,
.MobileMenu_navItemActive__gqH5H {
    opacity: 1
}

.MobileMenu_navItem__Qr7lT:hover>.MobileMenu_navLink__ZW1Gk,
.MobileMenu_navItemActive__gqH5H>.MobileMenu_navLink__ZW1Gk {
    opacity: 1
}

.MobileMenu_navLink__ZW1Gk {
    padding: 20px 16px 20px 16px;
    box-shadow: inset 0 -1px 0 0 #eaecf0;
    font-size: 16px;
    color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.MobileMenu_navLink__ZW1Gk:hover {
    text-decoration: none;
    color: #000
}

.MobileMenu_fixedBottom__JqOC1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-top: 1px solid #eaecf0;
    flex: 0 0 auto;
    color: rgba(0, 0, 0, .5)
}

.MobileMenu_logo__pHixz {
    max-height: 40px;
    width: auto
}

.MobileMenu_burger__RwNwG {
    cursor: pointer;
    padding: 8px
}

.rtl .MobileMenu_burger__RwNwG {
    transform: scaleX(-1)
}

.MobileMenu_closeButton__Q-zQ4 {
    cursor: pointer;
    padding: 8px;
    position: absolute;
    color: #000;
    top: 10px;
    right: 10px;
    z-index: 10
}

.rtl .MobileMenu_closeButton__Q-zQ4 {
    right: unset;
    left: 10px
}

.MobileMenu_languageSwitcher__uTmWy {
    border: .5px solid #d0d5dd;
    width: 130px;
    justify-content: space-between;
    background: rgba(0, 0, 0, 0);
    color: rgba(41, 41, 41, .5)
}

@media(max-width: 992px) {
    .MobileMenu_languageSwitcher__uTmWy {
        width: 100%
    }
}

.MobileMenu_linksWrapper__Ua1Wm {
    padding: 25px;
    box-shadow: 0px -2px 12px 0px rgba(16, 24, 40, .08)
}

.MobileMenu_styledFlex__heHiT {
    display: flex;
    margin: 10px 0px
}

.MobileMenu_icon__LfmSU {
    color: #000 !important;
    padding-top: 2px
}

.MobileMenu_linkButton__lFhup {
    color: #000 !important;
    padding: 0px 3px !important;
    min-height: fit-content !important
}

.MobileMenu_styledButton__igzSh {
    width: 100%;
    margin: 10px 0px
}

.MobileMenu_styledButton__igzSh button {
    width: 100% !important
}

.MobileMenu_styledButton__igzSh a {
    width: 100% !important;
    display: flex;
    justify-content: center
}

.DesktopMenu_navMenu__17SDB {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex
}

.DesktopMenu_navItem__I7bFc {
    opacity: 1;
    padding: 10px 0
}

.DesktopMenu_navItem__I7bFc .dropdown-menu {
    border-radius: 4px;
    position: absolute;
    width: 300px;
    margin: 0;
    box-shadow: 0 0 3px 0 rgba(0, 0, 0, .23);
    padding: 10px 0 10px 20px;
    border: none;
    background: #fff
}

.rtl .DesktopMenu_navItem__I7bFc .dropdown-menu {
    padding: 10px 20px 10px 0;
    left: auto;
    right: 0
}

.DesktopMenu_navItem__I7bFc .dropdown-menu:before {
    content: "";
    position: absolute;
    top: -6px;
    left: 0;
    z-index: 1;
    width: 12px;
    height: 12px;
    background: #fff;
    box-shadow: -1px -1px 1px rgba(0, 0, 0, .1);
    transform: translateX(18px) rotate(45deg)
}

.rtl .DesktopMenu_navItem__I7bFc .dropdown-menu:before {
    right: 0;
    transform: translateX(-18px) rotate(45deg)
}

.DesktopMenu_navItem__I7bFc .dropdown-menu:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 0;
    height: 25px;
    width: 100%;
    background: rgba(0, 0, 0, 0)
}

.DesktopMenu_navItem__I7bFc .dropdown-menu .dropdown-menu {
    left: 100%;
    top: 0;
    margin-top: -10px
}

.rtl .DesktopMenu_navItem__I7bFc .dropdown-menu .dropdown-menu {
    left: auto;
    right: 100%;
    margin-left: 0
}

.DesktopMenu_navItem__I7bFc .dropdown-menu .dropdown-menu:after,
.DesktopMenu_navItem__I7bFc .dropdown-menu .dropdown-menu:before {
    display: none
}

.DesktopMenu_navItem__I7bFc .dropdown-menu * {
    color: #000
}

.DesktopMenu_navItem__I7bFc:hover,
.DesktopMenu_navItemActive__z8T8I {
    opacity: 1
}

.DesktopMenu_navItem__I7bFc:hover>.DesktopMenu_navLink__uxcs4,
.DesktopMenu_navItemActive__z8T8I>.DesktopMenu_navLink__uxcs4 {
    opacity: 1;
    box-shadow: inset 0 -1px 0 0 #fff
}

.DesktopMenu_navLink__uxcs4 {
    padding: 20px 20px 20px 0px;
    box-shadow: inset 0 -1px 0 0 rgba(255, 255, 255, .3);
    opacity: .5;
    font-size: 20px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.DesktopMenu_navLink__uxcs4:hover {
    text-decoration: none;
    color: #fff
}

.EditorEmbed_embedContainer__A7EkL {
    background: #e8edf1;
    padding: 12px;
    border-radius: 8px
}

.EditorEmbed_embedContainer__A7EkL .EditorEmbed_embedComponent__HKtOS {
    border-radius: 8px;
    border: 1px dashed #bac7d5;
    width: 100%;
    height: 430px;
    justify-content: center;
    align-items: center;
    padding: 0px 30px;
    display: flex;
    flex-direction: column;
    text-align: center;
    color: #5f738c
}

.EditorEmbed_embedContainer__A7EkL .EditorEmbed_embedComponent__HKtOS p {
    color: #000;
    margin-top: 30px
}

.EditorEmbed_embedContainer__A7EkL .EditorEmbed_embedComponent__HKtOS p span {
    font-weight: 600;
    font-size: 14px;
    line-height: 20px
}

.EditorEmbed_embedContainer__A7EkL .EditorEmbed_embedComponent__HKtOS span {
    font-size: 12px;
    line-height: 16px
}

.EditorEmbed_embedContainer__A7EkL .EditorEmbed_embedComponent__HKtOS button {
    width: 149px;
    height: 36px;
    background: #0e9384;
    border-radius: 100px;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: #fff;
    border: none;
    outline: 0;
    margin-top: 30px;
    cursor: pointer
}

.EditorEmbed_iframeContainer__fMuK9 {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    height: 0
}

.EditorEmbed_iframeContainer__fMuK9 iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none
}

.LightBoxModal_modalCover__ActlA {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    transform: translateZ(0);
    background-color: rgba(0, 0, 0, .9)
}

.LightBoxModal_modal__eeQ72 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    -webkit-overflow-scrolling: touch
}

.LightBoxModal_modal__eeQ72 img {
    position: absolute;
    top: 0;
    left: 0
}

.LightBoxModal_modalClose__2zSoR {
    position: absolute;
    z-index: 1000;
    top: 0;
    right: 0;
    padding: .5em;
    line-height: 1;
    background: #f6f6f7;
    border: 0;
    box-shadow: 0;
    cursor: pointer
}

.LightBoxModal_modalClose__2zSoR .LightBoxModal_modalCloseIcon__LKXLL {
    width: 25px;
    height: 25px;
    fill: rgba(0, 0, 0, 0);
    stroke: #000;
    stroke-linecap: round;
    stroke-width: 2
}

.TextView_text__fJf-b h1,
.TextView_text__fJf-b h2,
.TextView_text__fJf-b h3,
.TextView_text__fJf-b h4,
.TextView_text__fJf-b h5,
.TextView_text__fJf-b h6,
.TextView_text__fJf-b p {
    margin: 0
}

.TextView_text__fJf-b strong,
.TextView_text__fJf-b strong * {
    font-weight: bold !important
}

.TextView_text__fJf-b em,
.TextView_text__fJf-b em * {
    font-style: italic !important
}

.TextView_text__fJf-b s,
.TextView_text__fJf-b s * {
    text-decoration: line-through !important
}

.TextView_text__fJf-b s span[style*="text-decoration: underline;"] {
    text-decoration: underline !important
}

.TextView_text__fJf-b s,
.TextView_text__fJf-b em,
.TextView_text__fJf-b strong,
.TextView_text__fJf-b span:not([style^=color]):not([style*=" color"]),
.TextView_text__fJf-b span[style^=color] *:not([style^=color]):not([style*=" color"]),
.TextView_text__fJf-b span[style*=" color"] *:not([style^=color]):not([style*=" color"]) {
    color: inherit !important
}

.TextView_text__fJf-b s,
.TextView_text__fJf-b em,
.TextView_text__fJf-b strong,
.TextView_text__fJf-b span:not([style*=font-size]),
.TextView_text__fJf-b span[style*=font-size] *:not([style*=font-size]) {
    font-size: inherit !important
}

.GeneralInputWrapper_desktop__lwGAC {
    display: block
}

@media(max-width: 600px) {
    .GeneralInputWrapper_desktop__lwGAC {
        display: none
    }
}

.GeneralInputWrapper_tablet__9Iro8 {
    display: none
}

@media(max-width: 768px) {
    .GeneralInputWrapper_tablet__9Iro8 {
        display: block
    }
}

.GeneralInputWrapper_mobile__xfdEw {
    display: none
}

@media(max-width: 600px) {
    .GeneralInputWrapper_mobile__xfdEw {
        display: block
    }
}

.GeneralInputWrapper_generalInputWrapper__ukhhD {
    margin-bottom: 4px;
    font-size: 16px;
    position: relative;
    display: flex;
    align-items: center;
    cursor: text;
    font-family: inherit;
    font-size: 14px;
    line-height: 34px
}

@media(max-width: 600px) {
    .GeneralInputWrapper_generalInputWrapper__ukhhD {
        font-size: 16px
    }
}

.GeneralInputWrapper_generalInputWrapper__ukhhD select {
    border: none
}

.GeneralInputWrapper_generalInputWrapper__ukhhD input,
.GeneralInputWrapper_generalInputWrapper__ukhhD textarea,
.GeneralInputWrapper_generalInputWrapper__ukhhD select {
    padding: 0;
    line-height: 34px;
    border: none;
    outline: none;
    color: inherit;
    font-family: inherit;
    background-color: rgba(0, 0, 0, 0);
    width: 100%;
    resize: vertical;
    -moz-appearance: textfield;
    font-size: 14px
}

.GeneralInputWrapper_generalInputWrapper__ukhhD input::placeholder,
.GeneralInputWrapper_generalInputWrapper__ukhhD textarea::placeholder,
.GeneralInputWrapper_generalInputWrapper__ukhhD select::placeholder {
    opacity: .5
}

.GeneralInputWrapper_generalInputWrapper__ukhhD input::-webkit-input-placeholder,
.GeneralInputWrapper_generalInputWrapper__ukhhD textarea::-webkit-input-placeholder,
.GeneralInputWrapper_generalInputWrapper__ukhhD select::-webkit-input-placeholder {
    opacity: .5
}

.GeneralInputWrapper_generalInputWrapper__ukhhD input:-moz-placeholder,
.GeneralInputWrapper_generalInputWrapper__ukhhD textarea:-moz-placeholder,
.GeneralInputWrapper_generalInputWrapper__ukhhD select:-moz-placeholder {
    opacity: .5
}

.GeneralInputWrapper_generalInputWrapper__ukhhD input:-ms-input-placeholder,
.GeneralInputWrapper_generalInputWrapper__ukhhD textarea:-ms-input-placeholder,
.GeneralInputWrapper_generalInputWrapper__ukhhD select:-ms-input-placeholder {
    opacity: .5
}

.GeneralInputWrapper_generalInputWrapper__ukhhD input::-webkit-inner-spin-button,
.GeneralInputWrapper_generalInputWrapper__ukhhD input ::-webkit-outer-spin-button,
.GeneralInputWrapper_generalInputWrapper__ukhhD textarea::-webkit-inner-spin-button,
.GeneralInputWrapper_generalInputWrapper__ukhhD textarea ::-webkit-outer-spin-button,
.GeneralInputWrapper_generalInputWrapper__ukhhD select::-webkit-inner-spin-button,
.GeneralInputWrapper_generalInputWrapper__ukhhD select ::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

@media(max-width: 600px) {
    .GeneralInputWrapper_generalInputWrapper__ukhhD input,
    .GeneralInputWrapper_generalInputWrapper__ukhhD textarea,
    .GeneralInputWrapper_generalInputWrapper__ukhhD select {
        font-size: 16px
    }
}

.GeneralInputWrapper_error__eWYSv {
    border-color: #fda29b !important
}

.GeneralInputWrapper_error__eWYSv select {
    color: #fda29b !important
}

.GeneralInputWrapper_error__eWYSv:focus-within {
    border: 2px solid #fda29b !important
}

.dateContainer {
    display: flex;
    align-items: center;
    width: -moz-max-content;
    width: max-content;
    gap: 10px;
    border-radius: 6px;
    background: none;
    width: 100%;
    min-height: 35.6px
}

.dateContainer .react-datepicker__input-container input {
    border: none;
    border-radius: 6px;
    width: 100%;
    box-shadow: none;
    background: none
}

.dateContainer .react-datepicker__navigation-icon::before {
    top: 20px;
    border-color: #344054;
    border-width: 2px 2px 0 0
}

.dateContainer .react-datepicker__day--keyboard-selected {
    background: #0e9384;
    border-radius: 100%
}

.dateContainer .react-datepicker__day--selected {
    background: #0e9384;
    border-radius: 100%
}

.dateContainer .react-datepicker__day:hover {
    border-radius: 100%
}

.dateContainer .react-datepicker__header {
    background-color: #fff;
    border-bottom: none
}

.dateContainer .react-datepicker__input-container {
    width: 100%;
    position: relative
}

.dateContainer .react-datepicker__input-container input:focus-visible {
    border: none;
    outline: none
}

.dateContainer .react-datepicker__current-month {
    color: #344054;
    padding-top: 10px;
    padding-bottom: 10px
}

.dateContainer .react-datepicker {
    box-shadow: 0px 20px 24px -4px rgba(16, 24, 40, .08), 0px 8px 8px -4px rgba(16, 24, 40, .03);
    border: 1px solid #f2f4f7;
    background: #fff;
    display: flex;
    flex-direction: column
}

.dateContainer .react-datepicker__navigation {
    outline: none
}

.dateContainer .react-datepicker__day--keyboard-selected {
    background: #fff;
    border-radius: 100%;
    color: #344054
}

.dateContainer .react-datepicker__input-container input::-moz-placeholder {
    color: #d0d5dd
}

.dateContainer .react-datepicker__input-container input::placeholder {
    color: #d0d5dd
}

.dateContainer .react-datepicker__day--today {
    font-weight: 400
}

.dateContainer .react-datepicker__day--outside-month {
    color: #667085
}

.dateContainer .react-datepicker__day-names {
    font-family: "Inter";
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    color: #344054
}

.error {
    border-color: #fda29b !important
}

.error:focus-within {
    border: 2px solid #fda29b !important
}

.calenderStyle {
    margin-inline-end: -50px;
    margin-bottom: 15px
}

.CheckboxInput_checkboxWrapper__ouPN4 {
    display: flex;
    flex-direction: column
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn {
    display: flex;
    align-items: center;
    position: relative;
    margin: 0 0 15px
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    z-index: 2;
    height: 16px;
    width: 16px;
    cursor: pointer
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input+label {
    display: flex;
    align-items: center;
    position: relative;
    line-height: 14px;
    margin: 0;
    padding-inline-start: 25px;
    font-size: 14px;
    color: #1d2939
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input+label:before {
    position: absolute;
    display: block;
    left: 0;
    top: 0;
    content: "";
    background: rgba(0, 0, 0, 0);
    width: 16px;
    height: 16px;
    border: 1px solid #d0d5dd;
    border-radius: 4px;
    transition: 200ms ease-in-out all
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input+label:after {
    position: absolute;
    display: block;
    top: 3px;
    left: 6.5px;
    content: "";
    width: 4px;
    height: 8px;
    border-right: 2px solid rgba(0, 0, 0, 0);
    border-bottom: 2px solid rgba(0, 0, 0, 0);
    transform: rotate(45deg);
    transition: 200ms ease-in-out all
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input:checked+label:before {
    border: 1px solid #475467;
    background: #fff
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input:checked+label:after {
    border-color: #1d2939
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input:disabled+label:before {
    background: #f2f2f2;
    box-shadow: none
}

.CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input:disabled+label:after {
    border-color: rgba(0, 0, 0, 0)
}

.rtl .CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input {
    right: 0
}

.rtl .CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input+label:before {
    right: 0
}

.rtl .CheckboxInput_checkboxWrapper__ouPN4 .CheckboxInput_checkboxInput__\+awAn input+label:after {
    left: 0;
    right: 6.5px
}

.RadioInput_radioWrapper__xRqjp {
    display: flex;
    flex-direction: column
}

.RadioInput_radioWrapper__xRqjp .RadioInput_radioInput__57xIF {
    display: flex;
    align-items: center;
    margin: 0 0 15px
}

.RadioInput_radioWrapper__xRqjp .RadioInput_radioInput__57xIF input {
    appearance: none;
    border-radius: 50%;
    border: 3px solid #fff;
    width: 14px;
    height: 14px;
    cursor: pointer;
    outline: 1px solid #d0d5dd
}

.RadioInput_radioWrapper__xRqjp .RadioInput_radioInput__57xIF input:checked {
    background: #475467;
    outline: 1px solid #475467
}

.RadioInput_radioWrapper__xRqjp .RadioInput_radioInput__57xIF label {
    margin-inline-start: 9px;
    margin-bottom: 0px
}

.TimeInput_timeInputContainer__r0a4q {
    display: flex;
    align-items: center;
    gap: 6px
}

.TimeInput_hours__mgsW7,
.TimeInput_minutes__0SHfd {
    width: 50px !important;
    border: 1px solid #d0d5dd;
    gap: 10px;
    box-shadow: 0px 1px 2px rgba(16, 24, 40, .05);
    background: #fff
}

.TimeInput_selectPmAm__-NbHL {
    border: 1px solid #d0d5dd;
    gap: 10px;
    border-radius: 6px;
    box-shadow: 0px 1px 2px rgba(16, 24, 40, .05);
    background: #fff;
    height: 35.6px;
    padding: 8px
}

.FileUploadInput_fileUploadContainer__Lg5Ri {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    cursor: pointer
}

.FileUploadInput_fileUploadContainer__Lg5Ri>div:first-of-type {
    width: 100%;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    cursor: pointer;
    justify-content: space-between
}

.FileUploadInput_fileUploadContainer__Lg5Ri .FileUploadInput_NoFileSelectedText__u0axZ {
    font-size: 16px;
    font-weight: 400;
    color: #667085;
    opacity: .5
}

.FileUploadInput_fileUploadContainer__Lg5Ri .FileUploadInput_fileName__\+jtUV {
    color: #101828;
    font-size: 14px
}

.FileUploadInput_fileUploadContainer__Lg5Ri .FileUploadInput_fileUploadInput__2a-Rz {
    display: none
}

.FileUploadInput_fileUploadContainer__Lg5Ri .FileUploadInput_fileUploadButton__DLOOU {
    display: flex;
    gap: 5px;
    font-family: inherit;
    flex: 1;
    align-self: stretch;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
    margin-bottom: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    cursor: pointer;
    width: max-content;
    border: 1px solid #d0d5dd;
    border-left: none
}

.FileUploadInput_fileUploadContainer__Lg5Ri .FileUploadInput_inputContainer__ngtDe {
    width: 100%;
    height: 35.6px
}

.FileUploadInput_error__Ei2Jj {
    color: #d92d20;
    font-size: 14px
}

.rtl .FileUploadInput_fileUploadContainer__Lg5Ri>div:first-of-type {
    border-right-width: 1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px
}

.rtl .FileUploadInput_fileUploadContainer__Lg5Ri .FileUploadInput_fileUploadButton__DLOOU {
    border-right: none;
    border-left: 1px solid #d0d5dd;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px
}

.UploadSpinner_loader__A-CM3 {
    margin: 0 5px;
    display: inline-block;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 13px;
    height: 13px;
    animation: UploadSpinner_spin__bi28K 1s linear infinite
}

@keyframes UploadSpinner_spin__bi28K {
    0% {
        transform: rotate(0deg)
    }
    100% {
        transform: rotate(360deg)
    }
}

.FeedbackMessage_feedbackMessage__mO-SY {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-radius: 8px;
    margin: 10px 0px;
    border: 2px solid
}

.FeedbackMessage_success__AWj-A {
    background-color: #ecf8f7;
    border-color: #c0e8e4;
    color: #0f766e
}

.FeedbackMessage_fail__koWnK {
    background-color: #faeaea;
    border-color: #eeb9b9;
    color: #970c0c
}

.FeedbackMessage_closeButton__WXnc0 {
    padding: 0;
    background-color: rgba(0, 0, 0, 0);
    border: none;
    cursor: pointer;
    opacity: .5
}

.FeedbackMessage_closeButton__WXnc0:hover {
    opacity: 1
}

.Spinner_lds-ring__nToQG {
    display: inline-block;
    position: relative;
    width: 32px;
    height: 32px
}

.Spinner_lds-ring__nToQG div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 32px;
    height: 32px;
    margin: 8px;
    border: 4px solid var(--theme-color-primary);
    border-radius: 50%;
    animation: Spinner_lds-ring__nToQG 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: var(--theme-color-primary) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0)
}

.Spinner_lds-ring__nToQG div:nth-child(1) {
    animation-delay: -0.45s
}

.Spinner_lds-ring__nToQG div:nth-child(2) {
    animation-delay: -0.3s
}

.Spinner_lds-ring__nToQG div:nth-child(3) {
    animation-delay: -0.15s
}

@keyframes Spinner_lds-ring__nToQG {
    0% {
        transform: rotate(0deg)
    }
    100% {
        transform: rotate(360deg)
    }
}

.FormView_FormFieldsContainer__y0gtQ {
    display: flex;
    flex-direction: column
}

.FormView_FormFieldsContainer__y0gtQ>* {
    margin-bottom: 16px
}

.FormView_FormFieldsContainer__y0gtQ>*:last-child {
    margin: 0
}

/*# sourceMappingURL=1384.abb6573308944474.css.map*/