(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_babel_runtime_helpers_esm_classCallCheck_js-node_modules_babel_runtime_h-6f4ce5"], {

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _arrayLikeToArray)
                    /* harmony export */
                });

                function _arrayLikeToArray(arr, len) {
                    if (len == null || len > arr.length) len = arr.length;
                    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];
                    return arr2;
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _arrayWithHoles)
                    /* harmony export */
                });

                function _arrayWithHoles(arr) {
                    if (Array.isArray(arr)) return arr;
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _arrayWithoutHoles)
                    /* harmony export */
                });
                /* harmony import */
                var _arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js");

                function _arrayWithoutHoles(arr) {
                    if (Array.isArray(arr)) return (0, _arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(arr);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _assertThisInitialized)
                    /* harmony export */
                });

                function _assertThisInitialized(self) {
                    if (self === void 0) {
                        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                    }
                    return self;
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/classCallCheck.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _classCallCheck)
                    /* harmony export */
                });

                function _classCallCheck(instance, Constructor) {
                    if (!(instance instanceof Constructor)) {
                        throw new TypeError("Cannot call a class as a function");
                    }
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/createClass.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _createClass)
                    /* harmony export */
                });
                /* harmony import */
                var _toPropertyKey_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/toPropertyKey.js");

                function _defineProperties(target, props) {
                    for (var i = 0; i < props.length; i++) {
                        var descriptor = props[i];
                        descriptor.enumerable = descriptor.enumerable || false;
                        descriptor.configurable = true;
                        if ("value" in descriptor) descriptor.writable = true;
                        Object.defineProperty(target, (0, _toPropertyKey_js__WEBPACK_IMPORTED_MODULE_0__["default"])(descriptor.key), descriptor);
                    }
                }

                function _createClass(Constructor, protoProps, staticProps) {
                    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
                    if (staticProps) _defineProperties(Constructor, staticProps);
                    Object.defineProperty(Constructor, "prototype", {
                        writable: false
                    });
                    return Constructor;
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/createSuper.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _createSuper)
                    /* harmony export */
                });
                /* harmony import */
                var _getPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js");
                /* harmony import */
                var _isNativeReflectConstruct_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js");
                /* harmony import */
                var _possibleConstructorReturn_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js");



                function _createSuper(Derived) {
                    var hasNativeReflectConstruct = (0, _isNativeReflectConstruct_js__WEBPACK_IMPORTED_MODULE_1__["default"])();
                    return function _createSuperInternal() {
                        var Super = (0, _getPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__["default"])(Derived),
                            result;
                        if (hasNativeReflectConstruct) {
                            var NewTarget = (0, _getPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__["default"])(this).constructor;
                            result = Reflect.construct(Super, arguments, NewTarget);
                        } else {
                            result = Super.apply(this, arguments);
                        }
                        return (0, _possibleConstructorReturn_js__WEBPACK_IMPORTED_MODULE_2__["default"])(this, result);
                    };
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/defineProperty.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _defineProperty)
                    /* harmony export */
                });
                /* harmony import */
                var _toPropertyKey_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/toPropertyKey.js");

                function _defineProperty(obj, key, value) {
                    key = (0, _toPropertyKey_js__WEBPACK_IMPORTED_MODULE_0__["default"])(key);
                    if (key in obj) {
                        Object.defineProperty(obj, key, {
                            value: value,
                            enumerable: true,
                            configurable: true,
                            writable: true
                        });
                    } else {
                        obj[key] = value;
                    }
                    return obj;
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/extends.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _extends)
                    /* harmony export */
                });

                function _extends() {
                    _extends = Object.assign ? Object.assign.bind() : function(target) {
                        for (var i = 1; i < arguments.length; i++) {
                            var source = arguments[i];
                            for (var key in source) {
                                if (Object.prototype.hasOwnProperty.call(source, key)) {
                                    target[key] = source[key];
                                }
                            }
                        }
                        return target;
                    };
                    return _extends.apply(this, arguments);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _getPrototypeOf)
                    /* harmony export */
                });

                function _getPrototypeOf(o) {
                    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {
                        return o.__proto__ || Object.getPrototypeOf(o);
                    };
                    return _getPrototypeOf(o);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/inherits.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _inherits)
                    /* harmony export */
                });
                /* harmony import */
                var _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js");

                function _inherits(subClass, superClass) {
                    if (typeof superClass !== "function" && superClass !== null) {
                        throw new TypeError("Super expression must either be null or a function");
                    }
                    subClass.prototype = Object.create(superClass && superClass.prototype, {
                        constructor: {
                            value: subClass,
                            writable: true,
                            configurable: true
                        }
                    });
                    Object.defineProperty(subClass, "prototype", {
                        writable: false
                    });
                    if (superClass)(0, _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__["default"])(subClass, superClass);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _isNativeReflectConstruct)
                    /* harmony export */
                });

                function _isNativeReflectConstruct() {
                    if (typeof Reflect === "undefined" || !Reflect.construct) return false;
                    if (Reflect.construct.sham) return false;
                    if (typeof Proxy === "function") return true;
                    try {
                        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
                        return true;
                    } catch (e) {
                        return false;
                    }
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/iterableToArray.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _iterableToArray)
                    /* harmony export */
                });

                function _iterableToArray(iter) {
                    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/iterableToArrayLimit.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _iterableToArrayLimit)
                    /* harmony export */
                });

                function _iterableToArrayLimit(arr, i) {
                    var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"];
                    if (null != _i) {
                        var _s,
                            _e,
                            _x,
                            _r,
                            _arr = [],
                            _n = !0,
                            _d = !1;
                        try {
                            if (_x = (_i = _i.call(arr)).next, 0 === i) {
                                if (Object(_i) !== _i) return;
                                _n = !1;
                            } else
                                for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0);
                        } catch (err) {
                            _d = !0, _e = err;
                        } finally {
                            try {
                                if (!_n && null != _i["return"] && (_r = _i["return"](), Object(_r) !== _r)) return;
                            } finally {
                                if (_d) throw _e;
                            }
                        }
                        return _arr;
                    }
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/nonIterableRest.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _nonIterableRest)
                    /* harmony export */
                });

                function _nonIterableRest() {
                    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _nonIterableSpread)
                    /* harmony export */
                });

                function _nonIterableSpread() {
                    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/objectSpread2.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _objectSpread2)
                    /* harmony export */
                });
                /* harmony import */
                var _defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/defineProperty.js");

                function ownKeys(object, enumerableOnly) {
                    var keys = Object.keys(object);
                    if (Object.getOwnPropertySymbols) {
                        var symbols = Object.getOwnPropertySymbols(object);
                        enumerableOnly && (symbols = symbols.filter(function(sym) {
                            return Object.getOwnPropertyDescriptor(object, sym).enumerable;
                        })), keys.push.apply(keys, symbols);
                    }
                    return keys;
                }

                function _objectSpread2(target) {
                    for (var i = 1; i < arguments.length; i++) {
                        var source = null != arguments[i] ? arguments[i] : {};
                        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {
                            (0, _defineProperty_js__WEBPACK_IMPORTED_MODULE_0__["default"])(target, key, source[key]);
                        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
                            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
                        });
                    }
                    return target;
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _objectWithoutProperties)
                    /* harmony export */
                });
                /* harmony import */
                var _objectWithoutPropertiesLoose_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js");

                function _objectWithoutProperties(source, excluded) {
                    if (source == null) return {};
                    var target = (0, _objectWithoutPropertiesLoose_js__WEBPACK_IMPORTED_MODULE_0__["default"])(source, excluded);
                    var key, i;
                    if (Object.getOwnPropertySymbols) {
                        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
                        for (i = 0; i < sourceSymbolKeys.length; i++) {
                            key = sourceSymbolKeys[i];
                            if (excluded.indexOf(key) >= 0) continue;
                            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
                            target[key] = source[key];
                        }
                    }
                    return target;
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _objectWithoutPropertiesLoose)
                    /* harmony export */
                });

                function _objectWithoutPropertiesLoose(source, excluded) {
                    if (source == null) return {};
                    var target = {};
                    var sourceKeys = Object.keys(source);
                    var key, i;
                    for (i = 0; i < sourceKeys.length; i++) {
                        key = sourceKeys[i];
                        if (excluded.indexOf(key) >= 0) continue;
                        target[key] = source[key];
                    }
                    return target;
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _possibleConstructorReturn)
                    /* harmony export */
                });
                /* harmony import */
                var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/typeof.js");
                /* harmony import */
                var _assertThisInitialized_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js");


                function _possibleConstructorReturn(self, call) {
                    if (call && ((0, _typeof_js__WEBPACK_IMPORTED_MODULE_0__["default"])(call) === "object" || typeof call === "function")) {
                        return call;
                    } else if (call !== void 0) {
                        throw new TypeError("Derived constructors may only return object or undefined");
                    }
                    return (0, _assertThisInitialized_js__WEBPACK_IMPORTED_MODULE_1__["default"])(self);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _setPrototypeOf)
                    /* harmony export */
                });

                function _setPrototypeOf(o, p) {
                    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {
                        o.__proto__ = p;
                        return o;
                    };
                    return _setPrototypeOf(o, p);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/slicedToArray.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _slicedToArray)
                    /* harmony export */
                });
                /* harmony import */
                var _arrayWithHoles_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js");
                /* harmony import */
                var _iterableToArrayLimit_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/iterableToArrayLimit.js");
                /* harmony import */
                var _unsupportedIterableToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js");
                /* harmony import */
                var _nonIterableRest_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/nonIterableRest.js");




                function _slicedToArray(arr, i) {
                    return (0, _arrayWithHoles_js__WEBPACK_IMPORTED_MODULE_0__["default"])(arr) || (0, _iterableToArrayLimit_js__WEBPACK_IMPORTED_MODULE_1__["default"])(arr, i) || (0, _unsupportedIterableToArray_js__WEBPACK_IMPORTED_MODULE_2__["default"])(arr, i) || (0, _nonIterableRest_js__WEBPACK_IMPORTED_MODULE_3__["default"])();
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _taggedTemplateLiteral)
                    /* harmony export */
                });

                function _taggedTemplateLiteral(strings, raw) {
                    if (!raw) {
                        raw = strings.slice(0);
                    }
                    return Object.freeze(Object.defineProperties(strings, {
                        raw: {
                            value: Object.freeze(raw)
                        }
                    }));
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/toConsumableArray.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _toConsumableArray)
                    /* harmony export */
                });
                /* harmony import */
                var _arrayWithoutHoles_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js");
                /* harmony import */
                var _iterableToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/iterableToArray.js");
                /* harmony import */
                var _unsupportedIterableToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js");
                /* harmony import */
                var _nonIterableSpread_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js");




                function _toConsumableArray(arr) {
                    return (0, _arrayWithoutHoles_js__WEBPACK_IMPORTED_MODULE_0__["default"])(arr) || (0, _iterableToArray_js__WEBPACK_IMPORTED_MODULE_1__["default"])(arr) || (0, _unsupportedIterableToArray_js__WEBPACK_IMPORTED_MODULE_2__["default"])(arr) || (0, _nonIterableSpread_js__WEBPACK_IMPORTED_MODULE_3__["default"])();
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/toPrimitive.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _toPrimitive)
                    /* harmony export */
                });
                /* harmony import */
                var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/typeof.js");

                function _toPrimitive(input, hint) {
                    if ((0, _typeof_js__WEBPACK_IMPORTED_MODULE_0__["default"])(input) !== "object" || input === null) return input;
                    var prim = input[Symbol.toPrimitive];
                    if (prim !== undefined) {
                        var res = prim.call(input, hint || "default");
                        if ((0, _typeof_js__WEBPACK_IMPORTED_MODULE_0__["default"])(res) !== "object") return res;
                        throw new TypeError("@@toPrimitive must return a primitive value.");
                    }
                    return (hint === "string" ? String : Number)(input);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/toPropertyKey.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _toPropertyKey)
                    /* harmony export */
                });
                /* harmony import */
                var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/typeof.js");
                /* harmony import */
                var _toPrimitive_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/toPrimitive.js");


                function _toPropertyKey(arg) {
                    var key = (0, _toPrimitive_js__WEBPACK_IMPORTED_MODULE_1__["default"])(arg, "string");
                    return (0, _typeof_js__WEBPACK_IMPORTED_MODULE_0__["default"])(key) === "symbol" ? key : String(key);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/typeof.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _typeof)
                    /* harmony export */
                });

                function _typeof(obj) {
                    "@babel/helpers - typeof";

                    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj) {
                        return typeof obj;
                    } : function(obj) {
                        return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
                    }, _typeof(obj);
                }

                /***/
            }),

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _unsupportedIterableToArray)
                    /* harmony export */
                });
                /* harmony import */
                var _arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js");

                function _unsupportedIterableToArray(o, minLen) {
                    if (!o) return;
                    if (typeof o === "string") return (0, _arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(o, minLen);
                    var n = Object.prototype.toString.call(o).slice(8, -1);
                    if (n === "Object" && o.constructor) n = o.constructor.name;
                    if (n === "Map" || n === "Set") return Array.from(o);
                    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return (0, _arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(o, minLen);
                }

                /***/
            }),

        /***/
        "../../node_modules/memoize-one/dist/memoize-one.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ memoizeOne)
                    /* harmony export */
                });
                var safeIsNaN = Number.isNaN ||
                    function ponyfill(value) {
                        return typeof value === 'number' && value !== value;
                    };

                function isEqual(first, second) {
                    if (first === second) {
                        return true;
                    }
                    if (safeIsNaN(first) && safeIsNaN(second)) {
                        return true;
                    }
                    return false;
                }

                function areInputsEqual(newInputs, lastInputs) {
                    if (newInputs.length !== lastInputs.length) {
                        return false;
                    }
                    for (var i = 0; i < newInputs.length; i++) {
                        if (!isEqual(newInputs[i], lastInputs[i])) {
                            return false;
                        }
                    }
                    return true;
                }

                function memoizeOne(resultFn, isEqual) {
                    if (isEqual === void 0) {
                        isEqual = areInputsEqual;
                    }
                    var cache = null;

                    function memoized() {
                        var newArgs = [];
                        for (var _i = 0; _i < arguments.length; _i++) {
                            newArgs[_i] = arguments[_i];
                        }
                        if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {
                            return cache.lastResult;
                        }
                        var lastResult = resultFn.apply(this, newArgs);
                        cache = {
                            lastResult: lastResult,
                            lastArgs: newArgs,
                            lastThis: this,
                        };
                        return lastResult;
                    }
                    memoized.clear = function clear() {
                        cache = null;
                    };
                    return memoized;
                }




                /***/
            }),

        /***/
        "../../node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.browser.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?5aae");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);


                var index = react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;

                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (index);


                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/core/dist/floating-ui.core.browser.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    arrow: () => ( /* binding */ arrow),
                    /* harmony export */
                    autoPlacement: () => ( /* binding */ autoPlacement),
                    /* harmony export */
                    computePosition: () => ( /* binding */ computePosition),
                    /* harmony export */
                    detectOverflow: () => ( /* binding */ detectOverflow),
                    /* harmony export */
                    flip: () => ( /* binding */ flip),
                    /* harmony export */
                    hide: () => ( /* binding */ hide),
                    /* harmony export */
                    inline: () => ( /* binding */ inline),
                    /* harmony export */
                    limitShift: () => ( /* binding */ limitShift),
                    /* harmony export */
                    offset: () => ( /* binding */ offset),
                    /* harmony export */
                    rectToClientRect: () => ( /* binding */ rectToClientRect),
                    /* harmony export */
                    shift: () => ( /* binding */ shift),
                    /* harmony export */
                    size: () => ( /* binding */ size)
                    /* harmony export */
                });

                function getAlignment(placement) {
                    return placement.split('-')[1];
                }

                function getLengthFromAxis(axis) {
                    return axis === 'y' ? 'height' : 'width';
                }

                function getSide(placement) {
                    return placement.split('-')[0];
                }

                function getMainAxisFromPlacement(placement) {
                    return ['top', 'bottom'].includes(getSide(placement)) ? 'x' : 'y';
                }

                function computeCoordsFromPlacement(_ref, placement, rtl) {
                    let {
                        reference,
                        floating
                    } = _ref;
                    const commonX = reference.x + reference.width / 2 - floating.width / 2;
                    const commonY = reference.y + reference.height / 2 - floating.height / 2;
                    const mainAxis = getMainAxisFromPlacement(placement);
                    const length = getLengthFromAxis(mainAxis);
                    const commonAlign = reference[length] / 2 - floating[length] / 2;
                    const side = getSide(placement);
                    const isVertical = mainAxis === 'x';
                    let coords;
                    switch (side) {
                        case 'top':
                            coords = {
                                x: commonX,
                                y: reference.y - floating.height
                            };
                            break;
                        case 'bottom':
                            coords = {
                                x: commonX,
                                y: reference.y + reference.height
                            };
                            break;
                        case 'right':
                            coords = {
                                x: reference.x + reference.width,
                                y: commonY
                            };
                            break;
                        case 'left':
                            coords = {
                                x: reference.x - floating.width,
                                y: commonY
                            };
                            break;
                        default:
                            coords = {
                                x: reference.x,
                                y: reference.y
                            };
                    }
                    switch (getAlignment(placement)) {
                        case 'start':
                            coords[mainAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);
                            break;
                        case 'end':
                            coords[mainAxis] += commonAlign * (rtl && isVertical ? -1 : 1);
                            break;
                    }
                    return coords;
                }

                /**
                 * Computes the `x` and `y` coordinates that will place the floating element
                 * next to a reference element when it is given a certain positioning strategy.
                 *
                 * This export does not have any `platform` interface logic. You will need to
                 * write one for the platform you are using Floating UI with.
                 */
                const computePosition = async (reference, floating, config) => {
                    const {
                        placement = 'bottom',
                            strategy = 'absolute',
                            middleware = [],
                            platform
                    } = config;
                    const validMiddleware = middleware.filter(Boolean);
                    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating)); {
                        if (platform == null) {
                            console.error(['Floating UI: `platform` property was not passed to config. If you', 'want to use Floating UI on the web, install @floating-ui/dom', 'instead of the /core package. Otherwise, you can create your own', '`platform`: https://floating-ui.com/docs/platform'].join(' '));
                        }
                        if (validMiddleware.filter(_ref => {
                                let {
                                    name
                                } = _ref;
                                return name === 'autoPlacement' || name === 'flip';
                            }).length > 1) {
                            throw new Error(['Floating UI: duplicate `flip` and/or `autoPlacement` middleware', 'detected. This will lead to an infinite loop. Ensure only one of', 'either has been passed to the `middleware` array.'].join(' '));
                        }
                        if (!reference || !floating) {
                            console.error(['Floating UI: The reference and/or floating element was not defined', 'when `computePosition()` was called. Ensure that both elements have', 'been created and can be measured.'].join(' '));
                        }
                    }
                    let rects = await platform.getElementRects({
                        reference,
                        floating,
                        strategy
                    });
                    let {
                        x,
                        y
                    } = computeCoordsFromPlacement(rects, placement, rtl);
                    let statefulPlacement = placement;
                    let middlewareData = {};
                    let resetCount = 0;
                    for (let i = 0; i < validMiddleware.length; i++) {
                        const {
                            name,
                            fn
                        } = validMiddleware[i];
                        const {
                            x: nextX,
                            y: nextY,
                            data,
                            reset
                        } = await fn({
                            x,
                            y,
                            initialPlacement: placement,
                            placement: statefulPlacement,
                            strategy,
                            middlewareData,
                            rects,
                            platform,
                            elements: {
                                reference,
                                floating
                            }
                        });
                        x = nextX != null ? nextX : x;
                        y = nextY != null ? nextY : y;
                        middlewareData = {
                            ...middlewareData,
                            [name]: {
                                ...middlewareData[name],
                                ...data
                            }
                        }; {
                            if (resetCount > 50) {
                                console.warn(['Floating UI: The middleware lifecycle appears to be running in an', 'infinite loop. This is usually caused by a `reset` continually', 'being returned without a break condition.'].join(' '));
                            }
                        }
                        if (reset && resetCount <= 50) {
                            resetCount++;
                            if (typeof reset === 'object') {
                                if (reset.placement) {
                                    statefulPlacement = reset.placement;
                                }
                                if (reset.rects) {
                                    rects = reset.rects === true ? await platform.getElementRects({
                                        reference,
                                        floating,
                                        strategy
                                    }) : reset.rects;
                                }
                                ({
                                    x,
                                    y
                                } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));
                            }
                            i = -1;
                            continue;
                        }
                    }
                    return {
                        x,
                        y,
                        placement: statefulPlacement,
                        strategy,
                        middlewareData
                    };
                };

                function expandPaddingObject(padding) {
                    return {
                        top: 0,
                        right: 0,
                        bottom: 0,
                        left: 0,
                        ...padding
                    };
                }

                function getSideObjectFromPadding(padding) {
                    return typeof padding !== 'number' ? expandPaddingObject(padding) : {
                        top: padding,
                        right: padding,
                        bottom: padding,
                        left: padding
                    };
                }

                function rectToClientRect(rect) {
                    return {
                        ...rect,
                        top: rect.y,
                        left: rect.x,
                        right: rect.x + rect.width,
                        bottom: rect.y + rect.height
                    };
                }

                /**
                 * Resolves with an object of overflow side offsets that determine how much the
                 * element is overflowing a given clipping boundary on each side.
                 * - positive = overflowing the boundary by that number of pixels
                 * - negative = how many pixels left before it will overflow
                 * - 0 = lies flush with the boundary
                 * @see https://floating-ui.com/docs/detectOverflow
                 */
                async function detectOverflow(state, options) {
                    var _await$platform$isEle;
                    if (options === void 0) {
                        options = {};
                    }
                    const {
                        x,
                        y,
                        platform,
                        rects,
                        elements,
                        strategy
                    } = state;
                    const {
                        boundary = 'clippingAncestors',
                            rootBoundary = 'viewport',
                            elementContext = 'floating',
                            altBoundary = false,
                            padding = 0
                    } = options;
                    const paddingObject = getSideObjectFromPadding(padding);
                    const altContext = elementContext === 'floating' ? 'reference' : 'floating';
                    const element = elements[altBoundary ? altContext : elementContext];
                    const clippingClientRect = rectToClientRect(await platform.getClippingRect({
                        element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),
                        boundary,
                        rootBoundary,
                        strategy
                    }));
                    const rect = elementContext === 'floating' ? {
                        ...rects.floating,
                        x,
                        y
                    } : rects.reference;
                    const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));
                    const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {
                        x: 1,
                        y: 1
                    } : {
                        x: 1,
                        y: 1
                    };
                    const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({
                        rect,
                        offsetParent,
                        strategy
                    }) : rect);
                    return {
                        top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,
                        bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,
                        left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,
                        right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x
                    };
                }

                const min = Math.min;
                const max = Math.max;

                function within(min$1, value, max$1) {
                    return max(min$1, min(value, max$1));
                }

                /**
                 * Provides data to position an inner element of the floating element so that it
                 * appears centered to the reference element.
                 * @see https://floating-ui.com/docs/arrow
                 */
                const arrow = options => ({
                    name: 'arrow',
                    options,
                    async fn(state) {
                        // Since `element` is required, we don't Partial<> the type.
                        const {
                            element,
                            padding = 0
                        } = options || {};
                        const {
                            x,
                            y,
                            placement,
                            rects,
                            platform,
                            elements
                        } = state;
                        if (element == null) {
                            {
                                console.warn('Floating UI: No `element` was passed to the `arrow` middleware.');
                            }
                            return {};
                        }
                        const paddingObject = getSideObjectFromPadding(padding);
                        const coords = {
                            x,
                            y
                        };
                        const axis = getMainAxisFromPlacement(placement);
                        const length = getLengthFromAxis(axis);
                        const arrowDimensions = await platform.getDimensions(element);
                        const isYAxis = axis === 'y';
                        const minProp = isYAxis ? 'top' : 'left';
                        const maxProp = isYAxis ? 'bottom' : 'right';
                        const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';
                        const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];
                        const startDiff = coords[axis] - rects.reference[axis];
                        const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));
                        let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;

                        // DOM platform can return `window` as the `offsetParent`.
                        if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {
                            clientSize = elements.floating[clientProp] || rects.floating[length];
                        }
                        const centerToReference = endDiff / 2 - startDiff / 2;

                        // Make sure the arrow doesn't overflow the floating element if the center
                        // point is outside the floating element's bounds.
                        const min = paddingObject[minProp];
                        const max = clientSize - arrowDimensions[length] - paddingObject[maxProp];
                        const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;
                        const offset = within(min, center, max);

                        // If the reference is small enough that the arrow's padding causes it to
                        // to point to nothing for an aligned placement, adjust the offset of the
                        // floating element itself. This stops `shift()` from taking action, but can
                        // be worked around by calling it again after the `arrow()` if desired.
                        const shouldAddOffset = getAlignment(placement) != null && center != offset && rects.reference[length] / 2 - (center < min ? paddingObject[minProp] : paddingObject[maxProp]) - arrowDimensions[length] / 2 < 0;
                        const alignmentOffset = shouldAddOffset ? center < min ? min - center : max - center : 0;
                        return {
                            [axis]: coords[axis] - alignmentOffset,
                            data: {
                                [axis]: offset,
                                centerOffset: center - offset
                            }
                        };
                    }
                });

                const sides = ['top', 'right', 'bottom', 'left'];
                const allPlacements = /*#__PURE__*/ sides.reduce((acc, side) => acc.concat(side, side + "-start", side + "-end"), []);

                const oppositeSideMap = {
                    left: 'right',
                    right: 'left',
                    bottom: 'top',
                    top: 'bottom'
                };

                function getOppositePlacement(placement) {
                    return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);
                }

                function getAlignmentSides(placement, rects, rtl) {
                    if (rtl === void 0) {
                        rtl = false;
                    }
                    const alignment = getAlignment(placement);
                    const mainAxis = getMainAxisFromPlacement(placement);
                    const length = getLengthFromAxis(mainAxis);
                    let mainAlignmentSide = mainAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';
                    if (rects.reference[length] > rects.floating[length]) {
                        mainAlignmentSide = getOppositePlacement(mainAlignmentSide);
                    }
                    return {
                        main: mainAlignmentSide,
                        cross: getOppositePlacement(mainAlignmentSide)
                    };
                }

                const oppositeAlignmentMap = {
                    start: 'end',
                    end: 'start'
                };

                function getOppositeAlignmentPlacement(placement) {
                    return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);
                }

                function getPlacementList(alignment, autoAlignment, allowedPlacements) {
                    const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);
                    return allowedPlacementsSortedByAlignment.filter(placement => {
                        if (alignment) {
                            return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);
                        }
                        return true;
                    });
                }
                /**
                 * Optimizes the visibility of the floating element by choosing the placement
                 * that has the most space available automatically, without needing to specify a
                 * preferred placement. Alternative to `flip`.
                 * @see https://floating-ui.com/docs/autoPlacement
                 */
                const autoPlacement = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'autoPlacement',
                        options,
                        async fn(state) {
                            var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;
                            const {
                                rects,
                                middlewareData,
                                placement,
                                platform,
                                elements
                            } = state;
                            const {
                                crossAxis = false,
                                    alignment,
                                    allowedPlacements = allPlacements,
                                    autoAlignment = true,
                                    ...detectOverflowOptions
                            } = options;
                            const placements = alignment !== undefined || allowedPlacements === allPlacements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;
                            const overflow = await detectOverflow(state, detectOverflowOptions);
                            const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;
                            const currentPlacement = placements[currentIndex];
                            if (currentPlacement == null) {
                                return {};
                            }
                            const {
                                main,
                                cross
                            } = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));

                            // Make `computeCoords` start from the right place.
                            if (placement !== currentPlacement) {
                                return {
                                    reset: {
                                        placement: placements[0]
                                    }
                                };
                            }
                            const currentOverflows = [overflow[getSide(currentPlacement)], overflow[main], overflow[cross]];
                            const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {
                                placement: currentPlacement,
                                overflows: currentOverflows
                            }];
                            const nextPlacement = placements[currentIndex + 1];

                            // There are more placements to check.
                            if (nextPlacement) {
                                return {
                                    data: {
                                        index: currentIndex + 1,
                                        overflows: allOverflows
                                    },
                                    reset: {
                                        placement: nextPlacement
                                    }
                                };
                            }
                            const placementsSortedByMostSpace = allOverflows.map(d => {
                                const alignment = getAlignment(d.placement);
                                return [d.placement, alignment && crossAxis ?
                                    // Check along the mainAxis and main crossAxis side.
                                    d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :
                                    // Check only the mainAxis.
                                    d.overflows[0], d.overflows
                                ];
                            }).sort((a, b) => a[1] - b[1]);
                            const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,
                                // Aligned placements should not check their opposite crossAxis
                                // side.
                                getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));
                            const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];
                            if (resetPlacement !== placement) {
                                return {
                                    data: {
                                        index: currentIndex + 1,
                                        overflows: allOverflows
                                    },
                                    reset: {
                                        placement: resetPlacement
                                    }
                                };
                            }
                            return {};
                        }
                    };
                };

                function getExpandedPlacements(placement) {
                    const oppositePlacement = getOppositePlacement(placement);
                    return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];
                }

                function getSideList(side, isStart, rtl) {
                    const lr = ['left', 'right'];
                    const rl = ['right', 'left'];
                    const tb = ['top', 'bottom'];
                    const bt = ['bottom', 'top'];
                    switch (side) {
                        case 'top':
                        case 'bottom':
                            if (rtl) return isStart ? rl : lr;
                            return isStart ? lr : rl;
                        case 'left':
                        case 'right':
                            return isStart ? tb : bt;
                        default:
                            return [];
                    }
                }

                function getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {
                    const alignment = getAlignment(placement);
                    let list = getSideList(getSide(placement), direction === 'start', rtl);
                    if (alignment) {
                        list = list.map(side => side + "-" + alignment);
                        if (flipAlignment) {
                            list = list.concat(list.map(getOppositeAlignmentPlacement));
                        }
                    }
                    return list;
                }

                /**
                 * Optimizes the visibility of the floating element by flipping the `placement`
                 * in order to keep it in view when the preferred placement(s) will overflow the
                 * clipping boundary. Alternative to `autoPlacement`.
                 * @see https://floating-ui.com/docs/flip
                 */
                const flip = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'flip',
                        options,
                        async fn(state) {
                            var _middlewareData$flip;
                            const {
                                placement,
                                middlewareData,
                                rects,
                                initialPlacement,
                                platform,
                                elements
                            } = state;
                            const {
                                mainAxis: checkMainAxis = true,
                                crossAxis: checkCrossAxis = true,
                                fallbackPlacements: specifiedFallbackPlacements,
                                fallbackStrategy = 'bestFit',
                                fallbackAxisSideDirection = 'none',
                                flipAlignment = true,
                                ...detectOverflowOptions
                            } = options;
                            const side = getSide(placement);
                            const isBasePlacement = getSide(initialPlacement) === initialPlacement;
                            const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));
                            const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));
                            if (!specifiedFallbackPlacements && fallbackAxisSideDirection !== 'none') {
                                fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));
                            }
                            const placements = [initialPlacement, ...fallbackPlacements];
                            const overflow = await detectOverflow(state, detectOverflowOptions);
                            const overflows = [];
                            let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];
                            if (checkMainAxis) {
                                overflows.push(overflow[side]);
                            }
                            if (checkCrossAxis) {
                                const {
                                    main,
                                    cross
                                } = getAlignmentSides(placement, rects, rtl);
                                overflows.push(overflow[main], overflow[cross]);
                            }
                            overflowsData = [...overflowsData, {
                                placement,
                                overflows
                            }];

                            // One or more sides is overflowing.
                            if (!overflows.every(side => side <= 0)) {
                                var _middlewareData$flip2, _overflowsData$filter;
                                const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;
                                const nextPlacement = placements[nextIndex];
                                if (nextPlacement) {
                                    // Try next placement and re-run the lifecycle.
                                    return {
                                        data: {
                                            index: nextIndex,
                                            overflows: overflowsData
                                        },
                                        reset: {
                                            placement: nextPlacement
                                        }
                                    };
                                }

                                // First, find the candidates that fit on the mainAxis side of overflow,
                                // then find the placement that fits the best on the main crossAxis side.
                                let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;

                                // Otherwise fallback.
                                if (!resetPlacement) {
                                    switch (fallbackStrategy) {
                                        case 'bestFit':
                                            {
                                                var _overflowsData$map$so;
                                                const placement = (_overflowsData$map$so = overflowsData.map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$map$so[0];
                                                if (placement) {
                                                    resetPlacement = placement;
                                                }
                                                break;
                                            }
                                        case 'initialPlacement':
                                            resetPlacement = initialPlacement;
                                            break;
                                    }
                                }
                                if (placement !== resetPlacement) {
                                    return {
                                        reset: {
                                            placement: resetPlacement
                                        }
                                    };
                                }
                            }
                            return {};
                        }
                    };
                };

                function getSideOffsets(overflow, rect) {
                    return {
                        top: overflow.top - rect.height,
                        right: overflow.right - rect.width,
                        bottom: overflow.bottom - rect.height,
                        left: overflow.left - rect.width
                    };
                }

                function isAnySideFullyClipped(overflow) {
                    return sides.some(side => overflow[side] >= 0);
                }
                /**
                 * Provides data to hide the floating element in applicable situations, such as
                 * when it is not in the same clipping context as the reference element.
                 * @see https://floating-ui.com/docs/hide
                 */
                const hide = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'hide',
                        options,
                        async fn(state) {
                            const {
                                strategy = 'referenceHidden',
                                    ...detectOverflowOptions
                            } = options;
                            const {
                                rects
                            } = state;
                            switch (strategy) {
                                case 'referenceHidden':
                                    {
                                        const overflow = await detectOverflow(state, {
                                            ...detectOverflowOptions,
                                            elementContext: 'reference'
                                        });
                                        const offsets = getSideOffsets(overflow, rects.reference);
                                        return {
                                            data: {
                                                referenceHiddenOffsets: offsets,
                                                referenceHidden: isAnySideFullyClipped(offsets)
                                            }
                                        };
                                    }
                                case 'escaped':
                                    {
                                        const overflow = await detectOverflow(state, {
                                            ...detectOverflowOptions,
                                            altBoundary: true
                                        });
                                        const offsets = getSideOffsets(overflow, rects.floating);
                                        return {
                                            data: {
                                                escapedOffsets: offsets,
                                                escaped: isAnySideFullyClipped(offsets)
                                            }
                                        };
                                    }
                                default:
                                    {
                                        return {};
                                    }
                            }
                        }
                    };
                };

                function getBoundingRect(rects) {
                    const minX = min(...rects.map(rect => rect.left));
                    const minY = min(...rects.map(rect => rect.top));
                    const maxX = max(...rects.map(rect => rect.right));
                    const maxY = max(...rects.map(rect => rect.bottom));
                    return {
                        x: minX,
                        y: minY,
                        width: maxX - minX,
                        height: maxY - minY
                    };
                }

                function getRectsByLine(rects) {
                    const sortedRects = rects.slice().sort((a, b) => a.y - b.y);
                    const groups = [];
                    let prevRect = null;
                    for (let i = 0; i < sortedRects.length; i++) {
                        const rect = sortedRects[i];
                        if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {
                            groups.push([rect]);
                        } else {
                            groups[groups.length - 1].push(rect);
                        }
                        prevRect = rect;
                    }
                    return groups.map(rect => rectToClientRect(getBoundingRect(rect)));
                }
                /**
                 * Provides improved positioning for inline reference elements that can span
                 * over multiple lines, such as hyperlinks or range selections.
                 * @see https://floating-ui.com/docs/inline
                 */
                const inline = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'inline',
                        options,
                        async fn(state) {
                            const {
                                placement,
                                elements,
                                rects,
                                platform,
                                strategy
                            } = state;
                            // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a
                            // ClientRect's bounds, despite the event listener being triggered. A
                            // padding of 2 seems to handle this issue.
                            const {
                                padding = 2,
                                    x,
                                    y
                            } = options;
                            const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);
                            const clientRects = getRectsByLine(nativeClientRects);
                            const fallback = rectToClientRect(getBoundingRect(nativeClientRects));
                            const paddingObject = getSideObjectFromPadding(padding);

                            function getBoundingClientRect() {
                                // There are two rects and they are disjoined.
                                if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {
                                    // Find the first rect in which the point is fully inside.
                                    return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;
                                }

                                // There are 2 or more connected rects.
                                if (clientRects.length >= 2) {
                                    if (getMainAxisFromPlacement(placement) === 'x') {
                                        const firstRect = clientRects[0];
                                        const lastRect = clientRects[clientRects.length - 1];
                                        const isTop = getSide(placement) === 'top';
                                        const top = firstRect.top;
                                        const bottom = lastRect.bottom;
                                        const left = isTop ? firstRect.left : lastRect.left;
                                        const right = isTop ? firstRect.right : lastRect.right;
                                        const width = right - left;
                                        const height = bottom - top;
                                        return {
                                            top,
                                            bottom,
                                            left,
                                            right,
                                            width,
                                            height,
                                            x: left,
                                            y: top
                                        };
                                    }
                                    const isLeftSide = getSide(placement) === 'left';
                                    const maxRight = max(...clientRects.map(rect => rect.right));
                                    const minLeft = min(...clientRects.map(rect => rect.left));
                                    const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);
                                    const top = measureRects[0].top;
                                    const bottom = measureRects[measureRects.length - 1].bottom;
                                    const left = minLeft;
                                    const right = maxRight;
                                    const width = right - left;
                                    const height = bottom - top;
                                    return {
                                        top,
                                        bottom,
                                        left,
                                        right,
                                        width,
                                        height,
                                        x: left,
                                        y: top
                                    };
                                }
                                return fallback;
                            }
                            const resetRects = await platform.getElementRects({
                                reference: {
                                    getBoundingClientRect
                                },
                                floating: elements.floating,
                                strategy
                            });
                            if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {
                                return {
                                    reset: {
                                        rects: resetRects
                                    }
                                };
                            }
                            return {};
                        }
                    };
                };

                async function convertValueToCoords(state, value) {
                    const {
                        placement,
                        platform,
                        elements
                    } = state;
                    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));
                    const side = getSide(placement);
                    const alignment = getAlignment(placement);
                    const isVertical = getMainAxisFromPlacement(placement) === 'x';
                    const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;
                    const crossAxisMulti = rtl && isVertical ? -1 : 1;
                    const rawValue = typeof value === 'function' ? value(state) : value;

                    // eslint-disable-next-line prefer-const
                    let {
                        mainAxis,
                        crossAxis,
                        alignmentAxis
                    } = typeof rawValue === 'number' ? {
                        mainAxis: rawValue,
                        crossAxis: 0,
                        alignmentAxis: null
                    } : {
                        mainAxis: 0,
                        crossAxis: 0,
                        alignmentAxis: null,
                        ...rawValue
                    };
                    if (alignment && typeof alignmentAxis === 'number') {
                        crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;
                    }
                    return isVertical ? {
                        x: crossAxis * crossAxisMulti,
                        y: mainAxis * mainAxisMulti
                    } : {
                        x: mainAxis * mainAxisMulti,
                        y: crossAxis * crossAxisMulti
                    };
                }

                /**
                 * Modifies the placement by translating the floating element along the
                 * specified axes.
                 * A number (shorthand for `mainAxis` or distance), or an axes configuration
                 * object may be passed.
                 * @see https://floating-ui.com/docs/offset
                 */
                const offset = function(value) {
                    if (value === void 0) {
                        value = 0;
                    }
                    return {
                        name: 'offset',
                        options: value,
                        async fn(state) {
                            const {
                                x,
                                y
                            } = state;
                            const diffCoords = await convertValueToCoords(state, value);
                            return {
                                x: x + diffCoords.x,
                                y: y + diffCoords.y,
                                data: diffCoords
                            };
                        }
                    };
                };

                function getCrossAxis(axis) {
                    return axis === 'x' ? 'y' : 'x';
                }

                /**
                 * Optimizes the visibility of the floating element by shifting it in order to
                 * keep it in view when it will overflow the clipping boundary.
                 * @see https://floating-ui.com/docs/shift
                 */
                const shift = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'shift',
                        options,
                        async fn(state) {
                            const {
                                x,
                                y,
                                placement
                            } = state;
                            const {
                                mainAxis: checkMainAxis = true,
                                crossAxis: checkCrossAxis = false,
                                limiter = {
                                    fn: _ref => {
                                        let {
                                            x,
                                            y
                                        } = _ref;
                                        return {
                                            x,
                                            y
                                        };
                                    }
                                },
                                ...detectOverflowOptions
                            } = options;
                            const coords = {
                                x,
                                y
                            };
                            const overflow = await detectOverflow(state, detectOverflowOptions);
                            const mainAxis = getMainAxisFromPlacement(getSide(placement));
                            const crossAxis = getCrossAxis(mainAxis);
                            let mainAxisCoord = coords[mainAxis];
                            let crossAxisCoord = coords[crossAxis];
                            if (checkMainAxis) {
                                const minSide = mainAxis === 'y' ? 'top' : 'left';
                                const maxSide = mainAxis === 'y' ? 'bottom' : 'right';
                                const min = mainAxisCoord + overflow[minSide];
                                const max = mainAxisCoord - overflow[maxSide];
                                mainAxisCoord = within(min, mainAxisCoord, max);
                            }
                            if (checkCrossAxis) {
                                const minSide = crossAxis === 'y' ? 'top' : 'left';
                                const maxSide = crossAxis === 'y' ? 'bottom' : 'right';
                                const min = crossAxisCoord + overflow[minSide];
                                const max = crossAxisCoord - overflow[maxSide];
                                crossAxisCoord = within(min, crossAxisCoord, max);
                            }
                            const limitedCoords = limiter.fn({
                                ...state,
                                [mainAxis]: mainAxisCoord,
                                [crossAxis]: crossAxisCoord
                            });
                            return {
                                ...limitedCoords,
                                data: {
                                    x: limitedCoords.x - x,
                                    y: limitedCoords.y - y
                                }
                            };
                        }
                    };
                };
                /**
                 * Built-in `limiter` that will stop `shift()` at a certain point.
                 */
                const limitShift = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        options,
                        fn(state) {
                            const {
                                x,
                                y,
                                placement,
                                rects,
                                middlewareData
                            } = state;
                            const {
                                offset = 0,
                                    mainAxis: checkMainAxis = true,
                                    crossAxis: checkCrossAxis = true
                            } = options;
                            const coords = {
                                x,
                                y
                            };
                            const mainAxis = getMainAxisFromPlacement(placement);
                            const crossAxis = getCrossAxis(mainAxis);
                            let mainAxisCoord = coords[mainAxis];
                            let crossAxisCoord = coords[crossAxis];
                            const rawOffset = typeof offset === 'function' ? offset(state) : offset;
                            const computedOffset = typeof rawOffset === 'number' ? {
                                mainAxis: rawOffset,
                                crossAxis: 0
                            } : {
                                mainAxis: 0,
                                crossAxis: 0,
                                ...rawOffset
                            };
                            if (checkMainAxis) {
                                const len = mainAxis === 'y' ? 'height' : 'width';
                                const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;
                                const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;
                                if (mainAxisCoord < limitMin) {
                                    mainAxisCoord = limitMin;
                                } else if (mainAxisCoord > limitMax) {
                                    mainAxisCoord = limitMax;
                                }
                            }
                            if (checkCrossAxis) {
                                var _middlewareData$offse, _middlewareData$offse2;
                                const len = mainAxis === 'y' ? 'width' : 'height';
                                const isOriginSide = ['top', 'left'].includes(getSide(placement));
                                const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);
                                const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);
                                if (crossAxisCoord < limitMin) {
                                    crossAxisCoord = limitMin;
                                } else if (crossAxisCoord > limitMax) {
                                    crossAxisCoord = limitMax;
                                }
                            }
                            return {
                                [mainAxis]: mainAxisCoord,
                                [crossAxis]: crossAxisCoord
                            };
                        }
                    };
                };

                /**
                 * Provides data that allows you to change the size of the floating element —
                 * for instance, prevent it from overflowing the clipping boundary or match the
                 * width of the reference element.
                 * @see https://floating-ui.com/docs/size
                 */
                const size = function(options) {
                    if (options === void 0) {
                        options = {};
                    }
                    return {
                        name: 'size',
                        options,
                        async fn(state) {
                            const {
                                placement,
                                rects,
                                platform,
                                elements
                            } = state;
                            const {
                                apply = () => {},
                                    ...detectOverflowOptions
                            } = options;
                            const overflow = await detectOverflow(state, detectOverflowOptions);
                            const side = getSide(placement);
                            const alignment = getAlignment(placement);
                            const axis = getMainAxisFromPlacement(placement);
                            const isXAxis = axis === 'x';
                            const {
                                width,
                                height
                            } = rects.floating;
                            let heightSide;
                            let widthSide;
                            if (side === 'top' || side === 'bottom') {
                                heightSide = side;
                                widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';
                            } else {
                                widthSide = side;
                                heightSide = alignment === 'end' ? 'top' : 'bottom';
                            }
                            const overflowAvailableHeight = height - overflow[heightSide];
                            const overflowAvailableWidth = width - overflow[widthSide];
                            let availableHeight = overflowAvailableHeight;
                            let availableWidth = overflowAvailableWidth;
                            if (isXAxis) {
                                availableWidth = min(
                                    // Maximum clipping viewport width
                                    width - overflow.right - overflow.left, overflowAvailableWidth);
                            } else {
                                availableHeight = min(
                                    // Maximum clipping viewport height
                                    height - overflow.bottom - overflow.top, overflowAvailableHeight);
                            }
                            if (!state.middlewareData.shift && !alignment) {
                                const xMin = max(overflow.left, 0);
                                const xMax = max(overflow.right, 0);
                                const yMin = max(overflow.top, 0);
                                const yMax = max(overflow.bottom, 0);
                                if (isXAxis) {
                                    availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));
                                } else {
                                    availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));
                                }
                            }
                            await apply({
                                ...state,
                                availableWidth,
                                availableHeight
                            });
                            const nextDimensions = await platform.getDimensions(elements.floating);
                            if (width !== nextDimensions.width || height !== nextDimensions.height) {
                                return {
                                    reset: {
                                        rects: true
                                    }
                                };
                            }
                            return {};
                        }
                    };
                };




                /***/
            }),

        /***/
        "../../node_modules/@floating-ui/dom/dist/floating-ui.dom.browser.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    arrow: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.arrow),
                    /* harmony export */
                    autoPlacement: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.autoPlacement),
                    /* harmony export */
                    autoUpdate: () => ( /* binding */ autoUpdate),
                    /* harmony export */
                    computePosition: () => ( /* binding */ computePosition),
                    /* harmony export */
                    detectOverflow: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.detectOverflow),
                    /* harmony export */
                    flip: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.flip),
                    /* harmony export */
                    getOverflowAncestors: () => ( /* binding */ getOverflowAncestors),
                    /* harmony export */
                    hide: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.hide),
                    /* harmony export */
                    inline: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.inline),
                    /* harmony export */
                    limitShift: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.limitShift),
                    /* harmony export */
                    offset: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.offset),
                    /* harmony export */
                    platform: () => ( /* binding */ platform),
                    /* harmony export */
                    shift: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.shift),
                    /* harmony export */
                    size: () => ( /* reexport safe */ _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.size)
                    /* harmony export */
                });
                /* harmony import */
                var _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@floating-ui/core/dist/floating-ui.core.browser.mjs");



                function getWindow(node) {
                    var _node$ownerDocument;
                    return ((_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;
                }

                function getComputedStyle$1(element) {
                    return getWindow(element).getComputedStyle(element);
                }

                function isNode(value) {
                    return value instanceof getWindow(value).Node;
                }

                function getNodeName(node) {
                    return isNode(node) ? (node.nodeName || '').toLowerCase() : '';
                }

                let uaString;

                function getUAString() {
                    if (uaString) {
                        return uaString;
                    }
                    const uaData = navigator.userAgentData;
                    if (uaData && Array.isArray(uaData.brands)) {
                        uaString = uaData.brands.map(item => item.brand + "/" + item.version).join(' ');
                        return uaString;
                    }
                    return navigator.userAgent;
                }

                function isHTMLElement(value) {
                    return value instanceof getWindow(value).HTMLElement;
                }

                function isElement(value) {
                    return value instanceof getWindow(value).Element;
                }

                function isShadowRoot(node) {
                    // Browsers without `ShadowRoot` support.
                    if (typeof ShadowRoot === 'undefined') {
                        return false;
                    }
                    const OwnElement = getWindow(node).ShadowRoot;
                    return node instanceof OwnElement || node instanceof ShadowRoot;
                }

                function isOverflowElement(element) {
                    const {
                        overflow,
                        overflowX,
                        overflowY,
                        display
                    } = getComputedStyle$1(element);
                    return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);
                }

                function isTableElement(element) {
                    return ['table', 'td', 'th'].includes(getNodeName(element));
                }

                function isContainingBlock(element) {
                    // TODO: Try to use feature detection here instead.
                    const isFirefox = /firefox/i.test(getUAString());
                    const css = getComputedStyle$1(element);
                    const backdropFilter = css.backdropFilter || css.WebkitBackdropFilter;

                    // This is non-exhaustive but covers the most common CSS properties that
                    // create a containing block.
                    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block
                    return css.transform !== 'none' || css.perspective !== 'none' || (backdropFilter ? backdropFilter !== 'none' : false) || isFirefox && css.willChange === 'filter' || isFirefox && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective'].some(value => css.willChange.includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => {
                        // Add type check for old browsers.
                        const contain = css.contain;
                        return contain != null ? contain.includes(value) : false;
                    });
                }

                /**
                 * Determines whether or not `.getBoundingClientRect()` is affected by visual
                 * viewport offsets. In Safari, the `x`/`y` offsets are values relative to the
                 * visual viewport, while in other engines, they are values relative to the
                 * layout viewport.
                 */
                function isClientRectVisualViewportBased() {
                    // TODO: Try to use feature detection here instead. Feature detection for
                    // this can fail in various ways, making the userAgent check the most
                    // reliable:
                    // • Always-visible scrollbar or not
                    // • Width of <html>

                    // Is Safari.
                    return /^((?!chrome|android).)*safari/i.test(getUAString());
                }

                function isLastTraversableNode(node) {
                    return ['html', 'body', '#document'].includes(getNodeName(node));
                }

                const min = Math.min;
                const max = Math.max;
                const round = Math.round;

                function getCssDimensions(element) {
                    const css = getComputedStyle$1(element);
                    let width = parseFloat(css.width);
                    let height = parseFloat(css.height);
                    const hasOffset = isHTMLElement(element);
                    const offsetWidth = hasOffset ? element.offsetWidth : width;
                    const offsetHeight = hasOffset ? element.offsetHeight : height;
                    const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;
                    if (shouldFallback) {
                        width = offsetWidth;
                        height = offsetHeight;
                    }
                    return {
                        width,
                        height,
                        fallback: shouldFallback
                    };
                }

                function unwrapElement(element) {
                    return !isElement(element) ? element.contextElement : element;
                }

                const FALLBACK_SCALE = {
                    x: 1,
                    y: 1
                };

                function getScale(element) {
                    const domElement = unwrapElement(element);
                    if (!isHTMLElement(domElement)) {
                        return FALLBACK_SCALE;
                    }
                    const rect = domElement.getBoundingClientRect();
                    const {
                        width,
                        height,
                        fallback
                    } = getCssDimensions(domElement);
                    let x = (fallback ? round(rect.width) : rect.width) / width;
                    let y = (fallback ? round(rect.height) : rect.height) / height;

                    // 0, NaN, or Infinity should always fallback to 1.

                    if (!x || !Number.isFinite(x)) {
                        x = 1;
                    }
                    if (!y || !Number.isFinite(y)) {
                        y = 1;
                    }
                    return {
                        x,
                        y
                    };
                }

                function getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {
                    var _win$visualViewport, _win$visualViewport2;
                    if (includeScale === void 0) {
                        includeScale = false;
                    }
                    if (isFixedStrategy === void 0) {
                        isFixedStrategy = false;
                    }
                    const clientRect = element.getBoundingClientRect();
                    const domElement = unwrapElement(element);
                    let scale = FALLBACK_SCALE;
                    if (includeScale) {
                        if (offsetParent) {
                            if (isElement(offsetParent)) {
                                scale = getScale(offsetParent);
                            }
                        } else {
                            scale = getScale(element);
                        }
                    }
                    const win = domElement ? getWindow(domElement) : window;
                    const addVisualOffsets = isClientRectVisualViewportBased() && isFixedStrategy;
                    let x = (clientRect.left + (addVisualOffsets ? ((_win$visualViewport = win.visualViewport) == null ? void 0 : _win$visualViewport.offsetLeft) || 0 : 0)) / scale.x;
                    let y = (clientRect.top + (addVisualOffsets ? ((_win$visualViewport2 = win.visualViewport) == null ? void 0 : _win$visualViewport2.offsetTop) || 0 : 0)) / scale.y;
                    let width = clientRect.width / scale.x;
                    let height = clientRect.height / scale.y;
                    if (domElement) {
                        const win = getWindow(domElement);
                        const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;
                        let currentIFrame = win.frameElement;
                        while (currentIFrame && offsetParent && offsetWin !== win) {
                            const iframeScale = getScale(currentIFrame);
                            const iframeRect = currentIFrame.getBoundingClientRect();
                            const css = getComputedStyle(currentIFrame);
                            iframeRect.x += (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;
                            iframeRect.y += (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;
                            x *= iframeScale.x;
                            y *= iframeScale.y;
                            width *= iframeScale.x;
                            height *= iframeScale.y;
                            x += iframeRect.x;
                            y += iframeRect.y;
                            currentIFrame = getWindow(currentIFrame).frameElement;
                        }
                    }
                    return (0, _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)({
                        width,
                        height,
                        x,
                        y
                    });
                }

                function getDocumentElement(node) {
                    return ((isNode(node) ? node.ownerDocument : node.document) || window.document).documentElement;
                }

                function getNodeScroll(element) {
                    if (isElement(element)) {
                        return {
                            scrollLeft: element.scrollLeft,
                            scrollTop: element.scrollTop
                        };
                    }
                    return {
                        scrollLeft: element.pageXOffset,
                        scrollTop: element.pageYOffset
                    };
                }

                function convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {
                    let {
                        rect,
                        offsetParent,
                        strategy
                    } = _ref;
                    const isOffsetParentAnElement = isHTMLElement(offsetParent);
                    const documentElement = getDocumentElement(offsetParent);
                    if (offsetParent === documentElement) {
                        return rect;
                    }
                    let scroll = {
                        scrollLeft: 0,
                        scrollTop: 0
                    };
                    let scale = {
                        x: 1,
                        y: 1
                    };
                    const offsets = {
                        x: 0,
                        y: 0
                    };
                    if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {
                        if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {
                            scroll = getNodeScroll(offsetParent);
                        }
                        if (isHTMLElement(offsetParent)) {
                            const offsetRect = getBoundingClientRect(offsetParent);
                            scale = getScale(offsetParent);
                            offsets.x = offsetRect.x + offsetParent.clientLeft;
                            offsets.y = offsetRect.y + offsetParent.clientTop;
                        }
                    }
                    return {
                        width: rect.width * scale.x,
                        height: rect.height * scale.y,
                        x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,
                        y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y
                    };
                }

                function getWindowScrollBarX(element) {
                    // If <html> has a CSS width greater than the viewport, then this will be
                    // incorrect for RTL.
                    return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;
                }

                // Gets the entire size of the scrollable document area, even extending outside
                // of the `<html>` and `<body>` rect bounds if horizontally scrollable.
                function getDocumentRect(element) {
                    const html = getDocumentElement(element);
                    const scroll = getNodeScroll(element);
                    const body = element.ownerDocument.body;
                    const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);
                    const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);
                    let x = -scroll.scrollLeft + getWindowScrollBarX(element);
                    const y = -scroll.scrollTop;
                    if (getComputedStyle$1(body).direction === 'rtl') {
                        x += max(html.clientWidth, body.clientWidth) - width;
                    }
                    return {
                        width,
                        height,
                        x,
                        y
                    };
                }

                function getParentNode(node) {
                    if (getNodeName(node) === 'html') {
                        return node;
                    }
                    const result =
                        // Step into the shadow DOM of the parent of a slotted node.
                        node.assignedSlot ||
                        // DOM Element detected.
                        node.parentNode ||
                        // ShadowRoot detected.
                        isShadowRoot(node) && node.host ||
                        // Fallback.
                        getDocumentElement(node);
                    return isShadowRoot(result) ? result.host : result;
                }

                function getNearestOverflowAncestor(node) {
                    const parentNode = getParentNode(node);
                    if (isLastTraversableNode(parentNode)) {
                        // `getParentNode` will never return a `Document` due to the fallback
                        // check, so it's either the <html> or <body> element.
                        return parentNode.ownerDocument.body;
                    }
                    if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {
                        return parentNode;
                    }
                    return getNearestOverflowAncestor(parentNode);
                }

                function getOverflowAncestors(node, list) {
                    var _node$ownerDocument;
                    if (list === void 0) {
                        list = [];
                    }
                    const scrollableAncestor = getNearestOverflowAncestor(node);
                    const isBody = scrollableAncestor === ((_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.body);
                    const win = getWindow(scrollableAncestor);
                    if (isBody) {
                        return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : []);
                    }
                    return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor));
                }

                function getViewportRect(element, strategy) {
                    const win = getWindow(element);
                    const html = getDocumentElement(element);
                    const visualViewport = win.visualViewport;
                    let width = html.clientWidth;
                    let height = html.clientHeight;
                    let x = 0;
                    let y = 0;
                    if (visualViewport) {
                        width = visualViewport.width;
                        height = visualViewport.height;
                        const visualViewportBased = isClientRectVisualViewportBased();
                        if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {
                            x = visualViewport.offsetLeft;
                            y = visualViewport.offsetTop;
                        }
                    }
                    return {
                        width,
                        height,
                        x,
                        y
                    };
                }

                // Returns the inner client rect, subtracting scrollbars if present.
                function getInnerBoundingClientRect(element, strategy) {
                    const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');
                    const top = clientRect.top + element.clientTop;
                    const left = clientRect.left + element.clientLeft;
                    const scale = isHTMLElement(element) ? getScale(element) : {
                        x: 1,
                        y: 1
                    };
                    const width = element.clientWidth * scale.x;
                    const height = element.clientHeight * scale.y;
                    const x = left * scale.x;
                    const y = top * scale.y;
                    return {
                        width,
                        height,
                        x,
                        y
                    };
                }

                function getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {
                    let rect;
                    if (clippingAncestor === 'viewport') {
                        rect = getViewportRect(element, strategy);
                    } else if (clippingAncestor === 'document') {
                        rect = getDocumentRect(getDocumentElement(element));
                    } else if (isElement(clippingAncestor)) {
                        rect = getInnerBoundingClientRect(clippingAncestor, strategy);
                    } else {
                        const mutableRect = {
                            ...clippingAncestor
                        };
                        if (isClientRectVisualViewportBased()) {
                            var _win$visualViewport, _win$visualViewport2;
                            const win = getWindow(element);
                            mutableRect.x -= ((_win$visualViewport = win.visualViewport) == null ? void 0 : _win$visualViewport.offsetLeft) || 0;
                            mutableRect.y -= ((_win$visualViewport2 = win.visualViewport) == null ? void 0 : _win$visualViewport2.offsetTop) || 0;
                        }
                        rect = mutableRect;
                    }
                    return (0, _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(rect);
                }

                // A "clipping ancestor" is an `overflow` element with the characteristic of
                // clipping (or hiding) child elements. This returns all clipping ancestors
                // of the given element up the tree.
                function getClippingElementAncestors(element, cache) {
                    const cachedResult = cache.get(element);
                    if (cachedResult) {
                        return cachedResult;
                    }
                    let result = getOverflowAncestors(element).filter(el => isElement(el) && getNodeName(el) !== 'body');
                    let currentContainingBlockComputedStyle = null;
                    const elementIsFixed = getComputedStyle$1(element).position === 'fixed';
                    let currentNode = elementIsFixed ? getParentNode(element) : element;

                    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block
                    while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {
                        const computedStyle = getComputedStyle$1(currentNode);
                        const containingBlock = isContainingBlock(currentNode);
                        const shouldIgnoreCurrentNode = computedStyle.position === 'fixed';
                        if (shouldIgnoreCurrentNode) {
                            currentContainingBlockComputedStyle = null;
                        } else {
                            const shouldDropCurrentNode = elementIsFixed ? !containingBlock && !currentContainingBlockComputedStyle : !containingBlock && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position);
                            if (shouldDropCurrentNode) {
                                // Drop non-containing blocks.
                                result = result.filter(ancestor => ancestor !== currentNode);
                            } else {
                                // Record last containing block for next iteration.
                                currentContainingBlockComputedStyle = computedStyle;
                            }
                        }
                        currentNode = getParentNode(currentNode);
                    }
                    cache.set(element, result);
                    return result;
                }

                // Gets the maximum area that the element is visible in due to any number of
                // clipping ancestors.
                function getClippingRect(_ref) {
                    let {
                        element,
                        boundary,
                        rootBoundary,
                        strategy
                    } = _ref;
                    const elementClippingAncestors = boundary === 'clippingAncestors' ? getClippingElementAncestors(element, this._c) : [].concat(boundary);
                    const clippingAncestors = [...elementClippingAncestors, rootBoundary];
                    const firstClippingAncestor = clippingAncestors[0];
                    const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {
                        const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);
                        accRect.top = max(rect.top, accRect.top);
                        accRect.right = min(rect.right, accRect.right);
                        accRect.bottom = min(rect.bottom, accRect.bottom);
                        accRect.left = max(rect.left, accRect.left);
                        return accRect;
                    }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));
                    return {
                        width: clippingRect.right - clippingRect.left,
                        height: clippingRect.bottom - clippingRect.top,
                        x: clippingRect.left,
                        y: clippingRect.top
                    };
                }

                function getDimensions(element) {
                    return getCssDimensions(element);
                }

                function getTrueOffsetParent(element, polyfill) {
                    if (!isHTMLElement(element) || getComputedStyle$1(element).position === 'fixed') {
                        return null;
                    }
                    if (polyfill) {
                        return polyfill(element);
                    }
                    return element.offsetParent;
                }

                function getContainingBlock(element) {
                    let currentNode = getParentNode(element);
                    while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {
                        if (isContainingBlock(currentNode)) {
                            return currentNode;
                        } else {
                            currentNode = getParentNode(currentNode);
                        }
                    }
                    return null;
                }

                // Gets the closest ancestor positioned element. Handles some edge cases,
                // such as table ancestors and cross browser bugs.
                function getOffsetParent(element, polyfill) {
                    const window = getWindow(element);
                    if (!isHTMLElement(element)) {
                        return window;
                    }
                    let offsetParent = getTrueOffsetParent(element, polyfill);
                    while (offsetParent && isTableElement(offsetParent) && getComputedStyle$1(offsetParent).position === 'static') {
                        offsetParent = getTrueOffsetParent(offsetParent, polyfill);
                    }
                    if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle$1(offsetParent).position === 'static' && !isContainingBlock(offsetParent))) {
                        return window;
                    }
                    return offsetParent || getContainingBlock(element) || window;
                }

                function getRectRelativeToOffsetParent(element, offsetParent, strategy) {
                    const isOffsetParentAnElement = isHTMLElement(offsetParent);
                    const documentElement = getDocumentElement(offsetParent);
                    const rect = getBoundingClientRect(element, true, strategy === 'fixed', offsetParent);
                    let scroll = {
                        scrollLeft: 0,
                        scrollTop: 0
                    };
                    const offsets = {
                        x: 0,
                        y: 0
                    };
                    if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {
                        if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {
                            scroll = getNodeScroll(offsetParent);
                        }
                        if (isHTMLElement(offsetParent)) {
                            const offsetRect = getBoundingClientRect(offsetParent, true);
                            offsets.x = offsetRect.x + offsetParent.clientLeft;
                            offsets.y = offsetRect.y + offsetParent.clientTop;
                        } else if (documentElement) {
                            offsets.x = getWindowScrollBarX(documentElement);
                        }
                    }
                    return {
                        x: rect.left + scroll.scrollLeft - offsets.x,
                        y: rect.top + scroll.scrollTop - offsets.y,
                        width: rect.width,
                        height: rect.height
                    };
                }

                const platform = {
                    getClippingRect,
                    convertOffsetParentRelativeRectToViewportRelativeRect,
                    isElement,
                    getDimensions,
                    getOffsetParent,
                    getDocumentElement,
                    getScale,
                    async getElementRects(_ref) {
                        let {
                            reference,
                            floating,
                            strategy
                        } = _ref;
                        const getOffsetParentFn = this.getOffsetParent || getOffsetParent;
                        const getDimensionsFn = this.getDimensions;
                        return {
                            reference: getRectRelativeToOffsetParent(reference, await getOffsetParentFn(floating), strategy),
                            floating: {
                                x: 0,
                                y: 0,
                                ...(await getDimensionsFn(floating))
                            }
                        };
                    },
                    getClientRects: element => Array.from(element.getClientRects()),
                    isRTL: element => getComputedStyle$1(element).direction === 'rtl'
                };

                /**
                 * Automatically updates the position of the floating element when necessary.
                 * Should only be called when the floating element is mounted on the DOM or
                 * visible on the screen.
                 * @returns cleanup function that should be invoked when the floating element is
                 * removed from the DOM or hidden from the screen.
                 * @see https://floating-ui.com/docs/autoUpdate
                 */
                function autoUpdate(reference, floating, update, options) {
                    if (options === void 0) {
                        options = {};
                    }
                    const {
                        ancestorScroll: _ancestorScroll = true,
                        ancestorResize = true,
                        elementResize = true,
                        animationFrame = false
                    } = options;
                    const ancestorScroll = _ancestorScroll && !animationFrame;
                    const ancestors = ancestorScroll || ancestorResize ? [...(isElement(reference) ? getOverflowAncestors(reference) : reference.contextElement ? getOverflowAncestors(reference.contextElement) : []), ...getOverflowAncestors(floating)] : [];
                    ancestors.forEach(ancestor => {
                        ancestorScroll && ancestor.addEventListener('scroll', update, {
                            passive: true
                        });
                        ancestorResize && ancestor.addEventListener('resize', update);
                    });
                    let observer = null;
                    if (elementResize) {
                        let initialUpdate = true;
                        observer = new ResizeObserver(() => {
                            if (!initialUpdate) {
                                update();
                            }
                            initialUpdate = false;
                        });
                        isElement(reference) && !animationFrame && observer.observe(reference);
                        if (!isElement(reference) && reference.contextElement && !animationFrame) {
                            observer.observe(reference.contextElement);
                        }
                        observer.observe(floating);
                    }
                    let frameId;
                    let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;
                    if (animationFrame) {
                        frameLoop();
                    }

                    function frameLoop() {
                        const nextRefRect = getBoundingClientRect(reference);
                        if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {
                            update();
                        }
                        prevRefRect = nextRefRect;
                        frameId = requestAnimationFrame(frameLoop);
                    }
                    update();
                    return () => {
                        var _observer;
                        ancestors.forEach(ancestor => {
                            ancestorScroll && ancestor.removeEventListener('scroll', update);
                            ancestorResize && ancestor.removeEventListener('resize', update);
                        });
                        (_observer = observer) == null ? void 0 : _observer.disconnect();
                        observer = null;
                        if (animationFrame) {
                            cancelAnimationFrame(frameId);
                        }
                    };
                }

                /**
                 * Computes the `x` and `y` coordinates that will place the floating element
                 * next to a reference element when it is given a certain CSS positioning
                 * strategy.
                 */
                const computePosition = (reference, floating, options) => {
                    // This caches the expensive `getClippingElementAncestors` function so that
                    // multiple lifecycle resets re-use the same result. It only lives for a
                    // single call. If other functions become expensive, we can add them as well.
                    const cache = new Map();
                    const mergedOptions = {
                        platform,
                        ...options
                    };
                    const platformWithCache = {
                        ...mergedOptions.platform,
                        _c: cache
                    };
                    return (0, _floating_ui_core__WEBPACK_IMPORTED_MODULE_0__.computePosition)(reference, floating, {
                        ...mergedOptions,
                        platform: platformWithCache
                    });
                };




                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_babel_runtime_helpers_esm_classCallCheck_js-node_modules_babel_runtime_h-6f4ce5.6151a6173eae8f4e.js.map