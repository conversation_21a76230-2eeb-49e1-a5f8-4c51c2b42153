/*! For license information please see 6492.f37308b43f9318ca.js.LICENSE.txt */
(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [6492, 5145], {
        4439: e => {
            var t = Object.getOwnPropertySymbols,
                r = Object.prototype.hasOwnProperty,
                n = Object.prototype.propertyIsEnumerable;
            e.exports = function() {
                try {
                    if (!Object.assign) return !1;
                    var e = new String("abc");
                    if (e[5] = "de", "5" === Object.getOwnPropertyNames(e)[0]) return !1;
                    for (var t = {}, r = 0; r < 10; r++) t["_" + String.fromCharCode(r)] = r;
                    if ("**********" !== Object.getOwnPropertyNames(t).map((function(e) {
                            return t[e]
                        })).join("")) return !1;
                    var n = {};
                    return "abcdefghijklmnopqrst".split("").forEach((function(e) {
                        n[e] = e
                    })), "abcdefghijklmnopqrst" === Object.keys(Object.assign({}, n)).join("")
                } catch (e) {
                    return !1
                }
            }() ? Object.assign : function(e, o) {
                for (var i, a, u = function(e) {
                        if (null == e) throw new TypeError("Object.assign cannot be called with null or undefined");
                        return Object(e)
                    }(e), c = 1; c < arguments.length; c++) {
                    for (var s in i = Object(arguments[c])) r.call(i, s) && (u[s] = i[s]);
                    if (t) {
                        a = t(i);
                        for (var f = 0; f < a.length; f++) n.call(i, a[f]) && (u[a[f]] = i[a[f]])
                    }
                }
                return u
            }
        },
        45145: e => {
            var t = "undefined" != typeof Element,
                r = "function" == typeof Map,
                n = "function" == typeof Set,
                o = "function" == typeof ArrayBuffer && !!ArrayBuffer.isView;

            function i(e, a) {
                if (e === a) return !0;
                if (e && a && "object" == typeof e && "object" == typeof a) {
                    if (e.constructor !== a.constructor) return !1;
                    var u, c, s, f;
                    if (Array.isArray(e)) {
                        if ((u = e.length) != a.length) return !1;
                        for (c = u; 0 != c--;)
                            if (!i(e[c], a[c])) return !1;
                        return !0
                    }
                    if (r && e instanceof Map && a instanceof Map) {
                        if (e.size !== a.size) return !1;
                        for (f = e.entries(); !(c = f.next()).done;)
                            if (!a.has(c.value[0])) return !1;
                        for (f = e.entries(); !(c = f.next()).done;)
                            if (!i(c.value[1], a.get(c.value[0]))) return !1;
                        return !0
                    }
                    if (n && e instanceof Set && a instanceof Set) {
                        if (e.size !== a.size) return !1;
                        for (f = e.entries(); !(c = f.next()).done;)
                            if (!a.has(c.value[0])) return !1;
                        return !0
                    }
                    if (o && ArrayBuffer.isView(e) && ArrayBuffer.isView(a)) {
                        if ((u = e.length) != a.length) return !1;
                        for (c = u; 0 != c--;)
                            if (e[c] !== a[c]) return !1;
                        return !0
                    }
                    if (e.constructor === RegExp) return e.source === a.source && e.flags === a.flags;
                    if (e.valueOf !== Object.prototype.valueOf && "function" == typeof e.valueOf && "function" == typeof a.valueOf) return e.valueOf() === a.valueOf();
                    if (e.toString !== Object.prototype.toString && "function" == typeof e.toString && "function" == typeof a.toString) return e.toString() === a.toString();
                    if ((u = (s = Object.keys(e)).length) !== Object.keys(a).length) return !1;
                    for (c = u; 0 != c--;)
                        if (!Object.prototype.hasOwnProperty.call(a, s[c])) return !1;
                    if (t && e instanceof Element) return !1;
                    for (c = u; 0 != c--;)
                        if (("_owner" !== s[c] && "__v" !== s[c] && "__o" !== s[c] || !e.$$typeof) && !i(e[s[c]], a[s[c]])) return !1;
                    return !0
                }
                return e != e && a != a
            }
            e.exports = function(e, t) {
                try {
                    return i(e, t)
                } catch (e) {
                    if ((e.message || "").match(/stack|recursion/i)) return console.warn("react-fast-compare cannot handle circular refs"), !1;
                    throw e
                }
            }
        },
        86492: (e, t, r) => {
            function n(e) {
                return e && "object" == typeof e && "default" in e ? e.default : e
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var o, i, a, u, c = n(r(61605)),
                s = n(r(47525)),
                f = n(r(45145)),
                l = n(r(4582)),
                p = n(r(4439)),
                d = "bodyAttributes",
                y = "htmlAttributes",
                h = {
                    BASE: "base",
                    BODY: "body",
                    HEAD: "head",
                    HTML: "html",
                    LINK: "link",
                    META: "meta",
                    NOSCRIPT: "noscript",
                    SCRIPT: "script",
                    STYLE: "style",
                    TITLE: "title"
                },
                b = (Object.keys(h).map((function(e) {
                    return h[e]
                })), "charset"),
                m = "cssText",
                T = "href",
                g = "innerHTML",
                v = "itemprop",
                w = "rel",
                O = {
                    accesskey: "accessKey",
                    charset: "charSet",
                    class: "className",
                    contenteditable: "contentEditable",
                    contextmenu: "contextMenu",
                    "http-equiv": "httpEquiv",
                    itemprop: "itemProp",
                    tabindex: "tabIndex"
                },
                A = Object.keys(O).reduce((function(e, t) {
                    return e[O[t]] = t, e
                }), {}),
                C = [h.NOSCRIPT, h.SCRIPT, h.STYLE],
                S = "data-react-helmet",
                E = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                },
                j = function() {
                    function e(e, t) {
                        for (var r = 0; r < t.length; r++) {
                            var n = t[r];
                            n.enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, n.key, n)
                        }
                    }
                    return function(t, r, n) {
                        return r && e(t.prototype, r), n && e(t, n), t
                    }
                }(),
                P = Object.assign || function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var r = arguments[t];
                        for (var n in r) Object.prototype.hasOwnProperty.call(r, n) && (e[n] = r[n])
                    }
                    return e
                },
                k = function(e, t) {
                    var r = {};
                    for (var n in e) t.indexOf(n) >= 0 || Object.prototype.hasOwnProperty.call(e, n) && (r[n] = e[n]);
                    return r
                },
                L = function(e) {
                    return !1 === (!(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1]) ? String(e) : String(e).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#x27;")
                },
                x = function(e) {
                    var t = _(e, h.TITLE),
                        r = _(e, "titleTemplate");
                    if (r && t) return r.replace(/%s/g, (function() {
                        return Array.isArray(t) ? t.join("") : t
                    }));
                    var n = _(e, "defaultTitle");
                    return t || n || void 0
                },
                I = function(e) {
                    return _(e, "onChangeClientState") || function() {}
                },
                M = function(e, t) {
                    return t.filter((function(t) {
                        return void 0 !== t[e]
                    })).map((function(t) {
                        return t[e]
                    })).reduce((function(e, t) {
                        return P({}, e, t)
                    }), {})
                },
                N = function(e, t) {
                    return t.filter((function(e) {
                        return void 0 !== e[h.BASE]
                    })).map((function(e) {
                        return e[h.BASE]
                    })).reverse().reduce((function(t, r) {
                        if (!t.length)
                            for (var n = Object.keys(r), o = 0; o < n.length; o++) {
                                var i = n[o].toLowerCase();
                                if (-1 !== e.indexOf(i) && r[i]) return t.concat(r)
                            }
                        return t
                    }), [])
                },
                R = function(e, t, r) {
                    var n = {};
                    return r.filter((function(t) {
                        return !!Array.isArray(t[e]) || (void 0 !== t[e] && Y("Helmet: " + e + ' should be of type "Array". Instead found type "' + E(t[e]) + '"'), !1)
                    })).map((function(t) {
                        return t[e]
                    })).reverse().reduce((function(e, r) {
                        var o = {};
                        r.filter((function(e) {
                            for (var r = void 0, i = Object.keys(e), a = 0; a < i.length; a++) {
                                var u = i[a],
                                    c = u.toLowerCase(); - 1 === t.indexOf(c) || r === w && "canonical" === e[r].toLowerCase() || c === w && "stylesheet" === e[c].toLowerCase() || (r = c), -1 === t.indexOf(u) || u !== g && u !== m && u !== v || (r = u)
                            }
                            if (!r || !e[r]) return !1;
                            var s = e[r].toLowerCase();
                            return n[r] || (n[r] = {}), o[r] || (o[r] = {}), !n[r][s] && (o[r][s] = !0, !0)
                        })).reverse().forEach((function(t) {
                            return e.push(t)
                        }));
                        for (var i = Object.keys(o), a = 0; a < i.length; a++) {
                            var u = i[a],
                                c = p({}, n[u], o[u]);
                            n[u] = c
                        }
                        return e
                    }), []).reverse()
                },
                _ = function(e, t) {
                    for (var r = e.length - 1; r >= 0; r--) {
                        var n = e[r];
                        if (n.hasOwnProperty(t)) return n[t]
                    }
                    return null
                },
                q = (o = Date.now(), function(e) {
                    var t = Date.now();
                    t - o > 16 ? (o = t, e(t)) : setTimeout((function() {
                        q(e)
                    }), 0)
                }),
                H = function(e) {
                    return clearTimeout(e)
                },
                B = "undefined" != typeof window ? window.requestAnimationFrame && window.requestAnimationFrame.bind(window) || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || q : r.g.requestAnimationFrame || q,
                D = "undefined" != typeof window ? window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || H : r.g.cancelAnimationFrame || H,
                Y = function(e) {
                    return console && "function" == typeof console.warn && console.warn(e)
                },
                F = null,
                U = function(e, t) {
                    var r = e.baseTag,
                        n = e.bodyAttributes,
                        o = e.htmlAttributes,
                        i = e.linkTags,
                        a = e.metaTags,
                        u = e.noscriptTags,
                        c = e.onChangeClientState,
                        s = e.scriptTags,
                        f = e.styleTags,
                        l = e.title,
                        p = e.titleAttributes;
                    V(h.BODY, n), V(h.HTML, o), K(l, p);
                    var d = {
                            baseTag: W(h.BASE, r),
                            linkTags: W(h.LINK, i),
                            metaTags: W(h.META, a),
                            noscriptTags: W(h.NOSCRIPT, u),
                            scriptTags: W(h.SCRIPT, s),
                            styleTags: W(h.STYLE, f)
                        },
                        y = {},
                        b = {};
                    Object.keys(d).forEach((function(e) {
                        var t = d[e],
                            r = t.newTags,
                            n = t.oldTags;
                        r.length && (y[e] = r), n.length && (b[e] = d[e].oldTags)
                    })), t && t(), c(e, y, b)
                },
                z = function(e) {
                    return Array.isArray(e) ? e.join("") : e
                },
                K = function(e, t) {
                    void 0 !== e && document.title !== e && (document.title = z(e)), V(h.TITLE, t)
                },
                V = function(e, t) {
                    var r = document.getElementsByTagName(e)[0];
                    if (r) {
                        for (var n = r.getAttribute(S), o = n ? n.split(",") : [], i = [].concat(o), a = Object.keys(t), u = 0; u < a.length; u++) {
                            var c = a[u],
                                s = t[c] || "";
                            r.getAttribute(c) !== s && r.setAttribute(c, s), -1 === o.indexOf(c) && o.push(c);
                            var f = i.indexOf(c); - 1 !== f && i.splice(f, 1)
                        }
                        for (var l = i.length - 1; l >= 0; l--) r.removeAttribute(i[l]);
                        o.length === i.length ? r.removeAttribute(S) : r.getAttribute(S) !== a.join(",") && r.setAttribute(S, a.join(","))
                    }
                },
                W = function(e, t) {
                    var r = document.head || document.querySelector(h.HEAD),
                        n = r.querySelectorAll(e + "[" + S + "]"),
                        o = Array.prototype.slice.call(n),
                        i = [],
                        a = void 0;
                    return t && t.length && t.forEach((function(t) {
                        var r = document.createElement(e);
                        for (var n in t)
                            if (t.hasOwnProperty(n))
                                if (n === g) r.innerHTML = t.innerHTML;
                                else if (n === m) r.styleSheet ? r.styleSheet.cssText = t.cssText : r.appendChild(document.createTextNode(t.cssText));
                        else {
                            var u = void 0 === t[n] ? "" : t[n];
                            r.setAttribute(n, u)
                        }
                        r.setAttribute(S, "true"), o.some((function(e, t) {
                            return a = t, r.isEqualNode(e)
                        })) ? o.splice(a, 1) : i.push(r)
                    })), o.forEach((function(e) {
                        return e.parentNode.removeChild(e)
                    })), i.forEach((function(e) {
                        return r.appendChild(e)
                    })), {
                        oldTags: o,
                        newTags: i
                    }
                },
                $ = function(e) {
                    return Object.keys(e).reduce((function(t, r) {
                        var n = void 0 !== e[r] ? r + '="' + e[r] + '"' : "" + r;
                        return t ? t + " " + n : n
                    }), "")
                },
                G = function(e) {
                    var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                    return Object.keys(e).reduce((function(t, r) {
                        return t[O[r] || r] = e[r], t
                    }), t)
                },
                J = function(e, t, r) {
                    switch (e) {
                        case h.TITLE:
                            return {
                                toComponent: function() {
                                    return e = t.title, r = t.titleAttributes, (n = {
                                        key: e
                                    })[S] = !0, o = G(r, n), [l.createElement(h.TITLE, o, e)];
                                    var e, r, n, o
                                },
                                toString: function() {
                                    return function(e, t, r, n) {
                                        var o = $(r),
                                            i = z(t);
                                        return o ? "<" + e + " " + S + '="true" ' + o + ">" + L(i, n) + "</" + e + ">" : "<" + e + " " + S + '="true">' + L(i, n) + "</" + e + ">"
                                    }(e, t.title, t.titleAttributes, r)
                                }
                            };
                        case d:
                        case y:
                            return {
                                toComponent: function() {
                                    return G(t)
                                },
                                toString: function() {
                                    return $(t)
                                }
                            };
                        default:
                            return {
                                toComponent: function() {
                                    return function(e, t) {
                                        return t.map((function(t, r) {
                                            var n, o = ((n = {
                                                key: r
                                            })[S] = !0, n);
                                            return Object.keys(t).forEach((function(e) {
                                                var r = O[e] || e;
                                                if (r === g || r === m) {
                                                    var n = t.innerHTML || t.cssText;
                                                    o.dangerouslySetInnerHTML = {
                                                        __html: n
                                                    }
                                                } else o[r] = t[e]
                                            })), l.createElement(e, o)
                                        }))
                                    }(e, t)
                                },
                                toString: function() {
                                    return function(e, t, r) {
                                        return t.reduce((function(t, n) {
                                            var o = Object.keys(n).filter((function(e) {
                                                    return !(e === g || e === m)
                                                })).reduce((function(e, t) {
                                                    var o = void 0 === n[t] ? t : t + '="' + L(n[t], r) + '"';
                                                    return e ? e + " " + o : o
                                                }), ""),
                                                i = n.innerHTML || n.cssText || "",
                                                a = -1 === C.indexOf(e);
                                            return t + "<" + e + " " + S + '="true" ' + o + (a ? "/>" : ">" + i + "</" + e + ">")
                                        }), "")
                                    }(e, t, r)
                                }
                            }
                    }
                },
                Q = function(e) {
                    var t = e.baseTag,
                        r = e.bodyAttributes,
                        n = e.encode,
                        o = e.htmlAttributes,
                        i = e.linkTags,
                        a = e.metaTags,
                        u = e.noscriptTags,
                        c = e.scriptTags,
                        s = e.styleTags,
                        f = e.title,
                        l = void 0 === f ? "" : f,
                        p = e.titleAttributes;
                    return {
                        base: J(h.BASE, t, n),
                        bodyAttributes: J(d, r, n),
                        htmlAttributes: J(y, o, n),
                        link: J(h.LINK, i, n),
                        meta: J(h.META, a, n),
                        noscript: J(h.NOSCRIPT, u, n),
                        script: J(h.SCRIPT, c, n),
                        style: J(h.STYLE, s, n),
                        title: J(h.TITLE, {
                            title: l,
                            titleAttributes: p
                        }, n)
                    }
                },
                X = s((function(e) {
                    return {
                        baseTag: N([T, "target"], e),
                        bodyAttributes: M(d, e),
                        defer: _(e, "defer"),
                        encode: _(e, "encodeSpecialCharacters"),
                        htmlAttributes: M(y, e),
                        linkTags: R(h.LINK, [w, T], e),
                        metaTags: R(h.META, ["name", b, "http-equiv", "property", v], e),
                        noscriptTags: R(h.NOSCRIPT, [g], e),
                        onChangeClientState: I(e),
                        scriptTags: R(h.SCRIPT, ["src", g], e),
                        styleTags: R(h.STYLE, [m], e),
                        title: x(e),
                        titleAttributes: M("titleAttributes", e)
                    }
                }), (function(e) {
                    F && D(F), e.defer ? F = B((function() {
                        U(e, (function() {
                            F = null
                        }))
                    })) : (U(e), F = null)
                }), Q)((function() {
                    return null
                })),
                Z = (i = X, u = a = function(e) {
                    function t() {
                        return function(e, t) {
                                if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                            }(this, t),
                            function(e, t) {
                                if (!e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                                return !t || "object" != typeof t && "function" != typeof t ? e : t
                            }(this, e.apply(this, arguments))
                    }
                    return function(e, t) {
                        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function, not " + typeof t);
                        e.prototype = Object.create(t && t.prototype, {
                            constructor: {
                                value: e,
                                enumerable: !1,
                                writable: !0,
                                configurable: !0
                            }
                        }), t && (Object.setPrototypeOf ? Object.setPrototypeOf(e, t) : e.__proto__ = t)
                    }(t, e), t.prototype.shouldComponentUpdate = function(e) {
                        return !f(this.props, e)
                    }, t.prototype.mapNestedChildrenToProps = function(e, t) {
                        if (!t) return null;
                        switch (e.type) {
                            case h.SCRIPT:
                            case h.NOSCRIPT:
                                return {
                                    innerHTML: t
                                };
                            case h.STYLE:
                                return {
                                    cssText: t
                                }
                        }
                        throw new Error("<" + e.type + " /> elements are self-closing and can not contain children. Refer to our API for more information.")
                    }, t.prototype.flattenArrayTypeChildren = function(e) {
                        var t, r = e.child,
                            n = e.arrayTypeChildren,
                            o = e.newChildProps,
                            i = e.nestedChildren;
                        return P({}, n, ((t = {})[r.type] = [].concat(n[r.type] || [], [P({}, o, this.mapNestedChildrenToProps(r, i))]), t))
                    }, t.prototype.mapObjectTypeChildren = function(e) {
                        var t, r, n = e.child,
                            o = e.newProps,
                            i = e.newChildProps,
                            a = e.nestedChildren;
                        switch (n.type) {
                            case h.TITLE:
                                return P({}, o, ((t = {})[n.type] = a, t.titleAttributes = P({}, i), t));
                            case h.BODY:
                                return P({}, o, {
                                    bodyAttributes: P({}, i)
                                });
                            case h.HTML:
                                return P({}, o, {
                                    htmlAttributes: P({}, i)
                                })
                        }
                        return P({}, o, ((r = {})[n.type] = P({}, i), r))
                    }, t.prototype.mapArrayTypeChildrenToProps = function(e, t) {
                        var r = P({}, t);
                        return Object.keys(e).forEach((function(t) {
                            var n;
                            r = P({}, r, ((n = {})[t] = e[t], n))
                        })), r
                    }, t.prototype.warnOnInvalidChildren = function(e, t) {
                        return !0
                    }, t.prototype.mapChildrenToProps = function(e, t) {
                        var r = this,
                            n = {};
                        return l.Children.forEach(e, (function(e) {
                            if (e && e.props) {
                                var o = e.props,
                                    i = o.children,
                                    a = function(e) {
                                        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                                        return Object.keys(e).reduce((function(t, r) {
                                            return t[A[r] || r] = e[r], t
                                        }), t)
                                    }(k(o, ["children"]));
                                switch (r.warnOnInvalidChildren(e, i), e.type) {
                                    case h.LINK:
                                    case h.META:
                                    case h.NOSCRIPT:
                                    case h.SCRIPT:
                                    case h.STYLE:
                                        n = r.flattenArrayTypeChildren({
                                            child: e,
                                            arrayTypeChildren: n,
                                            newChildProps: a,
                                            nestedChildren: i
                                        });
                                        break;
                                    default:
                                        t = r.mapObjectTypeChildren({
                                            child: e,
                                            newProps: t,
                                            newChildProps: a,
                                            nestedChildren: i
                                        })
                                }
                            }
                        })), t = this.mapArrayTypeChildrenToProps(n, t)
                    }, t.prototype.render = function() {
                        var e = this.props,
                            t = e.children,
                            r = k(e, ["children"]),
                            n = P({}, r);
                        return t && (n = this.mapChildrenToProps(t, n)), l.createElement(i, n)
                    }, j(t, null, [{
                        key: "canUseDOM",
                        set: function(e) {
                            i.canUseDOM = e
                        }
                    }]), t
                }(l.Component), a.propTypes = {
                    base: c.object,
                    bodyAttributes: c.object,
                    children: c.oneOfType([c.arrayOf(c.node), c.node]),
                    defaultTitle: c.string,
                    defer: c.bool,
                    encodeSpecialCharacters: c.bool,
                    htmlAttributes: c.object,
                    link: c.arrayOf(c.object),
                    meta: c.arrayOf(c.object),
                    noscript: c.arrayOf(c.object),
                    onChangeClientState: c.func,
                    script: c.arrayOf(c.object),
                    style: c.arrayOf(c.object),
                    title: c.string,
                    titleAttributes: c.object,
                    titleTemplate: c.string
                }, a.defaultProps = {
                    defer: !0,
                    encodeSpecialCharacters: !0
                }, a.peek = i.peek, a.rewind = function() {
                    var e = i.rewind();
                    return e || (e = Q({
                        baseTag: [],
                        bodyAttributes: {},
                        encodeSpecialCharacters: !0,
                        htmlAttributes: {},
                        linkTags: [],
                        metaTags: [],
                        noscriptTags: [],
                        scriptTags: [],
                        styleTags: [],
                        title: "",
                        titleAttributes: {}
                    })), e
                }, u);
            Z.renderStatic = Z.rewind, t.Helmet = Z, t.default = Z
        },
        47525: (e, t, r) => {
            var n, o = r(76789),
                i = (n = o) && "object" == typeof n && "default" in n ? n.default : n;

            function a(e, t, r) {
                return t in e ? Object.defineProperty(e, t, {
                    value: r,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = r, e
            }
            var u = !("undefined" == typeof window || !window.document || !window.document.createElement);
            e.exports = function(e, t, r) {
                if ("function" != typeof e) throw new Error("Expected reducePropsToState to be a function.");
                if ("function" != typeof t) throw new Error("Expected handleStateChangeOnClient to be a function.");
                if (void 0 !== r && "function" != typeof r) throw new Error("Expected mapStateOnServer to either be undefined or a function.");
                return function(n) {
                    if ("function" != typeof n) throw new Error("Expected WrappedComponent to be a React component.");
                    var c, s = [];

                    function f() {
                        c = e(s.map((function(e) {
                            return e.props
                        }))), l.canUseDOM ? t(c) : r && (c = r(c))
                    }
                    var l = function(e) {
                        var t, r;

                        function o() {
                            return e.apply(this, arguments) || this
                        }
                        r = e, (t = o).prototype = Object.create(r.prototype), t.prototype.constructor = t, t.__proto__ = r, o.peek = function() {
                            return c
                        }, o.rewind = function() {
                            if (o.canUseDOM) throw new Error("You may only call rewind() on the server. Call peek() to read the current state.");
                            var e = c;
                            return c = void 0, s = [], e
                        };
                        var a = o.prototype;
                        return a.UNSAFE_componentWillMount = function() {
                            s.push(this), f()
                        }, a.componentDidUpdate = function() {
                            f()
                        }, a.componentWillUnmount = function() {
                            var e = s.indexOf(this);
                            s.splice(e, 1), f()
                        }, a.render = function() {
                            return i.createElement(n, this.props)
                        }, o
                    }(o.PureComponent);
                    return a(l, "displayName", "SideEffect(" + function(e) {
                        return e.displayName || e.name || "Component"
                    }(n) + ")"), a(l, "canUseDOM", u), l
                }
            }
        }
    }
]);
//# sourceMappingURL=6492.f37308b43f9318ca.js.map