(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [3834], {
        53834: (e, t, l) => {
            l.r(t), l.d(t, {
                default: () => c
            });
            var i = l(37900),
                n = l(93500),
                a = l(84848),
                h = l(74499);
            const s = (0, i.lazy)((() => Promise.all([l.e(8231), l.e(5190), l.e(5862), l.e(6106), l.e(5335), l.e(2560), l.e(2526), l.e(5132), l.e(6584)]).then(l.bind(l, 16584)))),
                d = (0, i.lazy)((() => Promise.all([l.e(5760), l.e(5190), l.e(5862), l.e(6106), l.e(5335), l.e(6144), l.e(6756), l.e(2560), l.e(2017), l.e(5618), l.e(7371), l.e(3084), l.e(5132), l.e(760)]).then(l.bind(l, 57268)))),
                r = (0, i.lazy)((() => Promise.all([l.e(5190), l.e(5862), l.e(6106), l.e(5335), l.e(1975)]).then(l.bind(l, 21882)))),
                p = (0, i.lazy)((() => Promise.all([l.e(5862), l.e(7419)]).then(l.bind(l, 99487)))),
                Z = (0, i.lazy)((() => Promise.all([l.e(5862), l.e(5335), l.e(5618), l.e(3725), l.e(317)]).then(l.bind(l, 74070)))),
                m = (0, i.lazy)((() => Promise.all([l.e(5190), l.e(5862), l.e(6106), l.e(236), l.e(5335), l.e(3137), l.e(2560), l.e(5618), l.e(3084), l.e(6817)]).then(l.bind(l, 56817)))),
                o = (0, i.lazy)((() => Promise.all([l.e(4424), l.e(5190), l.e(5862), l.e(6106), l.e(5335), l.e(3725), l.e(4816)]).then(l.bind(l, 54156)))),
                y = (0, i.lazy)((() => Promise.all([l.e(5190), l.e(5862), l.e(6106), l.e(4204)]).then(l.bind(l, 4204))));

            function c() {
                return [{
                    path: "/",
                    element: (0, h.tZ)(a.Navigate, {
                        to: "/sites"
                    }),
                    type: "bare"
                }, {
                    path: "/editor-frame",
                    element: (0, h.tZ)(s, {}),
                    type: "frame"
                }, {
                    path: "site/:siteId/:mode/*",
                    element: (0, h.tZ)(n.Z, {
                        children: (0, h.tZ)(d, {})
                    }),
                    type: "bare"
                }, {
                    path: "site/:siteId/:mode/:pageId/*",
                    element: (0, h.tZ)(n.Z, {
                        children: (0, h.tZ)(d, {})
                    }),
                    type: "bare"
                }, {
                    path: "create-website",
                    element: (0, h.tZ)(n.Z, {
                        children: (0, h.tZ)(p, {})
                    }),
                    type: "bare"
                }, {
                    path: "/sites",
                    element: (0, h.tZ)(n.Z, {
                        children: (0, h.tZ)(r, {})
                    }),
                    type: "shell"
                }, {
                    path: "site/:siteId/settings/*",
                    element: (0, h.tZ)(n.Z, {
                        children: (0, h.tZ)(m, {})
                    }),
                    type: "shell"
                }, {
                    path: "site/:siteId/purchase-domain/*",
                    element: (0, h.tZ)(n.Z, {
                        children: (0, h.tZ)(o, {})
                    }),
                    type: "shell"
                }, {
                    path: "upgrade/*",
                    element: (0, h.tZ)(n.Z, {
                        children: (0, h.tZ)(Z, {})
                    }),
                    type: "shell"
                }, {
                    path: "site/:siteId/connect-domain/*",
                    element: (0, h.tZ)(n.Z, {
                        children: (0, h.tZ)(y, {})
                    }),
                    type: "shell"
                }]
            }
        }
    }
]);
//# sourceMappingURL=3834.562887d1572e8b35.js.map