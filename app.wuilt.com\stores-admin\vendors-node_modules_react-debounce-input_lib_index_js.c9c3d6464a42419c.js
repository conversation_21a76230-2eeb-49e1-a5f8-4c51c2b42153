(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_react-debounce-input_lib_index_js"], {

        /***/
        "../../node_modules/lodash.debounce/index.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                /**
                 * lodash (Custom Build) <https://lodash.com/>
                 * Build: `lodash modularize exports="npm" -o ./`
                 * Copyright jQuery Foundation and other contributors <https://jquery.org/>
                 * Released under MIT license <https://lodash.com/license>
                 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
                 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
                 */

                /** Used as the `TypeError` message for "Functions" methods. */
                var FUNC_ERROR_TEXT = 'Expected a function';

                /** Used as references for various `Number` constants. */
                var NAN = 0 / 0;

                /** `Object#toString` result references. */
                var symbolTag = '[object Symbol]';

                /** Used to match leading and trailing whitespace. */
                var reTrim = /^\s+|\s+$/g;

                /** Used to detect bad signed hexadecimal string values. */
                var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;

                /** Used to detect binary string values. */
                var reIsBinary = /^0b[01]+$/i;

                /** Used to detect octal string values. */
                var reIsOctal = /^0o[0-7]+$/i;

                /** Built-in method references without a dependency on `root`. */
                var freeParseInt = parseInt;

                /** Detect free variable `global` from Node.js. */
                var freeGlobal = typeof __webpack_require__.g == 'object' && __webpack_require__.g && __webpack_require__.g.Object === Object && __webpack_require__.g;

                /** Detect free variable `self`. */
                var freeSelf = typeof self == 'object' && self && self.Object === Object && self;

                /** Used as a reference to the global object. */
                var root = freeGlobal || freeSelf || Function('return this')();

                /** Used for built-in method references. */
                var objectProto = Object.prototype;

                /**
                 * Used to resolve the
                 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
                 * of values.
                 */
                var objectToString = objectProto.toString;

                /* Built-in method references for those with the same name as other `lodash` methods. */
                var nativeMax = Math.max,
                    nativeMin = Math.min;

                /**
                 * Gets the timestamp of the number of milliseconds that have elapsed since
                 * the Unix epoch (1 January 1970 00:00:00 UTC).
                 *
                 * @static
                 * @memberOf _
                 * @since 2.4.0
                 * @category Date
                 * @returns {number} Returns the timestamp.
                 * @example
                 *
                 * _.defer(function(stamp) {
                 *   console.log(_.now() - stamp);
                 * }, _.now());
                 * // => Logs the number of milliseconds it took for the deferred invocation.
                 */
                var now = function() {
                    return root.Date.now();
                };

                /**
                 * Creates a debounced function that delays invoking `func` until after `wait`
                 * milliseconds have elapsed since the last time the debounced function was
                 * invoked. The debounced function comes with a `cancel` method to cancel
                 * delayed `func` invocations and a `flush` method to immediately invoke them.
                 * Provide `options` to indicate whether `func` should be invoked on the
                 * leading and/or trailing edge of the `wait` timeout. The `func` is invoked
                 * with the last arguments provided to the debounced function. Subsequent
                 * calls to the debounced function return the result of the last `func`
                 * invocation.
                 *
                 * **Note:** If `leading` and `trailing` options are `true`, `func` is
                 * invoked on the trailing edge of the timeout only if the debounced function
                 * is invoked more than once during the `wait` timeout.
                 *
                 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
                 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
                 *
                 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
                 * for details over the differences between `_.debounce` and `_.throttle`.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Function
                 * @param {Function} func The function to debounce.
                 * @param {number} [wait=0] The number of milliseconds to delay.
                 * @param {Object} [options={}] The options object.
                 * @param {boolean} [options.leading=false]
                 *  Specify invoking on the leading edge of the timeout.
                 * @param {number} [options.maxWait]
                 *  The maximum time `func` is allowed to be delayed before it's invoked.
                 * @param {boolean} [options.trailing=true]
                 *  Specify invoking on the trailing edge of the timeout.
                 * @returns {Function} Returns the new debounced function.
                 * @example
                 *
                 * // Avoid costly calculations while the window size is in flux.
                 * jQuery(window).on('resize', _.debounce(calculateLayout, 150));
                 *
                 * // Invoke `sendMail` when clicked, debouncing subsequent calls.
                 * jQuery(element).on('click', _.debounce(sendMail, 300, {
                 *   'leading': true,
                 *   'trailing': false
                 * }));
                 *
                 * // Ensure `batchLog` is invoked once after 1 second of debounced calls.
                 * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });
                 * var source = new EventSource('/stream');
                 * jQuery(source).on('message', debounced);
                 *
                 * // Cancel the trailing debounced invocation.
                 * jQuery(window).on('popstate', debounced.cancel);
                 */
                function debounce(func, wait, options) {
                    var lastArgs,
                        lastThis,
                        maxWait,
                        result,
                        timerId,
                        lastCallTime,
                        lastInvokeTime = 0,
                        leading = false,
                        maxing = false,
                        trailing = true;

                    if (typeof func != 'function') {
                        throw new TypeError(FUNC_ERROR_TEXT);
                    }
                    wait = toNumber(wait) || 0;
                    if (isObject(options)) {
                        leading = !!options.leading;
                        maxing = 'maxWait' in options;
                        maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
                        trailing = 'trailing' in options ? !!options.trailing : trailing;
                    }

                    function invokeFunc(time) {
                        var args = lastArgs,
                            thisArg = lastThis;

                        lastArgs = lastThis = undefined;
                        lastInvokeTime = time;
                        result = func.apply(thisArg, args);
                        return result;
                    }

                    function leadingEdge(time) {
                        // Reset any `maxWait` timer.
                        lastInvokeTime = time;
                        // Start the timer for the trailing edge.
                        timerId = setTimeout(timerExpired, wait);
                        // Invoke the leading edge.
                        return leading ? invokeFunc(time) : result;
                    }

                    function remainingWait(time) {
                        var timeSinceLastCall = time - lastCallTime,
                            timeSinceLastInvoke = time - lastInvokeTime,
                            result = wait - timeSinceLastCall;

                        return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;
                    }

                    function shouldInvoke(time) {
                        var timeSinceLastCall = time - lastCallTime,
                            timeSinceLastInvoke = time - lastInvokeTime;

                        // Either this is the first call, activity has stopped and we're at the
                        // trailing edge, the system time has gone backwards and we're treating
                        // it as the trailing edge, or we've hit the `maxWait` limit.
                        return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||
                            (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));
                    }

                    function timerExpired() {
                        var time = now();
                        if (shouldInvoke(time)) {
                            return trailingEdge(time);
                        }
                        // Restart the timer.
                        timerId = setTimeout(timerExpired, remainingWait(time));
                    }

                    function trailingEdge(time) {
                        timerId = undefined;

                        // Only invoke if we have `lastArgs` which means `func` has been
                        // debounced at least once.
                        if (trailing && lastArgs) {
                            return invokeFunc(time);
                        }
                        lastArgs = lastThis = undefined;
                        return result;
                    }

                    function cancel() {
                        if (timerId !== undefined) {
                            clearTimeout(timerId);
                        }
                        lastInvokeTime = 0;
                        lastArgs = lastCallTime = lastThis = timerId = undefined;
                    }

                    function flush() {
                        return timerId === undefined ? result : trailingEdge(now());
                    }

                    function debounced() {
                        var time = now(),
                            isInvoking = shouldInvoke(time);

                        lastArgs = arguments;
                        lastThis = this;
                        lastCallTime = time;

                        if (isInvoking) {
                            if (timerId === undefined) {
                                return leadingEdge(lastCallTime);
                            }
                            if (maxing) {
                                // Handle invocations in a tight loop.
                                timerId = setTimeout(timerExpired, wait);
                                return invokeFunc(lastCallTime);
                            }
                        }
                        if (timerId === undefined) {
                            timerId = setTimeout(timerExpired, wait);
                        }
                        return result;
                    }
                    debounced.cancel = cancel;
                    debounced.flush = flush;
                    return debounced;
                }

                /**
                 * Checks if `value` is the
                 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
                 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
                 * @example
                 *
                 * _.isObject({});
                 * // => true
                 *
                 * _.isObject([1, 2, 3]);
                 * // => true
                 *
                 * _.isObject(_.noop);
                 * // => true
                 *
                 * _.isObject(null);
                 * // => false
                 */
                function isObject(value) {
                    var type = typeof value;
                    return !!value && (type == 'object' || type == 'function');
                }

                /**
                 * Checks if `value` is object-like. A value is object-like if it's not `null`
                 * and has a `typeof` result of "object".
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
                 * @example
                 *
                 * _.isObjectLike({});
                 * // => true
                 *
                 * _.isObjectLike([1, 2, 3]);
                 * // => true
                 *
                 * _.isObjectLike(_.noop);
                 * // => false
                 *
                 * _.isObjectLike(null);
                 * // => false
                 */
                function isObjectLike(value) {
                    return !!value && typeof value == 'object';
                }

                /**
                 * Checks if `value` is classified as a `Symbol` primitive or object.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
                 * @example
                 *
                 * _.isSymbol(Symbol.iterator);
                 * // => true
                 *
                 * _.isSymbol('abc');
                 * // => false
                 */
                function isSymbol(value) {
                    return typeof value == 'symbol' ||
                        (isObjectLike(value) && objectToString.call(value) == symbolTag);
                }

                /**
                 * Converts `value` to a number.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to process.
                 * @returns {number} Returns the number.
                 * @example
                 *
                 * _.toNumber(3.2);
                 * // => 3.2
                 *
                 * _.toNumber(Number.MIN_VALUE);
                 * // => 5e-324
                 *
                 * _.toNumber(Infinity);
                 * // => Infinity
                 *
                 * _.toNumber('3.2');
                 * // => 3.2
                 */
                function toNumber(value) {
                    if (typeof value == 'number') {
                        return value;
                    }
                    if (isSymbol(value)) {
                        return NAN;
                    }
                    if (isObject(value)) {
                        var other = typeof value.valueOf == 'function' ? value.valueOf() : value;
                        value = isObject(other) ? (other + '') : other;
                    }
                    if (typeof value != 'string') {
                        return value === 0 ? value : +value;
                    }
                    value = value.replace(reTrim, '');
                    var isBinary = reIsBinary.test(value);
                    return (isBinary || reIsOctal.test(value)) ?
                        freeParseInt(value.slice(2), isBinary ? 2 : 8) :
                        (reIsBadHex.test(value) ? NAN : +value);
                }

                module.exports = debounce;


                /***/
            }),

        /***/
        "../../node_modules/react-debounce-input/lib/Component.js":
            /***/
            ((__unused_webpack_module, exports, __webpack_require__) => {



                function _typeof(obj) {
                    "@babel/helpers - typeof";
                    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj) {
                        return typeof obj;
                    } : function(obj) {
                        return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
                    }, _typeof(obj);
                }

                Object.defineProperty(exports, "__esModule", ({
                    value: true
                }));
                exports.DebounceInput = void 0;

                var _react = _interopRequireDefault(__webpack_require__("webpack/sharing/consume/default/react/react?83d2"));

                var _lodash = _interopRequireDefault(__webpack_require__("../../node_modules/lodash.debounce/index.js"));

                var _excluded = ["element", "onChange", "value", "minLength", "debounceTimeout", "forceNotifyByEnter", "forceNotifyOnBlur", "onKeyDown", "onBlur", "inputRef"];

                function _interopRequireDefault(obj) {
                    return obj && obj.__esModule ? obj : {
                        "default": obj
                    };
                }

                function _objectWithoutProperties(source, excluded) {
                    if (source == null) return {};
                    var target = _objectWithoutPropertiesLoose(source, excluded);
                    var key, i;
                    if (Object.getOwnPropertySymbols) {
                        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
                        for (i = 0; i < sourceSymbolKeys.length; i++) {
                            key = sourceSymbolKeys[i];
                            if (excluded.indexOf(key) >= 0) continue;
                            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
                            target[key] = source[key];
                        }
                    }
                    return target;
                }

                function _objectWithoutPropertiesLoose(source, excluded) {
                    if (source == null) return {};
                    var target = {};
                    var sourceKeys = Object.keys(source);
                    var key, i;
                    for (i = 0; i < sourceKeys.length; i++) {
                        key = sourceKeys[i];
                        if (excluded.indexOf(key) >= 0) continue;
                        target[key] = source[key];
                    }
                    return target;
                }

                function ownKeys(object, enumerableOnly) {
                    var keys = Object.keys(object);
                    if (Object.getOwnPropertySymbols) {
                        var symbols = Object.getOwnPropertySymbols(object);
                        enumerableOnly && (symbols = symbols.filter(function(sym) {
                            return Object.getOwnPropertyDescriptor(object, sym).enumerable;
                        })), keys.push.apply(keys, symbols);
                    }
                    return keys;
                }

                function _objectSpread(target) {
                    for (var i = 1; i < arguments.length; i++) {
                        var source = null != arguments[i] ? arguments[i] : {};
                        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {
                            _defineProperty(target, key, source[key]);
                        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
                            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
                        });
                    }
                    return target;
                }

                function _classCallCheck(instance, Constructor) {
                    if (!(instance instanceof Constructor)) {
                        throw new TypeError("Cannot call a class as a function");
                    }
                }

                function _defineProperties(target, props) {
                    for (var i = 0; i < props.length; i++) {
                        var descriptor = props[i];
                        descriptor.enumerable = descriptor.enumerable || false;
                        descriptor.configurable = true;
                        if ("value" in descriptor) descriptor.writable = true;
                        Object.defineProperty(target, descriptor.key, descriptor);
                    }
                }

                function _createClass(Constructor, protoProps, staticProps) {
                    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
                    if (staticProps) _defineProperties(Constructor, staticProps);
                    Object.defineProperty(Constructor, "prototype", {
                        writable: false
                    });
                    return Constructor;
                }

                function _inherits(subClass, superClass) {
                    if (typeof superClass !== "function" && superClass !== null) {
                        throw new TypeError("Super expression must either be null or a function");
                    }
                    subClass.prototype = Object.create(superClass && superClass.prototype, {
                        constructor: {
                            value: subClass,
                            writable: true,
                            configurable: true
                        }
                    });
                    Object.defineProperty(subClass, "prototype", {
                        writable: false
                    });
                    if (superClass) _setPrototypeOf(subClass, superClass);
                }

                function _setPrototypeOf(o, p) {
                    _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {
                        o.__proto__ = p;
                        return o;
                    };
                    return _setPrototypeOf(o, p);
                }

                function _createSuper(Derived) {
                    var hasNativeReflectConstruct = _isNativeReflectConstruct();
                    return function _createSuperInternal() {
                        var Super = _getPrototypeOf(Derived),
                            result;
                        if (hasNativeReflectConstruct) {
                            var NewTarget = _getPrototypeOf(this).constructor;
                            result = Reflect.construct(Super, arguments, NewTarget);
                        } else {
                            result = Super.apply(this, arguments);
                        }
                        return _possibleConstructorReturn(this, result);
                    };
                }

                function _possibleConstructorReturn(self, call) {
                    if (call && (_typeof(call) === "object" || typeof call === "function")) {
                        return call;
                    } else if (call !== void 0) {
                        throw new TypeError("Derived constructors may only return object or undefined");
                    }
                    return _assertThisInitialized(self);
                }

                function _assertThisInitialized(self) {
                    if (self === void 0) {
                        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                    }
                    return self;
                }

                function _isNativeReflectConstruct() {
                    if (typeof Reflect === "undefined" || !Reflect.construct) return false;
                    if (Reflect.construct.sham) return false;
                    if (typeof Proxy === "function") return true;
                    try {
                        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
                        return true;
                    } catch (e) {
                        return false;
                    }
                }

                function _getPrototypeOf(o) {
                    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {
                        return o.__proto__ || Object.getPrototypeOf(o);
                    };
                    return _getPrototypeOf(o);
                }

                function _defineProperty(obj, key, value) {
                    if (key in obj) {
                        Object.defineProperty(obj, key, {
                            value: value,
                            enumerable: true,
                            configurable: true,
                            writable: true
                        });
                    } else {
                        obj[key] = value;
                    }
                    return obj;
                }

                var DebounceInput = /*#__PURE__*/ function(_React$PureComponent) {
                    _inherits(DebounceInput, _React$PureComponent);

                    var _super = _createSuper(DebounceInput);

                    function DebounceInput(props) {
                        var _this;

                        _classCallCheck(this, DebounceInput);

                        _this = _super.call(this, props);

                        _defineProperty(_assertThisInitialized(_this), "onChange", function(event) {
                            event.persist();
                            var oldValue = _this.state.value;
                            var minLength = _this.props.minLength;

                            _this.setState({
                                value: event.target.value
                            }, function() {
                                var value = _this.state.value;

                                if (value.length >= minLength) {
                                    _this.notify(event);

                                    return;
                                } // If user hits backspace and goes below minLength consider it cleaning the value


                                if (oldValue.length > value.length) {
                                    _this.notify(_objectSpread(_objectSpread({}, event), {}, {
                                        target: _objectSpread(_objectSpread({}, event.target), {}, {
                                            value: ''
                                        })
                                    }));
                                }
                            });
                        });

                        _defineProperty(_assertThisInitialized(_this), "onKeyDown", function(event) {
                            if (event.key === 'Enter') {
                                _this.forceNotify(event);
                            } // Invoke original onKeyDown if present


                            var onKeyDown = _this.props.onKeyDown;

                            if (onKeyDown) {
                                event.persist();
                                onKeyDown(event);
                            }
                        });

                        _defineProperty(_assertThisInitialized(_this), "onBlur", function(event) {
                            _this.forceNotify(event); // Invoke original onBlur if present


                            var onBlur = _this.props.onBlur;

                            if (onBlur) {
                                event.persist();
                                onBlur(event);
                            }
                        });

                        _defineProperty(_assertThisInitialized(_this), "createNotifier", function(debounceTimeout) {
                            if (debounceTimeout < 0) {
                                _this.notify = function() {
                                    return null;
                                };
                            } else if (debounceTimeout === 0) {
                                _this.notify = _this.doNotify;
                            } else {
                                var debouncedChangeFunc = (0, _lodash["default"])(function(event) {
                                    _this.isDebouncing = false;

                                    _this.doNotify(event);
                                }, debounceTimeout);

                                _this.notify = function(event) {
                                    _this.isDebouncing = true;
                                    debouncedChangeFunc(event);
                                };

                                _this.flush = function() {
                                    return debouncedChangeFunc.flush();
                                };

                                _this.cancel = function() {
                                    _this.isDebouncing = false;
                                    debouncedChangeFunc.cancel();
                                };
                            }
                        });

                        _defineProperty(_assertThisInitialized(_this), "doNotify", function() {
                            var onChange = _this.props.onChange;
                            onChange.apply(void 0, arguments);
                        });

                        _defineProperty(_assertThisInitialized(_this), "forceNotify", function(event) {
                            var debounceTimeout = _this.props.debounceTimeout;

                            if (!_this.isDebouncing && debounceTimeout > 0) {
                                return;
                            }

                            if (_this.cancel) {
                                _this.cancel();
                            }

                            var value = _this.state.value;
                            var minLength = _this.props.minLength;

                            if (value.length >= minLength) {
                                _this.doNotify(event);
                            } else {
                                _this.doNotify(_objectSpread(_objectSpread({}, event), {}, {
                                    target: _objectSpread(_objectSpread({}, event.target), {}, {
                                        value: value
                                    })
                                }));
                            }
                        });

                        _this.isDebouncing = false;
                        _this.state = {
                            value: typeof props.value === 'undefined' || props.value === null ? '' : props.value
                        };
                        var _debounceTimeout2 = _this.props.debounceTimeout;

                        _this.createNotifier(_debounceTimeout2);

                        return _this;
                    }

                    _createClass(DebounceInput, [{
                        key: "componentDidUpdate",
                        value: function componentDidUpdate(prevProps) {
                            if (this.isDebouncing) {
                                return;
                            }

                            var _this$props = this.props,
                                value = _this$props.value,
                                debounceTimeout = _this$props.debounceTimeout;
                            var oldTimeout = prevProps.debounceTimeout,
                                oldValue = prevProps.value;
                            var stateValue = this.state.value;

                            if (typeof value !== 'undefined' && oldValue !== value && stateValue !== value) {
                                // Update state.value if new value passed via props, yep re-render should happen
                                // eslint-disable-next-line react/no-did-update-set-state
                                this.setState({
                                    value: value
                                });
                            }

                            if (debounceTimeout !== oldTimeout) {
                                this.createNotifier(debounceTimeout);
                            }
                        }
                    }, {
                        key: "componentWillUnmount",
                        value: function componentWillUnmount() {
                            if (this.flush) {
                                this.flush();
                            }
                        }
                    }, {
                        key: "render",
                        value: function render() {
                            var _this$props2 = this.props,
                                element = _this$props2.element,
                                _onChange = _this$props2.onChange,
                                _value = _this$props2.value,
                                _minLength = _this$props2.minLength,
                                _debounceTimeout = _this$props2.debounceTimeout,
                                forceNotifyByEnter = _this$props2.forceNotifyByEnter,
                                forceNotifyOnBlur = _this$props2.forceNotifyOnBlur,
                                onKeyDown = _this$props2.onKeyDown,
                                onBlur = _this$props2.onBlur,
                                inputRef = _this$props2.inputRef,
                                props = _objectWithoutProperties(_this$props2, _excluded);

                            var value = this.state.value;
                            var maybeOnKeyDown;

                            if (forceNotifyByEnter) {
                                maybeOnKeyDown = {
                                    onKeyDown: this.onKeyDown
                                };
                            } else if (onKeyDown) {
                                maybeOnKeyDown = {
                                    onKeyDown: onKeyDown
                                };
                            } else {
                                maybeOnKeyDown = {};
                            }

                            var maybeOnBlur;

                            if (forceNotifyOnBlur) {
                                maybeOnBlur = {
                                    onBlur: this.onBlur
                                };
                            } else if (onBlur) {
                                maybeOnBlur = {
                                    onBlur: onBlur
                                };
                            } else {
                                maybeOnBlur = {};
                            }

                            var maybeRef = inputRef ? {
                                ref: inputRef
                            } : {};
                            return /*#__PURE__*/ _react["default"].createElement(element, _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, props), {}, {
                                onChange: this.onChange,
                                value: value
                            }, maybeOnKeyDown), maybeOnBlur), maybeRef));
                        }
                    }]);

                    return DebounceInput;
                }(_react["default"].PureComponent);

                exports.DebounceInput = DebounceInput;

                _defineProperty(DebounceInput, "defaultProps", {
                    element: 'input',
                    type: 'text',
                    onKeyDown: undefined,
                    onBlur: undefined,
                    value: undefined,
                    minLength: 0,
                    debounceTimeout: 100,
                    forceNotifyByEnter: true,
                    forceNotifyOnBlur: true,
                    inputRef: undefined
                });

                /***/
            }),

        /***/
        "../../node_modules/react-debounce-input/lib/index.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {



                var _require = __webpack_require__("../../node_modules/react-debounce-input/lib/Component.js"),
                    DebounceInput = _require.DebounceInput;

                DebounceInput.DebounceInput = DebounceInput;
                module.exports = DebounceInput;

                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_react-debounce-input_lib_index_js.c9c3d6464a42419c.js.map