! function() {
    "use strict";
    "function" == typeof SuppressedError && SuppressedError;
    var r, n, t = {
        IT_TREATMENT: 30,
        IT_ALLOW: {
            234: !0
        },
        IT_BLOCK: {},
        IT_VERSION: "0.0.243"
    };

    function e(r, n, t) {
        return n in r ? Object.defineProperty(r, n, {
            value: t,
            enumerable: !0,
            configurable: !0,
            writable: !0
        }) : r[n] = t, r
    }! function(r) {
        r[r.XHR = 0] = "XHR", r[r.ImagePixel = 1] = "ImagePixel"
    }(r || (r = {}));
    var a = "MEMBER";
    e(n = {}, "GUEST", "li_gc"), e(n, a, "li_mc");
    var i = function(r) {
            return /^\d+$/.test(r)
        },
        _ = function() {
            try {
                var r = Number(t.IT_TREATMENT || 0),
                    n = t.IT_ALLOW || {},
                    e = t.IT_BLOCK || {};
                return function(r, n, t, e) {
                    for (var a = 0, i = r; a < i.length; a++) {
                        var _ = i[a],
                            d = parseInt(_, 10) % 100 < n,
                            s = t.hasOwnProperty(_),
                            o = e.hasOwnProperty(_);
                        if ((d || s) && !o) return !0
                    }
                    return !1
                }(function(r) {
                    var n = {},
                        t = [];
                    if (r._bizo_data_partner_id && (n[r._bizo_data_partner_id] = !0, t.push(r._bizo_data_partner_id)), r._bizo_data_partner_ids)
                        for (var e = 0, a = r._bizo_data_partner_ids; e < a.length; e++) !n[s = a[e]] && i(s) && (n[s] = !0, t.push(s));
                    if (r._linkedin_data_partner_id && !n[r._linkedin_data_partner_id] && (n[r._linkedin_data_partner_id] = !0, t.push(r._linkedin_data_partner_id)), r._linkedin_data_partner_ids)
                        for (var _ = 0, d = r._linkedin_data_partner_ids; _ < d.length; _++) {
                            var s;
                            !n[s = d[_]] && i(s) && (n[s] = !0, t.push(s))
                        }
                    return t
                }(window), r, n, e)
            } catch (a) {
                return !1
            }
        }() ? "https://snap.licdn.com/li.lms-analytics/insight.beta.min.js" : "https://snap.licdn.com/li.lms-analytics/insight.old.min.js",
        d = document.createElement("script"),
        s = document.getElementsByTagName("script")[0];
    d.async = !0, d.src = _, s.parentNode && s.parentNode.insertBefore(d, s)
}();