{"_lastModified":"2025-08-01T10:46:01.538Z","integrations":{"Google Analytics":{"anonymizeIp":false,"classic":false,"contentGroupings":{},"dimensions":{},"domain":"","doubleClick":true,"enableServerIdentify":false,"enhancedEcommerce":false,"enhancedLinkAttribution":false,"identifyCategory":"","identifyEventName":"","ignoredReferrers":[],"includeSearch":false,"metrics":{},"mobileTrackingId":"","nameTracker":false,"nonInteraction":false,"optimize":"","preferAnonymousId":false,"protocolMappings":{},"reportUncaughtExceptions":false,"resetCustomDimensionsOnPage":[],"sampleRate":100,"sendUserId":true,"setAllMappedProps":true,"siteSpeedSampleRate":1,"topLevelContextMapping":false,"trackCategorizedPages":true,"trackNamedPages":true,"trackingId":"UA-111094753-1","useGoogleAmpClientId":false,"versionSettings":{"version":"2.18.6","componentTypes":["browser","ios","android","server"]},"type":"browser","bundlingStatus":"bundled"},"Twitter
Ads":{"events":{},"identifier":"productId","page":"o0k6f","universalTagPixelId":"o0k6f","versionSettings":{"version":"2.5.4","componentTypes":["browser"]},"type":"browser","bundlingStatus":"bundled"},"Facebook Pixel":{"automaticConfiguration":true,"blacklistPiiProperties":[],"contentTypes":{},"initWithExistingTraits":false,"keyForExternalId":"","legacyEvents":{},"limitedDataUse":true,"pixelId":"162451404508002","standardEvents":{},"standardEventsCustomProperties":[],"userIdAsExternalId":false,"valueIdentifier":"value","whitelistPiiProperties":[],"versionSettings":{"version":"2.11.5","componentTypes":["browser"]},"type":"browser","bundlingStatus":"bundled"},"Facebook
Pixel Server Side":{"versionSettings":{"componentTypes":["server"]},"type":"server","bundlingStatus":"unbundled"},"Webhooks":{"versionSettings":{"componentTypes":["server"]},"type":"server"},"Snapchat Audiences":{"versionSettings":{"version":"2.2.5","componentTypes":["server"]},"type":"server"},"LinkedIn
Insight Tag":{"partnerId":"2119633","versionSettings":{"version":"1.0.1","componentTypes":["browser"]},"type":"browser","bundlingStatus":"bundled"},"Google Tag Manager":{"containerId":"GTM-P358HG2","dataLayerName":"","environment":"","trackAllPages":true,"trackCategorizedPages":false,"trackNamedPages":false,"versionSettings":{"version":"2.5.1","componentTypes":["browser"]},"type":"browser","bundlingStatus":"bundled"},"Segment.io":{"apiKey":"WpSLNy7bllvPEh9wbnD6hUkSfliTlKIA","unbundledIntegrations":["Facebook
Pixel Server Side","Mixpanel"],"addBundledMetadata":true,"maybeBundledConfigIds":{"Twitter Ads":["5fa3f126756de353ec5d33b5"],"Google Analytics":["605745e8dd0615732ce1d736"],"Facebook Pixel":["60574782a1f1ae4a65e005db"],"LinkedIn Insight Tag":["636f03a2ebf57d7eff87f7a3"],"Google
Tag Manager":["636faf37632df095c14cbc20"]},"versionSettings":{"version":"4.4.7","componentTypes":["browser"]}}},"plan":{"track":{"__default":{"enabled":true,"integrations":{}}},"identify":{"__default":{"enabled":true}},"group":{"__default":{"enabled":true}}},"edgeFunction":{},"analyticsNextEnabled":true,"middlewareSettings":{},"enabledMiddleware":{},"metrics":{"sampleRate":0.1},"legacyVideoPluginsEnabled":false,"remotePlugins":[],"autoInstrumentationSettings":{"disableTraffic":false,"sampleRate":0}}