(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [2428], {
        2428: function(o) {
            var n, t, e;
            o.exports = (n = function(o, n, t, e) {
                return (o /= e / 2) < 1 ? t / 2 * o * o + n : -t / 2 * (--o * (o - 2) - 1) + n
            }, t = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o) {
                return typeof o
            } : function(o) {
                return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o
            }, e = function() {
                var o = void 0,
                    e = void 0,
                    i = void 0,
                    r = void 0,
                    u = void 0,
                    c = void 0,
                    d = void 0,
                    a = void 0,
                    f = void 0,
                    s = void 0,
                    l = void 0,
                    b = void 0;

                function v(o) {
                    return o.getBoundingClientRect().top + e
                }

                function w(n) {
                    f || (f = n), l = u(s = n - f, e, d, a), window.scrollTo(0, l), s < a ? window.requestAnimationFrame(w) : (window.scrollTo(0, e + d), o && c && (o.setAttribute("tabindex", "-1"), o.focus()), "function" == typeof b && b(), f = !1)
                }
                return function(f) {
                    var s = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                    switch (a = s.duration || 1e3, r = s.offset || 0, b = s.callback, u = s.easing || n, c = s.a11y || !1, e = window.scrollY || window.pageYOffset, void 0 === f ? "undefined" : t(f)) {
                        case "number":
                            o = void 0, c = !1, i = e + f;
                            break;
                        case "object":
                            i = v(o = f);
                            break;
                        case "string":
                            o = document.querySelector(f), i = v(o)
                    }
                    switch (d = i - e + r, t(s.duration)) {
                        case "number":
                            a = s.duration;
                            break;
                        case "function":
                            a = s.duration(d)
                    }
                    window.requestAnimationFrame(w)
                }
            }, e())
        }
    }
]);
//# sourceMappingURL=2428.f11352a7b64816d1.js.map