google.maps.__gjsload__('marker', function(_) {
    var gPa = function(a, b) {
            const c = _.Ba(b);
            a.Dg.set(c, b);
            _.Xp(a.Eg)
        },
        hPa = function(a, b) {
            if (a.Eg.has(b)) {
                _.Im(b, "UPDATE_BASEMAP_COLLISION");
                _.Im(b, "UPDATE_MARKER_COLLISION");
                _.Im(b, "REMOVE_COLLISION");
                a.Eg.delete(b);
                var c = a.Fg;
                const d = _.Ba(b);
                c.Dg.has(d) && (c.Dg.delete(d), b.Qn = null, _.Xp(c.Eg));
                _.$p(a.Dg, b)
            }
        },
        iPa = function(a, b) {
            a.Eg.has(b) || (a.Eg.add(b), _.Em(b, "UPDATE_BASEMAP_COLLISION", () => {
                    a.Gg.add(b);
                    a.Hg.hq()
                }), _.Em(b, "UPDATE_MARKER_COLLISION", () => {
                    a.Hg.hq()
                }), _.Em(b, "REMOVE_COLLISION", () => {
                    hPa(a, b)
                }),
                gPa(a.Fg, b), _.Zp(a.Dg, b))
        },
        jPa = function(a) {
            return `${_.Ol(a)}-internal-${a}`
        },
        kPa = function(a, b) {
            b = (a = a.__e3_) && a[b];
            return !!b && Object.values(b).some(c => c.iC)
        },
        lPa = function(a, b, c) {
            return new _.Dm(a, `${b}${"_removed"}`, c, 0, !1)
        },
        mPa = function(a, b, c) {
            return new _.Dm(a, `${b}${"_added"}`, c, 0, !1)
        },
        wR = function(a) {
            return a ? _.er.has(a) ? _.er.get(a) : a instanceof ShadowRoot ? wR(a.host) : wR(a.parentNode) : null
        },
        nPa = function(a) {
            var b = 1;
            return () => {
                --b || a()
            }
        },
        xR = function(a) {
            return a instanceof _.WD
        },
        yR = function(a) {
            return xR(a) ?
                a.getSize() : a.size
        },
        oPa = function(a, b) {
            if (!(a && b && a.isConnected && b.isConnected)) return !1;
            a = a.getBoundingClientRect();
            b = b.getBoundingClientRect();
            return b.x + b.width < a.x - 0 || b.x > a.x + a.width + 0 || b.y + b.height < a.y - 0 || b.y > a.y + a.height + 0 ? !1 : !0
        },
        pPa = function(a, b) {
            _.hJ().Dg.load(new _.IL(a), c => {
                b(c && c.size)
            })
        },
        qPa = function(a, b) {
            a = a.getBoundingClientRect();
            b = b instanceof Element ? b.getBoundingClientRect() : a;
            return {
                offset: new _.Nn(b.x - a.x, b.y - a.y),
                size: new _.Pn(b.width, b.height)
            }
        },
        rPa = function(a) {
            a = new DOMMatrixReadOnly(a.transform);
            return {
                offsetX: a.m41,
                offsetY: a.m42
            }
        },
        zR = function(a) {
            const b = window.devicePixelRatio || 1;
            return Math.round(a * b) / b
        },
        sPa = function(a, {
            clientX: b,
            clientY: c
        }) {
            const {
                height: d,
                left: e,
                top: f,
                width: g
            } = a.getBoundingClientRect();
            return {
                jh: zR(b - (e + g / 2)),
                kh: zR(c - (f + d / 2))
            }
        },
        tPa = function(a, b) {
            if (!a || !b) return null;
            a = a.getProjection();
            return _.fz(b, a)
        },
        uPa = function(a, b) {
            const c = _.nM(a);
            if (!b || !c) return !1;
            a = Math.abs(c.clientX - b.clientX);
            b = Math.abs(c.clientY - b.clientY);
            return a * a + b * b >= 4
        },
        vPa = function(a, b) {
            const c = [];
            c.push("@-webkit-keyframes ", b, " {\n");
            _.Ob(a.frames, d => {
                c.push(d.time * 100 + "% { ");
                c.push("-webkit-transform: translate3d(" + d.translate[0] + "px,", d.translate[1] + "px,0); ");
                c.push("-webkit-animation-timing-function: ", d.um, "; ");
                c.push("}\n")
            });
            c.push("}\n");
            return c.join("")
        },
        wPa = function(a, b) {
            for (let c = 0; c < a.frames.length - 1; c++) {
                const d = a.frames[c + 1];
                if (b >= a.frames[c].time && b < d.time) return c
            }
            return a.frames.length - 1
        },
        xPa = function(a) {
            if (a.Dg) return a.Dg;
            a.Dg = "_gm" + Math.round(Math.random() * 1E4);
            var b =
                vPa(a, a.Dg);
            AR || (AR = _.Dk("style"), AR.type = "text/css", document.querySelectorAll("HEAD")[0].appendChild(AR));
            b = AR.textContent + b;
            b = _.Jk(b);
            AR.textContent = _.Di(new _.Ci(b));
            return a.Dg
        },
        yPa = function(a) {
            a = a.get("collisionBehavior");
            return a === "REQUIRED_AND_HIDES_OPTIONAL" || a === "OPTIONAL_AND_HIDES_LOWER_PRIORITY"
        },
        zPa = function(a, b, c = !1) {
            if (!b.get("internalMarker")) {
                _.Fn(a, "Om");
                _.M(a, 149055);
                c ? (_.Fn(a, "Wgmk"), _.M(a, 149060)) : a instanceof _.nn ? (_.Fn(a, "Ramk"), _.M(a, 149057)) : a instanceof _.fo && (_.Fn(a, "Svmk"),
                    _.M(a, 149059), a.get("standAlone") && (_.Fn(a, "Ssvmk"), _.M(a, 149058)));
                c = a.get("styles") || [];
                Array.isArray(c) && c.some(e => "stylers" in e) && (_.Fn(a, "Csmm"), _.M(a, 174113));
                yPa(b) && (_.Fn(a, "Mocb"), _.M(a, 149062));
                b.get("anchorPoint") && (_.Fn(a, "Moap"), _.M(a, 149064));
                c = b.get("animation");
                c === 1 && (_.Fn(a, "Moab"), _.M(a, 149065));
                c === 2 && (_.Fn(a, "Moad"), _.M(a, 149066));
                b.get("clickable") === !1 && (_.Fn(a, "Ucmk"), _.M(a, 149091), b.get("title") && (_.Fn(a, "Uctmk"), _.M(a, 149063)));
                b.get("draggable") && (_.Fn(a, "Drmk"), _.M(a, 149069),
                    b.get("clickable") === !1 && (_.Fn(a, "Dumk"), _.M(a, 149070)));
                b.get("visible") === !1 && (_.Fn(a, "Ivmk"), _.M(a, 149081));
                b.get("crossOnDrag") && (_.Fn(a, "Mocd"), _.M(a, 149067));
                b.get("cursor") && (_.Fn(a, "Mocr"), _.M(a, 149068));
                b.get("label") && (_.Fn(a, "Molb"), _.M(a, 149080));
                b.get("title") && (_.Fn(a, "Moti"), _.M(a, 149090));
                b.get("opacity") != null && (_.Fn(a, "Moop"), _.M(a, 149082));
                b.get("optimized") === !0 ? (_.Fn(a, "Most"), _.M(a, 149085)) : b.get("optimized") === !1 && (_.Fn(a, "Mody"), _.M(a, 149071));
                b.get("zIndex") != null && (_.Fn(a,
                    "Mozi"), _.M(a, 149092));
                c = b.get("icon");
                var d = new BR;
                (d = !c || c === d.icon.url || c.url === d.icon.url) ? (_.Fn(a, "Dmii"), _.M(a, 173084)) : (_.Fn(a, "Cmii"), _.M(a, 173083));
                typeof c === "string" ? (_.Fn(a, "Mosi"), _.M(a, 149079)) : c && c.url != null ? (c.anchor && (_.Fn(a, "Moia"), _.M(a, 149074)), c.labelOrigin && (_.Fn(a, "Moil"), _.M(a, 149075)), c.origin && (_.Fn(a, "Moio"), _.M(a, 149076)), c.scaledSize && (_.Fn(a, "Mois"), _.M(a, 149077)), c.size && (_.Fn(a, "Moiz"), _.M(a, 149078))) : c && c.path != null ? (c = c.path, c === 0 ? (_.Fn(a, "Mosc"), _.M(a, 149088)) : c ===
                    1 ? (_.Fn(a, "Mosfc"), _.M(a, 149072)) : c === 2 ? (_.Fn(a, "Mosfo"), _.M(a, 149073)) : c === 3 ? (_.Fn(a, "Mosbc"), _.M(a, 149086)) : c === 4 ? (_.Fn(a, "Mosbo"), _.M(a, 149087)) : (_.Fn(a, "Mosbu"), _.M(a, 149089))) : xR(c) && (_.Fn(a, "Mpin"), _.M(a, 149083));
                b.get("shape") && (_.Fn(a, "Mosp"), _.M(a, 149084), d && (_.Fn(a, "Dismk"), _.M(a, 162762)));
                if (c = b.get("place")) c.placeId ? (_.Fn(a, "Smpi"), _.M(a, 149093)) : (_.Fn(a, "Smpq"), _.M(a, 149094)), b.get("attribution") && (_.Fn(a, "Sma"), _.M(a, 149061))
            }
        },
        BPa = function(a, b, c) {
            APa(a, c, d => {
                a.set(b, d);
                const e = d ? yR(d) :
                    null;
                b === "viewIcon" && d && e && a.Dg && a.Dg(e, d.anchor, d.labelOrigin);
                d = a.get("modelLabel");
                a.set("viewLabel", d ? {
                    text: d.text || d,
                    color: _.vl(d.color, "#000000"),
                    fontWeight: _.vl(d.fontWeight, ""),
                    fontSize: _.vl(d.fontSize, "14px"),
                    fontFamily: _.vl(d.fontFamily, "Roboto,Arial,sans-serif"),
                    className: d.className || ""
                } : null)
            })
        },
        APa = function(a, b, c) {
            b ? xR(b) ? c(b) : b.path != null ? c(a.Eg(b)) : (_.xl(b) || (b.size = b.size || b.scaledSize), b.size ? c(b) : (b.url || (b = {
                url: b
            }), pPa(b.url, d => {
                b.size = d || new _.Pn(24, 24);
                c(b)
            }))) : c(null)
        },
        CPa =
        function(a) {
            const b = a.get("mapPixelBoundsQ");
            var c = a.get("icon");
            const d = a.get("position");
            if (!b || !c || !d) return a.get("visible") !== !1;
            const e = c.anchor || _.jo,
                f = c.size.width + Math.abs(e.x);
            c = c.size.height + Math.abs(e.y);
            return d.x > b.minX - f && d.y > b.minY - c && d.x < b.maxX + f && d.y < b.maxY + c ? a.get("visible") !== !1 : !1
        },
        DPa = function(a, b) {
            a.origin = b;
            _.Xp(a.Eg)
        },
        CR = function(a) {
            a.Dg && (_.dz(a.Dg), a.Dg = null)
        },
        EPa = function(a, b, c) {
            _.jJ(() => {
                a.style.webkitAnimationDuration = c.duration ? c.duration + "ms" : "";
                a.style.webkitAnimationIterationCount =
                    `${c.pm}`;
                a.style.webkitAnimationName = b || ""
            })
        },
        FPa = function() {
            const a = [];
            for (let b = 0; b < DR.length; b++) {
                const c = DR[b];
                c.tick();
                c.Dg || a.push(c)
            }
            DR = a;
            DR.length === 0 && (window.clearInterval(ER), ER = null)
        },
        FR = function(a) {
            return a ? a.__gm_at || _.jo : null
        },
        HPa = function(a, b) {
            var c = 1,
                d = a.animation;
            var e = d.frames[wPa(d, b)];
            var f;
            d = a.animation;
            (f = d.frames[wPa(d, b) + 1]) && (c = (b - e.time) / (f.time - e.time));
            b = FR(a.element);
            d = a.element;
            f ? (c = (0, GPa[e.um || "linear"])(c), e = e.translate, f = f.translate, c = new _.Nn(Math.round(c * f[0] -
                c * e[0] + e[0]), Math.round(c * f[1] - c * e[1] + e[1]))) : c = new _.Nn(e.translate[0], e.translate[1]);
            c = d.__gm_at = c;
            d = c.x - b.x;
            b = c.y - b.y;
            if (d !== 0 || b !== 0) c = a.element, e = new _.Nn(_.gJ(c.style.left) || 0, _.gJ(c.style.top) || 0), e.x += d, e.y += b, _.Vy(c, e);
            _.Tm(a, "tick")
        },
        KPa = function(a, b, c) {
            let d;
            var e;
            if (e = c.GG !== !1) e = _.sq(), e = e.Dg.Kg || e.Dg.Ig && _.Px(e.Dg.version, 7);
            e ? d = new IPa(a, b, c) : d = new JPa(a, b, c);
            d.start();
            return d
        },
        IR = function(a) {
            a.Hg && (GR(a.Qh), a.Hg.release(), a.Hg = null);
            a.Dg && _.dz(a.Dg);
            a.Dg = null;
            a.Gg && _.dz(a.Gg);
            a.Gg =
                null;
            HR(a, !0);
            a.Kg = []
        },
        HR = function(a, b = !1) {
            a.Og ? a.Vg = !0 : (_.Tm(a, b ? "ELEMENTS_REMOVED" : "CLEAR_TARGET"), a.targetElement && _.dz(a.targetElement), a.targetElement = null, a.Ig && (a.Ig.unbindAll(), a.Ig.release(), a.Ig = null, GR(a.Qg), a.Qg = null), a.Lg && a.Lg.remove(), a.Mg && a.Mg.remove())
        },
        MPa = function(a, b) {
            const c = a.ah();
            if (c) {
                var d = c.url != null;
                a.Dg && a.wh == d && (_.dz(a.Dg), a.Dg = null);
                a.wh = !d;
                var e = null;
                d && (e = {
                    rs: () => {}
                });
                a.Dg = JR(a, b, a.Dg, c, e);
                LPa(a, c, KR(a))
            }
        },
        QPa = function(a) {
            var b = a.ih();
            if (b) {
                if (!a.Hg) {
                    const e = a.Hg =
                        new NPa(a.getPanes(), b, a.get("opacity"), a.get("visible"), a.Ai);
                    a.Qh = [_.Em(a, "label_changed", function() {
                        e.setLabel(this.get("label"))
                    }), _.Em(a, "opacity_changed", function() {
                        e.setOpacity(this.get("opacity"))
                    }), _.Em(a, "panes_changed", function() {
                        var f = this.get("panes");
                        e.Al = f;
                        CR(e);
                        _.Xp(e.Eg)
                    }), _.Em(a, "visible_changed", function() {
                        e.setVisible(this.get("visible"))
                    })]
                }
                if (b = a.ah()) {
                    var c = a.Dg,
                        d = KR(a);
                    c = OPa(a, b, d, FR(c) || _.jo);
                    d = yR(b);
                    d = b.labelOrigin || new _.Nn(d.width / 2, d.height / 2);
                    xR(b) && (b = b.getSize().width,
                        d = new _.Nn(b / 2, b / 2));
                    DPa(a.Hg, new _.Nn(c.x + d.x, c.y + d.y));
                    a.Hg.setZIndex(PPa(a));
                    _.Yp(a.Hg.Eg)
                }
            }
        },
        SPa = function(a) {
            if (!a.Ug) {
                a.Fg && (a.Ng && _.Gm(a.Ng), a.Fg.cancel(), a.Fg = null);
                var b = a.get("animation");
                if (b = RPa[b]) {
                    var c = b.options;
                    a.Dg && (a.Ug = !0, a.set("animating", !0), b = KPa(a.Dg, b.icon, c), a.Fg = b, a.Ng = _.Pm(b, "done", function() {
                        a.set("animating", !1);
                        a.Fg = null;
                        a.set("animation", null)
                    }))
                }
            }
        },
        GR = function(a) {
            if (a)
                for (let b = 0, c = a.length; b < c; b++) _.Gm(a[b])
        },
        KR = function(a) {
            return _.sq().transform ? Math.min(1, a.get("scale") ||
                1) : 1
        },
        OPa = function(a, b, c, d) {
            const e = a.getPosition(),
                f = yR(b);
            var g = (b = LR(b)) ? b.x : f.width / 2;
            a.nh.x = e.x + d.x - Math.round(g - (g - f.width / 2) * (1 - c));
            b = b ? b.y : f.height;
            a.nh.y = e.y + d.y - Math.round(b - (b - f.height / 2) * (1 - c));
            return a.nh
        },
        PPa = function(a) {
            let b = a.get("zIndex");
            a.Nk && (b = 1E6);
            _.tl(b) || (b = Math.min(a.getPosition().y, 999999));
            return b
        },
        LR = function(a) {
            return xR(a) ? a.getAnchor() : a.anchor
        },
        LPa = function(a, b, c) {
            const d = yR(b);
            a.Tg.width = c * d.width;
            a.Tg.height = c * d.height;
            a.set("size", a.Tg);
            const e = a.get("anchorPoint");
            if (!e || e.Dg) b = LR(b), a.Pg.x = c * (b ? d.width / 2 - b.x : 0), a.Pg.y = -c * (b ? b.y : d.height), a.Pg.Dg = !0, a.set("anchorPoint", a.Pg)
        },
        JR = function(a, b, c, d, e) {
            if (xR(d)) a = TPa(a, b, c, d);
            else if (d.url != null) {
                const f = d.origin || _.jo;
                a = a.get("opacity");
                const g = _.vl(a, 1);
                c ? (c.firstChild.__src__ != d.url && _.KL(c.firstChild, d.url), _.ML(c, d.size, f, d.scaledSize), c.firstChild.style.opacity = `${g}`) : (e = e || {}, e.dA = !_.oq.Jg, e.alpha = !0, e.opacity = a, c = _.LL(d.url, null, f, d.size, null, d.scaledSize, e), c.style.display = "none", b.appendChild(c));
                a =
                    c
            } else b = c || _.Wy("div", b), b.textContent = "", c = _.Gr(), e = _.Ry(b).createElement("canvas"), e.width = d.size.width * c, e.height = d.size.height * c, e.style.width = _.Bl(d.size.width), e.style.height = _.Bl(d.size.height), _.tq(b, d.size), b.appendChild(e), _.Vy(e, _.jo), _.wq(e), e = e.getContext("2d"), e.lineCap = e.lineJoin = "round", e.scale(c, c), c = new _.mBa(e), e.beginPath(), c.Hh(d.LF, d.anchor.x, d.anchor.y, d.rotation || 0, d.scale), d.fillOpacity && (e.fillStyle = d.fillColor, e.globalAlpha = d.fillOpacity, e.fill()), d.strokeWeight && (e.lineWidth =
                d.strokeWeight, e.strokeStyle = d.strokeColor, e.globalAlpha = d.strokeOpacity, e.stroke()), _.qJ(b, _.vl(a.get("opacity"), 1)), a = b;
            c = a;
            c.Ux = d;
            return c
        },
        UPa = function(a, b) {
            a.Lg && a.Mg && a.th == b || (a.th = b, a.Lg && a.Lg.remove(), a.Mg && a.Mg.remove(), a.Lg = _.Kz(b, {
                Dk: function(c) {
                    a.Og++;
                    _.vz(c);
                    _.Tm(a, "mousedown", c.Dg)
                },
                Ok: function(c) {
                    a.Og--;
                    !a.Og && a.Vg && _.iJ(this, function() {
                        a.Vg = !1;
                        HR(a);
                        _.Yp(a.Jg)
                    }, 0);
                    _.xz(c);
                    _.Tm(a, "mouseup", c.Dg)
                },
                Ul: ({
                    event: c,
                    Gq: d
                }) => {
                    if (a.get("clickable") !== !1 || a.getDraggable()) _.By(c.Dg), c.button ==
                        3 ? d || c.button == 3 && _.Tm(a, "rightclick", c.Dg) : d ? _.Tm(a, "dblclick", c.Dg) : (_.Tm(a, "click", c.Dg), _.Fn(window, "Mmi"), _.M(window, 171150))
                },
                Nt: c => {
                    _.yz(c);
                    _.Tm(a, "contextmenu", c.Dg)
                }
            }), a.Mg = new _.wD(b, b, {
                ss: function(c) {
                    _.Tm(a, "mouseout", c)
                },
                vs: function(c) {
                    _.Tm(a, "mouseover", c)
                }
            }))
        },
        TPa = function(a, b, c, d) {
            c = c || _.Wy("div", b);
            _.ar(c);
            b === a.getPanes().overlayMouseTarget ? (b = d.element.cloneNode(!0), _.qJ(b, 0), c.appendChild(b)) : c.appendChild(d.element);
            b = d.getSize();
            c.style.width = b.width + (b.Eg || "px");
            c.style.height =
                b.height + (b.Dg || "px");
            c.style.pointerEvents = "none";
            c.style.userSelect = "none";
            _.Pm(d, "changed", () => {
                a.Eg()
            });
            return c
        },
        MR = function(a) {
            const b = a.marker.get("place");
            a = a.marker.get("position");
            return b && b.location || a
        },
        NR = function(a, b) {
            a.Gg && a.Gg.has(b) && ({
                marker: a
            } = a.Gg.get(b), b.Am = VPa(a), b.Am && (b = a.getMap())) && (_.Fn(b, "Mwfl"), _.M(b, 184438))
        },
        XPa = function(a, b) {
            if (a.Gg) {
                var {
                    rE: c,
                    marker: d
                } = a.Gg.get(b);
                for (const e of WPa) c.push(mPa(d, e, () => {
                    NR(a, b)
                })), c.push(lPa(d, e, () => {
                    !VPa(d) && b.Am && NR(a, b)
                }))
            }
        },
        YPa =
        function(a) {
            const b = a.Eg.__gm;
            a.Dg.bindTo("mapPixelBounds", b, "pixelBounds");
            a.Dg.bindTo("panningEnabled", a.Eg, "draggable");
            a.Dg.bindTo("panes", b)
        },
        ZPa = function(a) {
            const b = a.Eg.__gm;
            _.Em(a.Lg, "dragging_changed", () => {
                b.set("markerDragging", a.marker.get("dragging"))
            });
            b.set("markerDragging", b.get("markerDragging") || a.marker.get("dragging"))
        },
        aQa = function(a) {
            a.Ig.push(_.Sm(a.Dg, "panbynow", a.Eg.__gm));
            $Pa.forEach(b => {
                a.Ig.push(_.Em(a.Dg, b, c => {
                    const d = a.Mg ? MR(a) : a.marker.get("internalPosition");
                    c = new _.xD(d,
                        c, a.Dg.get("position"));
                    _.Tm(a.marker, b, c)
                }))
            })
        },
        bQa = function(a) {
            const b = () => {
                a.marker.get("place") ? a.Dg.set("draggable", !1) : a.Dg.set("draggable", !!a.marker.get("draggable"))
            };
            a.Ig.push(_.Em(a.Lg, "draggable_changed", b));
            a.Ig.push(_.Em(a.Lg, "place_changed", b));
            b()
        },
        cQa = function(a) {
            a.Ig.push(_.Em(a.Eg, "projection_changed", () => {
                OR(a)
            }));
            a.Ig.push(_.Em(a.Lg, "position_changed", () => {
                OR(a)
            }));
            a.Ig.push(_.Em(a.Lg, "place_changed", () => {
                OR(a)
            }))
        },
        eQa = function(a) {
            a.Ig.push(_.Em(a.Dg, "dragging_changed", () => {
                if (a.Dg.get("dragging")) a.Pg =
                    a.Hg.jn(), a.Pg && _.RM(a.Hg, a.Pg);
                else {
                    a.Pg = null;
                    a.Og = null;
                    var b = a.Hg.getPosition();
                    if (b && (b = _.Qr(b, a.Eg.get("projection")), b = dQa(a, b))) {
                        const c = _.fz(b, a.Eg.get("projection"));
                        a.marker.get("place") || (a.Ng = !1, a.marker.set("position", b), a.Ng = !0);
                        a.Hg.setPosition(c)
                    }
                }
            }));
            a.Ig.push(_.Em(a.Dg, "deltaclientposition_changed", () => {
                var b = a.Dg.get("deltaClientPosition");
                if (b && (a.Pg || a.Og)) {
                    var c = a.Og || a.Pg;
                    a.Og = {
                        clientX: c.clientX + b.clientX,
                        clientY: c.clientY + b.clientY
                    };
                    b = a.Yg.Ql(a.Og);
                    b = _.Qr(b, a.Eg.get("projection"));
                    c = a.Og;
                    var d = dQa(a, b);
                    d && (a.marker.get("place") || (a.Ng = !1, a.marker.set("position", d), a.Ng = !0), d.equals(b) || (b = _.fz(d, a.Eg.get("projection")), c = a.Hg.jn(b)));
                    c && _.RM(a.Hg, c)
                }
            }))
        },
        fQa = function(a) {
            if (a.Fg) {
                a.Dg.bindTo("scale", a.Fg);
                a.Dg.bindTo("position", a.Fg, "pixelPosition");
                const b = a.Eg.__gm;
                a.Fg.bindTo("latLngPosition", a.marker, "internalPosition");
                a.Fg.bindTo("focus", a.Eg, "position");
                a.Fg.bindTo("zoom", b);
                a.Fg.bindTo("offset", b);
                a.Fg.bindTo("center", b, "projectionCenterQ");
                a.Fg.bindTo("projection",
                    a.Eg)
            }
        },
        hQa = function(a) {
            if (a.Fg) {
                const b = new gQa(a.Eg instanceof _.fo);
                b.bindTo("internalPosition", a.Fg, "latLngPosition");
                b.bindTo("place", a.marker);
                b.bindTo("position", a.marker);
                b.bindTo("draggable", a.marker);
                a.Dg.bindTo("draggable", b, "actuallyDraggable")
            }
        },
        OR = function(a) {
            if (a.Ng) {
                var b = MR(a);
                b && a.Hg.setPosition(_.fz(b, a.Eg.get("projection")))
            }
        },
        dQa = function(a, b) {
            const c = a.Eg.__gm.get("snappingCallback");
            return c && (a = c({
                latLng: b,
                overlay: a.marker
            })) ? a : b
        },
        VPa = function(a) {
            return WPa.some(b => kPa(a, b))
        },
        jQa = function(a, b, c) {
            if (b instanceof _.nn) {
                const d = b.__gm;
                Promise.all([d.Eg, d.Fg]).then(([{
                    Yg: e
                }, f]) => {
                    iQa(a, b, c, e, f)
                })
            } else iQa(a, b, c, null)
        },
        iQa = function(a, b, c, d, e = !1) {
            const f = new Map,
                g = h => {
                    var l = b instanceof _.nn;
                    const n = l ? h.__gm.Sq.map : h.__gm.Sq.streetView,
                        p = n && n.Eg === b,
                        r = p !== a.contains(h);
                    n && r && (l ? (h.__gm.Sq.map.dispose(), h.__gm.Sq.map = null) : (h.__gm.Sq.streetView.dispose(), h.__gm.Sq.streetView = null));
                    !a.contains(h) || !l && h.get("mapOnly") || p || (b instanceof _.nn ? (l = b.__gm, h.__gm.Sq.map = new kQa(h,
                        b, c, _.IM(l, h), d, l.Rg, f)) : h.__gm.Sq.streetView = new kQa(h, b, c, _.hk, null, null, null), zPa(b, h, e))
                };
            _.Em(a, "insert", g);
            _.Em(a, "remove", g);
            a.forEach(g)
        },
        PR = function(a) {
            return _.Gr() / (a.webkitBackingStorePixelRatio || a.mozBackingStorePixelRatio || a.msBackingStorePixelRatio || a.oBackingStorePixelRatio || a.backingStorePixelRatio || 1)
        },
        lQa = function(a, b, c) {
            a = a.Dg;
            a.width = b;
            a.height = c;
            return a
        },
        mQa = function(a) {
            const b = [];
            a.Pi.forEach(c => {
                b.push(c)
            });
            b.sort((c, d) => c.zIndex - d.zIndex);
            return b
        },
        nQa = function(a) {
            const b =
                mQa(a),
                c = a.getContext(),
                d = PR(c);
            a = a.Bh.size;
            c.clearRect(0, 0, Math.ceil(a.jh * d), Math.ceil(a.kh * d));
            b.forEach(e => {
                c.globalAlpha = _.vl(e.opacity, 1);
                c.drawImage(e.image, e.Oy, e.Py, e.Ny, e.Jy, Math.round(e.dx * d), Math.round(e.dy * d), e.tq * d, e.qq * d)
            })
        },
        oQa = function(a, b, c) {
            if (c.dx > a || c.dy > b || c.dx + c.tq < a || c.dy + c.qq < b) a = !1;
            else a: {
                var d = c.Jv.shape;a -= c.dx;b -= c.dy;
                if (!d) throw Error("Shape cannot be null.");c = d.coords || [];
                switch (d.type.toLowerCase()) {
                    case "rect":
                        a = c[0] <= a && a <= c[2] && c[1] <= b && b <= c[3];
                        break a;
                    case "circle":
                        d =
                            c[2];
                        a -= c[0];
                        b -= c[1];
                        a = a * a + b * b <= d * d;
                        break a;
                    default:
                        d = c, c = d.length, d[0] == d[c - 2] && d[1] == d[c - 1] || d.push(d[0], d[1]), a = _.Tza(a, b, d) != 0
                }
            }
            return a
        },
        QR = function(a, b, c, d) {
            var e = b.wi,
                f = a.Eg.get();
            if (!f) return null;
            f = f.Bh.size;
            c = _.SM(a.Fg, e, new _.Nn(c, d));
            if (!c) return null;
            a = new _.Nn(c.vt.qh * f.jh, c.vt.rh * f.kh);
            const g = [];
            c.nk.Pi.forEach(h => g.push(h));
            g.sort((h, l) => l.zIndex - h.zIndex);
            c = null;
            for (e = 0; d = g[e]; ++e)
                if (f = d.Jv, f.clickable !== !1 && (f = f.Uz, oQa(a.x, a.y, d))) {
                    c = f;
                    break
                }
            c && (b.sj = d);
            return c
        },
        qQa = function(a,
            b) {
            if (!b.RA) {
                b.RA = !0;
                var c = _.Pr(a.get("projection")),
                    d = b.Dt;
                d.dx < -64 || d.dy < -64 || d.dx + d.tq > 64 || d.dy + d.qq > 64 ? (_.cq(a.Gg, b), d = a.Fg.search(_.lu)) : (d = b.latLng, d = new _.Nn(d.lat(), d.lng()), b.wi = d, _.LM(a.Hg, {
                    wi: d,
                    marker: b
                }), d = _.Pza(a.Fg, d));
                for (let f = 0, g = d.length; f < g; ++f) {
                    var e = d[f];
                    const h = e.nk || null;
                    if (e = pQa(a, h, e.zG || null, b, c)) b.Pi[_.Wm(e)] = e, _.cq(h.Pi, e)
                }
            }
        },
        rQa = function(a, b) {
            b.RA && (b.RA = !1, a.Gg.contains(b) ? a.Gg.remove(b) : a.Hg.remove({
                wi: b.wi,
                marker: b
            }), _.ol(b.Pi, (c, d) => {
                delete b.Pi[c];
                d.nk.Pi.remove(d)
            }))
        },
        sQa = function(a, b) {
            a.Ig[_.Wm(b)] = b;
            var c = {
                qh: b.si.x,
                rh: b.si.y,
                Ah: b.zoom
            };
            const d = _.Pr(a.get("projection"));
            var e = _.nA(a.Eg, c);
            e = new _.Nn(e.Dg, e.Eg);
            const {
                min: f,
                max: g
            } = _.PI(a.Eg, c, 64 / a.Eg.size.jh);
            c = _.Co(f.Dg, f.Eg, g.Dg, g.Eg);
            _.Sza(c, d, e, (h, l) => {
                h.zG = l;
                h.nk = b;
                b.ap[_.Wm(h)] = h;
                _.JM(a.Fg, h);
                l = (a.Hg.search(h) || []).map(n => n.marker);
                a.Gg.forEach((0, _.Ca)(l.push, l));
                for (let n = 0, p = l.length; n < p; ++n) {
                    const r = l[n],
                        u = pQa(a, b, h.zG, r, d);
                    u && (r.Pi[_.Wm(u)] = u, _.cq(b.Pi, u))
                }
            });
            b.div && b.Pi && a.Kg(b.div, b.Pi)
        },
        tQa = function(a,
            b) {
            b && (delete a.Ig[_.Wm(b)], b.Pi.forEach(function(c) {
                b.Pi.remove(c);
                delete c.Jv.Pi[_.Wm(c)]
            }), _.ol(b.ap, (c, d) => {
                a.Fg.remove(d)
            }))
        },
        pQa = function(a, b, c, d, e) {
            if (!e || !c || !d.latLng) return null;
            var f = e.fromLatLngToPoint(c);
            c = e.fromLatLngToPoint(d.latLng);
            e = a.Eg.size;
            a = _.pva(a.Eg, new _.Kq(c.x, c.y), new _.Kq(f.x, f.y), b.zoom);
            c.x = a.qh * e.jh;
            c.y = a.rh * e.kh;
            a = d.zIndex;
            _.tl(a) || (a = c.y);
            a = Math.round(a * 1E3) + _.Wm(d) % 1E3;
            f = d.Dt;
            b = {
                image: f.image,
                Oy: f.Oy,
                Py: f.Py,
                Ny: f.Ny,
                Jy: f.Jy,
                dx: f.dx + c.x,
                dy: f.dy + c.y,
                tq: f.tq,
                qq: f.qq,
                zIndex: a,
                opacity: d.opacity,
                nk: b,
                Jv: d
            };
            return b.dx > e.jh || b.dy > e.kh || b.dx + b.tq < 0 || b.dy + b.qq < 0 ? null : b
        },
        uQa = function(a, b, c) {
            a.Gg++ < 4 ? c ? a.Eg.lD(b) : a.Eg.XL(b) : a.Dg = !0;
            a.fo || (a.fo = _.jJ((0, _.Ca)(a.Fg, a)))
        },
        vQa = function(a) {
            return typeof a === "string" ? (RR.has(a) || RR.set(a, {
                url: a
            }), RR.get(a)) : a
        },
        BQa = function(a, b, c) {
            const d = new _.bq,
                e = new _.bq,
                f = new wQa;
            new xQa(a, d, new BR, f, c);
            const g = _.Ry(b.getDiv()).createElement("canvas"),
                h = {};
            a = _.Co(-100, -300, 100, 300);
            const l = new _.uN(a);
            a = _.Co(-90, -180, 90, 180);
            const n = _.Rza(a,
                (x, y) => x.marker === y.marker);
            let p = null,
                r = null;
            const u = new _.co(null),
                w = b.__gm;
            w.Eg.then(x => {
                w.Jg.register(new yQa(h, w, u, x.Yg.zj));
                _.Nx(x.Ar, y => {
                    if (y && p !== y.Bh) {
                        r && r.unbindAll();
                        var D = p = y.Bh;
                        r = new zQa(h, d, e, function(I, L) {
                            return new AQa(L, new SR(I, L, g, D), I)
                        }, l, n, p);
                        r.bindTo("projection", b);
                        u.set(r.Dg())
                    }
                })
            });
            _.TM(b, u, "markerLayer", -1)
        },
        DQa = function(a) {
            a.fo || (a.fo = _.jJ(() => {
                a.fo = 0;
                const b = a.Uu;
                a.Uu = {};
                const c = a.Zv;
                for (const d of Object.values(b)) CQa(a, d);
                c && !a.Zv && a.st.forEach(d => {
                    CQa(a, d)
                })
            }))
        },
        CQa =
        function(a, b) {
            var c = b.get("place");
            c = c ? c.location : b.get("position");
            b.set("internalPosition", c);
            b.changed = a.qL;
            if (!b.get("animating"))
                if (a.mC.remove(b), !c || b.get("visible") == 0 || b.__gm && b.__gm.Qn) a.st.remove(b);
                else {
                    a.Zv && !a.sE && a.st.getSize() >= 256 && (a.Zv = !1);
                    c = b.get("optimized");
                    const e = b.get("draggable"),
                        f = !!b.get("animation");
                    var d = b.get("icon");
                    const g = !!d && d.path != null;
                    d = xR(d);
                    const h = b.get("label") != null;
                    a.sE || c == 0 || e || f || g || d || h || !c && a.Zv ? _.cq(a.st, b) : (a.st.remove(b), _.cq(a.mC, b))
                }
        },
        EQa = function(a,
            b) {
            const c = new _.zp;
            c.onAdd = () => {};
            c.onContextLost = () => {};
            c.onRemove = () => {};
            c.onContextRestored = () => {};
            c.onDraw = ({
                transformer: d
            }) => {
                a.onDraw(d)
            };
            _.dv.add(c);
            c.setMap(b);
            return c
        },
        FQa = function(a) {
            a.Jg || (a.Jg = setTimeout(() => {
                const b = [...a.Gg].filter(c => !c.Po).length;
                b > 0 && a.ui.Tg(a.map, b);
                a.Jg = 0
            }, 0))
        },
        IQa = function(a, b) {
            a.Hg.has(b) || (a.Hg.add(b), _.SA(_.RA(), () => {
                if (a.map) {
                    var c = [];
                    for (const d of a.Hg) {
                        if (!d.map) continue;
                        const e = d.targetElement;
                        e.parentNode || c.push(d);
                        const f = d.Qn !== !1 && TR(d) || d.Gt,
                            g =
                            _.on(a.map);
                        g || (a.Kg || (a.Kg = a.Eg.attachShadow({
                            mode: _.mq[166] ? "open" : "closed"
                        }), a.Kg.append(a.Ig, a.Dg)), a.Eg.append(e));
                        g && e.parentElement === g || !g && e.parentElement === a.Eg ? e.setAttribute("slot", f ? GQa : HQa) : e.style.visibility = f ? "hidden" : "";
                        d.Zl(!f);
                        d.Lv = !1
                    }
                    a.Hg.clear();
                    for (const d of c) d.Ty(!0)
                }
            }))
        },
        JQa = function(a) {
            UR || (UR = new ResizeObserver(b => {
                for (const c of b) c.target.dispatchEvent(new CustomEvent("resize", {
                    detail: c.contentRect
                }))
            }));
            UR.observe(a)
        },
        MQa = function(a, b) {
            const c = _.Ba(b);
            let d = VR.get(c);
            d || (d = new KQa(b), VR.set(c, d));
            b = d;
            LQa(a, b.Xm);
            b.Gg.add(a);
            FQa(b);
            JQa(a.targetElement)
        },
        NQa = function(a) {
            a = _.Ba(a);
            (a = VR.get(a)) && a.requestRedraw()
        },
        OQa = function(a, b) {
            b = _.Ba(b);
            (b = VR.get(b)) && IQa(b, a)
        },
        PQa = function(a) {
            let b = 0,
                c = 0;
            for (const d of a) switch (d) {
                case "ArrowLeft":
                    --b;
                    break;
                case "ArrowRight":
                    b += 1;
                    break;
                case "ArrowDown":
                    c += 1;
                    break;
                case "ArrowUp":
                    --c
            }
            return {
                deltaX: b,
                deltaY: c
            }
        },
        YR = function(a, b, c = !0) {
            a.Dg.position = a.Og;
            WR(a, b, c)
        },
        WR = function(a, b, c = !0) {
            b.preventDefault();
            b.stopImmediatePropagation();
            ZR(a);
            QQa(a);
            a.Fg && (a.Fg.release(), a.Fg = null);
            c && $R(a.Dg, "dragend", b)
        },
        SQa = function(a) {
            a.Eg.style.display = "none";
            a.Eg.style.opacity = "0.5";
            a.Eg.style.position = "absolute";
            a.Eg.style.left = "50%";
            a.Eg.style.transform = "translate(-50%, -50%)";
            a.Eg.style.zIndex = "-1";
            RQa(a);
            const b = a.Dg.Eo;
            b.addEventListener("pointerenter", a.Sg);
            b.addEventListener("pointerleave", a.Ug);
            b.addEventListener("focus", a.Sg);
            b.addEventListener("blur", a.Ug)
        },
        TQa = function(a, b = !1) {
            return a.Gg ? _.bB : b ? "pointer" : _.aia
        },
        aS = function(a) {
            const b =
                a.Dg.lj;
            b && b.appendChild(a.Eg)
        },
        RQa = function(a) {
            a.Eg.children[0] ? .remove();
            var b = a.Dg,
                c;
            if (!(c = b.dragIndicator)) {
                if (!b.Ru) {
                    const {
                        url: d,
                        scaledSize: e
                    } = (new BR).Dg;
                    b.Ru = new Image(e.width, e.height);
                    b.Ru.src = d;
                    b.Ru.alt = ""
                }
                c = b.Ru
            }
            a.Eg.appendChild(c);
            aS(a)
        },
        VQa = function(a) {
            if (!a.Dg.Nz) {
                a.Fg = new _.vN((c, d) => {
                    var e = a.Dg;
                    e.Gh && _.Tm(e.Gh, "panbynow", c, d)
                });
                _.QM(a.Fg, !0);
                var b = UQa(a.Dg);
                _.PM(a.Fg, b);
                a.Fg.Gg = a.Hg
            }
        },
        WQa = function(a, b) {
            ZR(a);
            a.Hg = !1;
            a.Fg && (a.Fg.Gg = !1);
            a.Ig = a.Dg.jn();
            a.Mg = _.nM(b)
        },
        XQa = function(a,
            b) {
            var c = _.nM(b);
            if (c) {
                b = c.clientX;
                c = c.clientY;
                var d = b - a.Mg.clientX,
                    e = c - a.Mg.clientY;
                a.Mg = {
                    clientX: b,
                    clientY: c
                };
                b = {
                    clientX: a.Ig.clientX + d,
                    clientY: a.Ig.clientY + e
                };
                a.Ig = b;
                a.Dg.bC(b)
            }
        },
        YQa = function(a, b) {
            a.Ig = a.Dg.jn();
            a.Og = a.Dg.position;
            a.Mg = _.nM(b);
            a.Gg = !0;
            VQa(a);
            a.Dg.Eo.setAttribute("aria-grabbed", "true");
            bS(a.Dg);
            a.Dg.Eo.style.zIndex = "2147483647";
            a.Eg.style.opacity = "1";
            a.Eg.style.display = "";
            $R(a.Dg, "dragstart", b)
        },
        ZQa = function(a) {
            a.Hg && (a.Ig = a.Dg.jn())
        },
        cS = function(a) {
            _.Jz !== 2 ? (document.removeEventListener("pointermove",
                a.Qg), document.removeEventListener("pointerup", a.Jg), document.removeEventListener("pointercancel", a.Jg)) : (document.removeEventListener("touchmove", a.Qg, {
                passive: !1
            }), document.removeEventListener("touchend", a.Jg), document.removeEventListener("touchcancel", a.Jg), document.removeEventListener("touchstart", a.Jg));
            ZR(a);
            QQa(a);
            a.Fg && (a.Fg.release(), a.Fg = null)
        },
        ZR = function(a) {
            const b = a.Dg.Eo;
            b.removeEventListener("keydown", a.mh);
            b.removeEventListener("keyup", a.th);
            b.removeEventListener("blur", a.nh)
        },
        $Qa =
        function(a) {
            if (a.Pg.size === 0) a.Ng = 0;
            else {
                var {
                    deltaX: b,
                    deltaY: c
                } = PQa(a.Pg), d = 1;
                _.vM(a.Vg) && (d = a.Vg.next());
                var e = Math.round(3 * d * b);
                d = Math.round(3 * d * c);
                e === 0 && (e = b);
                d === 0 && (d = c);
                e = {
                    clientX: a.Ig.clientX + e,
                    clientY: a.Ig.clientY + d
                };
                a.Ig = e;
                a.Dg.bC(e);
                a.Ng = window.setTimeout(() => {
                    $Qa(a)
                }, 10)
            }
        },
        QQa = function(a) {
            a.Gg = !1;
            a.Hg = !1;
            a.Mg = null;
            a.Ig = null;
            clearTimeout(a.Ng);
            a.Ng = 0;
            a.Og = null;
            a.Tg = null;
            a.Lg = null;
            const b = a.Dg.Eo,
                c = a.Dg.zIndex;
            a.Eg.style.opacity = "0.5";
            b.setAttribute("aria-grabbed", "false");
            b.style.zIndex =
                c == null ? "" : `${c}`;
            aRa(a.Dg)
        },
        LQa = function(a, b) {
            a.Qz = b;
            if (a.qu) {
                var c = a.getAttribute("aria-describedby");
                c = c ? c.split(" ") : [];
                c.push(b);
                a.setAttribute("aria-describedby", c.join(" "))
            }
        },
        TR = function(a) {
            return a.collisionBehavior !== "REQUIRED" && !a.Nk && !!a.map && !!a.position
        },
        UQa = function(a) {
            return a.Gh ? a.Gh.get("pixelBounds") : null
        },
        $R = function(a, b, c) {
            _.Tm(a, b, new _.xD(a.So, c, a.Dv ? new _.Nn(a.Dv.jh, a.Dv.kh) : null))
        },
        bS = function(a) {
            _.Tm(a, "REMOVE_COLLISION")
        },
        aRa = function(a) {
            a.style.cursor = a.Ri ? TQa(a.Ri, a.wv) :
                a.wv ? "pointer" : ""
        },
        dS = function(a, b = !1) {
            TR(a) && (a.Gh && iPa(a.Gh.Vg, a), _.Tm(a, "UPDATE_MARKER_COLLISION"), b && a.Fw && _.Tm(a, "UPDATE_BASEMAP_COLLISION"))
        },
        bRa = function(a) {
            a.iu.then(() => {
                _.Un(a, "marker-view");
                a.style.position = "absolute";
                a.style.left = "0px"
            })
        },
        eS = function(a) {
            a.style.pointerEvents = a.by ? "none" : a.XE ? "auto" : ""
        },
        fS = function(a) {
            a.Am = a.wv || !!a.qu
        },
        cRa = function(a) {
            !a.fk && a.map && a.Gh && (a.hC = !0, a.fk = _.Kz(a, {
                Ul: ({
                    event: b,
                    Gq: c
                }) => {
                    a.XE ? (_.By(b.Dg), b.button === 3 || c || a.Nu(b.Dg)) : a === b.Dg.target || a.by || (console.debug('To make AdvancedMarkerElement clickable and provide better accessible experiences, use addListener() to register a "click" event on the AdvancedMarkerElement instance.'),
                        a.ui.Mg(a.map))
                }
            }), a.eA = _.hia({
                draggable: a.JE,
                XD: new _.OD(a.map, "gestureHandling"),
                Bk: a.Gh.ul
            }), _.Nx(a.eA, a.vE), a.hC = !1)
        },
        gS = function(a) {
            const b = c => c.nodeType === Node.TEXT_NODE && c.nodeValue != null && !/\S/.test(c.nodeValue);
            return a.childNodes.length > 0 ? ([...a.childNodes].every(b) && _.ym(_.qp(a, "AdvancedMarkerElement is displaying empty text content. If you want a pin to appear, make sure to remove any whitespace between the <gmp-advanced-marker> tags.")), [...a.childNodes]) : a.Vk && a.Vk.contains(a.ql) ? [a.ql] : []
        },
        dRa = function(a, b, c) {
            if (b && c && ({
                    altitude: b
                } = new _.So(b), b > 0 || b < 0)) throw a.ui.Ng(window), _.Ul("Draggable AdvancedMarkerElement with non-zero altitude is not supported");
        },
        hS = function(a) {
            if (a.Rj) {
                const b = _.Ba(a.Rj),
                    c = VR.get(b);
                c && (c.Gg.delete(a), c.isEmpty() && (c.dispose(), VR.delete(b)));
                UR && UR.unobserve(a.targetElement);
                _.Tm(a, "REMOVE_FOCUS");
                _.Tm(a, "REMOVE_COLLISION");
                a.Yg && (a.Oj && (a.Yg.Cl(a.Oj), a.Oj = null), a.Yg = null);
                a.Ri && cS(a.Ri);
                a.BD ? .remove();
                a.UG ? .remove();
                a.PF ? .remove();
                a.rF ? .remove();
                a.eA ? .removeListener(a.vE);
                a.fk && (a.fk.remove(), a.fk = null);
                a.Eq.set("map", null);
                a.Fw = null;
                a.Gh = null;
                a.Rj = null;
                a.Lv = !0
            }
        },
        iS = function(a) {
            if (a.Gh && !a.Nk) {
                var b = a.Gh.Rg;
                b && (a.Am && a.Up && !a.Qn ? b.Vg(a) : _.Tm(a, "REMOVE_FOCUS"))
            }
        },
        fRa = function(a) {
            var b = a.Gh.get("baseMapType");
            b = b && (!b.mapTypeId || !Object.values(_.et).includes(b.mapTypeId));
            a.Fw = a.ZE && !b;
            if (!a.Po || a.position) a.Fw ? NQa(a.map) : eRa(a)
        },
        gRa = function(a) {
            if (!a.Po) {
                var b = a.Gh.Dg;
                b.cB.then(() => {
                    const c = _.up(b, "ADVANCED_MARKERS");
                    if (!c.isAvailable) {
                        a.Gh && a.Gh.xh();
                        for (const d of c.Dg) b.log(d);
                        a.ui.Lg(a.map);
                        a.dispose()
                    }
                })
            }
        },
        hRa = function(a) {
            a.ui.Sg(a.map);
            a.ui.Gg(a.map, a.by);
            if (a.wv) {
                const b = _.Fm(a, "gmp-click");
                a.ui.Eg(a.map, b)
            }
            a.gmpDraggable && a.ui.Hg(a.map);
            a.title && a.ui.Ig(a.map);
            a.zIndex !== null && a.ui.Jg(a.map);
            a.rl() > 0 && a.ui.Dg(a.map);
            a.ui.Fg(a.map, a.collisionBehavior)
        },
        jS = function(a, b) {
            a.Up = b;
            a.Ri && ZQa(a.Ri);
            a.Eq.set("pixelPosition", b);
            if (b) {
                a.style.transform = `translate(-50%, -100%) translate(${b.x}px, ${b.y}px)`;
                const c = a.style.willChange ? a.style.willChange.replace(/\s+/g, "").split(",") : [];
                c.includes("transform") || _.SA(_.RA(), () => {
                    c.push("transform");
                    a.style.willChange = c.join(",")
                }, a, a)
            }
            iS(a)
        },
        eRa = function(a) {
            var b = tPa(a.Rj, a.So);
            a.Oj ? a.Oj.setPosition(b, a.rl()) : a.Yg && (b = new _.xN(a.Yg.zj, a, b, a.Yg, null, a.rl(), a.DJ), a.Yg.Ni(b), a.Oj = b)
        };
    _.Nn.prototype.oy = _.ca(16, function() {
        return Math.sqrt(this.x * this.x + this.y * this.y)
    });
    var WPa = ["click", "dblclick", "rightclick", "contextmenu"],
        iRa = class extends _.Xm {
            constructor() {
                super();
                this.constraint = 0;
                this.Dg = !1
            }
            position_changed() {
                this.Dg || (this.Dg = !0, this.set("rawPosition", this.get("position")), this.Dg = !1)
            }
            rawPosition_changed() {
                if (!this.Dg) {
                    this.Dg = !0;
                    var a = this.set,
                        b;
                    var c = this.get("rawPosition");
                    if (c) {
                        (b = this.get("snappingCallback")) && (c = b(c));
                        b = c.x;
                        c = c.y;
                        var d = this.get("referencePosition");
                        d && (this.constraint === 2 ? b = d.x : this.constraint === 1 && (c = d.y));
                        b = new _.Nn(b, c)
                    } else b = null;
                    a.call(this, "position", b);
                    this.Dg = !1
                }
            }
        },
        jRa = class {
            constructor(a, b, c, d, e = 0, f = 0) {
                this.width = c;
                this.height = d;
                this.offsetX = e;
                this.offsetY = f;
                this.Eg = new Float64Array(2);
                this.Eg[0] = a;
                this.Eg[1] = b;
                this.Dg = new Float32Array(2)
            }
            transform(a) {
                a.gu(1, this.Eg, this.Dg, 0, 0, 0);
                this.Dg[0] += this.offsetX;
                this.Dg[1] += this.offsetY
            }
            isVisible(a) {
                return this.Dg[0] >= -this.width && this.Dg[0] <= a.width + this.width && this.Dg[1] >= -this.height && this.Dg[1] <= a.height + this.height
            }
            equals(a) {
                return this.Eg[0] === a.Eg[0] && this.Eg[1] === a.Eg[1] &&
                    this.width === a.width && this.height === a.height && this.offsetX === a.offsetX && this.offsetY === a.offsetY
            }
            Fg(a) {
                return this.Dg[0] > a.right || this.Dg[0] + this.width < a.left || this.Dg[1] > a.bottom || this.Dg[1] + this.height < a.top ? !1 : !0
            }
        },
        GPa = {
            linear: a => a,
            ["ease-out"]: a => 1 - Math.pow(a - 1, 2),
            ["ease-in"]: a => Math.pow(a, 2)
        },
        kS = class {
            constructor(a) {
                this.frames = a;
                this.Dg = ""
            }
        },
        AR;
    var RPa = {
        [1]: {
            options: {
                duration: 700,
                pm: "infinite"
            },
            icon: new kS([{
                time: 0,
                translate: [0, 0],
                um: "ease-out"
            }, {
                time: .5,
                translate: [0, -20],
                um: "ease-in"
            }, {
                time: 1,
                translate: [0, 0],
                um: "ease-out"
            }])
        },
        [2]: {
            options: {
                duration: 500,
                pm: 1
            },
            icon: new kS([{
                time: 0,
                translate: [0, -500],
                um: "ease-in"
            }, {
                time: .5,
                translate: [0, 0],
                um: "ease-out"
            }, {
                time: .75,
                translate: [0, -20],
                um: "ease-in"
            }, {
                time: 1,
                translate: [0, 0],
                um: "ease-out"
            }])
        },
        [3]: {
            options: {
                duration: 200,
                oy: 20,
                pm: 1,
                GG: !1
            },
            icon: new kS([{
                time: 0,
                translate: [0, 0],
                um: "ease-in"
            }, {
                time: 1,
                translate: [0, -20],
                um: "ease-out"
            }])
        },
        [4]: {
            options: {
                duration: 500,
                oy: 20,
                pm: 1,
                GG: !1
            },
            icon: new kS([{
                time: 0,
                translate: [0, -20],
                um: "ease-in"
            }, {
                time: .5,
                translate: [0, 0],
                um: "ease-out"
            }, {
                time: .75,
                translate: [0, -10],
                um: "ease-in"
            }, {
                time: 1,
                translate: [0, 0],
                um: "ease-out"
            }])
        }
    };
    var BR = class {
        constructor() {
            this.icon = {
                url: _.Hr("api-3/images/spotlight-poi3", !0),
                scaledSize: new _.Pn(26, 37),
                origin: new _.Nn(0, 0),
                anchor: new _.Nn(13, 37),
                labelOrigin: new _.Nn(13, 14)
            };
            this.Eg = {
                url: _.Hr("api-3/images/spotlight-poi-dotless3", !0),
                scaledSize: new _.Pn(26, 37),
                origin: new _.Nn(0, 0),
                anchor: new _.Nn(13, 37),
                labelOrigin: new _.Nn(13, 14)
            };
            this.Dg = {
                url: _.Hr("api-3/images/drag-cross", !0),
                scaledSize: new _.Pn(13, 11),
                origin: new _.Nn(0, 0),
                anchor: new _.Nn(7, 6)
            };
            this.shape = {
                coords: [13, 0, 4, 3.5, 0, 12, 2.75, 21,
                    13, 37, 23.5, 21, 26, 12, 22, 3.5
                ],
                type: "poly"
            }
        }
    };
    var kRa = class extends _.Xm {
            constructor(a, b) {
                super();
                this.Eg = a;
                this.Dg = b;
                lS || (lS = new BR)
            }
            changed(a) {
                a !== "modelIcon" && a !== "modelShape" && a !== "modelCross" && a !== "modelLabel" || _.SA(_.RA(), this.Fg, this, this)
            }
            Fg() {
                const a = this.get("modelIcon");
                var b = this.get("modelLabel");
                BPa(this, "viewIcon", a || b && lS.Eg || lS.icon);
                BPa(this, "viewCross", lS.Dg);
                b = this.get("useDefaults");
                let c = this.get("modelShape");
                c || a && !b || (c = lS.shape);
                this.get("viewShape") !== c && this.set("viewShape", c)
            }
        },
        lS;
    var lRa = class extends _.Xm {
        constructor() {
            super();
            this.Eg = !1;
            this.Dg = CPa(this);
            this.set("shouldRender", this.Dg)
        }
        changed() {
            if (!this.Eg) {
                var a = CPa(this);
                this.Dg !== a && (this.Dg = a, this.Eg = !0, this.set("shouldRender", this.Dg), this.Eg = !1)
            }
        }
    };
    var gQa = class extends _.Xm {
        constructor(a) {
            super();
            this.Eg = a;
            this.Dg = !1
        }
        internalPosition_changed() {
            if (!this.Dg) {
                this.Dg = !0;
                var a = this.get("position"),
                    b = this.get("internalPosition");
                a && b && !a.equals(b) && this.set("position", this.get("internalPosition"));
                this.Dg = !1
            }
        }
        draggable_changed() {
            if (!this.Dg) {
                this.Dg = !0;
                if (this.Eg) {
                    const a = this.get("place");
                    a ? this.set("internalPosition", a.location) : this.set("internalPosition", this.get("position"))
                }
                this.get("place") ? this.set("actuallyDraggable", !1) : this.set("actuallyDraggable",
                    this.get("draggable"));
                this.Dg = !1
            }
        }
        position_changed() {
            this.draggable_changed()
        }
        place_changed() {
            this.draggable_changed()
        }
    };
    var NPa = class {
        constructor(a, b, c, d, e) {
            this.Al = a;
            this.label = b;
            this.opacity = c;
            this.visible = d;
            this.origin = void 0;
            this.zIndex = 0;
            this.Fg = this.Hg = this.Dg = null;
            this.Eg = new _.Wp(this.Ig, 0, this);
            this.Gg = e;
            this.Al = a;
            this.label = b;
            this.opacity = c;
            this.visible = d
        }
        setOpacity(a) {
            this.opacity = a;
            _.Xp(this.Eg)
        }
        setLabel(a) {
            this.label = a;
            _.Xp(this.Eg)
        }
        setVisible(a) {
            this.visible = a;
            _.Xp(this.Eg)
        }
        setZIndex(a) {
            this.zIndex = a;
            _.Xp(this.Eg)
        }
        release() {
            this.Al = null;
            CR(this)
        }
        Ig() {
            if (this.Al && this.label && this.visible !== !1) {
                var a = this.Al.markerLayer,
                    b = this.label;
                this.Dg ? a.appendChild(this.Dg) : (this.Dg = document.createElement("div"), a.appendChild(this.Dg), this.Dg.style.transform = "translateZ(0)");
                a = this.Dg;
                this.origin && _.Vy(a, this.origin);
                var c = a.firstElementChild;
                c || (c = document.createElement("div"), a.appendChild(c), c.style.height = "100px", c.style.transform = "translate(-50%, -50px)", c.style.display = "table", c.style.borderSpacing = "0");
                let d = c.firstElementChild;
                d || (d = document.createElement("div"), c.appendChild(d), d.style.display = "table-cell", d.style.verticalAlign =
                    "middle", d.style.whiteSpace = "nowrap", d.style.textAlign = "center");
                c = d.firstElementChild || _.Wy("div", d);
                c.textContent = b.text;
                c.style.color = b.color;
                c.style.fontSize = b.fontSize;
                c.style.fontWeight = b.fontWeight;
                c.style.fontFamily = b.fontFamily;
                c.className = b.className;
                c.setAttribute("aria-hidden", "true");
                if (this.Gg && b !== this.Fg) {
                    this.Fg = b;
                    const {
                        width: e,
                        height: f
                    } = c.getBoundingClientRect();
                    b = new _.Pn(e, f);
                    b.equals(this.Hg) || (this.Hg = b, this.Gg(b))
                }
                _.qJ(c, _.vl(this.opacity, 1));
                _.Xy(a, this.zIndex)
            } else CR(this)
        }
    };
    var IPa = class {
        constructor(a, b, c) {
            this.element = a;
            this.animation = b;
            this.options = c;
            this.Eg = !1;
            this.Dg = null
        }
        start() {
            this.options.pm = this.options.pm || 1;
            this.options.duration = this.options.duration || 1;
            _.Nm(this.element, "webkitAnimationEnd", () => {
                this.Eg = !0;
                _.Tm(this, "done")
            });
            EPa(this.element, xPa(this.animation), this.options)
        }
        cancel() {
            this.Dg && (this.Dg.remove(), this.Dg = null);
            EPa(this.element, null, {});
            _.Tm(this, "done")
        }
        stop() {
            this.Eg || (this.Dg = _.Nm(this.element, "webkitAnimationIteration", () => {
                this.cancel()
            }))
        }
    };
    var DR = [],
        ER = null,
        JPa = class {
            constructor(a, b, c) {
                this.element = a;
                this.animation = b;
                this.pm = -1;
                this.Dg = !1;
                this.startTime = 0;
                c.pm !== "infinity" && (this.pm = c.pm || 1);
                this.duration = c.duration || 1E3
            }
            start() {
                DR.push(this);
                ER || (ER = window.setInterval(FPa, 10));
                this.startTime = Date.now();
                this.tick()
            }
            cancel() {
                this.Dg || (this.Dg = !0, HPa(this, 1), _.Tm(this, "done"))
            }
            stop() {
                this.Dg || (this.pm = 1)
            }
            tick() {
                if (!this.Dg) {
                    var a = Date.now();
                    HPa(this, (a - this.startTime) / this.duration);
                    a >= this.startTime + this.duration && (this.startTime = Date.now(),
                        this.pm !== "infinite" && (this.pm--, this.pm || this.cancel()))
                }
            }
        };
    var mRa = _.na.DEF_DEBUG_MARKERS,
        mS = class extends _.Xm {
            constructor(a, b, c) {
                super();
                this.Jg = new _.Wp(() => {
                        var d = this.get("panes"),
                            e = this.get("scale");
                        if (!d || !this.getPosition() || this.Ki() == 0 || _.tl(e) && e < .1 && !this.Nk) IR(this);
                        else {
                            MPa(this, d.markerLayer);
                            if (!this.Og) {
                                var f = this.ah();
                                if (f) {
                                    var g = f.url;
                                    e = this.get("clickable") != 0;
                                    var h = this.getDraggable(),
                                        l = this.get("title") || "",
                                        n = l;
                                    n || (n = (n = this.ih()) ? n.text : "");
                                    if (e || h || n) {
                                        var p = !e && !h && !l,
                                            r = xR(f),
                                            u = LR(f),
                                            w = this.get("shape"),
                                            x = yR(f),
                                            y = {};
                                        if (_.Zy()) f = x.width,
                                            x = x.height, r = new _.Pn(f + 16, x + 16), f = {
                                                url: _.uD,
                                                size: r,
                                                anchor: u ? new _.Nn(u.x + 8, u.y + 8) : new _.Nn(Math.round(f / 2) + 8, x + 8),
                                                scaledSize: r
                                            };
                                        else {
                                            const I = f.scaledSize || x;
                                            (_.oq.Eg || _.oq.Dg) && w && (y.shape = w, x = I);
                                            if (!r || w) f = {
                                                url: _.uD,
                                                size: x,
                                                anchor: u,
                                                scaledSize: I
                                            }
                                        }
                                        u = f.url != null;
                                        this.Lh === u && HR(this);
                                        this.Lh = !u;
                                        y = this.targetElement = JR(this, this.getPanes().overlayMouseTarget, this.targetElement, f, y);
                                        this.targetElement.style.pointerEvents = p ? "none" : "";
                                        if (p = y.querySelector("img")) p.style.removeProperty("position"), p.style.removeProperty("opacity"),
                                            p.style.removeProperty("left"), p.style.removeProperty("top");
                                        p = y;
                                        if ((u = p.getAttribute("usemap") || p.firstChild && p.firstChild.getAttribute("usemap")) && u.length && (p = _.Ry(p).getElementById(u.substr(1)))) var D = p.firstChild;
                                        D && (D.tabIndex = -1, D.style.display = "inline", D.style.position = "absolute", D.style.left = "0px", D.style.top = "0px");
                                        mRa && (y.dataset.debugMarkerImage = g);
                                        y = D || y;
                                        y.title = l;
                                        n && this.vp().setAttribute("aria-label", n);
                                        this.ow();
                                        h && !this.Ig && (g = this.Ig = new _.nBa(y, this.Sg, this.targetElement), this.Sg ?
                                            (g.bindTo("deltaClientPosition", this), g.bindTo("position", this)) : g.bindTo("position", this.Rg, "rawPosition"), g.bindTo("containerPixelBounds", this, "mapPixelBounds"), g.bindTo("anchorPoint", this), g.bindTo("size", this), g.bindTo("panningEnabled", this), this.Qg || (this.Qg = [_.Sm(g, "dragstart", this), _.Sm(g, "drag", this), _.Sm(g, "dragend", this), _.Sm(g, "panbynow", this)]));
                                        g = this.get("cursor") || "pointer";
                                        h ? this.Ig.set("draggableCursor", g) : y.style.cursor = e ? g : "";
                                        UPa(this, y)
                                    }
                                }
                            }
                            d = d.overlayLayer;
                            if (h = e = this.get("cross")) h =
                                this.get("crossOnDrag"), h === void 0 && (h = this.get("raiseOnDrag")), h = h != 0 && this.getDraggable() && this.Nk;
                            h ? this.Gg = JR(this, d, this.Gg, e) : (this.Gg && _.dz(this.Gg), this.Gg = null);
                            this.Kg = [this.Dg, this.Gg, this.targetElement];
                            QPa(this);
                            for (e = 0; e < this.Kg.length; ++e)
                                if (h = this.Kg[e]) d = h, g = h.Ux, l = FR(h) || _.jo, h = KR(this), g = OPa(this, g, h, l), _.Vy(d, g), (g = _.sq().transform) && (d.style[g] = h != 1 ? "scale(" + h + ") " : ""), d && _.Xy(d, PPa(this));
                            SPa(this);
                            for (d = 0; d < this.Kg.length; ++d)(e = this.Kg[d]) && _.oJ(e);
                            _.Tm(this, "UPDATE_FOCUS")
                        }
                    },
                    0);
                this.Ei = a;
                this.Ai = c;
                this.Sg = b || !1;
                this.Rg = new iRa;
                this.Rg.bindTo("position", this);
                this.Hg = this.Dg = null;
                this.Qh = [];
                this.wh = !1;
                this.targetElement = null;
                this.Lh = !1;
                this.Gg = null;
                this.Kg = [];
                this.nh = new _.Nn(0, 0);
                this.Tg = new _.Pn(0, 0);
                this.Pg = new _.Nn(0, 0);
                this.Ug = !0;
                this.Og = 0;
                this.Fg = this.Dh = this.ai = this.Wh = null;
                this.Vg = !1;
                this.xh = [_.Em(this, "dragstart", this.oi), _.Em(this, "dragend", this.ci), _.Em(this, "panbynow", () => _.Yp(this.Jg))];
                this.th = this.Mg = this.Lg = this.Ig = this.Ng = this.Qg = null;
                this.Xg = !1;
                this.getPosition =
                    _.yn("position");
                this.getPanes = _.yn("panes");
                this.Ki = _.yn("visible");
                this.ah = _.yn("icon");
                this.ih = _.yn("label");
                this.rp = null
            }
            RF() {}
            get Am() {
                return this.Xg
            }
            set Am(a) {
                this.Xg !== a && (this.Xg = a, _.Tm(this, "UPDATE_FOCUS"))
            }
            get Nk() {
                return this.get("dragging")
            }
            panes_changed() {
                IR(this);
                _.Xp(this.Jg)
            }
            bo(a) {
                this.set("position", a && new _.Nn(a.jh, a.kh))
            }
            Bs() {
                this.unbindAll();
                this.set("panes", null);
                this.Fg && this.Fg.stop();
                this.Ng && (_.Gm(this.Ng), this.Ng = null);
                this.Fg = null;
                GR(this.xh);
                this.xh = [];
                IR(this);
                _.Tm(this,
                    "RELEASED")
            }
            mh() {
                var a;
                if (!(a = this.Wh != (this.get("clickable") != 0) || this.ai != this.getDraggable())) {
                    a = this.Dh;
                    var b = this.get("shape");
                    a = !(a == null || b == null ? a == b : a.type == b.type && _.TI(a.coords, b.coords))
                }
                a && (this.Wh = this.get("clickable") != 0, this.ai = this.getDraggable(), this.Dh = this.get("shape"), HR(this), _.Xp(this.Jg))
            }
            Eg() {
                _.Xp(this.Jg)
            }
            position_changed() {
                this.Sg ? _.Yp(this.Jg) : _.Xp(this.Jg)
            }
            vp() {
                return this.targetElement
            }
            ow() {
                const a = this.vp();
                if (a) {
                    var b = !!this.get("title");
                    b || (b = (b = this.ih()) ? !!b.text :
                        !1);
                    this.Am ? a.setAttribute("role", "button") : b ? a.setAttribute("role", "img") : a.removeAttribute("role")
                }
            }
            Ox(a) {
                _.Tm(this, "click", a);
                _.Fn(window, "Mki");
                _.M(window, 171149)
            }
            Ks() {}
            Bq(a) {
                _.By(a);
                _.Tm(this, "click", a);
                _.Fn(window, "Mmi");
                _.M(window, 171150)
            }
            Nx() {}
            getDraggable() {
                return !!this.get("draggable")
            }
            oi() {
                this.set("dragging", !0);
                this.Rg.set("snappingCallback", this.Ei)
            }
            ci() {
                this.Rg.set("snappingCallback", null);
                this.set("dragging", !1)
            }
            animation_changed() {
                this.Ug = !1;
                this.get("animation") ? SPa(this) : (this.set("animating", !1), this.Fg && this.Fg.stop())
            }
            WE(a) {
                const b = this.get("markerPosition");
                return this.rp && b && this.rp.size ? oPa(a, this.targetElement) : !1
            }
        };
    _.B = mS.prototype;
    _.B.shape_changed = mS.prototype.mh;
    _.B.clickable_changed = mS.prototype.mh;
    _.B.draggable_changed = mS.prototype.mh;
    _.B.cursor_changed = mS.prototype.Eg;
    _.B.scale_changed = mS.prototype.Eg;
    _.B.raiseOnDrag_changed = mS.prototype.Eg;
    _.B.crossOnDrag_changed = mS.prototype.Eg;
    _.B.zIndex_changed = mS.prototype.Eg;
    _.B.opacity_changed = mS.prototype.Eg;
    _.B.title_changed = mS.prototype.Eg;
    _.B.cross_changed = mS.prototype.Eg;
    _.B.icon_changed = mS.prototype.Eg;
    _.B.visible_changed = mS.prototype.Eg;
    _.B.dragging_changed = mS.prototype.Eg;
    var $Pa = "click dblclick mouseup mousedown mouseover mouseout rightclick dragstart drag dragend contextmenu".split(" "),
        kQa = class {
            constructor(a, b, c, d, e, f, g) {
                this.marker = a;
                this.Eg = b;
                this.Yg = e;
                this.Qg = f;
                this.Gg = g;
                this.Ng = !0;
                this.Og = this.Pg = null;
                this.Ig = [];
                this.Mg = b instanceof _.nn;
                f = MR(this);
                b = this.Mg && f ? _.fz(f, b.getProjection()) : null;
                this.Dg = new mS(d, !!this.Mg, h => {
                    this.Dg.rp = a.__gm.rp = { ...a.__gm.rp,
                        UP: h
                    };
                    a.__gm.xx && a.__gm.xx()
                });
                _.Em(this.Dg, "RELEASED", () => {
                    var h = this.Dg;
                    if (this.Gg && this.Gg.has(h)) {
                        ({
                                rE: h
                            } =
                            this.Gg.get(h));
                        for (const l of h) l.remove()
                    }
                    this.Gg && this.Gg.delete(this.Dg)
                });
                this.Qg && this.Gg && !this.Gg.has(this.Dg) && (this.Gg.set(this.Dg, {
                    marker: this.marker,
                    rE: []
                }), this.Qg.Qg(this.Dg), NR(this, this.Dg), XPa(this, this.Dg));
                (this.Hg = this.Mg ? new _.xN(e.zj, this.Dg, b, e, () => {
                    if (this.Dg.get("dragging") && !this.marker.get("place")) {
                        var h = this.Hg.getPosition();
                        h && (h = _.Qr(h, this.Eg.get("projection")), this.Ng = !1, this.marker.set("position", h), this.Ng = !0)
                    }
                }) : null) && e.Ni(this.Hg);
                this.Jg = new kRa(c, (h, l, n) => {
                    this.Dg.rp =
                        a.__gm.rp = { ...a.__gm.rp,
                            size: h,
                            anchor: l,
                            labelOrigin: n
                        };
                    a.__gm.xx && a.__gm.xx()
                });
                this.Fg = this.Mg ? null : new _.lN;
                this.Kg = this.Mg ? null : new lRa;
                this.Lg = new _.Xm;
                this.Lg.bindTo("position", this.marker);
                this.Lg.bindTo("place", this.marker);
                this.Lg.bindTo("draggable", this.marker);
                this.Lg.bindTo("dragging", this.marker);
                this.Jg.bindTo("modelIcon", this.marker, "icon");
                this.Jg.bindTo("modelLabel", this.marker, "label");
                this.Jg.bindTo("modelCross", this.marker, "cross");
                this.Jg.bindTo("modelShape", this.marker, "shape");
                this.Jg.bindTo("useDefaults", this.marker, "useDefaults");
                this.Dg.bindTo("icon", this.Jg, "viewIcon");
                this.Dg.bindTo("label", this.Jg, "viewLabel");
                this.Dg.bindTo("cross", this.Jg, "viewCross");
                this.Dg.bindTo("shape", this.Jg, "viewShape");
                this.Dg.bindTo("title", this.marker);
                this.Dg.bindTo("cursor", this.marker);
                this.Dg.bindTo("dragging", this.marker);
                this.Dg.bindTo("clickable", this.marker);
                this.Dg.bindTo("zIndex", this.marker);
                this.Dg.bindTo("opacity", this.marker);
                this.Dg.bindTo("anchorPoint", this.marker);
                this.Dg.bindTo("markerPosition",
                    this.marker, "position");
                this.Dg.bindTo("animation", this.marker);
                this.Dg.bindTo("crossOnDrag", this.marker);
                this.Dg.bindTo("raiseOnDrag", this.marker);
                this.Dg.bindTo("animating", this.marker);
                this.Kg || this.Dg.bindTo("visible", this.marker);
                YPa(this);
                ZPa(this);
                aQa(this);
                this.Mg ? (bQa(this), cQa(this), eQa(this)) : (fQa(this), this.Fg && (this.Kg.bindTo("visible", this.marker), this.Kg.bindTo("cursor", this.marker), this.Kg.bindTo("icon", this.marker), this.Kg.bindTo("icon", this.Jg, "viewIcon"), this.Kg.bindTo("mapPixelBoundsQ",
                    this.Eg.__gm, "pixelBoundsQ"), this.Kg.bindTo("position", this.Fg, "pixelPosition"), this.Dg.bindTo("visible", this.Kg, "shouldRender")), hQa(this))
            }
            dispose() {
                this.Dg.set("animation", null);
                this.Dg.Bs();
                this.Yg && this.Hg ? this.Yg.Cl(this.Hg) : this.Dg.Bs();
                this.Kg && this.Kg.unbindAll();
                this.Fg && this.Fg.unbindAll();
                this.Jg.unbindAll();
                this.Lg.unbindAll();
                this.Ig.forEach(_.Gm);
                this.Ig.length = 0
            }
        };
    var SR = class {
        constructor(a, b, c, d) {
            this.div = a;
            this.Pi = b;
            this.Dg = c;
            this.Bh = d
        }
        getContext() {
            if (!this.context) {
                const a = this.div,
                    b = a.ownerDocument.createElement("canvas");
                _.wq(b);
                b.style.position = "absolute";
                b.style.top = b.style.left = "0";
                const c = b.getContext("2d"),
                    d = PR(c),
                    e = this.Bh.size;
                b.width = Math.ceil(e.jh * d);
                b.height = Math.ceil(e.kh * d);
                b.style.width = _.Bl(e.jh);
                b.style.height = _.Bl(e.kh);
                a.appendChild(b);
                this.context = c
            }
            return this.context
        }
        lD(a) {
            const b = mQa(this),
                c = this.getContext(),
                d = PR(c),
                e = Math.round(a.dx *
                    d),
                f = Math.round(a.dy * d),
                g = Math.ceil(a.tq * d);
            a = Math.ceil(a.qq * d);
            const h = lQa(this, g, a),
                l = h.getContext("2d");
            l.translate(-e, -f);
            b.forEach(n => {
                l.globalAlpha = _.vl(n.opacity, 1);
                l.drawImage(n.image, n.Oy, n.Py, n.Ny, n.Jy, Math.round(n.dx * d), Math.round(n.dy * d), n.tq * d, n.qq * d)
            });
            c.clearRect(e, f, g, a);
            c.globalAlpha = 1;
            c.drawImage(h, e, f)
        }
    };
    SR.prototype.XL = SR.prototype.lD;
    var wQa = class {
        constructor() {
            this.Dg = _.hJ().Dg
        }
        load(a, b) {
            return this.Dg.load(new _.IL(a.url), c => {
                if (c) {
                    var d = c.size,
                        e = a.size || a.scaledSize || d;
                    a.size = e;
                    var f = a.scaledSize || d,
                        g = a.anchor || new _.Nn(e.width / 2, e.height),
                        h = f.width / d.width,
                        l = f.height / d.height,
                        n = a.origin ? a.origin.x / h : 0,
                        p = a.origin ? a.origin.y / l : 0,
                        r = -g.x;
                    g = -g.y;
                    var u = e.width / h,
                        w = e.width,
                        x = e.height / l,
                        y = e.height;
                    n * h + e.width > f.width && (u = d.width - n * h, w = f.width);
                    p * l + e.height > f.height && (x = d.height - p * l, y = f.height);
                    b({
                        image: c,
                        Oy: n,
                        Py: p,
                        Ny: u,
                        Jy: x,
                        dx: r,
                        dy: g,
                        tq: w,
                        qq: y
                    })
                } else b(null)
            })
        }
        cancel(a) {
            this.Dg.cancel(a)
        }
    };
    var yQa = class {
        constructor(a, b, c, d) {
            this.Dg = b;
            this.Eg = c;
            this.zIndex = 40;
            this.Fg = new _.yN(a, d, c)
        }
        Hs(a) {
            return a !== "dragstart" && a !== "drag" && a !== "dragend"
        }
        Qs(a, b) {
            return b ? QR(this, a, -8, 0) || QR(this, a, 0, -8) || QR(this, a, 8, 0) || QR(this, a, 0, 8) : QR(this, a, 0, 0)
        }
        handleEvent(a, b, c) {
            const d = b.sj;
            if (a === "mouseout") this.Dg.set("cursor", ""), this.Dg.set("title", null);
            else if (a === "mouseover") {
                var e = d.Jv;
                this.Dg.set("cursor", e.cursor);
                (e = e.title) && this.Dg.set("title", e)
            }
            let f;
            d && a !== "mouseout" ? f = d.Jv.latLng : f = b.latLng;
            a ===
                "dblclick" && _.Cm(b.domEvent);
            _.Tm(c, a, new _.xD(f, b.domEvent))
        }
    };
    var zQa = class extends _.vr {
        constructor(a, b, c, d, e, f, g) {
            super();
            this.Ig = a;
            this.Kg = d;
            this.Gg = c;
            this.Fg = e;
            this.Hg = f;
            this.Eg = g || _.ID;
            b.Dg = h => {
                qQa(this, h)
            };
            b.onRemove = h => {
                rQa(this, h)
            };
            b.forEach(h => {
                qQa(this, h)
            })
        }
        Dg() {
            return {
                Bh: this.Eg,
                vl: 2,
                Wk: this.Jg.bind(this)
            }
        }
        Jg(a, b = {}) {
            const c = document.createElement("div"),
                d = this.Eg.size;
            c.style.width = `${d.jh}px`;
            c.style.height = `${d.kh}px`;
            c.style.overflow = "hidden";
            a = {
                div: c,
                zoom: a.Ah,
                si: new _.Nn(a.qh, a.rh),
                ap: {},
                Pi: new _.bq
            };
            c.nk = a;
            sQa(this, a);
            let e = !1;
            return {
                Oi: () =>
                    c,
                km: () => e,
                loaded: new Promise(f => {
                    _.Pm(c, "load", () => {
                        e = !0;
                        f()
                    })
                }),
                release: () => {
                    const f = c.nk;
                    c.nk = null;
                    tQa(this, f);
                    c.textContent = "";
                    b.cj && b.cj()
                }
            }
        }
    };
    var AQa = class {
        constructor(a, b, c) {
            this.Eg = b;
            this.fo = null;
            this.Dg = !1;
            this.Gg = 0;
            const d = this;
            a.Dg = e => {
                d.Mq(e)
            };
            a.onRemove = e => {
                d.ws(e)
            };
            this.Hg = c;
            a.getSize() ? (this.Dg = !0, this.Fg()) : _.Sp(_.rI(_.Tm, c, "load"))
        }
        Mq(a) {
            uQa(this, a, !0)
        }
        ws(a) {
            uQa(this, a, !1)
        }
        Fg() {
            this.Dg && nQa(this.Eg);
            this.Dg = !1;
            this.fo = null;
            this.Gg = 0;
            _.Sp(_.rI(_.Tm, this.Hg, "load"))
        }
    };
    var xQa = class {
        constructor(a, b, c, d, e) {
            var f = vQa;
            this.markers = a;
            this.Eg = b;
            this.Dg = c;
            this.Hg = f;
            this.Gg = d;
            this.Fg = e;
            this.markers.Dg = g => {
                this.Mq(g)
            };
            this.markers.onRemove = g => {
                this.ws(g)
            }
        }
        Mq(a) {
            var b = a.get("internalPosition"),
                c = a.get("zIndex");
            const d = a.get("opacity"),
                e = a.__gm.Tx = {
                    Uz: a,
                    latLng: b,
                    zIndex: c,
                    opacity: d,
                    Pi: {}
                };
            b = a.get("useDefaults");
            c = a.get("icon");
            const f = a.get("shape") || c && !b ? a.get("shape") : this.Dg.shape,
                g = c ? this.Hg(c) : this.Dg.icon,
                h = nPa(() => {
                    e === a.__gm.Tx && (e.Dt || e.tG) && this.yl(a, e, g, f)
                });
            g.url ? this.Gg.load(g, l => {
                e.Dt = l;
                h()
            }) : (e.tG = this.Fg(g), h())
        }
        ws(a) {
            this.Eg.remove(a.__gm.Tx);
            a.__gm.Tx = null
        }
        yl(a, b, c, d) {
            if (b.Dt) {
                c = c.size;
                var e = a.get("anchorPoint");
                if (!e || e.Dg) e = new _.Nn(b.Dt.dx + c.width / 2, b.Dt.dy), e.Dg = !0, a.set("anchorPoint", e)
            } else c = b.tG.size;
            d ? d.coords = d.coords || d.coord : d = {
                type: "rect",
                coords: [0, 0, c.width, c.height]
            };
            b.shape = d;
            b.clickable = a.get("clickable");
            b.title = a.get("title") || null;
            b.cursor = a.get("cursor") || "pointer";
            _.cq(this.Eg, b)
        }
    };
    var RR = new Map;
    var nRa = class {
        constructor(a, b, c, d) {
            this.Uu = {};
            this.fo = 0;
            this.Zv = !0;
            const e = this;
            this.mC = b;
            this.st = c;
            this.sE = d;
            const f = {
                animating: 1,
                animation: 1,
                attribution: 1,
                clickable: 1,
                cursor: 1,
                draggable: 1,
                flat: 1,
                icon: 1,
                label: 1,
                opacity: 1,
                optimized: 1,
                place: 1,
                position: 1,
                shape: 1,
                __gmHiddenByCollision: 1,
                title: 1,
                visible: 1,
                zIndex: 1
            };
            this.qL = function(g) {
                g in f && (delete this.changed, e.Uu[_.Wm(this)] = this, DQa(e))
            };
            a.Dg = g => {
                e.Mq(g)
            };
            a.onRemove = g => {
                e.ws(g)
            };
            a = a.Eg;
            for (const g of Object.values(a)) this.Mq(g)
        }
        Mq(a) {
            this.Uu[_.Wm(a)] =
                a;
            DQa(this)
        }
        ws(a) {
            delete a.changed;
            delete this.Uu[_.Wm(a)];
            this.mC.remove(a);
            this.st.remove(a)
        }
    };
    var nS = class extends Event {
        constructor() {
            super("gmp-click", {
                bubbles: !0
            })
        }
    };
    var oRa = class {
        Sg() {}
        Pg() {}
        Eg() {}
        Fg() {}
        Gg() {}
        Lg() {}
        Ng() {}
        Jg() {}
        Hg() {}
        Ig() {}
        Mg() {}
        Og() {}
        Dg() {}
        Qg() {}
        Rg() {}
        Ug() {}
        Tg() {}
        Kg() {}
    };
    var pRa = (0, _.Mi)
    `.yNHHyP-marker-view .IPAZAH-content-container\u003e*{pointer-events:none}.yNHHyP-marker-view .IPAZAH-content-container.HJDHPx-interactive\u003e*{pointer-events:auto}\n`;
    var HQa = jPa("visible-gmp-advanced-markers"),
        GQa = jPa("hidden-gmp-advanced-markers"),
        KQa = class {
            constructor(a) {
                this.ui = qRa;
                this.ro = null;
                this.Mg = !1;
                this.Kg = null;
                this.Jg = 0;
                this.Lg = null;
                this.map = a;
                this.Gg = new Set;
                this.Hg = new Set;
                this.Xm = `maps-aria-${_.mn()}`;
                this.Fg = document.createElement("span");
                this.Fg.id = this.Xm;
                this.Fg.textContent = "To activate drag with keyboard, press Alt + Enter. Once in keyboard drag state, use the arrow keys to move the marker. To complete the drag, press the Enter key. To cancel, press Escape.";
                this.Fg.style.display = "none";
                this.Ig = document.createElement("div");
                this.Dg = document.createElement("div");
                CSS.supports("content-visibility: hidden") ? this.Dg.style.contentVisibility = "hidden" : this.Dg.style.visibility = "hidden";
                var b = document.createElement("slot");
                b.setAttribute("name", HQa);
                this.Ig.appendChild(b);
                b = document.createElement("slot");
                b.setAttribute("name", GQa);
                this.Dg.appendChild(b);
                this.Eg = document.createElement("div");
                this.Eg.append(this.Ig, this.Dg);
                const c = a.__gm;
                this.Og = c.sp;
                this.Ng = new Promise(d => {
                    c.Fg.then(e => {
                        this.map && (e && (this.ro = EQa(this, a)), this.Mg = !0);
                        d()
                    })
                });
                _.cw(pRa, this.map.getDiv());
                Promise.all([c.Eg, this.Ng]).then(([{
                    Al: d
                }]) => {
                    this.map && d.overlayMouseTarget.append(this.Fg, this.Eg);
                    this.Lg = c.addListener("panes_changed", e => {
                        this.map && e.overlayMouseTarget.append(this.Fg, this.Eg)
                    })
                })
            }
            dispose() {
                this.ro && (this.ro.setMap(null), this.ro = null);
                this.Lg && this.Lg.remove();
                this.Fg.remove();
                this.Dg.remove();
                this.Ig.remove();
                this.Eg.remove();
                this.Dg.textContent = "";
                this.Ig.textContent = "";
                this.Gg.clear();
                this.Hg.clear();
                this.map = null
            }
            isEmpty() {
                return this.Gg.size === 0
            }
            requestRedraw() {
                this.Mg ? this.ro && this.ro.requestRedraw() : this.Ng.then(() => {
                    this.ro && this.ro.requestRedraw()
                })
            }
            onDraw(a) {
                if (this.map) {
                    var b = this.Og.offsetWidth,
                        c = this.Og.offsetHeight,
                        d = _.Jq(this.map.getZoom() || 1, this.map.getTilt() || 0, this.map.getHeading() || 0);
                    for (const h of this.Gg.values()) {
                        var e = h.DK;
                        var f = this.map.getCenter();
                        if (e && f) {
                            f = _.rl(f.lng(), -180, 180);
                            var g = _.rl(e.lng, -180, 180);
                            f > 0 && g < f - 180 ? g += 360 : f < 0 && g > f + 180 && (g -= 360);
                            e = new _.So({
                                altitude: e.altitude,
                                lat: e.lat,
                                lng: g
                            }, !0)
                        } else e = null;
                        if (!e) {
                            h.bo(null, d);
                            continue
                        }
                        e = a.fromLatLngAltitude(e);
                        f = Array.from(e);
                        e = g = [0, 0, 0];
                        const l = e[0],
                            n = e[1],
                            p = e[2],
                            r = 1 / (f[3] * l + f[7] * n + f[11] * p + f[15]);
                        e[0] = (f[0] * l + f[4] * n + f[8] * p + f[12]) * r;
                        e[1] = (f[1] * l + f[5] * n + f[9] * p + f[13]) * r;
                        e[2] = (f[2] * l + f[6] * n + f[10] * p + f[14]) * r;
                        const {
                            uK: u,
                            rN: w
                        } = {
                            uK: f[14] < 0 && f[15] < 0,
                            rN: g
                        };
                        u ? h.bo(null, d) : h.bo({
                            jh: zR(w[0] / 2 * b),
                            kh: zR(-w[1] / 2 * c)
                        }, d, {
                            jh: b,
                            kh: c
                        })
                    }
                }
            }
        };
    var VR = new Map,
        qRa = new class extends oRa {
            Sg(a) {
                a && this.bj(a, 181191, "Acamk")
            }
            Pg(a) {
                if (a) {
                    var b = a.getRenderingType();
                    b !== "UNINITIALIZED" && this.bj(a, 159713, "Mlamk");
                    b === "RASTER" ? this.bj(a, 157416, "Raamk") : b === "VECTOR" && this.bj(a, 157417, "Veamk")
                }
            }
            Eg(a, b = !1) {
                this.bj(a, 158896, "Camk");
                b && this.bj(a, 185214, "Cgmk")
            }
            Fg(a, b) {
                b && (b !== "REQUIRED" && this.bj(a, 160097, "Csamk"), b === "REQUIRED_AND_HIDES_OPTIONAL" ? this.bj(a, 160098, "Cramk") : b === "OPTIONAL_AND_HIDES_LOWER_PRIORITY" && this.bj(a, 160099, "Cpamk"))
            }
            Gg(a, b) {
                b ? this.bj(a,
                    159404, "Dcamk") : this.bj(a, 159405, "Ccamk")
            }
            Lg(a) {
                this.bj(a, 159484, "Ceamk")
            }
            Ng(a) {
                this.bj(a, 160438, "Dwaamk")
            }
            Jg(a) {
                this.bj(a, 159521, "Ziamk")
            }
            Hg(a) {
                this.bj(a, 160103, "Dgamk")
            }
            Ig(a) {
                this.bj(a, 159805, "Tiamk")
            }
            Mg(a) {
                this.bj(a, 159490, "Ckamk")
            }
            Og(a) {
                this.bj(a, 159812, "Fcamk")
            }
            Dg(a) {
                this.bj(a, 159609, "Atamk")
            }
            Qg(a) {
                this.bj(a, 160122, "Kdamk")
            }
            Rg(a) {
                this.bj(a, 160106, "Ldamk")
            }
            Ug(a) {
                this.bj(a, 160478, "pdamk")
            }
            Tg(a, b) {
                const c = [{
                    threshold: 1E4,
                    Ho: 160636,
                    Wo: "Amk10K"
                }, {
                    threshold: 5E3,
                    Ho: 160635,
                    Wo: "Amk5K"
                }, {
                    threshold: 2E3,
                    Ho: 160634,
                    Wo: "Amk2K"
                }, {
                    threshold: 1E3,
                    Ho: 160633,
                    Wo: "Amk1K"
                }, {
                    threshold: 500,
                    Ho: 160632,
                    Wo: "Amk500"
                }, {
                    threshold: 200,
                    Ho: 160631,
                    Wo: "Amk200"
                }, {
                    threshold: 100,
                    Ho: 160630,
                    Wo: "Amk100"
                }, {
                    threshold: 50,
                    Ho: 159732,
                    Wo: "Amk50"
                }, {
                    threshold: 10,
                    Ho: 160629,
                    Wo: "Amk10"
                }, {
                    threshold: 1,
                    Ho: 160628,
                    Wo: "Amk1"
                }];
                for (const {
                        threshold: d,
                        Ho: e,
                        Wo: f
                    } of c)
                    if (b >= d) {
                        this.bj(a, e, f);
                        break
                    }
            }
            Kg(a) {
                a = a instanceof KeyboardEvent;
                this.bj(window, a ? 171152 : 171153, a ? "Amki" : "Ammi")
            }
            bj(a, b, c) {
                a && (_.M(a, b), _.Fn(a, c))
            }
        },
        rRa = new oRa,
        UR = null;
    var sRa = class {
        constructor(a) {
            this.Dg = a;
            this.Hg = this.Gg = !1;
            this.Lg = this.Fg = this.Ig = this.Mg = this.Og = this.Tg = null;
            this.Ng = 0;
            this.Vg = null;
            this.ah = b => {
                this.Js(b)
            };
            this.ih = b => {
                b.touches.length === 1 && this.Js(b)
            };
            this.Xg = b => {
                b.preventDefault();
                b.stopImmediatePropagation()
            };
            this.Rg = b => {
                if (this.Hg || this.Kg || uPa(b, this.Tg)) this.Kg = !0
            };
            a = this.Dg.Eo;
            _.Jz !== 2 ? (a.addEventListener("pointerdown", this.ah), a.addEventListener("pointermove", this.Rg)) : (a.addEventListener("touchstart", this.ih, {
                passive: !1
            }), a.addEventListener("touchmove",
                this.Rg, {
                    passive: !1
                }));
            a.addEventListener("mousedown", this.Xg);
            this.Qg = b => {
                b.preventDefault();
                b.stopImmediatePropagation();
                this.Hg ? WQa(this, b) : this.Gg ? (XQa(this, b), $R(this.Dg, "drag", b)) : (YQa(this, b), b = this.Dg, b.ui.Ug(b.map))
            };
            this.Jg = b => {
                this.Lg && b.timeStamp - this.Lg >= 500 && (!this.Gg || this.Hg) ? (this.Hg ? WQa(this, b) : (YQa(this, b), b = this.Dg, b.ui.Rg(b.map), b.Po && _.Tm(b, "longpressdragstart")), this.Kg = !0) : (this.Gg && (this.Hg || this.Kg || uPa(b, this.Tg)) && (this.Kg = !0), this.Hg && WR(this, b), b.type === "touchend" && (this.Eg.style.display =
                    "none"), this.Gg ? (b.stopImmediatePropagation(), XQa(this, b), cS(this), dS(this.Dg, !0), $R(this.Dg, "dragend", b)) : cS(this))
            };
            this.mh = b => {
                this.xh(b)
            };
            this.th = b => {
                this.wh(b)
            };
            this.nh = b => {
                YR(this, b)
            };
            this.xh = b => {
                if (b.altKey && (_.UA(b) || b.key === _.nla)) YR(this, b);
                else if (!b.altKey && _.UA(b)) this.Kg = !0, WR(this, b);
                else if (_.VA(b) || _.XA(b) || _.WA(b) || _.YA(b)) b.preventDefault(), this.Pg.add(b.key), this.Ng || (this.Vg = new _.MM(100), $Qa(this)), $R(this.Dg, "drag", b);
                else if (b.code === "Equal" || b.code === "Minus") {
                    var c = this.Dg;
                    b = b.code === "Equal" ? 1 : -1;
                    const d = tPa(c.Rj, c.So);
                    d && c.Yg.TG(b, d)
                }
            };
            this.wh = b => {
                (_.VA(b) || _.XA(b) || _.WA(b) || _.YA(b)) && this.Pg.delete(b.key)
            };
            this.Sg = () => {
                this.Eg.style.display = ""
            };
            this.Ug = () => {
                this.Gg || (this.Eg.style.display = "none")
            };
            this.Eg = document.createElement("div");
            SQa(this);
            this.Kg = !1;
            this.Pg = new Set
        }
        Ty(a) {
            this.Fg && _.NM(this.Fg, a)
        }
        Js(a) {
            this.Kg = !1;
            if (this.Dg.gmpDraggable && (a.button === 0 || a.type === "touchstart")) {
                const b = this.Dg.Eo;
                b.focus();
                const c = document;
                _.Jz !== 2 || a.preventDefault();
                a.stopImmediatePropagation();
                this.Lg = a.timeStamp;
                _.Jz !== 2 ? (c.addEventListener("pointermove", this.Qg), c.addEventListener("pointerup", this.Jg), c.addEventListener("pointercancel", this.Jg)) : (c.addEventListener("touchmove", this.Qg, {
                    passive: !1
                }), c.addEventListener("touchend", this.Jg), c.addEventListener("touchcancel", this.Jg), c.addEventListener("touchstart", this.Jg));
                this.Gg || (this.Tg = _.nM(a));
                b.style.cursor = _.bB
            }
        }
        Ox() {
            this.Gg || (this.Kg = !1)
        }
        Ks(a) {
            if (this.Dg.gmpDraggable && !this.Hg && !this.Gg) {
                var b = this.Dg.Eo;
                b.addEventListener("keydown",
                    this.mh);
                b.addEventListener("keyup", this.th);
                b.addEventListener("blur", this.nh);
                this.Ig = this.Dg.jn();
                this.Og = this.Dg.position;
                this.Hg = this.Gg = !0;
                VQa(this);
                b = this.Dg.Eo;
                b.setAttribute("aria-grabbed", "true");
                bS(this.Dg);
                b.style.zIndex = "2147483647";
                this.Eg.style.opacity = "1";
                $R(this.Dg, "dragstart", a);
                a = this.Dg;
                a.ui.Qg(a.map)
            }
        }
        Nx(a, b = !0) {
            this.Hg ? YR(this, a, b) : this.Gg && (this.Dg.position = this.Og, a.stopImmediatePropagation(), cS(this), b && $R(this.Dg, "dragend", a))
        }
        Nk() {
            return this.Gg
        }
        dispose() {
            cS(this);
            const a =
                this.Dg.Eo;
            _.Jz !== 2 ? (a.removeEventListener("pointerdown", this.ah), a.removeEventListener("pointermove", this.Rg)) : (a.removeEventListener("touchstart", this.ih, {
                passive: !1
            }), a.removeEventListener("touchmove", this.Rg, {
                passive: !1
            }));
            a.removeEventListener("mousedown", this.Xg);
            a.removeEventListener("pointerenter", this.Sg);
            a.removeEventListener("pointerleave", this.Ug);
            a.removeEventListener("focus", this.Sg);
            a.removeEventListener("blur", this.Ug);
            this.Eg.remove()
        }
    };
    var oS = !1,
        pS = class extends _.av {
            constructor(a = {}) {
                super(a);
                this.Ru = this.fk = this.Ri = null;
                this.Qz = "";
                this.Qk = this.Dv = this.Up = this.Yg = this.Oj = this.Vk = null;
                this.RC = !1;
                this.Vy = null;
                this.CB = this.ZE = this.Wy = this.TC = !1;
                this.Gh = this.Fw = this.rF = this.PF = this.UG = this.BD = null;
                this.QC = void 0;
                this.qu = this.SC = !1;
                this.JE = _.eo(!1);
                this.So = this.su = this.eA = null;
                this.lr = "";
                this.Rj = this.Xy = void 0;
                this.zz = this.Az = !0;
                this.PA = this.hC = !1;
                this.Lv = !0;
                this.zD = document.createElement("div");
                bRa(this);
                this.targetElement = this;
                this.Eo =
                    this;
                this.Po = oS;
                Object.defineProperties(this, {
                    Po: {
                        value: oS,
                        writable: !1
                    }
                });
                this.ui = this.Po ? rRa : qRa;
                this.addEventListener("focus", e => {
                    this.xA(e)
                }, !0);
                this.addEventListener("resize", e => {
                    this.Eq.set("anchorPoint", new _.Nn(0, -e.detail.height))
                });
                this.ql = (new _.WD).element;
                this.tj = document.createElement("div");
                _.Un(this.tj, "content-container");
                this.Vk = document.createElement("slot");
                this.Vk.addEventListener("slotchange", () => {
                    this.Vr()
                });
                this.tj.appendChild(this.Vk);
                this.Vk.prepend(this.ql);
                this.Vr();
                Promise.resolve().then(() => {
                    eS(this)
                });
                this.ID = getComputedStyle(this);
                this.DJ = (e, f, g) => this.Cx(e, f, g);
                const b = () => {
                        eS(this);
                        fS(this);
                        const e = _.Fm(this, "gmp-click");
                        this.ui.Eg(this.map, e)
                    },
                    c = () => {
                        eS(this);
                        fS(this)
                    },
                    d = ["click"];
                d.push("gmp-click");
                for (const e of d) mPa(this, e, b), lPa(this, e, c);
                this.Eq = new _.Xm;
                this.vE = e => {
                    this.fk ? .Yq(e === "cooperative" || e === "none")
                };
                this.collisionBehavior = a.collisionBehavior;
                a.content != null && (this.content = a.content);
                this.Nz = !!a.Nz;
                this.gmpClickable = a.gmpClickable;
                this.gmpDraggable = a.gmpDraggable;
                this.position = a.position;
                this.title = a.title ? ? "";
                this.zIndex = a.zIndex;
                this.map = a.map;
                this.Uh(a, pS, "AdvancedMarkerElement")
            }
            addEventListener(a, b, c) {
                a !== "click" || this.hC || _.ym(_.qp(this, "Please use addEventListener('gmp-click', ...) instead of addEventListener('click', ...)."));
                super.addEventListener(a, b, c)
            }
            addListener(a, b) {
                a === "click" && _.ym(_.qp(this, "Please use addEventListener('gmp-click', ...) instead of addEventListener('click', ...)."));
                return _.Em(this, a, b)
            }
            Nu(a) {
                var b;
                if (b = this.Ri) b = this.Ri,
                    b = b.Lg && a.timeStamp - b.Lg >= 500 ? !0 : b.Kg;
                if (!b && this.So) {
                    this.gmpDraggable || this.focus();
                    $R(this, "click", a);
                    if (this.gmpClickable || _.Fm(this, "gmp-click")) b = new nS, _.Tm(this, "gmp-click", b), this.dispatchEvent(b);
                    this.ui.Kg(a)
                }
            }
            xA(a) {
                var b = a.target,
                    c = a.relatedTarget;
                if (this !== b)
                    if (a.stopPropagation(), a.stopImmediatePropagation(), console.debug('Focusable child elements in AdvancedMarkerElement are not supported. To make AdvancedMarkerElement focusable, use addListener() to register a "click" event on the AdvancedMarkerElement instance.'),
                        this.ui.Og(this.map), a = [document.body, ..._.az(document.body)], b = a.indexOf(b), c = a.indexOf(c), b === -1 || c === -1) this.focus();
                    else
                        for (c = b > c ? 1 : -1, b += c; b >= 0 && b < a.length; b += c) {
                            const d = a[b];
                            if (this.Am && d === this || !this.contains(d)) {
                                (d instanceof HTMLElement || d instanceof SVGElement) && d.focus();
                                break
                            }
                        }
            }
            Ox(a) {
                this.Ri && this.Ri.Ox();
                this.Nu(a)
            }
            Ks(a) {
                this.Ri && this.Ri.Ks(a)
            }
            Js(a) {
                this.Ri && this.Ri.Js(a)
            }
            qE() {
                return new Promise(a => {
                    if (this.Am) {
                        var b = () => {
                            this.isConnected && this.Up ? setTimeout(() => {
                                    this.focus();
                                    a()
                                },
                                0) : _.SA(_.RA(), b)
                        };
                        b()
                    }
                })
            }
            Bq() {}
            Nx(a) {
                this.Ri && (this.Ri.Nx(a, !this.Po), this.Po && _.Tm(this, "dragcancel"))
            }
            get collisionBehavior() {
                return this.QC
            }
            set collisionBehavior(a) {
                a = this.dh("collisionBehavior", _.fm(_.Zl(_.Zt)), a) || "REQUIRED";
                this.collisionBehavior !== a && (this.QC = a, this.ui.Fg(this.map, this.QC), this.map && (!TR(this) && this.Gh ? hPa(this.Gh.Vg, this) : dS(this, !0)))
            }
            get element() {
                return this
            }
            get by() {
                return gS(this)[0] === this.ql
            }
            get content() {
                const a = gS(this);
                a.length > 1 && console.debug("The content getter of AdvancedMarkerElement only returns the first content when there are multiple contents, use childNodes or children to get all the contents.");
                return a[0]
            }
            set content(a) {
                var b = _.fm(_.dm([_.Yl(Node, "Node"), _.cm(_.Xl)]));
                if (a instanceof _.WD) throw _.Ul(_.qp(this, "`content` invalid: PinElement must currently be assigned as `pinElement.element`."));
                a = this.dh("content", b, a) || this.ql;
                b = gS(this);
                if (b.length !== 1 || b[0] !== a) a !== this.ql ? this.replaceChildren(a) : a === this.ql && this.replaceChildren(), this.Vr()
            }
            Vr() {
                const a = () => {
                    this.Vk && !this.Vk.contains(this.ql) && this.Vk.prepend(this.ql);
                    this.Qk = null;
                    this.Ri && aS(this.Ri);
                    dS(this, !0);
                    eS(this)
                };
                this.ui.Gg(this.map,
                    this.by);
                this.iu.then(() => {
                    a()
                })
            }
            get dragIndicator() {}
            set dragIndicator(a) {}
            get gmpClickable() {
                return this.SC
            }
            set gmpClickable(a) {
                a = this.dh("gmpClickable", _.Ct, a) || !1;
                this.SC !== a && (this.SC = a, eS(this), fS(this))
            }
            get gmpDraggable() {
                return this.qu
            }
            set gmpDraggable(a) {
                a = this.dh("gmpDraggable", _.Ct, a) || !1;
                dRa(this, this.position, a);
                this.JE.set(a);
                this.qu !== a && ((this.qu = a) ? (this.ui.Hg(this.map), this.setAttribute("aria-grabbed", "false"), LQa(this, this.Qz), this.Ri = new sRa(this), RQa(this.Ri)) : (this.removeAttribute("aria-grabbed"),
                    this.RF(this.Qz), this.Ri.dispose(), this.Ri = null), eS(this), fS(this))
            }
            RF(a) {
                var b = this.getAttribute("aria-describedby");
                b = (b ? b.split(" ") : []).filter(c => c !== a);
                b.length > 0 ? this.setAttribute("aria-describedby", b.join(" ")) : this.removeAttribute("aria-describedby")
            }
            get map() {
                return this.Rj
            }
            set map(a) {
                this.setMap(a);
                this.Rj && (a = _.on(this.Rj)) && this.parentElement !== a && a.append(this)
            }
            setMap(a) {
                this.Rj !== a && (a = this.dh("map", _.fm(_.Yl(_.nn, "MapsApiMap")), a), a instanceof _.nn && (a = a.wM), a && this.isConnected ? hS(this) :
                    this.dispose(), this.Rj = a, this.Eq.set("map", this.Rj), this.Rj instanceof _.nn ? (this.Rj && MQa(this, this.Rj), this.Gh = this.Rj.__gm, cRa(this), this.BD = this.Rj.addListener("bounds_changed", () => {
                        iS(this)
                    }), this.UG = this.Rj.addListener("zoom_changed", () => {
                        iS(this)
                    }), this.PF = this.Rj.addListener("projection_changed", () => {
                        iS(this)
                    }), this.rF = this.Rj.addListener("maptypeid_changed", () => {
                        fRa(this)
                    }), Promise.all([this.Gh.Eg, this.Gh.Fg]).then(([b, c]) => {
                        this.Rj === b.map && (this.ZE = c, this.ui.Pg(b.map), c = this.Gh.Dg, this.Po ||
                            _.up(c, "ADVANCED_MARKERS").isAvailable) && (this.Yg = b.Yg, fRa(this))
                    }), gRa(this), hRa(this)) : this.Gh = null)
            }
            get position() {
                return this.su
            }
            set position(a) {
                a = (a = this.dh("position", _.fm(_.Qv), a) || null) && new _.So(a);
                const b = this.su;
                dRa(this, a, this.gmpDraggable);
                if (b && a) {
                    var c = new _.So(b, !0);
                    const d = new _.So(a, !0);
                    c = !c.equals(d)
                } else c = b !== a;
                c && (this.So = (this.su = a) ? new _.om(a) : null, this.CB = !0, a || jS(this, null), this.Eq.set("position", this.So), this.Fw ? NQa(this.map) : eRa(this), this.rl() > 0 && this.ui.Dg(this.map),
                    _.kp(this, "position", b))
            }
            get DK() {
                return this.su
            }
            get title() {
                return this.lr
            }
            set title(a) {
                a = this.dh("title", _.$r, a);
                const b = this.lr;
                a !== this.title && (this.lr = a, this.title && this.ui.Ig(this.map), this.title === "" ? (this.removeAttribute("aria-label"), this.removeAttribute("title")) : (this.setAttribute("aria-label", this.title), this.setAttribute("title", this.title)), this.ow(), _.kp(this, "title", b))
            }
            get zIndex() {
                return this.Xy
            }
            set zIndex(a) {
                a = this.dh("zIndex", _.fm(_.mt), a);
                this.Xy = a == null ? null : a;
                this.style.zIndex =
                    this.Xy == null ? "" : `${this.Xy}`;
                this.zIndex !== null && this.ui.Jg(this.map);
                dS(this)
            }
            get wv() {
                const a = _.Fm(this, "click"),
                    b = _.Fm(this, "gmp-click");
                return a || b || !!this.gmpClickable
            }
            get XE() {
                return this.wv || !!this.gmpDraggable
            }
            get Am() {
                return this.RC
            }
            set Am(a) {
                aRa(this);
                this.RC !== a && (this.RC = a, iS(this))
            }
            get Gt() {
                return this.Wy
            }
            set Gt(a) {
                if (a !== this.Wy) {
                    if (this.Wy = a) this.zz = this.Az = !1, bS(this);
                    this.map && OQa(this, this.map)
                }
            }
            get Qn() {
                return this.Vy
            }
            set Qn(a) {
                a !== this.Vy && (this.Vy = a, this.map && OQa(this, this.map),
                    iS(this), _.Tm(this, "UPDATE_BASEMAP_COLLISION"))
            }
            nv() {
                if (!this.Up) return null;
                if (!this.Qk)
                    for (const c of gS(this)) {
                        var a = this.ID;
                        const {
                            offset: d,
                            size: e
                        } = qPa(this, c);
                        var b = rPa(a);
                        a = b.offsetY + d.y;
                        b = b.offsetX + d.x;
                        a = _.Co(b, a, b + e.width, a + e.height);
                        this.Qk ? this.Qk.extendByBounds(a) : this.Qk = a
                    }
                return this.Qk
            }
            rl() {
                return this.su ? this.su.altitude : 0
            }
            Cx(a, b, c) {
                return this.Rj ? (c = _.kya(this.Rj.getProjection(), this.So, c)) ? a / c * Math.sin(b * Math.PI / 180) : 0 : 0
            }
            bo(a, b, c) {
                if (a) {
                    if (this.Ri) {
                        b = this.Ri;
                        var d = b.Dg;
                        b = (d = d.Gh ?
                            d.Gh.sp : null) && b.Ig && b.Gg && !b.Hg ? sPa(d, b.Ig) : null
                    } else b = null;
                    b && (a = b);
                    this.Dv = a;
                    b = this.Gt;
                    this.Gt = !(!c || !(Math.abs(a.jh) > c.jh / 2 + 512 || Math.abs(a.kh) > c.kh / 2 + 512));
                    b && this.Gt || (this.Lv && this.map && (c = _.Ba(this.map), (c = VR.get(c)) && IQa(c, this)), (new _.Nn(a.jh, a.kh)).equals(this.Up) || (jS(this, new _.Nn(a.jh, a.kh)), this.Ty(this.CB)), this.CB = !1, this.zz = this.Az = !0)
                } else this.Gt = !0, this.Dv = null, jS(this, null)
            }
            Ty(a) {
                this.Qk = null;
                this.Ri && this.Ri.Fg && this.Ri.Ty(this.nv());
                dS(this, a)
            }
            Zl(a) {
                this.TC !== a && (this.TC =
                    a, this.Eq.set("map", this.TC ? this.map : null))
            }
            Ix() {
                if (!TR(this) || this.Qn || !gS(this).length) return null;
                var a = this.map.getProjection();
                if (!a) return null;
                a = a.fromLatLngToPoint(this.So);
                const b = [];
                for (const f of gS(this)) {
                    a: {
                        var c = f;
                        var d = this.Up;
                        var e = this.ID;
                        if (!d) {
                            d = {
                                size: new _.Pn(0, 0),
                                offset: new _.Nn(0, 0)
                            };
                            break a
                        }
                        const {
                            size: l,
                            offset: n
                        } = qPa(this, c);c = rPa(e);d = {
                            size: l,
                            offset: new _.Nn(c.offsetX - d.x + n.x, c.offsetY - d.y + n.y)
                        }
                    }
                    const {
                        size: g,
                        offset: h
                    } = d;d = new jRa(a.x, a.y, g.width, g.height, h.x, h.y);b.push(d)
                }
                return b
            }
            Bs() {}
            vp() {
                return this
            }
            WE(a) {
                return !this.position ||
                    this.Vy ? !1 : oPa(a, this)
            }
            ow() {
                const a = this.vp();
                this.Am ? a.setAttribute("role", "button") : this.title ? a.setAttribute("role", "img") : a.removeAttribute("role")
            }
            get Nk() {
                return this.Ri ? this.Ri.Nk() : !1
            }
            Wl() {
                jS(this, null);
                bS(this);
                this.Az && this.Yg && this.Oj && (this.Yg.Cl(this.Oj), this.Oj = null);
                this.remove();
                this.Lv = !0
            }
            dispose() {
                if (!this.PA) {
                    this.PA = !0;
                    try {
                        this.Rj && (hS(this), this.Wl())
                    } finally {
                        this.PA = !1
                    }
                }
            }
            bC(a) {
                {
                    const c = this.Gh ? .get("projectionController");
                    if (this.Gh && a && c) {
                        var b = this.Gh.sp.getBoundingClientRect();
                        a = c.fromContainerPixelToLatLng(new _.Nn(a.clientX - b.left, a.clientY - b.top))
                    } else a = null
                }
                a && (this.position = a)
            }
            jn() {
                var a = this.Gh ? .get("projectionController");
                if (!this.Gh || !a || !this.So) return null;
                a = a.fromLatLngToContainerPixel(this.So);
                const b = this.Gh.sp.getBoundingClientRect();
                return {
                    clientX: a.x + b.left,
                    clientY: a.y + b.top
                }
            }
            connectedCallback() {
                super.connectedCallback();
                this.lj.appendChild(this.tj);
                this.Ri && aS(this.Ri);
                if (this.isConnected && this.parentNode) {
                    const a = wR(this);
                    a ? this.setMap(a) : (hS(this), console.error("AdvancedMarkerElement: parent element must be a <gmp-map>."))
                }
            }
            disconnectedCallback() {
                !this.isConnected &&
                    this.zz && (this.map = null);
                this.Lv = !0;
                super.disconnectedCallback()
            }
        };
    pS.prototype.addListener = pS.prototype.addListener;
    pS.prototype.addEventListener = pS.prototype.addEventListener;
    pS.prototype.constructor = pS.prototype.constructor;
    pS.ki = {
        mi: 181577,
        li: 181576
    };
    _.Na([_.lr({
        Zg: "gmp-clickable",
        type: Boolean,
        eh: !0
    }), _.C("design:type", Object), _.C("design:paramtypes", [Object])], pS.prototype, "gmpClickable", null);
    _.Na([_.lr({
        Jh: _.nu,
        yj: _.Zo,
        eh: !0
    }), _.C("design:type", Object), _.C("design:paramtypes", [Object])], pS.prototype, "position", null);
    _.Na([_.lr({
        Jh: {
            Tj: a => a || "",
            Gj: a => a || null
        },
        eh: !0
    }), _.C("design:type", String), _.C("design:paramtypes", [String])], pS.prototype, "title", null);
    var tRa = !1,
        uRa = class extends pS {
            constructor(a = {}) {
                super(a);
                if (!oS) throw Error("InternalUseAdvancedMarkerView is not allowed to be instantiated directly.");
            }
        };
    _.vo("gmp-internal-use-am", uRa);
    var qS = {
        Marker: _.ho,
        CollisionBehavior: _.Zt,
        Animation: _.yv,
        FI: () => {},
        wz: function(a, b, c) {
            const d = _.lAa();
            if (b instanceof _.fo) jQa(a, b, d);
            else {
                const e = new _.bq;
                jQa(e, b, d);
                const f = new _.bq;
                c || BQa(f, b, d);
                new nRa(a, f, e, c)
            }
        },
        LD: function(a = {}) {
            oS = !0;
            a = new uRa(a);
            oS = !1;
            return a
        },
        AdvancedMarkerElement: pS,
        PinElement: _.WD,
        AdvancedMarkerClickEvent: nS,
        AdvancedMarkerView: void 0,
        PinView: void 0,
        connectForExplicitThirdPartyLoad: () => {
            const a = {
                AdvancedMarkerElement: pS,
                PinElement: _.WD,
                AdvancedMarkerClickEvent: nS,
                AdvancedMarkerView: void 0,
                PinView: void 0
            };
            _.Ll(a);
            _.na.google.maps.marker = a;
            tRa || (tRa = !0, _.vo("gmp-advanced-marker", pS))
        }
    };
    _.Ml(qS, ["FI", "wz", "LD", "connectForExplicitThirdPartyLoad"]);
    _.Ll(qS);
    _.Uk("marker", qS);
});