(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["node_modules_react-uid_dist_es2015_index_js-_49b80"], {

        /***/
        "../../node_modules/react-uid/dist/es2015/Control.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    UIDConsumer: () => ( /* binding */ UIDConsumer),
                    /* harmony export */
                    UIDFork: () => ( /* binding */ UIDFork),
                    /* harmony export */
                    UIDReset: () => ( /* binding */ UIDReset)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?5aae");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/context.js");
                /* harmony import */
                var _UIDComponent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/UIDComponent.js");



                /**
                 * UID isolation component, required for SSR and testing.
                 * Wrap your application with it to guarantee UID consistency between SSR and CSR.
                 * @param {String} [prefix] - prefix for all generated ids
                 * @example
                 * <UIDReset>
                 *    <App />
                 * </UIDReset/>
                 * @see https://github.com/thearnica/react-uid#server-side-friendly-uid
                 */
                var UIDReset = function(_a) {
                    var children = _a.children,
                        _b = _a.prefix,
                        prefix = _b === void 0 ? '' : _b;
                    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_context__WEBPACK_IMPORTED_MODULE_1__.source.Provider, {
                        value: (0, _context__WEBPACK_IMPORTED_MODULE_1__.createSource)(prefix)
                    }, children));
                };
                /**
                 * Creates a sub-ids for nested components, isolating from inside a branch.
                 * Useful for self-contained elements or code splitting
                 * @see https://github.com/thearnica/react-uid#code-splitting
                 */
                var UIDFork = function(_a) {
                    var children = _a.children,
                        _b = _a.prefix,
                        prefix = _b === void 0 ? '' : _b;
                    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(UIDConsumer, null, function(id) {
                        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_context__WEBPACK_IMPORTED_MODULE_1__.source.Provider, {
                            value: (0, _context__WEBPACK_IMPORTED_MODULE_1__.createSource)(id + '-' + prefix)
                        }, children));
                    }));
                };
                /**
                 * UID in form of renderProps. Supports nesting and SSR. Prefer {@link useUID} hook version if possible.
                 * @see https://github.com/thearnica/react-uid#server-side-friendly-uid
                 * @see https://github.com/thearnica/react-uid#react-components
                 * @example
                 * // get UID to connect label to input
                 * <UIDConsumer>
                 *   {(id)} => <label htmlFor={id}><input id={id}/>}
                 * </UIDConsumer>
                 *
                 * // get uid to generate uid for a keys in a list
                 * <UIDConsumer>
                 *   {(, uid)} => items.map(item => <li key={uid(item) />)}
                 * </UIDConsumer>
                 *
                 * @see {@link useUID} - a hook version of this component
                 * @see {@link UID} - not SSR compatible version
                 */
                var UIDConsumer = function(_a) {
                    var name = _a.name,
                        children = _a.children;
                    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_context__WEBPACK_IMPORTED_MODULE_1__.source.Consumer, null, function(value) {
                        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_UIDComponent__WEBPACK_IMPORTED_MODULE_2__.UID, {
                            name: name,
                            idSource: value,
                            children: children
                        }));
                    }));
                };


                /***/
            }),

        /***/
        "../../node_modules/react-uid/dist/es2015/UIDComponent.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    UID: () => ( /* binding */ UID)
                    /* harmony export */
                });
                /* harmony import */
                var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/tslib/tslib.es6.js");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?5aae");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/context.js");



                // --------------------------------------------
                var prefixId = function(id, prefix, name) {
                    var uid = (prefix + id);
                    return String(name ? name(uid) : uid);
                };
                /**
                 * @deprecated
                 * UID in form of renderProps (not SSR friendly)
                 * @see https://github.com/thearnica/react-uid#react-components
                 * @example
                 * // get UID to connect label to input
                 * <UID>
                 *   {(id)} => <label htmlFor={id}><input id={id}/>}
                 * </UID>
                 *
                 * // get uid to generate uid for a keys in a list
                 * <UID>
                 *   {(, uid)} => items.map(item => <li key={uid(item) />)}
                 * </UID>
                 */
                var UID = /** @class */ (function(_super) {
                    (0, tslib__WEBPACK_IMPORTED_MODULE_1__.__extends)(UID, _super);

                    function UID() {
                        var _this = _super !== null && _super.apply(this, arguments) || this;
                        _this.state = {
                            quartz: _this.props.idSource || _context__WEBPACK_IMPORTED_MODULE_2__.counter,
                            prefix: (0, _context__WEBPACK_IMPORTED_MODULE_2__.getPrefix)(_this.props.idSource),
                            id: (0, _context__WEBPACK_IMPORTED_MODULE_2__.getId)(_this.props.idSource || _context__WEBPACK_IMPORTED_MODULE_2__.counter)
                        };
                        _this.uid = function(item) {
                            return prefixId(_this.state.id + '-' + _this.state.quartz.uid(item), _this.state.prefix, _this.props.name);
                        };
                        return _this;
                    }
                    UID.prototype.render = function() {
                        var _a = this.props,
                            children = _a.children,
                            name = _a.name;
                        var _b = this.state,
                            id = _b.id,
                            prefix = _b.prefix;
                        return children(prefixId(id, prefix, name), this.uid);
                    };
                    return UID;
                }(react__WEBPACK_IMPORTED_MODULE_0__.Component));



                /***/
            }),

        /***/
        "../../node_modules/react-uid/dist/es2015/context.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    counter: () => ( /* binding */ counter),
                    /* harmony export */
                    createSource: () => ( /* binding */ createSource),
                    /* harmony export */
                    getId: () => ( /* binding */ getId),
                    /* harmony export */
                    getPrefix: () => ( /* binding */ getPrefix),
                    /* harmony export */
                    source: () => ( /* binding */ source)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?5aae");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _uid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/uid.js");


                var createSource = function(prefix) {
                    if (prefix === void 0) {
                        prefix = '';
                    }
                    return ({
                        value: 1,
                        prefix: prefix,
                        uid: (0, _uid__WEBPACK_IMPORTED_MODULE_1__.generateUID)()
                    });
                };
                var counter = createSource();
                var source = react__WEBPACK_IMPORTED_MODULE_0__.createContext(createSource());
                var getId = function(source) {
                    return source.value++;
                };
                var getPrefix = function(source) {
                    return source ? source.prefix : '';
                };


                /***/
            }),

        /***/
        "../../node_modules/react-uid/dist/es2015/hooks.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    useUID: () => ( /* binding */ useUID),
                    /* harmony export */
                    useUIDSeed: () => ( /* binding */ useUIDSeed)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?5aae");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/context.js");


                var generateUID = function(context) {
                    var quartz = context || _context__WEBPACK_IMPORTED_MODULE_1__.counter;
                    var prefix = (0, _context__WEBPACK_IMPORTED_MODULE_1__.getPrefix)(quartz);
                    var id = (0, _context__WEBPACK_IMPORTED_MODULE_1__.getId)(quartz);
                    var uid = prefix + id;
                    var gen = function(item) {
                        return uid + quartz.uid(item);
                    };
                    return {
                        uid: uid,
                        gen: gen
                    };
                };
                var useUIDState = function() {
                    if (true) {
                        if (!("useContext" in react__WEBPACK_IMPORTED_MODULE_0__)) {
                            throw new Error('Hooks API requires React 16.8+');
                        }
                    }
                    // @ts-ignore
                    return react__WEBPACK_IMPORTED_MODULE_0__.useState(generateUID(react__WEBPACK_IMPORTED_MODULE_0__.useContext(_context__WEBPACK_IMPORTED_MODULE_1__.source)));
                };
                /**
                 * returns and unique id. SSR friendly
                 * returns {String}
                 * @see {@link UIDConsumer}
                 * @see https://github.com/thearnica/react-uid#hooks-168
                 * @example
                 * const id = useUID();
                 * id == 1; // for example
                 */
                var useUID = function() {
                    var uid = useUIDState()[0].uid;
                    return uid;
                };
                /**
                 * returns an uid generator
                 * @see {@link UIDConsumer}
                 * @see https://github.com/thearnica/react-uid#hooks-168
                 * @example
                 * const uid = useUIDSeed();
                 * return (
                 *  <>
                 *    <label for={seed('email')}>Email: </label>
                 *    <input id={seed('email')} name="email" />
                 *    {data.map(item => <div key={seed(item)}>...</div>
                 *  </>
                 * )
                 */
                var useUIDSeed = function() {
                    var gen = useUIDState()[0].gen;
                    return gen;
                };


                /***/
            }),

        /***/
        "../../node_modules/react-uid/dist/es2015/index.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    UID: () => ( /* reexport safe */ _UIDComponent__WEBPACK_IMPORTED_MODULE_1__.UID),
                    /* harmony export */
                    UIDConsumer: () => ( /* reexport safe */ _Control__WEBPACK_IMPORTED_MODULE_2__.UIDConsumer),
                    /* harmony export */
                    UIDFork: () => ( /* reexport safe */ _Control__WEBPACK_IMPORTED_MODULE_2__.UIDFork),
                    /* harmony export */
                    UIDReset: () => ( /* reexport safe */ _Control__WEBPACK_IMPORTED_MODULE_2__.UIDReset),
                    /* harmony export */
                    generateUID: () => ( /* reexport safe */ _uid__WEBPACK_IMPORTED_MODULE_0__.generateUID),
                    /* harmony export */
                    uid: () => ( /* reexport safe */ _uid__WEBPACK_IMPORTED_MODULE_0__.uid),
                    /* harmony export */
                    useUID: () => ( /* reexport safe */ _hooks__WEBPACK_IMPORTED_MODULE_3__.useUID),
                    /* harmony export */
                    useUIDSeed: () => ( /* reexport safe */ _hooks__WEBPACK_IMPORTED_MODULE_3__.useUIDSeed)
                    /* harmony export */
                });
                /* harmony import */
                var _uid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/uid.js");
                /* harmony import */
                var _Control__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/Control.js");
                /* harmony import */
                var _hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/hooks.js");
                /* harmony import */
                var _UIDComponent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/react-uid/dist/es2015/UIDComponent.js");







                /***/
            }),

        /***/
        "../../node_modules/react-uid/dist/es2015/uid.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    generateUID: () => ( /* binding */ generateUID),
                    /* harmony export */
                    uid: () => ( /* binding */ uid)
                    /* harmony export */
                });
                /**
                 * generates a UID factory
                 * @internal
                 * @example
                 * const uid = generateUID();
                 * uid(object) = 1;
                 * uid(object) = 1;
                 * uid(anotherObject) = 2;
                 */
                var generateUID = function() {
                    var counter = 1;
                    var map = new WeakMap();
                    /**
                     * @borrows {uid}
                     */
                    var uid = function(item, index) {
                        if (typeof item === 'number' ||
                            typeof item === 'string') {
                            return index ? "idx-" + index : "val-" + item;
                        }
                        if (!map.has(item)) {
                            map.set(item, counter++);
                            return uid(item);
                        }
                        return 'uid' + map.get(item);
                    };
                    return uid;
                };
                /**
                 * @name uid
                 * returns an UID associated with {item}
                 * @param {Object} item - object to generate UID for
                 * @param {Number} index, a fallback index
                 * @example
                 * uid(object) == 1;
                 * uid(object) == 1;
                 * uid(anotherObject) == 2;
                 * uid("not object", 42) == 42
                 *
                 * @see {@link useUID}
                 */
                var uid = generateUID();


                /***/
            })

    }
])
//# sourceMappingURL=node_modules_react-uid_dist_es2015_index_js-_49b80.cff9be5e437cccda.js.map