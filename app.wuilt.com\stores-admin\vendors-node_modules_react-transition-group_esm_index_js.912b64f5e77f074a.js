(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_react-transition-group_esm_index_js"], {

        /***/
        "../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ _inheritsLoose)
                    /* harmony export */
                });
                /* harmony import */
                var _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js");

                function _inheritsLoose(subClass, superClass) {
                    subClass.prototype = Object.create(superClass.prototype);
                    subClass.prototype.constructor = subClass;
                    (0, _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__["default"])(subClass, superClass);
                }

                /***/
            }),

        /***/
        "../../node_modules/dom-helpers/esm/addClass.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ addClass)
                    /* harmony export */
                });
                /* harmony import */
                var _hasClass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/dom-helpers/esm/hasClass.js");

                /**
                 * Adds a CSS class to a given element.
                 * 
                 * @param element the element
                 * @param className the CSS class name
                 */

                function addClass(element, className) {
                    if (element.classList) element.classList.add(className);
                    else if (!(0, _hasClass__WEBPACK_IMPORTED_MODULE_0__["default"])(element, className))
                        if (typeof element.className === 'string') element.className = element.className + " " + className;
                        else element.setAttribute('class', (element.className && element.className.baseVal || '') + " " + className);
                }

                /***/
            }),

        /***/
        "../../node_modules/dom-helpers/esm/hasClass.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ hasClass)
                    /* harmony export */
                });
                /**
                 * Checks if a given element has a CSS class.
                 * 
                 * @param element the element
                 * @param className the CSS class name
                 */
                function hasClass(element, className) {
                    if (element.classList) return !!className && element.classList.contains(className);
                    return (" " + (element.className.baseVal || element.className) + " ").indexOf(" " + className + " ") !== -1;
                }

                /***/
            }),

        /***/
        "../../node_modules/dom-helpers/esm/removeClass.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ removeClass)
                    /* harmony export */
                });

                function replaceClassName(origClass, classToRemove) {
                    return origClass.replace(new RegExp("(^|\\s)" + classToRemove + "(?:\\s|$)", 'g'), '$1').replace(/\s+/g, ' ').replace(/^\s*|\s*$/g, '');
                }
                /**
                 * Removes a CSS class from a given element.
                 * 
                 * @param element the element
                 * @param className the CSS class name
                 */


                function removeClass(element, className) {
                    if (element.classList) {
                        element.classList.remove(className);
                    } else if (typeof element.className === 'string') {
                        element.className = replaceClassName(element.className, className);
                    } else {
                        element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));
                    }
                }

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/CSSTransition.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/extends.js");
                /* harmony import */
                var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js");
                /* harmony import */
                var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("webpack/sharing/consume/default/prop-types/prop-types");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/ __webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);
                /* harmony import */
                var dom_helpers_addClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/dom-helpers/esm/addClass.js");
                /* harmony import */
                var dom_helpers_removeClass__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/dom-helpers/esm/removeClass.js");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__("webpack/sharing/consume/default/react/react?3966");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);
                /* harmony import */
                var _Transition__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__("../../node_modules/react-transition-group/esm/Transition.js");
                /* harmony import */
                var _utils_PropTypes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__("../../node_modules/react-transition-group/esm/utils/PropTypes.js");
                /* harmony import */
                var _utils_reflow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__("../../node_modules/react-transition-group/esm/utils/reflow.js");











                var _addClass = function addClass(node, classes) {
                    return node && classes && classes.split(' ').forEach(function(c) {
                        return (0, dom_helpers_addClass__WEBPACK_IMPORTED_MODULE_4__["default"])(node, c);
                    });
                };

                var removeClass = function removeClass(node, classes) {
                    return node && classes && classes.split(' ').forEach(function(c) {
                        return (0, dom_helpers_removeClass__WEBPACK_IMPORTED_MODULE_5__["default"])(node, c);
                    });
                };
                /**
                 * A transition component inspired by the excellent
                 * [ng-animate](https://docs.angularjs.org/api/ngAnimate) library, you should
                 * use it if you're using CSS transitions or animations. It's built upon the
                 * [`Transition`](https://reactcommunity.org/react-transition-group/transition)
                 * component, so it inherits all of its props.
                 *
                 * `CSSTransition` applies a pair of class names during the `appear`, `enter`,
                 * and `exit` states of the transition. The first class is applied and then a
                 * second `*-active` class in order to activate the CSS transition. After the
                 * transition, matching `*-done` class names are applied to persist the
                 * transition state.
                 *
                 * ```jsx
                 * function App() {
                 *   const [inProp, setInProp] = useState(false);
                 *   return (
                 *     <div>
                 *       <CSSTransition in={inProp} timeout={200} classNames="my-node">
                 *         <div>
                 *           {"I'll receive my-node-* classes"}
                 *         </div>
                 *       </CSSTransition>
                 *       <button type="button" onClick={() => setInProp(true)}>
                 *         Click to Enter
                 *       </button>
                 *     </div>
                 *   );
                 * }
                 * ```
                 *
                 * When the `in` prop is set to `true`, the child component will first receive
                 * the class `example-enter`, then the `example-enter-active` will be added in
                 * the next tick. `CSSTransition` [forces a
                 * reflow](https://github.com/reactjs/react-transition-group/blob/5007303e729a74be66a21c3e2205e4916821524b/src/CSSTransition.js#L208-L215)
                 * between before adding the `example-enter-active`. This is an important trick
                 * because it allows us to transition between `example-enter` and
                 * `example-enter-active` even though they were added immediately one after
                 * another. Most notably, this is what makes it possible for us to animate
                 * _appearance_.
                 *
                 * ```css
                 * .my-node-enter {
                 *   opacity: 0;
                 * }
                 * .my-node-enter-active {
                 *   opacity: 1;
                 *   transition: opacity 200ms;
                 * }
                 * .my-node-exit {
                 *   opacity: 1;
                 * }
                 * .my-node-exit-active {
                 *   opacity: 0;
                 *   transition: opacity 200ms;
                 * }
                 * ```
                 *
                 * `*-active` classes represent which styles you want to animate **to**, so it's
                 * important to add `transition` declaration only to them, otherwise transitions
                 * might not behave as intended! This might not be obvious when the transitions
                 * are symmetrical, i.e. when `*-enter-active` is the same as `*-exit`, like in
                 * the example above (minus `transition`), but it becomes apparent in more
                 * complex transitions.
                 *
                 * **Note**: If you're using the
                 * [`appear`](http://reactcommunity.org/react-transition-group/transition#Transition-prop-appear)
                 * prop, make sure to define styles for `.appear-*` classes as well.
                 */


                var CSSTransition = /*#__PURE__*/ function(_React$Component) {
                    (0, _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_2__["default"])(CSSTransition, _React$Component);

                    function CSSTransition() {
                        var _this;

                        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
                            args[_key] = arguments[_key];
                        }

                        _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;
                        _this.appliedClasses = {
                            appear: {},
                            enter: {},
                            exit: {}
                        };

                        _this.onEnter = function(maybeNode, maybeAppearing) {
                            var _this$resolveArgument = _this.resolveArguments(maybeNode, maybeAppearing),
                                node = _this$resolveArgument[0],
                                appearing = _this$resolveArgument[1];

                            _this.removeClasses(node, 'exit');

                            _this.addClass(node, appearing ? 'appear' : 'enter', 'base');

                            if (_this.props.onEnter) {
                                _this.props.onEnter(maybeNode, maybeAppearing);
                            }
                        };

                        _this.onEntering = function(maybeNode, maybeAppearing) {
                            var _this$resolveArgument2 = _this.resolveArguments(maybeNode, maybeAppearing),
                                node = _this$resolveArgument2[0],
                                appearing = _this$resolveArgument2[1];

                            var type = appearing ? 'appear' : 'enter';

                            _this.addClass(node, type, 'active');

                            if (_this.props.onEntering) {
                                _this.props.onEntering(maybeNode, maybeAppearing);
                            }
                        };

                        _this.onEntered = function(maybeNode, maybeAppearing) {
                            var _this$resolveArgument3 = _this.resolveArguments(maybeNode, maybeAppearing),
                                node = _this$resolveArgument3[0],
                                appearing = _this$resolveArgument3[1];

                            var type = appearing ? 'appear' : 'enter';

                            _this.removeClasses(node, type);

                            _this.addClass(node, type, 'done');

                            if (_this.props.onEntered) {
                                _this.props.onEntered(maybeNode, maybeAppearing);
                            }
                        };

                        _this.onExit = function(maybeNode) {
                            var _this$resolveArgument4 = _this.resolveArguments(maybeNode),
                                node = _this$resolveArgument4[0];

                            _this.removeClasses(node, 'appear');

                            _this.removeClasses(node, 'enter');

                            _this.addClass(node, 'exit', 'base');

                            if (_this.props.onExit) {
                                _this.props.onExit(maybeNode);
                            }
                        };

                        _this.onExiting = function(maybeNode) {
                            var _this$resolveArgument5 = _this.resolveArguments(maybeNode),
                                node = _this$resolveArgument5[0];

                            _this.addClass(node, 'exit', 'active');

                            if (_this.props.onExiting) {
                                _this.props.onExiting(maybeNode);
                            }
                        };

                        _this.onExited = function(maybeNode) {
                            var _this$resolveArgument6 = _this.resolveArguments(maybeNode),
                                node = _this$resolveArgument6[0];

                            _this.removeClasses(node, 'exit');

                            _this.addClass(node, 'exit', 'done');

                            if (_this.props.onExited) {
                                _this.props.onExited(maybeNode);
                            }
                        };

                        _this.resolveArguments = function(maybeNode, maybeAppearing) {
                            return _this.props.nodeRef ? [_this.props.nodeRef.current, maybeNode] // here `maybeNode` is actually `appearing`
                                :
                                [maybeNode, maybeAppearing];
                        };

                        _this.getClassNames = function(type) {
                            var classNames = _this.props.classNames;
                            var isStringClassNames = typeof classNames === 'string';
                            var prefix = isStringClassNames && classNames ? classNames + "-" : '';
                            var baseClassName = isStringClassNames ? "" + prefix + type : classNames[type];
                            var activeClassName = isStringClassNames ? baseClassName + "-active" : classNames[type + "Active"];
                            var doneClassName = isStringClassNames ? baseClassName + "-done" : classNames[type + "Done"];
                            return {
                                baseClassName: baseClassName,
                                activeClassName: activeClassName,
                                doneClassName: doneClassName
                            };
                        };

                        return _this;
                    }

                    var _proto = CSSTransition.prototype;

                    _proto.addClass = function addClass(node, type, phase) {
                        var className = this.getClassNames(type)[phase + "ClassName"];

                        var _this$getClassNames = this.getClassNames('enter'),
                            doneClassName = _this$getClassNames.doneClassName;

                        if (type === 'appear' && phase === 'done' && doneClassName) {
                            className += " " + doneClassName;
                        } // This is to force a repaint,
                        // which is necessary in order to transition styles when adding a class name.


                        if (phase === 'active') {
                            if (node)(0, _utils_reflow__WEBPACK_IMPORTED_MODULE_7__.forceReflow)(node);
                        }

                        if (className) {
                            this.appliedClasses[type][phase] = className;

                            _addClass(node, className);
                        }
                    };

                    _proto.removeClasses = function removeClasses(node, type) {
                        var _this$appliedClasses$ = this.appliedClasses[type],
                            baseClassName = _this$appliedClasses$.base,
                            activeClassName = _this$appliedClasses$.active,
                            doneClassName = _this$appliedClasses$.done;
                        this.appliedClasses[type] = {};

                        if (baseClassName) {
                            removeClass(node, baseClassName);
                        }

                        if (activeClassName) {
                            removeClass(node, activeClassName);
                        }

                        if (doneClassName) {
                            removeClass(node, doneClassName);
                        }
                    };

                    _proto.render = function render() {
                        var _this$props = this.props,
                            _ = _this$props.classNames,
                            props = (0, _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__["default"])(_this$props, ["classNames"]);

                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement(_Transition__WEBPACK_IMPORTED_MODULE_8__["default"], (0, _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, props, {
                            onEnter: this.onEnter,
                            onEntered: this.onEntered,
                            onEntering: this.onEntering,
                            onExit: this.onExit,
                            onExiting: this.onExiting,
                            onExited: this.onExited
                        }));
                    };

                    return CSSTransition;
                }((react__WEBPACK_IMPORTED_MODULE_6___default().Component));

                CSSTransition.defaultProps = {
                    classNames: ''
                };
                CSSTransition.propTypes = true ? (0, _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, _Transition__WEBPACK_IMPORTED_MODULE_8__["default"].propTypes, {
                    /**
                     * The animation classNames applied to the component as it appears, enters,
                     * exits or has finished the transition. A single name can be provided, which
                     * will be suffixed for each stage, e.g. `classNames="fade"` applies:
                     *
                     * - `fade-appear`, `fade-appear-active`, `fade-appear-done`
                     * - `fade-enter`, `fade-enter-active`, `fade-enter-done`
                     * - `fade-exit`, `fade-exit-active`, `fade-exit-done`
                     *
                     * A few details to note about how these classes are applied:
                     *
                     * 1. They are _joined_ with the ones that are already defined on the child
                     *    component, so if you want to add some base styles, you can use
                     *    `className` without worrying that it will be overridden.
                     *
                     * 2. If the transition component mounts with `in={false}`, no classes are
                     *    applied yet. You might be expecting `*-exit-done`, but if you think
                     *    about it, a component cannot finish exiting if it hasn't entered yet.
                     *
                     * 2. `fade-appear-done` and `fade-enter-done` will _both_ be applied. This
                     *    allows you to define different behavior for when appearing is done and
                     *    when regular entering is done, using selectors like
                     *    `.fade-enter-done:not(.fade-appear-done)`. For example, you could apply
                     *    an epic entrance animation when element first appears in the DOM using
                     *    [Animate.css](https://daneden.github.io/animate.css/). Otherwise you can
                     *    simply use `fade-enter-done` for defining both cases.
                     *
                     * Each individual classNames can also be specified independently like:
                     *
                     * ```js
                     * classNames={{
                     *  appear: 'my-appear',
                     *  appearActive: 'my-active-appear',
                     *  appearDone: 'my-done-appear',
                     *  enter: 'my-enter',
                     *  enterActive: 'my-active-enter',
                     *  enterDone: 'my-done-enter',
                     *  exit: 'my-exit',
                     *  exitActive: 'my-active-exit',
                     *  exitDone: 'my-done-exit',
                     * }}
                     * ```
                     *
                     * If you want to set these classes using CSS Modules:
                     *
                     * ```js
                     * import styles from './styles.css';
                     * ```
                     *
                     * you might want to use camelCase in your CSS file, that way could simply
                     * spread them instead of listing them one by one:
                     *
                     * ```js
                     * classNames={{ ...styles }}
                     * ```
                     *
                     * @type {string | {
                     *  appear?: string,
                     *  appearActive?: string,
                     *  appearDone?: string,
                     *  enter?: string,
                     *  enterActive?: string,
                     *  enterDone?: string,
                     *  exit?: string,
                     *  exitActive?: string,
                     *  exitDone?: string,
                     * }}
                     */
                    classNames: _utils_PropTypes__WEBPACK_IMPORTED_MODULE_9__.classNamesShape,

                    /**
                     * A `<Transition>` callback fired immediately after the 'enter' or 'appear' class is
                     * applied.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * @type Function(node: HtmlElement, isAppearing: bool)
                     */
                    onEnter: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func),

                    /**
                     * A `<Transition>` callback fired immediately after the 'enter-active' or
                     * 'appear-active' class is applied.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * @type Function(node: HtmlElement, isAppearing: bool)
                     */
                    onEntering: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func),

                    /**
                     * A `<Transition>` callback fired immediately after the 'enter' or
                     * 'appear' classes are **removed** and the `done` class is added to the DOM node.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * @type Function(node: HtmlElement, isAppearing: bool)
                     */
                    onEntered: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func),

                    /**
                     * A `<Transition>` callback fired immediately after the 'exit' class is
                     * applied.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed
                     *
                     * @type Function(node: HtmlElement)
                     */
                    onExit: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func),

                    /**
                     * A `<Transition>` callback fired immediately after the 'exit-active' is applied.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed
                     *
                     * @type Function(node: HtmlElement)
                     */
                    onExiting: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func),

                    /**
                     * A `<Transition>` callback fired immediately after the 'exit' classes
                     * are **removed** and the `exit-done` class is added to the DOM node.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed
                     *
                     * @type Function(node: HtmlElement)
                     */
                    onExited: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func)
                }) : 0;
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (CSSTransition);

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/ReplaceTransition.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js");
                /* harmony import */
                var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("webpack/sharing/consume/default/prop-types/prop-types");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/ __webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_2__);
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("webpack/sharing/consume/default/react/react?3966");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
                /* harmony import */
                var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("webpack/sharing/consume/default/react-dom/react-dom?3c2d");
                /* harmony import */
                var react_dom__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/ __webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_4__);
                /* harmony import */
                var _TransitionGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/react-transition-group/esm/TransitionGroup.js");






                /**
                 * The `<ReplaceTransition>` component is a specialized `Transition` component
                 * that animates between two children.
                 *
                 * ```jsx
                 * <ReplaceTransition in>
                 *   <Fade><div>I appear first</div></Fade>
                 *   <Fade><div>I replace the above</div></Fade>
                 * </ReplaceTransition>
                 * ```
                 */

                var ReplaceTransition = /*#__PURE__*/ function(_React$Component) {
                    (0, _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__["default"])(ReplaceTransition, _React$Component);

                    function ReplaceTransition() {
                        var _this;

                        for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {
                            _args[_key] = arguments[_key];
                        }

                        _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;

                        _this.handleEnter = function() {
                            for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
                                args[_key2] = arguments[_key2];
                            }

                            return _this.handleLifecycle('onEnter', 0, args);
                        };

                        _this.handleEntering = function() {
                            for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
                                args[_key3] = arguments[_key3];
                            }

                            return _this.handleLifecycle('onEntering', 0, args);
                        };

                        _this.handleEntered = function() {
                            for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
                                args[_key4] = arguments[_key4];
                            }

                            return _this.handleLifecycle('onEntered', 0, args);
                        };

                        _this.handleExit = function() {
                            for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {
                                args[_key5] = arguments[_key5];
                            }

                            return _this.handleLifecycle('onExit', 1, args);
                        };

                        _this.handleExiting = function() {
                            for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {
                                args[_key6] = arguments[_key6];
                            }

                            return _this.handleLifecycle('onExiting', 1, args);
                        };

                        _this.handleExited = function() {
                            for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {
                                args[_key7] = arguments[_key7];
                            }

                            return _this.handleLifecycle('onExited', 1, args);
                        };

                        return _this;
                    }

                    var _proto = ReplaceTransition.prototype;

                    _proto.handleLifecycle = function handleLifecycle(handler, idx, originalArgs) {
                        var _child$props;

                        var children = this.props.children;
                        var child = react__WEBPACK_IMPORTED_MODULE_3___default().Children.toArray(children)[idx];
                        if (child.props[handler])(_child$props = child.props)[handler].apply(_child$props, originalArgs);

                        if (this.props[handler]) {
                            var maybeNode = child.props.nodeRef ? undefined : react_dom__WEBPACK_IMPORTED_MODULE_4___default().findDOMNode(this);
                            this.props[handler](maybeNode);
                        }
                    };

                    _proto.render = function render() {
                        var _this$props = this.props,
                            children = _this$props.children,
                            inProp = _this$props.in,
                            props = (0, _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(_this$props, ["children", "in"]);

                        var _React$Children$toArr = react__WEBPACK_IMPORTED_MODULE_3___default().Children.toArray(children),
                            first = _React$Children$toArr[0],
                            second = _React$Children$toArr[1];

                        delete props.onEnter;
                        delete props.onEntering;
                        delete props.onEntered;
                        delete props.onExit;
                        delete props.onExiting;
                        delete props.onExited;
                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3___default().createElement(_TransitionGroup__WEBPACK_IMPORTED_MODULE_5__["default"], props, inProp ? react__WEBPACK_IMPORTED_MODULE_3___default().cloneElement(first, {
                            key: 'first',
                            onEnter: this.handleEnter,
                            onEntering: this.handleEntering,
                            onEntered: this.handleEntered
                        }) : react__WEBPACK_IMPORTED_MODULE_3___default().cloneElement(second, {
                            key: 'second',
                            onEnter: this.handleExit,
                            onEntering: this.handleExiting,
                            onEntered: this.handleExited
                        }));
                    };

                    return ReplaceTransition;
                }((react__WEBPACK_IMPORTED_MODULE_3___default().Component));

                ReplaceTransition.propTypes = true ? { in: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool).isRequired,
                    children: function children(props, propName) {
                        if (react__WEBPACK_IMPORTED_MODULE_3___default().Children.count(props[propName]) !== 2) return new Error("\"" + propName + "\" must be exactly two transition components.");
                        return null;
                    }
                } : 0;
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (ReplaceTransition);

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/SwitchTransition.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    modes: () => ( /* binding */ modes)
                    /* harmony export */
                });
                /* harmony import */
                var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("webpack/sharing/consume/default/react/react?3966");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("webpack/sharing/consume/default/prop-types/prop-types");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/ __webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_2__);
                /* harmony import */
                var _Transition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/react-transition-group/esm/Transition.js");
                /* harmony import */
                var _TransitionGroupContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/react-transition-group/esm/TransitionGroupContext.js");


                var _leaveRenders, _enterRenders;






                function areChildrenDifferent(oldChildren, newChildren) {
                    if (oldChildren === newChildren) return false;

                    if (react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(oldChildren) && react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(newChildren) && oldChildren.key != null && oldChildren.key === newChildren.key) {
                        return false;
                    }

                    return true;
                }
                /**
                 * Enum of modes for SwitchTransition component
                 * @enum { string }
                 */


                var modes = {
                    out: 'out-in',
                    in: 'in-out'
                };

                var callHook = function callHook(element, name, cb) {
                    return function() {
                        var _element$props;

                        element.props[name] && (_element$props = element.props)[name].apply(_element$props, arguments);
                        cb();
                    };
                };

                var leaveRenders = (_leaveRenders = {}, _leaveRenders[modes.out] = function(_ref) {
                    var current = _ref.current,
                        changeState = _ref.changeState;
                    return react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(current, { in: false,
                        onExited: callHook(current, 'onExited', function() {
                            changeState(_Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERING, null);
                        })
                    });
                }, _leaveRenders[modes.in] = function(_ref2) {
                    var current = _ref2.current,
                        changeState = _ref2.changeState,
                        children = _ref2.children;
                    return [current, react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, { in: true,
                        onEntered: callHook(children, 'onEntered', function() {
                            changeState(_Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERING);
                        })
                    })];
                }, _leaveRenders);
                var enterRenders = (_enterRenders = {}, _enterRenders[modes.out] = function(_ref3) {
                    var children = _ref3.children,
                        changeState = _ref3.changeState;
                    return react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, { in: true,
                        onEntered: callHook(children, 'onEntered', function() {
                            changeState(_Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERED, react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, { in: true
                            }));
                        })
                    });
                }, _enterRenders[modes.in] = function(_ref4) {
                    var current = _ref4.current,
                        children = _ref4.children,
                        changeState = _ref4.changeState;
                    return [react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(current, { in: false,
                        onExited: callHook(current, 'onExited', function() {
                            changeState(_Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERED, react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, { in: true
                            }));
                        })
                    }), react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, { in: true
                    })];
                }, _enterRenders);
                /**
                 * A transition component inspired by the [vue transition modes](https://vuejs.org/v2/guide/transitions.html#Transition-Modes).
                 * You can use it when you want to control the render between state transitions.
                 * Based on the selected mode and the child's key which is the `Transition` or `CSSTransition` component, the `SwitchTransition` makes a consistent transition between them.
                 *
                 * If the `out-in` mode is selected, the `SwitchTransition` waits until the old child leaves and then inserts a new child.
                 * If the `in-out` mode is selected, the `SwitchTransition` inserts a new child first, waits for the new child to enter and then removes the old child.
                 *
                 * **Note**: If you want the animation to happen simultaneously
                 * (that is, to have the old child removed and a new child inserted **at the same time**),
                 * you should use
                 * [`TransitionGroup`](https://reactcommunity.org/react-transition-group/transition-group)
                 * instead.
                 *
                 * ```jsx
                 * function App() {
                 *  const [state, setState] = useState(false);
                 *  return (
                 *    <SwitchTransition>
                 *      <CSSTransition
                 *        key={state ? "Goodbye, world!" : "Hello, world!"}
                 *        addEndListener={(node, done) => node.addEventListener("transitionend", done, false)}
                 *        classNames='fade'
                 *      >
                 *        <button onClick={() => setState(state => !state)}>
                 *          {state ? "Goodbye, world!" : "Hello, world!"}
                 *        </button>
                 *      </CSSTransition>
                 *    </SwitchTransition>
                 *  );
                 * }
                 * ```
                 *
                 * ```css
                 * .fade-enter{
                 *    opacity: 0;
                 * }
                 * .fade-exit{
                 *    opacity: 1;
                 * }
                 * .fade-enter-active{
                 *    opacity: 1;
                 * }
                 * .fade-exit-active{
                 *    opacity: 0;
                 * }
                 * .fade-enter-active,
                 * .fade-exit-active{
                 *    transition: opacity 500ms;
                 * }
                 * ```
                 */

                var SwitchTransition = /*#__PURE__*/ function(_React$Component) {
                    (0, _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(SwitchTransition, _React$Component);

                    function SwitchTransition() {
                        var _this;

                        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
                            args[_key] = arguments[_key];
                        }

                        _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;
                        _this.state = {
                            status: _Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERED,
                            current: null
                        };
                        _this.appeared = false;

                        _this.changeState = function(status, current) {
                            if (current === void 0) {
                                current = _this.state.current;
                            }

                            _this.setState({
                                status: status,
                                current: current
                            });
                        };

                        return _this;
                    }

                    var _proto = SwitchTransition.prototype;

                    _proto.componentDidMount = function componentDidMount() {
                        this.appeared = true;
                    };

                    SwitchTransition.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {
                        if (props.children == null) {
                            return {
                                current: null
                            };
                        }

                        if (state.status === _Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERING && props.mode === modes.in) {
                            return {
                                status: _Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERING
                            };
                        }

                        if (state.current && areChildrenDifferent(state.current, props.children)) {
                            return {
                                status: _Transition__WEBPACK_IMPORTED_MODULE_3__.EXITING
                            };
                        }

                        return {
                            current: react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(props.children, { in: true
                            })
                        };
                    };

                    _proto.render = function render() {
                        var _this$props = this.props,
                            children = _this$props.children,
                            mode = _this$props.mode,
                            _this$state = this.state,
                            status = _this$state.status,
                            current = _this$state.current;
                        var data = {
                            children: children,
                            current: current,
                            changeState: this.changeState,
                            status: status
                        };
                        var component;

                        switch (status) {
                            case _Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERING:
                                component = enterRenders[mode](data);
                                break;

                            case _Transition__WEBPACK_IMPORTED_MODULE_3__.EXITING:
                                component = leaveRenders[mode](data);
                                break;

                            case _Transition__WEBPACK_IMPORTED_MODULE_3__.ENTERED:
                                component = current;
                        }

                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_TransitionGroupContext__WEBPACK_IMPORTED_MODULE_4__["default"].Provider, {
                            value: {
                                isMounting: !this.appeared
                            }
                        }, component);
                    };

                    return SwitchTransition;
                }((react__WEBPACK_IMPORTED_MODULE_1___default().Component));

                SwitchTransition.propTypes = true ? {
                    /**
                     * Transition modes.
                     * `out-in`: Current element transitions out first, then when complete, the new element transitions in.
                     * `in-out`: New element transitions in first, then when complete, the current element transitions out.
                     *
                     * @type {'out-in'|'in-out'}
                     */
                    mode: prop_types__WEBPACK_IMPORTED_MODULE_2___default().oneOf([modes.in, modes.out]),

                    /**
                     * Any `Transition` or `CSSTransition` component.
                     */
                    children: prop_types__WEBPACK_IMPORTED_MODULE_2___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_2___default().element).isRequired])
                } : 0;
                SwitchTransition.defaultProps = {
                    mode: modes.out
                };
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (SwitchTransition);

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/Transition.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    ENTERED: () => ( /* binding */ ENTERED),
                    /* harmony export */
                    ENTERING: () => ( /* binding */ ENTERING),
                    /* harmony export */
                    EXITED: () => ( /* binding */ EXITED),
                    /* harmony export */
                    EXITING: () => ( /* binding */ EXITING),
                    /* harmony export */
                    UNMOUNTED: () => ( /* binding */ UNMOUNTED),
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js");
                /* harmony import */
                var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("webpack/sharing/consume/default/prop-types/prop-types");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/ __webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_2__);
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("webpack/sharing/consume/default/react/react?3966");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
                /* harmony import */
                var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("webpack/sharing/consume/default/react-dom/react-dom?3c2d");
                /* harmony import */
                var react_dom__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/ __webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_4__);
                /* harmony import */
                var _config__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__("../../node_modules/react-transition-group/esm/config.js");
                /* harmony import */
                var _utils_PropTypes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__("../../node_modules/react-transition-group/esm/utils/PropTypes.js");
                /* harmony import */
                var _TransitionGroupContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__("../../node_modules/react-transition-group/esm/TransitionGroupContext.js");
                /* harmony import */
                var _utils_reflow__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/react-transition-group/esm/utils/reflow.js");









                var UNMOUNTED = 'unmounted';
                var EXITED = 'exited';
                var ENTERING = 'entering';
                var ENTERED = 'entered';
                var EXITING = 'exiting';
                /**
                 * The Transition component lets you describe a transition from one component
                 * state to another _over time_ with a simple declarative API. Most commonly
                 * it's used to animate the mounting and unmounting of a component, but can also
                 * be used to describe in-place transition states as well.
                 *
                 * ---
                 *
                 * **Note**: `Transition` is a platform-agnostic base component. If you're using
                 * transitions in CSS, you'll probably want to use
                 * [`CSSTransition`](https://reactcommunity.org/react-transition-group/css-transition)
                 * instead. It inherits all the features of `Transition`, but contains
                 * additional features necessary to play nice with CSS transitions (hence the
                 * name of the component).
                 *
                 * ---
                 *
                 * By default the `Transition` component does not alter the behavior of the
                 * component it renders, it only tracks "enter" and "exit" states for the
                 * components. It's up to you to give meaning and effect to those states. For
                 * example we can add styles to a component when it enters or exits:
                 *
                 * ```jsx
                 * import { Transition } from 'react-transition-group';
                 *
                 * const duration = 300;
                 *
                 * const defaultStyle = {
                 *   transition: `opacity ${duration}ms ease-in-out`,
                 *   opacity: 0,
                 * }
                 *
                 * const transitionStyles = {
                 *   entering: { opacity: 1 },
                 *   entered:  { opacity: 1 },
                 *   exiting:  { opacity: 0 },
                 *   exited:  { opacity: 0 },
                 * };
                 *
                 * const Fade = ({ in: inProp }) => (
                 *   <Transition in={inProp} timeout={duration}>
                 *     {state => (
                 *       <div style={{
                 *         ...defaultStyle,
                 *         ...transitionStyles[state]
                 *       }}>
                 *         I'm a fade Transition!
                 *       </div>
                 *     )}
                 *   </Transition>
                 * );
                 * ```
                 *
                 * There are 4 main states a Transition can be in:
                 *  - `'entering'`
                 *  - `'entered'`
                 *  - `'exiting'`
                 *  - `'exited'`
                 *
                 * Transition state is toggled via the `in` prop. When `true` the component
                 * begins the "Enter" stage. During this stage, the component will shift from
                 * its current transition state, to `'entering'` for the duration of the
                 * transition and then to the `'entered'` stage once it's complete. Let's take
                 * the following example (we'll use the
                 * [useState](https://reactjs.org/docs/hooks-reference.html#usestate) hook):
                 *
                 * ```jsx
                 * function App() {
                 *   const [inProp, setInProp] = useState(false);
                 *   return (
                 *     <div>
                 *       <Transition in={inProp} timeout={500}>
                 *         {state => (
                 *           // ...
                 *         )}
                 *       </Transition>
                 *       <button onClick={() => setInProp(true)}>
                 *         Click to Enter
                 *       </button>
                 *     </div>
                 *   );
                 * }
                 * ```
                 *
                 * When the button is clicked the component will shift to the `'entering'` state
                 * and stay there for 500ms (the value of `timeout`) before it finally switches
                 * to `'entered'`.
                 *
                 * When `in` is `false` the same thing happens except the state moves from
                 * `'exiting'` to `'exited'`.
                 */

                var Transition = /*#__PURE__*/ function(_React$Component) {
                    (0, _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__["default"])(Transition, _React$Component);

                    function Transition(props, context) {
                        var _this;

                        _this = _React$Component.call(this, props, context) || this;
                        var parentGroup = context; // In the context of a TransitionGroup all enters are really appears

                        var appear = parentGroup && !parentGroup.isMounting ? props.enter : props.appear;
                        var initialStatus;
                        _this.appearStatus = null;

                        if (props.in) {
                            if (appear) {
                                initialStatus = EXITED;
                                _this.appearStatus = ENTERING;
                            } else {
                                initialStatus = ENTERED;
                            }
                        } else {
                            if (props.unmountOnExit || props.mountOnEnter) {
                                initialStatus = UNMOUNTED;
                            } else {
                                initialStatus = EXITED;
                            }
                        }

                        _this.state = {
                            status: initialStatus
                        };
                        _this.nextCallback = null;
                        return _this;
                    }

                    Transition.getDerivedStateFromProps = function getDerivedStateFromProps(_ref, prevState) {
                        var nextIn = _ref.in;

                        if (nextIn && prevState.status === UNMOUNTED) {
                            return {
                                status: EXITED
                            };
                        }

                        return null;
                    } // getSnapshotBeforeUpdate(prevProps) {
                    //   let nextStatus = null
                    //   if (prevProps !== this.props) {
                    //     const { status } = this.state
                    //     if (this.props.in) {
                    //       if (status !== ENTERING && status !== ENTERED) {
                    //         nextStatus = ENTERING
                    //       }
                    //     } else {
                    //       if (status === ENTERING || status === ENTERED) {
                    //         nextStatus = EXITING
                    //       }
                    //     }
                    //   }
                    //   return { nextStatus }
                    // }
                    ;

                    var _proto = Transition.prototype;

                    _proto.componentDidMount = function componentDidMount() {
                        this.updateStatus(true, this.appearStatus);
                    };

                    _proto.componentDidUpdate = function componentDidUpdate(prevProps) {
                        var nextStatus = null;

                        if (prevProps !== this.props) {
                            var status = this.state.status;

                            if (this.props.in) {
                                if (status !== ENTERING && status !== ENTERED) {
                                    nextStatus = ENTERING;
                                }
                            } else {
                                if (status === ENTERING || status === ENTERED) {
                                    nextStatus = EXITING;
                                }
                            }
                        }

                        this.updateStatus(false, nextStatus);
                    };

                    _proto.componentWillUnmount = function componentWillUnmount() {
                        this.cancelNextCallback();
                    };

                    _proto.getTimeouts = function getTimeouts() {
                        var timeout = this.props.timeout;
                        var exit, enter, appear;
                        exit = enter = appear = timeout;

                        if (timeout != null && typeof timeout !== 'number') {
                            exit = timeout.exit;
                            enter = timeout.enter; // TODO: remove fallback for next major

                            appear = timeout.appear !== undefined ? timeout.appear : enter;
                        }

                        return {
                            exit: exit,
                            enter: enter,
                            appear: appear
                        };
                    };

                    _proto.updateStatus = function updateStatus(mounting, nextStatus) {
                        if (mounting === void 0) {
                            mounting = false;
                        }

                        if (nextStatus !== null) {
                            // nextStatus will always be ENTERING or EXITING.
                            this.cancelNextCallback();

                            if (nextStatus === ENTERING) {
                                if (this.props.unmountOnExit || this.props.mountOnEnter) {
                                    var node = this.props.nodeRef ? this.props.nodeRef.current : react_dom__WEBPACK_IMPORTED_MODULE_4___default().findDOMNode(this); // https://github.com/reactjs/react-transition-group/pull/749
                                    // With unmountOnExit or mountOnEnter, the enter animation should happen at the transition between `exited` and `entering`.
                                    // To make the animation happen,  we have to separate each rendering and avoid being processed as batched.

                                    if (node)(0, _utils_reflow__WEBPACK_IMPORTED_MODULE_5__.forceReflow)(node);
                                }

                                this.performEnter(mounting);
                            } else {
                                this.performExit();
                            }
                        } else if (this.props.unmountOnExit && this.state.status === EXITED) {
                            this.setState({
                                status: UNMOUNTED
                            });
                        }
                    };

                    _proto.performEnter = function performEnter(mounting) {
                        var _this2 = this;

                        var enter = this.props.enter;
                        var appearing = this.context ? this.context.isMounting : mounting;

                        var _ref2 = this.props.nodeRef ? [appearing] : [react_dom__WEBPACK_IMPORTED_MODULE_4___default().findDOMNode(this), appearing],
                            maybeNode = _ref2[0],
                            maybeAppearing = _ref2[1];

                        var timeouts = this.getTimeouts();
                        var enterTimeout = appearing ? timeouts.appear : timeouts.enter; // no enter animation skip right to ENTERED
                        // if we are mounting and running this it means appear _must_ be set

                        if (!mounting && !enter || _config__WEBPACK_IMPORTED_MODULE_6__["default"].disabled) {
                            this.safeSetState({
                                status: ENTERED
                            }, function() {
                                _this2.props.onEntered(maybeNode);
                            });
                            return;
                        }

                        this.props.onEnter(maybeNode, maybeAppearing);
                        this.safeSetState({
                            status: ENTERING
                        }, function() {
                            _this2.props.onEntering(maybeNode, maybeAppearing);

                            _this2.onTransitionEnd(enterTimeout, function() {
                                _this2.safeSetState({
                                    status: ENTERED
                                }, function() {
                                    _this2.props.onEntered(maybeNode, maybeAppearing);
                                });
                            });
                        });
                    };

                    _proto.performExit = function performExit() {
                        var _this3 = this;

                        var exit = this.props.exit;
                        var timeouts = this.getTimeouts();
                        var maybeNode = this.props.nodeRef ? undefined : react_dom__WEBPACK_IMPORTED_MODULE_4___default().findDOMNode(this); // no exit animation skip right to EXITED

                        if (!exit || _config__WEBPACK_IMPORTED_MODULE_6__["default"].disabled) {
                            this.safeSetState({
                                status: EXITED
                            }, function() {
                                _this3.props.onExited(maybeNode);
                            });
                            return;
                        }

                        this.props.onExit(maybeNode);
                        this.safeSetState({
                            status: EXITING
                        }, function() {
                            _this3.props.onExiting(maybeNode);

                            _this3.onTransitionEnd(timeouts.exit, function() {
                                _this3.safeSetState({
                                    status: EXITED
                                }, function() {
                                    _this3.props.onExited(maybeNode);
                                });
                            });
                        });
                    };

                    _proto.cancelNextCallback = function cancelNextCallback() {
                        if (this.nextCallback !== null) {
                            this.nextCallback.cancel();
                            this.nextCallback = null;
                        }
                    };

                    _proto.safeSetState = function safeSetState(nextState, callback) {
                        // This shouldn't be necessary, but there are weird race conditions with
                        // setState callbacks and unmounting in testing, so always make sure that
                        // we can cancel any pending setState callbacks after we unmount.
                        callback = this.setNextCallback(callback);
                        this.setState(nextState, callback);
                    };

                    _proto.setNextCallback = function setNextCallback(callback) {
                        var _this4 = this;

                        var active = true;

                        this.nextCallback = function(event) {
                            if (active) {
                                active = false;
                                _this4.nextCallback = null;
                                callback(event);
                            }
                        };

                        this.nextCallback.cancel = function() {
                            active = false;
                        };

                        return this.nextCallback;
                    };

                    _proto.onTransitionEnd = function onTransitionEnd(timeout, handler) {
                        this.setNextCallback(handler);
                        var node = this.props.nodeRef ? this.props.nodeRef.current : react_dom__WEBPACK_IMPORTED_MODULE_4___default().findDOMNode(this);
                        var doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;

                        if (!node || doesNotHaveTimeoutOrListener) {
                            setTimeout(this.nextCallback, 0);
                            return;
                        }

                        if (this.props.addEndListener) {
                            var _ref3 = this.props.nodeRef ? [this.nextCallback] : [node, this.nextCallback],
                                maybeNode = _ref3[0],
                                maybeNextCallback = _ref3[1];

                            this.props.addEndListener(maybeNode, maybeNextCallback);
                        }

                        if (timeout != null) {
                            setTimeout(this.nextCallback, timeout);
                        }
                    };

                    _proto.render = function render() {
                        var status = this.state.status;

                        if (status === UNMOUNTED) {
                            return null;
                        }

                        var _this$props = this.props,
                            children = _this$props.children,
                            _in = _this$props.in,
                            _mountOnEnter = _this$props.mountOnEnter,
                            _unmountOnExit = _this$props.unmountOnExit,
                            _appear = _this$props.appear,
                            _enter = _this$props.enter,
                            _exit = _this$props.exit,
                            _timeout = _this$props.timeout,
                            _addEndListener = _this$props.addEndListener,
                            _onEnter = _this$props.onEnter,
                            _onEntering = _this$props.onEntering,
                            _onEntered = _this$props.onEntered,
                            _onExit = _this$props.onExit,
                            _onExiting = _this$props.onExiting,
                            _onExited = _this$props.onExited,
                            _nodeRef = _this$props.nodeRef,
                            childProps = (0, _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(_this$props, ["children", "in", "mountOnEnter", "unmountOnExit", "appear", "enter", "exit", "timeout", "addEndListener", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "onExited", "nodeRef"]);

                        return (
                            /*#__PURE__*/
                            // allows for nested Transitions
                            react__WEBPACK_IMPORTED_MODULE_3___default().createElement(_TransitionGroupContext__WEBPACK_IMPORTED_MODULE_7__["default"].Provider, {
                                value: null
                            }, typeof children === 'function' ? children(status, childProps) : react__WEBPACK_IMPORTED_MODULE_3___default().cloneElement(react__WEBPACK_IMPORTED_MODULE_3___default().Children.only(children), childProps))
                        );
                    };

                    return Transition;
                }((react__WEBPACK_IMPORTED_MODULE_3___default().Component));

                Transition.contextType = _TransitionGroupContext__WEBPACK_IMPORTED_MODULE_7__["default"];
                Transition.propTypes = true ? {
                    /**
                     * A React reference to DOM element that need to transition:
                     * https://stackoverflow.com/a/51127130/4671932
                     *
                     *   - When `nodeRef` prop is used, `node` is not passed to callback functions
                     *      (e.g. `onEnter`) because user already has direct access to the node.
                     *   - When changing `key` prop of `Transition` in a `TransitionGroup` a new
                     *     `nodeRef` need to be provided to `Transition` with changed `key` prop
                     *     (see
                     *     [test/CSSTransition-test.js](https://github.com/reactjs/react-transition-group/blob/13435f897b3ab71f6e19d724f145596f5910581c/test/CSSTransition-test.js#L362-L437)).
                     */
                    nodeRef: prop_types__WEBPACK_IMPORTED_MODULE_2___default().shape({
                        current: typeof Element === 'undefined' ? (prop_types__WEBPACK_IMPORTED_MODULE_2___default().any) : function(propValue, key, componentName, location, propFullName, secret) {
                            var value = propValue[key];
                            return prop_types__WEBPACK_IMPORTED_MODULE_2___default().instanceOf(value && 'ownerDocument' in value ? value.ownerDocument.defaultView.Element : Element)(propValue, key, componentName, location, propFullName, secret);
                        }
                    }),

                    /**
                     * A `function` child can be used instead of a React element. This function is
                     * called with the current transition status (`'entering'`, `'entered'`,
                     * `'exiting'`, `'exited'`), which can be used to apply context
                     * specific props to a component.
                     *
                     * ```jsx
                     * <Transition in={this.state.in} timeout={150}>
                     *   {state => (
                     *     <MyComponent className={`fade fade-${state}`} />
                     *   )}
                     * </Transition>
                     * ```
                     */
                    children: prop_types__WEBPACK_IMPORTED_MODULE_2___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_2___default().func).isRequired, (prop_types__WEBPACK_IMPORTED_MODULE_2___default().element).isRequired]).isRequired,

                    /**
                     * Show the component; triggers the enter or exit states
                     */
                    in: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),

                    /**
                     * By default the child component is mounted immediately along with
                     * the parent `Transition` component. If you want to "lazy mount" the component on the
                     * first `in={true}` you can set `mountOnEnter`. After the first enter transition the component will stay
                     * mounted, even on "exited", unless you also specify `unmountOnExit`.
                     */
                    mountOnEnter: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),

                    /**
                     * By default the child component stays mounted after it reaches the `'exited'` state.
                     * Set `unmountOnExit` if you'd prefer to unmount the component after it finishes exiting.
                     */
                    unmountOnExit: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),

                    /**
                     * By default the child component does not perform the enter transition when
                     * it first mounts, regardless of the value of `in`. If you want this
                     * behavior, set both `appear` and `in` to `true`.
                     *
                     * > **Note**: there are no special appear states like `appearing`/`appeared`, this prop
                     * > only adds an additional enter transition. However, in the
                     * > `<CSSTransition>` component that first enter transition does result in
                     * > additional `.appear-*` classes, that way you can choose to style it
                     * > differently.
                     */
                    appear: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),

                    /**
                     * Enable or disable enter transitions.
                     */
                    enter: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),

                    /**
                     * Enable or disable exit transitions.
                     */
                    exit: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),

                    /**
                     * The duration of the transition, in milliseconds.
                     * Required unless `addEndListener` is provided.
                     *
                     * You may specify a single timeout for all transitions:
                     *
                     * ```jsx
                     * timeout={500}
                     * ```
                     *
                     * or individually:
                     *
                     * ```jsx
                     * timeout={{
                     *  appear: 500,
                     *  enter: 300,
                     *  exit: 500,
                     * }}
                     * ```
                     *
                     * - `appear` defaults to the value of `enter`
                     * - `enter` defaults to `0`
                     * - `exit` defaults to `0`
                     *
                     * @type {number | { enter?: number, exit?: number, appear?: number }}
                     */
                    timeout: function timeout(props) {
                        var pt = _utils_PropTypes__WEBPACK_IMPORTED_MODULE_8__.timeoutsShape;
                        if (!props.addEndListener) pt = pt.isRequired;

                        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
                            args[_key - 1] = arguments[_key];
                        }

                        return pt.apply(void 0, [props].concat(args));
                    },

                    /**
                     * Add a custom transition end trigger. Called with the transitioning
                     * DOM node and a `done` callback. Allows for more fine grained transition end
                     * logic. Timeouts are still used as a fallback if provided.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * ```jsx
                     * addEndListener={(node, done) => {
                     *   // use the css transitionend event to mark the finish of a transition
                     *   node.addEventListener('transitionend', done, false);
                     * }}
                     * ```
                     */
                    addEndListener: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),

                    /**
                     * Callback fired before the "entering" status is applied. An extra parameter
                     * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * @type Function(node: HtmlElement, isAppearing: bool) -> void
                     */
                    onEnter: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),

                    /**
                     * Callback fired after the "entering" status is applied. An extra parameter
                     * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * @type Function(node: HtmlElement, isAppearing: bool)
                     */
                    onEntering: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),

                    /**
                     * Callback fired after the "entered" status is applied. An extra parameter
                     * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * @type Function(node: HtmlElement, isAppearing: bool) -> void
                     */
                    onEntered: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),

                    /**
                     * Callback fired before the "exiting" status is applied.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * @type Function(node: HtmlElement) -> void
                     */
                    onExit: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),

                    /**
                     * Callback fired after the "exiting" status is applied.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed.
                     *
                     * @type Function(node: HtmlElement) -> void
                     */
                    onExiting: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),

                    /**
                     * Callback fired after the "exited" status is applied.
                     *
                     * **Note**: when `nodeRef` prop is passed, `node` is not passed
                     *
                     * @type Function(node: HtmlElement) -> void
                     */
                    onExited: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func)
                } : 0; // Name the function so it is clearer in the documentation

                function noop() {}

                Transition.defaultProps = { in: false,
                    mountOnEnter: false,
                    unmountOnExit: false,
                    appear: false,
                    enter: true,
                    exit: true,
                    onEnter: noop,
                    onEntering: noop,
                    onEntered: noop,
                    onExit: noop,
                    onExiting: noop,
                    onExited: noop
                };
                Transition.UNMOUNTED = UNMOUNTED;
                Transition.EXITED = EXITED;
                Transition.ENTERING = ENTERING;
                Transition.ENTERED = ENTERED;
                Transition.EXITING = EXITING;
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (Transition);

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/TransitionGroup.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js");
                /* harmony import */
                var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/extends.js");
                /* harmony import */
                var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js");
                /* harmony import */
                var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("webpack/sharing/consume/default/prop-types/prop-types");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/ __webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("webpack/sharing/consume/default/react/react?3966");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);
                /* harmony import */
                var _TransitionGroupContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__("../../node_modules/react-transition-group/esm/TransitionGroupContext.js");
                /* harmony import */
                var _utils_ChildMapping__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__("../../node_modules/react-transition-group/esm/utils/ChildMapping.js");









                var values = Object.values || function(obj) {
                    return Object.keys(obj).map(function(k) {
                        return obj[k];
                    });
                };

                var defaultProps = {
                    component: 'div',
                    childFactory: function childFactory(child) {
                        return child;
                    }
                };
                /**
                 * The `<TransitionGroup>` component manages a set of transition components
                 * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition
                 * components, `<TransitionGroup>` is a state machine for managing the mounting
                 * and unmounting of components over time.
                 *
                 * Consider the example below. As items are removed or added to the TodoList the
                 * `in` prop is toggled automatically by the `<TransitionGroup>`.
                 *
                 * Note that `<TransitionGroup>`  does not define any animation behavior!
                 * Exactly _how_ a list item animates is up to the individual transition
                 * component. This means you can mix and match animations across different list
                 * items.
                 */

                var TransitionGroup = /*#__PURE__*/ function(_React$Component) {
                    (0, _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_3__["default"])(TransitionGroup, _React$Component);

                    function TransitionGroup(props, context) {
                        var _this;

                        _this = _React$Component.call(this, props, context) || this;

                        var handleExited = _this.handleExited.bind((0, _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2__["default"])(_this)); // Initial children should all be entering, dependent on appear


                        _this.state = {
                            contextValue: {
                                isMounting: true
                            },
                            handleExited: handleExited,
                            firstRender: true
                        };
                        return _this;
                    }

                    var _proto = TransitionGroup.prototype;

                    _proto.componentDidMount = function componentDidMount() {
                        this.mounted = true;
                        this.setState({
                            contextValue: {
                                isMounting: false
                            }
                        });
                    };

                    _proto.componentWillUnmount = function componentWillUnmount() {
                        this.mounted = false;
                    };

                    TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {
                        var prevChildMapping = _ref.children,
                            handleExited = _ref.handleExited,
                            firstRender = _ref.firstRender;
                        return {
                            children: firstRender ? (0, _utils_ChildMapping__WEBPACK_IMPORTED_MODULE_6__.getInitialChildMapping)(nextProps, handleExited) : (0, _utils_ChildMapping__WEBPACK_IMPORTED_MODULE_6__.getNextChildMapping)(nextProps, prevChildMapping, handleExited),
                            firstRender: false
                        };
                    } // node is `undefined` when user provided `nodeRef` prop
                    ;

                    _proto.handleExited = function handleExited(child, node) {
                        var currentChildMapping = (0, _utils_ChildMapping__WEBPACK_IMPORTED_MODULE_6__.getChildMapping)(this.props.children);
                        if (child.key in currentChildMapping) return;

                        if (child.props.onExited) {
                            child.props.onExited(node);
                        }

                        if (this.mounted) {
                            this.setState(function(state) {
                                var children = (0, _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__["default"])({}, state.children);

                                delete children[child.key];
                                return {
                                    children: children
                                };
                            });
                        }
                    };

                    _proto.render = function render() {
                        var _this$props = this.props,
                            Component = _this$props.component,
                            childFactory = _this$props.childFactory,
                            props = (0, _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(_this$props, ["component", "childFactory"]);

                        var contextValue = this.state.contextValue;
                        var children = values(this.state.children).map(childFactory);
                        delete props.appear;
                        delete props.enter;
                        delete props.exit;

                        if (Component === null) {
                            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_5___default().createElement(_TransitionGroupContext__WEBPACK_IMPORTED_MODULE_7__["default"].Provider, {
                                value: contextValue
                            }, children);
                        }

                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_5___default().createElement(_TransitionGroupContext__WEBPACK_IMPORTED_MODULE_7__["default"].Provider, {
                            value: contextValue
                        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_5___default().createElement(Component, props, children));
                    };

                    return TransitionGroup;
                }((react__WEBPACK_IMPORTED_MODULE_5___default().Component));

                TransitionGroup.propTypes = true ? {
                    /**
                     * `<TransitionGroup>` renders a `<div>` by default. You can change this
                     * behavior by providing a `component` prop.
                     * If you use React v16+ and would like to avoid a wrapping `<div>` element
                     * you can pass in `component={null}`. This is useful if the wrapping div
                     * borks your css styles.
                     */
                    component: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().any),

                    /**
                     * A set of `<Transition>` components, that are toggled `in` and out as they
                     * leave. the `<TransitionGroup>` will inject specific transition props, so
                     * remember to spread them through if you are wrapping the `<Transition>` as
                     * with our `<Fade>` example.
                     *
                     * While this component is meant for multiple `Transition` or `CSSTransition`
                     * children, sometimes you may want to have a single transition child with
                     * content that you want to be transitioned out and in when you change it
                     * (e.g. routes, images etc.) In that case you can change the `key` prop of
                     * the transition child as you change its content, this will cause
                     * `TransitionGroup` to transition the child out and back in.
                     */
                    children: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),

                    /**
                     * A convenience prop that enables or disables appear animations
                     * for all children. Note that specifying this will override any defaults set
                     * on individual children Transitions.
                     */
                    appear: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),

                    /**
                     * A convenience prop that enables or disables enter animations
                     * for all children. Note that specifying this will override any defaults set
                     * on individual children Transitions.
                     */
                    enter: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),

                    /**
                     * A convenience prop that enables or disables exit animations
                     * for all children. Note that specifying this will override any defaults set
                     * on individual children Transitions.
                     */
                    exit: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),

                    /**
                     * You may need to apply reactive updates to a child as it is exiting.
                     * This is generally done by using `cloneElement` however in the case of an exiting
                     * child the element has already been removed and not accessible to the consumer.
                     *
                     * If you do need to update a child as it leaves you can provide a `childFactory`
                     * to wrap every child, even the ones that are leaving.
                     *
                     * @type Function(child: ReactElement) -> ReactElement
                     */
                    childFactory: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().func)
                } : 0;
                TransitionGroup.defaultProps = defaultProps;
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (TransitionGroup);

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/TransitionGroupContext.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?3966");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null));

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/config.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = ({
                    disabled: false
                });

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/index.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    CSSTransition: () => ( /* reexport safe */ _CSSTransition__WEBPACK_IMPORTED_MODULE_0__["default"]),
                    /* harmony export */
                    ReplaceTransition: () => ( /* reexport safe */ _ReplaceTransition__WEBPACK_IMPORTED_MODULE_1__["default"]),
                    /* harmony export */
                    SwitchTransition: () => ( /* reexport safe */ _SwitchTransition__WEBPACK_IMPORTED_MODULE_2__["default"]),
                    /* harmony export */
                    Transition: () => ( /* reexport safe */ _Transition__WEBPACK_IMPORTED_MODULE_4__["default"]),
                    /* harmony export */
                    TransitionGroup: () => ( /* reexport safe */ _TransitionGroup__WEBPACK_IMPORTED_MODULE_3__["default"]),
                    /* harmony export */
                    config: () => ( /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_5__["default"])
                    /* harmony export */
                });
                /* harmony import */
                var _CSSTransition__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/react-transition-group/esm/CSSTransition.js");
                /* harmony import */
                var _ReplaceTransition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/react-transition-group/esm/ReplaceTransition.js");
                /* harmony import */
                var _SwitchTransition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/react-transition-group/esm/SwitchTransition.js");
                /* harmony import */
                var _TransitionGroup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/react-transition-group/esm/TransitionGroup.js");
                /* harmony import */
                var _Transition__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/react-transition-group/esm/Transition.js");
                /* harmony import */
                var _config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/react-transition-group/esm/config.js");







                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/utils/ChildMapping.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    getChildMapping: () => ( /* binding */ getChildMapping),
                    /* harmony export */
                    getInitialChildMapping: () => ( /* binding */ getInitialChildMapping),
                    /* harmony export */
                    getNextChildMapping: () => ( /* binding */ getNextChildMapping),
                    /* harmony export */
                    mergeChildMappings: () => ( /* binding */ mergeChildMappings)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?3966");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

                /**
                 * Given `this.props.children`, return an object mapping key to child.
                 *
                 * @param {*} children `this.props.children`
                 * @return {object} Mapping of key to child
                 */

                function getChildMapping(children, mapFn) {
                    var mapper = function mapper(child) {
                        return mapFn && (0, react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child) ? mapFn(child) : child;
                    };

                    var result = Object.create(null);
                    if (children) react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, function(c) {
                        return c;
                    }).forEach(function(child) {
                        // run the map function here instead so that the key is the computed one
                        result[child.key] = mapper(child);
                    });
                    return result;
                }
                /**
                 * When you're adding or removing children some may be added or removed in the
                 * same render pass. We want to show *both* since we want to simultaneously
                 * animate elements in and out. This function takes a previous set of keys
                 * and a new set of keys and merges them with its best guess of the correct
                 * ordering. In the future we may expose some of the utilities in
                 * ReactMultiChild to make this easy, but for now React itself does not
                 * directly have this concept of the union of prevChildren and nextChildren
                 * so we implement it here.
                 *
                 * @param {object} prev prev children as returned from
                 * `ReactTransitionChildMapping.getChildMapping()`.
                 * @param {object} next next children as returned from
                 * `ReactTransitionChildMapping.getChildMapping()`.
                 * @return {object} a key set that contains all keys in `prev` and all keys
                 * in `next` in a reasonable order.
                 */

                function mergeChildMappings(prev, next) {
                    prev = prev || {};
                    next = next || {};

                    function getValueForKey(key) {
                        return key in next ? next[key] : prev[key];
                    } // For each key of `next`, the list of keys to insert before that key in
                    // the combined list


                    var nextKeysPending = Object.create(null);
                    var pendingKeys = [];

                    for (var prevKey in prev) {
                        if (prevKey in next) {
                            if (pendingKeys.length) {
                                nextKeysPending[prevKey] = pendingKeys;
                                pendingKeys = [];
                            }
                        } else {
                            pendingKeys.push(prevKey);
                        }
                    }

                    var i;
                    var childMapping = {};

                    for (var nextKey in next) {
                        if (nextKeysPending[nextKey]) {
                            for (i = 0; i < nextKeysPending[nextKey].length; i++) {
                                var pendingNextKey = nextKeysPending[nextKey][i];
                                childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);
                            }
                        }

                        childMapping[nextKey] = getValueForKey(nextKey);
                    } // Finally, add the keys which didn't appear before any key in `next`


                    for (i = 0; i < pendingKeys.length; i++) {
                        childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);
                    }

                    return childMapping;
                }

                function getProp(child, prop, props) {
                    return props[prop] != null ? props[prop] : child.props[prop];
                }

                function getInitialChildMapping(props, onExited) {
                    return getChildMapping(props.children, function(child) {
                        return (0, react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {
                            onExited: onExited.bind(null, child),
                            in: true,
                            appear: getProp(child, 'appear', props),
                            enter: getProp(child, 'enter', props),
                            exit: getProp(child, 'exit', props)
                        });
                    });
                }

                function getNextChildMapping(nextProps, prevChildMapping, onExited) {
                    var nextChildMapping = getChildMapping(nextProps.children);
                    var children = mergeChildMappings(prevChildMapping, nextChildMapping);
                    Object.keys(children).forEach(function(key) {
                        var child = children[key];
                        if (!(0, react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) return;
                        var hasPrev = (key in prevChildMapping);
                        var hasNext = (key in nextChildMapping);
                        var prevChild = prevChildMapping[key];
                        var isLeaving = (0, react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(prevChild) && !prevChild.props.in; // item is new (entering)

                        if (hasNext && (!hasPrev || isLeaving)) {
                            // console.log('entering', key)
                            children[key] = (0, react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {
                                onExited: onExited.bind(null, child),
                                in: true,
                                exit: getProp(child, 'exit', nextProps),
                                enter: getProp(child, 'enter', nextProps)
                            });
                        } else if (!hasNext && hasPrev && !isLeaving) {
                            // item is old (exiting)
                            // console.log('leaving', key)
                            children[key] = (0, react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, { in: false
                            });
                        } else if (hasNext && hasPrev && (0, react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(prevChild)) {
                            // item hasn't changed transition states
                            // copy over the last transition props;
                            // console.log('unchanged', key)
                            children[key] = (0, react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {
                                onExited: onExited.bind(null, child),
                                in: prevChild.props.in,
                                exit: getProp(child, 'exit', nextProps),
                                enter: getProp(child, 'enter', nextProps)
                            });
                        }
                    });
                    return children;
                }

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/utils/PropTypes.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    classNamesShape: () => ( /* binding */ classNamesShape),
                    /* harmony export */
                    timeoutsShape: () => ( /* binding */ timeoutsShape)
                    /* harmony export */
                });
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/prop-types/prop-types");
                /* harmony import */
                var prop_types__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_0__);

                var timeoutsShape = true ? prop_types__WEBPACK_IMPORTED_MODULE_0___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_0___default().number), prop_types__WEBPACK_IMPORTED_MODULE_0___default().shape({
                    enter: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().number),
                    exit: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().number),
                    appear: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().number)
                }).isRequired]) : 0;
                var classNamesShape = true ? prop_types__WEBPACK_IMPORTED_MODULE_0___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_0___default().string), prop_types__WEBPACK_IMPORTED_MODULE_0___default().shape({
                    enter: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string),
                    exit: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string),
                    active: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string)
                }), prop_types__WEBPACK_IMPORTED_MODULE_0___default().shape({
                    enter: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string),
                    enterDone: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string),
                    enterActive: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string),
                    exit: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string),
                    exitDone: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string),
                    exitActive: (prop_types__WEBPACK_IMPORTED_MODULE_0___default().string)
                })]) : 0;

                /***/
            }),

        /***/
        "../../node_modules/react-transition-group/esm/utils/reflow.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    forceReflow: () => ( /* binding */ forceReflow)
                    /* harmony export */
                });
                var forceReflow = function forceReflow(node) {
                    return node.scrollTop;
                };

                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_react-transition-group_esm_index_js.912b64f5e77f074a.js.map