(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["src_routes_tsx"], {

        /***/
        "./src/routes.tsx":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => ( /* binding */ getRoutes)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?c222");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var react_router_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("webpack/sharing/consume/default/react-router-dom/react-router-dom?23b0");
                /* harmony import */
                var react_router_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/ __webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_1__);
                /* harmony import */
                var _utils_lazyWithRetry__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("./src/utils/lazyWithRetry.ts");
                /* harmony import */
                var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/react/jsx-dev-runtime.js");
                var _jsxFileName = "/home/<USER>/work/wuilt-client/wuilt-client/apps/stores-admin/src/routes.tsx";




                const CommerceAppContainer = (0, _utils_lazyWithRetry__WEBPACK_IMPORTED_MODULE_2__["default"])(() => Promise.all( /* import() */ [__webpack_require__.e("vendors-node_modules_chakra-ui_anatomy_dist_chunk-OA3DH5LS_mjs-node_modules_chakra-ui_styled--bf4cd7"), __webpack_require__.e("vendors-node_modules_nx_js_node_modules_babel_runtime_helpers_esm_objectDestructuringEmpty_js-2cbc99"), __webpack_require__.e("webpack_sharing_consume_default_chakra-ui_theme-tools_chakra-ui_theme-tools"), __webpack_require__.e("src_chakraTheme_theme_ts"), __webpack_require__.e("webpack_sharing_consume_default_wuilt_quilt_wuilt_quilt-webpack_sharing_consume_default_react-e738b7"), __webpack_require__.e("webpack_sharing_consume_default_wuilt_app-core_wuilt_app-core"), __webpack_require__.e("webpack_sharing_consume_default_styled-components_styled-components-_6dc1"), __webpack_require__.e("webpack_sharing_consume_default_apollo_client_apollo_client"), __webpack_require__.e("webpack_sharing_consume_default_amplitude_analytics-browser_amplitude_analytics-browser-webpa-7b7d20"), __webpack_require__.e("webpack_sharing_consume_default_chakra-ui_react_chakra-ui_react"), __webpack_require__.e("src_components_FreeStoreModal_FreeStoreModal_tsx-src_components_LoadingScreen_tsx"), __webpack_require__.e("src_CommerceAppContainer_tsx")]).then(__webpack_require__.bind(__webpack_require__, "./src/CommerceAppContainer.tsx")));
                const Home = (0, _utils_lazyWithRetry__WEBPACK_IMPORTED_MODULE_2__["default"])(() => Promise.all( /* import() */ [__webpack_require__.e("webpack_sharing_consume_default_wuilt_quilt_wuilt_quilt-webpack_sharing_consume_default_react-e738b7"), __webpack_require__.e("webpack_sharing_consume_default_wuilt_app-core_wuilt_app-core"), __webpack_require__.e("webpack_sharing_consume_default_styled-components_styled-components-_6dc1"), __webpack_require__.e("webpack_sharing_consume_default_apollo_client_apollo_client"), __webpack_require__.e("src_generated_graphql_tsx"), __webpack_require__.e("src_data_plansData_tsx-src_utils_getStoreLink_ts-src_utils_isStoreFree_ts"), __webpack_require__.e("src_screens_Home_tsx")]).then(__webpack_require__.bind(__webpack_require__, "./src/screens/Home.tsx")));
                const SingleStore = (0, _utils_lazyWithRetry__WEBPACK_IMPORTED_MODULE_2__["default"])(() => Promise.all( /* import() */ [__webpack_require__.e("vendors-node_modules_swiper_shared_utils_mjs"), __webpack_require__.e("vendors-node_modules_swiper_modules_pagination_css-node_modules_swiper_swiper_css-node_module-0db66b"), __webpack_require__.e("webpack_sharing_consume_default_wuilt_quilt_wuilt_quilt-webpack_sharing_consume_default_react-e738b7"), __webpack_require__.e("webpack_sharing_consume_default_wuilt_app-core_wuilt_app-core"), __webpack_require__.e("webpack_sharing_consume_default_styled-components_styled-components-_6dc1"), __webpack_require__.e("webpack_sharing_consume_default_apollo_client_apollo_client"), __webpack_require__.e("src_generated_graphql_tsx"), __webpack_require__.e("webpack_sharing_consume_default_chakra-ui_react_chakra-ui_react"), __webpack_require__.e("src_components_FreeStoreModal_FreeStoreModal_tsx-src_components_LoadingScreen_tsx"), __webpack_require__.e("src_data_plansData_tsx-src_utils_getStoreLink_ts-src_utils_isStoreFree_ts"), __webpack_require__.e("src_screens_Store_SingleStore_tsx")]).then(__webpack_require__.bind(__webpack_require__, "./src/screens/Store/SingleStore.tsx")));
                const CreateStore = (0, _utils_lazyWithRetry__WEBPACK_IMPORTED_MODULE_2__["default"])(() => Promise.all( /* import() */ [__webpack_require__.e("webpack_sharing_consume_default_chakra-react-select_chakra-react-select"), __webpack_require__.e("webpack_sharing_consume_default_react-hook-form_react-hook-form"), __webpack_require__.e("webpack_sharing_consume_default_wuilt_quilt_wuilt_quilt-webpack_sharing_consume_default_react-e738b7"), __webpack_require__.e("webpack_sharing_consume_default_wuilt_app-core_wuilt_app-core"), __webpack_require__.e("webpack_sharing_consume_default_apollo_client_apollo_client"), __webpack_require__.e("src_generated_graphql_tsx"), __webpack_require__.e("webpack_sharing_consume_default_chakra-ui_react_chakra-ui_react"), __webpack_require__.e("src_data_Industries_ts-src_data_currency_ar_json-src_data_currency_en_json"), __webpack_require__.e("src_screens_Store_create-store_CreateStore_tsx")]).then(__webpack_require__.bind(__webpack_require__, "./src/screens/Store/create-store/CreateStore.tsx")));
                const MaintenanceModeRoute = (0, _utils_lazyWithRetry__WEBPACK_IMPORTED_MODULE_2__["default"])(() => Promise.all( /* import() */ [__webpack_require__.e("webpack_sharing_consume_default_wuilt_quilt_wuilt_quilt-webpack_sharing_consume_default_react-e738b7"), __webpack_require__.e("src_screens_Store_Maintenance-mode_index_tsx")]).then(__webpack_require__.bind(__webpack_require__, "./src/screens/Store/Maintenance-mode/index.tsx")));

                function getRoutes() {
                    return [{
                        path: "/",
                        element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Navigate, {
                            to: "/stores"
                        }, void 0, false, {
                            fileName: _jsxFileName,
                            lineNumber: 21,
                            columnNumber: 16
                        }, this),
                        type: "bare"
                    }, {
                        path: "store/*",
                        element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(CommerceAppContainer, {
                            children: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Routes, {
                                children: [ /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {
                                    path: "/:storeId/:locale/*",
                                    element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(SingleStore, {}, void 0, false, {
                                        fileName: _jsxFileName,
                                        lineNumber: 29,
                                        columnNumber: 56
                                    }, this)
                                }, void 0, false, {
                                    fileName: _jsxFileName,
                                    lineNumber: 29,
                                    columnNumber: 13
                                }, this), /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {
                                    path: "*",
                                    element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Navigate, {
                                        to: "/",
                                        replace: true
                                    }, void 0, false, {
                                        fileName: _jsxFileName,
                                        lineNumber: 30,
                                        columnNumber: 38
                                    }, this)
                                }, void 0, false, {
                                    fileName: _jsxFileName,
                                    lineNumber: 30,
                                    columnNumber: 13
                                }, this)]
                            }, void 0, true, {
                                fileName: _jsxFileName,
                                lineNumber: 28,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: _jsxFileName,
                            lineNumber: 27,
                            columnNumber: 9
                        }, this),
                        type: "shell"
                    }, {
                        path: "stores/*",
                        element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(CommerceAppContainer, {
                            children: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Routes, {
                                children: [ /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {
                                    path: "/",
                                    element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(Home, {}, void 0, false, {
                                        fileName: _jsxFileName,
                                        lineNumber: 41,
                                        columnNumber: 38
                                    }, this)
                                }, void 0, false, {
                                    fileName: _jsxFileName,
                                    lineNumber: 41,
                                    columnNumber: 13
                                }, this), /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {
                                    path: "*",
                                    element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Navigate, {
                                        to: "/",
                                        replace: true
                                    }, void 0, false, {
                                        fileName: _jsxFileName,
                                        lineNumber: 42,
                                        columnNumber: 38
                                    }, this)
                                }, void 0, false, {
                                    fileName: _jsxFileName,
                                    lineNumber: 42,
                                    columnNumber: 13
                                }, this)]
                            }, void 0, true, {
                                fileName: _jsxFileName,
                                lineNumber: 40,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: _jsxFileName,
                            lineNumber: 39,
                            columnNumber: 9
                        }, this),
                        type: "shell"
                    }, {
                        path: "stores/create",
                        element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(CommerceAppContainer, {
                            children: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(CreateStore, {}, void 0, false, {
                                fileName: _jsxFileName,
                                lineNumber: 52,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: _jsxFileName,
                            lineNumber: 51,
                            columnNumber: 9
                        }, this),
                        type: "top-level-with-context"
                    }, {
                        path: "maintenance",
                        element: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(CommerceAppContainer, {
                            children: /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(MaintenanceModeRoute, {}, void 0, false, {
                                fileName: _jsxFileName,
                                lineNumber: 61,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: _jsxFileName,
                            lineNumber: 60,
                            columnNumber: 9
                        }, this),
                        type: "bare"
                    }];
                }

                /***/
            }),

        /***/
        "./src/utils/lazyWithRetry.ts":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?c222");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

                const lazyWithRetry = componentImport => /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__.lazy)(async () => {
                    const pageHasAlreadyBeenForceRefreshed = JSON.parse(window.localStorage.getItem('page-has-been-force-refreshed') || 'false');
                    try {
                        const component = await componentImport();
                        window.localStorage.setItem('page-has-been-force-refreshed', 'false');
                        return component;
                    } catch (error) {
                        if (!pageHasAlreadyBeenForceRefreshed) {
                            // Assuming that the user is not on the latest version of the application.
                            // Let's refresh the page immediately.
                            window.localStorage.setItem('page-has-been-force-refreshed', 'true');
                            return window.location.reload();
                        }

                        // The page has already been reloaded
                        // Assuming that user is already using the latest version of the application.
                        // Let's let the application crash and raise the error.
                        throw error;
                    }
                });
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (lazyWithRetry);

                /***/
            })

    }
])
//# sourceMappingURL=src_routes_tsx.f01dc72a1251bb54.js.map