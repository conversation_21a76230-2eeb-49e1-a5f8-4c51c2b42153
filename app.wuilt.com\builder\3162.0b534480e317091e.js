(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [3162], {
        80780: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.getBestPattern = void 0;
            var n = r(14393);

            function a(e) {
                var t = e.hourCycle;
                if (void 0 === t && e.hourCycles && e.hourCycles.length && (t = e.hourCycles[0]), t) switch (t) {
                    case "h24":
                        return "k";
                    case "h23":
                        return "H";
                    case "h12":
                        return "h";
                    case "h11":
                        return "K";
                    default:
                        throw new Error("Invalid hourCycle")
                }
                var r, a = e.language;
                return "root" !== a && (r = e.maximize().region), (n.timeData[r || ""] || n.timeData[a || ""] || n.timeData["".concat(a, "-001")] || n.timeData["001"])[0]
            }
            t.getBestPattern = function(e, t) {
                for (var r = "", n = 0; n < e.length; n++) {
                    var i = e.charAt(n);
                    if ("j" === i) {
                        for (var o = 0; n + 1 < e.length && e.charAt(n + 1) === i;) o++, n++;
                        var u = 1 + (1 & o),
                            s = o < 2 ? 1 : 3 + (o >> 1),
                            l = a(t);
                        for ("H" != l && "k" != l || (s = 0); s-- > 0;) r += "a";
                        for (; u-- > 0;) r = l + r
                    } else r += "J" === i ? "H" : i
                }
                return r
            }
        },
        57977: (e, t) => {
            var r;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ErrorKind = void 0, (r = t.ErrorKind || (t.ErrorKind = {}))[r.EXPECT_ARGUMENT_CLOSING_BRACE = 1] = "EXPECT_ARGUMENT_CLOSING_BRACE", r[r.EMPTY_ARGUMENT = 2] = "EMPTY_ARGUMENT", r[r.MALFORMED_ARGUMENT = 3] = "MALFORMED_ARGUMENT", r[r.EXPECT_ARGUMENT_TYPE = 4] = "EXPECT_ARGUMENT_TYPE", r[r.INVALID_ARGUMENT_TYPE = 5] = "INVALID_ARGUMENT_TYPE", r[r.EXPECT_ARGUMENT_STYLE = 6] = "EXPECT_ARGUMENT_STYLE", r[r.INVALID_NUMBER_SKELETON = 7] = "INVALID_NUMBER_SKELETON", r[r.INVALID_DATE_TIME_SKELETON = 8] = "INVALID_DATE_TIME_SKELETON", r[r.EXPECT_NUMBER_SKELETON = 9] = "EXPECT_NUMBER_SKELETON", r[r.EXPECT_DATE_TIME_SKELETON = 10] = "EXPECT_DATE_TIME_SKELETON", r[r.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE = 11] = "UNCLOSED_QUOTE_IN_ARGUMENT_STYLE", r[r.EXPECT_SELECT_ARGUMENT_OPTIONS = 12] = "EXPECT_SELECT_ARGUMENT_OPTIONS", r[r.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE = 13] = "EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE", r[r.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE = 14] = "INVALID_PLURAL_ARGUMENT_OFFSET_VALUE", r[r.EXPECT_SELECT_ARGUMENT_SELECTOR = 15] = "EXPECT_SELECT_ARGUMENT_SELECTOR", r[r.EXPECT_PLURAL_ARGUMENT_SELECTOR = 16] = "EXPECT_PLURAL_ARGUMENT_SELECTOR", r[r.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT = 17] = "EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT", r[r.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT = 18] = "EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT", r[r.INVALID_PLURAL_ARGUMENT_SELECTOR = 19] = "INVALID_PLURAL_ARGUMENT_SELECTOR", r[r.DUPLICATE_PLURAL_ARGUMENT_SELECTOR = 20] = "DUPLICATE_PLURAL_ARGUMENT_SELECTOR", r[r.DUPLICATE_SELECT_ARGUMENT_SELECTOR = 21] = "DUPLICATE_SELECT_ARGUMENT_SELECTOR", r[r.MISSING_OTHER_CLAUSE = 22] = "MISSING_OTHER_CLAUSE", r[r.INVALID_TAG = 23] = "INVALID_TAG", r[r.INVALID_TAG_NAME = 25] = "INVALID_TAG_NAME", r[r.UNMATCHED_CLOSING_TAG = 26] = "UNMATCHED_CLOSING_TAG", r[r.UNCLOSED_TAG = 27] = "UNCLOSED_TAG"
        },
        46057: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t._Parser = t.parse = void 0;
            var n = r(22970),
                a = r(57977),
                i = r(80754),
                o = r(67281);

            function u(e) {
                e.forEach((function(e) {
                    if (delete e.location, (0, o.isSelectElement)(e) || (0, o.isPluralElement)(e))
                        for (var t in e.options) delete e.options[t].location, u(e.options[t].value);
                    else(0, o.isNumberElement)(e) && (0, o.isNumberSkeleton)(e.style) || ((0, o.isDateElement)(e) || (0, o.isTimeElement)(e)) && (0, o.isDateTimeSkeleton)(e.style) ? delete e.style.location : (0, o.isTagElement)(e) && u(e.children)
                }))
            }
            t.parse = function(e, t) {
                void 0 === t && (t = {}), t = n.__assign({
                    shouldParseSkeletons: !0,
                    requiresOtherClause: !0
                }, t);
                var r = new i.Parser(e, t).parse();
                if (r.err) {
                    var o = SyntaxError(a.ErrorKind[r.err.kind]);
                    throw o.location = r.err.location, o.originalMessage = r.err.message, o
                }
                return (null == t ? void 0 : t.captureLocation) || u(r.val), r.val
            }, n.__exportStar(r(67281), t), t._Parser = i.Parser
        },
        80754: (e, t, r) => {
            var n;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.Parser = void 0;
            var a = r(22970),
                i = r(57977),
                o = r(67281),
                u = r(27830),
                s = r(78702),
                l = r(80780),
                c = new RegExp("^".concat(u.SPACE_SEPARATOR_REGEX.source, "*")),
                m = new RegExp("".concat(u.SPACE_SEPARATOR_REGEX.source, "*$"));

            function f(e, t) {
                return {
                    start: e,
                    end: t
                }
            }
            var p = !!String.prototype.startsWith,
                d = !!String.fromCodePoint,
                h = !!Object.fromEntries,
                g = !!String.prototype.codePointAt,
                v = !!String.prototype.trimStart,
                y = !!String.prototype.trimEnd,
                E = Number.isSafeInteger ? Number.isSafeInteger : function(e) {
                    return "number" == typeof e && isFinite(e) && Math.floor(e) === e && Math.abs(e) <= 9007199254740991
                },
                b = !0;
            try {
                b = "a" === (null === (n = O("([^\\p{White_Space}\\p{Pattern_Syntax}]*)", "yu").exec("a")) || void 0 === n ? void 0 : n[0])
            } catch (e) {
                b = !1
            }
            var _, D = p ? function(e, t, r) {
                    return e.startsWith(t, r)
                } : function(e, t, r) {
                    return e.slice(r, r + t.length) === t
                },
                F = d ? String.fromCodePoint : function() {
                    for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                    for (var r, n = "", a = e.length, i = 0; a > i;) {
                        if ((r = e[i++]) > 1114111) throw RangeError(r + " is not a valid code point");
                        n += r < 65536 ? String.fromCharCode(r) : String.fromCharCode(55296 + ((r -= 65536) >> 10), r % 1024 + 56320)
                    }
                    return n
                },
                S = h ? Object.fromEntries : function(e) {
                    for (var t = {}, r = 0, n = e; r < n.length; r++) {
                        var a = n[r],
                            i = a[0],
                            o = a[1];
                        t[i] = o
                    }
                    return t
                },
                T = g ? function(e, t) {
                    return e.codePointAt(t)
                } : function(e, t) {
                    var r = e.length;
                    if (!(t < 0 || t >= r)) {
                        var n, a = e.charCodeAt(t);
                        return a < 55296 || a > 56319 || t + 1 === r || (n = e.charCodeAt(t + 1)) < 56320 || n > 57343 ? a : n - 56320 + (a - 55296 << 10) + 65536
                    }
                },
                P = v ? function(e) {
                    return e.trimStart()
                } : function(e) {
                    return e.replace(c, "")
                },
                I = y ? function(e) {
                    return e.trimEnd()
                } : function(e) {
                    return e.replace(m, "")
                };

            function O(e, t) {
                return new RegExp(e, t)
            }
            if (b) {
                var N = O("([^\\p{White_Space}\\p{Pattern_Syntax}]*)", "yu");
                _ = function(e, t) {
                    var r;
                    return N.lastIndex = t, null !== (r = N.exec(e)[1]) && void 0 !== r ? r : ""
                }
            } else _ = function(e, t) {
                for (var r = [];;) {
                    var n = T(e, t);
                    if (void 0 === n || A(n) || L(n)) break;
                    r.push(n), t += n >= 65536 ? 2 : 1
                }
                return F.apply(void 0, r)
            };
            var C = function() {
                function e(e, t) {
                    void 0 === t && (t = {}), this.message = e, this.position = {
                        offset: 0,
                        line: 1,
                        column: 1
                    }, this.ignoreTag = !!t.ignoreTag, this.locale = t.locale, this.requiresOtherClause = !!t.requiresOtherClause, this.shouldParseSkeletons = !!t.shouldParseSkeletons
                }
                return e.prototype.parse = function() {
                    if (0 !== this.offset()) throw Error("parser can only be used once");
                    return this.parseMessage(0, "", !1)
                }, e.prototype.parseMessage = function(e, t, r) {
                    for (var n = []; !this.isEOF();) {
                        var a = this.char();
                        if (123 === a) {
                            if ((u = this.parseArgument(e, r)).err) return u;
                            n.push(u.val)
                        } else {
                            if (125 === a && e > 0) break;
                            if (35 !== a || "plural" !== t && "selectordinal" !== t) {
                                if (60 === a && !this.ignoreTag && 47 === this.peek()) {
                                    if (r) break;
                                    return this.error(i.ErrorKind.UNMATCHED_CLOSING_TAG, f(this.clonePosition(), this.clonePosition()))
                                }
                                if (60 === a && !this.ignoreTag && M(this.peek() || 0)) {
                                    if ((u = this.parseTag(e, t)).err) return u;
                                    n.push(u.val)
                                } else {
                                    var u;
                                    if ((u = this.parseLiteral(e, t)).err) return u;
                                    n.push(u.val)
                                }
                            } else {
                                var s = this.clonePosition();
                                this.bump(), n.push({
                                    type: o.TYPE.pound,
                                    location: f(s, this.clonePosition())
                                })
                            }
                        }
                    }
                    return {
                        val: n,
                        err: null
                    }
                }, e.prototype.parseTag = function(e, t) {
                    var r = this.clonePosition();
                    this.bump();
                    var n = this.parseTagName();
                    if (this.bumpSpace(), this.bumpIf("/>")) return {
                        val: {
                            type: o.TYPE.literal,
                            value: "<".concat(n, "/>"),
                            location: f(r, this.clonePosition())
                        },
                        err: null
                    };
                    if (this.bumpIf(">")) {
                        var a = this.parseMessage(e + 1, t, !0);
                        if (a.err) return a;
                        var u = a.val,
                            s = this.clonePosition();
                        if (this.bumpIf("</")) {
                            if (this.isEOF() || !M(this.char())) return this.error(i.ErrorKind.INVALID_TAG, f(s, this.clonePosition()));
                            var l = this.clonePosition();
                            return n !== this.parseTagName() ? this.error(i.ErrorKind.UNMATCHED_CLOSING_TAG, f(l, this.clonePosition())) : (this.bumpSpace(), this.bumpIf(">") ? {
                                val: {
                                    type: o.TYPE.tag,
                                    value: n,
                                    children: u,
                                    location: f(r, this.clonePosition())
                                },
                                err: null
                            } : this.error(i.ErrorKind.INVALID_TAG, f(s, this.clonePosition())))
                        }
                        return this.error(i.ErrorKind.UNCLOSED_TAG, f(r, this.clonePosition()))
                    }
                    return this.error(i.ErrorKind.INVALID_TAG, f(r, this.clonePosition()))
                }, e.prototype.parseTagName = function() {
                    var e, t = this.offset();
                    for (this.bump(); !this.isEOF() && (45 === (e = this.char()) || 46 === e || e >= 48 && e <= 57 || 95 === e || e >= 97 && e <= 122 || e >= 65 && e <= 90 || 183 == e || e >= 192 && e <= 214 || e >= 216 && e <= 246 || e >= 248 && e <= 893 || e >= 895 && e <= 8191 || e >= 8204 && e <= 8205 || e >= 8255 && e <= 8256 || e >= 8304 && e <= 8591 || e >= 11264 && e <= 12271 || e >= 12289 && e <= 55295 || e >= 63744 && e <= 64975 || e >= 65008 && e <= 65533 || e >= 65536 && e <= 983039);) this.bump();
                    return this.message.slice(t, this.offset())
                }, e.prototype.parseLiteral = function(e, t) {
                    for (var r = this.clonePosition(), n = "";;) {
                        var a = this.tryParseQuote(t);
                        if (a) n += a;
                        else {
                            var i = this.tryParseUnquoted(e, t);
                            if (i) n += i;
                            else {
                                var u = this.tryParseLeftAngleBracket();
                                if (!u) break;
                                n += u
                            }
                        }
                    }
                    var s = f(r, this.clonePosition());
                    return {
                        val: {
                            type: o.TYPE.literal,
                            value: n,
                            location: s
                        },
                        err: null
                    }
                }, e.prototype.tryParseLeftAngleBracket = function() {
                    return this.isEOF() || 60 !== this.char() || !this.ignoreTag && (M(e = this.peek() || 0) || 47 === e) ? null : (this.bump(), "<");
                    var e
                }, e.prototype.tryParseQuote = function(e) {
                    if (this.isEOF() || 39 !== this.char()) return null;
                    switch (this.peek()) {
                        case 39:
                            return this.bump(), this.bump(), "'";
                        case 123:
                        case 60:
                        case 62:
                        case 125:
                            break;
                        case 35:
                            if ("plural" === e || "selectordinal" === e) break;
                            return null;
                        default:
                            return null
                    }
                    this.bump();
                    var t = [this.char()];
                    for (this.bump(); !this.isEOF();) {
                        var r = this.char();
                        if (39 === r) {
                            if (39 !== this.peek()) {
                                this.bump();
                                break
                            }
                            t.push(39), this.bump()
                        } else t.push(r);
                        this.bump()
                    }
                    return F.apply(void 0, t)
                }, e.prototype.tryParseUnquoted = function(e, t) {
                    if (this.isEOF()) return null;
                    var r = this.char();
                    return 60 === r || 123 === r || 35 === r && ("plural" === t || "selectordinal" === t) || 125 === r && e > 0 ? null : (this.bump(), F(r))
                }, e.prototype.parseArgument = function(e, t) {
                    var r = this.clonePosition();
                    if (this.bump(), this.bumpSpace(), this.isEOF()) return this.error(i.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, f(r, this.clonePosition()));
                    if (125 === this.char()) return this.bump(), this.error(i.ErrorKind.EMPTY_ARGUMENT, f(r, this.clonePosition()));
                    var n = this.parseIdentifierIfPossible().value;
                    if (!n) return this.error(i.ErrorKind.MALFORMED_ARGUMENT, f(r, this.clonePosition()));
                    if (this.bumpSpace(), this.isEOF()) return this.error(i.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, f(r, this.clonePosition()));
                    switch (this.char()) {
                        case 125:
                            return this.bump(), {
                                val: {
                                    type: o.TYPE.argument,
                                    value: n,
                                    location: f(r, this.clonePosition())
                                },
                                err: null
                            };
                        case 44:
                            return this.bump(), this.bumpSpace(), this.isEOF() ? this.error(i.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, f(r, this.clonePosition())) : this.parseArgumentOptions(e, t, n, r);
                        default:
                            return this.error(i.ErrorKind.MALFORMED_ARGUMENT, f(r, this.clonePosition()))
                    }
                }, e.prototype.parseIdentifierIfPossible = function() {
                    var e = this.clonePosition(),
                        t = this.offset(),
                        r = _(this.message, t),
                        n = t + r.length;
                    return this.bumpTo(n), {
                        value: r,
                        location: f(e, this.clonePosition())
                    }
                }, e.prototype.parseArgumentOptions = function(e, t, r, n) {
                    var u, c = this.clonePosition(),
                        m = this.parseIdentifierIfPossible().value,
                        p = this.clonePosition();
                    switch (m) {
                        case "":
                            return this.error(i.ErrorKind.EXPECT_ARGUMENT_TYPE, f(c, p));
                        case "number":
                        case "date":
                        case "time":
                            this.bumpSpace();
                            var d = null;
                            if (this.bumpIf(",")) {
                                this.bumpSpace();
                                var h = this.clonePosition();
                                if ((T = this.parseSimpleArgStyleIfPossible()).err) return T;
                                if (0 === (E = I(T.val)).length) return this.error(i.ErrorKind.EXPECT_ARGUMENT_STYLE, f(this.clonePosition(), this.clonePosition()));
                                d = {
                                    style: E,
                                    styleLocation: f(h, this.clonePosition())
                                }
                            }
                            if ((O = this.tryParseArgumentClose(n)).err) return O;
                            var g = f(n, this.clonePosition());
                            if (d && D(null == d ? void 0 : d.style, "::", 0)) {
                                var v = P(d.style.slice(2));
                                if ("number" === m) return (T = this.parseNumberSkeletonFromString(v, d.styleLocation)).err ? T : {
                                    val: {
                                        type: o.TYPE.number,
                                        value: r,
                                        location: g,
                                        style: T.val
                                    },
                                    err: null
                                };
                                if (0 === v.length) return this.error(i.ErrorKind.EXPECT_DATE_TIME_SKELETON, g);
                                var y = v;
                                this.locale && (y = (0, l.getBestPattern)(v, this.locale));
                                var E = {
                                    type: o.SKELETON_TYPE.dateTime,
                                    pattern: y,
                                    location: d.styleLocation,
                                    parsedOptions: this.shouldParseSkeletons ? (0, s.parseDateTimeSkeleton)(y) : {}
                                };
                                return {
                                    val: {
                                        type: "date" === m ? o.TYPE.date : o.TYPE.time,
                                        value: r,
                                        location: g,
                                        style: E
                                    },
                                    err: null
                                }
                            }
                            return {
                                val: {
                                    type: "number" === m ? o.TYPE.number : "date" === m ? o.TYPE.date : o.TYPE.time,
                                    value: r,
                                    location: g,
                                    style: null !== (u = null == d ? void 0 : d.style) && void 0 !== u ? u : null
                                },
                                err: null
                            };
                        case "plural":
                        case "selectordinal":
                        case "select":
                            var b = this.clonePosition();
                            if (this.bumpSpace(), !this.bumpIf(",")) return this.error(i.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, f(b, a.__assign({}, b)));
                            this.bumpSpace();
                            var _ = this.parseIdentifierIfPossible(),
                                F = 0;
                            if ("select" !== m && "offset" === _.value) {
                                if (!this.bumpIf(":")) return this.error(i.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, f(this.clonePosition(), this.clonePosition()));
                                var T;
                                if (this.bumpSpace(), (T = this.tryParseDecimalInteger(i.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, i.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE)).err) return T;
                                this.bumpSpace(), _ = this.parseIdentifierIfPossible(), F = T.val
                            }
                            var O, N = this.tryParsePluralOrSelectOptions(e, m, t, _);
                            if (N.err) return N;
                            if ((O = this.tryParseArgumentClose(n)).err) return O;
                            var C = f(n, this.clonePosition());
                            return "select" === m ? {
                                val: {
                                    type: o.TYPE.select,
                                    value: r,
                                    options: S(N.val),
                                    location: C
                                },
                                err: null
                            } : {
                                val: {
                                    type: o.TYPE.plural,
                                    value: r,
                                    options: S(N.val),
                                    offset: F,
                                    pluralType: "plural" === m ? "cardinal" : "ordinal",
                                    location: C
                                },
                                err: null
                            };
                        default:
                            return this.error(i.ErrorKind.INVALID_ARGUMENT_TYPE, f(c, p))
                    }
                }, e.prototype.tryParseArgumentClose = function(e) {
                    return this.isEOF() || 125 !== this.char() ? this.error(i.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, f(e, this.clonePosition())) : (this.bump(), {
                        val: !0,
                        err: null
                    })
                }, e.prototype.parseSimpleArgStyleIfPossible = function() {
                    for (var e = 0, t = this.clonePosition(); !this.isEOF();) switch (this.char()) {
                        case 39:
                            this.bump();
                            var r = this.clonePosition();
                            if (!this.bumpUntil("'")) return this.error(i.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, f(r, this.clonePosition()));
                            this.bump();
                            break;
                        case 123:
                            e += 1, this.bump();
                            break;
                        case 125:
                            if (!(e > 0)) return {
                                val: this.message.slice(t.offset, this.offset()),
                                err: null
                            };
                            e -= 1;
                            break;
                        default:
                            this.bump()
                    }
                    return {
                        val: this.message.slice(t.offset, this.offset()),
                        err: null
                    }
                }, e.prototype.parseNumberSkeletonFromString = function(e, t) {
                    var r = [];
                    try {
                        r = (0, s.parseNumberSkeletonFromString)(e)
                    } catch (e) {
                        return this.error(i.ErrorKind.INVALID_NUMBER_SKELETON, t)
                    }
                    return {
                        val: {
                            type: o.SKELETON_TYPE.number,
                            tokens: r,
                            location: t,
                            parsedOptions: this.shouldParseSkeletons ? (0, s.parseNumberSkeleton)(r) : {}
                        },
                        err: null
                    }
                }, e.prototype.tryParsePluralOrSelectOptions = function(e, t, r, n) {
                    for (var a, o = !1, u = [], s = new Set, l = n.value, c = n.location;;) {
                        if (0 === l.length) {
                            var m = this.clonePosition();
                            if ("select" === t || !this.bumpIf("=")) break;
                            var p = this.tryParseDecimalInteger(i.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, i.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);
                            if (p.err) return p;
                            c = f(m, this.clonePosition()), l = this.message.slice(m.offset, this.offset())
                        }
                        if (s.has(l)) return this.error("select" === t ? i.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR : i.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, c);
                        "other" === l && (o = !0), this.bumpSpace();
                        var d = this.clonePosition();
                        if (!this.bumpIf("{")) return this.error("select" === t ? i.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT : i.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, f(this.clonePosition(), this.clonePosition()));
                        var h = this.parseMessage(e + 1, t, r);
                        if (h.err) return h;
                        var g = this.tryParseArgumentClose(d);
                        if (g.err) return g;
                        u.push([l, {
                            value: h.val,
                            location: f(d, this.clonePosition())
                        }]), s.add(l), this.bumpSpace(), l = (a = this.parseIdentifierIfPossible()).value, c = a.location
                    }
                    return 0 === u.length ? this.error("select" === t ? i.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR : i.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, f(this.clonePosition(), this.clonePosition())) : this.requiresOtherClause && !o ? this.error(i.ErrorKind.MISSING_OTHER_CLAUSE, f(this.clonePosition(), this.clonePosition())) : {
                        val: u,
                        err: null
                    }
                }, e.prototype.tryParseDecimalInteger = function(e, t) {
                    var r = 1,
                        n = this.clonePosition();
                    this.bumpIf("+") || this.bumpIf("-") && (r = -1);
                    for (var a = !1, i = 0; !this.isEOF();) {
                        var o = this.char();
                        if (!(o >= 48 && o <= 57)) break;
                        a = !0, i = 10 * i + (o - 48), this.bump()
                    }
                    var u = f(n, this.clonePosition());
                    return a ? E(i *= r) ? {
                        val: i,
                        err: null
                    } : this.error(t, u) : this.error(e, u)
                }, e.prototype.offset = function() {
                    return this.position.offset
                }, e.prototype.isEOF = function() {
                    return this.offset() === this.message.length
                }, e.prototype.clonePosition = function() {
                    return {
                        offset: this.position.offset,
                        line: this.position.line,
                        column: this.position.column
                    }
                }, e.prototype.char = function() {
                    var e = this.position.offset;
                    if (e >= this.message.length) throw Error("out of bound");
                    var t = T(this.message, e);
                    if (void 0 === t) throw Error("Offset ".concat(e, " is at invalid UTF-16 code unit boundary"));
                    return t
                }, e.prototype.error = function(e, t) {
                    return {
                        val: null,
                        err: {
                            kind: e,
                            message: this.message,
                            location: t
                        }
                    }
                }, e.prototype.bump = function() {
                    if (!this.isEOF()) {
                        var e = this.char();
                        10 === e ? (this.position.line += 1, this.position.column = 1, this.position.offset += 1) : (this.position.column += 1, this.position.offset += e < 65536 ? 1 : 2)
                    }
                }, e.prototype.bumpIf = function(e) {
                    if (D(this.message, e, this.offset())) {
                        for (var t = 0; t < e.length; t++) this.bump();
                        return !0
                    }
                    return !1
                }, e.prototype.bumpUntil = function(e) {
                    var t = this.offset(),
                        r = this.message.indexOf(e, t);
                    return r >= 0 ? (this.bumpTo(r), !0) : (this.bumpTo(this.message.length), !1)
                }, e.prototype.bumpTo = function(e) {
                    if (this.offset() > e) throw Error("targetOffset ".concat(e, " must be greater than or equal to the current offset ").concat(this.offset()));
                    for (e = Math.min(e, this.message.length);;) {
                        var t = this.offset();
                        if (t === e) break;
                        if (t > e) throw Error("targetOffset ".concat(e, " is at invalid UTF-16 code unit boundary"));
                        if (this.bump(), this.isEOF()) break
                    }
                }, e.prototype.bumpSpace = function() {
                    for (; !this.isEOF() && A(this.char());) this.bump()
                }, e.prototype.peek = function() {
                    if (this.isEOF()) return null;
                    var e = this.char(),
                        t = this.offset(),
                        r = this.message.charCodeAt(t + (e >= 65536 ? 2 : 1));
                    return null != r ? r : null
                }, e
            }();

            function M(e) {
                return e >= 97 && e <= 122 || e >= 65 && e <= 90
            }

            function A(e) {
                return e >= 9 && e <= 13 || 32 === e || 133 === e || e >= 8206 && e <= 8207 || 8232 === e || 8233 === e
            }

            function L(e) {
                return e >= 33 && e <= 35 || 36 === e || e >= 37 && e <= 39 || 40 === e || 41 === e || 42 === e || 43 === e || 44 === e || 45 === e || e >= 46 && e <= 47 || e >= 58 && e <= 59 || e >= 60 && e <= 62 || e >= 63 && e <= 64 || 91 === e || 92 === e || 93 === e || 94 === e || 96 === e || 123 === e || 124 === e || 125 === e || 126 === e || 161 === e || e >= 162 && e <= 165 || 166 === e || 167 === e || 169 === e || 171 === e || 172 === e || 174 === e || 176 === e || 177 === e || 182 === e || 187 === e || 191 === e || 215 === e || 247 === e || e >= 8208 && e <= 8213 || e >= 8214 && e <= 8215 || 8216 === e || 8217 === e || 8218 === e || e >= 8219 && e <= 8220 || 8221 === e || 8222 === e || 8223 === e || e >= 8224 && e <= 8231 || e >= 8240 && e <= 8248 || 8249 === e || 8250 === e || e >= 8251 && e <= 8254 || e >= 8257 && e <= 8259 || 8260 === e || 8261 === e || 8262 === e || e >= 8263 && e <= 8273 || 8274 === e || 8275 === e || e >= 8277 && e <= 8286 || e >= 8592 && e <= 8596 || e >= 8597 && e <= 8601 || e >= 8602 && e <= 8603 || e >= 8604 && e <= 8607 || 8608 === e || e >= 8609 && e <= 8610 || 8611 === e || e >= 8612 && e <= 8613 || 8614 === e || e >= 8615 && e <= 8621 || 8622 === e || e >= 8623 && e <= 8653 || e >= 8654 && e <= 8655 || e >= 8656 && e <= 8657 || 8658 === e || 8659 === e || 8660 === e || e >= 8661 && e <= 8691 || e >= 8692 && e <= 8959 || e >= 8960 && e <= 8967 || 8968 === e || 8969 === e || 8970 === e || 8971 === e || e >= 8972 && e <= 8991 || e >= 8992 && e <= 8993 || e >= 8994 && e <= 9e3 || 9001 === e || 9002 === e || e >= 9003 && e <= 9083 || 9084 === e || e >= 9085 && e <= 9114 || e >= 9115 && e <= 9139 || e >= 9140 && e <= 9179 || e >= 9180 && e <= 9185 || e >= 9186 && e <= 9254 || e >= 9255 && e <= 9279 || e >= 9280 && e <= 9290 || e >= 9291 && e <= 9311 || e >= 9472 && e <= 9654 || 9655 === e || e >= 9656 && e <= 9664 || 9665 === e || e >= 9666 && e <= 9719 || e >= 9720 && e <= 9727 || e >= 9728 && e <= 9838 || 9839 === e || e >= 9840 && e <= 10087 || 10088 === e || 10089 === e || 10090 === e || 10091 === e || 10092 === e || 10093 === e || 10094 === e || 10095 === e || 10096 === e || 10097 === e || 10098 === e || 10099 === e || 10100 === e || 10101 === e || e >= 10132 && e <= 10175 || e >= 10176 && e <= 10180 || 10181 === e || 10182 === e || e >= 10183 && e <= 10213 || 10214 === e || 10215 === e || 10216 === e || 10217 === e || 10218 === e || 10219 === e || 10220 === e || 10221 === e || 10222 === e || 10223 === e || e >= 10224 && e <= 10239 || e >= 10240 && e <= 10495 || e >= 10496 && e <= 10626 || 10627 === e || 10628 === e || 10629 === e || 10630 === e || 10631 === e || 10632 === e || 10633 === e || 10634 === e || 10635 === e || 10636 === e || 10637 === e || 10638 === e || 10639 === e || 10640 === e || 10641 === e || 10642 === e || 10643 === e || 10644 === e || 10645 === e || 10646 === e || 10647 === e || 10648 === e || e >= 10649 && e <= 10711 || 10712 === e || 10713 === e || 10714 === e || 10715 === e || e >= 10716 && e <= 10747 || 10748 === e || 10749 === e || e >= 10750 && e <= 11007 || e >= 11008 && e <= 11055 || e >= 11056 && e <= 11076 || e >= 11077 && e <= 11078 || e >= 11079 && e <= 11084 || e >= 11085 && e <= 11123 || e >= 11124 && e <= 11125 || e >= 11126 && e <= 11157 || 11158 === e || e >= 11159 && e <= 11263 || e >= 11776 && e <= 11777 || 11778 === e || 11779 === e || 11780 === e || 11781 === e || e >= 11782 && e <= 11784 || 11785 === e || 11786 === e || 11787 === e || 11788 === e || 11789 === e || e >= 11790 && e <= 11798 || 11799 === e || e >= 11800 && e <= 11801 || 11802 === e || 11803 === e || 11804 === e || 11805 === e || e >= 11806 && e <= 11807 || 11808 === e || 11809 === e || 11810 === e || 11811 === e || 11812 === e || 11813 === e || 11814 === e || 11815 === e || 11816 === e || 11817 === e || e >= 11818 && e <= 11822 || 11823 === e || e >= 11824 && e <= 11833 || e >= 11834 && e <= 11835 || e >= 11836 && e <= 11839 || 11840 === e || 11841 === e || 11842 === e || e >= 11843 && e <= 11855 || e >= 11856 && e <= 11857 || 11858 === e || e >= 11859 && e <= 11903 || e >= 12289 && e <= 12291 || 12296 === e || 12297 === e || 12298 === e || 12299 === e || 12300 === e || 12301 === e || 12302 === e || 12303 === e || 12304 === e || 12305 === e || e >= 12306 && e <= 12307 || 12308 === e || 12309 === e || 12310 === e || 12311 === e || 12312 === e || 12313 === e || 12314 === e || 12315 === e || 12316 === e || 12317 === e || e >= 12318 && e <= 12319 || 12320 === e || 12336 === e || 64830 === e || 64831 === e || e >= 65093 && e <= 65094
            }
            t.Parser = C
        },
        27830: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.WHITE_SPACE_REGEX = t.SPACE_SEPARATOR_REGEX = void 0, t.SPACE_SEPARATOR_REGEX = /[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/, t.WHITE_SPACE_REGEX = /[\t-\r \x85\u200E\u200F\u2028\u2029]/
        },
        14393: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.timeData = void 0, t.timeData = {
                "001": ["H", "h"],
                AC: ["H", "h", "hb", "hB"],
                AD: ["H", "hB"],
                AE: ["h", "hB", "hb", "H"],
                AF: ["H", "hb", "hB", "h"],
                AG: ["h", "hb", "H", "hB"],
                AI: ["H", "h", "hb", "hB"],
                AL: ["h", "H", "hB"],
                AM: ["H", "hB"],
                AO: ["H", "hB"],
                AR: ["H", "h", "hB", "hb"],
                AS: ["h", "H"],
                AT: ["H", "hB"],
                AU: ["h", "hb", "H", "hB"],
                AW: ["H", "hB"],
                AX: ["H"],
                AZ: ["H", "hB", "h"],
                BA: ["H", "hB", "h"],
                BB: ["h", "hb", "H", "hB"],
                BD: ["h", "hB", "H"],
                BE: ["H", "hB"],
                BF: ["H", "hB"],
                BG: ["H", "hB", "h"],
                BH: ["h", "hB", "hb", "H"],
                BJ: ["H", "hB"],
                BL: ["H", "hB"],
                BM: ["h", "hb", "H", "hB"],
                BN: ["hb", "hB", "h", "H"],
                BO: ["H", "hB", "h", "hb"],
                BQ: ["H"],
                BR: ["H", "hB"],
                BS: ["h", "hb", "H", "hB"],
                BT: ["h", "H"],
                BW: ["H", "h", "hb", "hB"],
                BZ: ["H", "h", "hb", "hB"],
                CA: ["h", "hb", "H", "hB"],
                CC: ["H", "h", "hb", "hB"],
                CD: ["hB", "H"],
                CF: ["H", "h", "hB"],
                CG: ["H", "hB"],
                CH: ["H", "hB", "h"],
                CI: ["H", "hB"],
                CK: ["H", "h", "hb", "hB"],
                CL: ["H", "h", "hB", "hb"],
                CM: ["H", "h", "hB"],
                CN: ["H", "hB", "hb", "h"],
                CO: ["h", "H", "hB", "hb"],
                CP: ["H"],
                CR: ["H", "h", "hB", "hb"],
                CU: ["H", "h", "hB", "hb"],
                CV: ["H", "hB"],
                CX: ["H", "h", "hb", "hB"],
                CY: ["h", "H", "hb", "hB"],
                CZ: ["H"],
                DE: ["H", "hB"],
                DG: ["H", "h", "hb", "hB"],
                DJ: ["h", "H"],
                DK: ["H"],
                DM: ["h", "hb", "H", "hB"],
                DO: ["h", "H", "hB", "hb"],
                DZ: ["h", "hB", "hb", "H"],
                EA: ["H", "h", "hB", "hb"],
                EC: ["H", "hB", "h", "hb"],
                EE: ["H", "hB"],
                EG: ["h", "hB", "hb", "H"],
                EH: ["h", "hB", "hb", "H"],
                ER: ["h", "H"],
                ES: ["H", "hB", "h", "hb"],
                ET: ["hB", "hb", "h", "H"],
                FI: ["H"],
                FJ: ["h", "hb", "H", "hB"],
                FK: ["H", "h", "hb", "hB"],
                FM: ["h", "hb", "H", "hB"],
                FR: ["H", "hB"],
                GA: ["H", "hB"],
                GB: ["H", "h", "hb", "hB"],
                GD: ["h", "hb", "H", "hB"],
                GE: ["H", "hB", "h"],
                GF: ["H", "hB"],
                GG: ["H", "h", "hb", "hB"],
                GH: ["h", "H"],
                GI: ["H", "h", "hb", "hB"],
                GM: ["h", "hb", "H", "hB"],
                GN: ["H", "hB"],
                GP: ["H", "hB"],
                GQ: ["H", "hB", "h", "hb"],
                GR: ["h", "H", "hb", "hB"],
                GT: ["H", "h", "hB", "hb"],
                GU: ["h", "hb", "H", "hB"],
                GW: ["H", "hB"],
                GY: ["h", "hb", "H", "hB"],
                HK: ["h", "hB", "hb", "H"],
                HN: ["H", "h", "hB", "hb"],
                HR: ["H", "hB"],
                IC: ["H", "h", "hB", "hb"],
                ID: ["H"],
                IE: ["H", "h", "hb", "hB"],
                IL: ["H", "hB"],
                IM: ["H", "h", "hb", "hB"],
                IN: ["h", "H"],
                IO: ["H", "h", "hb", "hB"],
                IQ: ["h", "hB", "hb", "H"],
                IR: ["hB", "H"],
                IS: ["H"],
                IT: ["H", "hB"],
                JE: ["H", "h", "hb", "hB"],
                JM: ["h", "hb", "H", "hB"],
                JO: ["h", "hB", "hb", "H"],
                JP: ["H", "h", "K"],
                KE: ["hB", "hb", "H", "h"],
                KG: ["H", "h", "hB", "hb"],
                KH: ["hB", "h", "H", "hb"],
                KI: ["h", "hb", "H", "hB"],
                KM: ["H", "h", "hB", "hb"],
                KN: ["h", "hb", "H", "hB"],
                KP: ["h", "H", "hB", "hb"],
                KR: ["h", "H", "hB", "hb"],
                KW: ["h", "hB", "hb", "H"],
                KY: ["h", "hb", "H", "hB"],
                KZ: ["H", "hB"],
                LA: ["H", "hb", "hB", "h"],
                LB: ["h", "hB", "hb", "H"],
                LC: ["h", "hb", "H", "hB"],
                LI: ["H", "hB", "h"],
                LK: ["H", "h", "hB", "hb"],
                LR: ["h", "hb", "H", "hB"],
                LS: ["h", "H"],
                LT: ["H", "h", "hb", "hB"],
                LU: ["H", "h", "hB"],
                LV: ["H", "hB", "hb", "h"],
                LY: ["h", "hB", "hb", "H"],
                MA: ["H", "h", "hB", "hb"],
                MC: ["H", "hB"],
                MD: ["H", "hB"],
                ME: ["H", "hB", "h"],
                MF: ["H", "hB"],
                MH: ["h", "hb", "H", "hB"],
                MK: ["H", "h", "hb", "hB"],
                ML: ["H"],
                MM: ["hB", "hb", "H", "h"],
                MN: ["H", "h", "hb", "hB"],
                MO: ["h", "hB", "hb", "H"],
                MP: ["h", "hb", "H", "hB"],
                MQ: ["H", "hB"],
                MR: ["h", "hB", "hb", "H"],
                MS: ["H", "h", "hb", "hB"],
                MW: ["h", "hb", "H", "hB"],
                MX: ["H", "h", "hB", "hb"],
                MY: ["hb", "hB", "h", "H"],
                MZ: ["H", "hB"],
                NA: ["h", "H", "hB", "hb"],
                NC: ["H", "hB"],
                NE: ["H"],
                NF: ["H", "h", "hb", "hB"],
                NG: ["H", "h", "hb", "hB"],
                NI: ["H", "h", "hB", "hb"],
                NL: ["H", "hB"],
                NP: ["H", "h", "hB"],
                NR: ["H", "h", "hb", "hB"],
                NU: ["H", "h", "hb", "hB"],
                NZ: ["h", "hb", "H", "hB"],
                OM: ["h", "hB", "hb", "H"],
                PA: ["h", "H", "hB", "hb"],
                PE: ["H", "hB", "h", "hb"],
                PF: ["H", "h", "hB"],
                PG: ["h", "H"],
                PH: ["h", "hB", "hb", "H"],
                PK: ["h", "hB", "H"],
                PM: ["H", "hB"],
                PN: ["H", "h", "hb", "hB"],
                PR: ["h", "H", "hB", "hb"],
                PS: ["h", "hB", "hb", "H"],
                PT: ["H", "hB"],
                PW: ["h", "H"],
                PY: ["H", "h", "hB", "hb"],
                QA: ["h", "hB", "hb", "H"],
                RE: ["H", "hB"],
                RO: ["H", "hB"],
                RS: ["H", "hB", "h"],
                RU: ["H"],
                SA: ["h", "hB", "hb", "H"],
                SB: ["h", "hb", "H", "hB"],
                SC: ["H", "h", "hB"],
                SD: ["h", "hB", "hb", "H"],
                SE: ["H"],
                SG: ["h", "hb", "H", "hB"],
                SH: ["H", "h", "hb", "hB"],
                SI: ["H", "hB"],
                SJ: ["H"],
                SK: ["H"],
                SL: ["h", "hb", "H", "hB"],
                SM: ["H", "h", "hB"],
                SN: ["H", "h", "hB"],
                SO: ["h", "H"],
                SR: ["H", "hB"],
                SS: ["h", "hb", "H", "hB"],
                ST: ["H", "hB"],
                SV: ["H", "h", "hB", "hb"],
                SX: ["H", "h", "hb", "hB"],
                SY: ["h", "hB", "hb", "H"],
                SZ: ["h", "hb", "H", "hB"],
                TA: ["H", "h", "hb", "hB"],
                TC: ["h", "hb", "H", "hB"],
                TD: ["h", "H", "hB"],
                TF: ["H", "h", "hB"],
                TG: ["H", "hB"],
                TL: ["H", "hB", "hb", "h"],
                TN: ["h", "hB", "hb", "H"],
                TO: ["h", "H"],
                TR: ["H", "hB"],
                TT: ["h", "hb", "H", "hB"],
                TW: ["hB", "hb", "h", "H"],
                TZ: ["hB", "hb", "H", "h"],
                UA: ["H", "hB", "h"],
                UG: ["hB", "hb", "H", "h"],
                UM: ["h", "hb", "H", "hB"],
                US: ["h", "hb", "H", "hB"],
                UY: ["H", "h", "hB", "hb"],
                UZ: ["H", "hB", "h"],
                VA: ["H", "h", "hB"],
                VC: ["h", "hb", "H", "hB"],
                VE: ["h", "H", "hB", "hb"],
                VG: ["h", "hb", "H", "hB"],
                VI: ["h", "hb", "H", "hB"],
                VU: ["h", "H"],
                WF: ["H", "hB"],
                WS: ["h", "H"],
                XK: ["H", "hB", "h"],
                YE: ["h", "hB", "hb", "H"],
                YT: ["H", "hB"],
                ZA: ["H", "h", "hb", "hB"],
                ZM: ["h", "hb", "H", "hB"],
                "af-ZA": ["H", "h", "hB", "hb"],
                "ar-001": ["h", "hB", "hb", "H"],
                "ca-ES": ["H", "h", "hB"],
                "en-001": ["h", "hb", "H", "hB"],
                "es-BO": ["H", "h", "hB", "hb"],
                "es-BR": ["H", "h", "hB", "hb"],
                "es-EC": ["H", "h", "hB", "hb"],
                "es-ES": ["H", "h", "hB", "hb"],
                "es-GQ": ["H", "h", "hB", "hb"],
                "es-PE": ["H", "h", "hB", "hb"],
                "fr-CA": ["H", "h", "hB"],
                "gl-ES": ["H", "h", "hB"],
                "gu-IN": ["hB", "hb", "h", "H"],
                "hi-IN": ["hB", "h", "H"],
                "it-CH": ["H", "h", "hB"],
                "it-IT": ["H", "h", "hB"],
                "kn-IN": ["hB", "h", "H"],
                "ml-IN": ["hB", "h", "H"],
                "mr-IN": ["hB", "hb", "h", "H"],
                "pa-IN": ["hB", "hb", "h", "H"],
                "ta-IN": ["hB", "h", "hb", "H"],
                "te-IN": ["hB", "h", "H"],
                "zu-ZA": ["H", "hB", "hb", "h"]
            }
        },
        67281: (e, t) => {
            var r, n;
            Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.createNumberElement = t.createLiteralElement = t.isDateTimeSkeleton = t.isNumberSkeleton = t.isTagElement = t.isPoundElement = t.isPluralElement = t.isSelectElement = t.isTimeElement = t.isDateElement = t.isNumberElement = t.isArgumentElement = t.isLiteralElement = t.SKELETON_TYPE = t.TYPE = void 0,
                function(e) {
                    e[e.literal = 0] = "literal", e[e.argument = 1] = "argument", e[e.number = 2] = "number", e[e.date = 3] = "date", e[e.time = 4] = "time", e[e.select = 5] = "select", e[e.plural = 6] = "plural", e[e.pound = 7] = "pound", e[e.tag = 8] = "tag"
                }(r = t.TYPE || (t.TYPE = {})),
                function(e) {
                    e[e.number = 0] = "number", e[e.dateTime = 1] = "dateTime"
                }(n = t.SKELETON_TYPE || (t.SKELETON_TYPE = {})), t.isLiteralElement = function(e) {
                    return e.type === r.literal
                }, t.isArgumentElement = function(e) {
                    return e.type === r.argument
                }, t.isNumberElement = function(e) {
                    return e.type === r.number
                }, t.isDateElement = function(e) {
                    return e.type === r.date
                }, t.isTimeElement = function(e) {
                    return e.type === r.time
                }, t.isSelectElement = function(e) {
                    return e.type === r.select
                }, t.isPluralElement = function(e) {
                    return e.type === r.plural
                }, t.isPoundElement = function(e) {
                    return e.type === r.pound
                }, t.isTagElement = function(e) {
                    return e.type === r.tag
                }, t.isNumberSkeleton = function(e) {
                    return !(!e || "object" != typeof e || e.type !== n.number)
                }, t.isDateTimeSkeleton = function(e) {
                    return !(!e || "object" != typeof e || e.type !== n.dateTime)
                }, t.createLiteralElement = function(e) {
                    return {
                        type: r.literal,
                        value: e
                    }
                }, t.createNumberElement = function(e, t) {
                    return {
                        type: r.number,
                        value: e,
                        style: t
                    }
                }
        },
        67996: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.parseDateTimeSkeleton = void 0;
            var r = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;
            t.parseDateTimeSkeleton = function(e) {
                var t = {};
                return e.replace(r, (function(e) {
                    var r = e.length;
                    switch (e[0]) {
                        case "G":
                            t.era = 4 === r ? "long" : 5 === r ? "narrow" : "short";
                            break;
                        case "y":
                            t.year = 2 === r ? "2-digit" : "numeric";
                            break;
                        case "Y":
                        case "u":
                        case "U":
                        case "r":
                            throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");
                        case "q":
                        case "Q":
                            throw new RangeError("`q/Q` (quarter) patterns are not supported");
                        case "M":
                        case "L":
                            t.month = ["numeric", "2-digit", "short", "long", "narrow"][r - 1];
                            break;
                        case "w":
                        case "W":
                            throw new RangeError("`w/W` (week) patterns are not supported");
                        case "d":
                            t.day = ["numeric", "2-digit"][r - 1];
                            break;
                        case "D":
                        case "F":
                        case "g":
                            throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");
                        case "E":
                            t.weekday = 4 === r ? "short" : 5 === r ? "narrow" : "short";
                            break;
                        case "e":
                            if (r < 4) throw new RangeError("`e..eee` (weekday) patterns are not supported");
                            t.weekday = ["short", "long", "narrow", "short"][r - 4];
                            break;
                        case "c":
                            if (r < 4) throw new RangeError("`c..ccc` (weekday) patterns are not supported");
                            t.weekday = ["short", "long", "narrow", "short"][r - 4];
                            break;
                        case "a":
                            t.hour12 = !0;
                            break;
                        case "b":
                        case "B":
                            throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");
                        case "h":
                            t.hourCycle = "h12", t.hour = ["numeric", "2-digit"][r - 1];
                            break;
                        case "H":
                            t.hourCycle = "h23", t.hour = ["numeric", "2-digit"][r - 1];
                            break;
                        case "K":
                            t.hourCycle = "h11", t.hour = ["numeric", "2-digit"][r - 1];
                            break;
                        case "k":
                            t.hourCycle = "h24", t.hour = ["numeric", "2-digit"][r - 1];
                            break;
                        case "j":
                        case "J":
                        case "C":
                            throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");
                        case "m":
                            t.minute = ["numeric", "2-digit"][r - 1];
                            break;
                        case "s":
                            t.second = ["numeric", "2-digit"][r - 1];
                            break;
                        case "S":
                        case "A":
                            throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");
                        case "z":
                            t.timeZoneName = r < 4 ? "short" : "long";
                            break;
                        case "Z":
                        case "O":
                        case "v":
                        case "V":
                        case "X":
                        case "x":
                            throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")
                    }
                    return ""
                })), t
            }
        },
        78702: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970);
            n.__exportStar(r(67996), t), n.__exportStar(r(68688), t)
        },
        68688: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.parseNumberSkeleton = t.parseNumberSkeletonFromString = void 0;
            var n = r(22970),
                a = r(520);
            t.parseNumberSkeletonFromString = function(e) {
                if (0 === e.length) throw new Error("Number skeleton cannot be empty");
                for (var t = [], r = 0, n = e.split(a.WHITE_SPACE_REGEX).filter((function(e) {
                        return e.length > 0
                    })); r < n.length; r++) {
                    var i = n[r].split("/");
                    if (0 === i.length) throw new Error("Invalid number skeleton");
                    for (var o = i[0], u = i.slice(1), s = 0, l = u; s < l.length; s++)
                        if (0 === l[s].length) throw new Error("Invalid number skeleton");
                    t.push({
                        stem: o,
                        options: u
                    })
                }
                return t
            };
            var i = /^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,
                o = /^(@+)?(\+|#+)?[rs]?$/g,
                u = /(\*)(0+)|(#+)(0+)|(0+)/g,
                s = /^(0+)$/;

            function l(e) {
                var t = {};
                return "r" === e[e.length - 1] ? t.roundingPriority = "morePrecision" : "s" === e[e.length - 1] && (t.roundingPriority = "lessPrecision"), e.replace(o, (function(e, r, n) {
                    return "string" != typeof n ? (t.minimumSignificantDigits = r.length, t.maximumSignificantDigits = r.length) : "+" === n ? t.minimumSignificantDigits = r.length : "#" === r[0] ? t.maximumSignificantDigits = r.length : (t.minimumSignificantDigits = r.length, t.maximumSignificantDigits = r.length + ("string" == typeof n ? n.length : 0)), ""
                })), t
            }

            function c(e) {
                switch (e) {
                    case "sign-auto":
                        return {
                            signDisplay: "auto"
                        };
                    case "sign-accounting":
                    case "()":
                        return {
                            currencySign: "accounting"
                        };
                    case "sign-always":
                    case "+!":
                        return {
                            signDisplay: "always"
                        };
                    case "sign-accounting-always":
                    case "()!":
                        return {
                            signDisplay: "always",
                            currencySign: "accounting"
                        };
                    case "sign-except-zero":
                    case "+?":
                        return {
                            signDisplay: "exceptZero"
                        };
                    case "sign-accounting-except-zero":
                    case "()?":
                        return {
                            signDisplay: "exceptZero",
                            currencySign: "accounting"
                        };
                    case "sign-never":
                    case "+_":
                        return {
                            signDisplay: "never"
                        }
                }
            }

            function m(e) {
                var t;
                if ("E" === e[0] && "E" === e[1] ? (t = {
                        notation: "engineering"
                    }, e = e.slice(2)) : "E" === e[0] && (t = {
                        notation: "scientific"
                    }, e = e.slice(1)), t) {
                    var r = e.slice(0, 2);
                    if ("+!" === r ? (t.signDisplay = "always", e = e.slice(2)) : "+?" === r && (t.signDisplay = "exceptZero", e = e.slice(2)), !s.test(e)) throw new Error("Malformed concise eng/scientific notation");
                    t.minimumIntegerDigits = e.length
                }
                return t
            }

            function f(e) {
                return c(e) || {}
            }
            t.parseNumberSkeleton = function(e) {
                for (var t = {}, r = 0, a = e; r < a.length; r++) {
                    var p = a[r];
                    switch (p.stem) {
                        case "percent":
                        case "%":
                            t.style = "percent";
                            continue;
                        case "%x100":
                            t.style = "percent", t.scale = 100;
                            continue;
                        case "currency":
                            t.style = "currency", t.currency = p.options[0];
                            continue;
                        case "group-off":
                        case ",_":
                            t.useGrouping = !1;
                            continue;
                        case "precision-integer":
                        case ".":
                            t.maximumFractionDigits = 0;
                            continue;
                        case "measure-unit":
                        case "unit":
                            t.style = "unit", t.unit = p.options[0].replace(/^(.*?)-/, "");
                            continue;
                        case "compact-short":
                        case "K":
                            t.notation = "compact", t.compactDisplay = "short";
                            continue;
                        case "compact-long":
                        case "KK":
                            t.notation = "compact", t.compactDisplay = "long";
                            continue;
                        case "scientific":
                            t = n.__assign(n.__assign(n.__assign({}, t), {
                                notation: "scientific"
                            }), p.options.reduce((function(e, t) {
                                return n.__assign(n.__assign({}, e), f(t))
                            }), {}));
                            continue;
                        case "engineering":
                            t = n.__assign(n.__assign(n.__assign({}, t), {
                                notation: "engineering"
                            }), p.options.reduce((function(e, t) {
                                return n.__assign(n.__assign({}, e), f(t))
                            }), {}));
                            continue;
                        case "notation-simple":
                            t.notation = "standard";
                            continue;
                        case "unit-width-narrow":
                            t.currencyDisplay = "narrowSymbol", t.unitDisplay = "narrow";
                            continue;
                        case "unit-width-short":
                            t.currencyDisplay = "code", t.unitDisplay = "short";
                            continue;
                        case "unit-width-full-name":
                            t.currencyDisplay = "name", t.unitDisplay = "long";
                            continue;
                        case "unit-width-iso-code":
                            t.currencyDisplay = "symbol";
                            continue;
                        case "scale":
                            t.scale = parseFloat(p.options[0]);
                            continue;
                        case "integer-width":
                            if (p.options.length > 1) throw new RangeError("integer-width stems only accept a single optional option");
                            p.options[0].replace(u, (function(e, r, n, a, i, o) {
                                if (r) t.minimumIntegerDigits = n.length;
                                else {
                                    if (a && i) throw new Error("We currently do not support maximum integer digits");
                                    if (o) throw new Error("We currently do not support exact integer digits")
                                }
                                return ""
                            }));
                            continue
                    }
                    if (s.test(p.stem)) t.minimumIntegerDigits = p.stem.length;
                    else if (i.test(p.stem)) {
                        if (p.options.length > 1) throw new RangeError("Fraction-precision stems only accept a single optional option");
                        p.stem.replace(i, (function(e, r, n, a, i, o) {
                            return "*" === n ? t.minimumFractionDigits = r.length : a && "#" === a[0] ? t.maximumFractionDigits = a.length : i && o ? (t.minimumFractionDigits = i.length, t.maximumFractionDigits = i.length + o.length) : (t.minimumFractionDigits = r.length, t.maximumFractionDigits = r.length), ""
                        }));
                        var d = p.options[0];
                        "w" === d ? t = n.__assign(n.__assign({}, t), {
                            trailingZeroDisplay: "stripIfInteger"
                        }) : d && (t = n.__assign(n.__assign({}, t), l(d)))
                    } else if (o.test(p.stem)) t = n.__assign(n.__assign({}, t), l(p.stem));
                    else {
                        var h = c(p.stem);
                        h && (t = n.__assign(n.__assign({}, t), h));
                        var g = m(p.stem);
                        g && (t = n.__assign(n.__assign({}, t), g))
                    }
                }
                return t
            }
        },
        520: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.WHITE_SPACE_REGEX = void 0, t.WHITE_SPACE_REGEX = /[\t-\r \x85\u200E\u200F\u2028\u2029]/i
        },
        81007: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.createIntl = t.formatNumberToParts = t.formatNumber = t.formatRelativeTime = t.formatPlural = t.formatList = t.formatDisplayName = t.formatTimeToParts = t.formatTime = t.formatDateToParts = t.formatDate = t.formatMessage = t.getNamedFormat = t.createFormatters = t.DEFAULT_INTL_CONFIG = t.filterProps = t.createIntlCache = t.defineMessage = t.defineMessages = void 0;
            var n = r(22970);
            n.__exportStar(r(46561), t), t.defineMessages = function(e) {
                return e
            }, t.defineMessage = function(e) {
                return e
            };
            var a = r(53918);
            Object.defineProperty(t, "createIntlCache", {
                enumerable: !0,
                get: function() {
                    return a.createIntlCache
                }
            }), Object.defineProperty(t, "filterProps", {
                enumerable: !0,
                get: function() {
                    return a.filterProps
                }
            }), Object.defineProperty(t, "DEFAULT_INTL_CONFIG", {
                enumerable: !0,
                get: function() {
                    return a.DEFAULT_INTL_CONFIG
                }
            }), Object.defineProperty(t, "createFormatters", {
                enumerable: !0,
                get: function() {
                    return a.createFormatters
                }
            }), Object.defineProperty(t, "getNamedFormat", {
                enumerable: !0,
                get: function() {
                    return a.getNamedFormat
                }
            }), n.__exportStar(r(53775), t);
            var i = r(83864);
            Object.defineProperty(t, "formatMessage", {
                enumerable: !0,
                get: function() {
                    return i.formatMessage
                }
            });
            var o = r(58075);
            Object.defineProperty(t, "formatDate", {
                enumerable: !0,
                get: function() {
                    return o.formatDate
                }
            }), Object.defineProperty(t, "formatDateToParts", {
                enumerable: !0,
                get: function() {
                    return o.formatDateToParts
                }
            }), Object.defineProperty(t, "formatTime", {
                enumerable: !0,
                get: function() {
                    return o.formatTime
                }
            }), Object.defineProperty(t, "formatTimeToParts", {
                enumerable: !0,
                get: function() {
                    return o.formatTimeToParts
                }
            });
            var u = r(68739);
            Object.defineProperty(t, "formatDisplayName", {
                enumerable: !0,
                get: function() {
                    return u.formatDisplayName
                }
            });
            var s = r(56739);
            Object.defineProperty(t, "formatList", {
                enumerable: !0,
                get: function() {
                    return s.formatList
                }
            });
            var l = r(10298);
            Object.defineProperty(t, "formatPlural", {
                enumerable: !0,
                get: function() {
                    return l.formatPlural
                }
            });
            var c = r(79556);
            Object.defineProperty(t, "formatRelativeTime", {
                enumerable: !0,
                get: function() {
                    return c.formatRelativeTime
                }
            });
            var m = r(19028);
            Object.defineProperty(t, "formatNumber", {
                enumerable: !0,
                get: function() {
                    return m.formatNumber
                }
            }), Object.defineProperty(t, "formatNumberToParts", {
                enumerable: !0,
                get: function() {
                    return m.formatNumberToParts
                }
            });
            var f = r(52978);
            Object.defineProperty(t, "createIntl", {
                enumerable: !0,
                get: function() {
                    return f.createIntl
                }
            })
        },
        45544: (e, t) => {
            function r(e) {
                if (void 0 === e) return NaN;
                if (null === e) return 0;
                if ("boolean" == typeof e) return e ? 1 : 0;
                if ("number" == typeof e) return e;
                if ("symbol" == typeof e || "bigint" == typeof e) throw new TypeError("Cannot convert symbol/bigint to number");
                return Number(e)
            }

            function n(e, t) {
                return Object.is ? Object.is(e, t) : e === t ? 0 !== e || 1 / e == 1 / t : e != e && t != t
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.msFromTime = t.OrdinaryHasInstance = t.SecFromTime = t.MinFromTime = t.HourFromTime = t.DateFromTime = t.MonthFromTime = t.InLeapYear = t.DayWithinYear = t.DaysInYear = t.YearFromTime = t.TimeFromYear = t.DayFromYear = t.WeekDay = t.Day = t.Type = t.HasOwnProperty = t.ArrayCreate = t.SameValue = t.ToObject = t.TimeClip = t.ToNumber = t.ToString = void 0, t.ToString = function(e) {
                if ("symbol" == typeof e) throw TypeError("Cannot convert a Symbol value to a string");
                return String(e)
            }, t.ToNumber = r, t.TimeClip = function(e) {
                return isFinite(e) ? Math.abs(e) > 8640000000000001 ? NaN : function(e) {
                    var t = r(e);
                    if (isNaN(t) || n(t, -0)) return 0;
                    if (isFinite(t)) return t;
                    var a = Math.floor(Math.abs(t));
                    return t < 0 && (a = -a), n(a, -0) ? 0 : a
                }(e) : NaN
            }, t.ToObject = function(e) {
                if (null == e) throw new TypeError("undefined/null cannot be converted to object");
                return Object(e)
            }, t.SameValue = n, t.ArrayCreate = function(e) {
                return new Array(e)
            }, t.HasOwnProperty = function(e, t) {
                return Object.prototype.hasOwnProperty.call(e, t)
            }, t.Type = function(e) {
                return null === e ? "Null" : void 0 === e ? "Undefined" : "function" == typeof e || "object" == typeof e ? "Object" : "number" == typeof e ? "Number" : "boolean" == typeof e ? "Boolean" : "string" == typeof e ? "String" : "symbol" == typeof e ? "Symbol" : "bigint" == typeof e ? "BigInt" : void 0
            };
            var a = 864e5;

            function i(e, t) {
                return e - Math.floor(e / t) * t
            }

            function o(e) {
                return Math.floor(e / a)
            }

            function u(e) {
                return Date.UTC(e, 0) / a
            }

            function s(e) {
                return new Date(e).getUTCFullYear()
            }

            function l(e) {
                return e % 4 != 0 ? 365 : e % 100 != 0 ? 366 : e % 400 != 0 ? 365 : 366
            }

            function c(e) {
                return o(e) - u(s(e))
            }

            function m(e) {
                return 365 === l(s(e)) ? 0 : 1
            }

            function f(e) {
                var t = c(e),
                    r = m(e);
                if (t >= 0 && t < 31) return 0;
                if (t < 59 + r) return 1;
                if (t < 90 + r) return 2;
                if (t < 120 + r) return 3;
                if (t < 151 + r) return 4;
                if (t < 181 + r) return 5;
                if (t < 212 + r) return 6;
                if (t < 243 + r) return 7;
                if (t < 273 + r) return 8;
                if (t < 304 + r) return 9;
                if (t < 334 + r) return 10;
                if (t < 365 + r) return 11;
                throw new Error("Invalid time")
            }
            t.Day = o, t.WeekDay = function(e) {
                return i(o(e) + 4, 7)
            }, t.DayFromYear = u, t.TimeFromYear = function(e) {
                return Date.UTC(e, 0)
            }, t.YearFromTime = s, t.DaysInYear = l, t.DayWithinYear = c, t.InLeapYear = m, t.MonthFromTime = f, t.DateFromTime = function(e) {
                var t = c(e),
                    r = f(e),
                    n = m(e);
                if (0 === r) return t + 1;
                if (1 === r) return t - 30;
                if (2 === r) return t - 58 - n;
                if (3 === r) return t - 89 - n;
                if (4 === r) return t - 119 - n;
                if (5 === r) return t - 150 - n;
                if (6 === r) return t - 180 - n;
                if (7 === r) return t - 211 - n;
                if (8 === r) return t - 242 - n;
                if (9 === r) return t - 272 - n;
                if (10 === r) return t - 303 - n;
                if (11 === r) return t - 333 - n;
                throw new Error("Invalid time")
            };
            t.HourFromTime = function(e) {
                return i(Math.floor(e / 36e5), 24)
            }, t.MinFromTime = function(e) {
                return i(Math.floor(e / 6e4), 60)
            }, t.SecFromTime = function(e) {
                return i(Math.floor(e / 1e3), 60)
            }, t.OrdinaryHasInstance = function(e, t, r) {
                if ("function" != typeof e) return !1;
                if (null == r ? void 0 : r.boundTargetFunction) return t instanceof(null == r ? void 0 : r.boundTargetFunction);
                if ("object" != typeof t) return !1;
                var n = e.prototype;
                if ("object" != typeof n) throw new TypeError("OrdinaryHasInstance called on an object with an invalid prototype property.");
                return Object.prototype.isPrototypeOf.call(n, t)
            }, t.msFromTime = function(e) {
                return i(e, 1e3)
            }
        },
        85386: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CanonicalizeLocaleList = void 0, t.CanonicalizeLocaleList = function(e) {
                return Intl.getCanonicalLocales(e)
            }
        },
        20163: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CanonicalizeTimeZoneName = void 0, t.CanonicalizeTimeZoneName = function(e, t) {
                var r = t.tzData,
                    n = t.uppercaseLinks,
                    a = e.toUpperCase(),
                    i = Object.keys(r).reduce((function(e, t) {
                        return e[t.toUpperCase()] = t, e
                    }), {}),
                    o = n[a] || i[a];
                return "Etc/UTC" === o || "Etc/GMT" === o ? "UTC" : o
            }
        },
        80935: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CoerceOptionsToObject = void 0;
            var n = r(45544);
            t.CoerceOptionsToObject = function(e) {
                return void 0 === e ? Object.create(null) : (0, n.ToObject)(e)
            }
        },
        20823: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.DefaultNumberOption = void 0, t.DefaultNumberOption = function(e, t, r, n) {
                if (void 0 !== e) {
                    if (e = Number(e), isNaN(e) || e < t || e > r) throw new RangeError("".concat(e, " is outside of range [").concat(t, ", ").concat(r, "]"));
                    return Math.floor(e)
                }
                return n
            }
        },
        42670: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetNumberOption = void 0;
            var n = r(20823);
            t.GetNumberOption = function(e, t, r, a, i) {
                var o = e[t];
                return (0, n.DefaultNumberOption)(o, r, a, i)
            }
        },
        80748: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetOption = void 0;
            var n = r(45544);
            t.GetOption = function(e, t, r, a, i) {
                if ("object" != typeof e) throw new TypeError("Options must be an object");
                var o = e[t];
                if (void 0 !== o) {
                    if ("boolean" !== r && "string" !== r) throw new TypeError("invalid type");
                    if ("boolean" === r && (o = Boolean(o)), "string" === r && (o = (0, n.ToString)(o)), void 0 !== a && !a.filter((function(e) {
                            return e == o
                        })).length) throw new RangeError("".concat(o, " is not within ").concat(a.join(", ")));
                    return o
                }
                return i
            }
        },
        77342: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetOptionsObject = void 0, t.GetOptionsObject = function(e) {
                if (void 0 === e) return Object.create(null);
                if ("object" == typeof e) return e;
                throw new TypeError("Options must be an object")
            }
        },
        49256: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetStringOrBooleanOption = void 0;
            var n = r(45544);
            t.GetStringOrBooleanOption = function(e, t, r, a, i, o) {
                var u = e[t];
                if (void 0 === u) return o;
                if (!0 === u) return a;
                if (!1 === Boolean(u)) return i;
                if ("true" === (u = (0, n.ToString)(u)) || "false" === u) return o;
                if (-1 === (r || []).indexOf(u)) throw new RangeError("Invalid value ".concat(u));
                return u
            }
        },
        40779: (e, t) => {
            function r(e) {
                return e.slice(e.indexOf("-") + 1)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IsSanctionedSimpleUnitIdentifier = t.SIMPLE_UNITS = t.removeUnitNamespace = t.SANCTIONED_UNITS = void 0, t.SANCTIONED_UNITS = ["angle-degree", "area-acre", "area-hectare", "concentr-percent", "digital-bit", "digital-byte", "digital-gigabit", "digital-gigabyte", "digital-kilobit", "digital-kilobyte", "digital-megabit", "digital-megabyte", "digital-petabyte", "digital-terabit", "digital-terabyte", "duration-day", "duration-hour", "duration-millisecond", "duration-minute", "duration-month", "duration-second", "duration-week", "duration-year", "length-centimeter", "length-foot", "length-inch", "length-kilometer", "length-meter", "length-mile-scandinavian", "length-mile", "length-millimeter", "length-yard", "mass-gram", "mass-kilogram", "mass-ounce", "mass-pound", "mass-stone", "temperature-celsius", "temperature-fahrenheit", "volume-fluid-ounce", "volume-gallon", "volume-liter", "volume-milliliter"], t.removeUnitNamespace = r, t.SIMPLE_UNITS = t.SANCTIONED_UNITS.map(r), t.IsSanctionedSimpleUnitIdentifier = function(e) {
                return t.SIMPLE_UNITS.indexOf(e) > -1
            }
        },
        74537: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IsValidTimeZoneName = void 0, t.IsValidTimeZoneName = function(e, t) {
                var r = t.tzData,
                    n = t.uppercaseLinks,
                    a = e.toUpperCase(),
                    i = new Set,
                    o = new Set;
                return Object.keys(r).map((function(e) {
                    return e.toUpperCase()
                })).forEach((function(e) {
                    return i.add(e)
                })), Object.keys(n).forEach((function(e) {
                    o.add(e.toUpperCase()), i.add(n[e].toUpperCase())
                })), i.has(a) || o.has(a)
            }
        },
        98733: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IsWellFormedCurrencyCode = void 0;
            var r = /[^A-Z]/;
            t.IsWellFormedCurrencyCode = function(e) {
                return 3 === (e = e.replace(/([a-z])/g, (function(e, t) {
                    return t.toUpperCase()
                }))).length && !r.test(e)
            }
        },
        29122: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IsWellFormedUnitIdentifier = void 0;
            var n = r(40779);
            t.IsWellFormedUnitIdentifier = function(e) {
                if (e = e.replace(/([A-Z])/g, (function(e, t) {
                        return t.toLowerCase()
                    })), (0, n.IsSanctionedSimpleUnitIdentifier)(e)) return !0;
                var t = e.split("-per-");
                if (2 !== t.length) return !1;
                var r = t[0],
                    a = t[1];
                return !(!(0, n.IsSanctionedSimpleUnitIdentifier)(r) || !(0, n.IsSanctionedSimpleUnitIdentifier)(a))
            }
        },
        8135: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ApplyUnsignedRoundingMode = void 0, t.ApplyUnsignedRoundingMode = function(e, t, r, n) {
                if (e === t) return t;
                if (void 0 === n) throw new Error("unsignedRoundingMode is mandatory");
                if ("zero" === n) return t;
                if ("infinity" === n) return r;
                var a = e - t,
                    i = r - e;
                if (a < i) return t;
                if (i < a) return r;
                if (a !== i) throw new Error("Unexpected error");
                if ("half-zero" === n) return t;
                if ("half-infinity" === n) return r;
                if ("half-even" !== n) throw new Error("Unexpected value for unsignedRoundingMode: ".concat(n));
                return 0 == t / (r - t) % 2 ? t : r
            }
        },
        49974: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CollapseNumberRange = void 0, t.CollapseNumberRange = function(e) {
                return e
            }
        },
        50605: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ComputeExponent = void 0;
            var n = r(18369),
                a = r(1404),
                i = r(13782);
            t.ComputeExponent = function(e, t, r) {
                var o = r.getInternalSlots;
                if (0 === t) return [0, 0];
                t < 0 && (t = -t);
                var u = (0, n.getMagnitude)(t),
                    s = (0, a.ComputeExponentForMagnitude)(e, u, {
                        getInternalSlots: o
                    });
                t = s < 0 ? t * Math.pow(10, -s) : t / Math.pow(10, s);
                var l = (0, i.FormatNumericToString)(o(e), t);
                return 0 === l.roundedNumber || (0, n.getMagnitude)(l.roundedNumber) === u - s ? [s, u] : [(0, a.ComputeExponentForMagnitude)(e, u + 1, {
                    getInternalSlots: o
                }), u + 1]
            }
        },
        1404: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ComputeExponentForMagnitude = void 0, t.ComputeExponentForMagnitude = function(e, t, r) {
                var n = (0, r.getInternalSlots)(e),
                    a = n.notation,
                    i = n.dataLocaleData,
                    o = n.numberingSystem;
                switch (a) {
                    case "standard":
                        return 0;
                    case "scientific":
                        return t;
                    case "engineering":
                        return 3 * Math.floor(t / 3);
                    default:
                        var u = n.compactDisplay,
                            s = n.style,
                            l = n.currencyDisplay,
                            c = void 0;
                        if ("currency" === s && "name" !== l) c = (i.numbers.currency[o] || i.numbers.currency[i.numbers.nu[0]]).short;
                        else {
                            var m = i.numbers.decimal[o] || i.numbers.decimal[i.numbers.nu[0]];
                            c = "long" === u ? m.long : m.short
                        }
                        if (!c) return 0;
                        var f = String(Math.pow(10, t)),
                            p = Object.keys(c);
                        if (f < p[0]) return 0;
                        if (f > p[p.length - 1]) return p[p.length - 1].length - 1;
                        var d = p.indexOf(f);
                        if (-1 === d) return 0;
                        var h = p[d];
                        return "0" === c[h].other ? 0 : h.length - c[h].other.match(/0+/)[0].length
                }
            }
        },
        88532: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CurrencyDigits = void 0;
            var n = r(45544);
            t.CurrencyDigits = function(e, t) {
                var r = t.currencyDigitsData;
                return (0, n.HasOwnProperty)(r, e) ? r[e] : 2
            }
        },
        26980: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatApproximately = void 0, t.FormatApproximately = function(e, t, r) {
                var n = (0, r.getInternalSlots)(e),
                    a = n.dataLocaleData.numbers.symbols[n.numberingSystem].approximatelySign;
                return t.push({
                    type: "approximatelySign",
                    value: a
                }), t
            }
        },
        45472: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatNumericRange = void 0;
            var n = r(34809);
            t.FormatNumericRange = function(e, t, r, a) {
                var i = a.getInternalSlots;
                return (0, n.PartitionNumberRangePattern)(e, t, r, {
                    getInternalSlots: i
                }).map((function(e) {
                    return e.value
                })).join("")
            }
        },
        50643: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatNumericRangeToParts = void 0;
            var n = r(34809);
            t.FormatNumericRangeToParts = function(e, t, r, a) {
                var i = a.getInternalSlots;
                return (0, n.PartitionNumberRangePattern)(e, t, r, {
                    getInternalSlots: i
                }).map((function(e, t) {
                    return {
                        type: e.type,
                        value: e.value,
                        source: e.source,
                        result: t.toString()
                    }
                }))
            }
        },
        91347: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatNumericToParts = void 0;
            var n = r(19530),
                a = r(45544);
            t.FormatNumericToParts = function(e, t, r) {
                for (var i = (0, n.PartitionNumberPattern)(e, t, r), o = (0, a.ArrayCreate)(0), u = 0, s = i; u < s.length; u++) {
                    var l = s[u];
                    o.push({
                        type: l.type,
                        value: l.value
                    })
                }
                return o
            }
        },
        13782: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatNumericToString = void 0;
            var n = r(45544),
                a = r(78325),
                i = r(18369),
                o = r(94331);
            t.FormatNumericToString = function(e, t) {
                var r, u = t < 0 || (0, n.SameValue)(t, -0);
                switch (u && (t = -t), e.roundingType) {
                    case "significantDigits":
                        r = (0, a.ToRawPrecision)(t, e.minimumSignificantDigits, e.maximumSignificantDigits);
                        break;
                    case "fractionDigits":
                        r = (0, o.ToRawFixed)(t, e.minimumFractionDigits, e.maximumFractionDigits);
                        break;
                    default:
                        (r = (0, a.ToRawPrecision)(t, 1, 2)).integerDigitsCount > 1 && (r = (0, o.ToRawFixed)(t, 0, 0))
                }
                t = r.roundedNumber;
                var s = r.formattedString,
                    l = r.integerDigitsCount,
                    c = e.minimumIntegerDigits;
                return l < c && (s = (0, i.repeat)("0", c - l) + s), u && (t = -t), {
                    roundedNumber: t,
                    formattedString: s
                }
            }
        },
        45267: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetUnsignedRoundingMode = void 0;
            var r = {
                    ceil: "zero",
                    floor: "infinity",
                    expand: "infinity",
                    trunc: "zero",
                    halfCeil: "half-zero",
                    halfFloor: "half-infinity",
                    halfExpand: "half-infinity",
                    halfTrunc: "half-zero",
                    halfEven: "half-even"
                },
                n = {
                    ceil: "infinity",
                    floor: "zero",
                    expand: "infinity",
                    trunc: "zero",
                    halfCeil: "half-infinity",
                    halfFloor: "half-zero",
                    halfExpand: "half-infinity",
                    halfTrunc: "half-zero",
                    halfEven: "half-even"
                };
            t.GetUnsignedRoundingMode = function(e, t) {
                return t ? r[e] : n[e]
            }
        },
        63547: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.InitializeNumberFormat = void 0;
            var n = r(85386),
                a = r(80748),
                i = r(62077),
                o = r(11982),
                u = r(88532),
                s = r(92682),
                l = r(18369),
                c = r(80935),
                m = r(42670),
                f = r(49256),
                p = [1, 2, 5, 10, 20, 25, 50, 100, 200, 250, 500, 1e3, 2e3];
            t.InitializeNumberFormat = function(e, t, r, d) {
                var h = d.getInternalSlots,
                    g = d.localeData,
                    v = d.availableLocales,
                    y = d.numberingSystemNames,
                    E = d.getDefaultLocale,
                    b = d.currencyDigitsData,
                    _ = (0, n.CanonicalizeLocaleList)(t),
                    D = (0, c.CoerceOptionsToObject)(r),
                    F = Object.create(null),
                    S = (0, a.GetOption)(D, "localeMatcher", "string", ["lookup", "best fit"], "best fit");
                F.localeMatcher = S;
                var T = (0, a.GetOption)(D, "numberingSystem", "string", void 0, void 0);
                if (void 0 !== T && y.indexOf(T) < 0) throw RangeError("Invalid numberingSystems: ".concat(T));
                F.nu = T;
                var P = (0, i.ResolveLocale)(v, _, F, ["nu"], g, E),
                    I = g[P.dataLocale];
                (0, l.invariant)(!!I, "Missing locale data for ".concat(P.dataLocale));
                var O = h(e);
                O.locale = P.locale, O.dataLocale = P.dataLocale, O.numberingSystem = P.nu, O.dataLocaleData = I, (0, o.SetNumberFormatUnitOptions)(e, D, {
                    getInternalSlots: h
                });
                var N, C, M = O.style;
                if ("currency" === M) {
                    var A = O.currency,
                        L = (0, u.CurrencyDigits)(A, {
                            currencyDigitsData: b
                        });
                    N = L, C = L
                } else N = 0, C = "percent" === M ? 0 : 3;
                var w = (0, a.GetOption)(D, "notation", "string", ["standard", "scientific", "engineering", "compact"], "standard");
                O.notation = w, (0, s.SetNumberFormatDigitOptions)(O, D, N, C, w);
                var B = (0, m.GetNumberOption)(D, "roundingIncrement", 1, 5e3, 1);
                if (-1 === p.indexOf(B)) throw new RangeError("Invalid rounding increment value: ".concat(B, ".\nValid values are ").concat(p, "."));
                if (1 !== B && "fractionDigits" !== O.roundingType) throw new TypeError("For roundingIncrement > 1 only fractionDigits is a valid roundingType");
                if (1 !== B && O.maximumFractionDigits !== O.minimumFractionDigits) throw new RangeError("With roundingIncrement > 1, maximumFractionDigits and minimumFractionDigits must be equal.");
                O.roundingIncrement = B;
                var R = (0, a.GetOption)(D, "trailingZeroDisplay", "string", ["auto", "stripIfInteger"], "auto");
                O.trailingZeroDisplay = R;
                var x = (0, a.GetOption)(D, "compactDisplay", "string", ["short", "long"], "short"),
                    j = "auto";
                return "compact" === w && (O.compactDisplay = x, j = "min2"), O.useGrouping = (0, f.GetStringOrBooleanOption)(D, "useGrouping", ["min2", "auto", "always"], "always", !1, j), O.signDisplay = (0, a.GetOption)(D, "signDisplay", "string", ["auto", "never", "always", "exceptZero", "negative"], "auto"), O.roundingMode = (0, a.GetOption)(D, "roundingMode", "string", ["ceil", "floor", "expand", "trunc", "halfCeil", "halfFloor", "halfExpand", "halfTrunc", "halfEven"], "halfExpand"), e
            }
        },
        19530: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.PartitionNumberPattern = void 0;
            var n = r(22970),
                a = r(13782),
                i = r(45544),
                o = r(50605),
                u = n.__importDefault(r(19646));
            t.PartitionNumberPattern = function(e, t, r) {
                var n, s, l, c = r.getInternalSlots,
                    m = c(e),
                    f = m.pl,
                    p = m.dataLocaleData,
                    d = m.numberingSystem,
                    h = p.numbers.symbols[d] || p.numbers.symbols[p.numbers.nu[0]],
                    g = 0,
                    v = 0;
                if (isNaN(t)) s = h.nan;
                else if (t == Number.POSITIVE_INFINITY || t == Number.NEGATIVE_INFINITY) s = h.infinity;
                else {
                    if (!(0, i.SameValue)(t, -0)) {
                        if (!isFinite(t)) throw new Error("Input must be a mathematical value");
                        "percent" == m.style && (t *= 100), v = (n = (0, o.ComputeExponent)(e, t, {
                            getInternalSlots: c
                        }))[0], g = n[1], t = v < 0 ? t * Math.pow(10, -v) : t / Math.pow(10, v)
                    }
                    var y = (0, a.FormatNumericToString)(m, t);
                    s = y.formattedString, t = y.roundedNumber
                }
                switch (m.signDisplay) {
                    case "never":
                        l = 0;
                        break;
                    case "auto":
                        l = (0, i.SameValue)(t, 0) || t > 0 || isNaN(t) ? 0 : -1;
                        break;
                    case "always":
                        l = (0, i.SameValue)(t, 0) || t > 0 || isNaN(t) ? 1 : -1;
                        break;
                    default:
                        l = 0 === t || isNaN(t) ? 0 : t > 0 ? 1 : -1
                }
                return (0, u.default)({
                    roundedNumber: t,
                    formattedString: s,
                    exponent: v,
                    magnitude: g,
                    sign: l
                }, m.dataLocaleData, f, m)
            }
        },
        34809: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.PartitionNumberRangePattern = void 0;
            var n = r(19530),
                a = r(49974),
                i = r(26980);
            t.PartitionNumberRangePattern = function(e, t, r, o) {
                var u = o.getInternalSlots;
                if (isNaN(t) || isNaN(r)) throw new RangeError("Input must be a number");
                var s = [],
                    l = (0, n.PartitionNumberPattern)(e, t, {
                        getInternalSlots: u
                    }),
                    c = (0, n.PartitionNumberPattern)(e, r, {
                        getInternalSlots: u
                    });
                if (l === c) return (0, i.FormatApproximately)(e, l, {
                    getInternalSlots: u
                });
                for (var m = 0, f = l; m < f.length; m++) f[m].source = "startRange";
                s = s.concat(l);
                var p = u(e),
                    d = p.dataLocaleData.numbers.symbols[p.numberingSystem];
                s.push({
                    type: "literal",
                    value: d.rangeSign,
                    source: "shared"
                });
                for (var h = 0, g = c; h < g.length; h++) g[h].source = "endRange";
                return s = s.concat(c), (0, a.CollapseNumberRange)(s)
            }
        },
        92682: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.SetNumberFormatDigitOptions = void 0;
            var n = r(42670),
                a = r(20823),
                i = r(80748);
            t.SetNumberFormatDigitOptions = function(e, t, r, o, u) {
                var s = (0, n.GetNumberOption)(t, "minimumIntegerDigits", 1, 21, 1),
                    l = t.minimumFractionDigits,
                    c = t.maximumFractionDigits,
                    m = t.minimumSignificantDigits,
                    f = t.maximumSignificantDigits;
                e.minimumIntegerDigits = s;
                var p = (0, i.GetOption)(t, "roundingPriority", "string", ["auto", "morePrecision", "lessPrecision"], "auto"),
                    d = void 0 !== m || void 0 !== f,
                    h = void 0 !== l || void 0 !== c,
                    g = !0,
                    v = !0;
                if ("auto" === p && (g = d, (d || !h && "compact" === u) && (v = !1)), g && (d ? (m = (0, a.DefaultNumberOption)(m, 1, 21, 1), f = (0, a.DefaultNumberOption)(f, m, 21, 21), e.minimumSignificantDigits = m, e.maximumSignificantDigits = f) : (e.minimumSignificantDigits = 1, e.maximumSignificantDigits = 21)), v)
                    if (h) {
                        if (l = (0, a.DefaultNumberOption)(l, 0, 20, void 0), c = (0, a.DefaultNumberOption)(c, 0, 20, void 0), void 0 === l) l = Math.min(r, c);
                        else if (void 0 === c) c = Math.max(o, l);
                        else if (l > c) throw new RangeError("Invalid range, ".concat(l, " > ").concat(c));
                        e.minimumFractionDigits = l, e.maximumFractionDigits = c
                    } else e.minimumFractionDigits = r, e.maximumFractionDigits = o;
                g || v ? e.roundingType = "morePrecision" === p ? "morePrecision" : "lessPrecision" === p ? "lessPrecision" : d ? "significantDigits" : "fractionDigits" : (e.roundingType = "morePrecision", e.minimumFractionDigits = 0, e.maximumFractionDigits = 0, e.minimumSignificantDigits = 1, e.maximumSignificantDigits = 2)
            }
        },
        11982: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.SetNumberFormatUnitOptions = void 0;
            var n = r(80748),
                a = r(98733),
                i = r(29122);
            t.SetNumberFormatUnitOptions = function(e, t, r) {
                void 0 === t && (t = Object.create(null));
                var o = (0, r.getInternalSlots)(e),
                    u = (0, n.GetOption)(t, "style", "string", ["decimal", "percent", "currency", "unit"], "decimal");
                o.style = u;
                var s = (0, n.GetOption)(t, "currency", "string", void 0, void 0);
                if (void 0 !== s && !(0, a.IsWellFormedCurrencyCode)(s)) throw RangeError("Malformed currency code");
                if ("currency" === u && void 0 === s) throw TypeError("currency cannot be undefined");
                var l = (0, n.GetOption)(t, "currencyDisplay", "string", ["code", "symbol", "narrowSymbol", "name"], "symbol"),
                    c = (0, n.GetOption)(t, "currencySign", "string", ["standard", "accounting"], "standard"),
                    m = (0, n.GetOption)(t, "unit", "string", void 0, void 0);
                if (void 0 !== m && !(0, i.IsWellFormedUnitIdentifier)(m)) throw RangeError("Invalid unit argument for Intl.NumberFormat()");
                if ("unit" === u && void 0 === m) throw TypeError("unit cannot be undefined");
                var f = (0, n.GetOption)(t, "unitDisplay", "string", ["short", "narrow", "long"], "short");
                "currency" === u && (o.currency = s.toUpperCase(), o.currencyDisplay = l, o.currencySign = c), "unit" === u && (o.unit = m, o.unitDisplay = f)
            }
        },
        94331: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ToRawFixed = void 0;
            var n = r(18369);
            t.ToRawFixed = function(e, t, r) {
                var a, i, o = r,
                    u = Math.round(e * Math.pow(10, o)),
                    s = u / Math.pow(10, o);
                if (u < 1e21) a = u.toString();
                else {
                    var l = (a = u.toString()).split("e"),
                        c = l[0],
                        m = l[1];
                    a = c.replace(".", ""), a += (0, n.repeat)("0", Math.max(+m - a.length + 1, 0))
                }
                if (0 !== o) {
                    var f = a.length;
                    f <= o && (a = (0, n.repeat)("0", o + 1 - f) + a, f = o + 1);
                    var p = a.slice(0, f - o),
                        d = a.slice(f - o);
                    a = "".concat(p, ".").concat(d), i = p.length
                } else i = a.length;
                for (var h = r - t; h > 0 && "0" === a[a.length - 1];) a = a.slice(0, -1), h--;
                return "." === a[a.length - 1] && (a = a.slice(0, -1)), {
                    formattedString: a,
                    roundedNumber: s,
                    integerDigitsCount: i
                }
            }
        },
        78325: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ToRawPrecision = void 0;
            var n = r(18369);
            t.ToRawPrecision = function(e, t, r) {
                var a, i, o, u, s = r;
                if (0 === e) a = (0, n.repeat)("0", s), i = 0, o = 0;
                else {
                    var l = e.toString(),
                        c = l.indexOf("e"),
                        m = l.split("e"),
                        f = m[0],
                        p = m[1],
                        d = f.replace(".", "");
                    if (c >= 0 && d.length <= s) i = +p, a = d + (0, n.repeat)("0", s - d.length), o = e;
                    else {
                        var h = (i = (0, n.getMagnitude)(e)) - s + 1,
                            g = Math.round(y(e, h));
                        y(g, s - 1) >= 10 && (i += 1, g = Math.floor(g / 10)), a = g.toString(), o = y(g, s - 1 - i)
                    }
                }
                if (i >= s - 1 ? (a += (0, n.repeat)("0", i - s + 1), u = i + 1) : i >= 0 ? (a = "".concat(a.slice(0, i + 1), ".").concat(a.slice(i + 1)), u = i + 1) : (a = "0.".concat((0, n.repeat)("0", -i - 1)).concat(a), u = 1), a.indexOf(".") >= 0 && r > t) {
                    for (var v = r - t; v > 0 && "0" === a[a.length - 1];) a = a.slice(0, -1), v--;
                    "." === a[a.length - 1] && (a = a.slice(0, -1))
                }
                return {
                    formattedString: a,
                    roundedNumber: o,
                    integerDigitsCount: u
                };

                function y(e, t) {
                    return t < 0 ? e * Math.pow(10, -t) : e / Math.pow(10, t)
                }
            }
        },
        8282: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.digitMapping = void 0, t.digitMapping = {
                adlm: ["𞥐", "𞥑", "𞥒", "𞥓", "𞥔", "𞥕", "𞥖", "𞥗", "𞥘", "𞥙"],
                ahom: ["𑜰", "𑜱", "𑜲", "𑜳", "𑜴", "𑜵", "𑜶", "𑜷", "𑜸", "𑜹"],
                arab: ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"],
                arabext: ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"],
                bali: ["᭐", "᭑", "᭒", "᭓", "᭔", "᭕", "᭖", "᭗", "᭘", "᭙"],
                beng: ["০", "১", "২", "৩", "৪", "৫", "৬", "৭", "৮", "৯"],
                bhks: ["𑱐", "𑱑", "𑱒", "𑱓", "𑱔", "𑱕", "𑱖", "𑱗", "𑱘", "𑱙"],
                brah: ["𑁦", "𑁧", "𑁨", "𑁩", "𑁪", "𑁫", "𑁬", "𑁭", "𑁮", "𑁯"],
                cakm: ["𑄶", "𑄷", "𑄸", "𑄹", "𑄺", "𑄻", "𑄼", "𑄽", "𑄾", "𑄿"],
                cham: ["꩐", "꩑", "꩒", "꩓", "꩔", "꩕", "꩖", "꩗", "꩘", "꩙"],
                deva: ["०", "१", "२", "३", "४", "५", "६", "७", "८", "९"],
                diak: ["𑥐", "𑥑", "𑥒", "𑥓", "𑥔", "𑥕", "𑥖", "𑥗", "𑥘", "𑥙"],
                fullwide: ["０", "１", "２", "３", "４", "５", "６", "７", "８", "９"],
                gong: ["𑶠", "𑶡", "𑶢", "𑶣", "𑶤", "𑶥", "𑶦", "𑶧", "𑶨", "𑶩"],
                gonm: ["𑵐", "𑵑", "𑵒", "𑵓", "𑵔", "𑵕", "𑵖", "𑵗", "𑵘", "𑵙"],
                gujr: ["૦", "૧", "૨", "૩", "૪", "૫", "૬", "૭", "૮", "૯"],
                guru: ["੦", "੧", "੨", "੩", "੪", "੫", "੬", "੭", "੮", "੯"],
                hanidec: ["〇", "一", "二", "三", "四", "五", "六", "七", "八", "九"],
                hmng: ["𖭐", "𖭑", "𖭒", "𖭓", "𖭔", "𖭕", "𖭖", "𖭗", "𖭘", "𖭙"],
                hmnp: ["𞅀", "𞅁", "𞅂", "𞅃", "𞅄", "𞅅", "𞅆", "𞅇", "𞅈", "𞅉"],
                java: ["꧐", "꧑", "꧒", "꧓", "꧔", "꧕", "꧖", "꧗", "꧘", "꧙"],
                kali: ["꤀", "꤁", "꤂", "꤃", "꤄", "꤅", "꤆", "꤇", "꤈", "꤉"],
                khmr: ["០", "១", "២", "៣", "៤", "៥", "៦", "៧", "៨", "៩"],
                knda: ["೦", "೧", "೨", "೩", "೪", "೫", "೬", "೭", "೮", "೯"],
                lana: ["᪀", "᪁", "᪂", "᪃", "᪄", "᪅", "᪆", "᪇", "᪈", "᪉"],
                lanatham: ["᪐", "᪑", "᪒", "᪓", "᪔", "᪕", "᪖", "᪗", "᪘", "᪙"],
                laoo: ["໐", "໑", "໒", "໓", "໔", "໕", "໖", "໗", "໘", "໙"],
                lepc: ["᪐", "᪑", "᪒", "᪓", "᪔", "᪕", "᪖", "᪗", "᪘", "᪙"],
                limb: ["᥆", "᥇", "᥈", "᥉", "᥊", "᥋", "᥌", "᥍", "᥎", "᥏"],
                mathbold: ["𝟎", "𝟏", "𝟐", "𝟑", "𝟒", "𝟓", "𝟔", "𝟕", "𝟖", "𝟗"],
                mathdbl: ["𝟘", "𝟙", "𝟚", "𝟛", "𝟜", "𝟝", "𝟞", "𝟟", "𝟠", "𝟡"],
                mathmono: ["𝟶", "𝟷", "𝟸", "𝟹", "𝟺", "𝟻", "𝟼", "𝟽", "𝟾", "𝟿"],
                mathsanb: ["𝟬", "𝟭", "𝟮", "𝟯", "𝟰", "𝟱", "𝟲", "𝟳", "𝟴", "𝟵"],
                mathsans: ["𝟢", "𝟣", "𝟤", "𝟥", "𝟦", "𝟧", "𝟨", "𝟩", "𝟪", "𝟫"],
                mlym: ["൦", "൧", "൨", "൩", "൪", "൫", "൬", "൭", "൮", "൯"],
                modi: ["𑙐", "𑙑", "𑙒", "𑙓", "𑙔", "𑙕", "𑙖", "𑙗", "𑙘", "𑙙"],
                mong: ["᠐", "᠑", "᠒", "᠓", "᠔", "᠕", "᠖", "᠗", "᠘", "᠙"],
                mroo: ["𖩠", "𖩡", "𖩢", "𖩣", "𖩤", "𖩥", "𖩦", "𖩧", "𖩨", "𖩩"],
                mtei: ["꯰", "꯱", "꯲", "꯳", "꯴", "꯵", "꯶", "꯷", "꯸", "꯹"],
                mymr: ["၀", "၁", "၂", "၃", "၄", "၅", "၆", "၇", "၈", "၉"],
                mymrshan: ["႐", "႑", "႒", "႓", "႔", "႕", "႖", "႗", "႘", "႙"],
                mymrtlng: ["꧰", "꧱", "꧲", "꧳", "꧴", "꧵", "꧶", "꧷", "꧸", "꧹"],
                newa: ["𑑐", "𑑑", "𑑒", "𑑓", "𑑔", "𑑕", "𑑖", "𑑗", "𑑘", "𑑙"],
                nkoo: ["߀", "߁", "߂", "߃", "߄", "߅", "߆", "߇", "߈", "߉"],
                olck: ["᱐", "᱑", "᱒", "᱓", "᱔", "᱕", "᱖", "᱗", "᱘", "᱙"],
                orya: ["୦", "୧", "୨", "୩", "୪", "୫", "୬", "୭", "୮", "୯"],
                osma: ["𐒠", "𐒡", "𐒢", "𐒣", "𐒤", "𐒥", "𐒦", "𐒧", "𐒨", "𐒩"],
                rohg: ["𐴰", "𐴱", "𐴲", "𐴳", "𐴴", "𐴵", "𐴶", "𐴷", "𐴸", "𐴹"],
                saur: ["꣐", "꣑", "꣒", "꣓", "꣔", "꣕", "꣖", "꣗", "꣘", "꣙"],
                segment: ["🯰", "🯱", "🯲", "🯳", "🯴", "🯵", "🯶", "🯷", "🯸", "🯹"],
                shrd: ["𑇐", "𑇑", "𑇒", "𑇓", "𑇔", "𑇕", "𑇖", "𑇗", "𑇘", "𑇙"],
                sind: ["𑋰", "𑋱", "𑋲", "𑋳", "𑋴", "𑋵", "𑋶", "𑋷", "𑋸", "𑋹"],
                sinh: ["෦", "෧", "෨", "෩", "෪", "෫", "෬", "෭", "෮", "෯"],
                sora: ["𑃰", "𑃱", "𑃲", "𑃳", "𑃴", "𑃵", "𑃶", "𑃷", "𑃸", "𑃹"],
                sund: ["᮰", "᮱", "᮲", "᮳", "᮴", "᮵", "᮶", "᮷", "᮸", "᮹"],
                takr: ["𑛀", "𑛁", "𑛂", "𑛃", "𑛄", "𑛅", "𑛆", "𑛇", "𑛈", "𑛉"],
                talu: ["᧐", "᧑", "᧒", "᧓", "᧔", "᧕", "᧖", "᧗", "᧘", "᧙"],
                tamldec: ["௦", "௧", "௨", "௩", "௪", "௫", "௬", "௭", "௮", "௯"],
                telu: ["౦", "౧", "౨", "౩", "౪", "౫", "౬", "౭", "౮", "౯"],
                thai: ["๐", "๑", "๒", "๓", "๔", "๕", "๖", "๗", "๘", "๙"],
                tibt: ["༠", "༡", "༢", "༣", "༤", "༥", "༦", "༧", "༨", "༩"],
                tirh: ["𑓐", "𑓑", "𑓒", "𑓓", "𑓔", "𑓕", "𑓖", "𑓗", "𑓘", "𑓙"],
                vaii: ["ᘠ", "ᘡ", "ᘢ", "ᘣ", "ᘤ", "ᘥ", "ᘦ", "ᘧ", "ᘨ", "ᘩ"],
                wara: ["𑣠", "𑣡", "𑣢", "𑣣", "𑣤", "𑣥", "𑣦", "𑣧", "𑣨", "𑣩"],
                wcho: ["𞋰", "𞋱", "𞋲", "𞋳", "𞋴", "𞋵", "𞋶", "𞋷", "𞋸", "𞋹"]
            }
        },
        19646: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(94331),
                a = r(8282),
                i = r(2978),
                o = new RegExp("^".concat(i.S_UNICODE_REGEX.source)),
                u = new RegExp("".concat(i.S_UNICODE_REGEX.source, "$")),
                s = /[#0](?:[\.,][#0]+)*/g;

            function l(e, t, r, i, o, u, s) {
                var l = [],
                    c = t.formattedString,
                    m = t.roundedNumber;
                if (isNaN(m)) return [{
                    type: "nan",
                    value: c
                }];
                if (!isFinite(m)) return [{
                    type: "infinity",
                    value: c
                }];
                var f = a.digitMapping[o];
                f && (c = c.replace(/\d/g, (function(e) {
                    return f[+e] || e
                })));
                var p, d, h = c.indexOf(".");
                if (h > 0 ? (p = c.slice(0, h), d = c.slice(h + 1)) : p = c, u && ("compact" !== r || m >= 1e4)) {
                    var g = e.group,
                        v = [],
                        y = s.split(".")[0].split(","),
                        E = 3,
                        b = 3;
                    y.length > 1 && (E = y[y.length - 1].length), y.length > 2 && (b = y[y.length - 2].length);
                    var _ = p.length - E;
                    if (_ > 0) {
                        for (v.push(p.slice(_, _ + E)), _ -= b; _ > 0; _ -= b) v.push(p.slice(_, _ + b));
                        v.push(p.slice(0, _ + b))
                    } else v.push(p);
                    for (; v.length > 0;) {
                        var D = v.pop();
                        l.push({
                            type: "integer",
                            value: D
                        }), v.length > 0 && l.push({
                            type: "group",
                            value: g
                        })
                    }
                } else l.push({
                    type: "integer",
                    value: p
                });
                if (void 0 !== d && l.push({
                        type: "decimal",
                        value: e.decimal
                    }, {
                        type: "fraction",
                        value: d
                    }), ("scientific" === r || "engineering" === r) && isFinite(m)) {
                    l.push({
                        type: "exponentSeparator",
                        value: e.exponential
                    }), i < 0 && (l.push({
                        type: "exponentMinusSign",
                        value: e.minusSign
                    }), i = -i);
                    var F = (0, n.ToRawFixed)(i, 0, 0);
                    l.push({
                        type: "exponentInteger",
                        value: F.formattedString
                    })
                }
                return l
            }

            function c(e, t) {
                e.indexOf(";") < 0 && (e = "".concat(e, ";-").concat(e));
                var r = e.split(";"),
                    n = r[0],
                    a = r[1];
                switch (t) {
                    case 0:
                        return n;
                    case -1:
                        return a;
                    default:
                        return a.indexOf("-") >= 0 ? a.replace(/-/g, "+") : "+".concat(n)
                }
            }

            function m(e, t, r) {
                return r[e.select(t)] || r.other
            }
            t.default = function(e, t, r, n) {
                var a, i, f = e.sign,
                    p = e.exponent,
                    d = e.magnitude,
                    h = n.notation,
                    g = n.style,
                    v = n.numberingSystem,
                    y = t.numbers.nu[0],
                    E = null;
                if ("compact" === h && d && (E = function(e, t, r, n, a, i, o) {
                        var u, s, l = e.roundedNumber,
                            f = e.sign,
                            p = e.magnitude,
                            d = String(Math.pow(10, p)),
                            h = r.numbers.nu[0];
                        if ("currency" === n && "name" !== i) {
                            var g = null === (u = ((v = r.numbers.currency)[o] || v[h]).short) || void 0 === u ? void 0 : u[d];
                            if (!g) return null;
                            s = m(t, l, g)
                        } else {
                            var v, y = ((v = r.numbers.decimal)[o] || v[h])[a][d];
                            if (!y) return null;
                            s = m(t, l, y)
                        }
                        return "0" === s ? null : s = c(s, f).replace(/([^\s;\-\+\d¤]+)/g, "{c:$1}").replace(/0+/, "0")
                    }(e, r, t, g, n.compactDisplay, n.currencyDisplay, v)), "currency" === g && "name" !== n.currencyDisplay) {
                    var b = t.currencies[n.currency];
                    if (b) switch (n.currencyDisplay) {
                        case "code":
                            a = n.currency;
                            break;
                        case "symbol":
                            a = b.symbol;
                            break;
                        default:
                            a = b.narrow
                    } else a = n.currency
                }
                i = E || ("decimal" === g || "unit" === g || "currency" === g && "name" === n.currencyDisplay ? c((t.numbers.decimal[v] || t.numbers.decimal[y]).standard, f) : c("currency" === g ? (D = t.numbers.currency[v] || t.numbers.currency[y])[n.currencySign] : t.numbers.percent[v] || t.numbers.percent[y], f));
                var _ = s.exec(i)[0];
                if (i = i.replace(s, "{0}").replace(/'(.)'/g, "$1"), "currency" === g && "name" !== n.currencyDisplay) {
                    var D, F = (D = t.numbers.currency[v] || t.numbers.currency[y]).currencySpacing.afterInsertBetween;
                    F && !u.test(a) && (i = i.replace("¤{0}", "¤".concat(F, "{0}")));
                    var S = D.currencySpacing.beforeInsertBetween;
                    S && !o.test(a) && (i = i.replace("{0}¤", "{0}".concat(S, "¤")))
                }
                for (var T = i.split(/({c:[^}]+}|\{0\}|[¤%\-\+])/g), P = [], I = t.numbers.symbols[v] || t.numbers.symbols[y], O = 0, N = T; O < N.length; O++)
                    if (Z = N[O]) switch (Z) {
                        case "{0}":
                            P.push.apply(P, l(I, e, h, p, v, !E && Boolean(n.useGrouping), _));
                            break;
                        case "-":
                            P.push({
                                type: "minusSign",
                                value: I.minusSign
                            });
                            break;
                        case "+":
                            P.push({
                                type: "plusSign",
                                value: I.plusSign
                            });
                            break;
                        case "%":
                            P.push({
                                type: "percentSign",
                                value: I.percentSign
                            });
                            break;
                        case "¤":
                            P.push({
                                type: "currency",
                                value: a
                            });
                            break;
                        default:
                            /^\{c:/.test(Z) ? P.push({
                                type: "compact",
                                value: Z.substring(3, Z.length - 1)
                            }) : P.push({
                                type: "literal",
                                value: Z
                            })
                    }
                switch (g) {
                    case "currency":
                        if ("name" === n.currencyDisplay) {
                            var C, M = (t.numbers.currency[v] || t.numbers.currency[y]).unitPattern,
                                A = t.currencies[n.currency];
                            C = A ? m(r, e.roundedNumber * Math.pow(10, p), A.displayName) : n.currency;
                            for (var L = [], w = 0, B = M.split(/(\{[01]\})/g); w < B.length; w++) switch (Z = B[w]) {
                                case "{0}":
                                    L.push.apply(L, P);
                                    break;
                                case "{1}":
                                    L.push({
                                        type: "currency",
                                        value: C
                                    });
                                    break;
                                default:
                                    Z && L.push({
                                        type: "literal",
                                        value: Z
                                    })
                            }
                            return L
                        }
                        return P;
                    case "unit":
                        var R = n.unit,
                            x = n.unitDisplay,
                            j = t.units.simple[R];
                        if (M = void 0, j) M = m(r, e.roundedNumber * Math.pow(10, p), t.units.simple[R][x]);
                        else {
                            var H = R.split("-per-"),
                                U = H[0],
                                G = H[1];
                            j = t.units.simple[U];
                            var k = m(r, e.roundedNumber * Math.pow(10, p), t.units.simple[U][x]),
                                V = t.units.simple[G].perUnit[x];
                            if (V) M = V.replace("{0}", k);
                            else {
                                var z = t.units.compound.per[x],
                                    X = m(r, 1, t.units.simple[G][x]);
                                M = M = z.replace("{0}", k).replace("{1}", X.replace("{0}", ""))
                            }
                        }
                        L = [];
                        for (var Y = 0, K = M.split(/(\s*\{0\}\s*)/); Y < K.length; Y++) {
                            var Z = K[Y],
                                W = /^(\s*)\{0\}(\s*)$/.exec(Z);
                            W ? (W[1] && L.push({
                                type: "literal",
                                value: W[1]
                            }), L.push.apply(L, P), W[2] && L.push({
                                type: "literal",
                                value: W[2]
                            })) : Z && L.push({
                                type: "unit",
                                value: Z
                            })
                        }
                        return L;
                    default:
                        return P
                }
            }
        },
        6198: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.PartitionPattern = void 0;
            var n = r(18369);
            t.PartitionPattern = function(e) {
                for (var t = [], r = e.indexOf("{"), a = 0, i = 0, o = e.length; r < e.length && r > -1;) a = e.indexOf("}", r), (0, n.invariant)(a > r, "Invalid pattern ".concat(e)), r > i && t.push({
                    type: "literal",
                    value: e.substring(i, r)
                }), t.push({
                    type: e.substring(r + 1, a),
                    value: void 0
                }), i = a + 1, r = e.indexOf("{", i);
                return i < o && t.push({
                    type: "literal",
                    value: e.substring(i, o)
                }), t
            }
        },
        2378: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.SupportedLocales = void 0;
            var n = r(45544),
                a = r(80748),
                i = r(62077);
            t.SupportedLocales = function(e, t, r) {
                return void 0 !== r && (r = (0, n.ToObject)(r), (0, a.GetOption)(r, "localeMatcher", "string", ["lookup", "best fit"], "best fit")), (0, i.LookupSupportedLocales)(e, t)
            }
        },
        75503: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.isMissingLocaleDataError = void 0;
            var n, a = r(22970);
            n = Error, a.__extends((function() {
                var e = null !== n && n.apply(this, arguments) || this;
                return e.type = "MISSING_LOCALE_DATA", e
            }), n), t.isMissingLocaleDataError = function(e) {
                return "MISSING_LOCALE_DATA" === e.type
            }
        },
        15462: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.invariant = t.isMissingLocaleDataError = t.defineProperty = t.getMagnitude = t.setMultiInternalSlots = t.setInternalSlot = t.isLiteralPart = t.getMultiInternalSlots = t.getInternalSlot = t._formatToParts = void 0;
            var n = r(22970);
            n.__exportStar(r(85386), t), n.__exportStar(r(20163), t), n.__exportStar(r(80935), t), n.__exportStar(r(42670), t), n.__exportStar(r(80748), t), n.__exportStar(r(77342), t), n.__exportStar(r(49256), t), n.__exportStar(r(40779), t), n.__exportStar(r(74537), t), n.__exportStar(r(98733), t), n.__exportStar(r(29122), t), n.__exportStar(r(8135), t), n.__exportStar(r(49974), t), n.__exportStar(r(50605), t), n.__exportStar(r(1404), t), n.__exportStar(r(88532), t), n.__exportStar(r(26980), t), n.__exportStar(r(45472), t), n.__exportStar(r(50643), t), n.__exportStar(r(91347), t), n.__exportStar(r(13782), t), n.__exportStar(r(45267), t), n.__exportStar(r(63547), t), n.__exportStar(r(19530), t), n.__exportStar(r(34809), t), n.__exportStar(r(92682), t), n.__exportStar(r(11982), t), n.__exportStar(r(94331), t), n.__exportStar(r(78325), t);
            var a = r(19646);
            Object.defineProperty(t, "_formatToParts", {
                enumerable: !0,
                get: function() {
                    return n.__importDefault(a).default
                }
            }), n.__exportStar(r(6198), t), n.__exportStar(r(2378), t);
            var i = r(18369);
            Object.defineProperty(t, "getInternalSlot", {
                enumerable: !0,
                get: function() {
                    return i.getInternalSlot
                }
            }), Object.defineProperty(t, "getMultiInternalSlots", {
                enumerable: !0,
                get: function() {
                    return i.getMultiInternalSlots
                }
            }), Object.defineProperty(t, "isLiteralPart", {
                enumerable: !0,
                get: function() {
                    return i.isLiteralPart
                }
            }), Object.defineProperty(t, "setInternalSlot", {
                enumerable: !0,
                get: function() {
                    return i.setInternalSlot
                }
            }), Object.defineProperty(t, "setMultiInternalSlots", {
                enumerable: !0,
                get: function() {
                    return i.setMultiInternalSlots
                }
            }), Object.defineProperty(t, "getMagnitude", {
                enumerable: !0,
                get: function() {
                    return i.getMagnitude
                }
            }), Object.defineProperty(t, "defineProperty", {
                enumerable: !0,
                get: function() {
                    return i.defineProperty
                }
            });
            var o = r(75503);
            Object.defineProperty(t, "isMissingLocaleDataError", {
                enumerable: !0,
                get: function() {
                    return o.isMissingLocaleDataError
                }
            }), n.__exportStar(r(95464), t), n.__exportStar(r(77986), t), n.__exportStar(r(74840), t), n.__exportStar(r(21898), t), n.__exportStar(r(40050), t), n.__exportStar(r(78680), t);
            var u = r(18369);
            Object.defineProperty(t, "invariant", {
                enumerable: !0,
                get: function() {
                    return u.invariant
                }
            }), n.__exportStar(r(45544), t)
        },
        2978: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.S_UNICODE_REGEX = void 0, t.S_UNICODE_REGEX = /[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20BF\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFB\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC1\uFDFC\uFDFD\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDE8\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEE0-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF73\uDF80-\uDFD8\uDFE0-\uDFEB]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDD78\uDD7A-\uDDCB\uDDCD-\uDE53\uDE60-\uDE6D\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6\uDF00-\uDF92\uDF94-\uDFCA]/
        },
        77986: (e, t) => {
            var r;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.RangePatternType = void 0, (r = t.RangePatternType || (t.RangePatternType = {})).startRange = "startRange", r.shared = "shared", r.endRange = "endRange"
        },
        78680: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        74840: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        40050: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        21898: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        95464: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        18369: (e, t) => {
            function r(e, t, r, n) {
                e.get(t) || e.set(t, Object.create(null)), e.get(t)[r] = n
            }

            function n(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                var a = e.get(t);
                if (!a) throw new TypeError("".concat(t, " InternalSlot has not been initialized"));
                return r.reduce((function(e, t) {
                    return e[t] = a[t], e
                }), Object.create(null))
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.invariant = t.UNICODE_EXTENSION_SEQUENCE_REGEX = t.defineProperty = t.isLiteralPart = t.getMultiInternalSlots = t.getInternalSlot = t.setMultiInternalSlots = t.setInternalSlot = t.repeat = t.getMagnitude = void 0, t.getMagnitude = function(e) {
                return Math.floor(Math.log(e) * Math.LOG10E)
            }, t.repeat = function(e, t) {
                if ("function" == typeof e.repeat) return e.repeat(t);
                for (var r = new Array(t), n = 0; n < r.length; n++) r[n] = e;
                return r.join("")
            }, t.setInternalSlot = r, t.setMultiInternalSlots = function(e, t, n) {
                for (var a = 0, i = Object.keys(n); a < i.length; a++) {
                    var o = i[a];
                    r(e, t, o, n[o])
                }
            }, t.getInternalSlot = function(e, t, r) {
                return n(e, t, r)[r]
            }, t.getMultiInternalSlots = n, t.isLiteralPart = function(e) {
                return "literal" === e.type
            }, t.defineProperty = function(e, t, r) {
                var n = r.value;
                Object.defineProperty(e, t, {
                    configurable: !0,
                    enumerable: !1,
                    writable: !0,
                    value: n
                })
            }, t.UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi, t.invariant = function(e, t, r) {
                if (void 0 === r && (r = Error), !e) throw new r(t)
            }
        },
        47613: (e, t) => {
            function r(e, t, r, n) {
                var a, i = null == (a = n) || "number" == typeof a || "boolean" == typeof a ? n : r(n),
                    o = t.get(i);
                return void 0 === o && (o = e.call(this, n), t.set(i, o)), o
            }

            function n(e, t, r) {
                var n = Array.prototype.slice.call(arguments, 3),
                    a = r(n),
                    i = t.get(a);
                return void 0 === i && (i = e.apply(this, n), t.set(a, i)), i
            }

            function a(e, t, r, n, a) {
                return r.bind(t, e, n, a)
            }

            function i(e, t) {
                return a(e, this, 1 === e.length ? r : n, t.cache.create(), t.serializer)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.strategies = t.memoize = void 0, t.memoize = function(e, t) {
                var r = t && t.cache ? t.cache : s,
                    n = t && t.serializer ? t.serializer : o;
                return (t && t.strategy ? t.strategy : i)(e, {
                    cache: r,
                    serializer: n
                })
            };
            var o = function() {
                return JSON.stringify(arguments)
            };

            function u() {
                this.cache = Object.create(null)
            }
            u.prototype.get = function(e) {
                return this.cache[e]
            }, u.prototype.set = function(e, t) {
                this.cache[e] = t
            };
            var s = {
                create: function() {
                    return new u
                }
            };
            t.strategies = {
                variadic: function(e, t) {
                    return a(e, this, n, t.cache.create(), t.serializer)
                },
                monadic: function(e, t) {
                    return a(e, this, r, t.cache.create(), t.serializer)
                }
            }
        },
        67637: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.BestAvailableLocale = void 0, t.BestAvailableLocale = function(e, t) {
                for (var r = t;;) {
                    if (e.has(r)) return r;
                    var n = r.lastIndexOf("-");
                    if (!~n) return;
                    n >= 2 && "-" === r[n - 2] && (n -= 2), r = r.slice(0, n)
                }
            }
        },
        63776: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.BestFitMatcher = void 0;
            var n = r(67637),
                a = r(25076);
            t.BestFitMatcher = function(e, t, r) {
                var i, o = {},
                    u = {},
                    s = {},
                    l = new Set;
                e.forEach((function(e) {
                    var t = new Intl.Locale(e).minimize().toString(),
                        r = Intl.getCanonicalLocales(e)[0] || e;
                    o[t] = e, u[e] = e, s[r] = e, l.add(t), l.add(e), l.add(r)
                }));
                for (var c = 0, m = t; c < m.length; c++) {
                    var f = m[c];
                    if (i) break;
                    var p = f.replace(a.UNICODE_EXTENSION_SEQUENCE_REGEX, "");
                    if (e.has(p)) {
                        i = p;
                        break
                    }
                    if (l.has(p)) {
                        i = p;
                        break
                    }
                    var d = new Intl.Locale(p),
                        h = d.maximize().toString(),
                        g = d.minimize().toString();
                    if (l.has(g)) {
                        i = g;
                        break
                    }
                    i = (0, n.BestAvailableLocale)(l, h)
                }
                return i ? {
                    locale: u[i] || s[i] || o[i] || i
                } : {
                    locale: r()
                }
            }
        },
        18871: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CanonicalizeLocaleList = void 0, t.CanonicalizeLocaleList = function(e) {
                return Intl.getCanonicalLocales(e)
            }
        },
        59189: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.LookupMatcher = void 0;
            var n = r(25076),
                a = r(67637);
            t.LookupMatcher = function(e, t, r) {
                for (var i = {
                        locale: ""
                    }, o = 0, u = t; o < u.length; o++) {
                    var s = u[o],
                        l = s.replace(n.UNICODE_EXTENSION_SEQUENCE_REGEX, ""),
                        c = (0, a.BestAvailableLocale)(e, l);
                    if (c) return i.locale = c, s !== l && (i.extension = s.slice(l.length + 1, s.length)), i
                }
                return i.locale = r(), i
            }
        },
        97522: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.LookupSupportedLocales = void 0;
            var n = r(25076),
                a = r(67637);
            t.LookupSupportedLocales = function(e, t) {
                for (var r = [], i = 0, o = t; i < o.length; i++) {
                    var u = o[i].replace(n.UNICODE_EXTENSION_SEQUENCE_REGEX, ""),
                        s = (0, a.BestAvailableLocale)(e, u);
                    s && r.push(s)
                }
                return r
            }
        },
        61124: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ResolveLocale = void 0;
            var n = r(59189),
                a = r(63776),
                i = r(25076),
                o = r(36870);
            t.ResolveLocale = function(e, t, r, u, s, l) {
                for (var c, m = (c = "lookup" === r.localeMatcher ? (0, n.LookupMatcher)(e, t, l) : (0, a.BestFitMatcher)(e, t, l)).locale, f = {
                        locale: "",
                        dataLocale: m
                    }, p = "-u", d = 0, h = u; d < h.length; d++) {
                    var g = h[d];
                    (0, i.invariant)(m in s, "Missing locale data for ".concat(m));
                    var v = s[m];
                    (0, i.invariant)("object" == typeof v && null !== v, "locale data ".concat(g, " must be an object"));
                    var y = v[g];
                    (0, i.invariant)(Array.isArray(y), "keyLocaleData for ".concat(g, " must be an array"));
                    var E = y[0];
                    (0, i.invariant)("string" == typeof E || null === E, "value must be string or null but got ".concat(typeof E, " in key ").concat(g));
                    var b = "";
                    if (c.extension) {
                        var _ = (0, o.UnicodeExtensionValue)(c.extension, g);
                        void 0 !== _ && ("" !== _ ? ~y.indexOf(_) && (E = _, b = "-".concat(g, "-").concat(E)) : ~_.indexOf("true") && (E = "true", b = "-".concat(g)))
                    }
                    if (g in r) {
                        var D = r[g];
                        (0, i.invariant)("string" == typeof D || null == D, "optionsValue must be String, Undefined or Null"), ~y.indexOf(D) && D !== E && (E = D, b = "")
                    }
                    f[g] = E, p += b
                }
                if (p.length > 2) {
                    var F = m.indexOf("-x-");
                    if (-1 === F) m += p;
                    else {
                        var S = m.slice(0, F),
                            T = m.slice(F, m.length);
                        m = S + p + T
                    }
                    m = Intl.getCanonicalLocales(m)[0]
                }
                return f.locale = m, f
            }
        },
        36870: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.UnicodeExtensionValue = void 0;
            var n = r(25076);
            t.UnicodeExtensionValue = function(e, t) {
                (0, n.invariant)(2 === t.length, "key must have 2 elements");
                var r = e.length,
                    a = "-".concat(t, "-"),
                    i = e.indexOf(a);
                if (-1 !== i) {
                    for (var o = i + 4, u = o, s = o, l = !1; !l;) {
                        var c = e.indexOf("-", s);
                        2 == (-1 === c ? r - s : c - s) ? l = !0 : -1 === c ? (u = r, l = !0) : (u = c, s = c + 1)
                    }
                    return e.slice(o, u)
                }
                if (a = "-".concat(t), -1 !== (i = e.indexOf(a)) && i + 3 === r) return ""
            }
        },
        25076: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.invariant = t.UNICODE_EXTENSION_SEQUENCE_REGEX = void 0, t.UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi, t.invariant = function(e, t, r) {
                if (void 0 === r && (r = Error), !e) throw new r(t)
            }
        },
        62077: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ResolveLocale = t.LookupSupportedLocales = t.match = void 0;
            var n = r(18871),
                a = r(61124);
            t.match = function(e, t, r, i) {
                var o = t.reduce((function(e, t) {
                    return e.add(t), e
                }), new Set);
                return (0, a.ResolveLocale)(o, (0, n.CanonicalizeLocaleList)(e), {
                    localeMatcher: (null == i ? void 0 : i.algorithm) || "best fit"
                }, [], {}, (function() {
                    return r
                })).locale
            };
            var i = r(97522);
            Object.defineProperty(t, "LookupSupportedLocales", {
                enumerable: !0,
                get: function() {
                    return i.LookupSupportedLocales
                }
            });
            var o = r(61124);
            Object.defineProperty(t, "ResolveLocale", {
                enumerable: !0,
                get: function() {
                    return o.ResolveLocale
                }
            })
        },
        37238: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970),
                a = r(48517);
            n.__exportStar(r(76531), t), n.__exportStar(r(48517), t), n.__exportStar(r(40369), t), t.default = a.IntlMessageFormat
        },
        48517: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IntlMessageFormat = void 0;
            var n = r(22970),
                a = r(46057),
                i = r(47613),
                o = r(76531);

            function u(e) {
                return {
                    create: function() {
                        return {
                            get: function(t) {
                                return e[t]
                            },
                            set: function(t, r) {
                                e[t] = r
                            }
                        }
                    }
                }
            }
            t.IntlMessageFormat = function() {
                function e(t, r, a, s) {
                    void 0 === r && (r = e.defaultLocale);
                    var l, c, m, f = this;
                    if (this.formatterCache = {
                            number: {},
                            dateTime: {},
                            pluralRules: {}
                        }, this.format = function(e) {
                            var t = f.formatToParts(e);
                            if (1 === t.length) return t[0].value;
                            var r = t.reduce((function(e, t) {
                                return e.length && t.type === o.PART_TYPE.literal && "string" == typeof e[e.length - 1] ? e[e.length - 1] += t.value : e.push(t.value), e
                            }), []);
                            return r.length <= 1 ? r[0] || "" : r
                        }, this.formatToParts = function(e) {
                            return (0, o.formatToParts)(f.ast, f.locales, f.formatters, f.formats, e, void 0, f.message)
                        }, this.resolvedOptions = function() {
                            var e;
                            return {
                                locale: (null === (e = f.resolvedLocale) || void 0 === e ? void 0 : e.toString()) || Intl.NumberFormat.supportedLocalesOf(f.locales)[0]
                            }
                        }, this.getAst = function() {
                            return f.ast
                        }, this.locales = r, this.resolvedLocale = e.resolveLocale(r), "string" == typeof t) {
                        if (this.message = t, !e.__parse) throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");
                        var p = s || {},
                            d = (p.formatters, n.__rest(p, ["formatters"]));
                        this.ast = e.__parse(t, n.__assign(n.__assign({}, d), {
                            locale: this.resolvedLocale
                        }))
                    } else this.ast = t;
                    if (!Array.isArray(this.ast)) throw new TypeError("A message must be provided as a String or AST.");
                    this.formats = (c = e.formats, (m = a) ? Object.keys(c).reduce((function(e, t) {
                        var r, a;
                        return e[t] = (r = c[t], (a = m[t]) ? n.__assign(n.__assign(n.__assign({}, r || {}), a || {}), Object.keys(r).reduce((function(e, t) {
                            return e[t] = n.__assign(n.__assign({}, r[t]), a[t] || {}), e
                        }), {})) : r), e
                    }), n.__assign({}, c)) : c), this.formatters = s && s.formatters || (void 0 === (l = this.formatterCache) && (l = {
                        number: {},
                        dateTime: {},
                        pluralRules: {}
                    }), {
                        getNumberFormat: (0, i.memoize)((function() {
                            for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                            return new((e = Intl.NumberFormat).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                        }), {
                            cache: u(l.number),
                            strategy: i.strategies.variadic
                        }),
                        getDateTimeFormat: (0, i.memoize)((function() {
                            for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                            return new((e = Intl.DateTimeFormat).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                        }), {
                            cache: u(l.dateTime),
                            strategy: i.strategies.variadic
                        }),
                        getPluralRules: (0, i.memoize)((function() {
                            for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                            return new((e = Intl.PluralRules).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                        }), {
                            cache: u(l.pluralRules),
                            strategy: i.strategies.variadic
                        })
                    })
                }
                return Object.defineProperty(e, "defaultLocale", {
                    get: function() {
                        return e.memoizedDefaultLocale || (e.memoizedDefaultLocale = (new Intl.NumberFormat).resolvedOptions().locale), e.memoizedDefaultLocale
                    },
                    enumerable: !1,
                    configurable: !0
                }), e.memoizedDefaultLocale = null, e.resolveLocale = function(e) {
                    if (void 0 !== Intl.Locale) {
                        var t = Intl.NumberFormat.supportedLocalesOf(e);
                        return t.length > 0 ? new Intl.Locale(t[0]) : new Intl.Locale("string" == typeof e ? e : e[0])
                    }
                }, e.__parse = a.parse, e.formats = {
                    number: {
                        integer: {
                            maximumFractionDigits: 0
                        },
                        currency: {
                            style: "currency"
                        },
                        percent: {
                            style: "percent"
                        }
                    },
                    date: {
                        short: {
                            month: "numeric",
                            day: "numeric",
                            year: "2-digit"
                        },
                        medium: {
                            month: "short",
                            day: "numeric",
                            year: "numeric"
                        },
                        long: {
                            month: "long",
                            day: "numeric",
                            year: "numeric"
                        },
                        full: {
                            weekday: "long",
                            month: "long",
                            day: "numeric",
                            year: "numeric"
                        }
                    },
                    time: {
                        short: {
                            hour: "numeric",
                            minute: "numeric"
                        },
                        medium: {
                            hour: "numeric",
                            minute: "numeric",
                            second: "numeric"
                        },
                        long: {
                            hour: "numeric",
                            minute: "numeric",
                            second: "numeric",
                            timeZoneName: "short"
                        },
                        full: {
                            hour: "numeric",
                            minute: "numeric",
                            second: "numeric",
                            timeZoneName: "short"
                        }
                    }
                }, e
            }()
        },
        40369: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.MissingValueError = t.InvalidValueTypeError = t.InvalidValueError = t.FormatError = t.ErrorCode = void 0;
            var n, a = r(22970);
            ! function(e) {
                e.MISSING_VALUE = "MISSING_VALUE", e.INVALID_VALUE = "INVALID_VALUE", e.MISSING_INTL_API = "MISSING_INTL_API"
            }(n = t.ErrorCode || (t.ErrorCode = {}));
            var i = function(e) {
                function t(t, r, n) {
                    var a = e.call(this, t) || this;
                    return a.code = r, a.originalMessage = n, a
                }
                return a.__extends(t, e), t.prototype.toString = function() {
                    return "[formatjs Error: ".concat(this.code, "] ").concat(this.message)
                }, t
            }(Error);
            t.FormatError = i;
            var o = function(e) {
                function t(t, r, a, i) {
                    return e.call(this, 'Invalid values for "'.concat(t, '": "').concat(r, '". Options are "').concat(Object.keys(a).join('", "'), '"'), n.INVALID_VALUE, i) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.InvalidValueError = o;
            var u = function(e) {
                function t(t, r, a) {
                    return e.call(this, 'Value for "'.concat(t, '" must be of type ').concat(r), n.INVALID_VALUE, a) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.InvalidValueTypeError = u;
            var s = function(e) {
                function t(t, r) {
                    return e.call(this, 'The intl string context variable "'.concat(t, '" was not provided to the string "').concat(r, '"'), n.MISSING_VALUE, r) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.MissingValueError = s
        },
        76531: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatToParts = t.isFormatXMLElementFn = t.PART_TYPE = void 0;
            var n, a = r(46057),
                i = r(40369);

            function o(e) {
                return "function" == typeof e
            }! function(e) {
                e[e.literal = 0] = "literal", e[e.object = 1] = "object"
            }(n = t.PART_TYPE || (t.PART_TYPE = {})), t.isFormatXMLElementFn = o, t.formatToParts = function e(t, r, u, s, l, c, m) {
                if (1 === t.length && (0, a.isLiteralElement)(t[0])) return [{
                    type: n.literal,
                    value: t[0].value
                }];
                for (var f = [], p = 0, d = t; p < d.length; p++) {
                    var h = d[p];
                    if ((0, a.isLiteralElement)(h)) f.push({
                        type: n.literal,
                        value: h.value
                    });
                    else if ((0, a.isPoundElement)(h)) "number" == typeof c && f.push({
                        type: n.literal,
                        value: u.getNumberFormat(r).format(c)
                    });
                    else {
                        var g = h.value;
                        if (!l || !(g in l)) throw new i.MissingValueError(g, m);
                        var v = l[g];
                        if ((0, a.isArgumentElement)(h)) v && "string" != typeof v && "number" != typeof v || (v = "string" == typeof v || "number" == typeof v ? String(v) : ""), f.push({
                            type: "string" == typeof v ? n.literal : n.object,
                            value: v
                        });
                        else if ((0, a.isDateElement)(h)) {
                            var y = "string" == typeof h.style ? s.date[h.style] : (0, a.isDateTimeSkeleton)(h.style) ? h.style.parsedOptions : void 0;
                            f.push({
                                type: n.literal,
                                value: u.getDateTimeFormat(r, y).format(v)
                            })
                        } else if ((0, a.isTimeElement)(h)) y = "string" == typeof h.style ? s.time[h.style] : (0, a.isDateTimeSkeleton)(h.style) ? h.style.parsedOptions : s.time.medium, f.push({
                            type: n.literal,
                            value: u.getDateTimeFormat(r, y).format(v)
                        });
                        else if ((0, a.isNumberElement)(h))(y = "string" == typeof h.style ? s.number[h.style] : (0, a.isNumberSkeleton)(h.style) ? h.style.parsedOptions : void 0) && y.scale && (v *= y.scale || 1), f.push({
                            type: n.literal,
                            value: u.getNumberFormat(r, y).format(v)
                        });
                        else {
                            if ((0, a.isTagElement)(h)) {
                                var E = h.children,
                                    b = h.value,
                                    _ = l[b];
                                if (!o(_)) throw new i.InvalidValueTypeError(b, "function", m);
                                var D = _(e(E, r, u, s, l, c).map((function(e) {
                                    return e.value
                                })));
                                Array.isArray(D) || (D = [D]), f.push.apply(f, D.map((function(e) {
                                    return {
                                        type: "string" == typeof e ? n.literal : n.object,
                                        value: e
                                    }
                                })))
                            }
                            if ((0, a.isSelectElement)(h)) {
                                if (!(F = h.options[v] || h.options.other)) throw new i.InvalidValueError(h.value, v, Object.keys(h.options), m);
                                f.push.apply(f, e(F.value, r, u, s, l))
                            } else if ((0, a.isPluralElement)(h)) {
                                var F;
                                if (!(F = h.options["=".concat(v)])) {
                                    if (!Intl.PluralRules) throw new i.FormatError('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n', i.ErrorCode.MISSING_INTL_API, m);
                                    var S = u.getPluralRules(r, {
                                        type: h.pluralType
                                    }).select(v - (h.offset || 0));
                                    F = h.options[S] || h.options.other
                                }
                                if (!F) throw new i.InvalidValueError(h.value, v, Object.keys(h.options), m);
                                f.push.apply(f, e(F.value, r, u, s, l, v - (h.offset || 0)))
                            }
                        }
                    }
                }
                return (T = f).length < 2 ? T : T.reduce((function(e, t) {
                    var r = e[e.length - 1];
                    return r && r.type === n.literal && t.type === n.literal ? r.value += t.value : e.push(t), e
                }), []);
                var T
            }
        },
        52978: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.createIntl = void 0;
            var n = r(22970),
                a = r(53918),
                i = r(53775),
                o = r(19028),
                u = r(79556),
                s = r(58075),
                l = r(10298),
                c = r(83864),
                m = r(56739),
                f = r(68739);
            t.createIntl = function(e, t) {
                var r = (0, a.createFormatters)(t),
                    p = n.__assign(n.__assign({}, a.DEFAULT_INTL_CONFIG), e),
                    d = p.locale,
                    h = p.defaultLocale,
                    g = p.onError;
                return d ? !Intl.NumberFormat.supportedLocalesOf(d).length && g ? g(new i.MissingDataError('Missing locale data for locale: "'.concat(d, '" in Intl.NumberFormat. Using default locale: "').concat(h, '" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))) : !Intl.DateTimeFormat.supportedLocalesOf(d).length && g && g(new i.MissingDataError('Missing locale data for locale: "'.concat(d, '" in Intl.DateTimeFormat. Using default locale: "').concat(h, '" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))) : (g && g(new i.InvalidConfigError('"locale" was not configured, using "'.concat(h, '" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details'))), p.locale = p.defaultLocale || "en"),
                    function(e) {
                        var t;
                        e.onWarn && e.defaultRichTextElements && "string" == typeof((t = e.messages || {}) ? t[Object.keys(t)[0]] : void 0) && e.onWarn('[@formatjs/intl] "defaultRichTextElements" was specified but "message" was not pre-compiled. \nPlease consider using "@formatjs/cli" to pre-compile your messages for performance.\nFor more details see https://formatjs.io/docs/getting-started/message-distribution')
                    }(p), n.__assign(n.__assign({}, p), {
                        formatters: r,
                        formatNumber: o.formatNumber.bind(null, p, r.getNumberFormat),
                        formatNumberToParts: o.formatNumberToParts.bind(null, p, r.getNumberFormat),
                        formatRelativeTime: u.formatRelativeTime.bind(null, p, r.getRelativeTimeFormat),
                        formatDate: s.formatDate.bind(null, p, r.getDateTimeFormat),
                        formatDateToParts: s.formatDateToParts.bind(null, p, r.getDateTimeFormat),
                        formatTime: s.formatTime.bind(null, p, r.getDateTimeFormat),
                        formatDateTimeRange: s.formatDateTimeRange.bind(null, p, r.getDateTimeFormat),
                        formatTimeToParts: s.formatTimeToParts.bind(null, p, r.getDateTimeFormat),
                        formatPlural: l.formatPlural.bind(null, p, r.getPluralRules),
                        formatMessage: c.formatMessage.bind(null, p, r),
                        $t: c.formatMessage.bind(null, p, r),
                        formatList: m.formatList.bind(null, p, r.getListFormat),
                        formatListToParts: m.formatListToParts.bind(null, p, r.getListFormat),
                        formatDisplayName: f.formatDisplayName.bind(null, p, r.getDisplayNames)
                    })
            }
        },
        58075: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatTimeToParts = t.formatDateToParts = t.formatDateTimeRange = t.formatTime = t.formatDate = t.getFormatter = void 0;
            var n = r(22970),
                a = r(53918),
                i = r(53775),
                o = ["formatMatcher", "timeZone", "hour12", "weekday", "era", "year", "month", "day", "hour", "minute", "second", "timeZoneName", "hourCycle", "dateStyle", "timeStyle", "calendar", "numberingSystem", "fractionalSecondDigits"];

            function u(e, t, r, i) {
                var u = e.locale,
                    s = e.formats,
                    l = e.onError,
                    c = e.timeZone;
                void 0 === i && (i = {});
                var m = i.format,
                    f = n.__assign(n.__assign({}, c && {
                        timeZone: c
                    }), m && (0, a.getNamedFormat)(s, t, m, l)),
                    p = (0, a.filterProps)(i, o, f);
                return "time" !== t || p.hour || p.minute || p.second || p.timeStyle || p.dateStyle || (p = n.__assign(n.__assign({}, p), {
                    hour: "numeric",
                    minute: "numeric"
                })), r(u, p)
            }
            t.getFormatter = u, t.formatDate = function(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                var a = r[0],
                    o = r[1],
                    s = void 0 === o ? {} : o,
                    l = "string" == typeof a ? new Date(a || 0) : a;
                try {
                    return u(e, "date", t, s).format(l)
                } catch (t) {
                    e.onError(new i.IntlFormatError("Error formatting date.", e.locale, t))
                }
                return String(l)
            }, t.formatTime = function(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                var a = r[0],
                    o = r[1],
                    s = void 0 === o ? {} : o,
                    l = "string" == typeof a ? new Date(a || 0) : a;
                try {
                    return u(e, "time", t, s).format(l)
                } catch (t) {
                    e.onError(new i.IntlFormatError("Error formatting time.", e.locale, t))
                }
                return String(l)
            }, t.formatDateTimeRange = function(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                var u = r[0],
                    s = r[1],
                    l = r[2],
                    c = void 0 === l ? {} : l,
                    m = e.timeZone,
                    f = e.locale,
                    p = e.onError,
                    d = (0, a.filterProps)(c, o, m ? {
                        timeZone: m
                    } : {});
                try {
                    return t(f, d).formatRange(u, s)
                } catch (t) {
                    p(new i.IntlFormatError("Error formatting date time range.", e.locale, t))
                }
                return String(u)
            }, t.formatDateToParts = function(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                var a = r[0],
                    o = r[1],
                    s = void 0 === o ? {} : o,
                    l = "string" == typeof a ? new Date(a || 0) : a;
                try {
                    return u(e, "date", t, s).formatToParts(l)
                } catch (t) {
                    e.onError(new i.IntlFormatError("Error formatting date.", e.locale, t))
                }
                return []
            }, t.formatTimeToParts = function(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                var a = r[0],
                    o = r[1],
                    s = void 0 === o ? {} : o,
                    l = "string" == typeof a ? new Date(a || 0) : a;
                try {
                    return u(e, "time", t, s).formatToParts(l)
                } catch (t) {
                    e.onError(new i.IntlFormatError("Error formatting time.", e.locale, t))
                }
                return []
            }
        },
        68739: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatDisplayName = void 0;
            var n = r(53918),
                a = r(37238),
                i = r(53775),
                o = ["style", "type", "fallback", "languageDisplay"];
            t.formatDisplayName = function(e, t, r, u) {
                var s = e.locale,
                    l = e.onError;
                Intl.DisplayNames || l(new a.FormatError('Intl.DisplayNames is not available in this environment.\nTry polyfilling it using "@formatjs/intl-displaynames"\n', a.ErrorCode.MISSING_INTL_API));
                var c = (0, n.filterProps)(u, o);
                try {
                    return t(s, c).of(r)
                } catch (e) {
                    l(new i.IntlFormatError("Error formatting display name.", s, e))
                }
            }
        },
        53775: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.MissingTranslationError = t.MessageFormatError = t.IntlFormatError = t.MissingDataError = t.InvalidConfigError = t.UnsupportedFormatterError = t.IntlError = t.IntlErrorCode = void 0;
            var n, a = r(22970);
            ! function(e) {
                e.FORMAT_ERROR = "FORMAT_ERROR", e.UNSUPPORTED_FORMATTER = "UNSUPPORTED_FORMATTER", e.INVALID_CONFIG = "INVALID_CONFIG", e.MISSING_DATA = "MISSING_DATA", e.MISSING_TRANSLATION = "MISSING_TRANSLATION"
            }(n = t.IntlErrorCode || (t.IntlErrorCode = {}));
            var i = function(e) {
                function t(r, n, a) {
                    var i = this,
                        o = a ? a instanceof Error ? a : new Error(String(a)) : void 0;
                    return (i = e.call(this, "[@formatjs/intl Error ".concat(r, "] ").concat(n, "\n").concat(o ? "\n".concat(o.message, "\n").concat(o.stack) : "")) || this).code = r, "function" == typeof Error.captureStackTrace && Error.captureStackTrace(i, t), i
                }
                return a.__extends(t, e), t
            }(Error);
            t.IntlError = i;
            var o = function(e) {
                function t(t, r) {
                    return e.call(this, n.UNSUPPORTED_FORMATTER, t, r) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.UnsupportedFormatterError = o;
            var u = function(e) {
                function t(t, r) {
                    return e.call(this, n.INVALID_CONFIG, t, r) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.InvalidConfigError = u;
            var s = function(e) {
                function t(t, r) {
                    return e.call(this, n.MISSING_DATA, t, r) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.MissingDataError = s;
            var l = function(e) {
                function t(t, r, a) {
                    var i = e.call(this, n.FORMAT_ERROR, "".concat(t, "\nLocale: ").concat(r, "\n"), a) || this;
                    return i.locale = r, i
                }
                return a.__extends(t, e), t
            }(i);
            t.IntlFormatError = l;
            var c = function(e) {
                function t(t, r, n, a) {
                    var i = e.call(this, "".concat(t, "\nMessageID: ").concat(null == n ? void 0 : n.id, "\nDefault Message: ").concat(null == n ? void 0 : n.defaultMessage, "\nDescription: ").concat(null == n ? void 0 : n.description, "\n"), r, a) || this;
                    return i.descriptor = n, i.locale = r, i
                }
                return a.__extends(t, e), t
            }(l);
            t.MessageFormatError = c;
            var m = function(e) {
                function t(t, r) {
                    var a = e.call(this, n.MISSING_TRANSLATION, 'Missing message: "'.concat(t.id, '" for locale "').concat(r, '", using ').concat(t.defaultMessage ? "default message (".concat("string" == typeof t.defaultMessage ? t.defaultMessage : t.defaultMessage.map((function(e) {
                        var t;
                        return null !== (t = e.value) && void 0 !== t ? t : JSON.stringify(e)
                    })).join(), ")") : "id", " as fallback.")) || this;
                    return a.descriptor = t, a
                }
                return a.__extends(t, e), t
            }(i);
            t.MissingTranslationError = m
        },
        56739: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatListToParts = t.formatList = void 0;
            var n = r(22970),
                a = r(53918),
                i = r(37238),
                o = r(53775),
                u = ["type", "style"],
                s = Date.now();

            function l(e, t, r, l) {
                var c = e.locale,
                    m = e.onError;
                void 0 === l && (l = {}), Intl.ListFormat || m(new i.FormatError('Intl.ListFormat is not available in this environment.\nTry polyfilling it using "@formatjs/intl-listformat"\n', i.ErrorCode.MISSING_INTL_API));
                var f = (0, a.filterProps)(l, u);
                try {
                    var p = {},
                        d = r.map((function(e, t) {
                            if ("object" == typeof e) {
                                var r = function(e) {
                                    return "".concat(s, "_").concat(e, "_").concat(s)
                                }(t);
                                return p[r] = e, r
                            }
                            return String(e)
                        }));
                    return t(c, f).formatToParts(d).map((function(e) {
                        return "literal" === e.type ? e : n.__assign(n.__assign({}, e), {
                            value: p[e.value] || e.value
                        })
                    }))
                } catch (e) {
                    m(new o.IntlFormatError("Error formatting list.", c, e))
                }
                return r
            }
            t.formatList = function(e, t, r, n) {
                void 0 === n && (n = {});
                var a = l(e, t, r, n).reduce((function(e, t) {
                    var r = t.value;
                    return "string" != typeof r ? e.push(r) : "string" == typeof e[e.length - 1] ? e[e.length - 1] += r : e.push(r), e
                }), []);
                return 1 === a.length ? a[0] : 0 === a.length ? "" : a
            }, t.formatListToParts = l
        },
        83864: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatMessage = void 0;
            var n = r(22970),
                a = r(15462),
                i = r(37238),
                o = r(53775),
                u = r(46057);

            function s(e, t) {
                return Object.keys(e).reduce((function(r, a) {
                    return r[a] = n.__assign({
                        timeZone: t
                    }, e[a]), r
                }), {})
            }

            function l(e, t) {
                return Object.keys(n.__assign(n.__assign({}, e), t)).reduce((function(r, a) {
                    return r[a] = n.__assign(n.__assign({}, e[a] || {}), t[a] || {}), r
                }), {})
            }

            function c(e, t) {
                if (!t) return e;
                var r = i.IntlMessageFormat.formats;
                return n.__assign(n.__assign(n.__assign({}, r), e), {
                    date: l(s(r.date, t), s(e.date || {}, t)),
                    time: l(s(r.time, t), s(e.time || {}, t))
                })
            }
            t.formatMessage = function(e, t, r, i, s) {
                var l = e.locale,
                    m = e.formats,
                    f = e.messages,
                    p = e.defaultLocale,
                    d = e.defaultFormats,
                    h = e.fallbackOnEmptyString,
                    g = e.onError,
                    v = e.timeZone,
                    y = e.defaultRichTextElements;
                void 0 === r && (r = {
                    id: ""
                });
                var E = r.id,
                    b = r.defaultMessage;
                (0, a.invariant)(!!E, "[@formatjs/intl] An `id` must be provided to format a message. You can either:\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\nto autofix this issue");
                var _ = String(E),
                    D = f && Object.prototype.hasOwnProperty.call(f, _) && f[_];
                if (Array.isArray(D) && 1 === D.length && D[0].type === u.TYPE.literal) return D[0].value;
                if (!i && D && "string" == typeof D && !y) return D.replace(/'\{(.*?)\}'/gi, "{$1}");
                if (i = n.__assign(n.__assign({}, y), i || {}), m = c(m, v), d = c(d, v), !D) {
                    if (!1 === h && "" === D) return D;
                    if ((!b || l && l.toLowerCase() !== p.toLowerCase()) && g(new o.MissingTranslationError(r, l)), b) try {
                        return t.getMessageFormat(b, p, d, s).format(i)
                    } catch (e) {
                        return g(new o.MessageFormatError('Error formatting default message for: "'.concat(_, '", rendering default message verbatim'), l, r, e)), "string" == typeof b ? b : _
                    }
                    return _
                }
                try {
                    return t.getMessageFormat(D, l, m, n.__assign({
                        formatters: t
                    }, s || {})).format(i)
                } catch (e) {
                    g(new o.MessageFormatError('Error formatting message: "'.concat(_, '", using ').concat(b ? "default message" : "id", " as fallback."), l, r, e))
                }
                if (b) try {
                    return t.getMessageFormat(b, p, d, s).format(i)
                } catch (e) {
                    g(new o.MessageFormatError('Error formatting the default message for: "'.concat(_, '", rendering message verbatim'), l, r, e))
                }
                return "string" == typeof D ? D : "string" == typeof b ? b : _
            }
        },
        19028: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatNumberToParts = t.formatNumber = t.getFormatter = void 0;
            var n = r(53918),
                a = r(53775),
                i = ["style", "currency", "currencyDisplay", "unit", "unitDisplay", "useGrouping", "minimumIntegerDigits", "minimumFractionDigits", "maximumFractionDigits", "minimumSignificantDigits", "maximumSignificantDigits", "compactDisplay", "currencyDisplay", "currencySign", "notation", "signDisplay", "unit", "unitDisplay", "numberingSystem"];

            function o(e, t, r) {
                var a = e.locale,
                    o = e.formats,
                    u = e.onError;
                void 0 === r && (r = {});
                var s = r.format,
                    l = s && (0, n.getNamedFormat)(o, "number", s, u) || {};
                return t(a, (0, n.filterProps)(r, i, l))
            }
            t.getFormatter = o, t.formatNumber = function(e, t, r, n) {
                void 0 === n && (n = {});
                try {
                    return o(e, t, n).format(r)
                } catch (t) {
                    e.onError(new a.IntlFormatError("Error formatting number.", e.locale, t))
                }
                return String(r)
            }, t.formatNumberToParts = function(e, t, r, n) {
                void 0 === n && (n = {});
                try {
                    return o(e, t, n).formatToParts(r)
                } catch (t) {
                    e.onError(new a.IntlFormatError("Error formatting number.", e.locale, t))
                }
                return []
            }
        },
        10298: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatPlural = void 0;
            var n = r(53918),
                a = r(53775),
                i = r(37238),
                o = ["type"];
            t.formatPlural = function(e, t, r, u) {
                var s = e.locale,
                    l = e.onError;
                void 0 === u && (u = {}), Intl.PluralRules || l(new i.FormatError('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n', i.ErrorCode.MISSING_INTL_API));
                var c = (0, n.filterProps)(u, o);
                try {
                    return t(s, c).select(r)
                } catch (e) {
                    l(new a.IntlFormatError("Error formatting plural.", s, e))
                }
                return "other"
            }
        },
        79556: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatRelativeTime = void 0;
            var n = r(53918),
                a = r(37238),
                i = r(53775),
                o = ["numeric", "style"];
            t.formatRelativeTime = function(e, t, r, u, s) {
                void 0 === s && (s = {}), u || (u = "second"), Intl.RelativeTimeFormat || e.onError(new a.FormatError('Intl.RelativeTimeFormat is not available in this environment.\nTry polyfilling it using "@formatjs/intl-relativetimeformat"\n', a.ErrorCode.MISSING_INTL_API));
                try {
                    return function(e, t, r) {
                        var a = e.locale,
                            i = e.formats,
                            u = e.onError;
                        void 0 === r && (r = {});
                        var s = r.format,
                            l = !!s && (0, n.getNamedFormat)(i, "relative", s, u) || {};
                        return t(a, (0, n.filterProps)(r, o, l))
                    }(e, t, s).format(r, u)
                } catch (t) {
                    e.onError(new i.IntlFormatError("Error formatting relative time.", e.locale, t))
                }
                return String(r)
            }
        },
        46561: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        53918: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.getNamedFormat = t.createFormatters = t.createIntlCache = t.DEFAULT_INTL_CONFIG = t.filterProps = void 0;
            var n = r(22970),
                a = r(37238),
                i = r(47613),
                o = r(53775);

            function u(e) {
                return {
                    create: function() {
                        return {
                            get: function(t) {
                                return e[t]
                            },
                            set: function(t, r) {
                                e[t] = r
                            }
                        }
                    }
                }
            }
            t.filterProps = function(e, t, r) {
                return void 0 === r && (r = {}), t.reduce((function(t, n) {
                    return n in e ? t[n] = e[n] : n in r && (t[n] = r[n]), t
                }), {})
            }, t.DEFAULT_INTL_CONFIG = {
                formats: {},
                messages: {},
                timeZone: void 0,
                defaultLocale: "en",
                defaultFormats: {},
                fallbackOnEmptyString: !0,
                onError: function(e) {},
                onWarn: function(e) {}
            }, t.createIntlCache = function() {
                return {
                    dateTime: {},
                    number: {},
                    message: {},
                    relativeTime: {},
                    pluralRules: {},
                    list: {},
                    displayNames: {}
                }
            }, t.createFormatters = function(e) {
                void 0 === e && (e = {
                    dateTime: {},
                    number: {},
                    message: {},
                    relativeTime: {},
                    pluralRules: {},
                    list: {},
                    displayNames: {}
                });
                var t = Intl.RelativeTimeFormat,
                    r = Intl.ListFormat,
                    o = Intl.DisplayNames,
                    s = (0, i.memoize)((function() {
                        for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                        return new((e = Intl.DateTimeFormat).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                    }), {
                        cache: u(e.dateTime),
                        strategy: i.strategies.variadic
                    }),
                    l = (0, i.memoize)((function() {
                        for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                        return new((e = Intl.NumberFormat).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                    }), {
                        cache: u(e.number),
                        strategy: i.strategies.variadic
                    }),
                    c = (0, i.memoize)((function() {
                        for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                        return new((e = Intl.PluralRules).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                    }), {
                        cache: u(e.pluralRules),
                        strategy: i.strategies.variadic
                    });
                return {
                    getDateTimeFormat: s,
                    getNumberFormat: l,
                    getMessageFormat: (0, i.memoize)((function(e, t, r, i) {
                        return new a.IntlMessageFormat(e, t, r, n.__assign({
                            formatters: {
                                getNumberFormat: l,
                                getDateTimeFormat: s,
                                getPluralRules: c
                            }
                        }, i || {}))
                    }), {
                        cache: u(e.message),
                        strategy: i.strategies.variadic
                    }),
                    getRelativeTimeFormat: (0, i.memoize)((function() {
                        for (var e = [], r = 0; r < arguments.length; r++) e[r] = arguments[r];
                        return new(t.bind.apply(t, n.__spreadArray([void 0], e, !1)))
                    }), {
                        cache: u(e.relativeTime),
                        strategy: i.strategies.variadic
                    }),
                    getPluralRules: c,
                    getListFormat: (0, i.memoize)((function() {
                        for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                        return new(r.bind.apply(r, n.__spreadArray([void 0], e, !1)))
                    }), {
                        cache: u(e.list),
                        strategy: i.strategies.variadic
                    }),
                    getDisplayNames: (0, i.memoize)((function() {
                        for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                        return new(o.bind.apply(o, n.__spreadArray([void 0], e, !1)))
                    }), {
                        cache: u(e.displayNames),
                        strategy: i.strategies.variadic
                    })
                }
            }, t.getNamedFormat = function(e, t, r, n) {
                var a, i = e && e[t];
                if (i && (a = i[r]), a) return a;
                n(new o.UnsupportedFormatterError("No ".concat(t, " format named: ").concat(r)))
            }
        },
        33162: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormattedListParts = t.FormattedNumberParts = t.FormattedTimeParts = t.FormattedDateParts = t.FormattedDisplayName = t.FormattedList = t.FormattedNumber = t.FormattedTime = t.FormattedDate = t.defineMessage = t.defineMessages = t.ReactIntlError = t.ReactIntlErrorCode = t.MissingTranslationError = t.MessageFormatError = t.MissingDataError = t.InvalidConfigError = t.UnsupportedFormatterError = t.createIntlCache = t.useIntl = t.injectIntl = t.createIntl = t.RawIntlProvider = t.IntlProvider = t.IntlContext = t.FormattedRelativeTime = t.FormattedPlural = t.FormattedMessage = t.FormattedDateTimeRange = void 0;
            var n = r(22970),
                a = r(71353),
                i = n.__importStar(r(16714));
            t.injectIntl = i.default, Object.defineProperty(t, "RawIntlProvider", {
                enumerable: !0,
                get: function() {
                    return i.Provider
                }
            }), Object.defineProperty(t, "IntlContext", {
                enumerable: !0,
                get: function() {
                    return i.Context
                }
            });
            var o = n.__importDefault(r(55325));
            t.useIntl = o.default;
            var u = n.__importStar(r(16660));
            t.IntlProvider = u.default, Object.defineProperty(t, "createIntl", {
                enumerable: !0,
                get: function() {
                    return u.createIntl
                }
            });
            var s = n.__importDefault(r(76979));
            t.FormattedRelativeTime = s.default;
            var l = n.__importDefault(r(58647));
            t.FormattedPlural = l.default;
            var c = n.__importDefault(r(78428));
            t.FormattedMessage = c.default;
            var m = n.__importDefault(r(7886));
            t.FormattedDateTimeRange = m.default;
            var f = r(81007);
            Object.defineProperty(t, "createIntlCache", {
                enumerable: !0,
                get: function() {
                    return f.createIntlCache
                }
            }), Object.defineProperty(t, "UnsupportedFormatterError", {
                enumerable: !0,
                get: function() {
                    return f.UnsupportedFormatterError
                }
            }), Object.defineProperty(t, "InvalidConfigError", {
                enumerable: !0,
                get: function() {
                    return f.InvalidConfigError
                }
            }), Object.defineProperty(t, "MissingDataError", {
                enumerable: !0,
                get: function() {
                    return f.MissingDataError
                }
            }), Object.defineProperty(t, "MessageFormatError", {
                enumerable: !0,
                get: function() {
                    return f.MessageFormatError
                }
            }), Object.defineProperty(t, "MissingTranslationError", {
                enumerable: !0,
                get: function() {
                    return f.MissingTranslationError
                }
            }), Object.defineProperty(t, "ReactIntlErrorCode", {
                enumerable: !0,
                get: function() {
                    return f.IntlErrorCode
                }
            }), Object.defineProperty(t, "ReactIntlError", {
                enumerable: !0,
                get: function() {
                    return f.IntlError
                }
            }), t.defineMessages = function(e) {
                return e
            }, t.defineMessage = function(e) {
                return e
            }, t.FormattedDate = (0, a.createFormattedComponent)("formatDate"), t.FormattedTime = (0, a.createFormattedComponent)("formatTime"), t.FormattedNumber = (0, a.createFormattedComponent)("formatNumber"), t.FormattedList = (0, a.createFormattedComponent)("formatList"), t.FormattedDisplayName = (0, a.createFormattedComponent)("formatDisplayName"), t.FormattedDateParts = (0, a.createFormattedDateTimePartsComponent)("formatDate"), t.FormattedTimeParts = (0, a.createFormattedDateTimePartsComponent)("formatTime");
            var p = r(71353);
            Object.defineProperty(t, "FormattedNumberParts", {
                enumerable: !0,
                get: function() {
                    return p.FormattedNumberParts
                }
            }), Object.defineProperty(t, "FormattedListParts", {
                enumerable: !0,
                get: function() {
                    return p.FormattedListParts
                }
            })
        },
        53266: (e, t) => {
            function r(e) {
                if (void 0 === e) return NaN;
                if (null === e) return 0;
                if ("boolean" == typeof e) return e ? 1 : 0;
                if ("number" == typeof e) return e;
                if ("symbol" == typeof e || "bigint" == typeof e) throw new TypeError("Cannot convert symbol/bigint to number");
                return Number(e)
            }

            function n(e, t) {
                return Object.is ? Object.is(e, t) : e === t ? 0 !== e || 1 / e == 1 / t : e != e && t != t
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.msFromTime = t.OrdinaryHasInstance = t.SecFromTime = t.MinFromTime = t.HourFromTime = t.DateFromTime = t.MonthFromTime = t.InLeapYear = t.DayWithinYear = t.DaysInYear = t.YearFromTime = t.TimeFromYear = t.DayFromYear = t.WeekDay = t.Day = t.Type = t.HasOwnProperty = t.ArrayCreate = t.SameValue = t.ToObject = t.TimeClip = t.ToNumber = t.ToString = void 0, t.ToString = function(e) {
                if ("symbol" == typeof e) throw TypeError("Cannot convert a Symbol value to a string");
                return String(e)
            }, t.ToNumber = r, t.TimeClip = function(e) {
                return isFinite(e) ? Math.abs(e) > 8640000000000001 ? NaN : function(e) {
                    var t = r(e);
                    if (isNaN(t) || n(t, -0)) return 0;
                    if (isFinite(t)) return t;
                    var a = Math.floor(Math.abs(t));
                    return t < 0 && (a = -a), n(a, -0) ? 0 : a
                }(e) : NaN
            }, t.ToObject = function(e) {
                if (null == e) throw new TypeError("undefined/null cannot be converted to object");
                return Object(e)
            }, t.SameValue = n, t.ArrayCreate = function(e) {
                return new Array(e)
            }, t.HasOwnProperty = function(e, t) {
                return Object.prototype.hasOwnProperty.call(e, t)
            }, t.Type = function(e) {
                return null === e ? "Null" : void 0 === e ? "Undefined" : "function" == typeof e || "object" == typeof e ? "Object" : "number" == typeof e ? "Number" : "boolean" == typeof e ? "Boolean" : "string" == typeof e ? "String" : "symbol" == typeof e ? "Symbol" : "bigint" == typeof e ? "BigInt" : void 0
            };
            var a = 864e5;

            function i(e, t) {
                return e - Math.floor(e / t) * t
            }

            function o(e) {
                return Math.floor(e / a)
            }

            function u(e) {
                return Date.UTC(e, 0) / a
            }

            function s(e) {
                return new Date(e).getUTCFullYear()
            }

            function l(e) {
                return e % 4 != 0 ? 365 : e % 100 != 0 ? 366 : e % 400 != 0 ? 365 : 366
            }

            function c(e) {
                return o(e) - u(s(e))
            }

            function m(e) {
                return 365 === l(s(e)) ? 0 : 1
            }

            function f(e) {
                var t = c(e),
                    r = m(e);
                if (t >= 0 && t < 31) return 0;
                if (t < 59 + r) return 1;
                if (t < 90 + r) return 2;
                if (t < 120 + r) return 3;
                if (t < 151 + r) return 4;
                if (t < 181 + r) return 5;
                if (t < 212 + r) return 6;
                if (t < 243 + r) return 7;
                if (t < 273 + r) return 8;
                if (t < 304 + r) return 9;
                if (t < 334 + r) return 10;
                if (t < 365 + r) return 11;
                throw new Error("Invalid time")
            }
            t.Day = o, t.WeekDay = function(e) {
                return i(o(e) + 4, 7)
            }, t.DayFromYear = u, t.TimeFromYear = function(e) {
                return Date.UTC(e, 0)
            }, t.YearFromTime = s, t.DaysInYear = l, t.DayWithinYear = c, t.InLeapYear = m, t.MonthFromTime = f, t.DateFromTime = function(e) {
                var t = c(e),
                    r = f(e),
                    n = m(e);
                if (0 === r) return t + 1;
                if (1 === r) return t - 30;
                if (2 === r) return t - 58 - n;
                if (3 === r) return t - 89 - n;
                if (4 === r) return t - 119 - n;
                if (5 === r) return t - 150 - n;
                if (6 === r) return t - 180 - n;
                if (7 === r) return t - 211 - n;
                if (8 === r) return t - 242 - n;
                if (9 === r) return t - 272 - n;
                if (10 === r) return t - 303 - n;
                if (11 === r) return t - 333 - n;
                throw new Error("Invalid time")
            };
            t.HourFromTime = function(e) {
                return i(Math.floor(e / 36e5), 24)
            }, t.MinFromTime = function(e) {
                return i(Math.floor(e / 6e4), 60)
            }, t.SecFromTime = function(e) {
                return i(Math.floor(e / 1e3), 60)
            }, t.OrdinaryHasInstance = function(e, t, r) {
                if ("function" != typeof e) return !1;
                if (null == r ? void 0 : r.boundTargetFunction) return t instanceof(null == r ? void 0 : r.boundTargetFunction);
                if ("object" != typeof t) return !1;
                var n = e.prototype;
                if ("object" != typeof n) throw new TypeError("OrdinaryHasInstance called on an object with an invalid prototype property.");
                return Object.prototype.isPrototypeOf.call(n, t)
            }, t.msFromTime = function(e) {
                return i(e, 1e3)
            }
        },
        27644: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CanonicalizeLocaleList = void 0, t.CanonicalizeLocaleList = function(e) {
                return Intl.getCanonicalLocales(e)
            }
        },
        54669: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CanonicalizeTimeZoneName = void 0, t.CanonicalizeTimeZoneName = function(e, t) {
                var r = t.tzData,
                    n = t.uppercaseLinks,
                    a = e.toUpperCase(),
                    i = Object.keys(r).reduce((function(e, t) {
                        return e[t.toUpperCase()] = t, e
                    }), {}),
                    o = n[a] || i[a];
                return "Etc/UTC" === o || "Etc/GMT" === o ? "UTC" : o
            }
        },
        1363: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CoerceOptionsToObject = void 0;
            var n = r(53266);
            t.CoerceOptionsToObject = function(e) {
                return void 0 === e ? Object.create(null) : (0, n.ToObject)(e)
            }
        },
        23090: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.DefaultNumberOption = void 0, t.DefaultNumberOption = function(e, t, r, n) {
                if (void 0 !== e) {
                    if (e = Number(e), isNaN(e) || e < t || e > r) throw new RangeError("".concat(e, " is outside of range [").concat(t, ", ").concat(r, "]"));
                    return Math.floor(e)
                }
                return n
            }
        },
        54920: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetNumberOption = void 0;
            var n = r(23090);
            t.GetNumberOption = function(e, t, r, a, i) {
                var o = e[t];
                return (0, n.DefaultNumberOption)(o, r, a, i)
            }
        },
        73156: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetOption = void 0;
            var n = r(53266);
            t.GetOption = function(e, t, r, a, i) {
                if ("object" != typeof e) throw new TypeError("Options must be an object");
                var o = e[t];
                if (void 0 !== o) {
                    if ("boolean" !== r && "string" !== r) throw new TypeError("invalid type");
                    if ("boolean" === r && (o = Boolean(o)), "string" === r && (o = (0, n.ToString)(o)), void 0 !== a && !a.filter((function(e) {
                            return e == o
                        })).length) throw new RangeError("".concat(o, " is not within ").concat(a.join(", ")));
                    return o
                }
                return i
            }
        },
        73926: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetOptionsObject = void 0, t.GetOptionsObject = function(e) {
                if (void 0 === e) return Object.create(null);
                if ("object" == typeof e) return e;
                throw new TypeError("Options must be an object")
            }
        },
        29011: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetStringOrBooleanOption = void 0;
            var n = r(53266);
            t.GetStringOrBooleanOption = function(e, t, r, a, i, o) {
                var u = e[t];
                if (void 0 === u) return o;
                if (!0 === u) return a;
                if (!1 === Boolean(u)) return i;
                if ("true" === (u = (0, n.ToString)(u)) || "false" === u) return o;
                if (-1 === (r || []).indexOf(u)) throw new RangeError("Invalid value ".concat(u));
                return u
            }
        },
        44405: (e, t) => {
            function r(e) {
                return e.slice(e.indexOf("-") + 1)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IsSanctionedSimpleUnitIdentifier = t.SIMPLE_UNITS = t.removeUnitNamespace = t.SANCTIONED_UNITS = void 0, t.SANCTIONED_UNITS = ["angle-degree", "area-acre", "area-hectare", "concentr-percent", "digital-bit", "digital-byte", "digital-gigabit", "digital-gigabyte", "digital-kilobit", "digital-kilobyte", "digital-megabit", "digital-megabyte", "digital-petabyte", "digital-terabit", "digital-terabyte", "duration-day", "duration-hour", "duration-millisecond", "duration-minute", "duration-month", "duration-second", "duration-week", "duration-year", "length-centimeter", "length-foot", "length-inch", "length-kilometer", "length-meter", "length-mile-scandinavian", "length-mile", "length-millimeter", "length-yard", "mass-gram", "mass-kilogram", "mass-ounce", "mass-pound", "mass-stone", "temperature-celsius", "temperature-fahrenheit", "volume-fluid-ounce", "volume-gallon", "volume-liter", "volume-milliliter"], t.removeUnitNamespace = r, t.SIMPLE_UNITS = t.SANCTIONED_UNITS.map(r), t.IsSanctionedSimpleUnitIdentifier = function(e) {
                return t.SIMPLE_UNITS.indexOf(e) > -1
            }
        },
        73991: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IsValidTimeZoneName = void 0, t.IsValidTimeZoneName = function(e, t) {
                var r = t.tzData,
                    n = t.uppercaseLinks,
                    a = e.toUpperCase(),
                    i = new Set,
                    o = new Set;
                return Object.keys(r).map((function(e) {
                    return e.toUpperCase()
                })).forEach((function(e) {
                    return i.add(e)
                })), Object.keys(n).forEach((function(e) {
                    o.add(e.toUpperCase()), i.add(n[e].toUpperCase())
                })), i.has(a) || o.has(a)
            }
        },
        36878: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IsWellFormedCurrencyCode = void 0;
            var r = /[^A-Z]/;
            t.IsWellFormedCurrencyCode = function(e) {
                return 3 === (e = e.replace(/([a-z])/g, (function(e, t) {
                    return t.toUpperCase()
                }))).length && !r.test(e)
            }
        },
        37998: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IsWellFormedUnitIdentifier = void 0;
            var n = r(44405);
            t.IsWellFormedUnitIdentifier = function(e) {
                if (e = e.replace(/([A-Z])/g, (function(e, t) {
                        return t.toLowerCase()
                    })), (0, n.IsSanctionedSimpleUnitIdentifier)(e)) return !0;
                var t = e.split("-per-");
                if (2 !== t.length) return !1;
                var r = t[0],
                    a = t[1];
                return !(!(0, n.IsSanctionedSimpleUnitIdentifier)(r) || !(0, n.IsSanctionedSimpleUnitIdentifier)(a))
            }
        },
        51417: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ApplyUnsignedRoundingMode = void 0, t.ApplyUnsignedRoundingMode = function(e, t, r, n) {
                if (e === t) return t;
                if (void 0 === n) throw new Error("unsignedRoundingMode is mandatory");
                if ("zero" === n) return t;
                if ("infinity" === n) return r;
                var a = e - t,
                    i = r - e;
                if (a < i) return t;
                if (i < a) return r;
                if (a !== i) throw new Error("Unexpected error");
                if ("half-zero" === n) return t;
                if ("half-infinity" === n) return r;
                if ("half-even" !== n) throw new Error("Unexpected value for unsignedRoundingMode: ".concat(n));
                return 0 == t / (r - t) % 2 ? t : r
            }
        },
        89791: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CollapseNumberRange = void 0, t.CollapseNumberRange = function(e) {
                return e
            }
        },
        92271: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ComputeExponent = void 0;
            var n = r(78939),
                a = r(37208),
                i = r(33135);
            t.ComputeExponent = function(e, t, r) {
                var o = r.getInternalSlots;
                if (0 === t) return [0, 0];
                t < 0 && (t = -t);
                var u = (0, n.getMagnitude)(t),
                    s = (0, a.ComputeExponentForMagnitude)(e, u, {
                        getInternalSlots: o
                    });
                t = s < 0 ? t * Math.pow(10, -s) : t / Math.pow(10, s);
                var l = (0, i.FormatNumericToString)(o(e), t);
                return 0 === l.roundedNumber || (0, n.getMagnitude)(l.roundedNumber) === u - s ? [s, u] : [(0, a.ComputeExponentForMagnitude)(e, u + 1, {
                    getInternalSlots: o
                }), u + 1]
            }
        },
        37208: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ComputeExponentForMagnitude = void 0, t.ComputeExponentForMagnitude = function(e, t, r) {
                var n = (0, r.getInternalSlots)(e),
                    a = n.notation,
                    i = n.dataLocaleData,
                    o = n.numberingSystem;
                switch (a) {
                    case "standard":
                        return 0;
                    case "scientific":
                        return t;
                    case "engineering":
                        return 3 * Math.floor(t / 3);
                    default:
                        var u = n.compactDisplay,
                            s = n.style,
                            l = n.currencyDisplay,
                            c = void 0;
                        if ("currency" === s && "name" !== l) c = (i.numbers.currency[o] || i.numbers.currency[i.numbers.nu[0]]).short;
                        else {
                            var m = i.numbers.decimal[o] || i.numbers.decimal[i.numbers.nu[0]];
                            c = "long" === u ? m.long : m.short
                        }
                        if (!c) return 0;
                        var f = String(Math.pow(10, t)),
                            p = Object.keys(c);
                        if (f < p[0]) return 0;
                        if (f > p[p.length - 1]) return p[p.length - 1].length - 1;
                        var d = p.indexOf(f);
                        if (-1 === d) return 0;
                        var h = p[d];
                        return "0" === c[h].other ? 0 : h.length - c[h].other.match(/0+/)[0].length
                }
            }
        },
        70893: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CurrencyDigits = void 0;
            var n = r(53266);
            t.CurrencyDigits = function(e, t) {
                var r = t.currencyDigitsData;
                return (0, n.HasOwnProperty)(r, e) ? r[e] : 2
            }
        },
        47485: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatApproximately = void 0, t.FormatApproximately = function(e, t, r) {
                var n = (0, r.getInternalSlots)(e),
                    a = n.dataLocaleData.numbers.symbols[n.numberingSystem].approximatelySign;
                return t.push({
                    type: "approximatelySign",
                    value: a
                }), t
            }
        },
        633: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatNumericRange = void 0;
            var n = r(63775);
            t.FormatNumericRange = function(e, t, r, a) {
                var i = a.getInternalSlots;
                return (0, n.PartitionNumberRangePattern)(e, t, r, {
                    getInternalSlots: i
                }).map((function(e) {
                    return e.value
                })).join("")
            }
        },
        63415: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatNumericRangeToParts = void 0;
            var n = r(63775);
            t.FormatNumericRangeToParts = function(e, t, r, a) {
                var i = a.getInternalSlots;
                return (0, n.PartitionNumberRangePattern)(e, t, r, {
                    getInternalSlots: i
                }).map((function(e, t) {
                    return {
                        type: e.type,
                        value: e.value,
                        source: e.source,
                        result: t.toString()
                    }
                }))
            }
        },
        17680: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatNumericToParts = void 0;
            var n = r(38709),
                a = r(53266);
            t.FormatNumericToParts = function(e, t, r) {
                for (var i = (0, n.PartitionNumberPattern)(e, t, r), o = (0, a.ArrayCreate)(0), u = 0, s = i; u < s.length; u++) {
                    var l = s[u];
                    o.push({
                        type: l.type,
                        value: l.value
                    })
                }
                return o
            }
        },
        33135: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.FormatNumericToString = void 0;
            var n = r(53266),
                a = r(27203),
                i = r(78939),
                o = r(83784);
            t.FormatNumericToString = function(e, t) {
                var r, u = t < 0 || (0, n.SameValue)(t, -0);
                switch (u && (t = -t), e.roundingType) {
                    case "significantDigits":
                        r = (0, a.ToRawPrecision)(t, e.minimumSignificantDigits, e.maximumSignificantDigits);
                        break;
                    case "fractionDigits":
                        r = (0, o.ToRawFixed)(t, e.minimumFractionDigits, e.maximumFractionDigits);
                        break;
                    default:
                        (r = (0, a.ToRawPrecision)(t, 1, 2)).integerDigitsCount > 1 && (r = (0, o.ToRawFixed)(t, 0, 0))
                }
                t = r.roundedNumber;
                var s = r.formattedString,
                    l = r.integerDigitsCount,
                    c = e.minimumIntegerDigits;
                return l < c && (s = (0, i.repeat)("0", c - l) + s), u && (t = -t), {
                    roundedNumber: t,
                    formattedString: s
                }
            }
        },
        17334: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.GetUnsignedRoundingMode = void 0;
            var r = {
                    ceil: "zero",
                    floor: "infinity",
                    expand: "infinity",
                    trunc: "zero",
                    halfCeil: "half-zero",
                    halfFloor: "half-infinity",
                    halfExpand: "half-infinity",
                    halfTrunc: "half-zero",
                    halfEven: "half-even"
                },
                n = {
                    ceil: "infinity",
                    floor: "zero",
                    expand: "infinity",
                    trunc: "zero",
                    halfCeil: "half-infinity",
                    halfFloor: "half-zero",
                    halfExpand: "half-infinity",
                    halfTrunc: "half-zero",
                    halfEven: "half-even"
                };
            t.GetUnsignedRoundingMode = function(e, t) {
                return t ? r[e] : n[e]
            }
        },
        46267: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.InitializeNumberFormat = void 0;
            var n = r(27644),
                a = r(73156),
                i = r(75990),
                o = r(22248),
                u = r(70893),
                s = r(68721),
                l = r(78939),
                c = r(1363),
                m = r(54920),
                f = r(29011),
                p = [1, 2, 5, 10, 20, 25, 50, 100, 200, 250, 500, 1e3, 2e3];
            t.InitializeNumberFormat = function(e, t, r, d) {
                var h = d.getInternalSlots,
                    g = d.localeData,
                    v = d.availableLocales,
                    y = d.numberingSystemNames,
                    E = d.getDefaultLocale,
                    b = d.currencyDigitsData,
                    _ = (0, n.CanonicalizeLocaleList)(t),
                    D = (0, c.CoerceOptionsToObject)(r),
                    F = Object.create(null),
                    S = (0, a.GetOption)(D, "localeMatcher", "string", ["lookup", "best fit"], "best fit");
                F.localeMatcher = S;
                var T = (0, a.GetOption)(D, "numberingSystem", "string", void 0, void 0);
                if (void 0 !== T && y.indexOf(T) < 0) throw RangeError("Invalid numberingSystems: ".concat(T));
                F.nu = T;
                var P = (0, i.ResolveLocale)(v, _, F, ["nu"], g, E),
                    I = g[P.dataLocale];
                (0, l.invariant)(!!I, "Missing locale data for ".concat(P.dataLocale));
                var O = h(e);
                O.locale = P.locale, O.dataLocale = P.dataLocale, O.numberingSystem = P.nu, O.dataLocaleData = I, (0, o.SetNumberFormatUnitOptions)(e, D, {
                    getInternalSlots: h
                });
                var N, C, M = O.style;
                if ("currency" === M) {
                    var A = O.currency,
                        L = (0, u.CurrencyDigits)(A, {
                            currencyDigitsData: b
                        });
                    N = L, C = L
                } else N = 0, C = "percent" === M ? 0 : 3;
                var w = (0, a.GetOption)(D, "notation", "string", ["standard", "scientific", "engineering", "compact"], "standard");
                O.notation = w, (0, s.SetNumberFormatDigitOptions)(O, D, N, C, w);
                var B = (0, m.GetNumberOption)(D, "roundingIncrement", 1, 5e3, 1);
                if (-1 === p.indexOf(B)) throw new RangeError("Invalid rounding increment value: ".concat(B, ".\nValid values are ").concat(p, "."));
                if (1 !== B && "fractionDigits" !== O.roundingType) throw new TypeError("For roundingIncrement > 1 only fractionDigits is a valid roundingType");
                if (1 !== B && O.maximumFractionDigits !== O.minimumFractionDigits) throw new RangeError("With roundingIncrement > 1, maximumFractionDigits and minimumFractionDigits must be equal.");
                O.roundingIncrement = B;
                var R = (0, a.GetOption)(D, "trailingZeroDisplay", "string", ["auto", "stripIfInteger"], "auto");
                O.trailingZeroDisplay = R;
                var x = (0, a.GetOption)(D, "compactDisplay", "string", ["short", "long"], "short"),
                    j = "auto";
                return "compact" === w && (O.compactDisplay = x, j = "min2"), O.useGrouping = (0, f.GetStringOrBooleanOption)(D, "useGrouping", ["min2", "auto", "always"], "always", !1, j), O.signDisplay = (0, a.GetOption)(D, "signDisplay", "string", ["auto", "never", "always", "exceptZero", "negative"], "auto"), O.roundingMode = (0, a.GetOption)(D, "roundingMode", "string", ["ceil", "floor", "expand", "trunc", "halfCeil", "halfFloor", "halfExpand", "halfTrunc", "halfEven"], "halfExpand"), e
            }
        },
        38709: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.PartitionNumberPattern = void 0;
            var n = r(22970),
                a = r(33135),
                i = r(53266),
                o = r(92271),
                u = n.__importDefault(r(16920));
            t.PartitionNumberPattern = function(e, t, r) {
                var n, s, l, c = r.getInternalSlots,
                    m = c(e),
                    f = m.pl,
                    p = m.dataLocaleData,
                    d = m.numberingSystem,
                    h = p.numbers.symbols[d] || p.numbers.symbols[p.numbers.nu[0]],
                    g = 0,
                    v = 0;
                if (isNaN(t)) s = h.nan;
                else if (t == Number.POSITIVE_INFINITY || t == Number.NEGATIVE_INFINITY) s = h.infinity;
                else {
                    if (!(0, i.SameValue)(t, -0)) {
                        if (!isFinite(t)) throw new Error("Input must be a mathematical value");
                        "percent" == m.style && (t *= 100), v = (n = (0, o.ComputeExponent)(e, t, {
                            getInternalSlots: c
                        }))[0], g = n[1], t = v < 0 ? t * Math.pow(10, -v) : t / Math.pow(10, v)
                    }
                    var y = (0, a.FormatNumericToString)(m, t);
                    s = y.formattedString, t = y.roundedNumber
                }
                switch (m.signDisplay) {
                    case "never":
                        l = 0;
                        break;
                    case "auto":
                        l = (0, i.SameValue)(t, 0) || t > 0 || isNaN(t) ? 0 : -1;
                        break;
                    case "always":
                        l = (0, i.SameValue)(t, 0) || t > 0 || isNaN(t) ? 1 : -1;
                        break;
                    default:
                        l = 0 === t || isNaN(t) ? 0 : t > 0 ? 1 : -1
                }
                return (0, u.default)({
                    roundedNumber: t,
                    formattedString: s,
                    exponent: v,
                    magnitude: g,
                    sign: l
                }, m.dataLocaleData, f, m)
            }
        },
        63775: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.PartitionNumberRangePattern = void 0;
            var n = r(38709),
                a = r(89791),
                i = r(47485);
            t.PartitionNumberRangePattern = function(e, t, r, o) {
                var u = o.getInternalSlots;
                if (isNaN(t) || isNaN(r)) throw new RangeError("Input must be a number");
                var s = [],
                    l = (0, n.PartitionNumberPattern)(e, t, {
                        getInternalSlots: u
                    }),
                    c = (0, n.PartitionNumberPattern)(e, r, {
                        getInternalSlots: u
                    });
                if (l === c) return (0, i.FormatApproximately)(e, l, {
                    getInternalSlots: u
                });
                for (var m = 0, f = l; m < f.length; m++) f[m].source = "startRange";
                s = s.concat(l);
                var p = u(e),
                    d = p.dataLocaleData.numbers.symbols[p.numberingSystem];
                s.push({
                    type: "literal",
                    value: d.rangeSign,
                    source: "shared"
                });
                for (var h = 0, g = c; h < g.length; h++) g[h].source = "endRange";
                return s = s.concat(c), (0, a.CollapseNumberRange)(s)
            }
        },
        68721: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.SetNumberFormatDigitOptions = void 0;
            var n = r(54920),
                a = r(23090),
                i = r(73156);
            t.SetNumberFormatDigitOptions = function(e, t, r, o, u) {
                var s = (0, n.GetNumberOption)(t, "minimumIntegerDigits", 1, 21, 1),
                    l = t.minimumFractionDigits,
                    c = t.maximumFractionDigits,
                    m = t.minimumSignificantDigits,
                    f = t.maximumSignificantDigits;
                e.minimumIntegerDigits = s;
                var p = (0, i.GetOption)(t, "roundingPriority", "string", ["auto", "morePrecision", "lessPrecision"], "auto"),
                    d = void 0 !== m || void 0 !== f,
                    h = void 0 !== l || void 0 !== c,
                    g = !0,
                    v = !0;
                if ("auto" === p && (g = d, (d || !h && "compact" === u) && (v = !1)), g && (d ? (m = (0, a.DefaultNumberOption)(m, 1, 21, 1), f = (0, a.DefaultNumberOption)(f, m, 21, 21), e.minimumSignificantDigits = m, e.maximumSignificantDigits = f) : (e.minimumSignificantDigits = 1, e.maximumSignificantDigits = 21)), v)
                    if (h) {
                        if (l = (0, a.DefaultNumberOption)(l, 0, 20, void 0), c = (0, a.DefaultNumberOption)(c, 0, 20, void 0), void 0 === l) l = Math.min(r, c);
                        else if (void 0 === c) c = Math.max(o, l);
                        else if (l > c) throw new RangeError("Invalid range, ".concat(l, " > ").concat(c));
                        e.minimumFractionDigits = l, e.maximumFractionDigits = c
                    } else e.minimumFractionDigits = r, e.maximumFractionDigits = o;
                g || v ? e.roundingType = "morePrecision" === p ? "morePrecision" : "lessPrecision" === p ? "lessPrecision" : d ? "significantDigits" : "fractionDigits" : (e.roundingType = "morePrecision", e.minimumFractionDigits = 0, e.maximumFractionDigits = 0, e.minimumSignificantDigits = 1, e.maximumSignificantDigits = 2)
            }
        },
        22248: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.SetNumberFormatUnitOptions = void 0;
            var n = r(73156),
                a = r(36878),
                i = r(37998);
            t.SetNumberFormatUnitOptions = function(e, t, r) {
                void 0 === t && (t = Object.create(null));
                var o = (0, r.getInternalSlots)(e),
                    u = (0, n.GetOption)(t, "style", "string", ["decimal", "percent", "currency", "unit"], "decimal");
                o.style = u;
                var s = (0, n.GetOption)(t, "currency", "string", void 0, void 0);
                if (void 0 !== s && !(0, a.IsWellFormedCurrencyCode)(s)) throw RangeError("Malformed currency code");
                if ("currency" === u && void 0 === s) throw TypeError("currency cannot be undefined");
                var l = (0, n.GetOption)(t, "currencyDisplay", "string", ["code", "symbol", "narrowSymbol", "name"], "symbol"),
                    c = (0, n.GetOption)(t, "currencySign", "string", ["standard", "accounting"], "standard"),
                    m = (0, n.GetOption)(t, "unit", "string", void 0, void 0);
                if (void 0 !== m && !(0, i.IsWellFormedUnitIdentifier)(m)) throw RangeError("Invalid unit argument for Intl.NumberFormat()");
                if ("unit" === u && void 0 === m) throw TypeError("unit cannot be undefined");
                var f = (0, n.GetOption)(t, "unitDisplay", "string", ["short", "narrow", "long"], "short");
                "currency" === u && (o.currency = s.toUpperCase(), o.currencyDisplay = l, o.currencySign = c), "unit" === u && (o.unit = m, o.unitDisplay = f)
            }
        },
        83784: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ToRawFixed = void 0;
            var n = r(78939);
            t.ToRawFixed = function(e, t, r) {
                var a, i, o = r,
                    u = Math.round(e * Math.pow(10, o)),
                    s = u / Math.pow(10, o);
                if (u < 1e21) a = u.toString();
                else {
                    var l = (a = u.toString()).split("e"),
                        c = l[0],
                        m = l[1];
                    a = c.replace(".", ""), a += (0, n.repeat)("0", Math.max(+m - a.length + 1, 0))
                }
                if (0 !== o) {
                    var f = a.length;
                    f <= o && (a = (0, n.repeat)("0", o + 1 - f) + a, f = o + 1);
                    var p = a.slice(0, f - o),
                        d = a.slice(f - o);
                    a = "".concat(p, ".").concat(d), i = p.length
                } else i = a.length;
                for (var h = r - t; h > 0 && "0" === a[a.length - 1];) a = a.slice(0, -1), h--;
                return "." === a[a.length - 1] && (a = a.slice(0, -1)), {
                    formattedString: a,
                    roundedNumber: s,
                    integerDigitsCount: i
                }
            }
        },
        27203: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ToRawPrecision = void 0;
            var n = r(78939);
            t.ToRawPrecision = function(e, t, r) {
                var a, i, o, u, s = r;
                if (0 === e) a = (0, n.repeat)("0", s), i = 0, o = 0;
                else {
                    var l = e.toString(),
                        c = l.indexOf("e"),
                        m = l.split("e"),
                        f = m[0],
                        p = m[1],
                        d = f.replace(".", "");
                    if (c >= 0 && d.length <= s) i = +p, a = d + (0, n.repeat)("0", s - d.length), o = e;
                    else {
                        var h = (i = (0, n.getMagnitude)(e)) - s + 1,
                            g = Math.round(y(e, h));
                        y(g, s - 1) >= 10 && (i += 1, g = Math.floor(g / 10)), a = g.toString(), o = y(g, s - 1 - i)
                    }
                }
                if (i >= s - 1 ? (a += (0, n.repeat)("0", i - s + 1), u = i + 1) : i >= 0 ? (a = "".concat(a.slice(0, i + 1), ".").concat(a.slice(i + 1)), u = i + 1) : (a = "0.".concat((0, n.repeat)("0", -i - 1)).concat(a), u = 1), a.indexOf(".") >= 0 && r > t) {
                    for (var v = r - t; v > 0 && "0" === a[a.length - 1];) a = a.slice(0, -1), v--;
                    "." === a[a.length - 1] && (a = a.slice(0, -1))
                }
                return {
                    formattedString: a,
                    roundedNumber: o,
                    integerDigitsCount: u
                };

                function y(e, t) {
                    return t < 0 ? e * Math.pow(10, -t) : e / Math.pow(10, t)
                }
            }
        },
        60476: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.digitMapping = void 0, t.digitMapping = {
                adlm: ["𞥐", "𞥑", "𞥒", "𞥓", "𞥔", "𞥕", "𞥖", "𞥗", "𞥘", "𞥙"],
                ahom: ["𑜰", "𑜱", "𑜲", "𑜳", "𑜴", "𑜵", "𑜶", "𑜷", "𑜸", "𑜹"],
                arab: ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"],
                arabext: ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"],
                bali: ["᭐", "᭑", "᭒", "᭓", "᭔", "᭕", "᭖", "᭗", "᭘", "᭙"],
                beng: ["০", "১", "২", "৩", "৪", "৫", "৬", "৭", "৮", "৯"],
                bhks: ["𑱐", "𑱑", "𑱒", "𑱓", "𑱔", "𑱕", "𑱖", "𑱗", "𑱘", "𑱙"],
                brah: ["𑁦", "𑁧", "𑁨", "𑁩", "𑁪", "𑁫", "𑁬", "𑁭", "𑁮", "𑁯"],
                cakm: ["𑄶", "𑄷", "𑄸", "𑄹", "𑄺", "𑄻", "𑄼", "𑄽", "𑄾", "𑄿"],
                cham: ["꩐", "꩑", "꩒", "꩓", "꩔", "꩕", "꩖", "꩗", "꩘", "꩙"],
                deva: ["०", "१", "२", "३", "४", "५", "६", "७", "८", "९"],
                diak: ["𑥐", "𑥑", "𑥒", "𑥓", "𑥔", "𑥕", "𑥖", "𑥗", "𑥘", "𑥙"],
                fullwide: ["０", "１", "２", "３", "４", "５", "６", "７", "８", "９"],
                gong: ["𑶠", "𑶡", "𑶢", "𑶣", "𑶤", "𑶥", "𑶦", "𑶧", "𑶨", "𑶩"],
                gonm: ["𑵐", "𑵑", "𑵒", "𑵓", "𑵔", "𑵕", "𑵖", "𑵗", "𑵘", "𑵙"],
                gujr: ["૦", "૧", "૨", "૩", "૪", "૫", "૬", "૭", "૮", "૯"],
                guru: ["੦", "੧", "੨", "੩", "੪", "੫", "੬", "੭", "੮", "੯"],
                hanidec: ["〇", "一", "二", "三", "四", "五", "六", "七", "八", "九"],
                hmng: ["𖭐", "𖭑", "𖭒", "𖭓", "𖭔", "𖭕", "𖭖", "𖭗", "𖭘", "𖭙"],
                hmnp: ["𞅀", "𞅁", "𞅂", "𞅃", "𞅄", "𞅅", "𞅆", "𞅇", "𞅈", "𞅉"],
                java: ["꧐", "꧑", "꧒", "꧓", "꧔", "꧕", "꧖", "꧗", "꧘", "꧙"],
                kali: ["꤀", "꤁", "꤂", "꤃", "꤄", "꤅", "꤆", "꤇", "꤈", "꤉"],
                khmr: ["០", "១", "២", "៣", "៤", "៥", "៦", "៧", "៨", "៩"],
                knda: ["೦", "೧", "೨", "೩", "೪", "೫", "೬", "೭", "೮", "೯"],
                lana: ["᪀", "᪁", "᪂", "᪃", "᪄", "᪅", "᪆", "᪇", "᪈", "᪉"],
                lanatham: ["᪐", "᪑", "᪒", "᪓", "᪔", "᪕", "᪖", "᪗", "᪘", "᪙"],
                laoo: ["໐", "໑", "໒", "໓", "໔", "໕", "໖", "໗", "໘", "໙"],
                lepc: ["᪐", "᪑", "᪒", "᪓", "᪔", "᪕", "᪖", "᪗", "᪘", "᪙"],
                limb: ["᥆", "᥇", "᥈", "᥉", "᥊", "᥋", "᥌", "᥍", "᥎", "᥏"],
                mathbold: ["𝟎", "𝟏", "𝟐", "𝟑", "𝟒", "𝟓", "𝟔", "𝟕", "𝟖", "𝟗"],
                mathdbl: ["𝟘", "𝟙", "𝟚", "𝟛", "𝟜", "𝟝", "𝟞", "𝟟", "𝟠", "𝟡"],
                mathmono: ["𝟶", "𝟷", "𝟸", "𝟹", "𝟺", "𝟻", "𝟼", "𝟽", "𝟾", "𝟿"],
                mathsanb: ["𝟬", "𝟭", "𝟮", "𝟯", "𝟰", "𝟱", "𝟲", "𝟳", "𝟴", "𝟵"],
                mathsans: ["𝟢", "𝟣", "𝟤", "𝟥", "𝟦", "𝟧", "𝟨", "𝟩", "𝟪", "𝟫"],
                mlym: ["൦", "൧", "൨", "൩", "൪", "൫", "൬", "൭", "൮", "൯"],
                modi: ["𑙐", "𑙑", "𑙒", "𑙓", "𑙔", "𑙕", "𑙖", "𑙗", "𑙘", "𑙙"],
                mong: ["᠐", "᠑", "᠒", "᠓", "᠔", "᠕", "᠖", "᠗", "᠘", "᠙"],
                mroo: ["𖩠", "𖩡", "𖩢", "𖩣", "𖩤", "𖩥", "𖩦", "𖩧", "𖩨", "𖩩"],
                mtei: ["꯰", "꯱", "꯲", "꯳", "꯴", "꯵", "꯶", "꯷", "꯸", "꯹"],
                mymr: ["၀", "၁", "၂", "၃", "၄", "၅", "၆", "၇", "၈", "၉"],
                mymrshan: ["႐", "႑", "႒", "႓", "႔", "႕", "႖", "႗", "႘", "႙"],
                mymrtlng: ["꧰", "꧱", "꧲", "꧳", "꧴", "꧵", "꧶", "꧷", "꧸", "꧹"],
                newa: ["𑑐", "𑑑", "𑑒", "𑑓", "𑑔", "𑑕", "𑑖", "𑑗", "𑑘", "𑑙"],
                nkoo: ["߀", "߁", "߂", "߃", "߄", "߅", "߆", "߇", "߈", "߉"],
                olck: ["᱐", "᱑", "᱒", "᱓", "᱔", "᱕", "᱖", "᱗", "᱘", "᱙"],
                orya: ["୦", "୧", "୨", "୩", "୪", "୫", "୬", "୭", "୮", "୯"],
                osma: ["𐒠", "𐒡", "𐒢", "𐒣", "𐒤", "𐒥", "𐒦", "𐒧", "𐒨", "𐒩"],
                rohg: ["𐴰", "𐴱", "𐴲", "𐴳", "𐴴", "𐴵", "𐴶", "𐴷", "𐴸", "𐴹"],
                saur: ["꣐", "꣑", "꣒", "꣓", "꣔", "꣕", "꣖", "꣗", "꣘", "꣙"],
                segment: ["🯰", "🯱", "🯲", "🯳", "🯴", "🯵", "🯶", "🯷", "🯸", "🯹"],
                shrd: ["𑇐", "𑇑", "𑇒", "𑇓", "𑇔", "𑇕", "𑇖", "𑇗", "𑇘", "𑇙"],
                sind: ["𑋰", "𑋱", "𑋲", "𑋳", "𑋴", "𑋵", "𑋶", "𑋷", "𑋸", "𑋹"],
                sinh: ["෦", "෧", "෨", "෩", "෪", "෫", "෬", "෭", "෮", "෯"],
                sora: ["𑃰", "𑃱", "𑃲", "𑃳", "𑃴", "𑃵", "𑃶", "𑃷", "𑃸", "𑃹"],
                sund: ["᮰", "᮱", "᮲", "᮳", "᮴", "᮵", "᮶", "᮷", "᮸", "᮹"],
                takr: ["𑛀", "𑛁", "𑛂", "𑛃", "𑛄", "𑛅", "𑛆", "𑛇", "𑛈", "𑛉"],
                talu: ["᧐", "᧑", "᧒", "᧓", "᧔", "᧕", "᧖", "᧗", "᧘", "᧙"],
                tamldec: ["௦", "௧", "௨", "௩", "௪", "௫", "௬", "௭", "௮", "௯"],
                telu: ["౦", "౧", "౨", "౩", "౪", "౫", "౬", "౭", "౮", "౯"],
                thai: ["๐", "๑", "๒", "๓", "๔", "๕", "๖", "๗", "๘", "๙"],
                tibt: ["༠", "༡", "༢", "༣", "༤", "༥", "༦", "༧", "༨", "༩"],
                tirh: ["𑓐", "𑓑", "𑓒", "𑓓", "𑓔", "𑓕", "𑓖", "𑓗", "𑓘", "𑓙"],
                vaii: ["ᘠ", "ᘡ", "ᘢ", "ᘣ", "ᘤ", "ᘥ", "ᘦ", "ᘧ", "ᘨ", "ᘩ"],
                wara: ["𑣠", "𑣡", "𑣢", "𑣣", "𑣤", "𑣥", "𑣦", "𑣧", "𑣨", "𑣩"],
                wcho: ["𞋰", "𞋱", "𞋲", "𞋳", "𞋴", "𞋵", "𞋶", "𞋷", "𞋸", "𞋹"]
            }
        },
        16920: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(83784),
                a = r(60476),
                i = r(56523),
                o = new RegExp("^".concat(i.S_UNICODE_REGEX.source)),
                u = new RegExp("".concat(i.S_UNICODE_REGEX.source, "$")),
                s = /[#0](?:[\.,][#0]+)*/g;

            function l(e, t, r, i, o, u, s) {
                var l = [],
                    c = t.formattedString,
                    m = t.roundedNumber;
                if (isNaN(m)) return [{
                    type: "nan",
                    value: c
                }];
                if (!isFinite(m)) return [{
                    type: "infinity",
                    value: c
                }];
                var f = a.digitMapping[o];
                f && (c = c.replace(/\d/g, (function(e) {
                    return f[+e] || e
                })));
                var p, d, h = c.indexOf(".");
                if (h > 0 ? (p = c.slice(0, h), d = c.slice(h + 1)) : p = c, u && ("compact" !== r || m >= 1e4)) {
                    var g = e.group,
                        v = [],
                        y = s.split(".")[0].split(","),
                        E = 3,
                        b = 3;
                    y.length > 1 && (E = y[y.length - 1].length), y.length > 2 && (b = y[y.length - 2].length);
                    var _ = p.length - E;
                    if (_ > 0) {
                        for (v.push(p.slice(_, _ + E)), _ -= b; _ > 0; _ -= b) v.push(p.slice(_, _ + b));
                        v.push(p.slice(0, _ + b))
                    } else v.push(p);
                    for (; v.length > 0;) {
                        var D = v.pop();
                        l.push({
                            type: "integer",
                            value: D
                        }), v.length > 0 && l.push({
                            type: "group",
                            value: g
                        })
                    }
                } else l.push({
                    type: "integer",
                    value: p
                });
                if (void 0 !== d && l.push({
                        type: "decimal",
                        value: e.decimal
                    }, {
                        type: "fraction",
                        value: d
                    }), ("scientific" === r || "engineering" === r) && isFinite(m)) {
                    l.push({
                        type: "exponentSeparator",
                        value: e.exponential
                    }), i < 0 && (l.push({
                        type: "exponentMinusSign",
                        value: e.minusSign
                    }), i = -i);
                    var F = (0, n.ToRawFixed)(i, 0, 0);
                    l.push({
                        type: "exponentInteger",
                        value: F.formattedString
                    })
                }
                return l
            }

            function c(e, t) {
                e.indexOf(";") < 0 && (e = "".concat(e, ";-").concat(e));
                var r = e.split(";"),
                    n = r[0],
                    a = r[1];
                switch (t) {
                    case 0:
                        return n;
                    case -1:
                        return a;
                    default:
                        return a.indexOf("-") >= 0 ? a.replace(/-/g, "+") : "+".concat(n)
                }
            }

            function m(e, t, r) {
                return r[e.select(t)] || r.other
            }
            t.default = function(e, t, r, n) {
                var a, i, f = e.sign,
                    p = e.exponent,
                    d = e.magnitude,
                    h = n.notation,
                    g = n.style,
                    v = n.numberingSystem,
                    y = t.numbers.nu[0],
                    E = null;
                if ("compact" === h && d && (E = function(e, t, r, n, a, i, o) {
                        var u, s, l = e.roundedNumber,
                            f = e.sign,
                            p = e.magnitude,
                            d = String(Math.pow(10, p)),
                            h = r.numbers.nu[0];
                        if ("currency" === n && "name" !== i) {
                            var g = null === (u = ((v = r.numbers.currency)[o] || v[h]).short) || void 0 === u ? void 0 : u[d];
                            if (!g) return null;
                            s = m(t, l, g)
                        } else {
                            var v, y = ((v = r.numbers.decimal)[o] || v[h])[a][d];
                            if (!y) return null;
                            s = m(t, l, y)
                        }
                        return "0" === s ? null : s = c(s, f).replace(/([^\s;\-\+\d¤]+)/g, "{c:$1}").replace(/0+/, "0")
                    }(e, r, t, g, n.compactDisplay, n.currencyDisplay, v)), "currency" === g && "name" !== n.currencyDisplay) {
                    var b = t.currencies[n.currency];
                    if (b) switch (n.currencyDisplay) {
                        case "code":
                            a = n.currency;
                            break;
                        case "symbol":
                            a = b.symbol;
                            break;
                        default:
                            a = b.narrow
                    } else a = n.currency
                }
                i = E || ("decimal" === g || "unit" === g || "currency" === g && "name" === n.currencyDisplay ? c((t.numbers.decimal[v] || t.numbers.decimal[y]).standard, f) : c("currency" === g ? (D = t.numbers.currency[v] || t.numbers.currency[y])[n.currencySign] : t.numbers.percent[v] || t.numbers.percent[y], f));
                var _ = s.exec(i)[0];
                if (i = i.replace(s, "{0}").replace(/'(.)'/g, "$1"), "currency" === g && "name" !== n.currencyDisplay) {
                    var D, F = (D = t.numbers.currency[v] || t.numbers.currency[y]).currencySpacing.afterInsertBetween;
                    F && !u.test(a) && (i = i.replace("¤{0}", "¤".concat(F, "{0}")));
                    var S = D.currencySpacing.beforeInsertBetween;
                    S && !o.test(a) && (i = i.replace("{0}¤", "{0}".concat(S, "¤")))
                }
                for (var T = i.split(/({c:[^}]+}|\{0\}|[¤%\-\+])/g), P = [], I = t.numbers.symbols[v] || t.numbers.symbols[y], O = 0, N = T; O < N.length; O++)
                    if (Z = N[O]) switch (Z) {
                        case "{0}":
                            P.push.apply(P, l(I, e, h, p, v, !E && Boolean(n.useGrouping), _));
                            break;
                        case "-":
                            P.push({
                                type: "minusSign",
                                value: I.minusSign
                            });
                            break;
                        case "+":
                            P.push({
                                type: "plusSign",
                                value: I.plusSign
                            });
                            break;
                        case "%":
                            P.push({
                                type: "percentSign",
                                value: I.percentSign
                            });
                            break;
                        case "¤":
                            P.push({
                                type: "currency",
                                value: a
                            });
                            break;
                        default:
                            /^\{c:/.test(Z) ? P.push({
                                type: "compact",
                                value: Z.substring(3, Z.length - 1)
                            }) : P.push({
                                type: "literal",
                                value: Z
                            })
                    }
                switch (g) {
                    case "currency":
                        if ("name" === n.currencyDisplay) {
                            var C, M = (t.numbers.currency[v] || t.numbers.currency[y]).unitPattern,
                                A = t.currencies[n.currency];
                            C = A ? m(r, e.roundedNumber * Math.pow(10, p), A.displayName) : n.currency;
                            for (var L = [], w = 0, B = M.split(/(\{[01]\})/g); w < B.length; w++) switch (Z = B[w]) {
                                case "{0}":
                                    L.push.apply(L, P);
                                    break;
                                case "{1}":
                                    L.push({
                                        type: "currency",
                                        value: C
                                    });
                                    break;
                                default:
                                    Z && L.push({
                                        type: "literal",
                                        value: Z
                                    })
                            }
                            return L
                        }
                        return P;
                    case "unit":
                        var R = n.unit,
                            x = n.unitDisplay,
                            j = t.units.simple[R];
                        if (M = void 0, j) M = m(r, e.roundedNumber * Math.pow(10, p), t.units.simple[R][x]);
                        else {
                            var H = R.split("-per-"),
                                U = H[0],
                                G = H[1];
                            j = t.units.simple[U];
                            var k = m(r, e.roundedNumber * Math.pow(10, p), t.units.simple[U][x]),
                                V = t.units.simple[G].perUnit[x];
                            if (V) M = V.replace("{0}", k);
                            else {
                                var z = t.units.compound.per[x],
                                    X = m(r, 1, t.units.simple[G][x]);
                                M = M = z.replace("{0}", k).replace("{1}", X.replace("{0}", ""))
                            }
                        }
                        L = [];
                        for (var Y = 0, K = M.split(/(\s*\{0\}\s*)/); Y < K.length; Y++) {
                            var Z = K[Y],
                                W = /^(\s*)\{0\}(\s*)$/.exec(Z);
                            W ? (W[1] && L.push({
                                type: "literal",
                                value: W[1]
                            }), L.push.apply(L, P), W[2] && L.push({
                                type: "literal",
                                value: W[2]
                            })) : Z && L.push({
                                type: "unit",
                                value: Z
                            })
                        }
                        return L;
                    default:
                        return P
                }
            }
        },
        75909: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.PartitionPattern = void 0;
            var n = r(78939);
            t.PartitionPattern = function(e) {
                for (var t = [], r = e.indexOf("{"), a = 0, i = 0, o = e.length; r < e.length && r > -1;) a = e.indexOf("}", r), (0, n.invariant)(a > r, "Invalid pattern ".concat(e)), r > i && t.push({
                    type: "literal",
                    value: e.substring(i, r)
                }), t.push({
                    type: e.substring(r + 1, a),
                    value: void 0
                }), i = a + 1, r = e.indexOf("{", i);
                return i < o && t.push({
                    type: "literal",
                    value: e.substring(i, o)
                }), t
            }
        },
        74668: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.SupportedLocales = void 0;
            var n = r(53266),
                a = r(73156),
                i = r(75990);
            t.SupportedLocales = function(e, t, r) {
                return void 0 !== r && (r = (0, n.ToObject)(r), (0, a.GetOption)(r, "localeMatcher", "string", ["lookup", "best fit"], "best fit")), (0, i.LookupSupportedLocales)(e, t)
            }
        },
        97753: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.isMissingLocaleDataError = void 0;
            var n, a = r(22970);
            n = Error, a.__extends((function() {
                var e = null !== n && n.apply(this, arguments) || this;
                return e.type = "MISSING_LOCALE_DATA", e
            }), n), t.isMissingLocaleDataError = function(e) {
                return "MISSING_LOCALE_DATA" === e.type
            }
        },
        73619: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.invariant = t.isMissingLocaleDataError = t.defineProperty = t.getMagnitude = t.setMultiInternalSlots = t.setInternalSlot = t.isLiteralPart = t.getMultiInternalSlots = t.getInternalSlot = t._formatToParts = void 0;
            var n = r(22970);
            n.__exportStar(r(27644), t), n.__exportStar(r(54669), t), n.__exportStar(r(1363), t), n.__exportStar(r(54920), t), n.__exportStar(r(73156), t), n.__exportStar(r(73926), t), n.__exportStar(r(29011), t), n.__exportStar(r(44405), t), n.__exportStar(r(73991), t), n.__exportStar(r(36878), t), n.__exportStar(r(37998), t), n.__exportStar(r(51417), t), n.__exportStar(r(89791), t), n.__exportStar(r(92271), t), n.__exportStar(r(37208), t), n.__exportStar(r(70893), t), n.__exportStar(r(47485), t), n.__exportStar(r(633), t), n.__exportStar(r(63415), t), n.__exportStar(r(17680), t), n.__exportStar(r(33135), t), n.__exportStar(r(17334), t), n.__exportStar(r(46267), t), n.__exportStar(r(38709), t), n.__exportStar(r(63775), t), n.__exportStar(r(68721), t), n.__exportStar(r(22248), t), n.__exportStar(r(83784), t), n.__exportStar(r(27203), t);
            var a = r(16920);
            Object.defineProperty(t, "_formatToParts", {
                enumerable: !0,
                get: function() {
                    return n.__importDefault(a).default
                }
            }), n.__exportStar(r(75909), t), n.__exportStar(r(74668), t);
            var i = r(78939);
            Object.defineProperty(t, "getInternalSlot", {
                enumerable: !0,
                get: function() {
                    return i.getInternalSlot
                }
            }), Object.defineProperty(t, "getMultiInternalSlots", {
                enumerable: !0,
                get: function() {
                    return i.getMultiInternalSlots
                }
            }), Object.defineProperty(t, "isLiteralPart", {
                enumerable: !0,
                get: function() {
                    return i.isLiteralPart
                }
            }), Object.defineProperty(t, "setInternalSlot", {
                enumerable: !0,
                get: function() {
                    return i.setInternalSlot
                }
            }), Object.defineProperty(t, "setMultiInternalSlots", {
                enumerable: !0,
                get: function() {
                    return i.setMultiInternalSlots
                }
            }), Object.defineProperty(t, "getMagnitude", {
                enumerable: !0,
                get: function() {
                    return i.getMagnitude
                }
            }), Object.defineProperty(t, "defineProperty", {
                enumerable: !0,
                get: function() {
                    return i.defineProperty
                }
            });
            var o = r(97753);
            Object.defineProperty(t, "isMissingLocaleDataError", {
                enumerable: !0,
                get: function() {
                    return o.isMissingLocaleDataError
                }
            }), n.__exportStar(r(16963), t), n.__exportStar(r(14228), t), n.__exportStar(r(98874), t), n.__exportStar(r(38295), t), n.__exportStar(r(18172), t), n.__exportStar(r(3316), t);
            var u = r(78939);
            Object.defineProperty(t, "invariant", {
                enumerable: !0,
                get: function() {
                    return u.invariant
                }
            }), n.__exportStar(r(53266), t)
        },
        56523: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.S_UNICODE_REGEX = void 0, t.S_UNICODE_REGEX = /[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20BF\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFB\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC1\uFDFC\uFDFD\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDE8\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEE0-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF73\uDF80-\uDFD8\uDFE0-\uDFEB]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDD78\uDD7A-\uDDCB\uDDCD-\uDE53\uDE60-\uDE6D\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6\uDF00-\uDF92\uDF94-\uDFCA]/
        },
        14228: (e, t) => {
            var r;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.RangePatternType = void 0, (r = t.RangePatternType || (t.RangePatternType = {})).startRange = "startRange", r.shared = "shared", r.endRange = "endRange"
        },
        3316: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        98874: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        18172: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        38295: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        16963: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        },
        78939: (e, t) => {
            function r(e, t, r, n) {
                e.get(t) || e.set(t, Object.create(null)), e.get(t)[r] = n
            }

            function n(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                var a = e.get(t);
                if (!a) throw new TypeError("".concat(t, " InternalSlot has not been initialized"));
                return r.reduce((function(e, t) {
                    return e[t] = a[t], e
                }), Object.create(null))
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.invariant = t.UNICODE_EXTENSION_SEQUENCE_REGEX = t.defineProperty = t.isLiteralPart = t.getMultiInternalSlots = t.getInternalSlot = t.setMultiInternalSlots = t.setInternalSlot = t.repeat = t.getMagnitude = void 0, t.getMagnitude = function(e) {
                return Math.floor(Math.log(e) * Math.LOG10E)
            }, t.repeat = function(e, t) {
                if ("function" == typeof e.repeat) return e.repeat(t);
                for (var r = new Array(t), n = 0; n < r.length; n++) r[n] = e;
                return r.join("")
            }, t.setInternalSlot = r, t.setMultiInternalSlots = function(e, t, n) {
                for (var a = 0, i = Object.keys(n); a < i.length; a++) {
                    var o = i[a];
                    r(e, t, o, n[o])
                }
            }, t.getInternalSlot = function(e, t, r) {
                return n(e, t, r)[r]
            }, t.getMultiInternalSlots = n, t.isLiteralPart = function(e) {
                return "literal" === e.type
            }, t.defineProperty = function(e, t, r) {
                var n = r.value;
                Object.defineProperty(e, t, {
                    configurable: !0,
                    enumerable: !1,
                    writable: !0,
                    value: n
                })
            }, t.UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi, t.invariant = function(e, t, r) {
                if (void 0 === r && (r = Error), !e) throw new r(t)
            }
        },
        63223: (e, t) => {
            function r(e, t, r, n) {
                var a, i = null == (a = n) || "number" == typeof a || "boolean" == typeof a ? n : r(n),
                    o = t.get(i);
                return void 0 === o && (o = e.call(this, n), t.set(i, o)), o
            }

            function n(e, t, r) {
                var n = Array.prototype.slice.call(arguments, 3),
                    a = r(n),
                    i = t.get(a);
                return void 0 === i && (i = e.apply(this, n), t.set(a, i)), i
            }

            function a(e, t, r, n, a) {
                return r.bind(t, e, n, a)
            }

            function i(e, t) {
                return a(e, this, 1 === e.length ? r : n, t.cache.create(), t.serializer)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.strategies = t.memoize = void 0, t.memoize = function(e, t) {
                var r = t && t.cache ? t.cache : s,
                    n = t && t.serializer ? t.serializer : o;
                return (t && t.strategy ? t.strategy : i)(e, {
                    cache: r,
                    serializer: n
                })
            };
            var o = function() {
                return JSON.stringify(arguments)
            };

            function u() {
                this.cache = Object.create(null)
            }
            u.prototype.get = function(e) {
                return this.cache[e]
            }, u.prototype.set = function(e, t) {
                this.cache[e] = t
            };
            var s = {
                create: function() {
                    return new u
                }
            };
            t.strategies = {
                variadic: function(e, t) {
                    return a(e, this, n, t.cache.create(), t.serializer)
                },
                monadic: function(e, t) {
                    return a(e, this, r, t.cache.create(), t.serializer)
                }
            }
        },
        80586: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.BestAvailableLocale = void 0, t.BestAvailableLocale = function(e, t) {
                for (var r = t;;) {
                    if (e.has(r)) return r;
                    var n = r.lastIndexOf("-");
                    if (!~n) return;
                    n >= 2 && "-" === r[n - 2] && (n -= 2), r = r.slice(0, n)
                }
            }
        },
        59892: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.BestFitMatcher = void 0;
            var n = r(80586),
                a = r(87567);
            t.BestFitMatcher = function(e, t, r) {
                var i, o = {},
                    u = {},
                    s = {},
                    l = new Set;
                e.forEach((function(e) {
                    var t = new Intl.Locale(e).minimize().toString(),
                        r = Intl.getCanonicalLocales(e)[0] || e;
                    o[t] = e, u[e] = e, s[r] = e, l.add(t), l.add(e), l.add(r)
                }));
                for (var c = 0, m = t; c < m.length; c++) {
                    var f = m[c];
                    if (i) break;
                    var p = f.replace(a.UNICODE_EXTENSION_SEQUENCE_REGEX, "");
                    if (e.has(p)) {
                        i = p;
                        break
                    }
                    if (l.has(p)) {
                        i = p;
                        break
                    }
                    var d = new Intl.Locale(p),
                        h = d.maximize().toString(),
                        g = d.minimize().toString();
                    if (l.has(g)) {
                        i = g;
                        break
                    }
                    i = (0, n.BestAvailableLocale)(l, h)
                }
                return i ? {
                    locale: u[i] || s[i] || o[i] || i
                } : {
                    locale: r()
                }
            }
        },
        75501: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.CanonicalizeLocaleList = void 0, t.CanonicalizeLocaleList = function(e) {
                return Intl.getCanonicalLocales(e)
            }
        },
        72164: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.LookupMatcher = void 0;
            var n = r(87567),
                a = r(80586);
            t.LookupMatcher = function(e, t, r) {
                for (var i = {
                        locale: ""
                    }, o = 0, u = t; o < u.length; o++) {
                    var s = u[o],
                        l = s.replace(n.UNICODE_EXTENSION_SEQUENCE_REGEX, ""),
                        c = (0, a.BestAvailableLocale)(e, l);
                    if (c) return i.locale = c, s !== l && (i.extension = s.slice(l.length + 1, s.length)), i
                }
                return i.locale = r(), i
            }
        },
        92090: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.LookupSupportedLocales = void 0;
            var n = r(87567),
                a = r(80586);
            t.LookupSupportedLocales = function(e, t) {
                for (var r = [], i = 0, o = t; i < o.length; i++) {
                    var u = o[i].replace(n.UNICODE_EXTENSION_SEQUENCE_REGEX, ""),
                        s = (0, a.BestAvailableLocale)(e, u);
                    s && r.push(s)
                }
                return r
            }
        },
        74937: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ResolveLocale = void 0;
            var n = r(72164),
                a = r(59892),
                i = r(87567),
                o = r(40735);
            t.ResolveLocale = function(e, t, r, u, s, l) {
                for (var c, m = (c = "lookup" === r.localeMatcher ? (0, n.LookupMatcher)(e, t, l) : (0, a.BestFitMatcher)(e, t, l)).locale, f = {
                        locale: "",
                        dataLocale: m
                    }, p = "-u", d = 0, h = u; d < h.length; d++) {
                    var g = h[d];
                    (0, i.invariant)(m in s, "Missing locale data for ".concat(m));
                    var v = s[m];
                    (0, i.invariant)("object" == typeof v && null !== v, "locale data ".concat(g, " must be an object"));
                    var y = v[g];
                    (0, i.invariant)(Array.isArray(y), "keyLocaleData for ".concat(g, " must be an array"));
                    var E = y[0];
                    (0, i.invariant)("string" == typeof E || null === E, "value must be string or null but got ".concat(typeof E, " in key ").concat(g));
                    var b = "";
                    if (c.extension) {
                        var _ = (0, o.UnicodeExtensionValue)(c.extension, g);
                        void 0 !== _ && ("" !== _ ? ~y.indexOf(_) && (E = _, b = "-".concat(g, "-").concat(E)) : ~_.indexOf("true") && (E = "true", b = "-".concat(g)))
                    }
                    if (g in r) {
                        var D = r[g];
                        (0, i.invariant)("string" == typeof D || null == D, "optionsValue must be String, Undefined or Null"), ~y.indexOf(D) && D !== E && (E = D, b = "")
                    }
                    f[g] = E, p += b
                }
                if (p.length > 2) {
                    var F = m.indexOf("-x-");
                    if (-1 === F) m += p;
                    else {
                        var S = m.slice(0, F),
                            T = m.slice(F, m.length);
                        m = S + p + T
                    }
                    m = Intl.getCanonicalLocales(m)[0]
                }
                return f.locale = m, f
            }
        },
        40735: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.UnicodeExtensionValue = void 0;
            var n = r(87567);
            t.UnicodeExtensionValue = function(e, t) {
                (0, n.invariant)(2 === t.length, "key must have 2 elements");
                var r = e.length,
                    a = "-".concat(t, "-"),
                    i = e.indexOf(a);
                if (-1 !== i) {
                    for (var o = i + 4, u = o, s = o, l = !1; !l;) {
                        var c = e.indexOf("-", s);
                        2 == (-1 === c ? r - s : c - s) ? l = !0 : -1 === c ? (u = r, l = !0) : (u = c, s = c + 1)
                    }
                    return e.slice(o, u)
                }
                if (a = "-".concat(t), -1 !== (i = e.indexOf(a)) && i + 3 === r) return ""
            }
        },
        87567: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.invariant = t.UNICODE_EXTENSION_SEQUENCE_REGEX = void 0, t.UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi, t.invariant = function(e, t, r) {
                if (void 0 === r && (r = Error), !e) throw new r(t)
            }
        },
        75990: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.ResolveLocale = t.LookupSupportedLocales = t.match = void 0;
            var n = r(75501),
                a = r(74937);
            t.match = function(e, t, r, i) {
                var o = t.reduce((function(e, t) {
                    return e.add(t), e
                }), new Set);
                return (0, a.ResolveLocale)(o, (0, n.CanonicalizeLocaleList)(e), {
                    localeMatcher: (null == i ? void 0 : i.algorithm) || "best fit"
                }, [], {}, (function() {
                    return r
                })).locale
            };
            var i = r(92090);
            Object.defineProperty(t, "LookupSupportedLocales", {
                enumerable: !0,
                get: function() {
                    return i.LookupSupportedLocales
                }
            });
            var o = r(74937);
            Object.defineProperty(t, "ResolveLocale", {
                enumerable: !0,
                get: function() {
                    return o.ResolveLocale
                }
            })
        },
        35341: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970),
                a = r(68435);
            n.__exportStar(r(82844), t), n.__exportStar(r(68435), t), n.__exportStar(r(50664), t), t.default = a.IntlMessageFormat
        },
        68435: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.IntlMessageFormat = void 0;
            var n = r(22970),
                a = r(46057),
                i = r(63223),
                o = r(82844);

            function u(e) {
                return {
                    create: function() {
                        return {
                            get: function(t) {
                                return e[t]
                            },
                            set: function(t, r) {
                                e[t] = r
                            }
                        }
                    }
                }
            }
            t.IntlMessageFormat = function() {
                function e(t, r, a, s) {
                    void 0 === r && (r = e.defaultLocale);
                    var l, c, m, f = this;
                    if (this.formatterCache = {
                            number: {},
                            dateTime: {},
                            pluralRules: {}
                        }, this.format = function(e) {
                            var t = f.formatToParts(e);
                            if (1 === t.length) return t[0].value;
                            var r = t.reduce((function(e, t) {
                                return e.length && t.type === o.PART_TYPE.literal && "string" == typeof e[e.length - 1] ? e[e.length - 1] += t.value : e.push(t.value), e
                            }), []);
                            return r.length <= 1 ? r[0] || "" : r
                        }, this.formatToParts = function(e) {
                            return (0, o.formatToParts)(f.ast, f.locales, f.formatters, f.formats, e, void 0, f.message)
                        }, this.resolvedOptions = function() {
                            var e;
                            return {
                                locale: (null === (e = f.resolvedLocale) || void 0 === e ? void 0 : e.toString()) || Intl.NumberFormat.supportedLocalesOf(f.locales)[0]
                            }
                        }, this.getAst = function() {
                            return f.ast
                        }, this.locales = r, this.resolvedLocale = e.resolveLocale(r), "string" == typeof t) {
                        if (this.message = t, !e.__parse) throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");
                        var p = s || {},
                            d = (p.formatters, n.__rest(p, ["formatters"]));
                        this.ast = e.__parse(t, n.__assign(n.__assign({}, d), {
                            locale: this.resolvedLocale
                        }))
                    } else this.ast = t;
                    if (!Array.isArray(this.ast)) throw new TypeError("A message must be provided as a String or AST.");
                    this.formats = (c = e.formats, (m = a) ? Object.keys(c).reduce((function(e, t) {
                        var r, a;
                        return e[t] = (r = c[t], (a = m[t]) ? n.__assign(n.__assign(n.__assign({}, r || {}), a || {}), Object.keys(r).reduce((function(e, t) {
                            return e[t] = n.__assign(n.__assign({}, r[t]), a[t] || {}), e
                        }), {})) : r), e
                    }), n.__assign({}, c)) : c), this.formatters = s && s.formatters || (void 0 === (l = this.formatterCache) && (l = {
                        number: {},
                        dateTime: {},
                        pluralRules: {}
                    }), {
                        getNumberFormat: (0, i.memoize)((function() {
                            for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                            return new((e = Intl.NumberFormat).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                        }), {
                            cache: u(l.number),
                            strategy: i.strategies.variadic
                        }),
                        getDateTimeFormat: (0, i.memoize)((function() {
                            for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                            return new((e = Intl.DateTimeFormat).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                        }), {
                            cache: u(l.dateTime),
                            strategy: i.strategies.variadic
                        }),
                        getPluralRules: (0, i.memoize)((function() {
                            for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                            return new((e = Intl.PluralRules).bind.apply(e, n.__spreadArray([void 0], t, !1)))
                        }), {
                            cache: u(l.pluralRules),
                            strategy: i.strategies.variadic
                        })
                    })
                }
                return Object.defineProperty(e, "defaultLocale", {
                    get: function() {
                        return e.memoizedDefaultLocale || (e.memoizedDefaultLocale = (new Intl.NumberFormat).resolvedOptions().locale), e.memoizedDefaultLocale
                    },
                    enumerable: !1,
                    configurable: !0
                }), e.memoizedDefaultLocale = null, e.resolveLocale = function(e) {
                    if (void 0 !== Intl.Locale) {
                        var t = Intl.NumberFormat.supportedLocalesOf(e);
                        return t.length > 0 ? new Intl.Locale(t[0]) : new Intl.Locale("string" == typeof e ? e : e[0])
                    }
                }, e.__parse = a.parse, e.formats = {
                    number: {
                        integer: {
                            maximumFractionDigits: 0
                        },
                        currency: {
                            style: "currency"
                        },
                        percent: {
                            style: "percent"
                        }
                    },
                    date: {
                        short: {
                            month: "numeric",
                            day: "numeric",
                            year: "2-digit"
                        },
                        medium: {
                            month: "short",
                            day: "numeric",
                            year: "numeric"
                        },
                        long: {
                            month: "long",
                            day: "numeric",
                            year: "numeric"
                        },
                        full: {
                            weekday: "long",
                            month: "long",
                            day: "numeric",
                            year: "numeric"
                        }
                    },
                    time: {
                        short: {
                            hour: "numeric",
                            minute: "numeric"
                        },
                        medium: {
                            hour: "numeric",
                            minute: "numeric",
                            second: "numeric"
                        },
                        long: {
                            hour: "numeric",
                            minute: "numeric",
                            second: "numeric",
                            timeZoneName: "short"
                        },
                        full: {
                            hour: "numeric",
                            minute: "numeric",
                            second: "numeric",
                            timeZoneName: "short"
                        }
                    }
                }, e
            }()
        },
        50664: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.MissingValueError = t.InvalidValueTypeError = t.InvalidValueError = t.FormatError = t.ErrorCode = void 0;
            var n, a = r(22970);
            ! function(e) {
                e.MISSING_VALUE = "MISSING_VALUE", e.INVALID_VALUE = "INVALID_VALUE", e.MISSING_INTL_API = "MISSING_INTL_API"
            }(n = t.ErrorCode || (t.ErrorCode = {}));
            var i = function(e) {
                function t(t, r, n) {
                    var a = e.call(this, t) || this;
                    return a.code = r, a.originalMessage = n, a
                }
                return a.__extends(t, e), t.prototype.toString = function() {
                    return "[formatjs Error: ".concat(this.code, "] ").concat(this.message)
                }, t
            }(Error);
            t.FormatError = i;
            var o = function(e) {
                function t(t, r, a, i) {
                    return e.call(this, 'Invalid values for "'.concat(t, '": "').concat(r, '". Options are "').concat(Object.keys(a).join('", "'), '"'), n.INVALID_VALUE, i) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.InvalidValueError = o;
            var u = function(e) {
                function t(t, r, a) {
                    return e.call(this, 'Value for "'.concat(t, '" must be of type ').concat(r), n.INVALID_VALUE, a) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.InvalidValueTypeError = u;
            var s = function(e) {
                function t(t, r) {
                    return e.call(this, 'The intl string context variable "'.concat(t, '" was not provided to the string "').concat(r, '"'), n.MISSING_VALUE, r) || this
                }
                return a.__extends(t, e), t
            }(i);
            t.MissingValueError = s
        },
        82844: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.formatToParts = t.isFormatXMLElementFn = t.PART_TYPE = void 0;
            var n, a = r(46057),
                i = r(50664);

            function o(e) {
                return "function" == typeof e
            }! function(e) {
                e[e.literal = 0] = "literal", e[e.object = 1] = "object"
            }(n = t.PART_TYPE || (t.PART_TYPE = {})), t.isFormatXMLElementFn = o, t.formatToParts = function e(t, r, u, s, l, c, m) {
                if (1 === t.length && (0, a.isLiteralElement)(t[0])) return [{
                    type: n.literal,
                    value: t[0].value
                }];
                for (var f = [], p = 0, d = t; p < d.length; p++) {
                    var h = d[p];
                    if ((0, a.isLiteralElement)(h)) f.push({
                        type: n.literal,
                        value: h.value
                    });
                    else if ((0, a.isPoundElement)(h)) "number" == typeof c && f.push({
                        type: n.literal,
                        value: u.getNumberFormat(r).format(c)
                    });
                    else {
                        var g = h.value;
                        if (!l || !(g in l)) throw new i.MissingValueError(g, m);
                        var v = l[g];
                        if ((0, a.isArgumentElement)(h)) v && "string" != typeof v && "number" != typeof v || (v = "string" == typeof v || "number" == typeof v ? String(v) : ""), f.push({
                            type: "string" == typeof v ? n.literal : n.object,
                            value: v
                        });
                        else if ((0, a.isDateElement)(h)) {
                            var y = "string" == typeof h.style ? s.date[h.style] : (0, a.isDateTimeSkeleton)(h.style) ? h.style.parsedOptions : void 0;
                            f.push({
                                type: n.literal,
                                value: u.getDateTimeFormat(r, y).format(v)
                            })
                        } else if ((0, a.isTimeElement)(h)) y = "string" == typeof h.style ? s.time[h.style] : (0, a.isDateTimeSkeleton)(h.style) ? h.style.parsedOptions : s.time.medium, f.push({
                            type: n.literal,
                            value: u.getDateTimeFormat(r, y).format(v)
                        });
                        else if ((0, a.isNumberElement)(h))(y = "string" == typeof h.style ? s.number[h.style] : (0, a.isNumberSkeleton)(h.style) ? h.style.parsedOptions : void 0) && y.scale && (v *= y.scale || 1), f.push({
                            type: n.literal,
                            value: u.getNumberFormat(r, y).format(v)
                        });
                        else {
                            if ((0, a.isTagElement)(h)) {
                                var E = h.children,
                                    b = h.value,
                                    _ = l[b];
                                if (!o(_)) throw new i.InvalidValueTypeError(b, "function", m);
                                var D = _(e(E, r, u, s, l, c).map((function(e) {
                                    return e.value
                                })));
                                Array.isArray(D) || (D = [D]), f.push.apply(f, D.map((function(e) {
                                    return {
                                        type: "string" == typeof e ? n.literal : n.object,
                                        value: e
                                    }
                                })))
                            }
                            if ((0, a.isSelectElement)(h)) {
                                if (!(F = h.options[v] || h.options.other)) throw new i.InvalidValueError(h.value, v, Object.keys(h.options), m);
                                f.push.apply(f, e(F.value, r, u, s, l))
                            } else if ((0, a.isPluralElement)(h)) {
                                var F;
                                if (!(F = h.options["=".concat(v)])) {
                                    if (!Intl.PluralRules) throw new i.FormatError('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n', i.ErrorCode.MISSING_INTL_API, m);
                                    var S = u.getPluralRules(r, {
                                        type: h.pluralType
                                    }).select(v - (h.offset || 0));
                                    F = h.options[S] || h.options.other
                                }
                                if (!F) throw new i.InvalidValueError(h.value, v, Object.keys(h.options), m);
                                f.push.apply(f, e(F.value, r, u, s, l, v - (h.offset || 0)))
                            }
                        }
                    }
                }
                return (T = f).length < 2 ? T : T.reduce((function(e, t) {
                    var r = e[e.length - 1];
                    return r && r.type === n.literal && t.type === n.literal ? r.value += t.value : e.push(t), e
                }), []);
                var T
            }
        },
        71353: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.createFormattedComponent = t.createFormattedDateTimePartsComponent = t.FormattedListParts = t.FormattedNumberParts = void 0;
            var n, a, i = r(22970),
                o = i.__importStar(r(2459)),
                u = i.__importDefault(r(55325));
            ! function(e) {
                e.formatDate = "FormattedDate", e.formatTime = "FormattedTime", e.formatNumber = "FormattedNumber", e.formatList = "FormattedList", e.formatDisplayName = "FormattedDisplayName"
            }(n || (n = {})),
            function(e) {
                e.formatDate = "FormattedDateParts", e.formatTime = "FormattedTimeParts", e.formatNumber = "FormattedNumberParts", e.formatList = "FormattedListParts"
            }(a || (a = {})), t.FormattedNumberParts = function(e) {
                var t = (0, u.default)(),
                    r = e.value,
                    n = e.children,
                    a = i.__rest(e, ["value", "children"]);
                return n(t.formatNumberToParts(r, a))
            }, t.FormattedNumberParts.displayName = "FormattedNumberParts", t.FormattedListParts = function(e) {
                var t = (0, u.default)(),
                    r = e.value,
                    n = e.children,
                    a = i.__rest(e, ["value", "children"]);
                return n(t.formatListToParts(r, a))
            }, t.FormattedNumberParts.displayName = "FormattedNumberParts", t.createFormattedDateTimePartsComponent = function(e) {
                var t = function(t) {
                    var r = (0, u.default)(),
                        n = t.value,
                        a = t.children,
                        o = i.__rest(t, ["value", "children"]),
                        s = "string" == typeof n ? new Date(n || 0) : n;
                    return a("formatDate" === e ? r.formatDateToParts(s, o) : r.formatTimeToParts(s, o))
                };
                return t.displayName = a[e], t
            }, t.createFormattedComponent = function(e) {
                var t = function(t) {
                    var r = (0, u.default)(),
                        n = t.value,
                        a = t.children,
                        s = i.__rest(t, ["value", "children"]),
                        l = r[e](n, s);
                    if ("function" == typeof a) return a(l);
                    var c = r.textComponent || o.Fragment;
                    return o.createElement(c, null, l)
                };
                return t.displayName = n[e], t
            }
        },
        7886: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970),
                a = n.__importStar(r(2459)),
                i = n.__importDefault(r(55325)),
                o = function(e) {
                    var t = (0, i.default)(),
                        r = e.from,
                        o = e.to,
                        u = e.children,
                        s = n.__rest(e, ["from", "to", "children"]),
                        l = t.formatDateTimeRange(r, o, s);
                    if ("function" == typeof u) return u(l);
                    var c = t.textComponent || a.Fragment;
                    return a.createElement(c, null, l)
                };
            o.displayName = "FormattedDateTimeRange", t.default = o
        },
        16714: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.Context = t.Provider = void 0;
            var n = r(22970),
                a = n.__importStar(r(2459)),
                i = n.__importDefault(r(69060)),
                o = r(26456),
                u = "undefined" == typeof window || window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__ ? a.createContext(null) : window.__REACT_INTL_CONTEXT__ || (window.__REACT_INTL_CONTEXT__ = a.createContext(null)),
                s = u.Consumer,
                l = u.Provider;
            t.Provider = l, t.Context = u, t.default = function(e, t) {
                var r, u = t || {},
                    l = u.intlPropName,
                    c = void 0 === l ? "intl" : l,
                    m = u.forwardRef,
                    f = void 0 !== m && m,
                    p = u.enforceContext,
                    d = void 0 === p || p,
                    h = function(t) {
                        return a.createElement(s, null, (function(r) {
                            var i;
                            d && (0, o.invariantIntlContext)(r);
                            var u = ((i = {})[c] = r, i);
                            return a.createElement(e, n.__assign({}, t, u, {
                                ref: f ? t.forwardedRef : null
                            }))
                        }))
                    };
                return h.displayName = "injectIntl(".concat((r = e).displayName || r.name || "Component", ")"), h.WrappedComponent = e, f ? (0, i.default)(a.forwardRef((function(e, t) {
                    return a.createElement(h, n.__assign({}, e, {
                        forwardedRef: t
                    }))
                })), e) : (0, i.default)(h, e)
            }
        },
        78428: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970),
                a = n.__importStar(r(2459)),
                i = n.__importDefault(r(55325)),
                o = r(26456);

            function u(e) {
                var t = (0, i.default)(),
                    r = t.formatMessage,
                    n = t.textComponent,
                    o = void 0 === n ? a.Fragment : n,
                    u = e.id,
                    s = e.description,
                    l = e.defaultMessage,
                    c = e.values,
                    m = e.children,
                    f = e.tagName,
                    p = void 0 === f ? o : f,
                    d = r({
                        id: u,
                        description: s,
                        defaultMessage: l
                    }, c, {
                        ignoreTag: e.ignoreTag
                    });
                return "function" == typeof m ? m(Array.isArray(d) ? d : [d]) : p ? a.createElement(p, null, a.Children.toArray(d)) : a.createElement(a.Fragment, null, d)
            }
            u.displayName = "FormattedMessage";
            var s = a.memo(u, (function(e, t) {
                var r = e.values,
                    a = n.__rest(e, ["values"]),
                    i = t.values,
                    u = n.__rest(t, ["values"]);
                return (0, o.shallowEqual)(i, r) && (0, o.shallowEqual)(a, u)
            }));
            s.displayName = "MemoizedFormattedMessage", t.default = s
        },
        58647: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970),
                a = n.__importStar(r(2459)),
                i = n.__importDefault(r(55325)),
                o = function(e) {
                    var t = (0, i.default)(),
                        r = t.formatPlural,
                        n = t.textComponent,
                        o = e.value,
                        u = e.other,
                        s = e.children,
                        l = e[r(o, e)] || u;
                    return "function" == typeof s ? s(l) : n ? a.createElement(n, null, l) : l
                };
            o.defaultProps = {
                type: "cardinal"
            }, o.displayName = "FormattedPlural", t.default = o
        },
        16660: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.createIntl = void 0;
            var n = r(22970),
                a = n.__importStar(r(2459)),
                i = r(16714),
                o = r(26456),
                u = r(81007),
                s = r(35341);

            function l(e) {
                return {
                    locale: e.locale,
                    timeZone: e.timeZone,
                    fallbackOnEmptyString: e.fallbackOnEmptyString,
                    formats: e.formats,
                    textComponent: e.textComponent,
                    messages: e.messages,
                    defaultLocale: e.defaultLocale,
                    defaultFormats: e.defaultFormats,
                    onError: e.onError,
                    onWarn: e.onWarn,
                    wrapRichTextChunksInFragment: e.wrapRichTextChunksInFragment,
                    defaultRichTextElements: e.defaultRichTextElements
                }
            }

            function c(e) {
                return e ? Object.keys(e).reduce((function(t, r) {
                    var n = e[r];
                    return t[r] = (0, s.isFormatXMLElementFn)(n) ? (0, o.assignUniqueKeysToParts)(n) : n, t
                }), {}) : e
            }
            var m = function(e, t, r, i) {
                for (var o = [], s = 4; s < arguments.length; s++) o[s - 4] = arguments[s];
                var l = c(i),
                    m = u.formatMessage.apply(void 0, n.__spreadArray([e, t, r, l], o, !1));
                return Array.isArray(m) ? a.Children.toArray(m) : m
            };
            t.createIntl = function(e, t) {
                var r = e.defaultRichTextElements,
                    a = n.__rest(e, ["defaultRichTextElements"]),
                    i = c(r),
                    s = (0, u.createIntl)(n.__assign(n.__assign(n.__assign({}, o.DEFAULT_INTL_CONFIG), a), {
                        defaultRichTextElements: i
                    }), t),
                    l = {
                        locale: s.locale,
                        timeZone: s.timeZone,
                        fallbackOnEmptyString: s.fallbackOnEmptyString,
                        formats: s.formats,
                        defaultLocale: s.defaultLocale,
                        defaultFormats: s.defaultFormats,
                        messages: s.messages,
                        onError: s.onError,
                        defaultRichTextElements: i
                    };
                return n.__assign(n.__assign({}, s), {
                    formatMessage: m.bind(null, l, s.formatters),
                    $t: m.bind(null, l, s.formatters)
                })
            };
            var f = function(e) {
                function r() {
                    var r = null !== e && e.apply(this, arguments) || this;
                    return r.cache = (0, u.createIntlCache)(), r.state = {
                        cache: r.cache,
                        intl: (0, t.createIntl)(l(r.props), r.cache),
                        prevConfig: l(r.props)
                    }, r
                }
                return n.__extends(r, e), r.getDerivedStateFromProps = function(e, r) {
                    var n = r.prevConfig,
                        a = r.cache,
                        i = l(e);
                    return (0, o.shallowEqual)(n, i) ? null : {
                        intl: (0, t.createIntl)(i, a),
                        prevConfig: i
                    }
                }, r.prototype.render = function() {
                    return (0, o.invariantIntlContext)(this.state.intl), a.createElement(i.Provider, {
                        value: this.state.intl
                    }, this.props.children)
                }, r.displayName = "IntlProvider", r.defaultProps = o.DEFAULT_INTL_CONFIG, r
            }(a.PureComponent);
            t.default = f
        },
        76979: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970),
                a = n.__importStar(r(2459)),
                i = r(73619),
                o = n.__importDefault(r(55325)),
                u = 3600;

            function s(e) {
                var t = Math.abs(e);
                return t < 60 ? "second" : t < u ? "minute" : t < 86400 ? "hour" : "day"
            }

            function l(e) {
                switch (e) {
                    case "second":
                        return 1;
                    case "minute":
                        return 60;
                    case "hour":
                        return u;
                    default:
                        return 86400
                }
            }
            var c = ["second", "minute", "hour"];

            function m(e) {
                return void 0 === e && (e = "second"), c.indexOf(e) > -1
            }
            var f = function(e) {
                    var t = (0, o.default)(),
                        r = t.formatRelativeTime,
                        i = t.textComponent,
                        u = e.children,
                        s = r(e.value || 0, e.unit, n.__rest(e, ["children", "value", "unit"]));
                    return "function" == typeof u ? u(s) : i ? a.createElement(i, null, s) : a.createElement(a.Fragment, null, s)
                },
                p = function(e) {
                    var t = e.value,
                        r = e.unit,
                        o = e.updateIntervalInSeconds,
                        c = n.__rest(e, ["value", "unit", "updateIntervalInSeconds"]);
                    (0, i.invariant)(!o || !(!o || !m(r)), "Cannot schedule update with unit longer than hour");
                    var p, d = a.useState(),
                        h = d[0],
                        g = d[1],
                        v = a.useState(0),
                        y = v[0],
                        E = v[1],
                        b = a.useState(0),
                        _ = b[0],
                        D = b[1];
                    r === h && t === y || (E(t || 0), g(r), D(m(r) ? function(e, t) {
                        if (!e) return 0;
                        switch (t) {
                            case "second":
                                return e;
                            case "minute":
                                return 60 * e;
                            default:
                                return e * u
                        }
                    }(t, r) : 0)), a.useEffect((function() {
                        function e() {
                            clearTimeout(p)
                        }
                        if (e(), !o || !m(r)) return e;
                        var t = _ - o,
                            n = s(t);
                        if ("day" === n) return e;
                        var a = l(n),
                            i = t - t % a,
                            u = i >= _ ? i - a : i,
                            c = Math.abs(u - _);
                        return _ !== u && (p = setTimeout((function() {
                            return D(u)
                        }), 1e3 * c)), e
                    }), [_, o, r]);
                    var F = t || 0,
                        S = r;
                    if (m(r) && "number" == typeof _ && o) {
                        var T = l(S = s(_));
                        F = Math.round(_ / T)
                    }
                    return a.createElement(f, n.__assign({
                        value: F,
                        unit: S
                    }, c))
                };
            p.displayName = "FormattedRelativeTime", p.defaultProps = {
                value: 0,
                unit: "second"
            }, t.default = p
        },
        55325: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970).__importStar(r(2459)),
                a = r(16714),
                i = r(26456);
            t.default = function() {
                var e = n.useContext(a.Context);
                return (0, i.invariantIntlContext)(e), e
            }
        },
        26456: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.shallowEqual = t.assignUniqueKeysToParts = t.DEFAULT_INTL_CONFIG = t.invariantIntlContext = void 0;
            var n = r(22970),
                a = n.__importStar(r(2459)),
                i = r(73619),
                o = r(81007);
            t.invariantIntlContext = function(e) {
                (0, i.invariant)(e, "[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.")
            }, t.DEFAULT_INTL_CONFIG = n.__assign(n.__assign({}, o.DEFAULT_INTL_CONFIG), {
                textComponent: a.Fragment
            }), t.assignUniqueKeysToParts = function(e) {
                return function(t) {
                    return e(a.Children.toArray(t))
                }
            }, t.shallowEqual = function(e, t) {
                if (e === t) return !0;
                if (!e || !t) return !1;
                var r = Object.keys(e),
                    n = Object.keys(t),
                    a = r.length;
                if (n.length !== a) return !1;
                for (var i = 0; i < a; i++) {
                    var o = r[i];
                    if (e[o] !== t[o] || !Object.prototype.hasOwnProperty.call(t, o)) return !1
                }
                return !0
            }
        }
    }
]);
//# sourceMappingURL=3162.0b534480e317091e.js.map