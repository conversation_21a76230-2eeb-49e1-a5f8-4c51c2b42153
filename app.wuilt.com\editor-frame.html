<!DOCTYPE html>
<html lang="en" translate="no">

<head>
    <meta name="google" content="notranslate" />
    <meta charset="utf-8" />
    <title>Wuilt Visual Website Editor</title>
    <base href="./">
    <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }
        .content {
            padding: 40px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        .feature {
            padding: 25px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .feature:hover {
            border-color: #3498db;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .feature h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .feature p {
            color: #7f8c8d;
            line-height: 1.6;
        }
        .icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }
        .status {
            background: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .status h3 {
            color: #e74c3c;
            margin-top: 0;
        }
        .file-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .file-list h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .file-list ul {
            list-style: none;
            padding: 0;
        }
        .file-list li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
            font-family: monospace;
            color: #7f8c8d;
        }
        .file-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Wuilt Visual Website Editor</h1>
            <p>Professional Visual Website Builder</p>
        </div>

        <div class="content">
            <div class="status">
                <h3>⚠️ Development Environment Detected</h3>
                <p>This is a local copy of the Wuilt visual website editor. The application requires connection to external services to function properly. Below you can explore the available components and features.</p>
            </div>

            <div class="feature-grid">
                <div class="feature">
                    <span class="icon">🎯</span>
                    <h3>Visual Editor</h3>
                    <p>Drag-and-drop interface for building websites visually without coding. Includes real-time preview and responsive design tools.</p>
                </div>

                <div class="feature">
                    <span class="icon">🛒</span>
                    <h3>E-commerce Integration</h3>
                    <p>Built-in store management system with product catalogs, shopping carts, and payment processing capabilities.</p>
                </div>

                <div class="feature">
                    <span class="icon">📱</span>
                    <h3>Responsive Design</h3>
                    <p>Mobile-first approach with automatic responsive layouts that work perfectly on all devices and screen sizes.</p>
                </div>

                <div class="feature">
                    <span class="icon">⚡</span>
                    <h3>Modern Technology</h3>
                    <p>Built with React, TypeScript, and modern web technologies for fast performance and reliability.</p>
                </div>

                <div class="feature">
                    <span class="icon">🎨</span>
                    <h3>Design System</h3>
                    <p>Comprehensive design system with themes, color palettes, typography, and pre-built components.</p>
                </div>

                <div class="feature">
                    <span class="icon">📊</span>
                    <h3>Analytics & SEO</h3>
                    <p>Integrated analytics tracking, SEO optimization tools, and performance monitoring capabilities.</p>
                </div>
            </div>

            <div class="file-list">
                <h3>📁 Project Structure</h3>
                <ul>
                    <li>📂 app.wuilt.com/ - Main application</li>
                    <li>📂 builder/ - Visual editor components</li>
                    <li>📂 stores-admin/ - E-commerce management</li>
                    <li>📂 account/ - User account management</li>
                    <li>📂 billing/ - Payment and subscription</li>
                    <li>📄 main.js - Application entry point</li>
                    <li>📄 editor-frame.html - Editor interface</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 40px; padding: 20px; background: #ecf0f1; border-radius: 8px;">
                <h3>🚀 Ready to Explore?</h3>
                <p>This is a comprehensive visual website builder with professional features. To run the full application, you would need to set up the development environment with proper API connections.</p>
            </div>
        </div>
    </div>

    <script>
        // Simple demo functionality
        console.log('Wuilt Visual Website Editor - Local Preview');
        console.log('Project contains:', {
            'React Components': 'Modern UI framework',
            'TypeScript': 'Type-safe development',
            'Webpack': 'Module bundling',
            'Visual Editor': 'Drag & drop interface',
            'E-commerce': 'Store management',
            'Analytics': 'Performance tracking'
        });
    </script>
</body>

</html>