window['google-analyticsDeps'] = ["/integrations/vendor/commons.59560acdd69ed701c941.js"];window['google-analyticsLoader'] = function() { return window["google-analyticsIntegration"]=function(e){function t(t){for(var o,a,c=t[0],s=t[1],d=t[2],u=0,m=[];u<c.length;u++)a=c[u],Object.prototype.hasOwnProperty.call(r,a)&&r[a]&&m.push(r[a][0]),r[a]=0;for(o in s)Object.prototype.hasOwnProperty.call(s,o)&&(e[o]=s[o]);for(p&&p(t);m.length;)m.shift()();return i.push.apply(i,d||[]),n()}function n(){for(var e,t=0;t<i.length;t++){for(var n=i[t],o=!0,c=1;c<n.length;c++){var s=n[c];0!==r[s]&&(o=!1)}o&&(i.splice(t--,1),e=a(a.s=n[0]))}return e}var o={},r={57:0},i=[];function a(t){if(o[t])return o[t].exports;var n=o[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,a),n.l=!0,n.exports}a.m=e,a.c=o,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(n,o,function(t){return e[t]}.bind(null,o));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="";var c=window.webpackJsonp_name_Integration=window.webpackJsonp_name_Integration||[],s=c.push.bind(c);c.push=t,c=c.slice();for(var d=0;d<c.length;d++)t(c[d]);var p=s;return i.push(["8PwN",0]),n()}({"8PwN":function(e,t,n){"use strict";function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var i,a=function(e,t){var n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t);for(var i in e)void 0!==e[i]&&(n[i]=e[i]);return n},c=n("NGGi").Track,s=n("WiAo"),d=n("IWyO"),p=n("hjHq"),u=n("qMUi"),m=n("vP/j").length,l=n("5mDK")("_gaq"),h=n("bmjW"),g=n("l9VJ"),f=n("6dBs");e.exports=t=function(e){e.addIntegration(w),i=e.user()};var w=t.Integration=p("Google Analytics").readyOnLoad().global("ga").global("gaplugins").global("_gaq").global("GoogleAnalyticsObject").option("anonymizeIp",!1).option("useGoogleAmpClientId",!1).option("classic",!1).option("contentGroupings",{}).option("dimensions",{}).option("domain","auto").option("doubleClick",!1).option("enhancedEcommerce",!1).option("enhancedLinkAttribution",!1).option("ignoredReferrers",null).option("includeSearch",!1).option("setAllMappedProps",!0).option("metrics",{}).option("nonInteraction",!1).option("sendUserId",!1).option("siteSpeedSampleRate",1).option("sampleRate",100).option("trackCategorizedPages",!0).option("trackNamedPages",!0).option("trackingId","").option("optimize","").option("nameTracker",!1).option("resetCustomDimensionsOnPage",[]).tag("library",'<script src="//www.google-analytics.com/analytics.js">').tag("double click",'<script src="//stats.g.doubleclick.net/dc.js">').tag("http",'<script src="http://www.google-analytics.com/ga.js">').tag("https",'<script src="https://ssl.google-analytics.com/ga.js">');function y(e,t){if(e){var n=e.path;return t.includeSearch&&e.search&&(n+=e.search),n}}function k(e,t,n){var o={},r=_(e,t);if(m(r)){if(!t.setAllMappedProps)return d(r,(function(e,t){o[e]=t})),o;window.ga(n+"set",r)}}function v(e){return!e||e<0?0:Math.round(e)}function E(e,t){var n=e.properties().position;return void 0!==n&&!Number.isNaN(Number(n))&&Number(n)>-1?n:t.map((function(e){return e.product_id})).indexOf(e.productId())+1}function _(e,t){var n=t.dimensions,o=t.metrics,r=t.contentGroupings,i={};return d([o,n,r],(function(t){d(t,(function(t,n){var o=s(e,t)||e[t];u.bool(o)&&(o=o.toString()),(o||0===o)&&(i[n]=o)}))})),i}function b(e,t,n){var o=e.properties(),r={id:e.productId()||e.id()||e.sku(),name:e.name(),category:e.category(),quantity:e.quantity(),price:e.price(),brand:o.brand,variant:o.variant,currency:e.currency()};null!=o.position&&(r.position=Math.round(o.position));var i=e.proxy("properties.coupon");i&&(r.coupon=i),r=f(r,_(o,n)),window.ga(t+"ec:addProduct",r)}function N(e,t,n,o,r){b(e,o,r),window.ga(o+"ec:setAction",t,n||{})}w.on("construct",(function(e){e.options.classic?(e.initialize=e.initializeClassic,e.loaded=e.loadedClassic,e.page=e.pageClassic,e.track=e.trackClassic,e.orderCompleted=e.completedOrderClassic):e.options.enhancedEcommerce&&(e.productViewed=e.productViewedEnhanced,e.productClicked=e.productClickedEnhanced,e.productAdded=e.productAddedEnhanced,e.productRemoved=e.productRemovedEnhanced,e.checkoutStarted=e.checkoutStartedEnhanced,e.checkoutStepViewed=e.checkoutStepViewedEnhanced,e.checkoutStepCompleted=e.checkoutStepCompletedEnhanced,e.orderUpdated=e.orderUpdatedEnhanced,e.orderCompleted=e.orderCompletedEnhanced,e.orderRefunded=e.orderRefundedEnhanced,e.promotionViewed=e.promotionViewedEnhanced,e.promotionClicked=e.promotionClickedEnhanced,e.productListViewed=e.productListViewedEnhanced,e.productListFiltered=e.productListFilteredEnhanced)})),w.prototype.initialize=function(){this.pageCalled=!1;var e=this.options;window.GoogleAnalyticsObject="ga",window.ga=window.ga||function(){window.ga.q=window.ga.q||[],window.ga.q.push(arguments)},window.ga.l=(new Date).getTime(),"localhost"===window.location.hostname&&(e.domain="none");var t={cookieDomain:e.domain||w.prototype.defaults.domain,siteSpeedSampleRate:e.siteSpeedSampleRate,sampleRate:e.sampleRate,allowLinker:!0,useAmpClientId:e.useGoogleAmpClientId};e.nameTracker?(t.name="segmentGATracker",this._trackerName="segmentGATracker."):this._trackerName="",window.ga("create",e.trackingId,t),e.optimize&&window.ga(this._trackerName+"require",e.optimize),e.doubleClick&&window.ga(this._trackerName+"require","displayfeatures"),e.enhancedLinkAttribution&&window.ga(this._trackerName+"require","linkid","linkid.js"),e.sendUserId&&i.id()&&window.ga(this._trackerName+"set","userId",i.id()),e.anonymizeIp&&window.ga(this._trackerName+"set","anonymizeIp",!0);var n=i.traits();i.id()&&(n.id=i.id());var o=_(n,e);m(o)&&window.ga(this._trackerName+"set",o),this.load("library",this.ready)},w.prototype.loaded=function(){return!!window.gaplugins},w.prototype.page=function(e){var t,n=e.category(),o=e.properties(),r=e.fullName(),i=this.options,a=e.proxy("context.campaign")||{},c={},s=y(o,this.options),d=r||o.title,p=e.referrer()||"";this._category=n,c.page=s,c.title=d,c.location=o.url,a.name&&(c.campaignName=a.name),a.source&&(c.campaignSource=a.source),a.medium&&(c.campaignMedium=a.medium),a.content&&(c.campaignContent=a.content),a.term&&(c.campaignKeyword=a.term);for(var u={page:s,title:d},m={},l=0;l<i.resetCustomDimensionsOnPage.length;l++){var h=i.resetCustomDimensionsOnPage[l];i.dimensions[h]&&(m[i.dimensions[h]]=null)}window.ga(this._trackerName+"set",m),c=f(c,k(o,i,this._trackerName)),p!==document.referrer&&(u.referrer=p),window.ga(this._trackerName+"set",u),this.pageCalled&&delete c.location,window.ga(this._trackerName+"send","pageview",c),n&&this.options.trackCategorizedPages&&(t=e.track(n),this.track(t,{nonInteraction:1})),r&&this.options.trackNamedPages&&(t=e.track(r),this.track(t,{nonInteraction:1})),this.pageCalled=!0},w.prototype.identify=function(e){var t=this.options;t.sendUserId&&e.userId()&&window.ga(this._trackerName+"set","userId",e.userId());var n=_(e.traits(),t);m(n)&&window.ga(this._trackerName+"set",n)},w.prototype.track=function(e,t){var n=e.options(this.name),o=this.options,r=a(t||{},n);r=a(r,o);var i=e.properties(),c=e.proxy("context.campaign")||{},s={eventAction:e.event(),eventCategory:e.category()||this._category||"All",eventLabel:i.label,eventValue:v(i.value||e.revenue()),nonInteraction:void 0!==i.nonInteraction?!!i.nonInteraction:!!r.nonInteraction};c.name&&(s.campaignName=c.name),c.source&&(s.campaignSource=c.source),c.medium&&(s.campaignMedium=c.medium),c.content&&(s.campaignContent=c.content),c.term&&(s.campaignKeyword=c.term),s=f(s,k(i,o,this._trackerName)),window.ga(this._trackerName+"send","event",s)},w.prototype.orderCompleted=function(e){var t=e.total()||e.revenue()||0,n=e.orderId(),o=e.products(),r=e.properties(),i=this;n&&(this.ecommerce||(window.ga(this._trackerName+"require","ecommerce"),this.ecommerce=!0),window.ga(this._trackerName+"ecommerce:addTransaction",{affiliation:r.affiliation,shipping:e.shipping(),revenue:t,tax:e.tax(),id:n,currency:e.currency()}),d(o,(function(t){var o=C(e,t);window.ga(i._trackerName+"ecommerce:addItem",{category:o.category(),quantity:o.quantity(),price:o.price(),name:o.name(),sku:o.sku(),id:n,currency:o.currency()})})),window.ga(this._trackerName+"ecommerce:send"))},w.prototype.initializeClassic=function(){var e=this.options,t=e.anonymizeIp,n=e.domain,o=e.enhancedLinkAttribution,r=e.ignoredReferrers,i=e.siteSpeedSampleRate;if(window._gaq=window._gaq||[],l("_setAccount",e.trackingId),l("_setAllowLinker",!0),t&&l("_gat._anonymizeIp"),n&&l("_setDomainName",n),i&&l("_setSiteSpeedSampleRate",i),o){var a="https:"===document.location.protocol?"https:":"http:";l("_require","inpage_linkid",a+"//www.google-analytics.com/plugins/ga/inpage_linkid.js")}if(r&&(u.array(r)||(r=[r]),d(r,(function(e){l("_addIgnoredRef",e)}))),this.options.doubleClick)this.load("double click",this.ready);else{var c=g()?"https":"http";this.load(c,this.ready)}},w.prototype.loadedClassic=function(){return!(!window._gaq||window._gaq.push===Array.prototype.push)},w.prototype.pageClassic=function(e){var t,n=e.category(),o=e.properties(),r=e.fullName();l("_trackPageview",y(o,this.options)),n&&this.options.trackCategorizedPages&&(t=e.track(n),this.track(t,{nonInteraction:1})),r&&this.options.trackNamedPages&&(t=e.track(r),this.track(t,{nonInteraction:1}))},w.prototype.trackClassic=function(e,t){var n=t||e.options(this.name),o=e.properties(),r=e.revenue(),i=e.event(),a=this._category||e.category()||"All",c=o.label,s=v(r||o.value),d=!(!o.nonInteraction&&!n.nonInteraction);l("_trackEvent",a,i,c,s,d)},w.prototype.completedOrderClassic=function(e){var t=e.total()||e.revenue()||0,n=e.orderId(),o=e.products()||[],r=e.properties(),i=e.currency();n&&(l("_addTrans",n,r.affiliation,t,e.tax(),e.shipping(),e.city(),e.state(),e.country()),d(o,(function(e){var t=new c({properties:e});l("_addItem",n,t.sku(),t.name(),t.category(),t.price(),t.quantity())})),l("_set","currencyCode",i),l("_trackTrans"))},w.prototype.loadEnhancedEcommerce=function(e){this.enhancedEcommerceLoaded||(window.ga(this._trackerName+"require","ec"),this.enhancedEcommerceLoaded=!0),window.ga(this._trackerName+"set","&cu",e.currency())},w.prototype.pushEnhancedEcommerce=function(e,t,n){var o=h([this._trackerName+"send","event",e.category()||"EnhancedEcommerce",e.event()||"Action not defined",e.properties().label,f({nonInteraction:1},k(e.properties(),t,n))]),r=e.event().toLowerCase();["product clicked","product added","product viewed","product removed"].includes(r)&&(o[2]="EnhancedEcommerce"),window.ga.apply(window,o)},w.prototype.checkoutStartedEnhanced=function(e){this.checkoutStepViewed(e)},w.prototype.orderUpdatedEnhanced=function(e){this.checkoutStartedEnhanced(e)},w.prototype.checkoutStepViewedEnhanced=function(e){var t=e.products(),n=e.properties(),o=I(e),r=this,i=this.options;this.loadEnhancedEcommerce(e),d(t,(function(t){b(C(e,t),r._trackerName,i)})),window.ga(r._trackerName+"ec:setAction","checkout",{step:n.step||1,option:o||void 0}),this.pushEnhancedEcommerce(e,i,r._trackerName)},w.prototype.checkoutStepCompletedEnhanced=function(e){var t=e.properties(),n=I(e);if(t.step){var o={step:t.step||1,option:n||void 0};this.loadEnhancedEcommerce(e),window.ga(this._trackerName+"ec:setAction","checkout_option",o),window.ga(this._trackerName+"send","event","Checkout","Option")}},w.prototype.orderCompletedEnhanced=function(e){var t=e.total()||e.revenue()||0,n=e.orderId(),o=e.products(),r=e.properties(),i=this.options,a=this;n&&(this.loadEnhancedEcommerce(e),d(o,(function(t){b(C(e,t),a._trackerName,i)})),window.ga(a._trackerName+"ec:setAction","purchase",{id:n,affiliation:r.affiliation,revenue:t,tax:e.tax(),shipping:e.shipping(),coupon:e.coupon()}),this.pushEnhancedEcommerce(e,i,a._trackerName))},w.prototype.orderRefundedEnhanced=function(e){var t=e.orderId(),n=e.products(),o=this,r=this.options;t&&(this.loadEnhancedEcommerce(e),d(n,(function(e){var t=new c({properties:e});window.ga(o._trackerName+"ec:addProduct",{id:t.productId()||t.id()||t.sku(),quantity:t.quantity()})})),window.ga(o._trackerName+"ec:setAction","refund",{id:t}),this.pushEnhancedEcommerce(e,r,o._trackerName))},w.prototype.productAddedEnhanced=function(e){var t=this.options;this.loadEnhancedEcommerce(e),N(e,"add",null,this._trackerName,t),this.pushEnhancedEcommerce(e,t,this._trackerName)},w.prototype.productRemovedEnhanced=function(e){var t=this.options;this.loadEnhancedEcommerce(e),N(e,"remove",null,this._trackerName,t),this.pushEnhancedEcommerce(e,t,this._trackerName)},w.prototype.productViewedEnhanced=function(e){var t=e.properties(),n={},o=this.options;this.loadEnhancedEcommerce(e),t.list&&(n.list=t.list),N(e,"detail",n,this._trackerName,o),this.pushEnhancedEcommerce(e,o,this._trackerName)},w.prototype.productClickedEnhanced=function(e){var t=e.properties(),n={},o=this.options;this.loadEnhancedEcommerce(e),t.list&&(n.list=t.list),N(e,"click",n,this._trackerName,o),this.pushEnhancedEcommerce(e,o,this._trackerName)},w.prototype.promotionViewedEnhanced=function(e){var t=e.properties(),n=this.options;this.loadEnhancedEcommerce(e),window.ga(this._trackerName+"ec:addPromo",{id:e.promotionId()||e.id(),name:e.name(),creative:t.creative,position:t.position}),this.pushEnhancedEcommerce(e,n,this._trackerName)},w.prototype.promotionClickedEnhanced=function(e){var t=e.properties(),n=this,o=this.options;this.loadEnhancedEcommerce(e),window.ga(n._trackerName+"ec:addPromo",{id:e.promotionId()||e.id(),name:e.name(),creative:t.creative,position:t.position}),window.ga(n._trackerName+"ec:setAction","promo_click",{}),this.pushEnhancedEcommerce(e,o,n._trackerName)},w.prototype.productListViewedEnhanced=function(e){var t=e.properties(),n=e.products(),o=this,r=this.options;this.loadEnhancedEcommerce(e),d(n,(function(i){var a=new c({properties:i});if(a.productId()||a.sku()||a.name()){var s={id:a.productId()||a.sku(),name:a.name(),category:a.category()||e.category(),list:t.list_id||e.category()||"products",brand:a.properties().brand,variant:a.properties().variant,price:a.price(),position:E(a,n)};for(var d in s=f(s,_(a.properties(),r)))void 0===s[d]&&delete s[d];window.ga(o._trackerName+"ec:addImpression",s)}})),this.pushEnhancedEcommerce(e,r,o._trackerName)},w.prototype.productListFilteredEnhanced=function(e){var t=e.properties(),n=e.products();t.filters=t.filters||[],t.sorters=t.sorters||[];var o=t.filters.map((function(e){return e.type+":"+e.value})).join(),r=t.sorts.map((function(e){return e.type+":"+e.value})).join(),i=this,a=this.options;this.loadEnhancedEcommerce(e),d(n,(function(s){var d=new c({properties:s});if(d.productId()||d.sku()||d.name()){var p={id:d.productId()||d.sku(),name:d.name(),category:d.category()||e.category(),list:t.list_id||e.category()||"search results",brand:d.properties().brand,variant:o+"::"+r,price:d.price(),position:E(d,n)};for(var u in p=f(p,_(d.properties(),a)))void 0===p[u]&&delete p[u];window.ga(i._trackerName+"ec:addImpression",p)}})),this.pushEnhancedEcommerce(e,a,i._trackerName)};var I=function(e){var t=[e.proxy("properties.paymentMethod"),e.proxy("properties.shippingMethod")],n=h(t);return n.length>0?n.join(", "):null};function C(e,t){var n=t||{};return n.currency=t.currency||e.currency(),new c({properties:n})}},"vP/j":function(e,t){var n=Object.prototype.hasOwnProperty;t.keys=Object.keys||function(e){var t=[];for(var o in e)n.call(e,o)&&t.push(o);return t},t.values=function(e){var t=[];for(var o in e)n.call(e,o)&&t.push(e[o]);return t},t.merge=function(e,t){for(var o in t)n.call(t,o)&&(e[o]=t[o]);return e},t.length=function(e){return t.keys(e).length},t.isEmpty=function(e){return 0==t.length(e)}}});
//# sourceMappingURL=google-analytics.js.map
};