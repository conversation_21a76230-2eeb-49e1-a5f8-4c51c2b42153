(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["packages_quilt_src_components_icons_CheckIcon_tsx"], {

        /***/
        "../../packages/quilt/src/components/Icon/Icon.tsx":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    Icon: () => ( /* binding */ Icon),
                    /* harmony export */
                    getSize: () => ( /* binding */ getSize)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?2a4b");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("webpack/sharing/consume/default/styled-components/styled-components?d27b");
                /* harmony import */
                var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/ __webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);
                /* harmony import */
                var _themes_property_overriding__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../packages/quilt/src/themes/property-overriding/colors.ts");
                /* harmony import */
                var _consts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../packages/quilt/src/components/Icon/consts.ts");
                /* harmony import */
                var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/react/jsx-dev-runtime.js");
                var _jsxFileName = "/home/<USER>/work/wuilt-client/wuilt-client/packages/quilt/src/components/Icon/Icon.tsx";






                const getSize = ({
                    size
                }) => {
                    return _consts__WEBPACK_IMPORTED_MODULE_3__.ICON_SIZES[size] || _consts__WEBPACK_IMPORTED_MODULE_3__.ICON_SIZES.md;
                };
                const styles = ({
                    width,
                    height,
                    customColor,
                    reverseOnRtl,
                    theme,
                    rotate: _rotate = 0
                }) => (0, styled_components__WEBPACK_IMPORTED_MODULE_1__.css)(["width:", ";height:", ";flex-shrink:0;vertical-align:middle;fill:currentColor;", " ", ";transform:rotate(", "deg) ", ";"], width || getSize, height || getSize, _themes_property_overriding__WEBPACK_IMPORTED_MODULE_4__.Color, customColor && `color:${customColor}`, _rotate, reverseOnRtl && theme.rtl && "scale(-1, 1)");
                const StyledIcon = /*#__PURE__*/ styled_components__WEBPACK_IMPORTED_MODULE_1___default()(({
                    Component,
                    className,
                    viewBox,
                    dataTest,
                    children,
                    ariaHidden,
                    ariaLabel,
                    title,
                    svgString
                }) => {
                    return (
                        /*#__PURE__*/
                        // eslint-disable-next-line react/no-danger-with-children
                        (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(Component, {
                            className: className,
                            viewBox: viewBox,
                            "data-test": dataTest,
                            preserveAspectRatio: "xMidYMid meet",
                            "aria-hidden": ariaHidden ? "true" : undefined,
                            "aria-label": ariaLabel,
                            dangerouslySetInnerHTML: svgString ? {
                                __html: svgString
                            } : undefined,
                            title: svgString ? title : undefined,
                            children: children ? /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {
                                children: [title && /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)("title", {
                                    children: title
                                }, void 0, false, {
                                    fileName: _jsxFileName,
                                    lineNumber: 54,
                                    columnNumber: 25
                                }, undefined), children]
                            }, void 0, true) : undefined
                        }, void 0, false, {
                            fileName: _jsxFileName,
                            lineNumber: 42,
                            columnNumber: 7
                        }, undefined)
                    );
                }).withConfig({
                    displayName: "Icon__StyledIcon",
                    componentId: "sc-178dh1b-0"
                })(["", " svg{", "}"], styles, styles);
                const Icon = props => {
                    const {
                        size = "md",
                            width = props.svgString ? "auto" : props.width,
                            height = props.svgString ? "auto" : props.height,
                            color,
                            customColor,
                            className,
                            children,
                            viewBox,
                            dataTest,
                            ariaHidden,
                            reverseOnRtl,
                            ariaLabel,
                            title,
                            svgString,
                            rotate
                    } = props;
                    return /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(StyledIcon, {
                        Component: svgString ? "span" : "svg",
                        svgString: svgString,
                        viewBox: viewBox,
                        size: size,
                        width: width,
                        height: height,
                        className: className,
                        dataTest: dataTest,
                        customColor: customColor,
                        color: color,
                        ariaHidden: ariaHidden,
                        reverseOnRtl: reverseOnRtl,
                        ariaLabel: ariaLabel,
                        title: title,
                        rotate: rotate,
                        children: children
                    }, void 0, false, {
                        fileName: _jsxFileName,
                        lineNumber: 105,
                        columnNumber: 5
                    }, undefined);
                };
                Icon.defaultProps = {
                    size: "md"
                };


                /***/
            }),

        /***/
        "../../packages/quilt/src/components/Icon/consts.ts":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    ICON_SIZES: () => ( /* binding */ ICON_SIZES)
                    /* harmony export */
                });
                const ICON_SIZES = {
                    xxxs: "8px",
                    xxs: "10px",
                    xs: "12px",
                    sm: "14px",
                    md: "16px",
                    lg: "18px",
                    xl: "20px",
                    xxl: "24px",
                    xxxl: "28px",
                    xxxxl: "38px"
                };

                /***/
            }),

        /***/
        "../../packages/quilt/src/components/Icon/createIcon.tsx":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?2a4b");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _whiteListProps__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../packages/quilt/src/components/Icon/whiteListProps.ts");
                /* harmony import */
                var _Icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../packages/quilt/src/components/Icon/Icon.tsx");
                /* harmony import */
                var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/react/jsx-dev-runtime.js");
                var _jsxFileName = "/home/<USER>/work/wuilt-client/wuilt-client/packages/quilt/src/components/Icon/createIcon.tsx";




                const createIcon = (def, viewBox, displayName) => {
                    const icon = props => /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Icon__WEBPACK_IMPORTED_MODULE_2__.Icon, Object.assign({
                        viewBox: viewBox
                    }, (0, _whiteListProps__WEBPACK_IMPORTED_MODULE_3__["default"])(props), {
                        children: def
                    }), void 0, false, {
                        fileName: _jsxFileName,
                        lineNumber: 11,
                        columnNumber: 5
                    }, undefined);
                    icon.displayName = displayName;
                    return icon;
                };
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (createIcon);

                /***/
            }),

        /***/
        "../../packages/quilt/src/components/Icon/whiteListProps.ts":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                const ALLOWED_PROPS = ["size", "width", "height", "color", "customColor", "className", "dataTest", "ariaHidden", "reverseOnRtl", "ariaLabel", "dataTest", "title", "rotate", "viewBox"];
                const whiteListProps = props => Object.assign({}, ...ALLOWED_PROPS.map(k => k in props ? {
                    [k]: props[k]
                } : {}));
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (whiteListProps);

                /***/
            }),

        /***/
        "../../packages/quilt/src/components/icons/CheckIcon.tsx":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    CheckIcon: () => ( /* binding */ CheckIcon)
                    /* harmony export */
                });
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/react/react?2a4b");
                /* harmony import */
                var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _Icon_createIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../packages/quilt/src/components/Icon/createIcon.tsx");
                /* harmony import */
                var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/react/jsx-dev-runtime.js");
                var _jsxFileName = "/home/<USER>/work/wuilt-client/wuilt-client/packages/quilt/src/components/icons/CheckIcon.tsx";



                const CheckIcon = (0, _Icon_createIcon__WEBPACK_IMPORTED_MODULE_2__["default"])( /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("path", {
                    d: "M0 0h24v24H0V0z",
                    fill: "none"
                }, void 0, false, {
                    fileName: _jsxFileName,
                    lineNumber: 7,
                    columnNumber: 5
                }, undefined), /*#__PURE__*/ (0, react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("path", {
                    d: "M9 16.17 5.53 12.7a.996.996 0 1 0-1.41 1.41l4.18 4.18c.39.39 1.02.39 1.41 0L20.29 7.71a.996.996 0 1 0-1.41-1.41L9 16.17z"
                }, void 0, false, {
                    fileName: _jsxFileName,
                    lineNumber: 8,
                    columnNumber: 5
                }, undefined)), "0 0 24 24", "CheckIcon");

                /***/
            }),

        /***/
        "../../packages/quilt/src/themes/original.ts":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    base: () => ( /* binding */ base),
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    palette: () => ( /* binding */ palette)
                    /* harmony export */
                });
                const palette = {
                    product: {
                        light: "#ECF8F7",
                        lightHover: "#D6F0EE",
                        lightActive: "#C0E8E4",
                        normal: "#00A991",
                        normalHover: "#009882",
                        normalActive: "#008F7B",
                        dark: "#0F766E",
                        darkHover: "#007060",
                        darkActive: "#006657",
                        darker: "#005C4E",
                        critical: "#fd5f60",
                        criticalActive: "#fd5f60",
                        criticalHover: "#fd5f60",
                        primarySubtle: "#447FA0",
                        primarySubtleActive: "#447FA0",
                        primarySubtleHover: "#447FA0"
                    },
                    white: {
                        normal: "#FFFFFF",
                        normalHover: "#F1F4F7",
                        normalActive: "#E7ECF1"
                    },
                    black: {
                        normal: "#000000",
                        normalHover: "#222222",
                        normalActive: "#333333"
                    },
                    transparent: {
                        normal: "transparent",
                        normalHover: "transparent",
                        normalActive: "transparent"
                    },
                    cloud: {
                        lighter: "#F5F5F5",
                        light: "#F5F7F9",
                        lightHover: "#E5EAEF",
                        lightActive: "#D6DEE6",
                        normal: "#EFF2F5",
                        normalHover: "#DCE3E9",
                        normalActive: "#CAD4DE",
                        dark: "#E8EDF1",
                        darker: "#DFE3E8",
                        darkest: "#E2E8F0",
                        darkActive: "#EAECF0"
                    },
                    ink: {
                        lighter: "#BAC7D5",
                        lighterHover: "#A6B6C8",
                        lighterActive: "#94A8BE",
                        light: "#5F738C",
                        // #637381 darker,
                        lightHover: "#52647A",
                        lightActive: "#465567",
                        normal: "#252A31",
                        normalHover: "#181B20",
                        normalActive: "#0B0C0F"
                    },
                    orange: {
                        light: "#FDF0E3",
                        lightHover: "#FAE2C7",
                        lightActive: "#F8D3AB",
                        normal: "#E98305",
                        normalHover: "#DC7C05",
                        normalActive: "#CD7304",
                        dark: "#A25100",
                        darkHover: "#8F4700",
                        darkActive: "#753A00",
                        darker: "#803F00",
                        gold: "#FCD34D",
                        lightGold: "#FFFEF8"
                    },
                    red: {
                        light: "#FAEAEA",
                        lightHover: "#F4D2D2",
                        lightActive: "#EEB9B9",
                        normal: "#D21C1C",
                        normalHover: "#B91919",
                        normalActive: "#9D1515",
                        dark: "#970C0C",
                        darkHover: "#890B0B",
                        darkActive: "#6D0909",
                        darker: "#760909"
                    },
                    green: {
                        light: "#f0fdf9",
                        lightHover: "#D7EAD9",
                        lightActive: "#C3DFC7",
                        normal: "#28A138",
                        normalHover: "#238B31",
                        normalActive: "#1D7228",
                        dark: "#2B7336",
                        darkHover: "#25642F",
                        darkActive: "#1F5126",
                        darker: "#235C2B"
                    },
                    blue: {
                        light: "#E8F4FD",
                        lightHover: "#D0E9FB",
                        lightActive: "#B4DBF8",
                        normal: "#0172CB",
                        normalHover: "#0161AC",
                        normalActive: "#01508E",
                        dark: "#005AA3",
                        darkHover: "#004F8F",
                        darkActive: "#003E70",
                        darker: "#004680"
                    },
                    pink: {
                        lighter: "#FEF6FB",
                        light: "#FDF2FA",
                        lightHover: "#FCE7F6",
                        lightActive: "#FCE7F6",
                        normal: "#FAA7E0",
                        normalHover: "#EE46BC",
                        normalActive: "#EE46BC",
                        dark: "#DD2590",
                        darkHover: "#C11574",
                        darkActive: "#9E165F",
                        darker: "#851651"
                    },
                    gray: {
                        25: "#FCFCFD",
                        50: "#F9FAFB",
                        100: "#F2F4F7",
                        200: "#EAECF0",
                        300: "#D0D5DD",
                        normal: "#98A2B3",
                        500: "#667085",
                        600: "#475467",
                        700: "#344054",
                        800: "#1D2939",
                        900: "#101828",
                        950: "#0C111D"
                    }
                };
                const base = {
                    fontFamily: `Inter, Almarai`,
                    fontSize: {
                        xxxs: "8px",
                        xxs: "10px",
                        xs: "12px",
                        sm: "14px",
                        md: "16px",
                        md2: "18px",
                        lg: "20px",
                        xl: "24px",
                        xxl: "28px",
                        xxl2: "30px",
                        xxxl: "36px"
                    },
                    fontWeight: {
                        normal: "400",
                        medium: "500",
                        semiBold: "600",
                        bold: "700"
                    },
                    boxShadow: {
                        xxxs: "0px 1px 2px rgba(16, 24, 40, 0.05)",
                        xxs: "0px 1px 3px 0px rgba(63, 63, 68, 0.15), 0px 0px 0px 1px rgba(63, 63, 68, 0.05)",
                        xs: "0px 0px 1px rgba(26, 32, 36, 0.32), 0px 1px 2px rgba(91, 104, 113, 0.32)",
                        sm: "0px 0px 1px rgba(26, 32, 36, 0.32), 0px 4px 8px rgba(91, 104, 113, 0.24)",
                        md: "0px 0px 1px rgba(26, 32, 36, 0.32), 0px 8px 16px rgba(91, 104, 113, 0.24)",
                        lg: "0px 0px 1px rgba(26, 32, 36, 0.32), 0px 8px 16px rgba(91, 104, 113, 0.24)",
                        xl: "0px 0px 1px rgba(26, 32, 36, 0.32), 0px 24px 32px rgba(91, 104, 113, 0.24)",
                        xxl: "0px 0px 1px rgba(26, 32, 36, 0.32), 0px 40px 64px rgba(91, 104, 113, 0.24)",
                        fixed: "0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1)",
                        none: "none"
                    },
                    breakpoints: {
                        smallMobile: 320,
                        mediumMobile: 414,
                        largeMobile: 576,
                        tablet: 768,
                        desktop: 1024,
                        largeDesktop: 1440
                    },
                    space: {
                        none: "0",
                        xxxs: "2px",
                        xxs: "4px",
                        xs: "8px",
                        sm: "10px",
                        md: "16px",
                        lg: "24px",
                        xl: "32px",
                        xxl: "40px",
                        xxxl: "52px"
                    },
                    colors: {
                        primary: palette.product.normal,
                        secondary: palette.ink.normal,
                        info: palette.ink.light,
                        grey: palette.cloud.normal,
                        darkerGrey: palette.cloud.darkest,
                        overlay: palette.cloud.dark,
                        disabled: palette.cloud.darker,
                        success: palette.green.normal,
                        lightGreen: palette.green.light,
                        warning: palette.orange.light,
                        gold: palette.orange.gold,
                        lightGold: palette.orange.lightGold,
                        orange: palette.orange.normal,
                        darkOrange: palette.orange.dark,
                        danger: palette.red.normal,
                        lightDanger: palette.red.light,
                        darkDanger: palette.red.dark,
                        white: palette.white.normal,
                        black: palette.black.normal,
                        transparent: palette.transparent.normal,
                        darkPrimary: palette.product.dark,
                        darkerPrimary: palette.product.darker
                    },
                    textTransform: {
                        capitalize: "capitalize",
                        lowercase: "lowercase",
                        uppercase: "uppercase",
                        none: "none"
                    }
                };
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = ({
                    base,
                    palette
                });

                /***/
            }),

        /***/
        "../../packages/quilt/src/themes/property-overriding/colors.ts":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    Color: () => ( /* binding */ Color),
                    /* harmony export */
                    getColorValue: () => ( /* binding */ getColorValue),
                    /* harmony export */
                    modifyColorProp: () => ( /* binding */ modifyColorProp)
                    /* harmony export */
                });
                /* harmony import */
                var styled_system__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/styled-system/styled-system");
                /* harmony import */
                var styled_system__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(styled_system__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _theme__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../packages/quilt/src/themes/theme.ts");



                // type KeyedTuple<T> = { [K in keyof T]: [K, T[K]] }[keyof T];

                const background = (0, styled_system__WEBPACK_IMPORTED_MODULE_0__.system)({
                    backgroundColor: {
                        property: "backgroundColor",
                        transform: getColorValue
                    },
                    bgColor: {
                        property: "backgroundColor",
                        transform: getColorValue
                    },
                    bg: {
                        property: "backgroundColor",
                        transform: getColorValue
                    }
                });
                const textColor = (0, styled_system__WEBPACK_IMPORTED_MODULE_0__.system)({
                    color: {
                        property: "color",
                        transform: getColorValue
                    },
                    textColor: {
                        property: "color",
                        transform: getColorValue
                    }
                });

                function getColorValue(value) {
                    if (!value) return;
                    if (typeof value === "string") return _theme__WEBPACK_IMPORTED_MODULE_1__.theme.base.colors[value];
                    const mainColor = Object.keys(value)[0];
                    const variantColor = value[mainColor];
                    return _theme__WEBPACK_IMPORTED_MODULE_1__.theme.palette[mainColor][variantColor];
                }

                function modifyColorProp(propName, value) {
                    if (!value) return {};
                    return typeof value === "string" ? {
                        [propName]: value
                    } : {
                        [propName]: {
                            color: value
                        }
                    };
                }
                const Color = props => {
                    const modifiedColorProps = Object.assign({}, props, modifyColorProp("color", props.color), modifyColorProp("textColor", props.textColor), modifyColorProp("bg", props.bg), modifyColorProp("bgColor", props.bgColor), modifyColorProp("backgroundColor", props.backgroundColor));
                    return (0, styled_system__WEBPACK_IMPORTED_MODULE_0__.compose)(background, styled_system__WEBPACK_IMPORTED_MODULE_0__.opacity, textColor)(modifiedColorProps);
                };

                /***/
            }),

        /***/
        "../../packages/quilt/src/themes/theme.ts":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    theme: () => ( /* binding */ theme)
                    /* harmony export */
                });
                /* harmony import */
                var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("webpack/sharing/consume/default/styled-components/styled-components?d27b");
                /* harmony import */
                var styled_components__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_0__);
                /* harmony import */
                var _original__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../packages/quilt/src/themes/original.ts");


                let dir = "ltr";
                let rtl = false;
                const theme = {
                    palette: _original__WEBPACK_IMPORTED_MODULE_1__.palette,
                    base: _original__WEBPACK_IMPORTED_MODULE_1__.base,
                    dir,
                    rtl
                };

                /***/
            })

    }
])
//# sourceMappingURL=packages_quilt_src_components_icons_CheckIcon_tsx.775bf7430044ef62.js.map