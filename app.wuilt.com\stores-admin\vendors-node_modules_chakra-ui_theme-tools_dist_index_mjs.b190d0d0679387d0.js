(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_chakra-ui_theme-tools_dist_index_mjs"], {

        /***/
        "../../node_modules/@chakra-ui/theme-tools/dist/chunk-6IC2I3BY.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    blacken: () => ( /* binding */ blacken),
                    /* harmony export */
                    complementary: () => ( /* binding */ complementary),
                    /* harmony export */
                    contrast: () => ( /* binding */ contrast),
                    /* harmony export */
                    darken: () => ( /* binding */ darken),
                    /* harmony export */
                    generateStripe: () => ( /* binding */ generateStripe),
                    /* harmony export */
                    getColor: () => ( /* binding */ getColor),
                    /* harmony export */
                    getColorVar: () => ( /* binding */ getColorVar),
                    /* harmony export */
                    isAccessible: () => ( /* binding */ isAccessible),
                    /* harmony export */
                    isDark: () => ( /* binding */ isDark),
                    /* harmony export */
                    isLight: () => ( /* binding */ isLight),
                    /* harmony export */
                    isReadable: () => ( /* binding */ isReadable),
                    /* harmony export */
                    lighten: () => ( /* binding */ lighten),
                    /* harmony export */
                    randomColor: () => ( /* binding */ randomColor),
                    /* harmony export */
                    readability: () => ( /* binding */ readability),
                    /* harmony export */
                    tone: () => ( /* binding */ tone),
                    /* harmony export */
                    transparentize: () => ( /* binding */ transparentize),
                    /* harmony export */
                    whiten: () => ( /* binding */ whiten)
                    /* harmony export */
                });
                /* harmony import */
                var _chakra_ui_styled_system__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@chakra-ui/styled-system/dist/index.mjs");
                /* harmony import */
                var color2k__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/color2k/dist/index.exports.import.es.mjs");
                // src/color.ts



                // ../../../node_modules/.pnpm/dlv@1.1.3/node_modules/dlv/dist/dlv.es.js
                function dlv_es_default(t, e, l, n, r) {
                    for (e = e.split ? e.split(".") : e, n = 0; n < e.length; n++)
                        t = t ? t[e[n]] : r;
                    return t === r ? l : t;
                }

                // src/color.ts
                var isEmptyObject = (obj) => Object.keys(obj).length === 0;
                var getColor = (theme, color, fallback) => {
                    const hex = dlv_es_default(theme, `colors.${color}`, color);
                    try {
                        (0, color2k__WEBPACK_IMPORTED_MODULE_0__.toHex)(hex);
                        return hex;
                    } catch {
                        return fallback != null ? fallback : "#000000";
                    }
                };
                var getColorVar = (theme, color, fallback) => {
                    var _a;
                    return (_a = (0, _chakra_ui_styled_system__WEBPACK_IMPORTED_MODULE_1__.getCSSVar)(theme, "colors", color)) != null ? _a : fallback;
                };
                var getBrightness = (color) => {
                    const [r, g, b] = (0, color2k__WEBPACK_IMPORTED_MODULE_0__.parseToRgba)(color);
                    return (r * 299 + g * 587 + b * 114) / 1e3;
                };
                var tone = (color) => (theme) => {
                    const hex = getColor(theme, color);
                    const brightness = getBrightness(hex);
                    const isDark2 = brightness < 128;
                    return isDark2 ? "dark" : "light";
                };
                var isDark = (color) => (theme) => tone(color)(theme) === "dark";
                var isLight = (color) => (theme) => tone(color)(theme) === "light";
                var transparentize = (color, opacity) => (theme) => {
                    const raw = getColor(theme, color);
                    return (0, color2k__WEBPACK_IMPORTED_MODULE_0__.transparentize)(raw, 1 - opacity);
                };
                var whiten = (color, amount) => (theme) => {
                    const raw = getColor(theme, color);
                    return (0, color2k__WEBPACK_IMPORTED_MODULE_0__.toHex)((0, color2k__WEBPACK_IMPORTED_MODULE_0__.mix)(raw, "#fff", amount));
                };
                var blacken = (color, amount) => (theme) => {
                    const raw = getColor(theme, color);
                    return (0, color2k__WEBPACK_IMPORTED_MODULE_0__.toHex)((0, color2k__WEBPACK_IMPORTED_MODULE_0__.mix)(raw, "#000", amount / 100));
                };
                var darken = (color, amount) => (theme) => {
                    const raw = getColor(theme, color);
                    return (0, color2k__WEBPACK_IMPORTED_MODULE_0__.toHex)((0, color2k__WEBPACK_IMPORTED_MODULE_0__.darken)(raw, amount / 100));
                };
                var lighten = (color, amount) => (theme) => {
                    const raw = getColor(theme, color);
                    (0, color2k__WEBPACK_IMPORTED_MODULE_0__.toHex)((0, color2k__WEBPACK_IMPORTED_MODULE_0__.lighten)(raw, amount / 100));
                };
                var contrast = (fg, bg) => (theme) => (0, color2k__WEBPACK_IMPORTED_MODULE_0__.getContrast)(getColor(theme, bg), getColor(theme, fg));
                var isAccessible = (textColor, bgColor, options) => (theme) => isReadable(getColor(theme, bgColor), getColor(theme, textColor), options);

                function isReadable(color1, color2, wcag2 = {
                    level: "AA",
                    size: "small"
                }) {
                    var _a, _b;
                    const readabilityLevel = readability(color1, color2);
                    switch (((_a = wcag2.level) != null ? _a : "AA") + ((_b = wcag2.size) != null ? _b : "small")) {
                        case "AAsmall":
                        case "AAAlarge":
                            return readabilityLevel >= 4.5;
                        case "AAlarge":
                            return readabilityLevel >= 3;
                        case "AAAsmall":
                            return readabilityLevel >= 7;
                        default:
                            return false;
                    }
                }

                function readability(color1, color2) {
                    return (Math.max((0, color2k__WEBPACK_IMPORTED_MODULE_0__.getLuminance)(color1), (0, color2k__WEBPACK_IMPORTED_MODULE_0__.getLuminance)(color2)) + 0.05) / (Math.min((0, color2k__WEBPACK_IMPORTED_MODULE_0__.getLuminance)(color1), (0, color2k__WEBPACK_IMPORTED_MODULE_0__.getLuminance)(color2)) + 0.05);
                }
                var complementary = (color) => (theme) => {
                    const raw = getColor(theme, color);
                    const hsl = (0, color2k__WEBPACK_IMPORTED_MODULE_0__.parseToHsla)(raw);
                    const complementHsl = Object.assign(hsl, [
                        (hsl[0] + 180) % 360
                    ]);
                    return (0, color2k__WEBPACK_IMPORTED_MODULE_0__.toHex)((0, color2k__WEBPACK_IMPORTED_MODULE_0__.hsla)(...complementHsl));
                };

                function generateStripe(size = "1rem", color = "rgba(255, 255, 255, 0.15)") {
                    return {
                        backgroundImage: `linear-gradient(
    45deg,
    ${color} 25%,
    transparent 25%,
    transparent 50%,
    ${color} 50%,
    ${color} 75%,
    transparent 75%,
    transparent
  )`,
                        backgroundSize: `${size} ${size}`
                    };
                }
                var randomHex = () => `#${Math.floor(Math.random() * 16777215).toString(16).padEnd(6, "0")}`;

                function randomColor(opts) {
                    const fallback = randomHex();
                    if (!opts || isEmptyObject(opts)) {
                        return fallback;
                    }
                    if (opts.string && opts.colors) {
                        return randomColorFromList(opts.string, opts.colors);
                    }
                    if (opts.string && !opts.colors) {
                        return randomColorFromString(opts.string);
                    }
                    if (opts.colors && !opts.string) {
                        return randomFromList(opts.colors);
                    }
                    return fallback;
                }

                function randomColorFromString(str) {
                    let hash = 0;
                    if (str.length === 0)
                        return hash.toString();
                    for (let i = 0; i < str.length; i += 1) {
                        hash = str.charCodeAt(i) + ((hash << 5) - hash);
                        hash = hash & hash;
                    }
                    let color = "#";
                    for (let j = 0; j < 3; j += 1) {
                        const value = hash >> j * 8 & 255;
                        color += `00${value.toString(16)}`.substr(-2);
                    }
                    return color;
                }

                function randomColorFromList(str, list) {
                    let index = 0;
                    if (str.length === 0)
                        return list[0];
                    for (let i = 0; i < str.length; i += 1) {
                        index = str.charCodeAt(i) + ((index << 5) - index);
                        index = index & index;
                    }
                    index = (index % list.length + list.length) % list.length;
                    return list[index];
                }

                function randomFromList(list) {
                    return list[Math.floor(Math.random() * list.length)];
                }


                //# sourceMappingURL=chunk-6IC2I3BY.mjs.map

                /***/
            }),

        /***/
        "../../node_modules/@chakra-ui/theme-tools/dist/chunk-FNB7ZWWX.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    mode: () => ( /* binding */ mode),
                    /* harmony export */
                    orient: () => ( /* binding */ orient)
                    /* harmony export */
                });
                // src/component.ts
                function mode(light, dark) {
                    return (props) => props.colorMode === "dark" ? dark : light;
                }

                function orient(options) {
                    const {
                        orientation,
                        vertical,
                        horizontal
                    } = options;
                    if (!orientation)
                        return {};
                    return orientation === "vertical" ? vertical : horizontal;
                }


                //# sourceMappingURL=chunk-FNB7ZWWX.mjs.map

                /***/
            }),

        /***/
        "../../node_modules/@chakra-ui/theme-tools/dist/chunk-N4TQSR52.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    createBreakpoints: () => ( /* binding */ createBreakpoints)
                    /* harmony export */
                });
                /* harmony import */
                var _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@chakra-ui/shared-utils/dist/index.mjs");
                // src/create-breakpoints.ts

                var createBreakpoints = (config) => {
                    (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.warn)({
                        condition: true,
                        message: [
                            `[chakra-ui]: createBreakpoints(...) will be deprecated pretty soon`,
                            `simply pass the breakpoints as an object. Remove the createBreakpoints(..) call`
                        ].join("")
                    });
                    return {
                        base: "0em",
                        ...config
                    };
                };


                //# sourceMappingURL=chunk-N4TQSR52.mjs.map

                /***/
            }),

        /***/
        "../../node_modules/@chakra-ui/theme-tools/dist/chunk-WSAJBJJ4.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    addPrefix: () => ( /* binding */ addPrefix),
                    /* harmony export */
                    cssVar: () => ( /* binding */ cssVar),
                    /* harmony export */
                    isDecimal: () => ( /* binding */ isDecimal),
                    /* harmony export */
                    toVar: () => ( /* binding */ toVar),
                    /* harmony export */
                    toVarRef: () => ( /* binding */ toVarRef)
                    /* harmony export */
                });
                // src/css-var.ts
                function isDecimal(value) {
                    return !Number.isInteger(parseFloat(value.toString()));
                }

                function replaceWhiteSpace(value, replaceValue = "-") {
                    return value.replace(/\s+/g, replaceValue);
                }

                function escape(value) {
                    const valueStr = replaceWhiteSpace(value.toString());
                    if (valueStr.includes("\\."))
                        return value;
                    return isDecimal(value) ? valueStr.replace(".", `\\.`) : value;
                }

                function addPrefix(value, prefix = "") {
                    return [prefix, escape(value)].filter(Boolean).join("-");
                }

                function toVarRef(name, fallback) {
                    return `var(${escape(name)}${fallback ? `, ${fallback}` : ""})`;
                }

                function toVar(value, prefix = "") {
                    return `--${addPrefix(value, prefix)}`;
                }

                function cssVar(name, options) {
                    const cssVariable = toVar(name, options == null ? void 0 : options.prefix);
                    return {
                        variable: cssVariable,
                        reference: toVarRef(cssVariable, getFallback(options == null ? void 0 : options.fallback))
                    };
                }

                function getFallback(fallback) {
                    if (typeof fallback === "string")
                        return fallback;
                    return fallback == null ? void 0 : fallback.reference;
                }


                //# sourceMappingURL=chunk-WSAJBJJ4.mjs.map

                /***/
            }),

        /***/
        "../../node_modules/@chakra-ui/theme-tools/dist/chunk-XMZHFSTS.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    calc: () => ( /* binding */ calc)
                    /* harmony export */
                });
                /* harmony import */
                var _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@chakra-ui/shared-utils/dist/index.mjs");
                // src/css-calc.ts

                function toRef(operand) {
                    if ((0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(operand) && operand.reference) {
                        return operand.reference;
                    }
                    return String(operand);
                }
                var toExpr = (operator, ...operands) => operands.map(toRef).join(` ${operator} `).replace(/calc/g, "");
                var add = (...operands) => `calc(${toExpr("+", ...operands)})`;
                var subtract = (...operands) => `calc(${toExpr("-", ...operands)})`;
                var multiply = (...operands) => `calc(${toExpr("*", ...operands)})`;
                var divide = (...operands) => `calc(${toExpr("/", ...operands)})`;
                var negate = (x) => {
                    const value = toRef(x);
                    if (value != null && !Number.isNaN(parseFloat(value))) {
                        return String(value).startsWith("-") ? String(value).slice(1) : `-${value}`;
                    }
                    return multiply(value, -1);
                };
                var calc = Object.assign(
                    (x) => ({
                        add: (...operands) => calc(add(x, ...operands)),
                        subtract: (...operands) => calc(subtract(x, ...operands)),
                        multiply: (...operands) => calc(multiply(x, ...operands)),
                        divide: (...operands) => calc(divide(x, ...operands)),
                        negate: () => calc(negate(x)),
                        toString: () => x.toString()
                    }), {
                        add,
                        subtract,
                        multiply,
                        divide,
                        negate
                    }
                );


                //# sourceMappingURL=chunk-XMZHFSTS.mjs.map

                /***/
            }),

        /***/
        "../../node_modules/@chakra-ui/theme-tools/dist/index.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    addPrefix: () => ( /* reexport safe */ _chunk_WSAJBJJ4_mjs__WEBPACK_IMPORTED_MODULE_0__.addPrefix),
                    /* harmony export */
                    anatomy: () => ( /* reexport safe */ _chakra_ui_anatomy__WEBPACK_IMPORTED_MODULE_1__.anatomy),
                    /* harmony export */
                    blacken: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.blacken),
                    /* harmony export */
                    calc: () => ( /* reexport safe */ _chunk_XMZHFSTS_mjs__WEBPACK_IMPORTED_MODULE_3__.calc),
                    /* harmony export */
                    complementary: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.complementary),
                    /* harmony export */
                    contrast: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.contrast),
                    /* harmony export */
                    createBreakpoints: () => ( /* reexport safe */ _chunk_N4TQSR52_mjs__WEBPACK_IMPORTED_MODULE_4__.createBreakpoints),
                    /* harmony export */
                    cssVar: () => ( /* reexport safe */ _chunk_WSAJBJJ4_mjs__WEBPACK_IMPORTED_MODULE_0__.cssVar),
                    /* harmony export */
                    darken: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.darken),
                    /* harmony export */
                    generateStripe: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.generateStripe),
                    /* harmony export */
                    getColor: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.getColor),
                    /* harmony export */
                    getColorVar: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.getColorVar),
                    /* harmony export */
                    isAccessible: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.isAccessible),
                    /* harmony export */
                    isDark: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.isDark),
                    /* harmony export */
                    isDecimal: () => ( /* reexport safe */ _chunk_WSAJBJJ4_mjs__WEBPACK_IMPORTED_MODULE_0__.isDecimal),
                    /* harmony export */
                    isLight: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.isLight),
                    /* harmony export */
                    isReadable: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.isReadable),
                    /* harmony export */
                    lighten: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.lighten),
                    /* harmony export */
                    mode: () => ( /* reexport safe */ _chunk_FNB7ZWWX_mjs__WEBPACK_IMPORTED_MODULE_5__.mode),
                    /* harmony export */
                    orient: () => ( /* reexport safe */ _chunk_FNB7ZWWX_mjs__WEBPACK_IMPORTED_MODULE_5__.orient),
                    /* harmony export */
                    randomColor: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.randomColor),
                    /* harmony export */
                    readability: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.readability),
                    /* harmony export */
                    toVar: () => ( /* reexport safe */ _chunk_WSAJBJJ4_mjs__WEBPACK_IMPORTED_MODULE_0__.toVar),
                    /* harmony export */
                    toVarRef: () => ( /* reexport safe */ _chunk_WSAJBJJ4_mjs__WEBPACK_IMPORTED_MODULE_0__.toVarRef),
                    /* harmony export */
                    tone: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.tone),
                    /* harmony export */
                    transparentize: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.transparentize),
                    /* harmony export */
                    whiten: () => ( /* reexport safe */ _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__.whiten)
                    /* harmony export */
                });
                /* harmony import */
                var _chunk_6IC2I3BY_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@chakra-ui/theme-tools/dist/chunk-6IC2I3BY.mjs");
                /* harmony import */
                var _chunk_FNB7ZWWX_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/@chakra-ui/theme-tools/dist/chunk-FNB7ZWWX.mjs");
                /* harmony import */
                var _chunk_N4TQSR52_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/@chakra-ui/theme-tools/dist/chunk-N4TQSR52.mjs");
                /* harmony import */
                var _chunk_XMZHFSTS_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@chakra-ui/theme-tools/dist/chunk-XMZHFSTS.mjs");
                /* harmony import */
                var _chunk_WSAJBJJ4_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@chakra-ui/theme-tools/dist/chunk-WSAJBJJ4.mjs");
                /* harmony import */
                var _chakra_ui_anatomy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@chakra-ui/anatomy/dist/chunk-OA3DH5LS.mjs");






                // src/index.ts


                //# sourceMappingURL=index.mjs.map

                /***/
            }),

        /***/
        "../../node_modules/color2k/dist/index.exports.import.es.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    ColorError: () => ( /* binding */ ColorError$1),
                    /* harmony export */
                    adjustHue: () => ( /* binding */ adjustHue),
                    /* harmony export */
                    darken: () => ( /* binding */ darken),
                    /* harmony export */
                    desaturate: () => ( /* binding */ desaturate),
                    /* harmony export */
                    getContrast: () => ( /* binding */ getContrast),
                    /* harmony export */
                    getLuminance: () => ( /* binding */ getLuminance),
                    /* harmony export */
                    getScale: () => ( /* binding */ getScale),
                    /* harmony export */
                    guard: () => ( /* binding */ guard),
                    /* harmony export */
                    hasBadContrast: () => ( /* binding */ hasBadContrast),
                    /* harmony export */
                    hsla: () => ( /* binding */ hsla),
                    /* harmony export */
                    lighten: () => ( /* binding */ lighten),
                    /* harmony export */
                    mix: () => ( /* binding */ mix),
                    /* harmony export */
                    opacify: () => ( /* binding */ opacify),
                    /* harmony export */
                    parseToHsla: () => ( /* binding */ parseToHsla),
                    /* harmony export */
                    parseToRgba: () => ( /* binding */ parseToRgba),
                    /* harmony export */
                    readableColor: () => ( /* binding */ readableColor),
                    /* harmony export */
                    readableColorIsBlack: () => ( /* binding */ readableColorIsBlack),
                    /* harmony export */
                    rgba: () => ( /* binding */ rgba),
                    /* harmony export */
                    saturate: () => ( /* binding */ saturate),
                    /* harmony export */
                    toHex: () => ( /* binding */ toHex),
                    /* harmony export */
                    toHsla: () => ( /* binding */ toHsla),
                    /* harmony export */
                    toRgba: () => ( /* binding */ toRgba),
                    /* harmony export */
                    transparentize: () => ( /* binding */ transparentize)
                    /* harmony export */
                });
                /**
                 * A simple guard function:
                 *
                 * ```js
                 * Math.min(Math.max(low, value), high)
                 * ```
                 */
                function guard(low, high, value) {
                    return Math.min(Math.max(low, value), high);
                }

                class ColorError extends Error {
                    constructor(color) {
                        super(`Failed to parse color: "${color}"`);
                    }
                }
                var ColorError$1 = ColorError;

                /**
                 * Parses a color into red, gree, blue, alpha parts
                 *
                 * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color
                 */
                function parseToRgba(color) {
                    if (typeof color !== 'string') throw new ColorError$1(color);
                    if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];
                    let normalizedColor = color.trim();
                    normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;
                    const reducedHexMatch = reducedHexRegex.exec(normalizedColor);
                    if (reducedHexMatch) {
                        const arr = Array.from(reducedHexMatch).slice(1);
                        return [...arr.slice(0, 3).map(x => parseInt(r(x, 2), 16)), parseInt(r(arr[3] || 'f', 2), 16) / 255];
                    }
                    const hexMatch = hexRegex.exec(normalizedColor);
                    if (hexMatch) {
                        const arr = Array.from(hexMatch).slice(1);
                        return [...arr.slice(0, 3).map(x => parseInt(x, 16)), parseInt(arr[3] || 'ff', 16) / 255];
                    }
                    const rgbaMatch = rgbaRegex.exec(normalizedColor);
                    if (rgbaMatch) {
                        const arr = Array.from(rgbaMatch).slice(1);
                        return [...arr.slice(0, 3).map(x => parseInt(x, 10)), parseFloat(arr[3] || '1')];
                    }
                    const hslaMatch = hslaRegex.exec(normalizedColor);
                    if (hslaMatch) {
                        const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);
                        if (guard(0, 100, s) !== s) throw new ColorError$1(color);
                        if (guard(0, 100, l) !== l) throw new ColorError$1(color);
                        return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a];
                    }
                    throw new ColorError$1(color);
                }

                function hash(str) {
                    let hash = 5381;
                    let i = str.length;
                    while (i) {
                        hash = hash * 33 ^ str.charCodeAt(--i);
                    }

                    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed
                     * integers. Since we want the results to be always positive, convert the
                     * signed int to an unsigned by doing an unsigned bitshift. */
                    return (hash >>> 0) % 2341;
                }
                const colorToInt = x => parseInt(x.replace(/_/g, ''), 36);
                const compressedColorMap = '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'.split(' ').reduce((acc, next) => {
                    const key = colorToInt(next.substring(0, 3));
                    const hex = colorToInt(next.substring(3)).toString(16);

                    // NOTE: padStart could be used here but it breaks Node 6 compat
                    // https://github.com/ricokahler/color2k/issues/351
                    let prefix = '';
                    for (let i = 0; i < 6 - hex.length; i++) {
                        prefix += '0';
                    }
                    acc[key] = `${prefix}${hex}`;
                    return acc;
                }, {});

                /**
                 * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.
                 */
                function nameToHex(color) {
                    const normalizedColorName = color.toLowerCase().trim();
                    const result = compressedColorMap[hash(normalizedColorName)];
                    if (!result) throw new ColorError$1(color);
                    return `#${result}`;
                }
                const r = (str, amount) => Array.from(Array(amount)).map(() => str).join('');
                const reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');
                const hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');
                const rgbaRegex = new RegExp(`^rgba?\\(\\s*(\\d+)\\s*${r(',\\s*(\\d+)\\s*', 2)}(?:,\\s*([\\d.]+))?\\s*\\)$`, 'i');
                const hslaRegex = /^hsla?\(\s*([\d.]+)\s*,\s*([\d.]+)%\s*,\s*([\d.]+)%(?:\s*,\s*([\d.]+))?\s*\)$/i;
                const namedColorRegex = /^[a-z]+$/i;
                const roundColor = color => {
                    return Math.round(color * 255);
                };
                const hslToRgb = (hue, saturation, lightness) => {
                    let l = lightness / 100;
                    if (saturation === 0) {
                        // achromatic
                        return [l, l, l].map(roundColor);
                    }

                    // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV
                    const huePrime = (hue % 360 + 360) % 360 / 60;
                    const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);
                    const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));
                    let red = 0;
                    let green = 0;
                    let blue = 0;
                    if (huePrime >= 0 && huePrime < 1) {
                        red = chroma;
                        green = secondComponent;
                    } else if (huePrime >= 1 && huePrime < 2) {
                        red = secondComponent;
                        green = chroma;
                    } else if (huePrime >= 2 && huePrime < 3) {
                        green = chroma;
                        blue = secondComponent;
                    } else if (huePrime >= 3 && huePrime < 4) {
                        green = secondComponent;
                        blue = chroma;
                    } else if (huePrime >= 4 && huePrime < 5) {
                        red = secondComponent;
                        blue = chroma;
                    } else if (huePrime >= 5 && huePrime < 6) {
                        red = chroma;
                        blue = secondComponent;
                    }
                    const lightnessModification = l - chroma / 2;
                    const finalRed = red + lightnessModification;
                    const finalGreen = green + lightnessModification;
                    const finalBlue = blue + lightnessModification;
                    return [finalRed, finalGreen, finalBlue].map(roundColor);
                };

                // taken from:
                // https://github.com/styled-components/polished/blob/a23a6a2bb26802b3d922d9c3b67bac3f3a54a310/src/internalHelpers/_rgbToHsl.js

                /**
                 * Parses a color in hue, saturation, lightness, and the alpha channel.
                 *
                 * Hue is a number between 0 and 360, saturation, lightness, and alpha are
                 * decimal percentages between 0 and 1
                 */
                function parseToHsla(color) {
                    const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>
                        // 3rd index is alpha channel which is already normalized
                        index === 3 ? value : value / 255);
                    const max = Math.max(red, green, blue);
                    const min = Math.min(red, green, blue);
                    const lightness = (max + min) / 2;

                    // achromatic
                    if (max === min) return [0, 0, lightness, alpha];
                    const delta = max - min;
                    const saturation = lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);
                    const hue = 60 * (red === max ? (green - blue) / delta + (green < blue ? 6 : 0) : green === max ? (blue - red) / delta + 2 : (red - green) / delta + 4);
                    return [hue, saturation, lightness, alpha];
                }

                /**
                 * Takes in hsla parts and constructs an hsla string
                 *
                 * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue
                 * @param saturation Percentage of saturation, given as a decimal between 0 and 1
                 * @param lightness Percentage of lightness, given as a decimal between 0 and 1
                 * @param alpha Percentage of opacity, given as a decimal between 0 and 1
                 */
                function hsla(hue, saturation, lightness, alpha) {
                    return `hsla(${(hue % 360).toFixed()}, ${guard(0, 100, saturation * 100).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;
                }

                /**
                 * Adjusts the current hue of the color by the given degrees. Wraps around when
                 * over 360.
                 *
                 * @param color input color
                 * @param degrees degrees to adjust the input color, accepts degree integers
                 * (0 - 360) and wraps around on overflow
                 */
                function adjustHue(color, degrees) {
                    const [h, s, l, a] = parseToHsla(color);
                    return hsla(h + degrees, s, l, a);
                }

                /**
                 * Darkens using lightness. This is equivalent to subtracting the lightness
                 * from the L in HSL.
                 *
                 * @param amount The amount to darken, given as a decimal between 0 and 1
                 */
                function darken(color, amount) {
                    const [hue, saturation, lightness, alpha] = parseToHsla(color);
                    return hsla(hue, saturation, lightness - amount, alpha);
                }

                /**
                 * Desaturates the input color by the given amount via subtracting from the `s`
                 * in `hsla`.
                 *
                 * @param amount The amount to desaturate, given as a decimal between 0 and 1
                 */
                function desaturate(color, amount) {
                    const [h, s, l, a] = parseToHsla(color);
                    return hsla(h, s - amount, l, a);
                }

                // taken from:
                // https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js

                /**
                 * Returns a number (float) representing the luminance of a color.
                 */
                function getLuminance(color) {
                    if (color === 'transparent') return 0;

                    function f(x) {
                        const channel = x / 255;
                        return channel <= 0.04045 ? channel / 12.92 : Math.pow((channel + 0.055) / 1.055, 2.4);
                    }
                    const [r, g, b] = parseToRgba(color);
                    return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);
                }

                // taken from:
                // https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getContrast.js

                /**
                 * Returns the contrast ratio between two colors based on
                 * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).
                 */
                function getContrast(color1, color2) {
                    const luminance1 = getLuminance(color1);
                    const luminance2 = getLuminance(color2);
                    return luminance1 > luminance2 ? (luminance1 + 0.05) / (luminance2 + 0.05) : (luminance2 + 0.05) / (luminance1 + 0.05);
                }

                /**
                 * Takes in rgba parts and returns an rgba string
                 *
                 * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive
                 * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive
                 * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive
                 * @param alpha Percentage of opacity, given as a decimal between 0 and 1
                 */
                function rgba(red, green, blue, alpha) {
                    return `rgba(${guard(0, 255, red).toFixed()}, ${guard(0, 255, green).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;
                }

                /**
                 * Mixes two colors together. Taken from sass's implementation.
                 */
                function mix(color1, color2, weight) {
                    const normalize = (n, index) =>
                        // 3rd index is alpha channel which is already normalized
                        index === 3 ? n : n / 255;
                    const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);
                    const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);

                    // The formula is copied from the original Sass implementation:
                    // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method
                    const alphaDelta = a2 - a1;
                    const normalizedWeight = weight * 2 - 1;
                    const combinedWeight = normalizedWeight * alphaDelta === -1 ? normalizedWeight : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);
                    const weight2 = (combinedWeight + 1) / 2;
                    const weight1 = 1 - weight2;
                    const r = (r1 * weight1 + r2 * weight2) * 255;
                    const g = (g1 * weight1 + g2 * weight2) * 255;
                    const b = (b1 * weight1 + b2 * weight2) * 255;
                    const a = a2 * weight + a1 * (1 - weight);
                    return rgba(r, g, b, a);
                }

                /**
                 * Given a series colors, this function will return a `scale(x)` function that
                 * accepts a percentage as a decimal between 0 and 1 and returns the color at
                 * that percentage in the scale.
                 *
                 * ```js
                 * const scale = getScale('red', 'yellow', 'green');
                 * console.log(scale(0)); // rgba(255, 0, 0, 1)
                 * console.log(scale(0.5)); // rgba(255, 255, 0, 1)
                 * console.log(scale(1)); // rgba(0, 128, 0, 1)
                 * ```
                 *
                 * If you'd like to limit the domain and range like chroma-js, we recommend
                 * wrapping scale again.
                 *
                 * ```js
                 * const _scale = getScale('red', 'yellow', 'green');
                 * const scale = x => _scale(x / 100);
                 *
                 * console.log(scale(0)); // rgba(255, 0, 0, 1)
                 * console.log(scale(50)); // rgba(255, 255, 0, 1)
                 * console.log(scale(100)); // rgba(0, 128, 0, 1)
                 * ```
                 */
                function getScale(...colors) {
                    return n => {
                        const lastIndex = colors.length - 1;
                        const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));
                        const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));
                        const color1 = colors[lowIndex];
                        const color2 = colors[highIndex];
                        const unit = 1 / lastIndex;
                        const weight = (n - unit * lowIndex) / unit;
                        return mix(color1, color2, weight);
                    };
                }

                const guidelines = {
                    decorative: 1.5,
                    readable: 3,
                    aa: 4.5,
                    aaa: 7
                };

                /**
                 * Returns whether or not a color has bad contrast against a background
                 * according to a given standard.
                 */
                function hasBadContrast(color, standard = 'aa', background = '#fff') {
                    return getContrast(color, background) < guidelines[standard];
                }

                /**
                 * Lightens a color by a given amount. This is equivalent to
                 * `darken(color, -amount)`
                 *
                 * @param amount The amount to darken, given as a decimal between 0 and 1
                 */
                function lighten(color, amount) {
                    return darken(color, -amount);
                }

                /**
                 * Takes in a color and makes it more transparent by convert to `rgba` and
                 * decreasing the amount in the alpha channel.
                 *
                 * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1
                 */
                function transparentize(color, amount) {
                    const [r, g, b, a] = parseToRgba(color);
                    return rgba(r, g, b, a - amount);
                }

                /**
                 * Takes a color and un-transparentizes it. Equivalent to
                 * `transparentize(color, -amount)`
                 *
                 * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1
                 */
                function opacify(color, amount) {
                    return transparentize(color, -amount);
                }

                /**
                 * An alternative function to `readableColor`. Returns whether or not the 
                 * readable color (i.e. the color to be place on top the input color) should be
                 * black.
                 */
                function readableColorIsBlack(color) {
                    return getLuminance(color) > 0.179;
                }

                /**
                 * Returns black or white for best contrast depending on the luminosity of the
                 * given color.
                 */
                function readableColor(color) {
                    return readableColorIsBlack(color) ? '#000' : '#fff';
                }

                /**
                 * Saturates a color by converting it to `hsl` and increasing the saturation
                 * amount. Equivalent to `desaturate(color, -amount)`
                 * 
                 * @param color Input color
                 * @param amount The amount to darken, given as a decimal between 0 and 1
                 */
                function saturate(color, amount) {
                    return desaturate(color, -amount);
                }

                /**
                 * Takes in any color and returns it as a hex code.
                 */
                function toHex(color) {
                    const [r, g, b, a] = parseToRgba(color);
                    let hex = x => {
                        const h = guard(0, 255, x).toString(16);
                        // NOTE: padStart could be used here but it breaks Node 6 compat
                        // https://github.com/ricokahler/color2k/issues/351
                        return h.length === 1 ? `0${h}` : h;
                    };
                    return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;
                }

                /**
                 * Takes in any color and returns it as an rgba string.
                 */
                function toRgba(color) {
                    return rgba(...parseToRgba(color));
                }

                /**
                 * Takes in any color and returns it as an hsla string.
                 */
                function toHsla(color) {
                    return hsla(...parseToHsla(color));
                }


                //# sourceMappingURL=index.exports.import.es.mjs.map


                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_chakra-ui_theme-tools_dist_index_mjs.b190d0d0679387d0.js.map