/*! For license information please see 2813.64c7c0c9700b5e18.js.LICENSE.txt */
(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [2813], {
        92813: (r, t) => {
            var n;
            ! function() {
                var e = {}.hasOwnProperty;

                function o() {
                    for (var r = "", t = 0; t < arguments.length; t++) {
                        var n = arguments[t];
                        n && (r = u(r, i(n)))
                    }
                    return r
                }

                function i(r) {
                    if ("string" == typeof r || "number" == typeof r) return r;
                    if ("object" != typeof r) return "";
                    if (Array.isArray(r)) return o.apply(null, r);
                    if (r.toString !== Object.prototype.toString && !r.toString.toString().includes("[native code]")) return r.toString();
                    var t = "";
                    for (var n in r) e.call(r, n) && r[n] && (t = u(t, n));
                    return t
                }

                function u(r, t) {
                    return t ? r ? r + " " + t : r + t : r
                }
                r.exports ? (o.default = o, r.exports = o) : void 0 === (n = function() {
                    return o
                }.apply(t, [])) || (r.exports = n)
            }()
        }
    }
]);
//# sourceMappingURL=2813.64c7c0c9700b5e18.js.map