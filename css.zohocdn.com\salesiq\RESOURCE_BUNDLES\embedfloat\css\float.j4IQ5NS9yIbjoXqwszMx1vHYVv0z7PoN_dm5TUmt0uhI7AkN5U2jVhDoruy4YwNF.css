:root {
    --zsiqf-custom-bg-color: #06c;
    --zsiqf-empty-cont-bg-color: var(--zsiqf-custom-bg-color);
    --zsiqf-tooltip-close-bg: #000;
    --zsiqf-intigator-bg-color: #ff3520;
    --zsiqf-mob-minimise-bg-color: rgba(0, 0, 0, .14);
    --zsiqf-progress-load-cont-bg: hsla(0, 0%, 90%, .3);
    --zsiqf-progress-load-bar-bg: hsla(0, 0%, 90%, .15);
    --zsiqf-primary-color: #fff;
    --zsiqf-primary-bg: #fff;
    --zsiqf-media-thumbnail-icon-bg: rgba(221, 28, 28, .05);
    --zsiqf-media-thumbnail-icon-color: #dd1c1c;
    --zsiqf-float-close-height: 55px;
    --zsiq-seasonal-float-image-position: 0px
}

:root [seasonal-type=christmas] {
    --zsiq-seasonal-float-image-position: 8px
}

[theme=lloyd] {
    --zsiqf-empty-cont-bg-color: #2f2f2f;
    --zsiqf-primary-bg: #444
}

@font-face {
    font-display: swap;
    font-family: siq;
    font-style: normal;
    font-weight: 400;
    src: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/fonts/siq_OI-PzsXydw17goVOJ2c2in0Osuah_dj_rEg8S8qkbsL3nlENq5PxcUebIWFygw_r_.eot);
    src: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/fonts/siq_OI-PzsXydw17goVOJ2c2in0Osuah_dj_rEg8S8qkbsL3nlENq5PxcUebIWFygw_r_.eot) format("embedded-opentype"), url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/fonts/siq_woVkgxwnerWFukYaiWxL3dGrvPm9B1i5mhoGDAP4gTeTSlxTWEXY93tAIiYbq1qY_.ttf) format("truetype"), url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/fonts/siq_35QpFBWJxzo3ufdUd_jA1u0ox2emkR9OUERkraZ-U_yu2GVtot3PAvRKRlwrI4T2_.woff) format("woff"), url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/fonts/siq_Q3XbwBCjYCxCn5ppyntAynE3bLmgDxlTZLjz-izf1D1b55MmIltgv5wv5MTB2K0I_.svg) format("svg")
}

[class*=siqico-] {
    align-items: center;
    display: inline-flex;
    justify-content: center
}

[class*=siqico-]:after,
[class*=siqico-]:before {
    font-family: siq !important;
    speak: none;
    font-style: normal;
    font-variant: normal;
    font-weight: 400;
    line-height: 1;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.siqico-chat:before {
    content: "\e902"
}

.siqico-call:before {
    content: "\e900"
}

.siqico-close:before {
    content: "\e903"
}

.siqico-screenshare:before {
    content: "\e904"
}

.siqico-warning:before {
    content: "\e905"
}

[data-id=zsalesiq] {
    position: relative;
    z-index: 2247483647 !important
}

[data-id=zsalesiq],
[data-id=zsalesiq] * {
    box-sizing: border-box
}

.zsiq-float {
    background-color: var(--zsiqf-custom-bg-color);
    border-radius: 100%;
    bottom: 10px;
    cursor: pointer;
    font-size: 15px;
    height: var(--zsiqf-float-close-height);
    padding: 3px;
    position: fixed;
    right: calc(10px + var(--zsiq-seasonal-float-image-position));
    width: var(--zsiqf-float-close-height)
}

.zsiq-float.zsiq-toggle .zsiq-widget-close {
    display: none
}

.zsiq-float-tooltip {
    animation: zsiqcntanim 1s ease-in-out;
    background-color: #fff;
    border-radius: 8px;
    bottom: 0;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, .25);
    height: 60px;
    line-height: 1.3;
    margin: auto;
    max-width: 300px;
    min-width: 200px;
    padding: 10px 15px;
    position: absolute;
    right: calc(100% + 20px);
    top: 0;
    white-space: nowrap
}

.zsiq-float-tooltip:before {
    background-color: inherit;
    bottom: 0;
    box-shadow: -2px -2px 5px 0 rgba(0, 0, 0, .08);
    content: "";
    height: 10px;
    margin: auto;
    position: absolute;
    right: -5px;
    top: 0;
    transform: rotate(135deg);
    width: 10px;
    z-index: 1
}

.zsiq-float-tooltip:hover .zsiq-tultip-close {
    opacity: 1
}

.zsiq-tooltip-lft:before {
    left: -5px;
    right: auto;
    transform: rotate(-45deg)
}

.zsiq-tultip-desc {
    font-size: 13px
}

.zsiq-count-elem,
.zsiq-tultip-close,
.zsiq-widget-close {
    background-color: var(--zsiqf-tooltip-close-bg);
    border-radius: 100%;
    color: var(--zsiqf-primary-color);
    font-size: 14px;
    height: 22px;
    left: -6px;
    line-height: 22px;
    position: absolute;
    top: -6px;
    width: 22px
}

.zsiq-widget-close {
    height: 20px;
    left: calc(var(--zsiqf-float-close-height) - 15px);
    top: -5px;
    width: 20px
}

.zsiq-tultip-close {
    left: -10px;
    opacity: 0;
    top: -10px
}

.zsiq-count-elem {
    background-color: var(--zsiqf-intigator-bg-color);
    border-radius: 22px;
    min-width: 22px;
    padding: 0 5px;
    width: auto
}

.zsiq-count-elem.zsiq-indicator-anim:after {
    animation: indicator-anim .8s linear infinite;
    background-color: var(--zsiqf-intigator-bg-color);
    border-radius: 100%;
    content: "";
    height: 100%;
    position: absolute;
    width: 100%;
    z-index: -1
}

.zsiq-chat-icn,
.zsiq-close-icn {
    color: var(--zsiqf-primary-color);
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    transition: .25s linear;
    width: 100%
}

.zsiq-chat-icn:focus-visible,
.zsiq-close-icn:focus-visible {
    border-radius: 100%;
    content: "";
    height: 100%;
    outline: 2px solid var(--zsiqf-custom-bg-color);
    outline-offset: 1px;
    position: absolute;
    transition: none;
    width: 100%
}

.zsiq-chat-icn {
    font-size: 25px;
    opacity: 1;
    transform: scale(1)
}

.zsiq-close-icn {
    font-size: 23px;
    opacity: 0;
    transform: rotate(-25deg)
}

[theme=lloyd] .zsiq-toggle {
    --zsiqf-custom-bg-color: #2f2f2f
}

.zsiq-toggle .zsiq-float-tooltip {
    display: none
}

.zsiq-toggle .zsiq-chat-icn {
    opacity: 0;
    transform: scale(0)
}

.zsiq-toggle .zsiq-close-icn {
    opacity: 1;
    transform: rotate(0)
}

.chat-iframe-wrap {
    bottom: calc(var(--zsiqf-float-close-height) + 20px);
    height: calc(100% - 100px);
    max-height: calc(100% - 80px);
    min-height: 540px !important;
    opacity: 0;
    position: fixed;
    right: 5px;
    transform: translateY(15px);
    transform: scale(0);
    transform-origin: bottom right;
    transition: width .4s cubic-bezier(.25, .1, .17, 1.01), max-height .4s cubic-bezier(.25, .1, .26, .99), transform .4s cubic-bezier(0, 1.18, .99, .98), opacity .2s cubic-bezier(0, 0, .58, 1.05);
    visibility: hidden;
    width: 400px;
    z-index: 1
}

.chat-iframe-wrap iframe {
    border: none;
    border-radius: 18px;
    height: 100%;
    width: 100%
}

.chat-iframe-open {
    opacity: 1;
    transform: scale(1);
    visibility: visible
}

.zsiq-custom-stickercont {
    background-color: transparent;
    bottom: 0;
    height: auto;
    max-height: 300px;
    max-width: 300px;
    position: fixed;
    right: 0;
    width: auto
}

.seasonal-float-img:has(~.zsiq-custom-stickercont) {
    display: none
}

.grayscl {
    filter: grayscale(1)
}

.zsiq-medium-size {
    max-height: 780px !important
}

.zsiq-large-size {
    max-height: 880px !important
}

.zsiq-chat-minview {
    height: 170px;
    min-height: 100px !important
}

@media only screen and (max-height:1024px) {
    .zsiq-medium-size {
        max-height: 70% !important
    }
    .zsiq-large-size {
        max-height: 85% !important
    }
}

[position*=left] .zsiq-float {
    left: calc(10px + var(--zsiq-seasonal-float-image-position));
    right: auto
}

[position*=left] .zsiq-float-tooltip {
    left: calc(100% + 20px)
}

[position*=left] .zsiq-float-tooltip:before {
    left: -5px;
    right: auto;
    transform: rotate(315deg)
}

[position*=left] .zsiq-count-elem,
[position*=left] .zsiq-tultip-close {
    left: auto;
    right: -6px
}

[position*=left] .chat-iframe-wrap {
    left: 5px;
    right: auto;
    transform-origin: bottom left
}

[position=left] .zsiq-float:not(.zsiq-toggle),
[position=right] .zsiq-float:not(.zsiq-toggle) {
    bottom: 50%
}

[position=left-top] .zsiq-float:not(.zsiq-toggle),
[position=right-top] .zsiq-float:not(.zsiq-toggle) {
    bottom: auto;
    top: 50px
}

.siqcw-exp-window {
    max-height: 850px !important;
    max-width: 100% !important;
    width: 700px !important
}

.attach-preview {
    bottom: 0 !important;
    height: 100% !important;
    max-height: 100% !important;
    max-width: 100% !important;
    right: 0 !important;
    width: 100% !important
}

[position*=left] .attach-preview {
    left: 0 !important
}

[dragpos=left].attach-preview {
    left: 10px !important
}

.zsiq_prv_main {
    background: rgba(0, 0, 0, .35);
    font-family: inherit;
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1111111111111111 !important
}

.zsiq_prv_main .siqico-close,
.zsiq_prv_main .siqico-larrow {
    background-color: #f3f3f3;
    border-radius: 26px;
    cursor: pointer;
    font-size: 18px;
    height: 26px;
    line-height: normal;
    position: absolute;
    right: 20px;
    text-align: center;
    top: 20px;
    width: 26px
}

.zsiq_prv_main .siqico-larrow {
    border: 1px solid #d6d6d6;
    left: 20px
}

.zsiq-cal-turtrp {
    align-items: center;
    background-color: rgba(0, 0, 0, .7);
    display: flex;
    justify-content: center
}

.zsiq-cal-hnt-cont {
    background-color: #fff;
    border-radius: 18px;
    box-shadow: 0 4px 6px 0 rgba(0, 0, 0, .15);
    color: #121212;
    font-size: 15px;
    max-width: 600px;
    padding: 30px;
    position: relative;
    text-align: center;
    top: 0
}

.zsiq-media-title {
    font-size: 20px;
    margin-top: 10px
}

.zsiq-media-sub-title {
    color: #444;
    font-size: 15px;
    margin: 20px 0 8px;
    text-align: left
}

.zsiq-media-desc {
    margin: 0;
    padding-left: 14px
}

.zsiq-media-desc li {
    margin-top: 10px;
    text-align: left
}

.zsiq-cal-hnt-cont .zsiq-media-close {
    align-items: center;
    background-color: hsla(0, 0%, 80%, .81);
    border-radius: 100%;
    display: flex;
    font-size: 16px;
    height: 30px;
    justify-content: center;
    position: absolute;
    right: 15px;
    top: 15px;
    width: 30px
}

.zsiq-cal-hnt-cont .zsiq-media-close:hover {
    background-color: #888
}

.zsiq-media-thumbnail {
    background-color: var(--zsiqf-media-thumbnail-icon-bg);
    border-radius: 100%;
    color: var(--zsiqf-media-thumbnail-icon-color);
    height: 56px;
    margin: auto;
    width: 56px
}

.zsiq-media-footer {
    align-items: center;
    display: flex;
    justify-content: flex-end;
    margin-top: 30px
}

.zsiq-cal-hnt-cont [class*=siqcw-] {
    background-color: #008fff;
    border-radius: 19px;
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    height: 38px;
    line-height: 38px;
    margin: 0;
    max-width: 250px;
    padding: 0 15px;
    text-align: center
}

.zsiq-cal-hnt-cont .siqcw-wbtn {
    background-color: #fff;
    border: 1px solid #aaa;
    color: #121212;
    margin-right: 15px
}

.zsiq-signature-chat .zsiq-float {
    display: none !important
}

.zsiq-signature-chat .chat-iframe-wrap:not(.attach-preview, .mobile) {
    bottom: 0 !important;
    left: 68vh !important;
    margin: auto;
    right: 0 !important;
    top: 0 !important;
    transform: scale(.9);
    transform-origin: center
}

.zsiq-signature-chat .chat-iframe-wrap:not(.attach-preview, .mobile).zsiq-medium-size {
    height: 75% !important;
    max-height: 700px !important
}

.zsiq-signature-chat .chat-iframe-wrap:not(.attach-preview, .mobile).zsiq-large-size {
    height: 87% !important;
    max-height: 800px !important
}

.zsiq-signature-chat .chat-iframe-wrap:not(.attach-preview, .mobile).mobile {
    left: 0 !important
}

.zsiq-signature-chat .seasonal-float-img {
    display: none !important
}

.fullview_embed [data-id=zsalesiq].zsiq-signature-chat .chat-iframe-wrap {
    height: 100% !important;
    left: 0 !important;
    max-height: 100% !important;
    top: 0 !important;
    transform: scale(1) !important;
    width: 100% !important
}

body.zsiq-signature-chat.chat-iframe-wrap {
    transform: scale(.6);
    transform-origin: center
}

@media only screen and (min-width:555px) and (max-width:1100px) {
    [signature_bg]:not([signaturepreview]) .siqsig_leftnav {
        display: none !important
    }
    [signature_bg]:not([signaturepreview]) .chat-iframe-wrap {
        bottom: 0 !important;
        left: 0 !important;
        margin: auto !important;
        position: fixed !important;
        right: 0 !important;
        top: 0 !important
    }
}

.zsiq-progress-load {
    border-radius: 3px;
    overflow: hidden;
    position: relative
}

.zsiq-progress-load:before {
    background-color: var(--zsiqf-progress-load-cont-bg);
    content: "";
    height: 100%;
    left: 0;
    opacity: .7;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1
}

.zsiq-progress-load:after {
    animation: move-forward .8s ease-in-out;
    animation: move-forward .9s ease-in-out infinite;
    background-color: var(--zsiqf-progress-load-bar-bg);
    border-radius: 100%;
    content: "";
    filter: blur(8px);
    height: 100%;
    left: 0;
    opacity: 1;
    position: absolute;
    width: 50%;
    z-index: 2
}

.zsiq-load-circle {
    border-radius: 100%;
    height: 36px;
    margin: 0 15px;
    width: 36px
}

.zsiq-load-desc,
.zsiq-load-title {
    height: 10px;
    width: 80%
}

.zsiq-load-desc {
    width: 65%
}

.zsiq-circle-load {
    animation: circle-load .6s linear infinite;
    border: 3px solid hsla(0, 0%, 58%, .3);
    border-radius: 100%;
    border-top-color: #939393;
    font-size: 24px;
    height: 1.3em;
    width: 1.3em
}

.chat-loader-cont {
    background-color: var(--zsiqf-primary-bg);
    border-radius: 18px;
    bottom: -5px;
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, .2);
    display: flex;
    flex-direction: column;
    height: calc(100% - 10px);
    margin: 10px;
    overflow: hidden;
    position: absolute;
    right: -5px;
    width: calc(100% - 10px);
    z-index: 1
}

.chat-loader-hdr {
    align-items: center;
    background-color: var(--zsiqf-empty-cont-bg-color);
    display: flex;
    flex-shrink: 0;
    height: 60px;
    position: relative
}

.chat-loader-sec {
    padding: 20px
}

.chat-loader-ftr {
    background-color: var(--zsiqf-primary-bg);
    box-shadow: 0 4px 9.6px 1.4px var(--zsiqf-mob-minimise-bg-color);
    flex-grow: 0;
    height: 60px;
    overflow: hidden;
    position: relative
}

.bot-preview {
    bottom: 0 !important;
    height: 100% !important;
    max-height: 100% !important;
    right: 0 !important
}

[data-id=zsalesiq][seasonal-type][position*=left] .seasonal-float-img {
    left: 0;
    right: auto
}

[data-id=zsalesiq][seasonal-type][position*=left] .seasonal-cw-bg {
    left: auto;
    right: -56px
}

[data-id=zsalesiq][seasonal-type] .seasonal-float-img {
    background-repeat: no-repeat;
    background-size: 100%;
    bottom: calc(100% - 14px);
    height: 57px;
    position: absolute;
    right: 0;
    transform-origin: top;
    width: 68px
}

[data-id=zsalesiq][seasonal-type] .zsiq-toggle .seasonal-float-img {
    display: none
}

[data-id=zsalesiq][seasonal-type] .seasonal-cw-bg {
    background-repeat: no-repeat;
    background-size: 100%;
    height: 120px;
    left: -56px;
    position: absolute;
    top: -35px;
    width: 120px;
    z-index: -1
}

[data-id=zsalesiq]:not([seasonal-type=diwali]) .seasonal-float-img:has(~.zsiq-float .zsiq-count-elem) {
    opacity: .1;
    z-index: 0
}

[data-id=zsalesiq][seasonal-type=diwali] .seasonal-float-img {
    background-image: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/images/Diwali/diwali_light_tRi3EXHRBHpitJSyZx08rUhtSoJH6MXK1fA94DTRrbPAG_czaNUNW9L9F48apJpL_.png)
}

[data-id=zsalesiq][seasonal-type=diwali] .seasonal-cw-bg {
    background-image: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/images/Diwali/crackers_Wvy0J0S9NuBBbbFtNff9ANHp8VplCKKfdVtoPfKbuV5rTj2G2hMSr7k67dNCnjqA_.png)
}

[data-id=zsalesiq][seasonal-type=diwali][position*=left] .seasonal-cw-bg,
[data-id=zsalesiq][seasonal-type=diwali][position*=left] .seasonal-float-img {
    transform: rotateY(180deg)
}

[data-id=zsalesiq][seasonal-type=anniversary] .seasonal-float-img {
    background-image: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/images/Anniversary/celebration_sticker_K_SAYv-NkGW6srEiS9QJskTrQwUvBiuZ0tXfK9wHb_wuC0d7sLhaqrS6sQLRYvky_.png);
    display: block
}

[data-id=zsalesiq][seasonal-type=anniversary] .seasonal-cw-bg {
    background-image: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/images/Anniversary/Baloons_4g-_M5lVnr-NAhs6reZzV6ILg1v5BH-aSxrlFDibhupADp2sNWBFODdZwY5z66oq_.svg);
    display: block;
    left: -20px;
    top: -72px
}

[data-id=zsalesiq][seasonal-type=anniversary] .zsiq-float:not(.zsiq-toggle) {
    --zsiqf-float-close-height: 90px;
    background-color: transparent
}

[data-id=zsalesiq][seasonal-type=anniversary] .zsiq-float:not(.zsiq-toggle) .zsiq-chat-icn {
    display: none
}

[data-id=zsalesiq][seasonal-type=anniversary] .seasonal-float-img {
    height: 92px;
    right: 10px;
    width: 89px;
    z-index: 0
}

[data-id=zsalesiq][seasonal-type=christmas] .seasonal-float-img {
    background-image: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/images/Christmas/christmas_2x_aFhSywk4zsZQqUXBUoW28Jz7LK_rXplzVHEUCHx668x6PhACsvXorf7m2qpTY-Hn_.png);
    bottom: calc(100% - 24px);
    height: 69px;
    right: calc(50% - 40px);
    width: 82px;
    z-index: 1
}

[data-id=zsalesiq][seasonal-type=christmas] .seasonal-float-img:has(~.zsiq-toggle) {
    display: none
}

[data-id=zsalesiq][seasonal-type=christmas][position*=left] .seasonal-float-img {
    left: calc(50% - 42px);
    right: auto
}

[data-id=zsalesiq][seasonal-type=newyear] .seasonal-float-img {
    bottom: 48px;
    right: 0
}

[data-id=zsalesiq][seasonal-type=newyear] .seasonal-float-img,
[seasonal-type=newyear] .seasonal-float-img {
    background-image: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/images/Newyear/newyear_2x_3iQ5cVthsPykgF0WDw97KaqGGrQ9LZzpYw7jM20Qxr2d7FS7E-2wnVPX3L1Lk_4v_.png);
    height: 48px;
    width: 55px;
    z-index: 1
}

[seasonal-type=newyear] .seasonal-float-img {
    bottom: 58px;
    right: 8px
}

.zsiq-overlay {
    background-color: hsla(0, 0%, 100%, .8);
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 111
}

.zsiq-celebration-gif,
.zsiq-celebration-img {
    background-image: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/images/Anniversary/celebration_gif_SmBDLckHnjURSLEml9TpmeaOmne4BCRf7chMUG3ZwoqQ_f5HCOJNspAYk65pSIUF_.gif);
    background-repeat: no-repeat;
    height: 600px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    z-index: 1
}

.zsiq-celebration-img {
    animation: celebration .85s linear;
    background-image: url(/salesiq/RESOURCE_BUNDLES/embedfloat/ASSETS_V6/images/Anniversary/celebration_sticker_K_SAYv-NkGW6srEiS9QJskTrQwUvBiuZ0tXfK9wHb_wuC0d7sLhaqrS6sQLRYvky_.png);
    height: 390px;
    transform-origin: left top;
    width: 419px
}

@keyframes celebration {
    0% {
        left: 50%;
        scale: 1;
        top: 50%
    }
    to {
        left: calc(100% - 80px);
        scale: .2;
        top: calc(100% - 60px)
    }
}

.confetti-container {
    height: 100%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%
}

.confetti {
    animation: fall linear;
    animation-duration: 1s;
    background-color: red;
    height: 20px;
    opacity: .7;
    position: absolute;
    transform-origin: left;
    width: 10px
}

@keyframes fall {
    0% {
        transform: rotate(0deg)
    }
    to {
        top: 100vh;
        transform: rotate(1turn)
    }
}

@keyframes zsiqcntanim {
    0%,
    60% {
        opacity: 0;
        transform: translateX(20px)
    }
    to {
        opacity: 1;
        transform: translateX(0)
    }
}

@keyframes indicator-anim {
    0% {
        opacity: 1;
        transform: scale(0)
    }
    to {
        opacity: 0;
        transform: scale(2)
    }
}

@keyframes move-forward {
    0% {
        left: -100%
    }
    to {
        left: 100%
    }
}

@keyframes circle-load {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

@media (max-width:554px) {
    .zsiq-toggle {
        display: none
    }
    .zsiq-tultip-close {
        opacity: 1 !important
    }
    .seasonal-float-img,
    .zsiq-float {
        transform: scale(.8) !important
    }
    [position*=left] .seasonal-float-img {
        transform: scale(.8) rotateY(180deg) !important
    }
    [position=left-top] .seasonal-float-img,
    [position=right-top] .seasonal-float-img {
        top: 17px
    }
    .zsiq-custom-stickercont {
        max-height: 150px;
        max-width: 150px
    }
    .chat-iframe-wrap,
    .chat-loader-cont {
        bottom: 0 !important;
        height: 100% !important;
        left: auto !important;
        max-height: 100% !important;
        max-width: 100% !important;
        right: 0 !important;
        width: 100% !important
    }
    .chat-iframe-wrap,
    .chat-iframe-wrap iframe,
    .chat-loader-cont {
        border-radius: 0 !important
    }
    .chat-loader-cont {
        margin: 0 !important
    }
    .siqcw-chat-cont {
        border-radius: 0 !important
    }
    [seasonal-type=christmas] .seasonal-float-img,
    [seasonal-type=newyear] .seasonal-float-img {
        transform: scale(.8) translateY(6px) !important;
        transform-origin: bottom
    }
    [seasonal-type=christmas][position*=left] .seasonal-float-img,
    [seasonal-type=newyear][position*=left] .seasonal-float-img {
        transform: scale(.8) translate(5px, 6px) !important
    }
    .mobile-view {
        display: none
    }
}

.zsiq-posfix {
    position: fixed
}

.zsiq-posabs {
    position: absolute
}

.zsiq-posrel {
    position: relative
}

.zsiq-v-abs-center {
    bottom: 0;
    margin: auto;
    position: absolute;
    top: 0
}

.zsiq-rgt-auto {
    right: auto
}

.zsiq-l0 {
    left: 0
}

.zsiq-flex {
    display: flex
}

.zsiq-flexC,
.zsiq-flexM {
    align-items: center;
    display: flex
}

.zsiq-flexM,
.zsiq-iflexM {
    justify-content: center
}

.zsiq-iflexM {
    align-items: center;
    display: inline-flex
}

.zsiq-flexG {
    flex-grow: 1
}

.zsiq-hide {
    display: none
}

.zsiq-show {
    display: block
}

.zsiq-v-hid {
    visibility: hidden
}

.zsiq-v-vis {
    visibility: visible
}

.zsiq-curP {
    cursor: pointer
}

.zsiq-font14 {
    font-size: 14px
}

.zsiq-clr3 {
    color: #121212
}

.zsiq-clr6 {
    color: #666
}

.zsiq-bg-white {
    background-color: #fff
}

.zsiq-m0 {
    margin: 0
}

.zsiq-mT10 {
    margin-top: 10px
}

.zsiq-mT3 {
    margin-top: 3px
}

.zsiq-ovrflw-auto {
    overflow-x: hidden;
    overflow-y: auto
}

.zsiq-bdr-rad100 {
    border-radius: 100%
}

.zsiq-elips {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.zsiq-hw-100 {
    height: 100%;
    width: 100%
}