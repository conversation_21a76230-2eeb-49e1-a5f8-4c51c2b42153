window['google-tag-managerDeps'] = ["/integrations/vendor/commons.59560acdd69ed701c941.js"];window['google-tag-managerLoader'] = function() { return window["google-tag-managerIntegration"]=function(t){function e(e){for(var n,i,s=e[0],p=e[1],c=e[2],l=0,g=[];l<s.length;l++)i=s[l],Object.prototype.hasOwnProperty.call(o,i)&&o[i]&&g.push(o[i][0]),o[i]=0;for(n in p)Object.prototype.hasOwnProperty.call(p,n)&&(t[n]=p[n]);for(u&&u(e);g.length;)g.shift()();return a.push.apply(a,c||[]),r()}function r(){for(var t,e=0;e<a.length;e++){for(var r=a[e],n=!0,s=1;s<r.length;s++){var p=r[s];0!==o[p]&&(n=!1)}n&&(a.splice(e--,1),t=i(i.s=r[0]))}return t}var n={},o={59:0},a=[];function i(e){if(n[e])return n[e].exports;var r=n[e]={i:e,l:!1,exports:{}};return t[e].call(r.exports,r,r.exports,i),r.l=!0,r.exports}i.m=t,i.c=n,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(r,n,function(e){return t[e]}.bind(null,n));return r},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="";var s=window.webpackJsonp_name_Integration=window.webpackJsonp_name_Integration||[],p=s.push.bind(s);s.push=e,s=s.slice();for(var c=0;c<s.length;c++)e(s[c]);var u=p;return a.push(["sbnM",0]),r()}({sbnM:function(t,e,r){"use strict";var n=r("hjHq"),o=r("5mDK")("dataLayer",{wrap:!1}),a=t.exports=n("Google Tag Manager").global("dataLayer").global("google_tag_manager").option("containerId","").option("environment","").option("trackNamedPages",!0).option("trackCategorizedPages",!0).tag("no-env",'<script src="//www.googletagmanager.com/gtm.js?id={{ containerId }}&l=dataLayer">').tag("with-env",'<script src="//www.googletagmanager.com/gtm.js?id={{ containerId }}&l=dataLayer&gtm_preview={{ environment }}">');a.prototype.initialize=function(){o({"gtm.start":Number(new Date),event:"gtm.js"}),this.options.environment.length?this.load("with-env",this.options,this.ready):this.load("no-env",this.options,this.ready)},a.prototype.loaded=function(){return!(!window.dataLayer||Array.prototype.push===window.dataLayer.push)},a.prototype.page=function(t){var e=t.category(),r=t.fullName(),n=this.options;n.trackAllPages&&this.track(t.track()),e&&n.trackCategorizedPages&&this.track(t.track(e)),r&&n.trackNamedPages&&this.track(t.track(r))},a.prototype.track=function(t){var e=t.properties(),r=this.analytics.user().id(),n=this.analytics.user().anonymousId();r&&(e.userId=r),n&&(e.segmentAnonymousId=n),e.event=t.event(),o(e)}}});
//# sourceMappingURL=google-tag-manager.js.map
};