(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [4641], {
        14418: t => {
            function e() {
                return t.exports = e = Object.assign ? Object.assign.bind() : function(t) {
                    for (var e = 1; e < arguments.length; e++) {
                        var n = arguments[e];
                        for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (t[r] = n[r])
                    }
                    return t
                }, t.exports.__esModule = !0, t.exports.default = t.exports, e.apply(this, arguments)
            }
            t.exports = e, t.exports.__esModule = !0, t.exports.default = t.exports
        },
        79292: (t, e, n) => {
            var r = n(22129);
            t.exports = function(t, e) {
                t.prototype = Object.create(e.prototype), t.prototype.constructor = t, r(t, e)
            }, t.exports.__esModule = !0, t.exports.default = t.exports
        },
        72328: t => {
            t.exports = function(t, e) {
                if (null == t) return {};
                var n, r, o = {},
                    u = Object.keys(t);
                for (r = 0; r < u.length; r++) n = u[r], e.indexOf(n) >= 0 || (o[n] = t[n]);
                return o
            }, t.exports.__esModule = !0, t.exports.default = t.exports
        },
        22129: t => {
            function e(n, r) {
                return t.exports = e = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(t, e) {
                    return t.__proto__ = e, t
                }, t.exports.__esModule = !0, t.exports.default = t.exports, e(n, r)
            }
            t.exports = e, t.exports.__esModule = !0, t.exports.default = t.exports
        },
        5482: (t, e) => {
            Object.defineProperty(e, "__esModule", {
                value: !0
            }), e.createChangeEmitter = function() {
                var t = [],
                    e = t;

                function n() {
                    e === t && (e = t.slice())
                }
                return {
                    listen: function(t) {
                        if ("function" != typeof t) throw new Error("Expected listener to be a function.");
                        var r = !0;
                        return n(), e.push(t),
                            function() {
                                if (r) {
                                    r = !1, n();
                                    var o = e.indexOf(t);
                                    e.splice(o, 1)
                                }
                            }
                    },
                    emit: function() {
                        for (var n = t = e, r = 0; r < n.length; r++) n[r].apply(n, arguments)
                    }
                }
            }
        },
        19802: (t, e) => {
            function n() {
                var t = this.constructor.getDerivedStateFromProps(this.props, this.state);
                null != t && this.setState(t)
            }

            function r(t) {
                this.setState(function(e) {
                    var n = this.constructor.getDerivedStateFromProps(t, e);
                    return null != n ? n : null
                }.bind(this))
            }

            function o(t, e) {
                try {
                    var n = this.props,
                        r = this.state;
                    this.props = t, this.state = e, this.__reactInternalSnapshotFlag = !0, this.__reactInternalSnapshot = this.getSnapshotBeforeUpdate(n, r)
                } finally {
                    this.props = n, this.state = r
                }
            }
            Object.defineProperty(e, "__esModule", {
                value: !0
            }), n.__suppressDeprecationWarning = !0, r.__suppressDeprecationWarning = !0, o.__suppressDeprecationWarning = !0, e.polyfill = function(t) {
                var e = t.prototype;
                if (!e || !e.isReactComponent) throw new Error("Can only polyfill class components");
                if ("function" != typeof t.getDerivedStateFromProps && "function" != typeof e.getSnapshotBeforeUpdate) return t;
                var u = null,
                    i = null,
                    c = null;
                if ("function" == typeof e.componentWillMount ? u = "componentWillMount" : "function" == typeof e.UNSAFE_componentWillMount && (u = "UNSAFE_componentWillMount"), "function" == typeof e.componentWillReceiveProps ? i = "componentWillReceiveProps" : "function" == typeof e.UNSAFE_componentWillReceiveProps && (i = "UNSAFE_componentWillReceiveProps"), "function" == typeof e.componentWillUpdate ? c = "componentWillUpdate" : "function" == typeof e.UNSAFE_componentWillUpdate && (c = "UNSAFE_componentWillUpdate"), null !== u || null !== i || null !== c) {
                    var a = t.displayName || t.name,
                        p = "function" == typeof t.getDerivedStateFromProps ? "getDerivedStateFromProps()" : "getSnapshotBeforeUpdate()";
                    throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n" + a + " uses " + p + " but also contains the following legacy lifecycles:" + (null !== u ? "\n  " + u : "") + (null !== i ? "\n  " + i : "") + (null !== c ? "\n  " + c : "") + "\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")
                }
                if ("function" == typeof t.getDerivedStateFromProps && (e.componentWillMount = n, e.componentWillReceiveProps = r), "function" == typeof e.getSnapshotBeforeUpdate) {
                    if ("function" != typeof e.componentDidUpdate) throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");
                    e.componentWillUpdate = o;
                    var s = e.componentDidUpdate;
                    e.componentDidUpdate = function(t, e, n) {
                        var r = this.__reactInternalSnapshotFlag ? this.__reactInternalSnapshot : n;
                        s.call(this, t, e, r)
                    }
                }
                return t
            }
        },
        54641: (t, e, n) => {
            function r(t) {
                return t && "object" == typeof t && "default" in t ? t.default : t
            }
            Object.defineProperty(e, "__esModule", {
                value: !0
            });
            var o = n(24980),
                u = r(o),
                i = r(n(14418)),
                c = r(n(87847)),
                a = r(n(79292)),
                p = n(19802),
                s = r(n(72328)),
                f = r(n(42500)),
                l = n(5482),
                y = r(n(60008)),
                h = function(t, e) {
                    return function(n) {
                        return n[t] = e, n
                    }
                },
                d = function(t) {
                    return "string" == typeof t ? t : t ? t.displayName || t.name || "Component" : void 0
                },
                v = function(t) {
                    return function(e) {
                        var n = o.createFactory(e);
                        return function(e) {
                            return n(t(e))
                        }
                    }
                },
                m = function(t, e) {
                    for (var n = {}, r = 0; r < e.length; r++) {
                        var o = e[r];
                        t.hasOwnProperty(o) && (n[o] = t[o])
                    }
                    return n
                },
                b = function(t, e) {
                    var n = {};
                    for (var r in t) t.hasOwnProperty(r) && (n[r] = e(t[r], r));
                    return n
                },
                g = function(t, e) {
                    for (var n = i({}, t), r = 0; r < e.length; r++) {
                        var o = e[r];
                        n.hasOwnProperty(o) && delete n[o]
                    }
                    return n
                },
                P = Object.keys,
                S = function() {},
                O = function(t) {
                    return t
                },
                _ = function(t) {
                    function e() {
                        return t.apply(this, arguments) || this
                    }
                    return a(e, t), e.prototype.render = function() {
                        return null
                    }, e
                }(o.Component),
                w = function(t) {
                    return function(e) {
                        var n = o.createFactory(e),
                            r = function(e) {
                                function r() {
                                    return e.apply(this, arguments) || this
                                }
                                a(r, e);
                                var o = r.prototype;
                                return o.shouldComponentUpdate = function(e) {
                                    return t(this.props, e)
                                }, o.render = function() {
                                    return n(this.props)
                                }, r
                            }(o.Component);
                        return r
                    }
                },
                x = function(t) {
                    return w((function(e, n) {
                        return !c(m(n, t), m(e, t))
                    }))
                },
                F = function(t) {
                    return Boolean(t && t.prototype && "function" == typeof t.prototype.render)
                },
                E = {
                    fromESObservable: null,
                    toESObservable: null
                },
                C = {
                    fromESObservable: function(t) {
                        return "function" == typeof E.fromESObservable ? E.fromESObservable(t) : t
                    },
                    toESObservable: function(t) {
                        return "function" == typeof E.toESObservable ? E.toESObservable(t) : t
                    }
                },
                U = function(t) {
                    return function(e) {
                        return function(n) {
                            function r() {
                                for (var r, o, u = arguments.length, i = new Array(u), c = 0; c < u; c++) i[c] = arguments[c];
                                return (o = n.call.apply(n, [this].concat(i)) || this).state = {
                                    vdom: null
                                }, o.propsEmitter = l.createChangeEmitter(), o.props$ = t.fromESObservable(((r = {
                                    subscribe: function(t) {
                                        return {
                                            unsubscribe: o.propsEmitter.listen((function(e) {
                                                e ? t.next(e) : t.complete()
                                            }))
                                        }
                                    }
                                })[y] = function() {
                                    return this
                                }, r)), o.vdom$ = t.toESObservable(e(o.props$)), o
                            }
                            a(r, n);
                            var o = r.prototype;
                            return o.componentWillMount = function() {
                                var t = this;
                                this.subscription = this.vdom$.subscribe({
                                    next: function(e) {
                                        t.setState({
                                            vdom: e
                                        })
                                    }
                                }), this.propsEmitter.emit(this.props)
                            }, o.componentWillReceiveProps = function(t) {
                                this.propsEmitter.emit(t)
                            }, o.shouldComponentUpdate = function(t, e) {
                                return e.vdom !== this.state.vdom
                            }, o.componentWillUnmount = function() {
                                this.propsEmitter.emit(), this.subscription.unsubscribe()
                            }, o.render = function() {
                                return this.state.vdom
                            }, r
                        }(o.Component)
                    }
                },
                j = function(t) {
                    return t
                },
                W = function(t) {
                    var e = U({
                        fromESObservable: j,
                        toESObservable: j
                    });
                    return function(n) {
                        return function(r) {
                            var u = o.createFactory(r),
                                i = t.fromESObservable,
                                c = t.toESObservable;
                            return e((function(t) {
                                var e;
                                return (e = {
                                    subscribe: function(e) {
                                        var r = c(n(i(t))).subscribe({
                                            next: function(t) {
                                                return e.next(u(t))
                                            }
                                        });
                                        return {
                                            unsubscribe: function() {
                                                return r.unsubscribe()
                                            }
                                        }
                                    }
                                })[y] = function() {
                                    return this
                                }, e
                            }))
                        }
                    }
                },
                D = function(t) {
                    return function() {
                        var e, n = l.createChangeEmitter(),
                            r = t.fromESObservable(((e = {
                                subscribe: function(t) {
                                    return {
                                        unsubscribe: n.listen((function(e) {
                                            return t.next(e)
                                        }))
                                    }
                                }
                            })[y] = function() {
                                return this
                            }, e));
                        return {
                            handler: n.emit,
                            stream: r
                        }
                    }
                },
                A = D(C);
            e.mapProps = v, e.withProps = function(t) {
                return v((function(e) {
                    return i({}, e, "function" == typeof t ? t(e) : t)
                }))
            }, e.withPropsOnChange = function(t, e) {
                return function(n) {
                    var r = o.createFactory(n),
                        u = "function" == typeof t ? t : function(e, n) {
                            return !c(m(e, t), m(n, t))
                        },
                        s = function(t) {
                            function n() {
                                for (var n, r = arguments.length, o = new Array(r), u = 0; u < r; u++) o[u] = arguments[u];
                                return (n = t.call.apply(t, [this].concat(o)) || this).state = {
                                    computedProps: e(n.props),
                                    prevProps: n.props
                                }, n
                            }
                            return a(n, t), n.getDerivedStateFromProps = function(t, n) {
                                return u(n.prevProps, t) ? {
                                    computedProps: e(t),
                                    prevProps: t
                                } : {
                                    prevProps: t
                                }
                            }, n.prototype.render = function() {
                                return r(i({}, this.props, this.state.computedProps))
                            }, n
                        }(o.Component);
                    return p.polyfill(s), s
                }
            }, e.withHandlers = function(t) {
                return function(e) {
                    var n = o.createFactory(e),
                        r = function(e) {
                            function r() {
                                for (var n, r = arguments.length, o = new Array(r), u = 0; u < r; u++) o[u] = arguments[u];
                                return (n = e.call.apply(e, [this].concat(o)) || this).handlers = b("function" == typeof t ? t(n.props) : t, (function(t) {
                                    return function() {
                                        return t(n.props).apply(void 0, arguments)
                                    }
                                })), n
                            }
                            return a(r, e), r.prototype.render = function() {
                                return n(i({}, this.props, this.handlers))
                            }, r
                        }(o.Component);
                    return r
                }
            }, e.defaultProps = function(t) {
                return function(e) {
                    var n = o.createFactory(e),
                        r = function(t) {
                            return n(t)
                        };
                    return r.defaultProps = t, r
                }
            }, e.renameProp = function(t, e) {
                return v((function(n) {
                    var r;
                    return i({}, g(n, [t]), ((r = {})[e] = n[t], r))
                }))
            }, e.renameProps = function(t) {
                return v((function(e) {
                    return i({}, g(e, P(t)), (n = m(e, P(t)), r = function(e, n) {
                        return t[n]
                    }, P(n).reduce((function(t, e) {
                        var o = n[e];
                        return t[r(0, e)] = o, t
                    }), {})));
                    var n, r
                }))
            }, e.flattenProp = function(t) {
                return function(e) {
                    var n = o.createFactory(e);
                    return function(e) {
                        return n(i({}, e, e[t]))
                    }
                }
            }, e.withState = function(t, e, n) {
                return function(r) {
                    var u = o.createFactory(r),
                        c = function(r) {
                            function o() {
                                for (var t, e = arguments.length, o = new Array(e), u = 0; u < e; u++) o[u] = arguments[u];
                                return (t = r.call.apply(r, [this].concat(o)) || this).state = {
                                    stateValue: "function" == typeof n ? n(t.props) : n
                                }, t.updateStateValue = function(e, n) {
                                    return t.setState((function(t) {
                                        var n = t.stateValue;
                                        return {
                                            stateValue: "function" == typeof e ? e(n) : e
                                        }
                                    }), n)
                                }, t
                            }
                            return a(o, r), o.prototype.render = function() {
                                var n;
                                return u(i({}, this.props, ((n = {})[t] = this.state.stateValue, n[e] = this.updateStateValue, n)))
                            }, o
                        }(o.Component);
                    return c
                }
            }, e.withStateHandlers = function(t, e) {
                return function(n) {
                    var r = o.createFactory(n),
                        u = function(n) {
                            function o() {
                                for (var r, o = arguments.length, u = new Array(o), i = 0; i < o; i++) u[i] = arguments[i];
                                return (r = n.call.apply(n, [this].concat(u)) || this).state = "function" == typeof t ? t(r.props) : t, r.stateUpdaters = b(e, (function(t) {
                                    return function(e) {
                                        for (var n = arguments.length, o = new Array(n > 1 ? n - 1 : 0), u = 1; u < n; u++) o[u - 1] = arguments[u];
                                        e && "function" == typeof e.persist && e.persist(), r.setState((function(n, r) {
                                            return t(n, r).apply(void 0, [e].concat(o))
                                        }))
                                    }
                                })), r
                            }
                            return a(o, n), o.prototype.render = function() {
                                return r(i({}, this.props, this.state, this.stateUpdaters))
                            }, o
                        }(o.Component);
                    return u
                }
            }, e.withReducer = function(t, e, n, r) {
                return function(u) {
                    var c = o.createFactory(u),
                        p = function(o) {
                            function u() {
                                for (var t, e = arguments.length, r = new Array(e), u = 0; u < e; u++) r[u] = arguments[u];
                                return (t = o.call.apply(o, [this].concat(r)) || this).state = {
                                    stateValue: t.initializeStateValue()
                                }, t.dispatch = function(e, r) {
                                    return void 0 === r && (r = S), t.setState((function(t) {
                                        var r = t.stateValue;
                                        return {
                                            stateValue: n(r, e)
                                        }
                                    }), (function() {
                                        return r(t.state.stateValue)
                                    }))
                                }, t
                            }
                            a(u, o);
                            var p = u.prototype;
                            return p.initializeStateValue = function() {
                                return void 0 !== r ? "function" == typeof r ? r(this.props) : r : n(void 0, {
                                    type: "@@recompose/INIT"
                                })
                            }, p.render = function() {
                                var n;
                                return c(i({}, this.props, ((n = {})[t] = this.state.stateValue, n[e] = this.dispatch, n)))
                            }, u
                        }(o.Component);
                    return p
                }
            }, e.branch = function(t, e, n) {
                return void 0 === n && (n = O),
                    function(r) {
                        var u, i;
                        return function(c) {
                            return t(c) ? (u = u || o.createFactory(e(r)))(c) : (i = i || o.createFactory(n(r)))(c)
                        }
                    }
            }, e.renderComponent = function(t) {
                return function(e) {
                    var n = o.createFactory(t);
                    return function(t) {
                        return n(t)
                    }
                }
            }, e.renderNothing = function(t) {
                return _
            }, e.shouldUpdate = w, e.pure = function(t) {
                return w((function(t, e) {
                    return !c(t, e)
                }))(t)
            }, e.onlyUpdateForKeys = x, e.onlyUpdateForPropTypes = function(t) {
                var e = t.propTypes,
                    n = Object.keys(e || {});
                return x(n)(t)
            }, e.withContext = function(t, e) {
                return function(n) {
                    var r = o.createFactory(n),
                        u = function(t) {
                            function n() {
                                for (var n, r = arguments.length, o = new Array(r), u = 0; u < r; u++) o[u] = arguments[u];
                                return (n = t.call.apply(t, [this].concat(o)) || this).getChildContext = function() {
                                    return e(n.props)
                                }, n
                            }
                            return a(n, t), n.prototype.render = function() {
                                return r(this.props)
                            }, n
                        }(o.Component);
                    return u.childContextTypes = t, u
                }
            }, e.getContext = function(t) {
                return function(e) {
                    var n = o.createFactory(e),
                        r = function(t, e) {
                            return n(i({}, t, e))
                        };
                    return r.contextTypes = t, r
                }
            }, e.lifecycle = function(t) {
                return function(e) {
                    var n = o.createFactory(e),
                        r = function(t) {
                            function e() {
                                return t.apply(this, arguments) || this
                            }
                            return a(e, t), e.prototype.render = function() {
                                return n(i({}, this.props, this.state))
                            }, e
                        }(o.Component);
                    return Object.keys(t).forEach((function(e) {
                        return r.prototype[e] = t[e]
                    })), r
                }
            }, e.toClass = function(t) {
                var e, n;
                return F(t) ? t : (n = e = function(e) {
                    function n() {
                        return e.apply(this, arguments) || this
                    }
                    return a(n, e), n.prototype.render = function() {
                        return "string" == typeof t ? u.createElement(t, this.props) : t(this.props, this.context)
                    }, n
                }(o.Component), e.displayName = d(t), e.propTypes = t.propTypes, e.contextTypes = t.contextTypes, e.defaultProps = t.defaultProps, n)
            }, e.toRenderProps = function(t) {
                return t((function(t) {
                    return t.children(t)
                }))
            }, e.fromRenderProps = function(t, e, n) {
                return void 0 === n && (n = "children"),
                    function(r) {
                        var o = u.createFactory(r),
                            c = u.createFactory(t);
                        return function(t) {
                            var r;
                            return c(((r = {})[n] = function() {
                                return o(i({}, t, e.apply(void 0, arguments)))
                            }, r))
                        }
                    }
            }, e.setStatic = h, e.setPropTypes = function(t) {
                return h("propTypes", t)
            }, e.setDisplayName = function(t) {
                return h("displayName", t)
            }, e.compose = function() {
                for (var t = arguments.length, e = new Array(t), n = 0; n < t; n++) e[n] = arguments[n];
                return e.reduce((function(t, e) {
                    return function() {
                        return t(e.apply(void 0, arguments))
                    }
                }), (function(t) {
                    return t
                }))
            }, e.getDisplayName = d, e.wrapDisplayName = function(t, e) {
                return e + "(" + d(t) + ")"
            }, e.shallowEqual = c, e.isClassComponent = F, e.createSink = function(t) {
                var e = function(e) {
                    function n() {
                        for (var t, n = arguments.length, r = new Array(n), o = 0; o < n; o++) r[o] = arguments[o];
                        return (t = e.call.apply(e, [this].concat(r)) || this).state = {}, t
                    }
                    return a(n, e), n.getDerivedStateFromProps = function(e) {
                        return t(e), null
                    }, n.prototype.render = function() {
                        return null
                    }, n
                }(o.Component);
                return p.polyfill(e), e
            }, e.componentFromProp = function(t) {
                var e = function(e) {
                    return o.createElement(e[t], g(e, [t]))
                };
                return e.displayName = "componentFromProp(" + t + ")", e
            }, e.nest = function() {
                for (var t = arguments.length, e = new Array(t), n = 0; n < t; n++) e[n] = arguments[n];
                var r = e.map(o.createFactory);
                return function(t) {
                    var e = t.children,
                        n = s(t, ["children"]);
                    return r.reduceRight((function(t, e) {
                        return e(n, t)
                    }), e)
                }
            }, e.hoistStatics = function(t, e) {
                return function(n) {
                    var r = t(n);
                    return f(r, n, e), r
                }
            }, e.componentFromStream = function(t) {
                return U(C)(t)
            }, e.componentFromStreamWithConfig = U, e.mapPropsStream = function(t) {
                return W(C)(t)
            }, e.mapPropsStreamWithConfig = W, e.createEventHandler = A, e.createEventHandlerWithConfig = D, e.setObservableConfig = function(t) {
                E = t
            }
        },
        87847: t => {
            var e = Object.prototype.hasOwnProperty;

            function n(t, e) {
                return t === e ? 0 !== t || 0 !== e || 1 / t == 1 / e : t != t && e != e
            }
            t.exports = function(t, r) {
                if (n(t, r)) return !0;
                if ("object" != typeof t || null === t || "object" != typeof r || null === r) return !1;
                var o = Object.keys(t),
                    u = Object.keys(r);
                if (o.length !== u.length) return !1;
                for (var i = 0; i < o.length; i++)
                    if (!e.call(r, o[i]) || !n(t[o[i]], r[o[i]])) return !1;
                return !0
            }
        },
        42500: t => {
            var e = {
                    childContextTypes: !0,
                    contextTypes: !0,
                    defaultProps: !0,
                    displayName: !0,
                    getDefaultProps: !0,
                    getDerivedStateFromProps: !0,
                    mixins: !0,
                    propTypes: !0,
                    type: !0
                },
                n = {
                    name: !0,
                    length: !0,
                    prototype: !0,
                    caller: !0,
                    callee: !0,
                    arguments: !0,
                    arity: !0
                },
                r = Object.defineProperty,
                o = Object.getOwnPropertyNames,
                u = Object.getOwnPropertySymbols,
                i = Object.getOwnPropertyDescriptor,
                c = Object.getPrototypeOf,
                a = c && c(Object);
            t.exports = function t(p, s, f) {
                if ("string" != typeof s) {
                    if (a) {
                        var l = c(s);
                        l && l !== a && t(p, l, f)
                    }
                    var y = o(s);
                    u && (y = y.concat(u(s)));
                    for (var h = 0; h < y.length; ++h) {
                        var d = y[h];
                        if (!(e[d] || n[d] || f && f[d])) {
                            var v = i(s, d);
                            try {
                                r(p, d, v)
                            } catch (t) {}
                        }
                    }
                    return p
                }
                return p
            }
        },
        60008: (t, e, n) => {
            t = n.nmd(t), Object.defineProperty(e, "__esModule", {
                value: !0
            });
            var r, o, u = (r = n(19123)) && r.__esModule ? r : {
                default: r
            };
            o = "undefined" != typeof self ? self : "undefined" != typeof window ? window : void 0 !== n.g ? n.g : t;
            var i = (0, u.default)(o);
            e.default = i
        },
        19123: (t, e) => {
            Object.defineProperty(e, "__esModule", {
                value: !0
            }), e.default = function(t) {
                var e, n = t.Symbol;
                return "function" == typeof n ? n.observable ? e = n.observable : (e = n("observable"), n.observable = e) : e = "@@observable", e
            }
        }
    }
]);
//# sourceMappingURL=4641.a2c2d6d4e24f3659.js.map