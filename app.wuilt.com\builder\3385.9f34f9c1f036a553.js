(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [3385], {
        20793: (t, e, n) => {
            function r(t, e) {
                (null == e || e > t.length) && (e = t.length);
                for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
                return r
            }
            n.d(e, {
                Z: () => r
            })
        },
        95767: (t, e, n) => {
            n.d(e, {
                Z: () => o
            });
            var r = n(25840);

            function o(t, e, n) {
                return (e = (0, r.Z)(e)) in t ? Object.defineProperty(t, e, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : t[e] = n, t
            }
        },
        90056: (t, e, n) => {
            n.d(e, {
                Z: () => i
            });
            var r = n(95767);

            function o(t, e) {
                var n = Object.keys(t);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(t);
                    e && (r = r.filter((function(e) {
                        return Object.getOwnPropertyDescriptor(t, e).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function i(t) {
                for (var e = 1; e < arguments.length; e++) {
                    var n = null != arguments[e] ? arguments[e] : {};
                    e % 2 ? o(Object(n), !0).forEach((function(e) {
                        (0, r.Z)(t, e, n[e])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : o(Object(n)).forEach((function(e) {
                        Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e))
                    }))
                }
                return t
            }
        },
        3150: (t, e, n) => {
            n.d(e, {
                Z: () => o
            });
            var r = n(15878);

            function o(t, e) {
                if (null == t) return {};
                var n, o, i = (0, r.Z)(t, e);
                if (Object.getOwnPropertySymbols) {
                    var c = Object.getOwnPropertySymbols(t);
                    for (o = 0; o < c.length; o++) n = c[o], e.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(t, n) && (i[n] = t[n])
                }
                return i
            }
        },
        88060: (t, e, n) => {
            n.d(e, {
                Z: () => o
            });
            var r = n(17880);

            function o(t, e) {
                return function(t) {
                    if (Array.isArray(t)) return t
                }(t) || function(t, e) {
                    var n = null == t ? null : "undefined" != typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
                    if (null != n) {
                        var r, o, i, c, u = [],
                            l = !0,
                            f = !1;
                        try {
                            if (i = (n = n.call(t)).next, 0 === e) {
                                if (Object(n) !== n) return;
                                l = !1
                            } else
                                for (; !(l = (r = i.call(n)).done) && (u.push(r.value), u.length !== e); l = !0);
                        } catch (t) {
                            f = !0, o = t
                        } finally {
                            try {
                                if (!l && null != n.return && (c = n.return(), Object(c) !== c)) return
                            } finally {
                                if (f) throw o
                            }
                        }
                        return u
                    }
                }(t, e) || (0, r.Z)(t, e) || function() {
                    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }()
            }
        },
        24645: (t, e, n) => {
            function r(t, e) {
                return e || (e = t.slice(0)), Object.freeze(Object.defineProperties(t, {
                    raw: {
                        value: Object.freeze(e)
                    }
                }))
            }
            n.d(e, {
                Z: () => r
            })
        },
        25840: (t, e, n) => {
            n.d(e, {
                Z: () => o
            });
            var r = n(66522);

            function o(t) {
                var e = function(t, e) {
                    if ("object" !== (0, r.Z)(t) || null === t) return t;
                    var n = t[Symbol.toPrimitive];
                    if (void 0 !== n) {
                        var o = n.call(t, "string");
                        if ("object" !== (0, r.Z)(o)) return o;
                        throw new TypeError("@@toPrimitive must return a primitive value.")
                    }
                    return String(t)
                }(t);
                return "symbol" === (0, r.Z)(e) ? e : String(e)
            }
        },
        66522: (t, e, n) => {
            function r(t) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
                    return typeof t
                } : function(t) {
                    return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
                }, r(t)
            }
            n.d(e, {
                Z: () => r
            })
        },
        17880: (t, e, n) => {
            n.d(e, {
                Z: () => o
            });
            var r = n(20793);

            function o(t, e) {
                if (t) {
                    if ("string" == typeof t) return (0, r.Z)(t, e);
                    var n = Object.prototype.toString.call(t).slice(8, -1);
                    return "Object" === n && t.constructor && (n = t.constructor.name), "Map" === n || "Set" === n ? Array.from(t) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? (0, r.Z)(t, e) : void 0
                }
            }
        },
        98519: (t, e, n) => {
            Object.defineProperty(e, "__esModule", {
                value: !0
            });
            var r = n(60893).useLayoutEffect;
            e.default = r
        },
        75580: (t, e, n) => {
            n.d(e, {
                Me: () => a
            });
            var r = n(4029),
                o = n(47372);

            function i(t) {
                return (0, o.kK)(t) ? t : t.contextElement
            }

            function c(t) {
                const e = i(t);
                if (!(0, o.Re)(e)) return (0, r.ze)(1);
                const n = e.getBoundingClientRect(),
                    {
                        width: c,
                        height: u,
                        $: l
                    } = function(t) {
                        const e = (0, o.Dx)(t);
                        let n = parseFloat(e.width) || 0,
                            i = parseFloat(e.height) || 0;
                        const c = (0, o.Re)(t),
                            u = c ? t.offsetWidth : n,
                            l = c ? t.offsetHeight : i,
                            f = (0, r.NM)(n) !== u || (0, r.NM)(i) !== l;
                        return f && (n = u, i = l), {
                            width: n,
                            height: i,
                            $: f
                        }
                    }(e);
                let f = (l ? (0, r.NM)(n.width) : n.width) / c,
                    a = (l ? (0, r.NM)(n.height) : n.height) / u;
                return f && Number.isFinite(f) || (f = 1), a && Number.isFinite(a) || (a = 1), {
                    x: f,
                    y: a
                }
            }
            const u = (0, r.ze)(0);

            function l(t) {
                const e = (0, o.Jj)(t);
                return (0, o.Pf)() && e.visualViewport ? {
                    x: e.visualViewport.offsetLeft,
                    y: e.visualViewport.offsetTop
                } : u
            }

            function f(t, e, n, u) {
                void 0 === e && (e = !1), void 0 === n && (n = !1);
                const f = t.getBoundingClientRect(),
                    a = i(t);
                let s = (0, r.ze)(1);
                e && (u ? (0, o.kK)(u) && (s = c(u)) : s = c(t));
                const d = function(t, e, n) {
                    return void 0 === e && (e = !1), !(!n || e && n !== (0, o.Jj)(t)) && e
                }(a, n, u) ? l(a) : (0, r.ze)(0);
                let p = (f.left + d.x) / s.x,
                    m = (f.top + d.y) / s.y,
                    h = f.width / s.x,
                    y = f.height / s.y;
                if (a) {
                    const t = (0, o.Jj)(a),
                        e = u && (0, o.kK)(u) ? (0, o.Jj)(u) : u;
                    let n = t.frameElement;
                    for (; n && u && e !== t;) {
                        const t = c(n),
                            e = n.getBoundingClientRect(),
                            r = (0, o.Dx)(n),
                            i = e.left + (n.clientLeft + parseFloat(r.paddingLeft)) * t.x,
                            u = e.top + (n.clientTop + parseFloat(r.paddingTop)) * t.y;
                        p *= t.x, m *= t.y, h *= t.x, y *= t.y, p += i, m += u, n = (0, o.Jj)(n).frameElement
                    }
                }
                return (0, r.JB)({
                    width: h,
                    height: y,
                    x: p,
                    y: m
                })
            }

            function a(t, e, n, c) {
                void 0 === c && (c = {});
                const {
                    ancestorScroll: u = !0,
                    ancestorResize: l = !0,
                    elementResize: a = "function" == typeof ResizeObserver,
                    layoutShift: s = "function" == typeof IntersectionObserver,
                    animationFrame: d = !1
                } = c, p = i(t), m = u || l ? [...p ? (0, o.Kx)(p) : [], ...(0, o.Kx)(e)] : [];
                m.forEach((t => {
                    u && t.addEventListener("scroll", n, {
                        passive: !0
                    }), l && t.addEventListener("resize", n)
                }));
                const h = p && s ? function(t, e) {
                    let n, i = null;
                    const c = (0, o.tF)(t);

                    function u() {
                        clearTimeout(n), i && i.disconnect(), i = null
                    }
                    return function o(l, f) {
                        void 0 === l && (l = !1), void 0 === f && (f = 1), u();
                        const {
                            left: a,
                            top: s,
                            width: d,
                            height: p
                        } = t.getBoundingClientRect();
                        if (l || e(), !d || !p) return;
                        const m = {
                            rootMargin: -(0, r.GW)(s) + "px " + -(0, r.GW)(c.clientWidth - (a + d)) + "px " + -(0, r.GW)(c.clientHeight - (s + p)) + "px " + -(0, r.GW)(a) + "px",
                            threshold: (0, r.Fp)(0, (0, r.VV)(1, f)) || 1
                        };
                        let h = !0;

                        function y(t) {
                            const e = t[0].intersectionRatio;
                            if (e !== f) {
                                if (!h) return o();
                                e ? o(!1, e) : n = setTimeout((() => {
                                    o(!1, 1e-7)
                                }), 100)
                            }
                            h = !1
                        }
                        try {
                            i = new IntersectionObserver(y, { ...m,
                                root: c.ownerDocument
                            })
                        } catch (t) {
                            i = new IntersectionObserver(y, m)
                        }
                        i.observe(t)
                    }(!0), u
                }(p, n) : null;
                let y, b = -1,
                    v = null;
                a && (v = new ResizeObserver((t => {
                    let [r] = t;
                    r && r.target === p && v && (v.unobserve(e), cancelAnimationFrame(b), b = requestAnimationFrame((() => {
                        v && v.observe(e)
                    }))), n()
                })), p && !d && v.observe(p), v.observe(e));
                let g = d ? f(t) : null;
                return d && function e() {
                    const r = f(t);
                    !g || r.x === g.x && r.y === g.y && r.width === g.width && r.height === g.height || n(), g = r, y = requestAnimationFrame(e)
                }(), n(), () => {
                    m.forEach((t => {
                        u && t.removeEventListener("scroll", n), l && t.removeEventListener("resize", n)
                    })), h && h(), v && v.disconnect(), v = null, d && cancelAnimationFrame(y)
                }
            }
            o.tF, o.kK
        },
        4029: (t, e, n) => {
            n.d(e, {
                Ct: () => i,
                Fp: () => u,
                GW: () => f,
                Go: () => x,
                I4: () => v,
                JB: () => F,
                KX: () => S,
                NM: () => l,
                Qq: () => g,
                Rn: () => b,
                VV: () => c,
                Wh: () => w,
                gy: () => j,
                hp: () => y,
                i8: () => O,
                k3: () => h,
                ku: () => m,
                mA: () => r,
                pw: () => Z,
                uZ: () => p,
                yd: () => E,
                ze: () => a
            });
            const r = ["top", "right", "bottom", "left"],
                o = ["start", "end"],
                i = r.reduce(((t, e) => t.concat(e, e + "-" + o[0], e + "-" + o[1])), []),
                c = Math.min,
                u = Math.max,
                l = Math.round,
                f = Math.floor,
                a = t => ({
                    x: t,
                    y: t
                }),
                s = {
                    left: "right",
                    right: "left",
                    bottom: "top",
                    top: "bottom"
                },
                d = {
                    start: "end",
                    end: "start"
                };

            function p(t, e, n) {
                return u(t, c(e, n))
            }

            function m(t, e) {
                return "function" == typeof t ? t(e) : t
            }

            function h(t) {
                return t.split("-")[0]
            }

            function y(t) {
                return t.split("-")[1]
            }

            function b(t) {
                return "x" === t ? "y" : "x"
            }

            function v(t) {
                return "y" === t ? "height" : "width"
            }

            function g(t) {
                return ["top", "bottom"].includes(h(t)) ? "y" : "x"
            }

            function w(t) {
                return b(g(t))
            }

            function O(t, e, n) {
                void 0 === n && (n = !1);
                const r = y(t),
                    o = w(t),
                    i = v(o);
                let c = "x" === o ? r === (n ? "end" : "start") ? "right" : "left" : "start" === r ? "bottom" : "top";
                return e.reference[i] > e.floating[i] && (c = Z(c)), [c, Z(c)]
            }

            function j(t) {
                const e = Z(t);
                return [x(t), e, x(e)]
            }

            function x(t) {
                return t.replace(/start|end/g, (t => d[t]))
            }

            function S(t, e, n, r) {
                const o = y(t);
                let i = function(t, e, n) {
                    const r = ["left", "right"],
                        o = ["right", "left"],
                        i = ["top", "bottom"],
                        c = ["bottom", "top"];
                    switch (t) {
                        case "top":
                        case "bottom":
                            return n ? e ? o : r : e ? r : o;
                        case "left":
                        case "right":
                            return e ? i : c;
                        default:
                            return []
                    }
                }(h(t), "start" === n, r);
                return o && (i = i.map((t => t + "-" + o)), e && (i = i.concat(i.map(x)))), i
            }

            function Z(t) {
                return t.replace(/left|right|bottom|top/g, (t => s[t]))
            }

            function E(t) {
                return "number" != typeof t ? function(t) {
                    return {
                        top: 0,
                        right: 0,
                        bottom: 0,
                        left: 0,
                        ...t
                    }
                }(t) : {
                    top: t,
                    right: t,
                    bottom: t,
                    left: t
                }
            }

            function F(t) {
                return { ...t,
                    top: t.y,
                    left: t.x,
                    right: t.x + t.width,
                    bottom: t.y + t.height
                }
            }
        },
        47372: (t, e, n) => {
            function r(t) {
                return c(t) ? (t.nodeName || "").toLowerCase() : "#document"
            }

            function o(t) {
                var e;
                return (null == t || null == (e = t.ownerDocument) ? void 0 : e.defaultView) || window
            }

            function i(t) {
                var e;
                return null == (e = (c(t) ? t.ownerDocument : t.document) || window.document) ? void 0 : e.documentElement
            }

            function c(t) {
                return t instanceof Node || t instanceof o(t).Node
            }

            function u(t) {
                return t instanceof Element || t instanceof o(t).Element
            }

            function l(t) {
                return t instanceof HTMLElement || t instanceof o(t).HTMLElement
            }

            function f(t) {
                return "undefined" != typeof ShadowRoot && (t instanceof ShadowRoot || t instanceof o(t).ShadowRoot)
            }

            function a(t) {
                const {
                    overflow: e,
                    overflowX: n,
                    overflowY: r,
                    display: o
                } = y(t);
                return /auto|scroll|overlay|hidden|clip/.test(e + r + n) && !["inline", "contents"].includes(o)
            }

            function s(t) {
                return ["table", "td", "th"].includes(r(t))
            }

            function d(t) {
                const e = m(),
                    n = y(t);
                return "none" !== n.transform || "none" !== n.perspective || !!n.containerType && "normal" !== n.containerType || !e && !!n.backdropFilter && "none" !== n.backdropFilter || !e && !!n.filter && "none" !== n.filter || ["transform", "perspective", "filter"].some((t => (n.willChange || "").includes(t))) || ["paint", "layout", "strict", "content"].some((t => (n.contain || "").includes(t)))
            }

            function p(t) {
                let e = v(t);
                for (; l(e) && !h(e);) {
                    if (d(e)) return e;
                    e = v(e)
                }
                return null
            }

            function m() {
                return !("undefined" == typeof CSS || !CSS.supports) && CSS.supports("-webkit-backdrop-filter", "none")
            }

            function h(t) {
                return ["html", "body", "#document"].includes(r(t))
            }

            function y(t) {
                return o(t).getComputedStyle(t)
            }

            function b(t) {
                return u(t) ? {
                    scrollLeft: t.scrollLeft,
                    scrollTop: t.scrollTop
                } : {
                    scrollLeft: t.pageXOffset,
                    scrollTop: t.pageYOffset
                }
            }

            function v(t) {
                if ("html" === r(t)) return t;
                const e = t.assignedSlot || t.parentNode || f(t) && t.host || i(t);
                return f(e) ? e.host : e
            }

            function g(t) {
                const e = v(t);
                return h(e) ? t.ownerDocument ? t.ownerDocument.body : t.body : l(e) && a(e) ? e : g(e)
            }

            function w(t, e) {
                var n;
                void 0 === e && (e = []);
                const r = g(t),
                    i = r === (null == (n = t.ownerDocument) ? void 0 : n.body),
                    c = o(r);
                return i ? e.concat(c, c.visualViewport || [], a(r) ? r : []) : e.concat(r, w(r))
            }
            n.d(e, {
                Dx: () => y,
                Jj: () => o,
                Kx: () => w,
                Lw: () => b,
                Ow: () => v,
                Pf: () => m,
                Py: () => h,
                Re: () => l,
                Ze: () => s,
                Zq: () => f,
                ao: () => a,
                gQ: () => p,
                hT: () => d,
                kK: () => u,
                tF: () => i,
                wk: () => r
            })
        }
    }
]);
//# sourceMappingURL=3385.9f34f9c1f036a553.js.map