(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["node_modules_amplitude_engagement-browser_index_js"], {

        /***/
        "../../node_modules/@amplitude/engagement-browser/index.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    init: () => ( /* binding */ J),
                    /* harmony export */
                    plugin: () => ( /* binding */ Q)
                    /* harmony export */
                });
                var U = function() {
                        function n() {}
                        return n.prototype.getApplicationContext = function() {
                            return {
                                versionName: this.versionName,
                                language: q(),
                                platform: "Web",
                                os: void 0,
                                deviceModel: void 0
                            }
                        }, n
                    }(),
                    q = function() {
                        return typeof navigator < "u" && (navigator.languages && navigator.languages[0] || navigator.language) || ""
                    },
                    B = function() {
                        function n() {
                            this.queue = []
                        }
                        return n.prototype.logEvent = function(e) {
                            this.receiver ? this.receiver(e) : this.queue.length < 512 && this.queue.push(e)
                        }, n.prototype.setEventReceiver = function(e) {
                            this.receiver = e, this.queue.length > 0 && (this.queue.forEach(function(t) {
                                e(t)
                            }), this.queue = [])
                        }, n
                    }(),
                    h = function() {
                        return h = Object.assign || function(e) {
                            for (var t, r = 1, o = arguments.length; r < o; r++) {
                                t = arguments[r];
                                for (var s in t) Object.prototype.hasOwnProperty.call(t, s) && (e[s] = t[s])
                            }
                            return e
                        }, h.apply(this, arguments)
                    };

                function A(n) {
                    var e = typeof Symbol == "function" && Symbol.iterator,
                        t = e && n[e],
                        r = 0;
                    if (t) return t.call(n);
                    if (n && typeof n.length == "number") return {
                        next: function() {
                            return n && r >= n.length && (n = void 0), {
                                value: n && n[r++],
                                done: !n
                            }
                        }
                    };
                    throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
                }

                function K(n, e) {
                    var t = typeof Symbol == "function" && n[Symbol.iterator];
                    if (!t) return n;
                    var r = t.call(n),
                        o, s = [],
                        y;
                    try {
                        for (;
                            (e === void 0 || e-- > 0) && !(o = r.next()).done;) s.push(o.value)
                    } catch (c) {
                        y = {
                            error: c
                        }
                    } finally {
                        try {
                            o && !o.done && (t = r.return) && t.call(r)
                        } finally {
                            if (y) throw y.error
                        }
                    }
                    return s
                }
                var _ = function(n, e) {
                        var t, r, o = ["string", "number", "boolean", "undefined"],
                            s = typeof n,
                            y = typeof e;
                        if (s !== y) return !1;
                        try {
                            for (var c = A(o), d = c.next(); !d.done; d = c.next()) {
                                var v = d.value;
                                if (v === s) return n === e
                            }
                        } catch (g) {
                            t = {
                                error: g
                            }
                        } finally {
                            try {
                                d && !d.done && (r = c.return) && r.call(c)
                            } finally {
                                if (t) throw t.error
                            }
                        }
                        if (n == null && e == null) return !0;
                        if (n == null || e == null || n.length !== e.length) return !1;
                        var m = Array.isArray(n),
                            f = Array.isArray(e);
                        if (m !== f) return !1;
                        if (m && f) {
                            for (var i = 0; i < n.length; i++)
                                if (!_(n[i], e[i])) return !1
                        } else {
                            var a = Object.keys(n).sort(),
                                u = Object.keys(e).sort();
                            if (!_(a, u)) return !1;
                            var l = !0;
                            return Object.keys(n).forEach(function(g) {
                                _(n[g], e[g]) || (l = !1)
                            }), l
                        }
                        return !0
                    },
                    N = "$set",
                    T = "$unset",
                    $ = "$clearAll";
                Object.entries || (Object.entries = function(n) {
                    for (var e = Object.keys(n), t = e.length, r = new Array(t); t--;) r[t] = [e[t], n[e[t]]];
                    return r
                });
                var M = function() {
                        function n() {
                            this.identity = {
                                userProperties: {}
                            }, this.listeners = new Set
                        }
                        return n.prototype.editIdentity = function() {
                            var e = this,
                                t = h({}, this.identity.userProperties),
                                r = h(h({}, this.identity), {
                                    userProperties: t
                                });
                            return {
                                setUserId: function(o) {
                                    return r.userId = o, this
                                },
                                setDeviceId: function(o) {
                                    return r.deviceId = o, this
                                },
                                setUserProperties: function(o) {
                                    return r.userProperties = o, this
                                },
                                setOptOut: function(o) {
                                    return r.optOut = o, this
                                },
                                updateUserProperties: function(o) {
                                    var s, y, c, d, v, m, f = r.userProperties || {};
                                    try {
                                        for (var i = A(Object.entries(o)), a = i.next(); !a.done; a = i.next()) {
                                            var u = K(a.value, 2),
                                                l = u[0],
                                                g = u[1];
                                            switch (l) {
                                                case N:
                                                    try {
                                                        for (var I = (c = void 0, A(Object.entries(g))), p = I.next(); !p.done; p = I.next()) {
                                                            var C = K(p.value, 2),
                                                                D = C[0],
                                                                L = C[1];
                                                            f[D] = L
                                                        }
                                                    } catch (S) {
                                                        c = {
                                                            error: S
                                                        }
                                                    } finally {
                                                        try {
                                                            p && !p.done && (d = I.return) && d.call(I)
                                                        } finally {
                                                            if (c) throw c.error
                                                        }
                                                    }
                                                    break;
                                                case T:
                                                    try {
                                                        for (var P = (v = void 0, A(Object.keys(g))), w = P.next(); !w.done; w = P.next()) {
                                                            var D = w.value;
                                                            delete f[D]
                                                        }
                                                    } catch (S) {
                                                        v = {
                                                            error: S
                                                        }
                                                    } finally {
                                                        try {
                                                            w && !w.done && (m = P.return) && m.call(P)
                                                        } finally {
                                                            if (v) throw v.error
                                                        }
                                                    }
                                                    break;
                                                case $:
                                                    f = {};
                                                    break
                                            }
                                        }
                                    } catch (S) {
                                        s = {
                                            error: S
                                        }
                                    } finally {
                                        try {
                                            a && !a.done && (y = i.return) && y.call(i)
                                        } finally {
                                            if (s) throw s.error
                                        }
                                    }
                                    return r.userProperties = f, this
                                },
                                commit: function() {
                                    return e.setIdentity(r), this
                                }
                            }
                        }, n.prototype.getIdentity = function() {
                            return h({}, this.identity)
                        }, n.prototype.setIdentity = function(e) {
                            var t = h({}, this.identity);
                            this.identity = h({}, e), _(t, this.identity) || this.listeners.forEach(function(r) {
                                r(e)
                            })
                        }, n.prototype.addIdentityListener = function(e) {
                            this.listeners.add(e)
                        }, n.prototype.removeIdentityListener = function(e) {
                            this.listeners.delete(e)
                        }, n
                    }(),
                    E = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : self,
                    O = function() {
                        function n() {
                            this.identityStore = new M, this.eventBridge = new B, this.applicationContextProvider = new U
                        }
                        return n.getInstance = function(e) {
                            return E.analyticsConnectorInstances || (E.analyticsConnectorInstances = {}), E.analyticsConnectorInstances[e] || (E.analyticsConnectorInstances[e] = new n), E.analyticsConnectorInstances[e]
                        }, n
                    }();
                var Z = ["boot"];
                var R = "$default_instance";

                function k(n) {
                    var o, s, y, c, d, v, m;
                    let e = window.engagement,
                        t = {
                            _q: (o = e == null ? void 0 : e._q) != null ? o : [],
                            _configuration: {
                                apiKey: (y = (s = e == null ? void 0 : e._configuration) == null ? void 0 : s.apiKey) != null ? y : "",
                                serverUrl: (c = e == null ? void 0 : e._configuration) == null ? void 0 : c.serverUrl,
                                serverZone: (v = (d = e == null ? void 0 : e._configuration) == null ? void 0 : d.serverZone) != null ? v : "US",
                                options: { ...(m = e == null ? void 0 : e._configuration) == null ? void 0 : m.options
                                }
                            },
                            _sentry: void 0,
                            init(f, i) {
                                var a, u, l;
                                if (t._configuration.apiKey) {
                                    console.log("Engagement SDK has already been initialized. Ignoring additional init call.");
                                    return
                                }
                                t._configuration = { ...t._configuration,
                                    ...i,
                                    apiKey: f,
                                    options: { ...t._configuration.options,
                                        ...i == null ? void 0 : i.options
                                    }
                                }, (u = (a = t._configuration) == null ? void 0 : a.options) != null && u.logger && t._configuration.options.logger.enable((l = t._configuration.options.logLevel) != null ? l : 2), n()
                            },
                            plugin(f) {
                                let i = t.init;
                                return {
                                    name: "@amplitude/engagement-browser",
                                    type: "enrichment",
                                    async setup(a, u) {
                                        var I;
                                        let l = (I = a.instanceName) != null ? I : R,
                                            g = O.getInstance(l).identityStore;
                                        i(a.apiKey, {
                                            options: {
                                                logLevel: a.logLevel
                                            },
                                            ...f,
                                            serverZone: a.serverZone
                                        }), await window.engagement.boot({
                                            user: () => {
                                                let p = g.getIdentity();
                                                return {
                                                    user_id: u.getUserId(),
                                                    device_id: u.getDeviceId(),
                                                    user_properties: p.userProperties,
                                                    getSessionId: u.getSessionId
                                                }
                                            },
                                            integrations: [{
                                                track: p => {
                                                    u.track(p)
                                                }
                                            }]
                                        }), g.addIdentityListener(p => {
                                            window.engagement._setUserProperties(p.userProperties)
                                        })
                                    },
                                    async execute(a) {
                                        return window.engagement.forwardEvent(a), a
                                    }
                                }
                            }
                        },
                        r = t;
                    return new Proxy(t, {
                        get: function(f, i) {
                            if (i in r) return r[i];
                            if (i !== "then") return i === "gs" ? new Proxy({}, {
                                get: function(a, u) {
                                    return function() {
                                        let l = Array.from(arguments),
                                            g = `${i}.${u}`;
                                        l.unshift(g), t._q.push(l)
                                    }
                                }
                            }) : Z.includes(i) ? function() {
                                let a = Array.prototype.slice.call(arguments);
                                return new Promise((u, l) => {
                                    a.unshift(i, u, l), t._q.push(a)
                                })
                            } : function() {
                                let a = Array.prototype.slice.call(arguments);
                                a.unshift(i), t._q.push(a)
                            }
                        }
                    })
                }
                var b = () => {
                    function n(e) {
                        for (let t of e) {
                            let r = document.createElement("script");
                            r.src = t, r.id = "engagement-sdk-bundle";
                            let o = document.querySelector("[nonce]");
                            o && r.setAttribute("nonce", o.nonce || o.getAttribute("nonce")), document.getElementsByTagName("head")[0].appendChild(r)
                        }
                    }
                    return k(() => n(["https://cdn.amplitude.com/engagement-browser/prod/index.min.js.gz"]))
                };
                window.engagement = b();
                var x = b();
                window.engagement = x;
                var {
                    init: J,
                    plugin: Q
                } = x;


                /***/
            })

    }
])
//# sourceMappingURL=node_modules_amplitude_engagement-browser_index_js.71cee78b0eef5a6c.js.map