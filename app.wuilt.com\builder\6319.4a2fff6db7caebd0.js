(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [6319], {
        6319: (n, t, r) => {
            r.r(t), r.d(t, {
                ArgumentOutOfRangeError: () => it,
                AsyncSubject: () => Q,
                BehaviorSubject: () => G,
                ConnectableObservable: () => M,
                EMPTY: () => En,
                EmptyError: () => tt,
                NEVER: () => Ht,
                NotFoundError: () => ut,
                Notification: () => Qn,
                NotificationKind: () => zn,
                ObjectUnsubscribedError: () => H,
                Observable: () => j,
                ReplaySubject: () => X,
                Scheduler: () => an,
                SequenceError: () => ot,
                Subject: () => K,
                Subscriber: () => w,
                Subscription: () => s,
                TimeoutError: () => st,
                UnsubscriptionError: () => o,
                VirtualAction: () => An,
                VirtualTimeScheduler: () => gn,
                animationFrame: () => xn,
                animationFrameScheduler: () => wn,
                animationFrames: () => U,
                asap: () => vn,
                asapScheduler: () => dn,
                async: () => pn,
                asyncScheduler: () => hn,
                audit: () => or,
                auditTime: () => cr,
                bindCallback: () => pt,
                bindNodeCallback: () => bt,
                buffer: () => sr,
                bufferCount: () => lr,
                bufferTime: () => ar,
                bufferToggle: () => fr,
                bufferWhen: () => dr,
                catchError: () => vr,
                combineAll: () => wr,
                combineLatest: () => At,
                combineLatestAll: () => _r,
                combineLatestWith: () => gr,
                concat: () => Ct,
                concatAll: () => kt,
                concatMap: () => Ar,
                concatMapTo: () => Er,
                concatWith: () => Tr,
                config: () => d,
                connect: () => Or,
                connectable: () => Nt,
                count: () => kr,
                debounce: () => Cr,
                debounceTime: () => Fr,
                defaultIfEmpty: () => jr,
                defer: () => Ft,
                delay: () => Wr,
                delayWhen: () => zr,
                dematerialize: () => Lr,
                distinct: () => Mr,
                distinctUntilChanged: () => Rr,
                distinctUntilKeyChanged: () => Ur,
                elementAt: () => Br,
                empty: () => Sn,
                endWith: () => Hr,
                every: () => Kr,
                exhaust: () => Jr,
                exhaustAll: () => Gr,
                exhaustMap: () => Zr,
                expand: () => Xr,
                filter: () => $t,
                finalize: () => Qr,
                find: () => $r,
                findIndex: () => te,
                first: () => re,
                firstValueFrom: () => et,
                flatMap: () => le,
                forkJoin: () => Pt,
                from: () => Gn,
                fromEvent: () => Lt,
                fromEventPattern: () => Rt,
                generate: () => Vt,
                groupBy: () => ee,
                identity: () => k,
                ignoreElements: () => Pr,
                iif: () => Ut,
                interval: () => Yt,
                isEmpty: () => ie,
                isObservable: () => nt,
                last: () => oe,
                lastValueFrom: () => rt,
                map: () => ft,
                mapTo: () => qr,
                materialize: () => ce,
                max: () => se,
                merge: () => Bt,
                mergeAll: () => Ot,
                mergeMap: () => It,
                mergeMapTo: () => ae,
                mergeScan: () => fe,
                mergeWith: () => ve,
                min: () => he,
                multicast: () => pe,
                never: () => Kt,
                noop: () => p,
                observable: () => O,
                observeOn: () => Yn,
                of: () => Jn,
                onErrorResumeNext: () => Jt,
                onErrorResumeNextWith: () => be,
                pairs: () => Xt,
                pairwise: () => ye,
                partition: () => nr,
                pipe: () => C,
                pluck: () => me,
                publish: () => _e,
                publishBehavior: () => we,
                publishLast: () => xe,
                publishReplay: () => ge,
                queue: () => mn,
                queueScheduler: () => yn,
                race: () => tr,
                raceWith: () => Ae,
                range: () => er,
                reduce: () => pr,
                refCount: () => L,
                repeat: () => Ee,
                repeatWhen: () => Se,
                retry: () => Te,
                retryWhen: () => Ie,
                sample: () => Oe,
                sampleTime: () => ke,
                scan: () => Ce,
                scheduled: () => Zn,
                sequenceEqual: () => Fe,
                share: () => je,
                shareReplay: () => Pe,
                single: () => qe,
                skip: () => ze,
                skipLast: () => We,
                skipUntil: () => Le,
                skipWhile: () => Me,
                startWith: () => Re,
                subscribeOn: () => Bn,
                switchAll: () => Ue,
                switchMap: () => Ve,
                switchMapTo: () => De,
                switchScan: () => Ye,
                take: () => Nr,
                takeLast: () => ue,
                takeUntil: () => Be,
                takeWhile: () => He,
                tap: () => Ke,
                throttle: () => Ze,
                throttleTime: () => Ge,
                throwError: () => Xn,
                throwIfEmpty: () => Dr,
                timeInterval: () => Je,
                timeout: () => lt,
                timeoutWith: () => Qe,
                timer: () => Dt,
                timestamp: () => $e,
                toArray: () => yr,
                using: () => ir,
                window: () => ni,
                windowCount: () => ti,
                windowTime: () => ri,
                windowToggle: () => ei,
                windowWhen: () => ii,
                withLatestFrom: () => ui,
                zip: () => ur,
                zipAll: () => oi,
                zipWith: () => si
            });
            var e = r(22970);

            function i(n) {
                return "function" == typeof n
            }
            var u = r(56176),
                o = (0, u.d)((function(n) {
                    return function(t) {
                        n(this), this.message = t ? t.length + " errors occurred during unsubscription:\n" + t.map((function(n, t) {
                            return t + 1 + ") " + n.toString()
                        })).join("\n  ") : "", this.name = "UnsubscriptionError", this.errors = t
                    }
                }));

            function c(n, t) {
                if (n) {
                    var r = n.indexOf(t);
                    0 <= r && n.splice(r, 1)
                }
            }
            var s = function() {
                    function n(n) {
                        this.initialTeardown = n, this.closed = !1, this._parentage = null, this._finalizers = null
                    }
                    return n.prototype.unsubscribe = function() {
                        var n, t, r, u, c;
                        if (!this.closed) {
                            this.closed = !0;
                            var s = this._parentage;
                            if (s)
                                if (this._parentage = null, Array.isArray(s)) try {
                                    for (var l = (0, e.__values)(s), a = l.next(); !a.done; a = l.next()) a.value.remove(this)
                                } catch (t) {
                                    n = {
                                        error: t
                                    }
                                } finally {
                                    try {
                                        a && !a.done && (t = l.return) && t.call(l)
                                    } finally {
                                        if (n) throw n.error
                                    }
                                } else s.remove(this);
                            var d = this.initialTeardown;
                            if (i(d)) try {
                                d()
                            } catch (n) {
                                c = n instanceof o ? n.errors : [n]
                            }
                            var v = this._finalizers;
                            if (v) {
                                this._finalizers = null;
                                try {
                                    for (var h = (0, e.__values)(v), p = h.next(); !p.done; p = h.next()) {
                                        var b = p.value;
                                        try {
                                            f(b)
                                        } catch (n) {
                                            c = null != c ? c : [], n instanceof o ? c = (0, e.__spreadArray)((0, e.__spreadArray)([], (0, e.__read)(c)), (0, e.__read)(n.errors)) : c.push(n)
                                        }
                                    }
                                } catch (n) {
                                    r = {
                                        error: n
                                    }
                                } finally {
                                    try {
                                        p && !p.done && (u = h.return) && u.call(h)
                                    } finally {
                                        if (r) throw r.error
                                    }
                                }
                            }
                            if (c) throw new o(c)
                        }
                    }, n.prototype.add = function(t) {
                        var r;
                        if (t && t !== this)
                            if (this.closed) f(t);
                            else {
                                if (t instanceof n) {
                                    if (t.closed || t._hasParent(this)) return;
                                    t._addParent(this)
                                }(this._finalizers = null !== (r = this._finalizers) && void 0 !== r ? r : []).push(t)
                            }
                    }, n.prototype._hasParent = function(n) {
                        var t = this._parentage;
                        return t === n || Array.isArray(t) && t.includes(n)
                    }, n.prototype._addParent = function(n) {
                        var t = this._parentage;
                        this._parentage = Array.isArray(t) ? (t.push(n), t) : t ? [t, n] : n
                    }, n.prototype._removeParent = function(n) {
                        var t = this._parentage;
                        t === n ? this._parentage = null : Array.isArray(t) && c(t, n)
                    }, n.prototype.remove = function(t) {
                        var r = this._finalizers;
                        r && c(r, t), t instanceof n && t._removeParent(this)
                    }, n.EMPTY = function() {
                        var t = new n;
                        return t.closed = !0, t
                    }(), n
                }(),
                l = s.EMPTY;

            function a(n) {
                return n instanceof s || n && "closed" in n && i(n.remove) && i(n.add) && i(n.unsubscribe)
            }

            function f(n) {
                i(n) ? n() : n.unsubscribe()
            }
            var d = {
                    onUnhandledError: null,
                    onStoppedNotification: null,
                    Promise: void 0,
                    useDeprecatedSynchronousErrorHandling: !1,
                    useDeprecatedNextContext: !1
                },
                v = {
                    setTimeout: function(n, t) {
                        for (var r = [], i = 2; i < arguments.length; i++) r[i - 2] = arguments[i];
                        var u = v.delegate;
                        return (null == u ? void 0 : u.setTimeout) ? u.setTimeout.apply(u, (0, e.__spreadArray)([n, t], (0, e.__read)(r))) : setTimeout.apply(void 0, (0, e.__spreadArray)([n, t], (0, e.__read)(r)))
                    },
                    clearTimeout: function(n) {
                        var t = v.delegate;
                        return ((null == t ? void 0 : t.clearTimeout) || clearTimeout)(n)
                    },
                    delegate: void 0
                };

            function h(n) {
                v.setTimeout((function() {
                    var t = d.onUnhandledError;
                    if (!t) throw n;
                    t(n)
                }))
            }

            function p() {}
            var b = y("C", void 0, void 0);

            function y(n, t, r) {
                return {
                    kind: n,
                    value: t,
                    error: r
                }
            }
            var m = null;

            function _(n) {
                if (d.useDeprecatedSynchronousErrorHandling) {
                    var t = !m;
                    if (t && (m = {
                            errorThrown: !1,
                            error: null
                        }), n(), t) {
                        var r = m,
                            e = r.errorThrown,
                            i = r.error;
                        if (m = null, e) throw i
                    }
                } else n()
            }
            var w = function(n) {
                    function t(t) {
                        var r = n.call(this) || this;
                        return r.isStopped = !1, t ? (r.destination = t, a(t) && t.add(r)) : r.destination = I, r
                    }
                    return (0, e.__extends)(t, n), t.create = function(n, t, r) {
                        return new E(n, t, r)
                    }, t.prototype.next = function(n) {
                        this.isStopped ? T(function(n) {
                            return y("N", n, void 0)
                        }(n), this) : this._next(n)
                    }, t.prototype.error = function(n) {
                        this.isStopped ? T(y("E", void 0, n), this) : (this.isStopped = !0, this._error(n))
                    }, t.prototype.complete = function() {
                        this.isStopped ? T(b, this) : (this.isStopped = !0, this._complete())
                    }, t.prototype.unsubscribe = function() {
                        this.closed || (this.isStopped = !0, n.prototype.unsubscribe.call(this), this.destination = null)
                    }, t.prototype._next = function(n) {
                        this.destination.next(n)
                    }, t.prototype._error = function(n) {
                        try {
                            this.destination.error(n)
                        } finally {
                            this.unsubscribe()
                        }
                    }, t.prototype._complete = function() {
                        try {
                            this.destination.complete()
                        } finally {
                            this.unsubscribe()
                        }
                    }, t
                }(s),
                x = Function.prototype.bind;

            function g(n, t) {
                return x.call(n, t)
            }
            var A = function() {
                    function n(n) {
                        this.partialObserver = n
                    }
                    return n.prototype.next = function(n) {
                        var t = this.partialObserver;
                        if (t.next) try {
                            t.next(n)
                        } catch (n) {
                            S(n)
                        }
                    }, n.prototype.error = function(n) {
                        var t = this.partialObserver;
                        if (t.error) try {
                            t.error(n)
                        } catch (n) {
                            S(n)
                        } else S(n)
                    }, n.prototype.complete = function() {
                        var n = this.partialObserver;
                        if (n.complete) try {
                            n.complete()
                        } catch (n) {
                            S(n)
                        }
                    }, n
                }(),
                E = function(n) {
                    function t(t, r, e) {
                        var u, o, c = n.call(this) || this;
                        return i(t) || !t ? u = {
                            next: null != t ? t : void 0,
                            error: null != r ? r : void 0,
                            complete: null != e ? e : void 0
                        } : c && d.useDeprecatedNextContext ? ((o = Object.create(t)).unsubscribe = function() {
                            return c.unsubscribe()
                        }, u = {
                            next: t.next && g(t.next, o),
                            error: t.error && g(t.error, o),
                            complete: t.complete && g(t.complete, o)
                        }) : u = t, c.destination = new A(u), c
                    }
                    return (0, e.__extends)(t, n), t
                }(w);

            function S(n) {
                var t;
                d.useDeprecatedSynchronousErrorHandling ? (t = n, d.useDeprecatedSynchronousErrorHandling && m && (m.errorThrown = !0, m.error = t)) : h(n)
            }

            function T(n, t) {
                var r = d.onStoppedNotification;
                r && v.setTimeout((function() {
                    return r(n, t)
                }))
            }
            var I = {
                    closed: !0,
                    next: p,
                    error: function(n) {
                        throw n
                    },
                    complete: p
                },
                O = "function" == typeof Symbol && Symbol.observable || "@@observable";

            function k(n) {
                return n
            }

            function C() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return F(n)
            }

            function F(n) {
                return 0 === n.length ? k : 1 === n.length ? n[0] : function(t) {
                    return n.reduce((function(n, t) {
                        return t(n)
                    }), t)
                }
            }
            var j = function() {
                function n(n) {
                    n && (this._subscribe = n)
                }
                return n.prototype.lift = function(t) {
                    var r = new n;
                    return r.source = this, r.operator = t, r
                }, n.prototype.subscribe = function(n, t, r) {
                    var e, u = this,
                        o = (e = n) && e instanceof w || function(n) {
                            return n && i(n.next) && i(n.error) && i(n.complete)
                        }(e) && a(e) ? n : new E(n, t, r);
                    return _((function() {
                        var n = u,
                            t = n.operator,
                            r = n.source;
                        o.add(t ? t.call(o, r) : r ? u._subscribe(o) : u._trySubscribe(o))
                    })), o
                }, n.prototype._trySubscribe = function(n) {
                    try {
                        return this._subscribe(n)
                    } catch (t) {
                        n.error(t)
                    }
                }, n.prototype.forEach = function(n, t) {
                    var r = this;
                    return new(t = N(t))((function(t, e) {
                        var i = new E({
                            next: function(t) {
                                try {
                                    n(t)
                                } catch (n) {
                                    e(n), i.unsubscribe()
                                }
                            },
                            error: e,
                            complete: t
                        });
                        r.subscribe(i)
                    }))
                }, n.prototype._subscribe = function(n) {
                    var t;
                    return null === (t = this.source) || void 0 === t ? void 0 : t.subscribe(n)
                }, n.prototype[O] = function() {
                    return this
                }, n.prototype.pipe = function() {
                    for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                    return F(n)(this)
                }, n.prototype.toPromise = function(n) {
                    var t = this;
                    return new(n = N(n))((function(n, r) {
                        var e;
                        t.subscribe((function(n) {
                            return e = n
                        }), (function(n) {
                            return r(n)
                        }), (function() {
                            return n(e)
                        }))
                    }))
                }, n.create = function(t) {
                    return new n(t)
                }, n
            }();

            function N(n) {
                var t;
                return null !== (t = null != n ? n : d.Promise) && void 0 !== t ? t : Promise
            }

            function P(n) {
                return i(null == n ? void 0 : n.lift)
            }

            function q(n) {
                return function(t) {
                    if (P(t)) return t.lift((function(t) {
                        try {
                            return n(t, this)
                        } catch (n) {
                            this.error(n)
                        }
                    }));
                    throw new TypeError("Unable to lift unknown Observable type")
                }
            }

            function z(n, t, r, e, i) {
                return new W(n, t, r, e, i)
            }
            var W = function(n) {
                function t(t, r, e, i, u, o) {
                    var c = n.call(this, t) || this;
                    return c.onFinalize = u, c.shouldUnsubscribe = o, c._next = r ? function(n) {
                        try {
                            r(n)
                        } catch (n) {
                            t.error(n)
                        }
                    } : n.prototype._next, c._error = i ? function(n) {
                        try {
                            i(n)
                        } catch (n) {
                            t.error(n)
                        } finally {
                            this.unsubscribe()
                        }
                    } : n.prototype._error, c._complete = e ? function() {
                        try {
                            e()
                        } catch (n) {
                            t.error(n)
                        } finally {
                            this.unsubscribe()
                        }
                    } : n.prototype._complete, c
                }
                return (0, e.__extends)(t, n), t.prototype.unsubscribe = function() {
                    var t;
                    if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {
                        var r = this.closed;
                        n.prototype.unsubscribe.call(this), !r && (null === (t = this.onFinalize) || void 0 === t || t.call(this))
                    }
                }, t
            }(w);

            function L() {
                return q((function(n, t) {
                    var r = null;
                    n._refCount++;
                    var e = z(t, void 0, void 0, void 0, (function() {
                        if (!n || n._refCount <= 0 || 0 < --n._refCount) r = null;
                        else {
                            var e = n._connection,
                                i = r;
                            r = null, !e || i && e !== i || e.unsubscribe(), t.unsubscribe()
                        }
                    }));
                    n.subscribe(e), e.closed || (r = n.connect())
                }))
            }
            var M = function(n) {
                    function t(t, r) {
                        var e = n.call(this) || this;
                        return e.source = t, e.subjectFactory = r, e._subject = null, e._refCount = 0, e._connection = null, P(t) && (e.lift = t.lift), e
                    }
                    return (0, e.__extends)(t, n), t.prototype._subscribe = function(n) {
                        return this.getSubject().subscribe(n)
                    }, t.prototype.getSubject = function() {
                        var n = this._subject;
                        return n && !n.isStopped || (this._subject = this.subjectFactory()), this._subject
                    }, t.prototype._teardown = function() {
                        this._refCount = 0;
                        var n = this._connection;
                        this._subject = this._connection = null, null == n || n.unsubscribe()
                    }, t.prototype.connect = function() {
                        var n = this,
                            t = this._connection;
                        if (!t) {
                            t = this._connection = new s;
                            var r = this.getSubject();
                            t.add(this.source.subscribe(z(r, void 0, (function() {
                                n._teardown(), r.complete()
                            }), (function(t) {
                                n._teardown(), r.error(t)
                            }), (function() {
                                return n._teardown()
                            })))), t.closed && (this._connection = null, t = s.EMPTY)
                        }
                        return t
                    }, t.prototype.refCount = function() {
                        return L()(this)
                    }, t
                }(j),
                R = {
                    now: function() {
                        return (R.delegate || performance).now()
                    },
                    delegate: void 0
                },
                V = {
                    schedule: function(n) {
                        var t = requestAnimationFrame,
                            r = cancelAnimationFrame,
                            e = V.delegate;
                        e && (t = e.requestAnimationFrame, r = e.cancelAnimationFrame);
                        var i = t((function(t) {
                            r = void 0, n(t)
                        }));
                        return new s((function() {
                            return null == r ? void 0 : r(i)
                        }))
                    },
                    requestAnimationFrame: function() {
                        for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                        var r = V.delegate;
                        return ((null == r ? void 0 : r.requestAnimationFrame) || requestAnimationFrame).apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n)))
                    },
                    cancelAnimationFrame: function() {
                        for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                        var r = V.delegate;
                        return ((null == r ? void 0 : r.cancelAnimationFrame) || cancelAnimationFrame).apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n)))
                    },
                    delegate: void 0
                };

            function U(n) {
                return n ? D(n) : B
            }

            function D(n) {
                return new j((function(t) {
                    var r = n || R,
                        e = r.now(),
                        i = 0,
                        u = function() {
                            t.closed || (i = V.requestAnimationFrame((function(o) {
                                i = 0;
                                var c = r.now();
                                t.next({
                                    timestamp: n ? c : o,
                                    elapsed: c - e
                                }), u()
                            })))
                        };
                    return u(),
                        function() {
                            i && V.cancelAnimationFrame(i)
                        }
                }))
            }
            var Y, B = D(),
                H = (0, u.d)((function(n) {
                    return function() {
                        n(this), this.name = "ObjectUnsubscribedError", this.message = "object unsubscribed"
                    }
                })),
                K = function(n) {
                    function t() {
                        var t = n.call(this) || this;
                        return t.closed = !1, t.currentObservers = null, t.observers = [], t.isStopped = !1, t.hasError = !1, t.thrownError = null, t
                    }
                    return (0, e.__extends)(t, n), t.prototype.lift = function(n) {
                        var t = new Z(this, this);
                        return t.operator = n, t
                    }, t.prototype._throwIfClosed = function() {
                        if (this.closed) throw new H
                    }, t.prototype.next = function(n) {
                        var t = this;
                        _((function() {
                            var r, i;
                            if (t._throwIfClosed(), !t.isStopped) {
                                t.currentObservers || (t.currentObservers = Array.from(t.observers));
                                try {
                                    for (var u = (0, e.__values)(t.currentObservers), o = u.next(); !o.done; o = u.next()) o.value.next(n)
                                } catch (n) {
                                    r = {
                                        error: n
                                    }
                                } finally {
                                    try {
                                        o && !o.done && (i = u.return) && i.call(u)
                                    } finally {
                                        if (r) throw r.error
                                    }
                                }
                            }
                        }))
                    }, t.prototype.error = function(n) {
                        var t = this;
                        _((function() {
                            if (t._throwIfClosed(), !t.isStopped) {
                                t.hasError = t.isStopped = !0, t.thrownError = n;
                                for (var r = t.observers; r.length;) r.shift().error(n)
                            }
                        }))
                    }, t.prototype.complete = function() {
                        var n = this;
                        _((function() {
                            if (n._throwIfClosed(), !n.isStopped) {
                                n.isStopped = !0;
                                for (var t = n.observers; t.length;) t.shift().complete()
                            }
                        }))
                    }, t.prototype.unsubscribe = function() {
                        this.isStopped = this.closed = !0, this.observers = this.currentObservers = null
                    }, Object.defineProperty(t.prototype, "observed", {
                        get: function() {
                            var n;
                            return (null === (n = this.observers) || void 0 === n ? void 0 : n.length) > 0
                        },
                        enumerable: !1,
                        configurable: !0
                    }), t.prototype._trySubscribe = function(t) {
                        return this._throwIfClosed(), n.prototype._trySubscribe.call(this, t)
                    }, t.prototype._subscribe = function(n) {
                        return this._throwIfClosed(), this._checkFinalizedStatuses(n), this._innerSubscribe(n)
                    }, t.prototype._innerSubscribe = function(n) {
                        var t = this,
                            r = this,
                            e = r.hasError,
                            i = r.isStopped,
                            u = r.observers;
                        return e || i ? l : (this.currentObservers = null, u.push(n), new s((function() {
                            t.currentObservers = null, c(u, n)
                        })))
                    }, t.prototype._checkFinalizedStatuses = function(n) {
                        var t = this,
                            r = t.hasError,
                            e = t.thrownError,
                            i = t.isStopped;
                        r ? n.error(e) : i && n.complete()
                    }, t.prototype.asObservable = function() {
                        var n = new j;
                        return n.source = this, n
                    }, t.create = function(n, t) {
                        return new Z(n, t)
                    }, t
                }(j),
                Z = function(n) {
                    function t(t, r) {
                        var e = n.call(this) || this;
                        return e.destination = t, e.source = r, e
                    }
                    return (0, e.__extends)(t, n), t.prototype.next = function(n) {
                        var t, r;
                        null === (r = null === (t = this.destination) || void 0 === t ? void 0 : t.next) || void 0 === r || r.call(t, n)
                    }, t.prototype.error = function(n) {
                        var t, r;
                        null === (r = null === (t = this.destination) || void 0 === t ? void 0 : t.error) || void 0 === r || r.call(t, n)
                    }, t.prototype.complete = function() {
                        var n, t;
                        null === (t = null === (n = this.destination) || void 0 === n ? void 0 : n.complete) || void 0 === t || t.call(n)
                    }, t.prototype._subscribe = function(n) {
                        var t, r;
                        return null !== (r = null === (t = this.source) || void 0 === t ? void 0 : t.subscribe(n)) && void 0 !== r ? r : l
                    }, t
                }(K),
                G = function(n) {
                    function t(t) {
                        var r = n.call(this) || this;
                        return r._value = t, r
                    }
                    return (0, e.__extends)(t, n), Object.defineProperty(t.prototype, "value", {
                        get: function() {
                            return this.getValue()
                        },
                        enumerable: !1,
                        configurable: !0
                    }), t.prototype._subscribe = function(t) {
                        var r = n.prototype._subscribe.call(this, t);
                        return !r.closed && t.next(this._value), r
                    }, t.prototype.getValue = function() {
                        var n = this,
                            t = n.hasError,
                            r = n.thrownError,
                            e = n._value;
                        if (t) throw r;
                        return this._throwIfClosed(), e
                    }, t.prototype.next = function(t) {
                        n.prototype.next.call(this, this._value = t)
                    }, t
                }(K),
                J = {
                    now: function() {
                        return (J.delegate || Date).now()
                    },
                    delegate: void 0
                },
                X = function(n) {
                    function t(t, r, e) {
                        void 0 === t && (t = 1 / 0), void 0 === r && (r = 1 / 0), void 0 === e && (e = J);
                        var i = n.call(this) || this;
                        return i._bufferSize = t, i._windowTime = r, i._timestampProvider = e, i._buffer = [], i._infiniteTimeWindow = !0, i._infiniteTimeWindow = r === 1 / 0, i._bufferSize = Math.max(1, t), i._windowTime = Math.max(1, r), i
                    }
                    return (0, e.__extends)(t, n), t.prototype.next = function(t) {
                        var r = this,
                            e = r.isStopped,
                            i = r._buffer,
                            u = r._infiniteTimeWindow,
                            o = r._timestampProvider,
                            c = r._windowTime;
                        e || (i.push(t), !u && i.push(o.now() + c)), this._trimBuffer(), n.prototype.next.call(this, t)
                    }, t.prototype._subscribe = function(n) {
                        this._throwIfClosed(), this._trimBuffer();
                        for (var t = this._innerSubscribe(n), r = this._infiniteTimeWindow, e = this._buffer.slice(), i = 0; i < e.length && !n.closed; i += r ? 1 : 2) n.next(e[i]);
                        return this._checkFinalizedStatuses(n), t
                    }, t.prototype._trimBuffer = function() {
                        var n = this,
                            t = n._bufferSize,
                            r = n._timestampProvider,
                            e = n._buffer,
                            i = n._infiniteTimeWindow,
                            u = (i ? 1 : 2) * t;
                        if (t < 1 / 0 && u < e.length && e.splice(0, e.length - u), !i) {
                            for (var o = r.now(), c = 0, s = 1; s < e.length && e[s] <= o; s += 2) c = s;
                            c && e.splice(0, c + 1)
                        }
                    }, t
                }(K),
                Q = function(n) {
                    function t() {
                        var t = null !== n && n.apply(this, arguments) || this;
                        return t._value = null, t._hasValue = !1, t._isComplete = !1, t
                    }
                    return (0, e.__extends)(t, n), t.prototype._checkFinalizedStatuses = function(n) {
                        var t = this,
                            r = t.hasError,
                            e = t._hasValue,
                            i = t._value,
                            u = t.thrownError,
                            o = t.isStopped,
                            c = t._isComplete;
                        r ? n.error(u) : (o || c) && (e && n.next(i), n.complete())
                    }, t.prototype.next = function(n) {
                        this.isStopped || (this._value = n, this._hasValue = !0)
                    }, t.prototype.complete = function() {
                        var t = this,
                            r = t._hasValue,
                            e = t._value;
                        t._isComplete || (this._isComplete = !0, r && n.prototype.next.call(this, e), n.prototype.complete.call(this))
                    }, t
                }(K),
                $ = function(n) {
                    function t(t, r) {
                        return n.call(this) || this
                    }
                    return (0, e.__extends)(t, n), t.prototype.schedule = function(n, t) {
                        return void 0 === t && (t = 0), this
                    }, t
                }(s),
                nn = {
                    setInterval: function(n, t) {
                        for (var r = [], i = 2; i < arguments.length; i++) r[i - 2] = arguments[i];
                        var u = nn.delegate;
                        return (null == u ? void 0 : u.setInterval) ? u.setInterval.apply(u, (0, e.__spreadArray)([n, t], (0, e.__read)(r))) : setInterval.apply(void 0, (0, e.__spreadArray)([n, t], (0, e.__read)(r)))
                    },
                    clearInterval: function(n) {
                        var t = nn.delegate;
                        return ((null == t ? void 0 : t.clearInterval) || clearInterval)(n)
                    },
                    delegate: void 0
                },
                tn = function(n) {
                    function t(t, r) {
                        var e = n.call(this, t, r) || this;
                        return e.scheduler = t, e.work = r, e.pending = !1, e
                    }
                    return (0, e.__extends)(t, n), t.prototype.schedule = function(n, t) {
                        var r;
                        if (void 0 === t && (t = 0), this.closed) return this;
                        this.state = n;
                        var e = this.id,
                            i = this.scheduler;
                        return null != e && (this.id = this.recycleAsyncId(i, e, t)), this.pending = !0, this.delay = t, this.id = null !== (r = this.id) && void 0 !== r ? r : this.requestAsyncId(i, this.id, t), this
                    }, t.prototype.requestAsyncId = function(n, t, r) {
                        return void 0 === r && (r = 0), nn.setInterval(n.flush.bind(n, this), r)
                    }, t.prototype.recycleAsyncId = function(n, t, r) {
                        if (void 0 === r && (r = 0), null != r && this.delay === r && !1 === this.pending) return t;
                        null != t && nn.clearInterval(t)
                    }, t.prototype.execute = function(n, t) {
                        if (this.closed) return new Error("executing a cancelled action");
                        this.pending = !1;
                        var r = this._execute(n, t);
                        if (r) return r;
                        !1 === this.pending && null != this.id && (this.id = this.recycleAsyncId(this.scheduler, this.id, null))
                    }, t.prototype._execute = function(n, t) {
                        var r, e = !1;
                        try {
                            this.work(n)
                        } catch (n) {
                            e = !0, r = n || new Error("Scheduled action threw falsy error")
                        }
                        if (e) return this.unsubscribe(), r
                    }, t.prototype.unsubscribe = function() {
                        if (!this.closed) {
                            var t = this.id,
                                r = this.scheduler,
                                e = r.actions;
                            this.work = this.state = this.scheduler = null, this.pending = !1, c(e, this), null != t && (this.id = this.recycleAsyncId(r, t, null)), this.delay = null, n.prototype.unsubscribe.call(this)
                        }
                    }, t
                }($),
                rn = 1,
                en = {};

            function un(n) {
                return n in en && (delete en[n], !0)
            }
            var on = function(n) {
                    var t = rn++;
                    return en[t] = !0, Y || (Y = Promise.resolve()), Y.then((function() {
                        return un(t) && n()
                    })), t
                },
                cn = function(n) {
                    un(n)
                },
                sn = {
                    setImmediate: function() {
                        for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                        var r = sn.delegate;
                        return ((null == r ? void 0 : r.setImmediate) || on).apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n)))
                    },
                    clearImmediate: function(n) {
                        var t = sn.delegate;
                        return ((null == t ? void 0 : t.clearImmediate) || cn)(n)
                    },
                    delegate: void 0
                },
                ln = function(n) {
                    function t(t, r) {
                        var e = n.call(this, t, r) || this;
                        return e.scheduler = t, e.work = r, e
                    }
                    return (0, e.__extends)(t, n), t.prototype.requestAsyncId = function(t, r, e) {
                        return void 0 === e && (e = 0), null !== e && e > 0 ? n.prototype.requestAsyncId.call(this, t, r, e) : (t.actions.push(this), t._scheduled || (t._scheduled = sn.setImmediate(t.flush.bind(t, void 0))))
                    }, t.prototype.recycleAsyncId = function(t, r, e) {
                        var i;
                        if (void 0 === e && (e = 0), null != e ? e > 0 : this.delay > 0) return n.prototype.recycleAsyncId.call(this, t, r, e);
                        var u = t.actions;
                        null != r && (null === (i = u[u.length - 1]) || void 0 === i ? void 0 : i.id) !== r && (sn.clearImmediate(r), t._scheduled === r && (t._scheduled = void 0))
                    }, t
                }(tn),
                an = function() {
                    function n(t, r) {
                        void 0 === r && (r = n.now), this.schedulerActionCtor = t, this.now = r
                    }
                    return n.prototype.schedule = function(n, t, r) {
                        return void 0 === t && (t = 0), new this.schedulerActionCtor(this, n).schedule(r, t)
                    }, n.now = J.now, n
                }(),
                fn = function(n) {
                    function t(t, r) {
                        void 0 === r && (r = an.now);
                        var e = n.call(this, t, r) || this;
                        return e.actions = [], e._active = !1, e
                    }
                    return (0, e.__extends)(t, n), t.prototype.flush = function(n) {
                        var t = this.actions;
                        if (this._active) t.push(n);
                        else {
                            var r;
                            this._active = !0;
                            do {
                                if (r = n.execute(n.state, n.delay)) break
                            } while (n = t.shift());
                            if (this._active = !1, r) {
                                for (; n = t.shift();) n.unsubscribe();
                                throw r
                            }
                        }
                    }, t
                }(an),
                dn = new(function(n) {
                    function t() {
                        return null !== n && n.apply(this, arguments) || this
                    }
                    return (0, e.__extends)(t, n), t.prototype.flush = function(n) {
                        this._active = !0;
                        var t = this._scheduled;
                        this._scheduled = void 0;
                        var r, e = this.actions;
                        n = n || e.shift();
                        do {
                            if (r = n.execute(n.state, n.delay)) break
                        } while ((n = e[0]) && n.id === t && e.shift());
                        if (this._active = !1, r) {
                            for (;
                                (n = e[0]) && n.id === t && e.shift();) n.unsubscribe();
                            throw r
                        }
                    }, t
                }(fn))(ln),
                vn = dn,
                hn = new fn(tn),
                pn = hn,
                bn = function(n) {
                    function t(t, r) {
                        var e = n.call(this, t, r) || this;
                        return e.scheduler = t, e.work = r, e
                    }
                    return (0, e.__extends)(t, n), t.prototype.schedule = function(t, r) {
                        return void 0 === r && (r = 0), r > 0 ? n.prototype.schedule.call(this, t, r) : (this.delay = r, this.state = t, this.scheduler.flush(this), this)
                    }, t.prototype.execute = function(t, r) {
                        return r > 0 || this.closed ? n.prototype.execute.call(this, t, r) : this._execute(t, r)
                    }, t.prototype.requestAsyncId = function(t, r, e) {
                        return void 0 === e && (e = 0), null != e && e > 0 || null == e && this.delay > 0 ? n.prototype.requestAsyncId.call(this, t, r, e) : (t.flush(this), 0)
                    }, t
                }(tn),
                yn = new(function(n) {
                    function t() {
                        return null !== n && n.apply(this, arguments) || this
                    }
                    return (0, e.__extends)(t, n), t
                }(fn))(bn),
                mn = yn,
                _n = function(n) {
                    function t(t, r) {
                        var e = n.call(this, t, r) || this;
                        return e.scheduler = t, e.work = r, e
                    }
                    return (0, e.__extends)(t, n), t.prototype.requestAsyncId = function(t, r, e) {
                        return void 0 === e && (e = 0), null !== e && e > 0 ? n.prototype.requestAsyncId.call(this, t, r, e) : (t.actions.push(this), t._scheduled || (t._scheduled = V.requestAnimationFrame((function() {
                            return t.flush(void 0)
                        }))))
                    }, t.prototype.recycleAsyncId = function(t, r, e) {
                        var i;
                        if (void 0 === e && (e = 0), null != e ? e > 0 : this.delay > 0) return n.prototype.recycleAsyncId.call(this, t, r, e);
                        var u = t.actions;
                        null != r && (null === (i = u[u.length - 1]) || void 0 === i ? void 0 : i.id) !== r && (V.cancelAnimationFrame(r), t._scheduled = void 0)
                    }, t
                }(tn),
                wn = new(function(n) {
                    function t() {
                        return null !== n && n.apply(this, arguments) || this
                    }
                    return (0, e.__extends)(t, n), t.prototype.flush = function(n) {
                        this._active = !0;
                        var t = this._scheduled;
                        this._scheduled = void 0;
                        var r, e = this.actions;
                        n = n || e.shift();
                        do {
                            if (r = n.execute(n.state, n.delay)) break
                        } while ((n = e[0]) && n.id === t && e.shift());
                        if (this._active = !1, r) {
                            for (;
                                (n = e[0]) && n.id === t && e.shift();) n.unsubscribe();
                            throw r
                        }
                    }, t
                }(fn))(_n),
                xn = wn,
                gn = function(n) {
                    function t(t, r) {
                        void 0 === t && (t = An), void 0 === r && (r = 1 / 0);
                        var e = n.call(this, t, (function() {
                            return e.frame
                        })) || this;
                        return e.maxFrames = r, e.frame = 0, e.index = -1, e
                    }
                    return (0, e.__extends)(t, n), t.prototype.flush = function() {
                        for (var n, t, r = this.actions, e = this.maxFrames;
                            (t = r[0]) && t.delay <= e && (r.shift(), this.frame = t.delay, !(n = t.execute(t.state, t.delay))););
                        if (n) {
                            for (; t = r.shift();) t.unsubscribe();
                            throw n
                        }
                    }, t.frameTimeFactor = 10, t
                }(fn),
                An = function(n) {
                    function t(t, r, e) {
                        void 0 === e && (e = t.index += 1);
                        var i = n.call(this, t, r) || this;
                        return i.scheduler = t, i.work = r, i.index = e, i.active = !0, i.index = t.index = e, i
                    }
                    return (0, e.__extends)(t, n), t.prototype.schedule = function(r, e) {
                        if (void 0 === e && (e = 0), Number.isFinite(e)) {
                            if (!this.id) return n.prototype.schedule.call(this, r, e);
                            this.active = !1;
                            var i = new t(this.scheduler, this.work);
                            return this.add(i), i.schedule(r, e)
                        }
                        return s.EMPTY
                    }, t.prototype.requestAsyncId = function(n, r, e) {
                        void 0 === e && (e = 0), this.delay = n.frame + e;
                        var i = n.actions;
                        return i.push(this), i.sort(t.sortActions), 1
                    }, t.prototype.recycleAsyncId = function(n, t, r) {
                        void 0 === r && (r = 0)
                    }, t.prototype._execute = function(t, r) {
                        if (!0 === this.active) return n.prototype._execute.call(this, t, r)
                    }, t.sortActions = function(n, t) {
                        return n.delay === t.delay ? n.index === t.index ? 0 : n.index > t.index ? 1 : -1 : n.delay > t.delay ? 1 : -1
                    }, t
                }(tn),
                En = new j((function(n) {
                    return n.complete()
                }));

            function Sn(n) {
                return n ? function(n) {
                    return new j((function(t) {
                        return n.schedule((function() {
                            return t.complete()
                        }))
                    }))
                }(n) : En
            }

            function Tn(n) {
                return n && i(n.schedule)
            }

            function In(n) {
                return n[n.length - 1]
            }

            function On(n) {
                return i(In(n)) ? n.pop() : void 0
            }

            function kn(n) {
                return Tn(In(n)) ? n.pop() : void 0
            }

            function Cn(n, t) {
                return "number" == typeof In(n) ? n.pop() : t
            }
            var Fn = function(n) {
                return n && "number" == typeof n.length && "function" != typeof n
            };

            function jn(n) {
                return i(null == n ? void 0 : n.then)
            }

            function Nn(n) {
                return i(n[O])
            }

            function Pn(n) {
                return Symbol.asyncIterator && i(null == n ? void 0 : n[Symbol.asyncIterator])
            }

            function qn(n) {
                return new TypeError("You provided " + (null !== n && "object" == typeof n ? "an invalid object" : "'" + n + "'") + " where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")
            }
            var zn, Wn = "function" == typeof Symbol && Symbol.iterator ? Symbol.iterator : "@@iterator";

            function Ln(n) {
                return i(null == n ? void 0 : n[Wn])
            }

            function Mn(n) {
                return (0, e.__asyncGenerator)(this, arguments, (function() {
                    var t, r, i;
                    return (0, e.__generator)(this, (function(u) {
                        switch (u.label) {
                            case 0:
                                t = n.getReader(), u.label = 1;
                            case 1:
                                u.trys.push([1, , 9, 10]), u.label = 2;
                            case 2:
                                return [4, (0, e.__await)(t.read())];
                            case 3:
                                return r = u.sent(), i = r.value, r.done ? [4, (0, e.__await)(void 0)] : [3, 5];
                            case 4:
                                return [2, u.sent()];
                            case 5:
                                return [4, (0, e.__await)(i)];
                            case 6:
                                return [4, u.sent()];
                            case 7:
                                return u.sent(), [3, 2];
                            case 8:
                                return [3, 10];
                            case 9:
                                return t.releaseLock(), [7];
                            case 10:
                                return [2]
                        }
                    }))
                }))
            }

            function Rn(n) {
                return i(null == n ? void 0 : n.getReader)
            }

            function Vn(n) {
                if (n instanceof j) return n;
                if (null != n) {
                    if (Nn(n)) return o = n, new j((function(n) {
                        var t = o[O]();
                        if (i(t.subscribe)) return t.subscribe(n);
                        throw new TypeError("Provided object does not correctly implement Symbol.observable")
                    }));
                    if (Fn(n)) return u = n, new j((function(n) {
                        for (var t = 0; t < u.length && !n.closed; t++) n.next(u[t]);
                        n.complete()
                    }));
                    if (jn(n)) return r = n, new j((function(n) {
                        r.then((function(t) {
                            n.closed || (n.next(t), n.complete())
                        }), (function(t) {
                            return n.error(t)
                        })).then(null, h)
                    }));
                    if (Pn(n)) return Un(n);
                    if (Ln(n)) return t = n, new j((function(n) {
                        var r, i;
                        try {
                            for (var u = (0, e.__values)(t), o = u.next(); !o.done; o = u.next()) {
                                var c = o.value;
                                if (n.next(c), n.closed) return
                            }
                        } catch (n) {
                            r = {
                                error: n
                            }
                        } finally {
                            try {
                                o && !o.done && (i = u.return) && i.call(u)
                            } finally {
                                if (r) throw r.error
                            }
                        }
                        n.complete()
                    }));
                    if (Rn(n)) return Un(Mn(n))
                }
                var t, r, u, o;
                throw qn(n)
            }

            function Un(n) {
                return new j((function(t) {
                    (function(n, t) {
                        var r, i, u, o;
                        return (0, e.__awaiter)(this, void 0, void 0, (function() {
                            var c, s;
                            return (0, e.__generator)(this, (function(l) {
                                switch (l.label) {
                                    case 0:
                                        l.trys.push([0, 5, 6, 11]), r = (0, e.__asyncValues)(n), l.label = 1;
                                    case 1:
                                        return [4, r.next()];
                                    case 2:
                                        if ((i = l.sent()).done) return [3, 4];
                                        if (c = i.value, t.next(c), t.closed) return [2];
                                        l.label = 3;
                                    case 3:
                                        return [3, 1];
                                    case 4:
                                        return [3, 11];
                                    case 5:
                                        return s = l.sent(), u = {
                                            error: s
                                        }, [3, 11];
                                    case 6:
                                        return l.trys.push([6, , 9, 10]), i && !i.done && (o = r.return) ? [4, o.call(r)] : [3, 8];
                                    case 7:
                                        l.sent(), l.label = 8;
                                    case 8:
                                        return [3, 10];
                                    case 9:
                                        if (u) throw u.error;
                                        return [7];
                                    case 10:
                                        return [7];
                                    case 11:
                                        return t.complete(), [2]
                                }
                            }))
                        }))
                    })(n, t).catch((function(n) {
                        return t.error(n)
                    }))
                }))
            }

            function Dn(n, t, r, e, i) {
                void 0 === e && (e = 0), void 0 === i && (i = !1);
                var u = t.schedule((function() {
                    r(), i ? n.add(this.schedule(null, e)) : this.unsubscribe()
                }), e);
                if (n.add(u), !i) return u
            }

            function Yn(n, t) {
                return void 0 === t && (t = 0), q((function(r, e) {
                    r.subscribe(z(e, (function(r) {
                        return Dn(e, n, (function() {
                            return e.next(r)
                        }), t)
                    }), (function() {
                        return Dn(e, n, (function() {
                            return e.complete()
                        }), t)
                    }), (function(r) {
                        return Dn(e, n, (function() {
                            return e.error(r)
                        }), t)
                    })))
                }))
            }

            function Bn(n, t) {
                return void 0 === t && (t = 0), q((function(r, e) {
                    e.add(n.schedule((function() {
                        return r.subscribe(e)
                    }), t))
                }))
            }

            function Hn(n, t) {
                return new j((function(r) {
                    var e;
                    return Dn(r, t, (function() {
                            e = n[Wn](), Dn(r, t, (function() {
                                var n, t, i;
                                try {
                                    t = (n = e.next()).value, i = n.done
                                } catch (n) {
                                    return void r.error(n)
                                }
                                i ? r.complete() : r.next(t)
                            }), 0, !0)
                        })),
                        function() {
                            return i(null == e ? void 0 : e.return) && e.return()
                        }
                }))
            }

            function Kn(n, t) {
                if (!n) throw new Error("Iterable cannot be null");
                return new j((function(r) {
                    Dn(r, t, (function() {
                        var e = n[Symbol.asyncIterator]();
                        Dn(r, t, (function() {
                            e.next().then((function(n) {
                                n.done ? r.complete() : r.next(n.value)
                            }))
                        }), 0, !0)
                    }))
                }))
            }

            function Zn(n, t) {
                if (null != n) {
                    if (Nn(n)) return function(n, t) {
                        return Vn(n).pipe(Bn(t), Yn(t))
                    }(n, t);
                    if (Fn(n)) return function(n, t) {
                        return new j((function(r) {
                            var e = 0;
                            return t.schedule((function() {
                                e === n.length ? r.complete() : (r.next(n[e++]), r.closed || this.schedule())
                            }))
                        }))
                    }(n, t);
                    if (jn(n)) return function(n, t) {
                        return Vn(n).pipe(Bn(t), Yn(t))
                    }(n, t);
                    if (Pn(n)) return Kn(n, t);
                    if (Ln(n)) return Hn(n, t);
                    if (Rn(n)) return function(n, t) {
                        return Kn(Mn(n), t)
                    }(n, t)
                }
                throw qn(n)
            }

            function Gn(n, t) {
                return t ? Zn(n, t) : Vn(n)
            }

            function Jn() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return Gn(n, kn(n))
            }

            function Xn(n, t) {
                var r = i(n) ? n : function() {
                        return n
                    },
                    e = function(n) {
                        return n.error(r())
                    };
                return new j(t ? function(n) {
                    return t.schedule(e, 0, n)
                } : e)
            }! function(n) {
                n.NEXT = "N", n.ERROR = "E", n.COMPLETE = "C"
            }(zn || (zn = {}));
            var Qn = function() {
                function n(n, t, r) {
                    this.kind = n, this.value = t, this.error = r, this.hasValue = "N" === n
                }
                return n.prototype.observe = function(n) {
                    return $n(this, n)
                }, n.prototype.do = function(n, t, r) {
                    var e = this,
                        i = e.kind,
                        u = e.value,
                        o = e.error;
                    return "N" === i ? null == n ? void 0 : n(u) : "E" === i ? null == t ? void 0 : t(o) : null == r ? void 0 : r()
                }, n.prototype.accept = function(n, t, r) {
                    var e;
                    return i(null === (e = n) || void 0 === e ? void 0 : e.next) ? this.observe(n) : this.do(n, t, r)
                }, n.prototype.toObservable = function() {
                    var n = this,
                        t = n.kind,
                        r = n.value,
                        e = n.error,
                        i = "N" === t ? Jn(r) : "E" === t ? Xn((function() {
                            return e
                        })) : "C" === t ? En : 0;
                    if (!i) throw new TypeError("Unexpected notification kind " + t);
                    return i
                }, n.createNext = function(t) {
                    return new n("N", t)
                }, n.createError = function(t) {
                    return new n("E", void 0, t)
                }, n.createComplete = function() {
                    return n.completeNotification
                }, n.completeNotification = new n("C"), n
            }();

            function $n(n, t) {
                var r, e, i, u = n,
                    o = u.kind,
                    c = u.value,
                    s = u.error;
                if ("string" != typeof o) throw new TypeError('Invalid notification, missing "kind"');
                "N" === o ? null === (r = t.next) || void 0 === r || r.call(t, c) : "E" === o ? null === (e = t.error) || void 0 === e || e.call(t, s) : null === (i = t.complete) || void 0 === i || i.call(t)
            }

            function nt(n) {
                return !!n && (n instanceof j || i(n.lift) && i(n.subscribe))
            }
            var tt = (0, u.d)((function(n) {
                return function() {
                    n(this), this.name = "EmptyError", this.message = "no elements in sequence"
                }
            }));

            function rt(n, t) {
                var r = "object" == typeof t;
                return new Promise((function(e, i) {
                    var u, o = !1;
                    n.subscribe({
                        next: function(n) {
                            u = n, o = !0
                        },
                        error: i,
                        complete: function() {
                            o ? e(u) : r ? e(t.defaultValue) : i(new tt)
                        }
                    })
                }))
            }

            function et(n, t) {
                var r = "object" == typeof t;
                return new Promise((function(e, i) {
                    var u = new E({
                        next: function(n) {
                            e(n), u.unsubscribe()
                        },
                        error: i,
                        complete: function() {
                            r ? e(t.defaultValue) : i(new tt)
                        }
                    });
                    n.subscribe(u)
                }))
            }
            var it = (0, u.d)((function(n) {
                    return function() {
                        n(this), this.name = "ArgumentOutOfRangeError", this.message = "argument out of range"
                    }
                })),
                ut = (0, u.d)((function(n) {
                    return function(t) {
                        n(this), this.name = "NotFoundError", this.message = t
                    }
                })),
                ot = (0, u.d)((function(n) {
                    return function(t) {
                        n(this), this.name = "SequenceError", this.message = t
                    }
                }));

            function ct(n) {
                return n instanceof Date && !isNaN(n)
            }
            var st = (0, u.d)((function(n) {
                return function(t) {
                    void 0 === t && (t = null), n(this), this.message = "Timeout has occurred", this.name = "TimeoutError", this.info = t
                }
            }));

            function lt(n, t) {
                var r = ct(n) ? {
                        first: n
                    } : "number" == typeof n ? {
                        each: n
                    } : n,
                    e = r.first,
                    i = r.each,
                    u = r.with,
                    o = void 0 === u ? at : u,
                    c = r.scheduler,
                    s = void 0 === c ? null != t ? t : hn : c,
                    l = r.meta,
                    a = void 0 === l ? null : l;
                if (null == e && null == i) throw new TypeError("No timeout provided.");
                return q((function(n, t) {
                    var r, u, c = null,
                        l = 0,
                        f = function(n) {
                            u = Dn(t, s, (function() {
                                try {
                                    r.unsubscribe(), Vn(o({
                                        meta: a,
                                        lastValue: c,
                                        seen: l
                                    })).subscribe(t)
                                } catch (n) {
                                    t.error(n)
                                }
                            }), n)
                        };
                    r = n.subscribe(z(t, (function(n) {
                        null == u || u.unsubscribe(), l++, t.next(c = n), i > 0 && f(i)
                    }), void 0, void 0, (function() {
                        (null == u ? void 0 : u.closed) || null == u || u.unsubscribe(), c = null
                    }))), !l && f(null != e ? "number" == typeof e ? e : +e - s.now() : i)
                }))
            }

            function at(n) {
                throw new st(n)
            }

            function ft(n, t) {
                return q((function(r, e) {
                    var i = 0;
                    r.subscribe(z(e, (function(r) {
                        e.next(n.call(t, r, i++))
                    })))
                }))
            }
            var dt = Array.isArray;

            function vt(n) {
                return ft((function(t) {
                    return function(n, t) {
                        return dt(t) ? n.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(t))) : n(t)
                    }(n, t)
                }))
            }

            function ht(n, t, r, i) {
                if (r) {
                    if (!Tn(r)) return function() {
                        for (var e = [], u = 0; u < arguments.length; u++) e[u] = arguments[u];
                        return ht(n, t, i).apply(this, e).pipe(vt(r))
                    };
                    i = r
                }
                return i ? function() {
                    for (var r = [], e = 0; e < arguments.length; e++) r[e] = arguments[e];
                    return ht(n, t).apply(this, r).pipe(Bn(i), Yn(i))
                } : function() {
                    for (var r = this, i = [], u = 0; u < arguments.length; u++) i[u] = arguments[u];
                    var o = new Q,
                        c = !0;
                    return new j((function(u) {
                        var s = o.subscribe(u);
                        if (c) {
                            c = !1;
                            var l = !1,
                                a = !1;
                            t.apply(r, (0, e.__spreadArray)((0, e.__spreadArray)([], (0, e.__read)(i)), [function() {
                                for (var t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                                if (n) {
                                    var e = t.shift();
                                    if (null != e) return void o.error(e)
                                }
                                o.next(1 < t.length ? t : t[0]), a = !0, l && o.complete()
                            }])), a && o.complete(), l = !0
                        }
                        return s
                    }))
                }
            }

            function pt(n, t, r) {
                return ht(!1, n, t, r)
            }

            function bt(n, t, r) {
                return ht(!0, n, t, r)
            }
            var yt = Array.isArray,
                mt = Object.getPrototypeOf,
                _t = Object.prototype,
                wt = Object.keys;

            function xt(n) {
                if (1 === n.length) {
                    var t = n[0];
                    if (yt(t)) return {
                        args: t,
                        keys: null
                    };
                    if ((e = t) && "object" == typeof e && mt(e) === _t) {
                        var r = wt(t);
                        return {
                            args: r.map((function(n) {
                                return t[n]
                            })),
                            keys: r
                        }
                    }
                }
                var e;
                return {
                    args: n,
                    keys: null
                }
            }

            function gt(n, t) {
                return n.reduce((function(n, r, e) {
                    return n[r] = t[e], n
                }), {})
            }

            function At() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = kn(n),
                    e = On(n),
                    i = xt(n),
                    u = i.args,
                    o = i.keys;
                if (0 === u.length) return Gn([], r);
                var c = new j(Et(u, r, o ? function(n) {
                    return gt(o, n)
                } : k));
                return e ? c.pipe(vt(e)) : c
            }

            function Et(n, t, r) {
                return void 0 === r && (r = k),
                    function(e) {
                        St(t, (function() {
                            for (var i = n.length, u = new Array(i), o = i, c = i, s = function(i) {
                                    St(t, (function() {
                                        var s = Gn(n[i], t),
                                            l = !1;
                                        s.subscribe(z(e, (function(n) {
                                            u[i] = n, l || (l = !0, c--), c || e.next(r(u.slice()))
                                        }), (function() {
                                            --o || e.complete()
                                        })))
                                    }), e)
                                }, l = 0; l < i; l++) s(l)
                        }), e)
                    }
            }

            function St(n, t, r) {
                n ? Dn(r, n, t) : t()
            }

            function Tt(n, t, r, e, i, u, o, c) {
                var s = [],
                    l = 0,
                    a = 0,
                    f = !1,
                    d = function() {
                        !f || s.length || l || t.complete()
                    },
                    v = function(n) {
                        return l < e ? h(n) : s.push(n)
                    },
                    h = function(n) {
                        u && t.next(n), l++;
                        var c = !1;
                        Vn(r(n, a++)).subscribe(z(t, (function(n) {
                            null == i || i(n), u ? v(n) : t.next(n)
                        }), (function() {
                            c = !0
                        }), void 0, (function() {
                            if (c) try {
                                l--;
                                for (var n = function() {
                                        var n = s.shift();
                                        o ? Dn(t, o, (function() {
                                            return h(n)
                                        })) : h(n)
                                    }; s.length && l < e;) n();
                                d()
                            } catch (n) {
                                t.error(n)
                            }
                        })))
                    };
                return n.subscribe(z(t, v, (function() {
                        f = !0, d()
                    }))),
                    function() {
                        null == c || c()
                    }
            }

            function It(n, t, r) {
                return void 0 === r && (r = 1 / 0), i(t) ? It((function(r, e) {
                    return ft((function(n, i) {
                        return t(r, n, e, i)
                    }))(Vn(n(r, e)))
                }), r) : ("number" == typeof t && (r = t), q((function(t, e) {
                    return Tt(t, e, n, r)
                })))
            }

            function Ot(n) {
                return void 0 === n && (n = 1 / 0), It(k, n)
            }

            function kt() {
                return Ot(1)
            }

            function Ct() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return kt()(Gn(n, kn(n)))
            }

            function Ft(n) {
                return new j((function(t) {
                    Vn(n()).subscribe(t)
                }))
            }
            var jt = {
                connector: function() {
                    return new K
                },
                resetOnDisconnect: !0
            };

            function Nt(n, t) {
                void 0 === t && (t = jt);
                var r = null,
                    e = t.connector,
                    i = t.resetOnDisconnect,
                    u = void 0 === i || i,
                    o = e(),
                    c = new j((function(n) {
                        return o.subscribe(n)
                    }));
                return c.connect = function() {
                    return r && !r.closed || (r = Ft((function() {
                        return n
                    })).subscribe(o), u && r.add((function() {
                        return o = e()
                    }))), r
                }, c
            }

            function Pt() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = On(n),
                    e = xt(n),
                    i = e.args,
                    u = e.keys,
                    o = new j((function(n) {
                        var t = i.length;
                        if (t)
                            for (var r = new Array(t), e = t, o = t, c = function(t) {
                                    var c = !1;
                                    Vn(i[t]).subscribe(z(n, (function(n) {
                                        c || (c = !0, o--), r[t] = n
                                    }), (function() {
                                        return e--
                                    }), void 0, (function() {
                                        e && c || (o || n.next(u ? gt(u, r) : r), n.complete())
                                    })))
                                }, s = 0; s < t; s++) c(s);
                        else n.complete()
                    }));
                return r ? o.pipe(vt(r)) : o
            }
            var qt = ["addListener", "removeListener"],
                zt = ["addEventListener", "removeEventListener"],
                Wt = ["on", "off"];

            function Lt(n, t, r, u) {
                if (i(r) && (u = r, r = void 0), u) return Lt(n, t, r).pipe(vt(u));
                var o = (0, e.__read)(function(n) {
                        return i(n.addEventListener) && i(n.removeEventListener)
                    }(n) ? zt.map((function(e) {
                        return function(i) {
                            return n[e](t, i, r)
                        }
                    })) : function(n) {
                        return i(n.addListener) && i(n.removeListener)
                    }(n) ? qt.map(Mt(n, t)) : function(n) {
                        return i(n.on) && i(n.off)
                    }(n) ? Wt.map(Mt(n, t)) : [], 2),
                    c = o[0],
                    s = o[1];
                if (!c && Fn(n)) return It((function(n) {
                    return Lt(n, t, r)
                }))(Vn(n));
                if (!c) throw new TypeError("Invalid event target");
                return new j((function(n) {
                    var t = function() {
                        for (var t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                        return n.next(1 < t.length ? t : t[0])
                    };
                    return c(t),
                        function() {
                            return s(t)
                        }
                }))
            }

            function Mt(n, t) {
                return function(r) {
                    return function(e) {
                        return n[r](t, e)
                    }
                }
            }

            function Rt(n, t, r) {
                return r ? Rt(n, t).pipe(vt(r)) : new j((function(r) {
                    var e = function() {
                            for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                            return r.next(1 === n.length ? n[0] : n)
                        },
                        u = n(e);
                    return i(t) ? function() {
                        return t(e, u)
                    } : void 0
                }))
            }

            function Vt(n, t, r, i, u) {
                var o, c, s, l;

                function a() {
                    var n;
                    return (0, e.__generator)(this, (function(e) {
                        switch (e.label) {
                            case 0:
                                n = l, e.label = 1;
                            case 1:
                                return t && !t(n) ? [3, 4] : [4, s(n)];
                            case 2:
                                e.sent(), e.label = 3;
                            case 3:
                                return n = r(n), [3, 1];
                            case 4:
                                return [2]
                        }
                    }))
                }
                return 1 === arguments.length ? (l = (o = n).initialState, t = o.condition, r = o.iterate, c = o.resultSelector, s = void 0 === c ? k : c, u = o.scheduler) : (l = n, !i || Tn(i) ? (s = k, u = i) : s = i), Ft(u ? function() {
                    return Hn(a(), u)
                } : a)
            }

            function Ut(n, t, r) {
                return Ft((function() {
                    return n() ? t : r
                }))
            }

            function Dt(n, t, r) {
                void 0 === n && (n = 0), void 0 === r && (r = pn);
                var e = -1;
                return null != t && (Tn(t) ? r = t : e = t), new j((function(t) {
                    var i = ct(n) ? +n - r.now() : n;
                    i < 0 && (i = 0);
                    var u = 0;
                    return r.schedule((function() {
                        t.closed || (t.next(u++), 0 <= e ? this.schedule(void 0, e) : t.complete())
                    }), i)
                }))
            }

            function Yt(n, t) {
                return void 0 === n && (n = 0), void 0 === t && (t = hn), n < 0 && (n = 0), Dt(n, n, t)
            }

            function Bt() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = kn(n),
                    e = Cn(n, 1 / 0),
                    i = n;
                return i.length ? 1 === i.length ? Vn(i[0]) : Ot(e)(Gn(i, r)) : En
            }
            var Ht = new j(p);

            function Kt() {
                return Ht
            }
            var Zt = Array.isArray;

            function Gt(n) {
                return 1 === n.length && Zt(n[0]) ? n[0] : n
            }

            function Jt() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = Gt(n);
                return new j((function(n) {
                    var t = 0,
                        e = function() {
                            if (t < r.length) {
                                var i = void 0;
                                try {
                                    i = Vn(r[t++])
                                } catch (n) {
                                    return void e()
                                }
                                var u = new W(n, void 0, p, p);
                                i.subscribe(u), u.add(e)
                            } else n.complete()
                        };
                    e()
                }))
            }

            function Xt(n, t) {
                return Gn(Object.entries(n), t)
            }

            function Qt(n, t) {
                return function(r, e) {
                    return !n.call(t, r, e)
                }
            }

            function $t(n, t) {
                return q((function(r, e) {
                    var i = 0;
                    r.subscribe(z(e, (function(r) {
                        return n.call(t, r, i++) && e.next(r)
                    })))
                }))
            }

            function nr(n, t, r) {
                return [$t(t, r)(Vn(n)), $t(Qt(t, r))(Vn(n))]
            }

            function tr() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return 1 === (n = Gt(n)).length ? Vn(n[0]) : new j(rr(n))
            }

            function rr(n) {
                return function(t) {
                    for (var r = [], e = function(e) {
                            r.push(Vn(n[e]).subscribe(z(t, (function(n) {
                                if (r) {
                                    for (var i = 0; i < r.length; i++) i !== e && r[i].unsubscribe();
                                    r = null
                                }
                                t.next(n)
                            }))))
                        }, i = 0; r && !t.closed && i < n.length; i++) e(i)
                }
            }

            function er(n, t, r) {
                if (null == t && (t = n, n = 0), t <= 0) return En;
                var e = t + n;
                return new j(r ? function(t) {
                    var i = n;
                    return r.schedule((function() {
                        i < e ? (t.next(i++), this.schedule()) : t.complete()
                    }))
                } : function(t) {
                    for (var r = n; r < e && !t.closed;) t.next(r++);
                    t.complete()
                })
            }

            function ir(n, t) {
                return new j((function(r) {
                    var e = n(),
                        i = t(e);
                    return (i ? Vn(i) : En).subscribe(r),
                        function() {
                            e && e.unsubscribe()
                        }
                }))
            }

            function ur() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = On(n),
                    i = Gt(n);
                return i.length ? new j((function(n) {
                    var t = i.map((function() {
                            return []
                        })),
                        u = i.map((function() {
                            return !1
                        }));
                    n.add((function() {
                        t = u = null
                    }));
                    for (var o = function(o) {
                            Vn(i[o]).subscribe(z(n, (function(i) {
                                if (t[o].push(i), t.every((function(n) {
                                        return n.length
                                    }))) {
                                    var c = t.map((function(n) {
                                        return n.shift()
                                    }));
                                    n.next(r ? r.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(c))) : c), t.some((function(n, t) {
                                        return !n.length && u[t]
                                    })) && n.complete()
                                }
                            }), (function() {
                                u[o] = !0, !t[o].length && n.complete()
                            })))
                        }, c = 0; !n.closed && c < i.length; c++) o(c);
                    return function() {
                        t = u = null
                    }
                })) : En
            }

            function or(n) {
                return q((function(t, r) {
                    var e = !1,
                        i = null,
                        u = null,
                        o = !1,
                        c = function() {
                            if (null == u || u.unsubscribe(), u = null, e) {
                                e = !1;
                                var n = i;
                                i = null, r.next(n)
                            }
                            o && r.complete()
                        },
                        s = function() {
                            u = null, o && r.complete()
                        };
                    t.subscribe(z(r, (function(t) {
                        e = !0, i = t, u || Vn(n(t)).subscribe(u = z(r, c, s))
                    }), (function() {
                        o = !0, (!e || !u || u.closed) && r.complete()
                    })))
                }))
            }

            function cr(n, t) {
                return void 0 === t && (t = hn), or((function() {
                    return Dt(n, t)
                }))
            }

            function sr(n) {
                return q((function(t, r) {
                    var e = [];
                    return t.subscribe(z(r, (function(n) {
                            return e.push(n)
                        }), (function() {
                            r.next(e), r.complete()
                        }))), Vn(n).subscribe(z(r, (function() {
                            var n = e;
                            e = [], r.next(n)
                        }), p)),
                        function() {
                            e = null
                        }
                }))
            }

            function lr(n, t) {
                return void 0 === t && (t = null), t = null != t ? t : n, q((function(r, i) {
                    var u = [],
                        o = 0;
                    r.subscribe(z(i, (function(r) {
                        var s, l, a, f, d = null;
                        o++ % t == 0 && u.push([]);
                        try {
                            for (var v = (0, e.__values)(u), h = v.next(); !h.done; h = v.next())(y = h.value).push(r), n <= y.length && (d = null != d ? d : []).push(y)
                        } catch (n) {
                            s = {
                                error: n
                            }
                        } finally {
                            try {
                                h && !h.done && (l = v.return) && l.call(v)
                            } finally {
                                if (s) throw s.error
                            }
                        }
                        if (d) try {
                            for (var p = (0, e.__values)(d), b = p.next(); !b.done; b = p.next()) {
                                var y = b.value;
                                c(u, y), i.next(y)
                            }
                        } catch (n) {
                            a = {
                                error: n
                            }
                        } finally {
                            try {
                                b && !b.done && (f = p.return) && f.call(p)
                            } finally {
                                if (a) throw a.error
                            }
                        }
                    }), (function() {
                        var n, t;
                        try {
                            for (var r = (0, e.__values)(u), o = r.next(); !o.done; o = r.next()) {
                                var c = o.value;
                                i.next(c)
                            }
                        } catch (t) {
                            n = {
                                error: t
                            }
                        } finally {
                            try {
                                o && !o.done && (t = r.return) && t.call(r)
                            } finally {
                                if (n) throw n.error
                            }
                        }
                        i.complete()
                    }), void 0, (function() {
                        u = null
                    })))
                }))
            }

            function ar(n) {
                for (var t, r, i = [], u = 1; u < arguments.length; u++) i[u - 1] = arguments[u];
                var o = null !== (t = kn(i)) && void 0 !== t ? t : hn,
                    l = null !== (r = i[0]) && void 0 !== r ? r : null,
                    a = i[1] || 1 / 0;
                return q((function(t, r) {
                    var i = [],
                        u = !1,
                        f = function(n) {
                            var t = n.buffer;
                            n.subs.unsubscribe(), c(i, n), r.next(t), u && d()
                        },
                        d = function() {
                            if (i) {
                                var t = new s;
                                r.add(t);
                                var e = {
                                    buffer: [],
                                    subs: t
                                };
                                i.push(e), Dn(t, o, (function() {
                                    return f(e)
                                }), n)
                            }
                        };
                    null !== l && l >= 0 ? Dn(r, o, d, l, !0) : u = !0, d();
                    var v = z(r, (function(n) {
                        var t, r, u = i.slice();
                        try {
                            for (var o = (0, e.__values)(u), c = o.next(); !c.done; c = o.next()) {
                                var s = c.value,
                                    l = s.buffer;
                                l.push(n), a <= l.length && f(s)
                            }
                        } catch (n) {
                            t = {
                                error: n
                            }
                        } finally {
                            try {
                                c && !c.done && (r = o.return) && r.call(o)
                            } finally {
                                if (t) throw t.error
                            }
                        }
                    }), (function() {
                        for (; null == i ? void 0 : i.length;) r.next(i.shift().buffer);
                        null == v || v.unsubscribe(), r.complete(), r.unsubscribe()
                    }), void 0, (function() {
                        return i = null
                    }));
                    t.subscribe(v)
                }))
            }

            function fr(n, t) {
                return q((function(r, i) {
                    var u = [];
                    Vn(n).subscribe(z(i, (function(n) {
                        var r = [];
                        u.push(r);
                        var e = new s;
                        e.add(Vn(t(n)).subscribe(z(i, (function() {
                            c(u, r), i.next(r), e.unsubscribe()
                        }), p)))
                    }), p)), r.subscribe(z(i, (function(n) {
                        var t, r;
                        try {
                            for (var i = (0, e.__values)(u), o = i.next(); !o.done; o = i.next()) o.value.push(n)
                        } catch (n) {
                            t = {
                                error: n
                            }
                        } finally {
                            try {
                                o && !o.done && (r = i.return) && r.call(i)
                            } finally {
                                if (t) throw t.error
                            }
                        }
                    }), (function() {
                        for (; u.length > 0;) i.next(u.shift());
                        i.complete()
                    })))
                }))
            }

            function dr(n) {
                return q((function(t, r) {
                    var e = null,
                        i = null,
                        u = function() {
                            null == i || i.unsubscribe();
                            var t = e;
                            e = [], t && r.next(t), Vn(n()).subscribe(i = z(r, u, p))
                        };
                    u(), t.subscribe(z(r, (function(n) {
                        return null == e ? void 0 : e.push(n)
                    }), (function() {
                        e && r.next(e), r.complete()
                    }), void 0, (function() {
                        return e = i = null
                    })))
                }))
            }

            function vr(n) {
                return q((function(t, r) {
                    var e, i = null,
                        u = !1;
                    i = t.subscribe(z(r, void 0, void 0, (function(o) {
                        e = Vn(n(o, vr(n)(t))), i ? (i.unsubscribe(), i = null, e.subscribe(r)) : u = !0
                    }))), u && (i.unsubscribe(), i = null, e.subscribe(r))
                }))
            }

            function hr(n, t, r, e, i) {
                return function(u, o) {
                    var c = r,
                        s = t,
                        l = 0;
                    u.subscribe(z(o, (function(t) {
                        var r = l++;
                        s = c ? n(s, t, r) : (c = !0, t), e && o.next(s)
                    }), i && function() {
                        c && o.next(s), o.complete()
                    }))
                }
            }

            function pr(n, t) {
                return q(hr(n, t, arguments.length >= 2, !1, !0))
            }
            var br = function(n, t) {
                return n.push(t), n
            };

            function yr() {
                return q((function(n, t) {
                    pr(br, [])(n).subscribe(t)
                }))
            }

            function mr(n, t) {
                return C(yr(), It((function(t) {
                    return n(t)
                })), t ? vt(t) : k)
            }

            function _r(n) {
                return mr(At, n)
            }
            var wr = _r;

            function xr() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = On(n);
                return r ? C(xr.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n))), vt(r)) : q((function(t, r) {
                    Et((0, e.__spreadArray)([t], (0, e.__read)(Gt(n))))(r)
                }))
            }

            function gr() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return xr.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n)))
            }

            function Ar(n, t) {
                return i(t) ? It(n, t, 1) : It(n, 1)
            }

            function Er(n, t) {
                return i(t) ? Ar((function() {
                    return n
                }), t) : Ar((function() {
                    return n
                }))
            }

            function Sr() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = kn(n);
                return q((function(t, i) {
                    kt()(Gn((0, e.__spreadArray)([t], (0, e.__read)(n)), r)).subscribe(i)
                }))
            }

            function Tr() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return Sr.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n)))
            }
            var Ir = {
                connector: function() {
                    return new K
                }
            };

            function Or(n, t) {
                void 0 === t && (t = Ir);
                var r = t.connector;
                return q((function(t, e) {
                    var i, u = r();
                    Vn(n((i = u, new j((function(n) {
                        return i.subscribe(n)
                    }))))).subscribe(e), e.add(t.subscribe(u))
                }))
            }

            function kr(n) {
                return pr((function(t, r, e) {
                    return !n || n(r, e) ? t + 1 : t
                }), 0)
            }

            function Cr(n) {
                return q((function(t, r) {
                    var e = !1,
                        i = null,
                        u = null,
                        o = function() {
                            if (null == u || u.unsubscribe(), u = null, e) {
                                e = !1;
                                var n = i;
                                i = null, r.next(n)
                            }
                        };
                    t.subscribe(z(r, (function(t) {
                        null == u || u.unsubscribe(), e = !0, i = t, u = z(r, o, p), Vn(n(t)).subscribe(u)
                    }), (function() {
                        o(), r.complete()
                    }), void 0, (function() {
                        i = u = null
                    })))
                }))
            }

            function Fr(n, t) {
                return void 0 === t && (t = hn), q((function(r, e) {
                    var i = null,
                        u = null,
                        o = null,
                        c = function() {
                            if (i) {
                                i.unsubscribe(), i = null;
                                var n = u;
                                u = null, e.next(n)
                            }
                        };

                    function s() {
                        var r = o + n,
                            u = t.now();
                        if (u < r) return i = this.schedule(void 0, r - u), void e.add(i);
                        c()
                    }
                    r.subscribe(z(e, (function(r) {
                        u = r, o = t.now(), i || (i = t.schedule(s, n), e.add(i))
                    }), (function() {
                        c(), e.complete()
                    }), void 0, (function() {
                        u = i = null
                    })))
                }))
            }

            function jr(n) {
                return q((function(t, r) {
                    var e = !1;
                    t.subscribe(z(r, (function(n) {
                        e = !0, r.next(n)
                    }), (function() {
                        e || r.next(n), r.complete()
                    })))
                }))
            }

            function Nr(n) {
                return n <= 0 ? function() {
                    return En
                } : q((function(t, r) {
                    var e = 0;
                    t.subscribe(z(r, (function(t) {
                        ++e <= n && (r.next(t), n <= e && r.complete())
                    })))
                }))
            }

            function Pr() {
                return q((function(n, t) {
                    n.subscribe(z(t, p))
                }))
            }

            function qr(n) {
                return ft((function() {
                    return n
                }))
            }

            function zr(n, t) {
                return t ? function(r) {
                    return Ct(t.pipe(Nr(1), Pr()), r.pipe(zr(n)))
                } : It((function(t, r) {
                    return Vn(n(t, r)).pipe(Nr(1), qr(t))
                }))
            }

            function Wr(n, t) {
                void 0 === t && (t = hn);
                var r = Dt(n, t);
                return zr((function() {
                    return r
                }))
            }

            function Lr() {
                return q((function(n, t) {
                    n.subscribe(z(t, (function(n) {
                        return $n(n, t)
                    })))
                }))
            }

            function Mr(n, t) {
                return q((function(r, e) {
                    var i = new Set;
                    r.subscribe(z(e, (function(t) {
                        var r = n ? n(t) : t;
                        i.has(r) || (i.add(r), e.next(t))
                    }))), t && Vn(t).subscribe(z(e, (function() {
                        return i.clear()
                    }), p))
                }))
            }

            function Rr(n, t) {
                return void 0 === t && (t = k), n = null != n ? n : Vr, q((function(r, e) {
                    var i, u = !0;
                    r.subscribe(z(e, (function(r) {
                        var o = t(r);
                        !u && n(i, o) || (u = !1, i = o, e.next(r))
                    })))
                }))
            }

            function Vr(n, t) {
                return n === t
            }

            function Ur(n, t) {
                return Rr((function(r, e) {
                    return t ? t(r[n], e[n]) : r[n] === e[n]
                }))
            }

            function Dr(n) {
                return void 0 === n && (n = Yr), q((function(t, r) {
                    var e = !1;
                    t.subscribe(z(r, (function(n) {
                        e = !0, r.next(n)
                    }), (function() {
                        return e ? r.complete() : r.error(n())
                    })))
                }))
            }

            function Yr() {
                return new tt
            }

            function Br(n, t) {
                if (n < 0) throw new it;
                var r = arguments.length >= 2;
                return function(e) {
                    return e.pipe($t((function(t, r) {
                        return r === n
                    })), Nr(1), r ? jr(t) : Dr((function() {
                        return new it
                    })))
                }
            }

            function Hr() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return function(t) {
                    return Ct(t, Jn.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n))))
                }
            }

            function Kr(n, t) {
                return q((function(r, e) {
                    var i = 0;
                    r.subscribe(z(e, (function(u) {
                        n.call(t, u, i++, r) || (e.next(!1), e.complete())
                    }), (function() {
                        e.next(!0), e.complete()
                    })))
                }))
            }

            function Zr(n, t) {
                return t ? function(r) {
                    return r.pipe(Zr((function(r, e) {
                        return Vn(n(r, e)).pipe(ft((function(n, i) {
                            return t(r, n, e, i)
                        })))
                    })))
                } : q((function(t, r) {
                    var e = 0,
                        i = null,
                        u = !1;
                    t.subscribe(z(r, (function(t) {
                        i || (i = z(r, void 0, (function() {
                            i = null, u && r.complete()
                        })), Vn(n(t, e++)).subscribe(i))
                    }), (function() {
                        u = !0, !i && r.complete()
                    })))
                }))
            }

            function Gr() {
                return Zr(k)
            }
            var Jr = Gr;

            function Xr(n, t, r) {
                return void 0 === t && (t = 1 / 0), t = (t || 0) < 1 ? 1 / 0 : t, q((function(e, i) {
                    return Tt(e, i, n, t, void 0, !0, r)
                }))
            }

            function Qr(n) {
                return q((function(t, r) {
                    try {
                        t.subscribe(r)
                    } finally {
                        r.add(n)
                    }
                }))
            }

            function $r(n, t) {
                return q(ne(n, t, "value"))
            }

            function ne(n, t, r) {
                var e = "index" === r;
                return function(r, i) {
                    var u = 0;
                    r.subscribe(z(i, (function(o) {
                        var c = u++;
                        n.call(t, o, c, r) && (i.next(e ? c : o), i.complete())
                    }), (function() {
                        i.next(e ? -1 : void 0), i.complete()
                    })))
                }
            }

            function te(n, t) {
                return q(ne(n, t, "index"))
            }

            function re(n, t) {
                var r = arguments.length >= 2;
                return function(e) {
                    return e.pipe(n ? $t((function(t, r) {
                        return n(t, r, e)
                    })) : k, Nr(1), r ? jr(t) : Dr((function() {
                        return new tt
                    })))
                }
            }

            function ee(n, t, r, e) {
                return q((function(i, u) {
                    var o;
                    t && "function" != typeof t ? (r = t.duration, o = t.element, e = t.connector) : o = t;
                    var c = new Map,
                        s = function(n) {
                            c.forEach(n), n(u)
                        },
                        l = function(n) {
                            return s((function(t) {
                                return t.error(n)
                            }))
                        },
                        a = 0,
                        f = !1,
                        d = new W(u, (function(t) {
                            try {
                                var i = n(t),
                                    s = c.get(i);
                                if (!s) {
                                    c.set(i, s = e ? e() : new K);
                                    var v = (p = i, b = s, (y = new j((function(n) {
                                        a++;
                                        var t = b.subscribe(n);
                                        return function() {
                                            t.unsubscribe(), 0 == --a && f && d.unsubscribe()
                                        }
                                    }))).key = p, y);
                                    if (u.next(v), r) {
                                        var h = z(s, (function() {
                                            s.complete(), null == h || h.unsubscribe()
                                        }), void 0, void 0, (function() {
                                            return c.delete(i)
                                        }));
                                        d.add(Vn(r(v)).subscribe(h))
                                    }
                                }
                                s.next(o ? o(t) : t)
                            } catch (n) {
                                l(n)
                            }
                            var p, b, y
                        }), (function() {
                            return s((function(n) {
                                return n.complete()
                            }))
                        }), l, (function() {
                            return c.clear()
                        }), (function() {
                            return f = !0, 0 === a
                        }));
                    i.subscribe(d)
                }))
            }

            function ie() {
                return q((function(n, t) {
                    n.subscribe(z(t, (function() {
                        t.next(!1), t.complete()
                    }), (function() {
                        t.next(!0), t.complete()
                    })))
                }))
            }

            function ue(n) {
                return n <= 0 ? function() {
                    return En
                } : q((function(t, r) {
                    var i = [];
                    t.subscribe(z(r, (function(t) {
                        i.push(t), n < i.length && i.shift()
                    }), (function() {
                        var n, t;
                        try {
                            for (var u = (0, e.__values)(i), o = u.next(); !o.done; o = u.next()) {
                                var c = o.value;
                                r.next(c)
                            }
                        } catch (t) {
                            n = {
                                error: t
                            }
                        } finally {
                            try {
                                o && !o.done && (t = u.return) && t.call(u)
                            } finally {
                                if (n) throw n.error
                            }
                        }
                        r.complete()
                    }), void 0, (function() {
                        i = null
                    })))
                }))
            }

            function oe(n, t) {
                var r = arguments.length >= 2;
                return function(e) {
                    return e.pipe(n ? $t((function(t, r) {
                        return n(t, r, e)
                    })) : k, ue(1), r ? jr(t) : Dr((function() {
                        return new tt
                    })))
                }
            }

            function ce() {
                return q((function(n, t) {
                    n.subscribe(z(t, (function(n) {
                        t.next(Qn.createNext(n))
                    }), (function() {
                        t.next(Qn.createComplete()), t.complete()
                    }), (function(n) {
                        t.next(Qn.createError(n)), t.complete()
                    })))
                }))
            }

            function se(n) {
                return pr(i(n) ? function(t, r) {
                    return n(t, r) > 0 ? t : r
                } : function(n, t) {
                    return n > t ? n : t
                })
            }
            var le = It;

            function ae(n, t, r) {
                return void 0 === r && (r = 1 / 0), i(t) ? It((function() {
                    return n
                }), t, r) : ("number" == typeof t && (r = t), It((function() {
                    return n
                }), r))
            }

            function fe(n, t, r) {
                return void 0 === r && (r = 1 / 0), q((function(e, i) {
                    var u = t;
                    return Tt(e, i, (function(t, r) {
                        return n(u, t, r)
                    }), r, (function(n) {
                        u = n
                    }), !1, void 0, (function() {
                        return u = null
                    }))
                }))
            }

            function de() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = kn(n),
                    i = Cn(n, 1 / 0);
                return n = Gt(n), q((function(t, u) {
                    Ot(i)(Gn((0, e.__spreadArray)([t], (0, e.__read)(n)), r)).subscribe(u)
                }))
            }

            function ve() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return de.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n)))
            }

            function he(n) {
                return pr(i(n) ? function(t, r) {
                    return n(t, r) < 0 ? t : r
                } : function(n, t) {
                    return n < t ? n : t
                })
            }

            function pe(n, t) {
                var r = i(n) ? n : function() {
                    return n
                };
                return i(t) ? Or(t, {
                    connector: r
                }) : function(n) {
                    return new M(n, r)
                }
            }

            function be() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = Gt(n);
                return function(n) {
                    return Jt.apply(void 0, (0, e.__spreadArray)([n], (0, e.__read)(r)))
                }
            }

            function ye() {
                return q((function(n, t) {
                    var r, e = !1;
                    n.subscribe(z(t, (function(n) {
                        var i = r;
                        r = n, e && t.next([i, n]), e = !0
                    })))
                }))
            }

            function me() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = n.length;
                if (0 === r) throw new Error("list of properties cannot be empty.");
                return ft((function(t) {
                    for (var e = t, i = 0; i < r; i++) {
                        var u = null == e ? void 0 : e[n[i]];
                        if (void 0 === u) return;
                        e = u
                    }
                    return e
                }))
            }

            function _e(n) {
                return n ? function(t) {
                    return Or(n)(t)
                } : function(n) {
                    return pe(new K)(n)
                }
            }

            function we(n) {
                return function(t) {
                    var r = new G(n);
                    return new M(t, (function() {
                        return r
                    }))
                }
            }

            function xe() {
                return function(n) {
                    var t = new Q;
                    return new M(n, (function() {
                        return t
                    }))
                }
            }

            function ge(n, t, r, e) {
                r && !i(r) && (e = r);
                var u = i(r) ? r : void 0;
                return function(r) {
                    return pe(new X(n, t, e), u)(r)
                }
            }

            function Ae() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return n.length ? q((function(t, r) {
                    rr((0, e.__spreadArray)([t], (0, e.__read)(n)))(r)
                })) : k
            }

            function Ee(n) {
                var t, r, e = 1 / 0;
                return null != n && ("object" == typeof n ? (t = n.count, e = void 0 === t ? 1 / 0 : t, r = n.delay) : e = n), e <= 0 ? function() {
                    return En
                } : q((function(n, t) {
                    var i, u = 0,
                        o = function() {
                            if (null == i || i.unsubscribe(), i = null, null != r) {
                                var n = "number" == typeof r ? Dt(r) : Vn(r(u)),
                                    e = z(t, (function() {
                                        e.unsubscribe(), c()
                                    }));
                                n.subscribe(e)
                            } else c()
                        },
                        c = function() {
                            var r = !1;
                            i = n.subscribe(z(t, void 0, (function() {
                                ++u < e ? i ? o() : r = !0 : t.complete()
                            }))), r && o()
                        };
                    c()
                }))
            }

            function Se(n) {
                return q((function(t, r) {
                    var e, i, u = !1,
                        o = !1,
                        c = !1,
                        s = function() {
                            return c && o && (r.complete(), !0)
                        },
                        l = function() {
                            c = !1, e = t.subscribe(z(r, void 0, (function() {
                                c = !0, !s() && (i || (i = new K, Vn(n(i)).subscribe(z(r, (function() {
                                    e ? l() : u = !0
                                }), (function() {
                                    o = !0, s()
                                })))), i).next()
                            }))), u && (e.unsubscribe(), e = null, u = !1, l())
                        };
                    l()
                }))
            }

            function Te(n) {
                var t;
                void 0 === n && (n = 1 / 0);
                var r = (t = n && "object" == typeof n ? n : {
                        count: n
                    }).count,
                    e = void 0 === r ? 1 / 0 : r,
                    i = t.delay,
                    u = t.resetOnSuccess,
                    o = void 0 !== u && u;
                return e <= 0 ? k : q((function(n, t) {
                    var r, u = 0,
                        c = function() {
                            var s = !1;
                            r = n.subscribe(z(t, (function(n) {
                                o && (u = 0), t.next(n)
                            }), void 0, (function(n) {
                                if (u++ < e) {
                                    var o = function() {
                                        r ? (r.unsubscribe(), r = null, c()) : s = !0
                                    };
                                    if (null != i) {
                                        var l = "number" == typeof i ? Dt(i) : Vn(i(n, u)),
                                            a = z(t, (function() {
                                                a.unsubscribe(), o()
                                            }), (function() {
                                                t.complete()
                                            }));
                                        l.subscribe(a)
                                    } else o()
                                } else t.error(n)
                            }))), s && (r.unsubscribe(), r = null, c())
                        };
                    c()
                }))
            }

            function Ie(n) {
                return q((function(t, r) {
                    var e, i, u = !1,
                        o = function() {
                            e = t.subscribe(z(r, void 0, void 0, (function(t) {
                                i || (i = new K, Vn(n(i)).subscribe(z(r, (function() {
                                    return e ? o() : u = !0
                                })))), i && i.next(t)
                            }))), u && (e.unsubscribe(), e = null, u = !1, o())
                        };
                    o()
                }))
            }

            function Oe(n) {
                return q((function(t, r) {
                    var e = !1,
                        i = null;
                    t.subscribe(z(r, (function(n) {
                        e = !0, i = n
                    }))), Vn(n).subscribe(z(r, (function() {
                        if (e) {
                            e = !1;
                            var n = i;
                            i = null, r.next(n)
                        }
                    }), p))
                }))
            }

            function ke(n, t) {
                return void 0 === t && (t = hn), Oe(Yt(n, t))
            }

            function Ce(n, t) {
                return q(hr(n, t, arguments.length >= 2, !0))
            }

            function Fe(n, t) {
                return void 0 === t && (t = function(n, t) {
                    return n === t
                }), q((function(r, e) {
                    var i = {
                            buffer: [],
                            complete: !1
                        },
                        u = {
                            buffer: [],
                            complete: !1
                        },
                        o = function(n) {
                            e.next(n), e.complete()
                        },
                        c = function(n, r) {
                            var i = z(e, (function(e) {
                                var i = r.buffer,
                                    u = r.complete;
                                0 === i.length ? u ? o(!1) : n.buffer.push(e) : !t(e, i.shift()) && o(!1)
                            }), (function() {
                                n.complete = !0;
                                var t = r.complete,
                                    e = r.buffer;
                                t && o(0 === e.length), null == i || i.unsubscribe()
                            }));
                            return i
                        };
                    r.subscribe(c(i, u)), Vn(n).subscribe(c(u, i))
                }))
            }

            function je(n) {
                void 0 === n && (n = {});
                var t = n.connector,
                    r = void 0 === t ? function() {
                        return new K
                    } : t,
                    e = n.resetOnError,
                    i = void 0 === e || e,
                    u = n.resetOnComplete,
                    o = void 0 === u || u,
                    c = n.resetOnRefCountZero,
                    s = void 0 === c || c;
                return function(n) {
                    var t, e, u, c = 0,
                        l = !1,
                        a = !1,
                        f = function() {
                            null == e || e.unsubscribe(), e = void 0
                        },
                        d = function() {
                            f(), t = u = void 0, l = a = !1
                        },
                        v = function() {
                            var n = t;
                            d(), null == n || n.unsubscribe()
                        };
                    return q((function(n, h) {
                        c++, a || l || f();
                        var p = u = null != u ? u : r();
                        h.add((function() {
                            0 != --c || a || l || (e = Ne(v, s))
                        })), p.subscribe(h), !t && c > 0 && (t = new E({
                            next: function(n) {
                                return p.next(n)
                            },
                            error: function(n) {
                                a = !0, f(), e = Ne(d, i, n), p.error(n)
                            },
                            complete: function() {
                                l = !0, f(), e = Ne(d, o), p.complete()
                            }
                        }), Vn(n).subscribe(t))
                    }))(n)
                }
            }

            function Ne(n, t) {
                for (var r = [], i = 2; i < arguments.length; i++) r[i - 2] = arguments[i];
                if (!0 !== t) {
                    if (!1 !== t) {
                        var u = new E({
                            next: function() {
                                u.unsubscribe(), n()
                            }
                        });
                        return Vn(t.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(r)))).subscribe(u)
                    }
                } else n()
            }

            function Pe(n, t, r) {
                var e, i, u, o, c = !1;
                return n && "object" == typeof n ? (e = n.bufferSize, o = void 0 === e ? 1 / 0 : e, i = n.windowTime, t = void 0 === i ? 1 / 0 : i, c = void 0 !== (u = n.refCount) && u, r = n.scheduler) : o = null != n ? n : 1 / 0, je({
                    connector: function() {
                        return new X(o, t, r)
                    },
                    resetOnError: !0,
                    resetOnComplete: !1,
                    resetOnRefCountZero: c
                })
            }

            function qe(n) {
                return q((function(t, r) {
                    var e, i = !1,
                        u = !1,
                        o = 0;
                    t.subscribe(z(r, (function(c) {
                        u = !0, n && !n(c, o++, t) || (i && r.error(new ot("Too many matching values")), i = !0, e = c)
                    }), (function() {
                        i ? (r.next(e), r.complete()) : r.error(u ? new ut("No matching values") : new tt)
                    })))
                }))
            }

            function ze(n) {
                return $t((function(t, r) {
                    return n <= r
                }))
            }

            function We(n) {
                return n <= 0 ? k : q((function(t, r) {
                    var e = new Array(n),
                        i = 0;
                    return t.subscribe(z(r, (function(t) {
                            var u = i++;
                            if (u < n) e[u] = t;
                            else {
                                var o = u % n,
                                    c = e[o];
                                e[o] = t, r.next(c)
                            }
                        }))),
                        function() {
                            e = null
                        }
                }))
            }

            function Le(n) {
                return q((function(t, r) {
                    var e = !1,
                        i = z(r, (function() {
                            null == i || i.unsubscribe(), e = !0
                        }), p);
                    Vn(n).subscribe(i), t.subscribe(z(r, (function(n) {
                        return e && r.next(n)
                    })))
                }))
            }

            function Me(n) {
                return q((function(t, r) {
                    var e = !1,
                        i = 0;
                    t.subscribe(z(r, (function(t) {
                        return (e || (e = !n(t, i++))) && r.next(t)
                    })))
                }))
            }

            function Re() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = kn(n);
                return q((function(t, e) {
                    (r ? Ct(n, t, r) : Ct(n, t)).subscribe(e)
                }))
            }

            function Ve(n, t) {
                return q((function(r, e) {
                    var i = null,
                        u = 0,
                        o = !1,
                        c = function() {
                            return o && !i && e.complete()
                        };
                    r.subscribe(z(e, (function(r) {
                        null == i || i.unsubscribe();
                        var o = 0,
                            s = u++;
                        Vn(n(r, s)).subscribe(i = z(e, (function(n) {
                            return e.next(t ? t(r, n, s, o++) : n)
                        }), (function() {
                            i = null, c()
                        })))
                    }), (function() {
                        o = !0, c()
                    })))
                }))
            }

            function Ue() {
                return Ve(k)
            }

            function De(n, t) {
                return i(t) ? Ve((function() {
                    return n
                }), t) : Ve((function() {
                    return n
                }))
            }

            function Ye(n, t) {
                return q((function(r, e) {
                    var i = t;
                    return Ve((function(t, r) {
                            return n(i, t, r)
                        }), (function(n, t) {
                            return i = t, t
                        }))(r).subscribe(e),
                        function() {
                            i = null
                        }
                }))
            }

            function Be(n) {
                return q((function(t, r) {
                    Vn(n).subscribe(z(r, (function() {
                        return r.complete()
                    }), p)), !r.closed && t.subscribe(r)
                }))
            }

            function He(n, t) {
                return void 0 === t && (t = !1), q((function(r, e) {
                    var i = 0;
                    r.subscribe(z(e, (function(r) {
                        var u = n(r, i++);
                        (u || t) && e.next(r), !u && e.complete()
                    })))
                }))
            }

            function Ke(n, t, r) {
                var e = i(n) || t || r ? {
                    next: n,
                    error: t,
                    complete: r
                } : n;
                return e ? q((function(n, t) {
                    var r;
                    null === (r = e.subscribe) || void 0 === r || r.call(e);
                    var i = !0;
                    n.subscribe(z(t, (function(n) {
                        var r;
                        null === (r = e.next) || void 0 === r || r.call(e, n), t.next(n)
                    }), (function() {
                        var n;
                        i = !1, null === (n = e.complete) || void 0 === n || n.call(e), t.complete()
                    }), (function(n) {
                        var r;
                        i = !1, null === (r = e.error) || void 0 === r || r.call(e, n), t.error(n)
                    }), (function() {
                        var n, t;
                        i && (null === (n = e.unsubscribe) || void 0 === n || n.call(e)), null === (t = e.finalize) || void 0 === t || t.call(e)
                    })))
                })) : k
            }

            function Ze(n, t) {
                return q((function(r, e) {
                    var i = null != t ? t : {},
                        u = i.leading,
                        o = void 0 === u || u,
                        c = i.trailing,
                        s = void 0 !== c && c,
                        l = !1,
                        a = null,
                        f = null,
                        d = !1,
                        v = function() {
                            null == f || f.unsubscribe(), f = null, s && (b(), d && e.complete())
                        },
                        h = function() {
                            f = null, d && e.complete()
                        },
                        p = function(t) {
                            return f = Vn(n(t)).subscribe(z(e, v, h))
                        },
                        b = function() {
                            if (l) {
                                l = !1;
                                var n = a;
                                a = null, e.next(n), !d && p(n)
                            }
                        };
                    r.subscribe(z(e, (function(n) {
                        l = !0, a = n, (!f || f.closed) && (o ? b() : p(n))
                    }), (function() {
                        d = !0, (!(s && l && f) || f.closed) && e.complete()
                    })))
                }))
            }

            function Ge(n, t, r) {
                void 0 === t && (t = hn);
                var e = Dt(n, t);
                return Ze((function() {
                    return e
                }), r)
            }

            function Je(n) {
                return void 0 === n && (n = hn), q((function(t, r) {
                    var e = n.now();
                    t.subscribe(z(r, (function(t) {
                        var i = n.now(),
                            u = i - e;
                        e = i, r.next(new Xe(t, u))
                    })))
                }))
            }
            var Xe = function(n, t) {
                this.value = n, this.interval = t
            };

            function Qe(n, t, r) {
                var e, i, u;
                if (r = null != r ? r : pn, ct(n) ? e = n : "number" == typeof n && (i = n), !t) throw new TypeError("No observable provided to switch to");
                if (u = function() {
                        return t
                    }, null == e && null == i) throw new TypeError("No timeout provided.");
                return lt({
                    first: e,
                    each: i,
                    scheduler: r,
                    with: u
                })
            }

            function $e(n) {
                return void 0 === n && (n = J), ft((function(t) {
                    return {
                        value: t,
                        timestamp: n.now()
                    }
                }))
            }

            function ni(n) {
                return q((function(t, r) {
                    var e = new K;
                    r.next(e.asObservable());
                    var i = function(n) {
                        e.error(n), r.error(n)
                    };
                    return t.subscribe(z(r, (function(n) {
                            return null == e ? void 0 : e.next(n)
                        }), (function() {
                            e.complete(), r.complete()
                        }), i)), Vn(n).subscribe(z(r, (function() {
                            e.complete(), r.next(e = new K)
                        }), p, i)),
                        function() {
                            null == e || e.unsubscribe(), e = null
                        }
                }))
            }

            function ti(n, t) {
                void 0 === t && (t = 0);
                var r = t > 0 ? t : n;
                return q((function(t, i) {
                    var u = [new K],
                        o = 0;
                    i.next(u[0].asObservable()), t.subscribe(z(i, (function(t) {
                        var c, s;
                        try {
                            for (var l = (0, e.__values)(u), a = l.next(); !a.done; a = l.next()) a.value.next(t)
                        } catch (n) {
                            c = {
                                error: n
                            }
                        } finally {
                            try {
                                a && !a.done && (s = l.return) && s.call(l)
                            } finally {
                                if (c) throw c.error
                            }
                        }
                        var f = o - n + 1;
                        if (f >= 0 && f % r == 0 && u.shift().complete(), ++o % r == 0) {
                            var d = new K;
                            u.push(d), i.next(d.asObservable())
                        }
                    }), (function() {
                        for (; u.length > 0;) u.shift().complete();
                        i.complete()
                    }), (function(n) {
                        for (; u.length > 0;) u.shift().error(n);
                        i.error(n)
                    }), (function() {
                        u = null
                    })))
                }))
            }

            function ri(n) {
                for (var t, r, e = [], i = 1; i < arguments.length; i++) e[i - 1] = arguments[i];
                var u = null !== (t = kn(e)) && void 0 !== t ? t : hn,
                    o = null !== (r = e[0]) && void 0 !== r ? r : null,
                    l = e[1] || 1 / 0;
                return q((function(t, r) {
                    var e = [],
                        i = !1,
                        a = function(n) {
                            var t = n.window,
                                r = n.subs;
                            t.complete(), r.unsubscribe(), c(e, n), i && f()
                        },
                        f = function() {
                            if (e) {
                                var t = new s;
                                r.add(t);
                                var i = new K,
                                    o = {
                                        window: i,
                                        subs: t,
                                        seen: 0
                                    };
                                e.push(o), r.next(i.asObservable()), Dn(t, u, (function() {
                                    return a(o)
                                }), n)
                            }
                        };
                    null !== o && o >= 0 ? Dn(r, u, f, o, !0) : i = !0, f();
                    var d = function(n) {
                            return e.slice().forEach(n)
                        },
                        v = function(n) {
                            d((function(t) {
                                var r = t.window;
                                return n(r)
                            })), n(r), r.unsubscribe()
                        };
                    return t.subscribe(z(r, (function(n) {
                            d((function(t) {
                                t.window.next(n), l <= ++t.seen && a(t)
                            }))
                        }), (function() {
                            return v((function(n) {
                                return n.complete()
                            }))
                        }), (function(n) {
                            return v((function(t) {
                                return t.error(n)
                            }))
                        }))),
                        function() {
                            e = null
                        }
                }))
            }

            function ei(n, t) {
                return q((function(r, i) {
                    var u = [],
                        o = function(n) {
                            for (; 0 < u.length;) u.shift().error(n);
                            i.error(n)
                        };
                    Vn(n).subscribe(z(i, (function(n) {
                        var r = new K;
                        u.push(r);
                        var e, l = new s;
                        try {
                            e = Vn(t(n))
                        } catch (n) {
                            return void o(n)
                        }
                        i.next(r.asObservable()), l.add(e.subscribe(z(i, (function() {
                            c(u, r), r.complete(), l.unsubscribe()
                        }), p, o)))
                    }), p)), r.subscribe(z(i, (function(n) {
                        var t, r, i = u.slice();
                        try {
                            for (var o = (0, e.__values)(i), c = o.next(); !c.done; c = o.next()) c.value.next(n)
                        } catch (n) {
                            t = {
                                error: n
                            }
                        } finally {
                            try {
                                c && !c.done && (r = o.return) && r.call(o)
                            } finally {
                                if (t) throw t.error
                            }
                        }
                    }), (function() {
                        for (; 0 < u.length;) u.shift().complete();
                        i.complete()
                    }), o, (function() {
                        for (; 0 < u.length;) u.shift().unsubscribe()
                    })))
                }))
            }

            function ii(n) {
                return q((function(t, r) {
                    var e, i, u = function(n) {
                            e.error(n), r.error(n)
                        },
                        o = function() {
                            var t;
                            null == i || i.unsubscribe(), null == e || e.complete(), e = new K, r.next(e.asObservable());
                            try {
                                t = Vn(n())
                            } catch (n) {
                                return void u(n)
                            }
                            t.subscribe(i = z(r, o, o, u))
                        };
                    o(), t.subscribe(z(r, (function(n) {
                        return e.next(n)
                    }), (function() {
                        e.complete(), r.complete()
                    }), u, (function() {
                        null == i || i.unsubscribe(), e = null
                    })))
                }))
            }

            function ui() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                var r = On(n);
                return q((function(t, i) {
                    for (var u = n.length, o = new Array(u), c = n.map((function() {
                            return !1
                        })), s = !1, l = function(t) {
                            Vn(n[t]).subscribe(z(i, (function(n) {
                                o[t] = n, s || c[t] || (c[t] = !0, (s = c.every(k)) && (c = null))
                            }), p))
                        }, a = 0; a < u; a++) l(a);
                    t.subscribe(z(i, (function(n) {
                        if (s) {
                            var t = (0, e.__spreadArray)([n], (0, e.__read)(o));
                            i.next(r ? r.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(t))) : t)
                        }
                    })))
                }))
            }

            function oi(n) {
                return mr(ur, n)
            }

            function ci() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return q((function(t, r) {
                    ur.apply(void 0, (0, e.__spreadArray)([t], (0, e.__read)(n))).subscribe(r)
                }))
            }

            function si() {
                for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
                return ci.apply(void 0, (0, e.__spreadArray)([], (0, e.__read)(n)))
            }
        }
    }
]);
//# sourceMappingURL=6319.4a2fff6db7caebd0.js.map