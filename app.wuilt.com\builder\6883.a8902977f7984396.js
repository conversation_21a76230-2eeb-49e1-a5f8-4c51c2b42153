(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [6883], {
        76883: (t, e) => {
            function n(t, e) {
                for (var n = 0; n < e.length; n++) {
                    var r = e[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, r.key, r)
                }
            }

            function r(t, e, r) {
                return e && n(t.prototype, e), r && n(t, r), t
            }

            function i() {
                return i = Object.assign || function(t) {
                    for (var e = 1; e < arguments.length; e++) {
                        var n = arguments[e];
                        for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (t[r] = n[r])
                    }
                    return t
                }, i.apply(this, arguments)
            }

            function o(t, e) {
                t.prototype = Object.create(e.prototype), t.prototype.constructor = t, t.__proto__ = e
            }

            function u(t) {
                return !(!t || "function" != typeof t.hasOwnProperty || !(t.hasOwnProperty("__ownerID") || t._map && t._map.hasOwnProperty("__ownerID")))
            }

            function a(t, e, n) {
                return Object.keys(t).reduce((function(e, r) {
                    var i = "" + r;
                    return e.has(i) ? e.set(i, n(e.get(i), t[i])) : e
                }), e)
            }
            Object.defineProperty(e, "__esModule", {
                value: !0
            });
            var c = function() {
                    function t(t, e, n) {
                        if (void 0 === e && (e = {}), void 0 === n && (n = {}), !t || "string" != typeof t) throw new Error("Expected a string key for Entity, but found " + t + ".");
                        var r = n,
                            o = r.idAttribute,
                            a = void 0 === o ? "id" : o,
                            c = r.mergeStrategy,
                            f = void 0 === c ? function(t, e) {
                                return i({}, t, e)
                            } : c,
                            s = r.processStrategy,
                            h = void 0 === s ? function(t) {
                                return i({}, t)
                            } : s,
                            l = r.fallbackStrategy,
                            y = void 0 === l ? function(t, e) {} : l;
                        this._key = t, this._getId = "function" == typeof a ? a : function(t) {
                            return function(e) {
                                return u(e) ? e.get(t) : e[t]
                            }
                        }(a), this._idAttribute = a, this._mergeStrategy = f, this._processStrategy = h, this._fallbackStrategy = y, this.define(e)
                    }
                    var e = t.prototype;
                    return e.define = function(t) {
                        this.schema = Object.keys(t).reduce((function(e, n) {
                            var r, o = t[n];
                            return i({}, e, ((r = {})[n] = o, r))
                        }), this.schema || {})
                    }, e.getId = function(t, e, n) {
                        return this._getId(t, e, n)
                    }, e.merge = function(t, e) {
                        return this._mergeStrategy(t, e)
                    }, e.fallback = function(t, e) {
                        return this._fallbackStrategy(t, e)
                    }, e.normalize = function(t, e, n, r, i, o) {
                        var u = this,
                            a = this.getId(t, e, n),
                            c = this.key;
                        if (c in o || (o[c] = {}), a in o[c] || (o[c][a] = []), o[c][a].some((function(e) {
                                return e === t
                            }))) return a;
                        o[c][a].push(t);
                        var f = this._processStrategy(t, e, n);
                        return Object.keys(this.schema).forEach((function(e) {
                            if (f.hasOwnProperty(e) && "object" == typeof f[e]) {
                                var n = u.schema[e],
                                    a = "function" == typeof n ? n(t) : n;
                                f[e] = r(f[e], f, e, a, i, o)
                            }
                        })), i(this, f, t, e, n), a
                    }, e.denormalize = function(t, e) {
                        var n = this;
                        return u(t) ? a(this.schema, t, e) : (Object.keys(this.schema).forEach((function(r) {
                            if (t.hasOwnProperty(r)) {
                                var i = n.schema[r];
                                t[r] = e(t[r], i)
                            }
                        })), t)
                    }, r(t, [{
                        key: "key",
                        get: function() {
                            return this._key
                        }
                    }, {
                        key: "idAttribute",
                        get: function() {
                            return this._idAttribute
                        }
                    }]), t
                }(),
                f = function() {
                    function t(t, e) {
                        e && (this._schemaAttribute = "string" == typeof e ? function(t) {
                            return t[e]
                        } : e), this.define(t)
                    }
                    var e = t.prototype;
                    return e.define = function(t) {
                        this.schema = t
                    }, e.getSchemaAttribute = function(t, e, n) {
                        return !this.isSingleSchema && this._schemaAttribute(t, e, n)
                    }, e.inferSchema = function(t, e, n) {
                        if (this.isSingleSchema) return this.schema;
                        var r = this.getSchemaAttribute(t, e, n);
                        return this.schema[r]
                    }, e.normalizeValue = function(t, e, n, r, i, o) {
                        var u = this.inferSchema(t, e, n);
                        if (!u) return t;
                        var a = r(t, e, n, u, i, o);
                        return this.isSingleSchema || null == a ? a : {
                            id: a,
                            schema: this.getSchemaAttribute(t, e, n)
                        }
                    }, e.denormalizeValue = function(t, e) {
                        var n = u(t) ? t.get("schema") : t.schema;
                        return this.isSingleSchema || n ? e((this.isSingleSchema ? void 0 : u(t) ? t.get("id") : t.id) || t, this.isSingleSchema ? this.schema : this.schema[n]) : t
                    }, r(t, [{
                        key: "isSingleSchema",
                        get: function() {
                            return !this._schemaAttribute
                        }
                    }]), t
                }(),
                s = function(t) {
                    function e(e, n) {
                        if (!n) throw new Error('Expected option "schemaAttribute" not found on UnionSchema.');
                        return t.call(this, e, n) || this
                    }
                    o(e, t);
                    var n = e.prototype;
                    return n.normalize = function(t, e, n, r, i, o) {
                        return this.normalizeValue(t, e, n, r, i, o)
                    }, n.denormalize = function(t, e) {
                        return this.denormalizeValue(t, e)
                    }, e
                }(f),
                h = function(t) {
                    function e() {
                        return t.apply(this, arguments) || this
                    }
                    o(e, t);
                    var n = e.prototype;
                    return n.normalize = function(t, e, n, r, o, u) {
                        var a = this;
                        return Object.keys(t).reduce((function(e, n, c) {
                            var f, s = t[n];
                            return null != s ? i({}, e, ((f = {})[n] = a.normalizeValue(s, t, n, r, o, u), f)) : e
                        }), {})
                    }, n.denormalize = function(t, e) {
                        var n = this;
                        return Object.keys(t).reduce((function(r, o) {
                            var u, a = t[o];
                            return i({}, r, ((u = {})[o] = n.denormalizeValue(a, e), u))
                        }), {})
                    }, e
                }(f),
                l = function(t) {
                    if (Array.isArray(t) && t.length > 1) throw new Error("Expected schema definition to be a single schema, but found " + t.length + ".");
                    return t[0]
                },
                y = function(t) {
                    return Array.isArray(t) ? t : Object.keys(t).map((function(e) {
                        return t[e]
                    }))
                },
                m = function(t, e, n, r, i, o, u) {
                    return t = l(t), y(e).map((function(e, a) {
                        return i(e, n, r, t, o, u)
                    }))
                },
                p = function(t, e, n) {
                    return t = l(t), e && e.map ? e.map((function(e) {
                        return n(e, t)
                    })) : e
                },
                d = function(t) {
                    function e() {
                        return t.apply(this, arguments) || this
                    }
                    o(e, t);
                    var n = e.prototype;
                    return n.normalize = function(t, e, n, r, i, o) {
                        var u = this;
                        return y(t).map((function(t, a) {
                            return u.normalizeValue(t, e, n, r, i, o)
                        })).filter((function(t) {
                            return null != t
                        }))
                    }, n.denormalize = function(t, e) {
                        var n = this;
                        return t && t.map ? t.map((function(t) {
                            return n.denormalizeValue(t, e)
                        })) : t
                    }, e
                }(f),
                v = function(t, e, n, r, o, u, a) {
                    var c = i({}, e);
                    return Object.keys(t).forEach((function(n) {
                        var r = t[n],
                            i = "function" == typeof r ? r(e) : r,
                            f = o(e[n], e, n, i, u, a);
                        null == f ? delete c[n] : c[n] = f
                    })), c
                },
                g = function(t, e, n) {
                    if (u(e)) return a(t, e, n);
                    var r = i({}, e);
                    return Object.keys(t).forEach((function(e) {
                        null != r[e] && (r[e] = n(r[e], t[e]))
                    })), r
                },
                b = function() {
                    function t(t) {
                        this.define(t)
                    }
                    var e = t.prototype;
                    return e.define = function(t) {
                        this.schema = Object.keys(t).reduce((function(e, n) {
                            var r, o = t[n];
                            return i({}, e, ((r = {})[n] = o, r))
                        }), this.schema || {})
                    }, e.normalize = function() {
                        for (var t = arguments.length, e = new Array(t), n = 0; n < t; n++) e[n] = arguments[n];
                        return v.apply(void 0, [this.schema].concat(e))
                    }, e.denormalize = function() {
                        for (var t = arguments.length, e = new Array(t), n = 0; n < t; n++) e[n] = arguments[n];
                        return g.apply(void 0, [this.schema].concat(e))
                    }, t
                }(),
                k = function t(e, n, r, i, o, u) {
                    return "object" == typeof e && e ? "object" != typeof i || i.normalize && "function" == typeof i.normalize ? i.normalize(e, n, r, t, o, u) : (Array.isArray(i) ? m : v)(i, e, n, r, t, o, u) : e
                },
                S = {
                    Array: d,
                    Entity: c,
                    Object: b,
                    Union: s,
                    Values: h
                },
                z = function(t) {
                    var e = {},
                        n = _(t);
                    return function t(r, o) {
                        return "object" != typeof o || o.denormalize && "function" == typeof o.denormalize ? null == r ? r : o instanceof c ? function(t, e, n, r, o) {
                            var a = r(t, e);
                            if (void 0 === a && e instanceof c && (a = e.fallback(t, e)), "object" != typeof a || null === a) return a;
                            if (o[e.key] || (o[e.key] = {}), !o[e.key][t]) {
                                var f = u(a) ? a : i({}, a);
                                o[e.key][t] = f, o[e.key][t] = e.denormalize(f, n)
                            }
                            return o[e.key][t]
                        }(r, o, t, n, e) : o.denormalize(r, t) : (Array.isArray(o) ? p : g)(o, r, t)
                    }
                },
                _ = function(t) {
                    var e = u(t);
                    return function(n, r) {
                        var i = r.key;
                        return "object" == typeof n ? n : e ? t.getIn([i, n.toString()]) : t[i] && t[i][n]
                    }
                };
            e.denormalize = function(t, e, n) {
                if (void 0 !== t) return z(n)(t, e)
            }, e.normalize = function(t, e) {
                if (!t || "object" != typeof t) throw new Error('Unexpected input given to normalize. Expected type to be "object", found "' + (null === t ? "null" : typeof t) + '".');
                var n = {},
                    r = function(t) {
                        return function(e, n, r, i, o) {
                            var u = e.key,
                                a = e.getId(r, i, o);
                            u in t || (t[u] = {});
                            var c = t[u][a];
                            t[u][a] = c ? e.merge(c, n) : n
                        }
                    }(n);
                return {
                    entities: n,
                    result: k(t, t, null, e, r, {})
                }
            }, e.schema = S
        }
    }
]);
//# sourceMappingURL=6883.a8902977f7984396.js.map