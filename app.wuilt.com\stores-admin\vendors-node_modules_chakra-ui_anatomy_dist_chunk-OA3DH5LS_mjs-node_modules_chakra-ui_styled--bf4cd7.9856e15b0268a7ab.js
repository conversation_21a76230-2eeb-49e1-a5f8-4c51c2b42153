(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_chakra-ui_anatomy_dist_chunk-OA3DH5LS_mjs-node_modules_chakra-ui_styled--bf4cd7"], {

        /***/
        "../../node_modules/lodash.mergewith/index.js":
            /***/
            ((module, exports, __webpack_require__) => {

                /* module decorator */
                module = __webpack_require__.nmd(module);
                /**
                 * Lodash (Custom Build) <https://lodash.com/>
                 * Build: `lodash modularize exports="npm" -o ./`
                 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
                 * Released under MIT license <https://lodash.com/license>
                 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
                 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
                 */

                /** Used as the size to enable large array optimizations. */
                var LARGE_ARRAY_SIZE = 200;

                /** Used to stand-in for `undefined` hash values. */
                var HASH_UNDEFINED = '__lodash_hash_undefined__';

                /** Used to detect hot functions by number of calls within a span of milliseconds. */
                var HOT_COUNT = 800,
                    HOT_SPAN = 16;

                /** Used as references for various `Number` constants. */
                var MAX_SAFE_INTEGER = 9007199254740991;

                /** `Object#toString` result references. */
                var argsTag = '[object Arguments]',
                    arrayTag = '[object Array]',
                    asyncTag = '[object AsyncFunction]',
                    boolTag = '[object Boolean]',
                    dateTag = '[object Date]',
                    errorTag = '[object Error]',
                    funcTag = '[object Function]',
                    genTag = '[object GeneratorFunction]',
                    mapTag = '[object Map]',
                    numberTag = '[object Number]',
                    nullTag = '[object Null]',
                    objectTag = '[object Object]',
                    proxyTag = '[object Proxy]',
                    regexpTag = '[object RegExp]',
                    setTag = '[object Set]',
                    stringTag = '[object String]',
                    undefinedTag = '[object Undefined]',
                    weakMapTag = '[object WeakMap]';

                var arrayBufferTag = '[object ArrayBuffer]',
                    dataViewTag = '[object DataView]',
                    float32Tag = '[object Float32Array]',
                    float64Tag = '[object Float64Array]',
                    int8Tag = '[object Int8Array]',
                    int16Tag = '[object Int16Array]',
                    int32Tag = '[object Int32Array]',
                    uint8Tag = '[object Uint8Array]',
                    uint8ClampedTag = '[object Uint8ClampedArray]',
                    uint16Tag = '[object Uint16Array]',
                    uint32Tag = '[object Uint32Array]';

                /**
                 * Used to match `RegExp`
                 * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).
                 */
                var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;

                /** Used to detect host constructors (Safari). */
                var reIsHostCtor = /^\[object .+?Constructor\]$/;

                /** Used to detect unsigned integer values. */
                var reIsUint = /^(?:0|[1-9]\d*)$/;

                /** Used to identify `toStringTag` values of typed arrays. */
                var typedArrayTags = {};
                typedArrayTags[float32Tag] = typedArrayTags[float64Tag] =
                    typedArrayTags[int8Tag] = typedArrayTags[int16Tag] =
                    typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] =
                    typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] =
                    typedArrayTags[uint32Tag] = true;
                typedArrayTags[argsTag] = typedArrayTags[arrayTag] =
                    typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] =
                    typedArrayTags[dataViewTag] = typedArrayTags[dateTag] =
                    typedArrayTags[errorTag] = typedArrayTags[funcTag] =
                    typedArrayTags[mapTag] = typedArrayTags[numberTag] =
                    typedArrayTags[objectTag] = typedArrayTags[regexpTag] =
                    typedArrayTags[setTag] = typedArrayTags[stringTag] =
                    typedArrayTags[weakMapTag] = false;

                /** Detect free variable `global` from Node.js. */
                var freeGlobal = typeof __webpack_require__.g == 'object' && __webpack_require__.g && __webpack_require__.g.Object === Object && __webpack_require__.g;

                /** Detect free variable `self`. */
                var freeSelf = typeof self == 'object' && self && self.Object === Object && self;

                /** Used as a reference to the global object. */
                var root = freeGlobal || freeSelf || Function('return this')();

                /** Detect free variable `exports`. */
                var freeExports = true && exports && !exports.nodeType && exports;

                /** Detect free variable `module`. */
                var freeModule = freeExports && "object" == 'object' && module && !module.nodeType && module;

                /** Detect the popular CommonJS extension `module.exports`. */
                var moduleExports = freeModule && freeModule.exports === freeExports;

                /** Detect free variable `process` from Node.js. */
                var freeProcess = moduleExports && freeGlobal.process;

                /** Used to access faster Node.js helpers. */
                var nodeUtil = (function() {
                    try {
                        // Use `util.types` for Node.js 10+.
                        var types = freeModule && freeModule.require && freeModule.require('util').types;

                        if (types) {
                            return types;
                        }

                        // Legacy `process.binding('util')` for Node.js < 10.
                        return freeProcess && freeProcess.binding && freeProcess.binding('util');
                    } catch (e) {}
                }());

                /* Node.js helper references. */
                var nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;

                /**
                 * A faster alternative to `Function#apply`, this function invokes `func`
                 * with the `this` binding of `thisArg` and the arguments of `args`.
                 *
                 * @private
                 * @param {Function} func The function to invoke.
                 * @param {*} thisArg The `this` binding of `func`.
                 * @param {Array} args The arguments to invoke `func` with.
                 * @returns {*} Returns the result of `func`.
                 */
                function apply(func, thisArg, args) {
                    switch (args.length) {
                        case 0:
                            return func.call(thisArg);
                        case 1:
                            return func.call(thisArg, args[0]);
                        case 2:
                            return func.call(thisArg, args[0], args[1]);
                        case 3:
                            return func.call(thisArg, args[0], args[1], args[2]);
                    }
                    return func.apply(thisArg, args);
                }

                /**
                 * The base implementation of `_.times` without support for iteratee shorthands
                 * or max array length checks.
                 *
                 * @private
                 * @param {number} n The number of times to invoke `iteratee`.
                 * @param {Function} iteratee The function invoked per iteration.
                 * @returns {Array} Returns the array of results.
                 */
                function baseTimes(n, iteratee) {
                    var index = -1,
                        result = Array(n);

                    while (++index < n) {
                        result[index] = iteratee(index);
                    }
                    return result;
                }

                /**
                 * The base implementation of `_.unary` without support for storing metadata.
                 *
                 * @private
                 * @param {Function} func The function to cap arguments for.
                 * @returns {Function} Returns the new capped function.
                 */
                function baseUnary(func) {
                    return function(value) {
                        return func(value);
                    };
                }

                /**
                 * Gets the value at `key` of `object`.
                 *
                 * @private
                 * @param {Object} [object] The object to query.
                 * @param {string} key The key of the property to get.
                 * @returns {*} Returns the property value.
                 */
                function getValue(object, key) {
                    return object == null ? undefined : object[key];
                }

                /**
                 * Creates a unary function that invokes `func` with its argument transformed.
                 *
                 * @private
                 * @param {Function} func The function to wrap.
                 * @param {Function} transform The argument transform.
                 * @returns {Function} Returns the new function.
                 */
                function overArg(func, transform) {
                    return function(arg) {
                        return func(transform(arg));
                    };
                }

                /** Used for built-in method references. */
                var arrayProto = Array.prototype,
                    funcProto = Function.prototype,
                    objectProto = Object.prototype;

                /** Used to detect overreaching core-js shims. */
                var coreJsData = root['__core-js_shared__'];

                /** Used to resolve the decompiled source of functions. */
                var funcToString = funcProto.toString;

                /** Used to check objects for own properties. */
                var hasOwnProperty = objectProto.hasOwnProperty;

                /** Used to detect methods masquerading as native. */
                var maskSrcKey = (function() {
                    var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');
                    return uid ? ('Symbol(src)_1.' + uid) : '';
                }());

                /**
                 * Used to resolve the
                 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
                 * of values.
                 */
                var nativeObjectToString = objectProto.toString;

                /** Used to infer the `Object` constructor. */
                var objectCtorString = funcToString.call(Object);

                /** Used to detect if a method is native. */
                var reIsNative = RegExp('^' +
                    funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\$&')
                    .replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, '$1.*?') + '$'
                );

                /** Built-in value references. */
                var Buffer = moduleExports ? root.Buffer : undefined,
                    Symbol = root.Symbol,
                    Uint8Array = root.Uint8Array,
                    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined,
                    getPrototype = overArg(Object.getPrototypeOf, Object),
                    objectCreate = Object.create,
                    propertyIsEnumerable = objectProto.propertyIsEnumerable,
                    splice = arrayProto.splice,
                    symToStringTag = Symbol ? Symbol.toStringTag : undefined;

                var defineProperty = (function() {
                    try {
                        var func = getNative(Object, 'defineProperty');
                        func({}, '', {});
                        return func;
                    } catch (e) {}
                }());

                /* Built-in method references for those with the same name as other `lodash` methods. */
                var nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined,
                    nativeMax = Math.max,
                    nativeNow = Date.now;

                /* Built-in method references that are verified to be native. */
                var Map = getNative(root, 'Map'),
                    nativeCreate = getNative(Object, 'create');

                /**
                 * The base implementation of `_.create` without support for assigning
                 * properties to the created object.
                 *
                 * @private
                 * @param {Object} proto The object to inherit from.
                 * @returns {Object} Returns the new object.
                 */
                var baseCreate = (function() {
                    function object() {}
                    return function(proto) {
                        if (!isObject(proto)) {
                            return {};
                        }
                        if (objectCreate) {
                            return objectCreate(proto);
                        }
                        object.prototype = proto;
                        var result = new object;
                        object.prototype = undefined;
                        return result;
                    };
                }());

                /**
                 * Creates a hash object.
                 *
                 * @private
                 * @constructor
                 * @param {Array} [entries] The key-value pairs to cache.
                 */
                function Hash(entries) {
                    var index = -1,
                        length = entries == null ? 0 : entries.length;

                    this.clear();
                    while (++index < length) {
                        var entry = entries[index];
                        this.set(entry[0], entry[1]);
                    }
                }

                /**
                 * Removes all key-value entries from the hash.
                 *
                 * @private
                 * @name clear
                 * @memberOf Hash
                 */
                function hashClear() {
                    this.__data__ = nativeCreate ? nativeCreate(null) : {};
                    this.size = 0;
                }

                /**
                 * Removes `key` and its value from the hash.
                 *
                 * @private
                 * @name delete
                 * @memberOf Hash
                 * @param {Object} hash The hash to modify.
                 * @param {string} key The key of the value to remove.
                 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
                 */
                function hashDelete(key) {
                    var result = this.has(key) && delete this.__data__[key];
                    this.size -= result ? 1 : 0;
                    return result;
                }

                /**
                 * Gets the hash value for `key`.
                 *
                 * @private
                 * @name get
                 * @memberOf Hash
                 * @param {string} key The key of the value to get.
                 * @returns {*} Returns the entry value.
                 */
                function hashGet(key) {
                    var data = this.__data__;
                    if (nativeCreate) {
                        var result = data[key];
                        return result === HASH_UNDEFINED ? undefined : result;
                    }
                    return hasOwnProperty.call(data, key) ? data[key] : undefined;
                }

                /**
                 * Checks if a hash value for `key` exists.
                 *
                 * @private
                 * @name has
                 * @memberOf Hash
                 * @param {string} key The key of the entry to check.
                 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
                 */
                function hashHas(key) {
                    var data = this.__data__;
                    return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);
                }

                /**
                 * Sets the hash `key` to `value`.
                 *
                 * @private
                 * @name set
                 * @memberOf Hash
                 * @param {string} key The key of the value to set.
                 * @param {*} value The value to set.
                 * @returns {Object} Returns the hash instance.
                 */
                function hashSet(key, value) {
                    var data = this.__data__;
                    this.size += this.has(key) ? 0 : 1;
                    data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;
                    return this;
                }

                // Add methods to `Hash`.
                Hash.prototype.clear = hashClear;
                Hash.prototype['delete'] = hashDelete;
                Hash.prototype.get = hashGet;
                Hash.prototype.has = hashHas;
                Hash.prototype.set = hashSet;

                /**
                 * Creates an list cache object.
                 *
                 * @private
                 * @constructor
                 * @param {Array} [entries] The key-value pairs to cache.
                 */
                function ListCache(entries) {
                    var index = -1,
                        length = entries == null ? 0 : entries.length;

                    this.clear();
                    while (++index < length) {
                        var entry = entries[index];
                        this.set(entry[0], entry[1]);
                    }
                }

                /**
                 * Removes all key-value entries from the list cache.
                 *
                 * @private
                 * @name clear
                 * @memberOf ListCache
                 */
                function listCacheClear() {
                    this.__data__ = [];
                    this.size = 0;
                }

                /**
                 * Removes `key` and its value from the list cache.
                 *
                 * @private
                 * @name delete
                 * @memberOf ListCache
                 * @param {string} key The key of the value to remove.
                 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
                 */
                function listCacheDelete(key) {
                    var data = this.__data__,
                        index = assocIndexOf(data, key);

                    if (index < 0) {
                        return false;
                    }
                    var lastIndex = data.length - 1;
                    if (index == lastIndex) {
                        data.pop();
                    } else {
                        splice.call(data, index, 1);
                    }
                    --this.size;
                    return true;
                }

                /**
                 * Gets the list cache value for `key`.
                 *
                 * @private
                 * @name get
                 * @memberOf ListCache
                 * @param {string} key The key of the value to get.
                 * @returns {*} Returns the entry value.
                 */
                function listCacheGet(key) {
                    var data = this.__data__,
                        index = assocIndexOf(data, key);

                    return index < 0 ? undefined : data[index][1];
                }

                /**
                 * Checks if a list cache value for `key` exists.
                 *
                 * @private
                 * @name has
                 * @memberOf ListCache
                 * @param {string} key The key of the entry to check.
                 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
                 */
                function listCacheHas(key) {
                    return assocIndexOf(this.__data__, key) > -1;
                }

                /**
                 * Sets the list cache `key` to `value`.
                 *
                 * @private
                 * @name set
                 * @memberOf ListCache
                 * @param {string} key The key of the value to set.
                 * @param {*} value The value to set.
                 * @returns {Object} Returns the list cache instance.
                 */
                function listCacheSet(key, value) {
                    var data = this.__data__,
                        index = assocIndexOf(data, key);

                    if (index < 0) {
                        ++this.size;
                        data.push([key, value]);
                    } else {
                        data[index][1] = value;
                    }
                    return this;
                }

                // Add methods to `ListCache`.
                ListCache.prototype.clear = listCacheClear;
                ListCache.prototype['delete'] = listCacheDelete;
                ListCache.prototype.get = listCacheGet;
                ListCache.prototype.has = listCacheHas;
                ListCache.prototype.set = listCacheSet;

                /**
                 * Creates a map cache object to store key-value pairs.
                 *
                 * @private
                 * @constructor
                 * @param {Array} [entries] The key-value pairs to cache.
                 */
                function MapCache(entries) {
                    var index = -1,
                        length = entries == null ? 0 : entries.length;

                    this.clear();
                    while (++index < length) {
                        var entry = entries[index];
                        this.set(entry[0], entry[1]);
                    }
                }

                /**
                 * Removes all key-value entries from the map.
                 *
                 * @private
                 * @name clear
                 * @memberOf MapCache
                 */
                function mapCacheClear() {
                    this.size = 0;
                    this.__data__ = {
                        'hash': new Hash,
                        'map': new(Map || ListCache),
                        'string': new Hash
                    };
                }

                /**
                 * Removes `key` and its value from the map.
                 *
                 * @private
                 * @name delete
                 * @memberOf MapCache
                 * @param {string} key The key of the value to remove.
                 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
                 */
                function mapCacheDelete(key) {
                    var result = getMapData(this, key)['delete'](key);
                    this.size -= result ? 1 : 0;
                    return result;
                }

                /**
                 * Gets the map value for `key`.
                 *
                 * @private
                 * @name get
                 * @memberOf MapCache
                 * @param {string} key The key of the value to get.
                 * @returns {*} Returns the entry value.
                 */
                function mapCacheGet(key) {
                    return getMapData(this, key).get(key);
                }

                /**
                 * Checks if a map value for `key` exists.
                 *
                 * @private
                 * @name has
                 * @memberOf MapCache
                 * @param {string} key The key of the entry to check.
                 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
                 */
                function mapCacheHas(key) {
                    return getMapData(this, key).has(key);
                }

                /**
                 * Sets the map `key` to `value`.
                 *
                 * @private
                 * @name set
                 * @memberOf MapCache
                 * @param {string} key The key of the value to set.
                 * @param {*} value The value to set.
                 * @returns {Object} Returns the map cache instance.
                 */
                function mapCacheSet(key, value) {
                    var data = getMapData(this, key),
                        size = data.size;

                    data.set(key, value);
                    this.size += data.size == size ? 0 : 1;
                    return this;
                }

                // Add methods to `MapCache`.
                MapCache.prototype.clear = mapCacheClear;
                MapCache.prototype['delete'] = mapCacheDelete;
                MapCache.prototype.get = mapCacheGet;
                MapCache.prototype.has = mapCacheHas;
                MapCache.prototype.set = mapCacheSet;

                /**
                 * Creates a stack cache object to store key-value pairs.
                 *
                 * @private
                 * @constructor
                 * @param {Array} [entries] The key-value pairs to cache.
                 */
                function Stack(entries) {
                    var data = this.__data__ = new ListCache(entries);
                    this.size = data.size;
                }

                /**
                 * Removes all key-value entries from the stack.
                 *
                 * @private
                 * @name clear
                 * @memberOf Stack
                 */
                function stackClear() {
                    this.__data__ = new ListCache;
                    this.size = 0;
                }

                /**
                 * Removes `key` and its value from the stack.
                 *
                 * @private
                 * @name delete
                 * @memberOf Stack
                 * @param {string} key The key of the value to remove.
                 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
                 */
                function stackDelete(key) {
                    var data = this.__data__,
                        result = data['delete'](key);

                    this.size = data.size;
                    return result;
                }

                /**
                 * Gets the stack value for `key`.
                 *
                 * @private
                 * @name get
                 * @memberOf Stack
                 * @param {string} key The key of the value to get.
                 * @returns {*} Returns the entry value.
                 */
                function stackGet(key) {
                    return this.__data__.get(key);
                }

                /**
                 * Checks if a stack value for `key` exists.
                 *
                 * @private
                 * @name has
                 * @memberOf Stack
                 * @param {string} key The key of the entry to check.
                 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
                 */
                function stackHas(key) {
                    return this.__data__.has(key);
                }

                /**
                 * Sets the stack `key` to `value`.
                 *
                 * @private
                 * @name set
                 * @memberOf Stack
                 * @param {string} key The key of the value to set.
                 * @param {*} value The value to set.
                 * @returns {Object} Returns the stack cache instance.
                 */
                function stackSet(key, value) {
                    var data = this.__data__;
                    if (data instanceof ListCache) {
                        var pairs = data.__data__;
                        if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {
                            pairs.push([key, value]);
                            this.size = ++data.size;
                            return this;
                        }
                        data = this.__data__ = new MapCache(pairs);
                    }
                    data.set(key, value);
                    this.size = data.size;
                    return this;
                }

                // Add methods to `Stack`.
                Stack.prototype.clear = stackClear;
                Stack.prototype['delete'] = stackDelete;
                Stack.prototype.get = stackGet;
                Stack.prototype.has = stackHas;
                Stack.prototype.set = stackSet;

                /**
                 * Creates an array of the enumerable property names of the array-like `value`.
                 *
                 * @private
                 * @param {*} value The value to query.
                 * @param {boolean} inherited Specify returning inherited property names.
                 * @returns {Array} Returns the array of property names.
                 */
                function arrayLikeKeys(value, inherited) {
                    var isArr = isArray(value),
                        isArg = !isArr && isArguments(value),
                        isBuff = !isArr && !isArg && isBuffer(value),
                        isType = !isArr && !isArg && !isBuff && isTypedArray(value),
                        skipIndexes = isArr || isArg || isBuff || isType,
                        result = skipIndexes ? baseTimes(value.length, String) : [],
                        length = result.length;

                    for (var key in value) {
                        if ((inherited || hasOwnProperty.call(value, key)) &&
                            !(skipIndexes && (
                                // Safari 9 has enumerable `arguments.length` in strict mode.
                                key == 'length' ||
                                // Node.js 0.10 has enumerable non-index properties on buffers.
                                (isBuff && (key == 'offset' || key == 'parent')) ||
                                // PhantomJS 2 has enumerable non-index properties on typed arrays.
                                (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||
                                // Skip index properties.
                                isIndex(key, length)
                            ))) {
                            result.push(key);
                        }
                    }
                    return result;
                }

                /**
                 * This function is like `assignValue` except that it doesn't assign
                 * `undefined` values.
                 *
                 * @private
                 * @param {Object} object The object to modify.
                 * @param {string} key The key of the property to assign.
                 * @param {*} value The value to assign.
                 */
                function assignMergeValue(object, key, value) {
                    if ((value !== undefined && !eq(object[key], value)) ||
                        (value === undefined && !(key in object))) {
                        baseAssignValue(object, key, value);
                    }
                }

                /**
                 * Assigns `value` to `key` of `object` if the existing value is not equivalent
                 * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
                 * for equality comparisons.
                 *
                 * @private
                 * @param {Object} object The object to modify.
                 * @param {string} key The key of the property to assign.
                 * @param {*} value The value to assign.
                 */
                function assignValue(object, key, value) {
                    var objValue = object[key];
                    if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||
                        (value === undefined && !(key in object))) {
                        baseAssignValue(object, key, value);
                    }
                }

                /**
                 * Gets the index at which the `key` is found in `array` of key-value pairs.
                 *
                 * @private
                 * @param {Array} array The array to inspect.
                 * @param {*} key The key to search for.
                 * @returns {number} Returns the index of the matched value, else `-1`.
                 */
                function assocIndexOf(array, key) {
                    var length = array.length;
                    while (length--) {
                        if (eq(array[length][0], key)) {
                            return length;
                        }
                    }
                    return -1;
                }

                /**
                 * The base implementation of `assignValue` and `assignMergeValue` without
                 * value checks.
                 *
                 * @private
                 * @param {Object} object The object to modify.
                 * @param {string} key The key of the property to assign.
                 * @param {*} value The value to assign.
                 */
                function baseAssignValue(object, key, value) {
                    if (key == '__proto__' && defineProperty) {
                        defineProperty(object, key, {
                            'configurable': true,
                            'enumerable': true,
                            'value': value,
                            'writable': true
                        });
                    } else {
                        object[key] = value;
                    }
                }

                /**
                 * The base implementation of `baseForOwn` which iterates over `object`
                 * properties returned by `keysFunc` and invokes `iteratee` for each property.
                 * Iteratee functions may exit iteration early by explicitly returning `false`.
                 *
                 * @private
                 * @param {Object} object The object to iterate over.
                 * @param {Function} iteratee The function invoked per iteration.
                 * @param {Function} keysFunc The function to get the keys of `object`.
                 * @returns {Object} Returns `object`.
                 */
                var baseFor = createBaseFor();

                /**
                 * The base implementation of `getTag` without fallbacks for buggy environments.
                 *
                 * @private
                 * @param {*} value The value to query.
                 * @returns {string} Returns the `toStringTag`.
                 */
                function baseGetTag(value) {
                    if (value == null) {
                        return value === undefined ? undefinedTag : nullTag;
                    }
                    return (symToStringTag && symToStringTag in Object(value)) ?
                        getRawTag(value) :
                        objectToString(value);
                }

                /**
                 * The base implementation of `_.isArguments`.
                 *
                 * @private
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is an `arguments` object,
                 */
                function baseIsArguments(value) {
                    return isObjectLike(value) && baseGetTag(value) == argsTag;
                }

                /**
                 * The base implementation of `_.isNative` without bad shim checks.
                 *
                 * @private
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a native function,
                 *  else `false`.
                 */
                function baseIsNative(value) {
                    if (!isObject(value) || isMasked(value)) {
                        return false;
                    }
                    var pattern = isFunction(value) ? reIsNative : reIsHostCtor;
                    return pattern.test(toSource(value));
                }

                /**
                 * The base implementation of `_.isTypedArray` without Node.js optimizations.
                 *
                 * @private
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.
                 */
                function baseIsTypedArray(value) {
                    return isObjectLike(value) &&
                        isLength(value.length) && !!typedArrayTags[baseGetTag(value)];
                }

                /**
                 * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.
                 *
                 * @private
                 * @param {Object} object The object to query.
                 * @returns {Array} Returns the array of property names.
                 */
                function baseKeysIn(object) {
                    if (!isObject(object)) {
                        return nativeKeysIn(object);
                    }
                    var isProto = isPrototype(object),
                        result = [];

                    for (var key in object) {
                        if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {
                            result.push(key);
                        }
                    }
                    return result;
                }

                /**
                 * The base implementation of `_.merge` without support for multiple sources.
                 *
                 * @private
                 * @param {Object} object The destination object.
                 * @param {Object} source The source object.
                 * @param {number} srcIndex The index of `source`.
                 * @param {Function} [customizer] The function to customize merged values.
                 * @param {Object} [stack] Tracks traversed source values and their merged
                 *  counterparts.
                 */
                function baseMerge(object, source, srcIndex, customizer, stack) {
                    if (object === source) {
                        return;
                    }
                    baseFor(source, function(srcValue, key) {
                        stack || (stack = new Stack);
                        if (isObject(srcValue)) {
                            baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);
                        } else {
                            var newValue = customizer ?
                                customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack) :
                                undefined;

                            if (newValue === undefined) {
                                newValue = srcValue;
                            }
                            assignMergeValue(object, key, newValue);
                        }
                    }, keysIn);
                }

                /**
                 * A specialized version of `baseMerge` for arrays and objects which performs
                 * deep merges and tracks traversed objects enabling objects with circular
                 * references to be merged.
                 *
                 * @private
                 * @param {Object} object The destination object.
                 * @param {Object} source The source object.
                 * @param {string} key The key of the value to merge.
                 * @param {number} srcIndex The index of `source`.
                 * @param {Function} mergeFunc The function to merge values.
                 * @param {Function} [customizer] The function to customize assigned values.
                 * @param {Object} [stack] Tracks traversed source values and their merged
                 *  counterparts.
                 */
                function baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {
                    var objValue = safeGet(object, key),
                        srcValue = safeGet(source, key),
                        stacked = stack.get(srcValue);

                    if (stacked) {
                        assignMergeValue(object, key, stacked);
                        return;
                    }
                    var newValue = customizer ?
                        customizer(objValue, srcValue, (key + ''), object, source, stack) :
                        undefined;

                    var isCommon = newValue === undefined;

                    if (isCommon) {
                        var isArr = isArray(srcValue),
                            isBuff = !isArr && isBuffer(srcValue),
                            isTyped = !isArr && !isBuff && isTypedArray(srcValue);

                        newValue = srcValue;
                        if (isArr || isBuff || isTyped) {
                            if (isArray(objValue)) {
                                newValue = objValue;
                            } else if (isArrayLikeObject(objValue)) {
                                newValue = copyArray(objValue);
                            } else if (isBuff) {
                                isCommon = false;
                                newValue = cloneBuffer(srcValue, true);
                            } else if (isTyped) {
                                isCommon = false;
                                newValue = cloneTypedArray(srcValue, true);
                            } else {
                                newValue = [];
                            }
                        } else if (isPlainObject(srcValue) || isArguments(srcValue)) {
                            newValue = objValue;
                            if (isArguments(objValue)) {
                                newValue = toPlainObject(objValue);
                            } else if (!isObject(objValue) || isFunction(objValue)) {
                                newValue = initCloneObject(srcValue);
                            }
                        } else {
                            isCommon = false;
                        }
                    }
                    if (isCommon) {
                        // Recursively merge objects and arrays (susceptible to call stack limits).
                        stack.set(srcValue, newValue);
                        mergeFunc(newValue, srcValue, srcIndex, customizer, stack);
                        stack['delete'](srcValue);
                    }
                    assignMergeValue(object, key, newValue);
                }

                /**
                 * The base implementation of `_.rest` which doesn't validate or coerce arguments.
                 *
                 * @private
                 * @param {Function} func The function to apply a rest parameter to.
                 * @param {number} [start=func.length-1] The start position of the rest parameter.
                 * @returns {Function} Returns the new function.
                 */
                function baseRest(func, start) {
                    return setToString(overRest(func, start, identity), func + '');
                }

                /**
                 * The base implementation of `setToString` without support for hot loop shorting.
                 *
                 * @private
                 * @param {Function} func The function to modify.
                 * @param {Function} string The `toString` result.
                 * @returns {Function} Returns `func`.
                 */
                var baseSetToString = !defineProperty ? identity : function(func, string) {
                    return defineProperty(func, 'toString', {
                        'configurable': true,
                        'enumerable': false,
                        'value': constant(string),
                        'writable': true
                    });
                };

                /**
                 * Creates a clone of  `buffer`.
                 *
                 * @private
                 * @param {Buffer} buffer The buffer to clone.
                 * @param {boolean} [isDeep] Specify a deep clone.
                 * @returns {Buffer} Returns the cloned buffer.
                 */
                function cloneBuffer(buffer, isDeep) {
                    if (isDeep) {
                        return buffer.slice();
                    }
                    var length = buffer.length,
                        result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);

                    buffer.copy(result);
                    return result;
                }

                /**
                 * Creates a clone of `arrayBuffer`.
                 *
                 * @private
                 * @param {ArrayBuffer} arrayBuffer The array buffer to clone.
                 * @returns {ArrayBuffer} Returns the cloned array buffer.
                 */
                function cloneArrayBuffer(arrayBuffer) {
                    var result = new arrayBuffer.constructor(arrayBuffer.byteLength);
                    new Uint8Array(result).set(new Uint8Array(arrayBuffer));
                    return result;
                }

                /**
                 * Creates a clone of `typedArray`.
                 *
                 * @private
                 * @param {Object} typedArray The typed array to clone.
                 * @param {boolean} [isDeep] Specify a deep clone.
                 * @returns {Object} Returns the cloned typed array.
                 */
                function cloneTypedArray(typedArray, isDeep) {
                    var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;
                    return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);
                }

                /**
                 * Copies the values of `source` to `array`.
                 *
                 * @private
                 * @param {Array} source The array to copy values from.
                 * @param {Array} [array=[]] The array to copy values to.
                 * @returns {Array} Returns `array`.
                 */
                function copyArray(source, array) {
                    var index = -1,
                        length = source.length;

                    array || (array = Array(length));
                    while (++index < length) {
                        array[index] = source[index];
                    }
                    return array;
                }

                /**
                 * Copies properties of `source` to `object`.
                 *
                 * @private
                 * @param {Object} source The object to copy properties from.
                 * @param {Array} props The property identifiers to copy.
                 * @param {Object} [object={}] The object to copy properties to.
                 * @param {Function} [customizer] The function to customize copied values.
                 * @returns {Object} Returns `object`.
                 */
                function copyObject(source, props, object, customizer) {
                    var isNew = !object;
                    object || (object = {});

                    var index = -1,
                        length = props.length;

                    while (++index < length) {
                        var key = props[index];

                        var newValue = customizer ?
                            customizer(object[key], source[key], key, object, source) :
                            undefined;

                        if (newValue === undefined) {
                            newValue = source[key];
                        }
                        if (isNew) {
                            baseAssignValue(object, key, newValue);
                        } else {
                            assignValue(object, key, newValue);
                        }
                    }
                    return object;
                }

                /**
                 * Creates a function like `_.assign`.
                 *
                 * @private
                 * @param {Function} assigner The function to assign values.
                 * @returns {Function} Returns the new assigner function.
                 */
                function createAssigner(assigner) {
                    return baseRest(function(object, sources) {
                        var index = -1,
                            length = sources.length,
                            customizer = length > 1 ? sources[length - 1] : undefined,
                            guard = length > 2 ? sources[2] : undefined;

                        customizer = (assigner.length > 3 && typeof customizer == 'function') ?
                            (length--, customizer) :
                            undefined;

                        if (guard && isIterateeCall(sources[0], sources[1], guard)) {
                            customizer = length < 3 ? undefined : customizer;
                            length = 1;
                        }
                        object = Object(object);
                        while (++index < length) {
                            var source = sources[index];
                            if (source) {
                                assigner(object, source, index, customizer);
                            }
                        }
                        return object;
                    });
                }

                /**
                 * Creates a base function for methods like `_.forIn` and `_.forOwn`.
                 *
                 * @private
                 * @param {boolean} [fromRight] Specify iterating from right to left.
                 * @returns {Function} Returns the new base function.
                 */
                function createBaseFor(fromRight) {
                    return function(object, iteratee, keysFunc) {
                        var index = -1,
                            iterable = Object(object),
                            props = keysFunc(object),
                            length = props.length;

                        while (length--) {
                            var key = props[fromRight ? length : ++index];
                            if (iteratee(iterable[key], key, iterable) === false) {
                                break;
                            }
                        }
                        return object;
                    };
                }

                /**
                 * Gets the data for `map`.
                 *
                 * @private
                 * @param {Object} map The map to query.
                 * @param {string} key The reference key.
                 * @returns {*} Returns the map data.
                 */
                function getMapData(map, key) {
                    var data = map.__data__;
                    return isKeyable(key) ?
                        data[typeof key == 'string' ? 'string' : 'hash'] :
                        data.map;
                }

                /**
                 * Gets the native function at `key` of `object`.
                 *
                 * @private
                 * @param {Object} object The object to query.
                 * @param {string} key The key of the method to get.
                 * @returns {*} Returns the function if it's native, else `undefined`.
                 */
                function getNative(object, key) {
                    var value = getValue(object, key);
                    return baseIsNative(value) ? value : undefined;
                }

                /**
                 * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.
                 *
                 * @private
                 * @param {*} value The value to query.
                 * @returns {string} Returns the raw `toStringTag`.
                 */
                function getRawTag(value) {
                    var isOwn = hasOwnProperty.call(value, symToStringTag),
                        tag = value[symToStringTag];

                    try {
                        value[symToStringTag] = undefined;
                        var unmasked = true;
                    } catch (e) {}

                    var result = nativeObjectToString.call(value);
                    if (unmasked) {
                        if (isOwn) {
                            value[symToStringTag] = tag;
                        } else {
                            delete value[symToStringTag];
                        }
                    }
                    return result;
                }

                /**
                 * Initializes an object clone.
                 *
                 * @private
                 * @param {Object} object The object to clone.
                 * @returns {Object} Returns the initialized clone.
                 */
                function initCloneObject(object) {
                    return (typeof object.constructor == 'function' && !isPrototype(object)) ?
                        baseCreate(getPrototype(object)) :
                        {};
                }

                /**
                 * Checks if `value` is a valid array-like index.
                 *
                 * @private
                 * @param {*} value The value to check.
                 * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.
                 * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.
                 */
                function isIndex(value, length) {
                    var type = typeof value;
                    length = length == null ? MAX_SAFE_INTEGER : length;

                    return !!length &&
                        (type == 'number' ||
                            (type != 'symbol' && reIsUint.test(value))) &&
                        (value > -1 && value % 1 == 0 && value < length);
                }

                /**
                 * Checks if the given arguments are from an iteratee call.
                 *
                 * @private
                 * @param {*} value The potential iteratee value argument.
                 * @param {*} index The potential iteratee index or key argument.
                 * @param {*} object The potential iteratee object argument.
                 * @returns {boolean} Returns `true` if the arguments are from an iteratee call,
                 *  else `false`.
                 */
                function isIterateeCall(value, index, object) {
                    if (!isObject(object)) {
                        return false;
                    }
                    var type = typeof index;
                    if (type == 'number' ?
                        (isArrayLike(object) && isIndex(index, object.length)) :
                        (type == 'string' && index in object)
                    ) {
                        return eq(object[index], value);
                    }
                    return false;
                }

                /**
                 * Checks if `value` is suitable for use as unique object key.
                 *
                 * @private
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is suitable, else `false`.
                 */
                function isKeyable(value) {
                    var type = typeof value;
                    return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean') ?
                        (value !== '__proto__') :
                        (value === null);
                }

                /**
                 * Checks if `func` has its source masked.
                 *
                 * @private
                 * @param {Function} func The function to check.
                 * @returns {boolean} Returns `true` if `func` is masked, else `false`.
                 */
                function isMasked(func) {
                    return !!maskSrcKey && (maskSrcKey in func);
                }

                /**
                 * Checks if `value` is likely a prototype object.
                 *
                 * @private
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.
                 */
                function isPrototype(value) {
                    var Ctor = value && value.constructor,
                        proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;

                    return value === proto;
                }

                /**
                 * This function is like
                 * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)
                 * except that it includes inherited enumerable properties.
                 *
                 * @private
                 * @param {Object} object The object to query.
                 * @returns {Array} Returns the array of property names.
                 */
                function nativeKeysIn(object) {
                    var result = [];
                    if (object != null) {
                        for (var key in Object(object)) {
                            result.push(key);
                        }
                    }
                    return result;
                }

                /**
                 * Converts `value` to a string using `Object.prototype.toString`.
                 *
                 * @private
                 * @param {*} value The value to convert.
                 * @returns {string} Returns the converted string.
                 */
                function objectToString(value) {
                    return nativeObjectToString.call(value);
                }

                /**
                 * A specialized version of `baseRest` which transforms the rest array.
                 *
                 * @private
                 * @param {Function} func The function to apply a rest parameter to.
                 * @param {number} [start=func.length-1] The start position of the rest parameter.
                 * @param {Function} transform The rest array transform.
                 * @returns {Function} Returns the new function.
                 */
                function overRest(func, start, transform) {
                    start = nativeMax(start === undefined ? (func.length - 1) : start, 0);
                    return function() {
                        var args = arguments,
                            index = -1,
                            length = nativeMax(args.length - start, 0),
                            array = Array(length);

                        while (++index < length) {
                            array[index] = args[start + index];
                        }
                        index = -1;
                        var otherArgs = Array(start + 1);
                        while (++index < start) {
                            otherArgs[index] = args[index];
                        }
                        otherArgs[start] = transform(array);
                        return apply(func, this, otherArgs);
                    };
                }

                /**
                 * Gets the value at `key`, unless `key` is "__proto__" or "constructor".
                 *
                 * @private
                 * @param {Object} object The object to query.
                 * @param {string} key The key of the property to get.
                 * @returns {*} Returns the property value.
                 */
                function safeGet(object, key) {
                    if (key === 'constructor' && typeof object[key] === 'function') {
                        return;
                    }

                    if (key == '__proto__') {
                        return;
                    }

                    return object[key];
                }

                /**
                 * Sets the `toString` method of `func` to return `string`.
                 *
                 * @private
                 * @param {Function} func The function to modify.
                 * @param {Function} string The `toString` result.
                 * @returns {Function} Returns `func`.
                 */
                var setToString = shortOut(baseSetToString);

                /**
                 * Creates a function that'll short out and invoke `identity` instead
                 * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`
                 * milliseconds.
                 *
                 * @private
                 * @param {Function} func The function to restrict.
                 * @returns {Function} Returns the new shortable function.
                 */
                function shortOut(func) {
                    var count = 0,
                        lastCalled = 0;

                    return function() {
                        var stamp = nativeNow(),
                            remaining = HOT_SPAN - (stamp - lastCalled);

                        lastCalled = stamp;
                        if (remaining > 0) {
                            if (++count >= HOT_COUNT) {
                                return arguments[0];
                            }
                        } else {
                            count = 0;
                        }
                        return func.apply(undefined, arguments);
                    };
                }

                /**
                 * Converts `func` to its source code.
                 *
                 * @private
                 * @param {Function} func The function to convert.
                 * @returns {string} Returns the source code.
                 */
                function toSource(func) {
                    if (func != null) {
                        try {
                            return funcToString.call(func);
                        } catch (e) {}
                        try {
                            return (func + '');
                        } catch (e) {}
                    }
                    return '';
                }

                /**
                 * Performs a
                 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
                 * comparison between two values to determine if they are equivalent.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to compare.
                 * @param {*} other The other value to compare.
                 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
                 * @example
                 *
                 * var object = { 'a': 1 };
                 * var other = { 'a': 1 };
                 *
                 * _.eq(object, object);
                 * // => true
                 *
                 * _.eq(object, other);
                 * // => false
                 *
                 * _.eq('a', 'a');
                 * // => true
                 *
                 * _.eq('a', Object('a'));
                 * // => false
                 *
                 * _.eq(NaN, NaN);
                 * // => true
                 */
                function eq(value, other) {
                    return value === other || (value !== value && other !== other);
                }

                /**
                 * Checks if `value` is likely an `arguments` object.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is an `arguments` object,
                 *  else `false`.
                 * @example
                 *
                 * _.isArguments(function() { return arguments; }());
                 * // => true
                 *
                 * _.isArguments([1, 2, 3]);
                 * // => false
                 */
                var isArguments = baseIsArguments(function() {
                    return arguments;
                }()) ? baseIsArguments : function(value) {
                    return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&
                        !propertyIsEnumerable.call(value, 'callee');
                };

                /**
                 * Checks if `value` is classified as an `Array` object.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is an array, else `false`.
                 * @example
                 *
                 * _.isArray([1, 2, 3]);
                 * // => true
                 *
                 * _.isArray(document.body.children);
                 * // => false
                 *
                 * _.isArray('abc');
                 * // => false
                 *
                 * _.isArray(_.noop);
                 * // => false
                 */
                var isArray = Array.isArray;

                /**
                 * Checks if `value` is array-like. A value is considered array-like if it's
                 * not a function and has a `value.length` that's an integer greater than or
                 * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is array-like, else `false`.
                 * @example
                 *
                 * _.isArrayLike([1, 2, 3]);
                 * // => true
                 *
                 * _.isArrayLike(document.body.children);
                 * // => true
                 *
                 * _.isArrayLike('abc');
                 * // => true
                 *
                 * _.isArrayLike(_.noop);
                 * // => false
                 */
                function isArrayLike(value) {
                    return value != null && isLength(value.length) && !isFunction(value);
                }

                /**
                 * This method is like `_.isArrayLike` except that it also checks if `value`
                 * is an object.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is an array-like object,
                 *  else `false`.
                 * @example
                 *
                 * _.isArrayLikeObject([1, 2, 3]);
                 * // => true
                 *
                 * _.isArrayLikeObject(document.body.children);
                 * // => true
                 *
                 * _.isArrayLikeObject('abc');
                 * // => false
                 *
                 * _.isArrayLikeObject(_.noop);
                 * // => false
                 */
                function isArrayLikeObject(value) {
                    return isObjectLike(value) && isArrayLike(value);
                }

                /**
                 * Checks if `value` is a buffer.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.3.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.
                 * @example
                 *
                 * _.isBuffer(new Buffer(2));
                 * // => true
                 *
                 * _.isBuffer(new Uint8Array(2));
                 * // => false
                 */
                var isBuffer = nativeIsBuffer || stubFalse;

                /**
                 * Checks if `value` is classified as a `Function` object.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a function, else `false`.
                 * @example
                 *
                 * _.isFunction(_);
                 * // => true
                 *
                 * _.isFunction(/abc/);
                 * // => false
                 */
                function isFunction(value) {
                    if (!isObject(value)) {
                        return false;
                    }
                    // The use of `Object#toString` avoids issues with the `typeof` operator
                    // in Safari 9 which returns 'object' for typed arrays and other constructors.
                    var tag = baseGetTag(value);
                    return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;
                }

                /**
                 * Checks if `value` is a valid array-like length.
                 *
                 * **Note:** This method is loosely based on
                 * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.
                 * @example
                 *
                 * _.isLength(3);
                 * // => true
                 *
                 * _.isLength(Number.MIN_VALUE);
                 * // => false
                 *
                 * _.isLength(Infinity);
                 * // => false
                 *
                 * _.isLength('3');
                 * // => false
                 */
                function isLength(value) {
                    return typeof value == 'number' &&
                        value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;
                }

                /**
                 * Checks if `value` is the
                 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
                 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
                 * @example
                 *
                 * _.isObject({});
                 * // => true
                 *
                 * _.isObject([1, 2, 3]);
                 * // => true
                 *
                 * _.isObject(_.noop);
                 * // => true
                 *
                 * _.isObject(null);
                 * // => false
                 */
                function isObject(value) {
                    var type = typeof value;
                    return value != null && (type == 'object' || type == 'function');
                }

                /**
                 * Checks if `value` is object-like. A value is object-like if it's not `null`
                 * and has a `typeof` result of "object".
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
                 * @example
                 *
                 * _.isObjectLike({});
                 * // => true
                 *
                 * _.isObjectLike([1, 2, 3]);
                 * // => true
                 *
                 * _.isObjectLike(_.noop);
                 * // => false
                 *
                 * _.isObjectLike(null);
                 * // => false
                 */
                function isObjectLike(value) {
                    return value != null && typeof value == 'object';
                }

                /**
                 * Checks if `value` is a plain object, that is, an object created by the
                 * `Object` constructor or one with a `[[Prototype]]` of `null`.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.8.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.
                 * @example
                 *
                 * function Foo() {
                 *   this.a = 1;
                 * }
                 *
                 * _.isPlainObject(new Foo);
                 * // => false
                 *
                 * _.isPlainObject([1, 2, 3]);
                 * // => false
                 *
                 * _.isPlainObject({ 'x': 0, 'y': 0 });
                 * // => true
                 *
                 * _.isPlainObject(Object.create(null));
                 * // => true
                 */
                function isPlainObject(value) {
                    if (!isObjectLike(value) || baseGetTag(value) != objectTag) {
                        return false;
                    }
                    var proto = getPrototype(value);
                    if (proto === null) {
                        return true;
                    }
                    var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;
                    return typeof Ctor == 'function' && Ctor instanceof Ctor &&
                        funcToString.call(Ctor) == objectCtorString;
                }

                /**
                 * Checks if `value` is classified as a typed array.
                 *
                 * @static
                 * @memberOf _
                 * @since 3.0.0
                 * @category Lang
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.
                 * @example
                 *
                 * _.isTypedArray(new Uint8Array);
                 * // => true
                 *
                 * _.isTypedArray([]);
                 * // => false
                 */
                var isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;

                /**
                 * Converts `value` to a plain object flattening inherited enumerable string
                 * keyed properties of `value` to own properties of the plain object.
                 *
                 * @static
                 * @memberOf _
                 * @since 3.0.0
                 * @category Lang
                 * @param {*} value The value to convert.
                 * @returns {Object} Returns the converted plain object.
                 * @example
                 *
                 * function Foo() {
                 *   this.b = 2;
                 * }
                 *
                 * Foo.prototype.c = 3;
                 *
                 * _.assign({ 'a': 1 }, new Foo);
                 * // => { 'a': 1, 'b': 2 }
                 *
                 * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));
                 * // => { 'a': 1, 'b': 2, 'c': 3 }
                 */
                function toPlainObject(value) {
                    return copyObject(value, keysIn(value));
                }

                /**
                 * Creates an array of the own and inherited enumerable property names of `object`.
                 *
                 * **Note:** Non-object values are coerced to objects.
                 *
                 * @static
                 * @memberOf _
                 * @since 3.0.0
                 * @category Object
                 * @param {Object} object The object to query.
                 * @returns {Array} Returns the array of property names.
                 * @example
                 *
                 * function Foo() {
                 *   this.a = 1;
                 *   this.b = 2;
                 * }
                 *
                 * Foo.prototype.c = 3;
                 *
                 * _.keysIn(new Foo);
                 * // => ['a', 'b', 'c'] (iteration order is not guaranteed)
                 */
                function keysIn(object) {
                    return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);
                }

                /**
                 * This method is like `_.merge` except that it accepts `customizer` which
                 * is invoked to produce the merged values of the destination and source
                 * properties. If `customizer` returns `undefined`, merging is handled by the
                 * method instead. The `customizer` is invoked with six arguments:
                 * (objValue, srcValue, key, object, source, stack).
                 *
                 * **Note:** This method mutates `object`.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.0.0
                 * @category Object
                 * @param {Object} object The destination object.
                 * @param {...Object} sources The source objects.
                 * @param {Function} customizer The function to customize assigned values.
                 * @returns {Object} Returns `object`.
                 * @example
                 *
                 * function customizer(objValue, srcValue) {
                 *   if (_.isArray(objValue)) {
                 *     return objValue.concat(srcValue);
                 *   }
                 * }
                 *
                 * var object = { 'a': [1], 'b': [2] };
                 * var other = { 'a': [3], 'b': [4] };
                 *
                 * _.mergeWith(object, other, customizer);
                 * // => { 'a': [1, 3], 'b': [2, 4] }
                 */
                var mergeWith = createAssigner(function(object, source, srcIndex, customizer) {
                    baseMerge(object, source, srcIndex, customizer);
                });

                /**
                 * Creates a function that returns `value`.
                 *
                 * @static
                 * @memberOf _
                 * @since 2.4.0
                 * @category Util
                 * @param {*} value The value to return from the new function.
                 * @returns {Function} Returns the new constant function.
                 * @example
                 *
                 * var objects = _.times(2, _.constant({ 'a': 1 }));
                 *
                 * console.log(objects);
                 * // => [{ 'a': 1 }, { 'a': 1 }]
                 *
                 * console.log(objects[0] === objects[1]);
                 * // => true
                 */
                function constant(value) {
                    return function() {
                        return value;
                    };
                }

                /**
                 * This method returns the first argument it receives.
                 *
                 * @static
                 * @since 0.1.0
                 * @memberOf _
                 * @category Util
                 * @param {*} value Any value.
                 * @returns {*} Returns `value`.
                 * @example
                 *
                 * var object = { 'a': 1 };
                 *
                 * console.log(_.identity(object) === object);
                 * // => true
                 */
                function identity(value) {
                    return value;
                }

                /**
                 * This method returns `false`.
                 *
                 * @static
                 * @memberOf _
                 * @since 4.13.0
                 * @category Util
                 * @returns {boolean} Returns `false`.
                 * @example
                 *
                 * _.times(2, _.stubFalse);
                 * // => [false, false]
                 */
                function stubFalse() {
                    return false;
                }

                module.exports = mergeWith;


                /***/
            }),

        /***/
        "../../node_modules/@chakra-ui/anatomy/dist/chunk-OA3DH5LS.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    anatomy: () => ( /* binding */ anatomy)
                    /* harmony export */
                });
                // src/anatomy.ts
                function anatomy(name, map = {}) {
                    let called = false;

                    function assert() {
                        if (!called) {
                            called = true;
                            return;
                        }
                        throw new Error(
                            "[anatomy] .part(...) should only be called once. Did you mean to use .extend(...) ?"
                        );
                    }

                    function parts(...values) {
                        assert();
                        for (const part of values) {;
                            map[part] = toPart(part);
                        }
                        return anatomy(name, map);
                    }

                    function extend(...parts2) {
                        for (const part of parts2) {
                            if (part in map)
                                continue;
                            map[part] = toPart(part);
                        }
                        return anatomy(name, map);
                    }

                    function selectors() {
                        const value = Object.fromEntries(
                            Object.entries(map).map(([key, part]) => [key, part.selector])
                        );
                        return value;
                    }

                    function classnames() {
                        const value = Object.fromEntries(
                            Object.entries(map).map(([key, part]) => [key, part.className])
                        );
                        return value;
                    }

                    function toPart(part) {
                        const el = ["container", "root"].includes(part != null ? part : "") ? [name] : [name, part];
                        const attr = el.filter(Boolean).join("__");
                        const className = `chakra-${attr}`;
                        const partObj = {
                            className,
                            selector: `.${className}`,
                            toString: () => part
                        };
                        return partObj;
                    }
                    const __type = {};
                    return {
                        parts,
                        toPart,
                        extend,
                        selectors,
                        classnames,
                        get keys() {
                            return Object.keys(map);
                        },
                        __type
                    };
                }


                //# sourceMappingURL=chunk-OA3DH5LS.mjs.map

                /***/
            }),

        /***/
        "../../node_modules/@chakra-ui/shared-utils/dist/index.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    ariaAttr: () => ( /* binding */ ariaAttr),
                    /* harmony export */
                    callAll: () => ( /* binding */ callAll),
                    /* harmony export */
                    callAllHandlers: () => ( /* binding */ callAllHandlers),
                    /* harmony export */
                    cx: () => ( /* binding */ cx),
                    /* harmony export */
                    dataAttr: () => ( /* binding */ dataAttr),
                    /* harmony export */
                    isObject: () => ( /* binding */ isObject),
                    /* harmony export */
                    runIfFn: () => ( /* binding */ runIfFn),
                    /* harmony export */
                    warn: () => ( /* binding */ warn)
                    /* harmony export */
                });
                // src/index.ts
                var cx = (...classNames) => classNames.filter(Boolean).join(" ");

                function isDev() {
                    return "development" !== "production";
                }

                function isObject(value) {
                    const type = typeof value;
                    return value != null && (type === "object" || type === "function") && !Array.isArray(value);
                }
                var warn = (options) => {
                    const {
                        condition,
                        message
                    } = options;
                    if (condition && isDev()) {
                        console.warn(message);
                    }
                };

                function runIfFn(valueOrFn, ...args) {
                    return isFunction(valueOrFn) ? valueOrFn(...args) : valueOrFn;
                }
                var isFunction = (value) => typeof value === "function";
                var dataAttr = (condition) => condition ? "" : void 0;
                var ariaAttr = (condition) => condition ? true : void 0;

                function callAllHandlers(...fns) {
                    return function func(event) {
                        fns.some((fn) => {
                            fn == null ? void 0 : fn(event);
                            return event == null ? void 0 : event.defaultPrevented;
                        });
                    };
                }

                function callAll(...fns) {
                    return function mergedFn(arg) {
                        fns.forEach((fn) => {
                            fn == null ? void 0 : fn(arg);
                        });
                    };
                }



                /***/
            }),

        /***/
        "../../node_modules/@chakra-ui/styled-system/dist/index.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    addPrefix: () => ( /* binding */ addPrefix),
                    /* harmony export */
                    background: () => ( /* binding */ background),
                    /* harmony export */
                    border: () => ( /* binding */ border),
                    /* harmony export */
                    calc: () => ( /* binding */ calc),
                    /* harmony export */
                    color: () => ( /* binding */ color),
                    /* harmony export */
                    createMultiStyleConfigHelpers: () => ( /* binding */ createMultiStyleConfigHelpers),
                    /* harmony export */
                    css: () => ( /* binding */ css),
                    /* harmony export */
                    cssVar: () => ( /* binding */ cssVar),
                    /* harmony export */
                    defineCssVars: () => ( /* binding */ defineCssVars),
                    /* harmony export */
                    defineStyle: () => ( /* binding */ defineStyle),
                    /* harmony export */
                    defineStyleConfig: () => ( /* binding */ defineStyleConfig),
                    /* harmony export */
                    effect: () => ( /* binding */ effect),
                    /* harmony export */
                    filter: () => ( /* binding */ filter),
                    /* harmony export */
                    flattenTokens: () => ( /* binding */ flattenTokens),
                    /* harmony export */
                    flexbox: () => ( /* binding */ flexbox),
                    /* harmony export */
                    getCSSVar: () => ( /* binding */ getCSSVar),
                    /* harmony export */
                    getCss: () => ( /* binding */ getCss),
                    /* harmony export */
                    grid: () => ( /* binding */ grid),
                    /* harmony export */
                    interactivity: () => ( /* binding */ interactivity),
                    /* harmony export */
                    isStyleProp: () => ( /* binding */ isStyleProp),
                    /* harmony export */
                    layout: () => ( /* binding */ layout),
                    /* harmony export */
                    layoutPropNames: () => ( /* binding */ layoutPropNames),
                    /* harmony export */
                    list: () => ( /* binding */ list),
                    /* harmony export */
                    omitThemingProps: () => ( /* binding */ omitThemingProps),
                    /* harmony export */
                    others: () => ( /* binding */ others),
                    /* harmony export */
                    position: () => ( /* binding */ position),
                    /* harmony export */
                    propNames: () => ( /* binding */ propNames),
                    /* harmony export */
                    pseudoPropNames: () => ( /* binding */ pseudoPropNames),
                    /* harmony export */
                    pseudoSelectors: () => ( /* binding */ pseudoSelectors),
                    /* harmony export */
                    resolveStyleConfig: () => ( /* binding */ resolveStyleConfig),
                    /* harmony export */
                    ring: () => ( /* binding */ ring),
                    /* harmony export */
                    scroll: () => ( /* binding */ scroll),
                    /* harmony export */
                    space: () => ( /* binding */ space),
                    /* harmony export */
                    systemProps: () => ( /* binding */ systemProps),
                    /* harmony export */
                    textDecoration: () => ( /* binding */ textDecoration),
                    /* harmony export */
                    toCSSVar: () => ( /* binding */ toCSSVar),
                    /* harmony export */
                    toVarDefinition: () => ( /* binding */ toVarDefinition),
                    /* harmony export */
                    toVarReference: () => ( /* binding */ toVarReference),
                    /* harmony export */
                    tokenToCSSVar: () => ( /* binding */ tokenToCSSVar),
                    /* harmony export */
                    transform: () => ( /* binding */ transform),
                    /* harmony export */
                    transition: () => ( /* binding */ transition),
                    /* harmony export */
                    typography: () => ( /* binding */ typography)
                    /* harmony export */
                });
                /* harmony import */
                var _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@chakra-ui/shared-utils/dist/index.mjs");
                /* harmony import */
                var lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/lodash.mergewith/index.js");
                // src/utils/create-transform.ts

                var isImportant = (value) => /!(important)?$/.test(value);
                var withoutImportant = (value) => typeof value === "string" ? value.replace(/!(important)?$/, "").trim() : value;
                var tokenToCSSVar = (scale, value) => (theme) => {
                    const valueStr = String(value);
                    const important = isImportant(valueStr);
                    const valueWithoutImportant = withoutImportant(valueStr);
                    const key = scale ? `${scale}.${valueWithoutImportant}` : valueWithoutImportant;
                    let transformed = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(theme.__cssMap) && key in theme.__cssMap ? theme.__cssMap[key].varRef : value;
                    transformed = withoutImportant(transformed);
                    return important ? `${transformed} !important` : transformed;
                };

                function createTransform(options) {
                    const {
                        scale,
                        transform: transform2,
                        compose
                    } = options;
                    const fn = (value, theme) => {
                        var _a;
                        const _value = tokenToCSSVar(scale, value)(theme);
                        let result = (_a = transform2 == null ? void 0 : transform2(_value, theme)) != null ? _a : _value;
                        if (compose) {
                            result = compose(result, theme);
                        }
                        return result;
                    };
                    return fn;
                }

                // src/utils/pipe.ts
                var pipe = (...fns) => (v) => fns.reduce((a, b) => b(a), v);

                // src/utils/prop-config.ts
                function toConfig(scale, transform2) {
                    return (property) => {
                        const result = {
                            property,
                            scale
                        };
                        result.transform = createTransform({
                            scale,
                            transform: transform2
                        });
                        return result;
                    };
                }
                var getRtl = ({
                    rtl,
                    ltr
                }) => (theme) => theme.direction === "rtl" ? rtl : ltr;

                function logical(options) {
                    const {
                        property,
                        scale,
                        transform: transform2
                    } = options;
                    return {
                        scale,
                        property: getRtl(property),
                        transform: scale ? createTransform({
                            scale,
                            compose: transform2
                        }) : transform2
                    };
                }

                // src/utils/templates.ts
                var transformTemplate = [
                    "rotate(var(--chakra-rotate, 0))",
                    "scaleX(var(--chakra-scale-x, 1))",
                    "scaleY(var(--chakra-scale-y, 1))",
                    "skewX(var(--chakra-skew-x, 0))",
                    "skewY(var(--chakra-skew-y, 0))"
                ];

                function getTransformTemplate() {
                    return [
                        "translateX(var(--chakra-translate-x, 0))",
                        "translateY(var(--chakra-translate-y, 0))",
                        ...transformTemplate
                    ].join(" ");
                }

                function getTransformGpuTemplate() {
                    return [
                        "translate3d(var(--chakra-translate-x, 0), var(--chakra-translate-y, 0), 0)",
                        ...transformTemplate
                    ].join(" ");
                }
                var filterTemplate = {
                    "--chakra-blur": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-brightness": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-contrast": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-grayscale": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-hue-rotate": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-invert": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-saturate": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-sepia": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-drop-shadow": "var(--chakra-empty,/*!*/ /*!*/)",
                    filter: [
                        "var(--chakra-blur)",
                        "var(--chakra-brightness)",
                        "var(--chakra-contrast)",
                        "var(--chakra-grayscale)",
                        "var(--chakra-hue-rotate)",
                        "var(--chakra-invert)",
                        "var(--chakra-saturate)",
                        "var(--chakra-sepia)",
                        "var(--chakra-drop-shadow)"
                    ].join(" ")
                };
                var backdropFilterTemplate = {
                    backdropFilter: [
                        "var(--chakra-backdrop-blur)",
                        "var(--chakra-backdrop-brightness)",
                        "var(--chakra-backdrop-contrast)",
                        "var(--chakra-backdrop-grayscale)",
                        "var(--chakra-backdrop-hue-rotate)",
                        "var(--chakra-backdrop-invert)",
                        "var(--chakra-backdrop-opacity)",
                        "var(--chakra-backdrop-saturate)",
                        "var(--chakra-backdrop-sepia)"
                    ].join(" "),
                    "--chakra-backdrop-blur": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-backdrop-brightness": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-backdrop-contrast": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-backdrop-grayscale": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-backdrop-hue-rotate": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-backdrop-invert": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-backdrop-opacity": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-backdrop-saturate": "var(--chakra-empty,/*!*/ /*!*/)",
                    "--chakra-backdrop-sepia": "var(--chakra-empty,/*!*/ /*!*/)"
                };

                function getRingTemplate(value) {
                    return {
                        "--chakra-ring-offset-shadow": `var(--chakra-ring-inset) 0 0 0 var(--chakra-ring-offset-width) var(--chakra-ring-offset-color)`,
                        "--chakra-ring-shadow": `var(--chakra-ring-inset) 0 0 0 calc(var(--chakra-ring-width) + var(--chakra-ring-offset-width)) var(--chakra-ring-color)`,
                        "--chakra-ring-width": value,
                        boxShadow: [
                            `var(--chakra-ring-offset-shadow)`,
                            `var(--chakra-ring-shadow)`,
                            `var(--chakra-shadow, 0 0 #0000)`
                        ].join(", ")
                    };
                }
                var flexDirectionTemplate = {
                    "row-reverse": {
                        space: "--chakra-space-x-reverse",
                        divide: "--chakra-divide-x-reverse"
                    },
                    "column-reverse": {
                        space: "--chakra-space-y-reverse",
                        divide: "--chakra-divide-y-reverse"
                    }
                };

                // src/utils/parse-gradient.ts
                var directionMap = {
                    "to-t": "to top",
                    "to-tr": "to top right",
                    "to-r": "to right",
                    "to-br": "to bottom right",
                    "to-b": "to bottom",
                    "to-bl": "to bottom left",
                    "to-l": "to left",
                    "to-tl": "to top left"
                };
                var valueSet = new Set(Object.values(directionMap));
                var globalSet = /* @__PURE__ */ new Set([
                    "none",
                    "-moz-initial",
                    "inherit",
                    "initial",
                    "revert",
                    "unset"
                ]);
                var trimSpace = (str) => str.trim();

                function parseGradient(value, theme) {
                    if (value == null || globalSet.has(value))
                        return value;
                    const prevent = isCSSFunction(value) || globalSet.has(value);
                    if (!prevent)
                        return `url('${value}')`;
                    const regex = /(^[a-z-A-Z]+)\((.*)\)/g;
                    const results = regex.exec(value);
                    const type = results == null ? void 0 : results[1];
                    const values = results == null ? void 0 : results[2];
                    if (!type || !values)
                        return value;
                    const _type = type.includes("-gradient") ? type : `${type}-gradient`;
                    const [maybeDirection, ...stops] = values.split(",").map(trimSpace).filter(Boolean);
                    if ((stops == null ? void 0 : stops.length) === 0)
                        return value;
                    const direction = maybeDirection in directionMap ? directionMap[maybeDirection] : maybeDirection;
                    stops.unshift(direction);
                    const _values = stops.map((stop) => {
                        if (valueSet.has(stop))
                            return stop;
                        const firstStop = stop.indexOf(" ");
                        const [_color, _stop] = firstStop !== -1 ? [stop.substr(0, firstStop), stop.substr(firstStop + 1)] : [stop];
                        const _stopOrFunc = isCSSFunction(_stop) ? _stop : _stop && _stop.split(" ");
                        const key = `colors.${_color}`;
                        const color2 = key in theme.__cssMap ? theme.__cssMap[key].varRef : _color;
                        return _stopOrFunc ? [
                            color2,
                            ...Array.isArray(_stopOrFunc) ? _stopOrFunc : [_stopOrFunc]
                        ].join(" ") : color2;
                    });
                    return `${_type}(${_values.join(", ")})`;
                }
                var isCSSFunction = (value) => {
                    return typeof value === "string" && value.includes("(") && value.includes(")");
                };
                var gradientTransform = (value, theme) => parseGradient(value, theme != null ? theme : {});

                // src/utils/transform-functions.ts
                function isCssVar(value) {
                    return /^var\(--.+\)$/.test(value);
                }
                var analyzeCSSValue = (value) => {
                    const num = parseFloat(value.toString());
                    const unit = value.toString().replace(String(num), "");
                    return {
                        unitless: !unit,
                        value: num,
                        unit
                    };
                };
                var wrap = (str) => (value) => `${str}(${value})`;
                var transformFunctions = {
                    filter(value) {
                        return value !== "auto" ? value : filterTemplate;
                    },
                    backdropFilter(value) {
                        return value !== "auto" ? value : backdropFilterTemplate;
                    },
                    ring(value) {
                        return getRingTemplate(transformFunctions.px(value));
                    },
                    bgClip(value) {
                        return value === "text" ? {
                            color: "transparent",
                            backgroundClip: "text"
                        } : {
                            backgroundClip: value
                        };
                    },
                    transform(value) {
                        if (value === "auto")
                            return getTransformTemplate();
                        if (value === "auto-gpu")
                            return getTransformGpuTemplate();
                        return value;
                    },
                    vh(value) {
                        return value === "$100vh" ? "var(--chakra-vh)" : value;
                    },
                    px(value) {
                        if (value == null)
                            return value;
                        const {
                            unitless
                        } = analyzeCSSValue(value);
                        return unitless || typeof value === "number" ? `${value}px` : value;
                    },
                    fraction(value) {
                        return !(typeof value === "number") || value > 1 ? value : `${value * 100}%`;
                    },
                    float(value, theme) {
                        const map = {
                            left: "right",
                            right: "left"
                        };
                        return theme.direction === "rtl" ? map[value] : value;
                    },
                    degree(value) {
                        if (isCssVar(value) || value == null)
                            return value;
                        const unitless = typeof value === "string" && !value.endsWith("deg");
                        return typeof value === "number" || unitless ? `${value}deg` : value;
                    },
                    gradient: gradientTransform,
                    blur: wrap("blur"),
                    opacity: wrap("opacity"),
                    brightness: wrap("brightness"),
                    contrast: wrap("contrast"),
                    dropShadow: wrap("drop-shadow"),
                    grayscale: wrap("grayscale"),
                    hueRotate: (value) => wrap("hue-rotate")(transformFunctions.degree(value)),
                    invert: wrap("invert"),
                    saturate: wrap("saturate"),
                    sepia: wrap("sepia"),
                    bgImage(value) {
                        if (value == null)
                            return value;
                        const prevent = isCSSFunction(value) || globalSet.has(value);
                        return !prevent ? `url(${value})` : value;
                    },
                    outline(value) {
                        const isNoneOrZero = String(value) === "0" || String(value) === "none";
                        return value !== null && isNoneOrZero ? {
                            outline: "2px solid transparent",
                            outlineOffset: "2px"
                        } : {
                            outline: value
                        };
                    },
                    flexDirection(value) {
                        var _a;
                        const {
                            space: space2,
                            divide: divide2
                        } = (_a = flexDirectionTemplate[value]) != null ? _a : {};
                        const result = {
                            flexDirection: value
                        };
                        if (space2)
                            result[space2] = 1;
                        if (divide2)
                            result[divide2] = 1;
                        return result;
                    }
                };

                // src/utils/index.ts
                var t = {
                    borderWidths: toConfig("borderWidths"),
                    borderStyles: toConfig("borderStyles"),
                    colors: toConfig("colors"),
                    borders: toConfig("borders"),
                    gradients: toConfig("gradients", transformFunctions.gradient),
                    radii: toConfig("radii", transformFunctions.px),
                    space: toConfig("space", pipe(transformFunctions.vh, transformFunctions.px)),
                    spaceT: toConfig("space", pipe(transformFunctions.vh, transformFunctions.px)),
                    degreeT(property) {
                        return {
                            property,
                            transform: transformFunctions.degree
                        };
                    },
                    prop(property, scale, transform2) {
                        return {
                            property,
                            scale,
                            ...scale && {
                                transform: createTransform({
                                    scale,
                                    transform: transform2
                                })
                            }
                        };
                    },
                    propT(property, transform2) {
                        return {
                            property,
                            transform: transform2
                        };
                    },
                    sizes: toConfig("sizes", pipe(transformFunctions.vh, transformFunctions.px)),
                    sizesT: toConfig("sizes", pipe(transformFunctions.vh, transformFunctions.fraction)),
                    shadows: toConfig("shadows"),
                    logical,
                    blur: toConfig("blur", transformFunctions.blur)
                };

                // src/config/background.ts
                var background = {
                    background: t.colors("background"),
                    backgroundColor: t.colors("backgroundColor"),
                    backgroundImage: t.gradients("backgroundImage"),
                    backgroundSize: true,
                    backgroundPosition: true,
                    backgroundRepeat: true,
                    backgroundAttachment: true,
                    backgroundClip: {
                        transform: transformFunctions.bgClip
                    },
                    bgSize: t.prop("backgroundSize"),
                    bgPosition: t.prop("backgroundPosition"),
                    bg: t.colors("background"),
                    bgColor: t.colors("backgroundColor"),
                    bgPos: t.prop("backgroundPosition"),
                    bgRepeat: t.prop("backgroundRepeat"),
                    bgAttachment: t.prop("backgroundAttachment"),
                    bgGradient: t.gradients("backgroundImage"),
                    bgClip: {
                        transform: transformFunctions.bgClip
                    }
                };
                Object.assign(background, {
                    bgImage: background.backgroundImage,
                    bgImg: background.backgroundImage
                });

                // src/config/border.ts
                var border = {
                    border: t.borders("border"),
                    borderWidth: t.borderWidths("borderWidth"),
                    borderStyle: t.borderStyles("borderStyle"),
                    borderColor: t.colors("borderColor"),
                    borderRadius: t.radii("borderRadius"),
                    borderTop: t.borders("borderTop"),
                    borderBlockStart: t.borders("borderBlockStart"),
                    borderTopLeftRadius: t.radii("borderTopLeftRadius"),
                    borderStartStartRadius: t.logical({
                        scale: "radii",
                        property: {
                            ltr: "borderTopLeftRadius",
                            rtl: "borderTopRightRadius"
                        }
                    }),
                    borderEndStartRadius: t.logical({
                        scale: "radii",
                        property: {
                            ltr: "borderBottomLeftRadius",
                            rtl: "borderBottomRightRadius"
                        }
                    }),
                    borderTopRightRadius: t.radii("borderTopRightRadius"),
                    borderStartEndRadius: t.logical({
                        scale: "radii",
                        property: {
                            ltr: "borderTopRightRadius",
                            rtl: "borderTopLeftRadius"
                        }
                    }),
                    borderEndEndRadius: t.logical({
                        scale: "radii",
                        property: {
                            ltr: "borderBottomRightRadius",
                            rtl: "borderBottomLeftRadius"
                        }
                    }),
                    borderRight: t.borders("borderRight"),
                    borderInlineEnd: t.borders("borderInlineEnd"),
                    borderBottom: t.borders("borderBottom"),
                    borderBlockEnd: t.borders("borderBlockEnd"),
                    borderBottomLeftRadius: t.radii("borderBottomLeftRadius"),
                    borderBottomRightRadius: t.radii("borderBottomRightRadius"),
                    borderLeft: t.borders("borderLeft"),
                    borderInlineStart: {
                        property: "borderInlineStart",
                        scale: "borders"
                    },
                    borderInlineStartRadius: t.logical({
                        scale: "radii",
                        property: {
                            ltr: ["borderTopLeftRadius", "borderBottomLeftRadius"],
                            rtl: ["borderTopRightRadius", "borderBottomRightRadius"]
                        }
                    }),
                    borderInlineEndRadius: t.logical({
                        scale: "radii",
                        property: {
                            ltr: ["borderTopRightRadius", "borderBottomRightRadius"],
                            rtl: ["borderTopLeftRadius", "borderBottomLeftRadius"]
                        }
                    }),
                    borderX: t.borders(["borderLeft", "borderRight"]),
                    borderInline: t.borders("borderInline"),
                    borderY: t.borders(["borderTop", "borderBottom"]),
                    borderBlock: t.borders("borderBlock"),
                    borderTopWidth: t.borderWidths("borderTopWidth"),
                    borderBlockStartWidth: t.borderWidths("borderBlockStartWidth"),
                    borderTopColor: t.colors("borderTopColor"),
                    borderBlockStartColor: t.colors("borderBlockStartColor"),
                    borderTopStyle: t.borderStyles("borderTopStyle"),
                    borderBlockStartStyle: t.borderStyles("borderBlockStartStyle"),
                    borderBottomWidth: t.borderWidths("borderBottomWidth"),
                    borderBlockEndWidth: t.borderWidths("borderBlockEndWidth"),
                    borderBottomColor: t.colors("borderBottomColor"),
                    borderBlockEndColor: t.colors("borderBlockEndColor"),
                    borderBottomStyle: t.borderStyles("borderBottomStyle"),
                    borderBlockEndStyle: t.borderStyles("borderBlockEndStyle"),
                    borderLeftWidth: t.borderWidths("borderLeftWidth"),
                    borderInlineStartWidth: t.borderWidths("borderInlineStartWidth"),
                    borderLeftColor: t.colors("borderLeftColor"),
                    borderInlineStartColor: t.colors("borderInlineStartColor"),
                    borderLeftStyle: t.borderStyles("borderLeftStyle"),
                    borderInlineStartStyle: t.borderStyles("borderInlineStartStyle"),
                    borderRightWidth: t.borderWidths("borderRightWidth"),
                    borderInlineEndWidth: t.borderWidths("borderInlineEndWidth"),
                    borderRightColor: t.colors("borderRightColor"),
                    borderInlineEndColor: t.colors("borderInlineEndColor"),
                    borderRightStyle: t.borderStyles("borderRightStyle"),
                    borderInlineEndStyle: t.borderStyles("borderInlineEndStyle"),
                    borderTopRadius: t.radii(["borderTopLeftRadius", "borderTopRightRadius"]),
                    borderBottomRadius: t.radii([
                        "borderBottomLeftRadius",
                        "borderBottomRightRadius"
                    ]),
                    borderLeftRadius: t.radii(["borderTopLeftRadius", "borderBottomLeftRadius"]),
                    borderRightRadius: t.radii([
                        "borderTopRightRadius",
                        "borderBottomRightRadius"
                    ])
                };
                Object.assign(border, {
                    rounded: border.borderRadius,
                    roundedTop: border.borderTopRadius,
                    roundedTopLeft: border.borderTopLeftRadius,
                    roundedTopRight: border.borderTopRightRadius,
                    roundedTopStart: border.borderStartStartRadius,
                    roundedTopEnd: border.borderStartEndRadius,
                    roundedBottom: border.borderBottomRadius,
                    roundedBottomLeft: border.borderBottomLeftRadius,
                    roundedBottomRight: border.borderBottomRightRadius,
                    roundedBottomStart: border.borderEndStartRadius,
                    roundedBottomEnd: border.borderEndEndRadius,
                    roundedLeft: border.borderLeftRadius,
                    roundedRight: border.borderRightRadius,
                    roundedStart: border.borderInlineStartRadius,
                    roundedEnd: border.borderInlineEndRadius,
                    borderStart: border.borderInlineStart,
                    borderEnd: border.borderInlineEnd,
                    borderTopStartRadius: border.borderStartStartRadius,
                    borderTopEndRadius: border.borderStartEndRadius,
                    borderBottomStartRadius: border.borderEndStartRadius,
                    borderBottomEndRadius: border.borderEndEndRadius,
                    borderStartRadius: border.borderInlineStartRadius,
                    borderEndRadius: border.borderInlineEndRadius,
                    borderStartWidth: border.borderInlineStartWidth,
                    borderEndWidth: border.borderInlineEndWidth,
                    borderStartColor: border.borderInlineStartColor,
                    borderEndColor: border.borderInlineEndColor,
                    borderStartStyle: border.borderInlineStartStyle,
                    borderEndStyle: border.borderInlineEndStyle
                });

                // src/config/color.ts
                var color = {
                    color: t.colors("color"),
                    textColor: t.colors("color"),
                    fill: t.colors("fill"),
                    stroke: t.colors("stroke")
                };

                // src/config/effect.ts
                var effect = {
                    boxShadow: t.shadows("boxShadow"),
                    mixBlendMode: true,
                    blendMode: t.prop("mixBlendMode"),
                    backgroundBlendMode: true,
                    bgBlendMode: t.prop("backgroundBlendMode"),
                    opacity: true
                };
                Object.assign(effect, {
                    shadow: effect.boxShadow
                });

                // src/config/filter.ts
                var filter = {
                    filter: {
                        transform: transformFunctions.filter
                    },
                    blur: t.blur("--chakra-blur"),
                    brightness: t.propT("--chakra-brightness", transformFunctions.brightness),
                    contrast: t.propT("--chakra-contrast", transformFunctions.contrast),
                    hueRotate: t.propT("--chakra-hue-rotate", transformFunctions.hueRotate),
                    invert: t.propT("--chakra-invert", transformFunctions.invert),
                    saturate: t.propT("--chakra-saturate", transformFunctions.saturate),
                    dropShadow: t.propT("--chakra-drop-shadow", transformFunctions.dropShadow),
                    backdropFilter: {
                        transform: transformFunctions.backdropFilter
                    },
                    backdropBlur: t.blur("--chakra-backdrop-blur"),
                    backdropBrightness: t.propT(
                        "--chakra-backdrop-brightness",
                        transformFunctions.brightness
                    ),
                    backdropContrast: t.propT("--chakra-backdrop-contrast", transformFunctions.contrast),
                    backdropHueRotate: t.propT(
                        "--chakra-backdrop-hue-rotate",
                        transformFunctions.hueRotate
                    ),
                    backdropInvert: t.propT("--chakra-backdrop-invert", transformFunctions.invert),
                    backdropSaturate: t.propT("--chakra-backdrop-saturate", transformFunctions.saturate)
                };

                // src/config/flexbox.ts
                var flexbox = {
                    alignItems: true,
                    alignContent: true,
                    justifyItems: true,
                    justifyContent: true,
                    flexWrap: true,
                    flexDirection: {
                        transform: transformFunctions.flexDirection
                    },
                    flex: true,
                    flexFlow: true,
                    flexGrow: true,
                    flexShrink: true,
                    flexBasis: t.sizes("flexBasis"),
                    justifySelf: true,
                    alignSelf: true,
                    order: true,
                    placeItems: true,
                    placeContent: true,
                    placeSelf: true,
                    gap: t.space("gap"),
                    rowGap: t.space("rowGap"),
                    columnGap: t.space("columnGap")
                };
                Object.assign(flexbox, {
                    flexDir: flexbox.flexDirection
                });

                // src/config/grid.ts
                var grid = {
                    gridGap: t.space("gridGap"),
                    gridColumnGap: t.space("gridColumnGap"),
                    gridRowGap: t.space("gridRowGap"),
                    gridColumn: true,
                    gridRow: true,
                    gridAutoFlow: true,
                    gridAutoColumns: true,
                    gridColumnStart: true,
                    gridColumnEnd: true,
                    gridRowStart: true,
                    gridRowEnd: true,
                    gridAutoRows: true,
                    gridTemplate: true,
                    gridTemplateColumns: true,
                    gridTemplateRows: true,
                    gridTemplateAreas: true,
                    gridArea: true
                };

                // src/config/interactivity.ts
                var interactivity = {
                    appearance: true,
                    cursor: true,
                    resize: true,
                    userSelect: true,
                    pointerEvents: true,
                    outline: {
                        transform: transformFunctions.outline
                    },
                    outlineOffset: true,
                    outlineColor: t.colors("outlineColor")
                };

                // src/config/layout.ts
                var layout = {
                    width: t.sizesT("width"),
                    inlineSize: t.sizesT("inlineSize"),
                    height: t.sizes("height"),
                    blockSize: t.sizes("blockSize"),
                    boxSize: t.sizes(["width", "height"]),
                    minWidth: t.sizes("minWidth"),
                    minInlineSize: t.sizes("minInlineSize"),
                    minHeight: t.sizes("minHeight"),
                    minBlockSize: t.sizes("minBlockSize"),
                    maxWidth: t.sizes("maxWidth"),
                    maxInlineSize: t.sizes("maxInlineSize"),
                    maxHeight: t.sizes("maxHeight"),
                    maxBlockSize: t.sizes("maxBlockSize"),
                    overflow: true,
                    overflowX: true,
                    overflowY: true,
                    overscrollBehavior: true,
                    overscrollBehaviorX: true,
                    overscrollBehaviorY: true,
                    display: true,
                    aspectRatio: true,
                    hideFrom: {
                        scale: "breakpoints",
                        transform: (value, theme) => {
                            var _a, _b, _c;
                            const breakpoint = (_c = (_b = (_a = theme.__breakpoints) == null ? void 0 : _a.get(value)) == null ? void 0 : _b.minW) != null ? _c : value;
                            const mq = `@media screen and (min-width: ${breakpoint})`;
                            return {
                                [mq]: {
                                    display: "none"
                                }
                            };
                        }
                    },
                    hideBelow: {
                        scale: "breakpoints",
                        transform: (value, theme) => {
                            var _a, _b, _c;
                            const breakpoint = (_c = (_b = (_a = theme.__breakpoints) == null ? void 0 : _a.get(value)) == null ? void 0 : _b._minW) != null ? _c : value;
                            const mq = `@media screen and (max-width: ${breakpoint})`;
                            return {
                                [mq]: {
                                    display: "none"
                                }
                            };
                        }
                    },
                    verticalAlign: true,
                    boxSizing: true,
                    boxDecorationBreak: true,
                    float: t.propT("float", transformFunctions.float),
                    objectFit: true,
                    objectPosition: true,
                    visibility: true,
                    isolation: true
                };
                Object.assign(layout, {
                    w: layout.width,
                    h: layout.height,
                    minW: layout.minWidth,
                    maxW: layout.maxWidth,
                    minH: layout.minHeight,
                    maxH: layout.maxHeight,
                    overscroll: layout.overscrollBehavior,
                    overscrollX: layout.overscrollBehaviorX,
                    overscrollY: layout.overscrollBehaviorY
                });

                // src/config/list.ts
                var list = {
                    listStyleType: true,
                    listStylePosition: true,
                    listStylePos: t.prop("listStylePosition"),
                    listStyleImage: true,
                    listStyleImg: t.prop("listStyleImage")
                };

                // src/get.ts
                function get(obj, path, fallback, index) {
                    const key = typeof path === "string" ? path.split(".") : [path];
                    for (index = 0; index < key.length; index += 1) {
                        if (!obj)
                            break;
                        obj = obj[key[index]];
                    }
                    return obj === void 0 ? fallback : obj;
                }
                var memoize = (fn) => {
                    const cache = /* @__PURE__ */ new WeakMap();
                    const memoizedFn = (obj, path, fallback, index) => {
                        if (typeof obj === "undefined") {
                            return fn(obj, path, fallback);
                        }
                        if (!cache.has(obj)) {
                            cache.set(obj, /* @__PURE__ */ new Map());
                        }
                        const map = cache.get(obj);
                        if (map.has(path)) {
                            return map.get(path);
                        }
                        const value = fn(obj, path, fallback, index);
                        map.set(path, value);
                        return value;
                    };
                    return memoizedFn;
                };
                var memoizedGet = memoize(get);

                // src/config/others.ts
                var srOnly = {
                    border: "0px",
                    clip: "rect(0, 0, 0, 0)",
                    width: "1px",
                    height: "1px",
                    margin: "-1px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    position: "absolute"
                };
                var srFocusable = {
                    position: "static",
                    width: "auto",
                    height: "auto",
                    clip: "auto",
                    padding: "0",
                    margin: "0",
                    overflow: "visible",
                    whiteSpace: "normal"
                };
                var getWithPriority = (theme, key, styles) => {
                    const result = {};
                    const obj = memoizedGet(theme, key, {});
                    for (const prop in obj) {
                        const isInStyles = prop in styles && styles[prop] != null;
                        if (!isInStyles)
                            result[prop] = obj[prop];
                    }
                    return result;
                };
                var others = {
                    srOnly: {
                        transform(value) {
                            if (value === true)
                                return srOnly;
                            if (value === "focusable")
                                return srFocusable;
                            return {};
                        }
                    },
                    layerStyle: {
                        processResult: true,
                        transform: (value, theme, styles) => getWithPriority(theme, `layerStyles.${value}`, styles)
                    },
                    textStyle: {
                        processResult: true,
                        transform: (value, theme, styles) => getWithPriority(theme, `textStyles.${value}`, styles)
                    },
                    apply: {
                        processResult: true,
                        transform: (value, theme, styles) => getWithPriority(theme, value, styles)
                    }
                };

                // src/config/position.ts
                var position = {
                    position: true,
                    pos: t.prop("position"),
                    zIndex: t.prop("zIndex", "zIndices"),
                    inset: t.spaceT("inset"),
                    insetX: t.spaceT(["left", "right"]),
                    insetInline: t.spaceT("insetInline"),
                    insetY: t.spaceT(["top", "bottom"]),
                    insetBlock: t.spaceT("insetBlock"),
                    top: t.spaceT("top"),
                    insetBlockStart: t.spaceT("insetBlockStart"),
                    bottom: t.spaceT("bottom"),
                    insetBlockEnd: t.spaceT("insetBlockEnd"),
                    left: t.spaceT("left"),
                    insetInlineStart: t.logical({
                        scale: "space",
                        property: {
                            ltr: "left",
                            rtl: "right"
                        }
                    }),
                    right: t.spaceT("right"),
                    insetInlineEnd: t.logical({
                        scale: "space",
                        property: {
                            ltr: "right",
                            rtl: "left"
                        }
                    })
                };
                Object.assign(position, {
                    insetStart: position.insetInlineStart,
                    insetEnd: position.insetInlineEnd
                });

                // src/config/ring.ts
                var ring = {
                    ring: {
                        transform: transformFunctions.ring
                    },
                    ringColor: t.colors("--chakra-ring-color"),
                    ringOffset: t.prop("--chakra-ring-offset-width"),
                    ringOffsetColor: t.colors("--chakra-ring-offset-color"),
                    ringInset: t.prop("--chakra-ring-inset")
                };

                // src/config/space.ts
                var space = {
                    margin: t.spaceT("margin"),
                    marginTop: t.spaceT("marginTop"),
                    marginBlockStart: t.spaceT("marginBlockStart"),
                    marginRight: t.spaceT("marginRight"),
                    marginInlineEnd: t.spaceT("marginInlineEnd"),
                    marginBottom: t.spaceT("marginBottom"),
                    marginBlockEnd: t.spaceT("marginBlockEnd"),
                    marginLeft: t.spaceT("marginLeft"),
                    marginInlineStart: t.spaceT("marginInlineStart"),
                    marginX: t.spaceT(["marginInlineStart", "marginInlineEnd"]),
                    marginInline: t.spaceT("marginInline"),
                    marginY: t.spaceT(["marginTop", "marginBottom"]),
                    marginBlock: t.spaceT("marginBlock"),
                    padding: t.space("padding"),
                    paddingTop: t.space("paddingTop"),
                    paddingBlockStart: t.space("paddingBlockStart"),
                    paddingRight: t.space("paddingRight"),
                    paddingBottom: t.space("paddingBottom"),
                    paddingBlockEnd: t.space("paddingBlockEnd"),
                    paddingLeft: t.space("paddingLeft"),
                    paddingInlineStart: t.space("paddingInlineStart"),
                    paddingInlineEnd: t.space("paddingInlineEnd"),
                    paddingX: t.space(["paddingInlineStart", "paddingInlineEnd"]),
                    paddingInline: t.space("paddingInline"),
                    paddingY: t.space(["paddingTop", "paddingBottom"]),
                    paddingBlock: t.space("paddingBlock")
                };
                Object.assign(space, {
                    m: space.margin,
                    mt: space.marginTop,
                    mr: space.marginRight,
                    me: space.marginInlineEnd,
                    marginEnd: space.marginInlineEnd,
                    mb: space.marginBottom,
                    ml: space.marginLeft,
                    ms: space.marginInlineStart,
                    marginStart: space.marginInlineStart,
                    mx: space.marginX,
                    my: space.marginY,
                    p: space.padding,
                    pt: space.paddingTop,
                    py: space.paddingY,
                    px: space.paddingX,
                    pb: space.paddingBottom,
                    pl: space.paddingLeft,
                    ps: space.paddingInlineStart,
                    paddingStart: space.paddingInlineStart,
                    pr: space.paddingRight,
                    pe: space.paddingInlineEnd,
                    paddingEnd: space.paddingInlineEnd
                });

                // src/config/text-decoration.ts
                var textDecoration = {
                    textDecorationColor: t.colors("textDecorationColor"),
                    textDecoration: true,
                    textDecor: {
                        property: "textDecoration"
                    },
                    textDecorationLine: true,
                    textDecorationStyle: true,
                    textDecorationThickness: true,
                    textUnderlineOffset: true,
                    textShadow: t.shadows("textShadow")
                };

                // src/config/transform.ts
                var transform = {
                    clipPath: true,
                    transform: t.propT("transform", transformFunctions.transform),
                    transformOrigin: true,
                    translateX: t.spaceT("--chakra-translate-x"),
                    translateY: t.spaceT("--chakra-translate-y"),
                    skewX: t.degreeT("--chakra-skew-x"),
                    skewY: t.degreeT("--chakra-skew-y"),
                    scaleX: t.prop("--chakra-scale-x"),
                    scaleY: t.prop("--chakra-scale-y"),
                    scale: t.prop(["--chakra-scale-x", "--chakra-scale-y"]),
                    rotate: t.degreeT("--chakra-rotate")
                };

                // src/config/transition.ts
                var transition = {
                    transition: true,
                    transitionDelay: true,
                    animation: true,
                    willChange: true,
                    transitionDuration: t.prop("transitionDuration", "transition.duration"),
                    transitionProperty: t.prop("transitionProperty", "transition.property"),
                    transitionTimingFunction: t.prop(
                        "transitionTimingFunction",
                        "transition.easing"
                    )
                };

                // src/config/typography.ts
                var typography = {
                    fontFamily: t.prop("fontFamily", "fonts"),
                    fontSize: t.prop("fontSize", "fontSizes", transformFunctions.px),
                    fontWeight: t.prop("fontWeight", "fontWeights"),
                    lineHeight: t.prop("lineHeight", "lineHeights"),
                    letterSpacing: t.prop("letterSpacing", "letterSpacings"),
                    textAlign: true,
                    fontStyle: true,
                    textIndent: true,
                    wordBreak: true,
                    overflowWrap: true,
                    textOverflow: true,
                    textTransform: true,
                    whiteSpace: true,
                    isTruncated: {
                        transform(value) {
                            if (value === true) {
                                return {
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap"
                                };
                            }
                        }
                    },
                    noOfLines: {
                        static: {
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitBoxOrient: "vertical",
                            //@ts-ignore
                            WebkitLineClamp: "var(--chakra-line-clamp)"
                        },
                        property: "--chakra-line-clamp"
                    }
                };

                // src/config/scroll.ts
                var scroll = {
                    scrollBehavior: true,
                    scrollSnapAlign: true,
                    scrollSnapStop: true,
                    scrollSnapType: true,
                    // scroll margin
                    scrollMargin: t.spaceT("scrollMargin"),
                    scrollMarginTop: t.spaceT("scrollMarginTop"),
                    scrollMarginBottom: t.spaceT("scrollMarginBottom"),
                    scrollMarginLeft: t.spaceT("scrollMarginLeft"),
                    scrollMarginRight: t.spaceT("scrollMarginRight"),
                    scrollMarginX: t.spaceT(["scrollMarginLeft", "scrollMarginRight"]),
                    scrollMarginY: t.spaceT(["scrollMarginTop", "scrollMarginBottom"]),
                    // scroll padding
                    scrollPadding: t.spaceT("scrollPadding"),
                    scrollPaddingTop: t.spaceT("scrollPaddingTop"),
                    scrollPaddingBottom: t.spaceT("scrollPaddingBottom"),
                    scrollPaddingLeft: t.spaceT("scrollPaddingLeft"),
                    scrollPaddingRight: t.spaceT("scrollPaddingRight"),
                    scrollPaddingX: t.spaceT(["scrollPaddingLeft", "scrollPaddingRight"]),
                    scrollPaddingY: t.spaceT(["scrollPaddingTop", "scrollPaddingBottom"])
                };

                // src/create-theme-vars/calc.ts

                function resolveReference(operand) {
                    if ((0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(operand) && operand.reference) {
                        return operand.reference;
                    }
                    return String(operand);
                }
                var toExpression = (operator, ...operands) => operands.map(resolveReference).join(` ${operator} `).replace(/calc/g, "");
                var add = (...operands) => `calc(${toExpression("+", ...operands)})`;
                var subtract = (...operands) => `calc(${toExpression("-", ...operands)})`;
                var multiply = (...operands) => `calc(${toExpression("*", ...operands)})`;
                var divide = (...operands) => `calc(${toExpression("/", ...operands)})`;
                var negate = (x) => {
                    const value = resolveReference(x);
                    if (value != null && !Number.isNaN(parseFloat(value))) {
                        return String(value).startsWith("-") ? String(value).slice(1) : `-${value}`;
                    }
                    return multiply(value, -1);
                };
                var calc = Object.assign(
                    (x) => ({
                        add: (...operands) => calc(add(x, ...operands)),
                        subtract: (...operands) => calc(subtract(x, ...operands)),
                        multiply: (...operands) => calc(multiply(x, ...operands)),
                        divide: (...operands) => calc(divide(x, ...operands)),
                        negate: () => calc(negate(x)),
                        toString: () => x.toString()
                    }), {
                        add,
                        subtract,
                        multiply,
                        divide,
                        negate
                    }
                );

                // src/create-theme-vars/css-var.ts
                function replaceWhiteSpace(value, replaceValue = "-") {
                    return value.replace(/\s+/g, replaceValue);
                }

                function escape(value) {
                    const valueStr = replaceWhiteSpace(value.toString());
                    return escapeSymbol(escapeDot(valueStr));
                }

                function escapeDot(value) {
                    if (value.includes("\\."))
                        return value;
                    const isDecimal = !Number.isInteger(parseFloat(value.toString()));
                    return isDecimal ? value.replace(".", `\\.`) : value;
                }

                function escapeSymbol(value) {
                    return value.replace(/[!-,/:-@[-^`{-~]/g, "\\$&");
                }

                function addPrefix(value, prefix = "") {
                    return [prefix, value].filter(Boolean).join("-");
                }

                function toVarReference(name, fallback) {
                    return `var(${name}${fallback ? `, ${fallback}` : ""})`;
                }

                function toVarDefinition(value, prefix = "") {
                    return escape(`--${addPrefix(value, prefix)}`);
                }

                function cssVar(name, fallback, cssVarPrefix) {
                    const cssVariable = toVarDefinition(name, cssVarPrefix);
                    return {
                        variable: cssVariable,
                        reference: toVarReference(cssVariable, fallback)
                    };
                }

                function defineCssVars(scope, keys2) {
                    const vars = {};
                    for (const key of keys2) {
                        if (Array.isArray(key)) {
                            const [name, fallback] = key;
                            vars[name] = cssVar(`${scope}-${name}`, fallback);
                            continue;
                        }
                        vars[key] = cssVar(`${scope}-${key}`);
                    }
                    return vars;
                }

                // ../../utilities/breakpoint-utils/src/breakpoint.ts

                function getLastItem(array) {
                    const length = array == null ? 0 : array.length;
                    return length ? array[length - 1] : void 0;
                }

                function analyzeCSSValue2(value) {
                    const num = parseFloat(value.toString());
                    const unit = value.toString().replace(String(num), "");
                    return {
                        unitless: !unit,
                        value: num,
                        unit
                    };
                }

                function px(value) {
                    if (value == null)
                        return value;
                    const {
                        unitless
                    } = analyzeCSSValue2(value);
                    return unitless || typeof value === "number" ? `${value}px` : value;
                }
                var sortByBreakpointValue = (a, b) => parseInt(a[1], 10) > parseInt(b[1], 10) ? 1 : -1;
                var sortBps = (breakpoints) => Object.fromEntries(Object.entries(breakpoints).sort(sortByBreakpointValue));

                function normalize(breakpoints) {
                    const sorted = sortBps(breakpoints);
                    return Object.assign(Object.values(sorted), sorted);
                }

                function keys(breakpoints) {
                    const value = Object.keys(sortBps(breakpoints));
                    return new Set(value);
                }

                function subtract2(value) {
                    var _a;
                    if (!value)
                        return value;
                    value = (_a = px(value)) != null ? _a : value;
                    const OFFSET = -0.02;
                    return typeof value === "number" ? `${value + OFFSET}` : value.replace(/(\d+\.?\d*)/u, (m) => `${parseFloat(m) + OFFSET}`);
                }

                function toMediaQueryString(min, max) {
                    const query = ["@media screen"];
                    if (min)
                        query.push("and", `(min-width: ${px(min)})`);
                    if (max)
                        query.push("and", `(max-width: ${px(max)})`);
                    return query.join(" ");
                }

                function analyzeBreakpoints(breakpoints) {
                    var _a;
                    if (!breakpoints)
                        return null;
                    breakpoints.base = (_a = breakpoints.base) != null ? _a : "0px";
                    const normalized = normalize(breakpoints);
                    const queries = Object.entries(breakpoints).sort(sortByBreakpointValue).map(([breakpoint, minW], index, entry) => {
                        var _a2;
                        let [, maxW] = (_a2 = entry[index + 1]) != null ? _a2 : [];
                        maxW = parseFloat(maxW) > 0 ? subtract2(maxW) : void 0;
                        return {
                            _minW: subtract2(minW),
                            breakpoint,
                            minW,
                            maxW,
                            maxWQuery: toMediaQueryString(null, maxW),
                            minWQuery: toMediaQueryString(minW),
                            minMaxQuery: toMediaQueryString(minW, maxW)
                        };
                    });
                    const _keys = keys(breakpoints);
                    const _keysArr = Array.from(_keys.values());
                    return {
                        keys: _keys,
                        normalized,
                        isResponsive(test) {
                            const keys2 = Object.keys(test);
                            return keys2.length > 0 && keys2.every((key) => _keys.has(key));
                        },
                        asObject: sortBps(breakpoints),
                        asArray: normalize(breakpoints),
                        details: queries,
                        get(key) {
                            return queries.find((q) => q.breakpoint === key);
                        },
                        media: [
                            null,
                            ...normalized.map((minW) => toMediaQueryString(minW)).slice(1)
                        ],
                        /**
                         * Converts the object responsive syntax to array syntax
                         *
                         * @example
                         * toArrayValue({ base: 1, sm: 2, md: 3 }) // => [1, 2, 3]
                         */
                        toArrayValue(test) {
                            if (!(0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(test)) {
                                throw new Error("toArrayValue: value must be an object");
                            }
                            const result = _keysArr.map((bp) => {
                                var _a2;
                                return (_a2 = test[bp]) != null ? _a2 : null;
                            });
                            while (getLastItem(result) === null) {
                                result.pop();
                            }
                            return result;
                        },
                        /**
                         * Converts the array responsive syntax to object syntax
                         *
                         * @example
                         * toObjectValue([1, 2, 3]) // => { base: 1, sm: 2, md: 3 }
                         */
                        toObjectValue(test) {
                            if (!Array.isArray(test)) {
                                throw new Error("toObjectValue: value must be an array");
                            }
                            return test.reduce((acc, value, index) => {
                                const key = _keysArr[index];
                                if (key != null && value != null)
                                    acc[key] = value;
                                return acc;
                            }, {});
                        }
                    };
                }

                // src/create-theme-vars/create-theme-vars.ts


                // src/pseudos.ts
                var state = {
                    hover: (str, post) => `${str}:hover ${post}, ${str}[data-hover] ${post}`,
                    focus: (str, post) => `${str}:focus ${post}, ${str}[data-focus] ${post}`,
                    focusVisible: (str, post) => `${str}:focus-visible ${post}`,
                    focusWithin: (str, post) => `${str}:focus-within ${post}`,
                    active: (str, post) => `${str}:active ${post}, ${str}[data-active] ${post}`,
                    disabled: (str, post) => `${str}:disabled ${post}, ${str}[data-disabled] ${post}`,
                    invalid: (str, post) => `${str}:invalid ${post}, ${str}[data-invalid] ${post}`,
                    checked: (str, post) => `${str}:checked ${post}, ${str}[data-checked] ${post}`,
                    indeterminate: (str, post) => `${str}:indeterminate ${post}, ${str}[aria-checked=mixed] ${post}, ${str}[data-indeterminate] ${post}`,
                    readOnly: (str, post) => `${str}:read-only ${post}, ${str}[readonly] ${post}, ${str}[data-read-only] ${post}`,
                    expanded: (str, post) => `${str}:read-only ${post}, ${str}[aria-expanded=true] ${post}, ${str}[data-expanded] ${post}`,
                    placeholderShown: (str, post) => `${str}:placeholder-shown ${post}`
                };
                var toGroup = (fn) => merge((v) => fn(v, "&"), "[role=group]", "[data-group]", ".group");
                var toPeer = (fn) => merge((v) => fn(v, "~ &"), "[data-peer]", ".peer");
                var merge = (fn, ...selectors) => selectors.map(fn).join(", ");
                var pseudoSelectors = {
                    /**
                     * Styles for CSS selector `&:hover`
                     */
                    _hover: "&:hover, &[data-hover]",
                    /**
                     * Styles for CSS Selector `&:active`
                     */
                    _active: "&:active, &[data-active]",
                    /**
                     * Styles for CSS selector `&:focus`
                     *
                     */
                    _focus: "&:focus, &[data-focus]",
                    /**
                     * Styles for the highlighted state.
                     */
                    _highlighted: "&[data-highlighted]",
                    /**
                     * Styles to apply when a child of this element has received focus
                     * - CSS Selector `&:focus-within`
                     */
                    _focusWithin: "&:focus-within",
                    /**
                     * Styles to apply when this element has received focus via tabbing
                     * - CSS Selector `&:focus-visible`
                     */
                    _focusVisible: "&:focus-visible, &[data-focus-visible]",
                    /**
                     * Styles to apply when this element is disabled. The passed styles are applied to these CSS selectors:
                     * - `&[aria-disabled=true]`
                     * - `&:disabled`
                     * - `&[data-disabled]`
                     * - `&[disabled]`
                     */
                    _disabled: "&:disabled, &[disabled], &[aria-disabled=true], &[data-disabled]",
                    /**
                     * Styles for CSS Selector `&:readonly`
                     */
                    _readOnly: "&[aria-readonly=true], &[readonly], &[data-readonly]",
                    /**
                     * Styles for CSS selector `&::before`
                     *
                     * NOTE:When using this, ensure the `content` is wrapped in a backtick.
                     * @example
                     * ```jsx
                     * <Box _before={{content:`""` }}/>
                     * ```
                     */
                    _before: "&::before",
                    /**
                     * Styles for CSS selector `&::after`
                     *
                     * NOTE:When using this, ensure the `content` is wrapped in a backtick.
                     * @example
                     * ```jsx
                     * <Box _after={{content:`""` }}/>
                     * ```
                     */
                    _after: "&::after",
                    /**
                     * Styles for CSS selector `&:empty`
                     */
                    _empty: "&:empty",
                    /**
                     * Styles to apply when the ARIA attribute `aria-expanded` is `true`
                     * - CSS selector `&[aria-expanded=true]`
                     */
                    _expanded: "&[aria-expanded=true], &[data-expanded]",
                    /**
                     * Styles to apply when the ARIA attribute `aria-checked` is `true`
                     * - CSS selector `&[aria-checked=true]`
                     */
                    _checked: "&[aria-checked=true], &[data-checked]",
                    /**
                     * Styles to apply when the ARIA attribute `aria-grabbed` is `true`
                     * - CSS selector `&[aria-grabbed=true]`
                     */
                    _grabbed: "&[aria-grabbed=true], &[data-grabbed]",
                    /**
                     * Styles for CSS Selector `&[aria-pressed=true]`
                     * Typically used to style the current "pressed" state of toggle buttons
                     */
                    _pressed: "&[aria-pressed=true], &[data-pressed]",
                    /**
                     * Styles to apply when the ARIA attribute `aria-invalid` is `true`
                     * - CSS selector `&[aria-invalid=true]`
                     */
                    _invalid: "&[aria-invalid=true], &[data-invalid]",
                    /**
                     * Styles for the valid state
                     * - CSS selector `&[data-valid], &[data-state=valid]`
                     */
                    _valid: "&[data-valid], &[data-state=valid]",
                    /**
                     * Styles for CSS Selector `&[aria-busy=true]` or `&[data-loading=true]`.
                     * Useful for styling loading states
                     */
                    _loading: "&[data-loading], &[aria-busy=true]",
                    /**
                     * Styles to apply when the ARIA attribute `aria-selected` is `true`
                     *
                     * - CSS selector `&[aria-selected=true]`
                     */
                    _selected: "&[aria-selected=true], &[data-selected]",
                    /**
                     * Styles for CSS Selector `[hidden=true]`
                     */
                    _hidden: "&[hidden], &[data-hidden]",
                    /**
                     * Styles for CSS Selector `&:-webkit-autofill`
                     */
                    _autofill: "&:-webkit-autofill",
                    /**
                     * Styles for CSS Selector `&:nth-child(even)`
                     */
                    _even: "&:nth-of-type(even)",
                    /**
                     * Styles for CSS Selector `&:nth-child(odd)`
                     */
                    _odd: "&:nth-of-type(odd)",
                    /**
                     * Styles for CSS Selector `&:first-of-type`
                     */
                    _first: "&:first-of-type",
                    /**
                     * Styles for CSS selector `&::first-letter`
                     *
                     * NOTE: This selector is only applied for block-level elements and not preceded by an image or table.
                     * @example
                     * ```jsx
                     * <Text _firstLetter={{ textDecoration: 'underline' }}>Once upon a time</Text>
                     * ```
                     */
                    _firstLetter: "&::first-letter",
                    /**
                     * Styles for CSS Selector `&:last-of-type`
                     */
                    _last: "&:last-of-type",
                    /**
                     * Styles for CSS Selector `&:not(:first-of-type)`
                     */
                    _notFirst: "&:not(:first-of-type)",
                    /**
                     * Styles for CSS Selector `&:not(:last-of-type)`
                     */
                    _notLast: "&:not(:last-of-type)",
                    /**
                     * Styles for CSS Selector `&:visited`
                     */
                    _visited: "&:visited",
                    /**
                     * Used to style the active link in a navigation
                     * Styles for CSS Selector `&[aria-current=page]`
                     */
                    _activeLink: "&[aria-current=page]",
                    /**
                     * Used to style the current step within a process
                     * Styles for CSS Selector `&[aria-current=step]`
                     */
                    _activeStep: "&[aria-current=step]",
                    /**
                     * Styles to apply when the ARIA attribute `aria-checked` is `mixed`
                     * - CSS selector `&[aria-checked=mixed]`
                     */
                    _indeterminate: "&:indeterminate, &[aria-checked=mixed], &[data-indeterminate]",
                    /**
                     * Styles to apply when a parent element with `.group`, `data-group` or `role=group` is hovered
                     */
                    _groupHover: toGroup(state.hover),
                    /**
                     * Styles to apply when a sibling element with `.peer` or `data-peer` is hovered
                     */
                    _peerHover: toPeer(state.hover),
                    /**
                     * Styles to apply when a parent element with `.group`, `data-group` or `role=group` is focused
                     */
                    _groupFocus: toGroup(state.focus),
                    /**
                     * Styles to apply when a sibling element with `.peer` or `data-peer` is focused
                     */
                    _peerFocus: toPeer(state.focus),
                    /**
                     * Styles to apply when a parent element with `.group`, `data-group` or `role=group` has visible focus
                     */
                    _groupFocusVisible: toGroup(state.focusVisible),
                    /**
                     * Styles to apply when a sibling element with `.peer`or `data-peer` has visible focus
                     */
                    _peerFocusVisible: toPeer(state.focusVisible),
                    /**
                     * Styles to apply when a parent element with `.group`, `data-group` or `role=group` is active
                     */
                    _groupActive: toGroup(state.active),
                    /**
                     * Styles to apply when a sibling element with `.peer` or `data-peer` is active
                     */
                    _peerActive: toPeer(state.active),
                    /**
                     * Styles to apply when a parent element with `.group`, `data-group` or `role=group` is disabled
                     */
                    _groupDisabled: toGroup(state.disabled),
                    /**
                     *  Styles to apply when a sibling element with `.peer` or `data-peer` is disabled
                     */
                    _peerDisabled: toPeer(state.disabled),
                    /**
                     *  Styles to apply when a parent element with `.group`, `data-group` or `role=group` is invalid
                     */
                    _groupInvalid: toGroup(state.invalid),
                    /**
                     *  Styles to apply when a sibling element with `.peer` or `data-peer` is invalid
                     */
                    _peerInvalid: toPeer(state.invalid),
                    /**
                     * Styles to apply when a parent element with `.group`, `data-group` or `role=group` is checked
                     */
                    _groupChecked: toGroup(state.checked),
                    /**
                     * Styles to apply when a sibling element with `.peer` or `data-peer` is checked
                     */
                    _peerChecked: toPeer(state.checked),
                    /**
                     *  Styles to apply when a parent element with `.group`, `data-group` or `role=group` has focus within
                     */
                    _groupFocusWithin: toGroup(state.focusWithin),
                    /**
                     *  Styles to apply when a sibling element with `.peer` or `data-peer` has focus within
                     */
                    _peerFocusWithin: toPeer(state.focusWithin),
                    /**
                     * Styles to apply when a sibling element with `.peer` or `data-peer` has placeholder shown
                     */
                    _peerPlaceholderShown: toPeer(state.placeholderShown),
                    /**
                     * Styles for CSS Selector `&::placeholder`.
                     */
                    _placeholder: "&::placeholder",
                    /**
                     * Styles for CSS Selector `&:placeholder-shown`.
                     */
                    _placeholderShown: "&:placeholder-shown",
                    /**
                     * Styles for CSS Selector `&:fullscreen`.
                     */
                    _fullScreen: "&:fullscreen",
                    /**
                     * Styles for CSS Selector `&::selection`
                     */
                    _selection: "&::selection",
                    /**
                     * Styles for CSS Selector `[dir=rtl] &`
                     * It is applied when a parent element or this element has `dir="rtl"`
                     */
                    _rtl: "[dir=rtl] &, &[dir=rtl]",
                    /**
                     * Styles for CSS Selector `[dir=ltr] &`
                     * It is applied when a parent element or this element has `dir="ltr"`
                     */
                    _ltr: "[dir=ltr] &, &[dir=ltr]",
                    /**
                     * Styles for CSS Selector `@media (prefers-color-scheme: dark)`
                     * It is used when the user has requested the system use a light or dark color theme.
                     */
                    _mediaDark: "@media (prefers-color-scheme: dark)",
                    /**
                     * Styles for CSS Selector `@media (prefers-reduced-motion: reduce)`
                     * It is used when the user has requested the system to reduce the amount of animations.
                     */
                    _mediaReduceMotion: "@media (prefers-reduced-motion: reduce)",
                    /**
                     * Styles for when `data-theme` is applied to any parent of
                     * this component or element.
                     */
                    _dark: ".chakra-ui-dark &:not([data-theme]),[data-theme=dark] &:not([data-theme]),&[data-theme=dark]",
                    /**
                     * Styles for when `data-theme` is applied to any parent of
                     * this component or element.
                     */
                    _light: ".chakra-ui-light &:not([data-theme]),[data-theme=light] &:not([data-theme]),&[data-theme=light]",
                    /**
                     * Styles for the CSS Selector `&[data-orientation=horizontal]`
                     */
                    _horizontal: "&[data-orientation=horizontal]",
                    /**
                     * Styles for the CSS Selector `&[data-orientation=vertical]`
                     */
                    _vertical: "&[data-orientation=vertical]"
                };
                var pseudoPropNames = Object.keys(
                    pseudoSelectors
                );

                // src/create-theme-vars/create-theme-vars.ts

                function tokenToCssVar(token, prefix) {
                    return cssVar(String(token).replace(/\./g, "-"), void 0, prefix);
                }

                function createThemeVars(flatTokens, options) {
                    let cssVars = {};
                    const cssMap = {};
                    for (const [token, tokenValue] of Object.entries(flatTokens)) {
                        const {
                            isSemantic,
                            value
                        } = tokenValue;
                        const {
                            variable,
                            reference
                        } = tokenToCssVar(token, options == null ? void 0 : options.cssVarPrefix);
                        if (!isSemantic) {
                            if (token.startsWith("space")) {
                                const keys2 = token.split(".");
                                const [firstKey, ...referenceKeys] = keys2;
                                const negativeLookupKey = `${firstKey}.-${referenceKeys.join(".")}`;
                                const negativeValue = calc.negate(value);
                                const negatedReference = calc.negate(reference);
                                cssMap[negativeLookupKey] = {
                                    value: negativeValue,
                                    var: variable,
                                    varRef: negatedReference
                                };
                            }
                            cssVars[variable] = value;
                            cssMap[token] = {
                                value,
                                var: variable,
                                varRef: reference
                            };
                            continue;
                        }
                        const lookupToken = (maybeToken) => {
                            const scale = String(token).split(".")[0];
                            const withScale = [scale, maybeToken].join(".");
                            const resolvedTokenValue = flatTokens[withScale];
                            if (!resolvedTokenValue)
                                return maybeToken;
                            const {
                                reference: reference2
                            } = tokenToCssVar(withScale, options == null ? void 0 : options.cssVarPrefix);
                            return reference2;
                        };
                        const normalizedValue = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(value) ? value : {
                            default: value
                        };
                        cssVars = lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__(
                            cssVars,
                            Object.entries(normalizedValue).reduce(
                                (acc, [conditionAlias, conditionValue]) => {
                                    var _a, _b;
                                    if (!conditionValue)
                                        return acc;
                                    const tokenReference = lookupToken(`${conditionValue}`);
                                    if (conditionAlias === "default") {
                                        acc[variable] = tokenReference;
                                        return acc;
                                    }
                                    const conditionSelector = (_b = (_a = pseudoSelectors) == null ? void 0 : _a[conditionAlias]) != null ? _b : conditionAlias;
                                    acc[conditionSelector] = {
                                        [variable]: tokenReference
                                    };
                                    return acc;
                                }, {}
                            )
                        );
                        cssMap[token] = {
                            value: reference,
                            var: variable,
                            varRef: reference
                        };
                    }
                    return {
                        cssVars,
                        cssMap
                    };
                }

                // ../../utilities/object-utils/src/omit.ts
                function omit(object, keysToOmit = []) {
                    const clone = Object.assign({}, object);
                    for (const key of keysToOmit) {
                        if (key in clone) {
                            delete clone[key];
                        }
                    }
                    return clone;
                }

                // ../../utilities/object-utils/src/pick.ts
                function pick(object, keysToPick) {
                    const result = {};
                    for (const key of keysToPick) {
                        if (key in object) {
                            result[key] = object[key];
                        }
                    }
                    return result;
                }

                // ../../utilities/object-utils/src/walk-object.ts
                function isObject5(value) {
                    return typeof value === "object" && value != null && !Array.isArray(value);
                }

                function walkObject(target, predicate, options = {}) {
                    const {
                        stop,
                        getKey
                    } = options;

                    function inner(value, path = []) {
                        var _a;
                        if (isObject5(value) || Array.isArray(value)) {
                            const result = {};
                            for (const [prop, child] of Object.entries(value)) {
                                const key = (_a = getKey == null ? void 0 : getKey(prop)) != null ? _a : prop;
                                const childPath = [...path, key];
                                if (stop == null ? void 0 : stop(value, childPath)) {
                                    return predicate(value, path);
                                }
                                result[key] = inner(child, childPath);
                            }
                            return result;
                        }
                        return predicate(value, path);
                    }
                    return inner(target);
                }

                // src/create-theme-vars/theme-tokens.ts
                var tokens = [
                    "colors",
                    "borders",
                    "borderWidths",
                    "borderStyles",
                    "fonts",
                    "fontSizes",
                    "fontWeights",
                    "gradients",
                    "letterSpacings",
                    "lineHeights",
                    "radii",
                    "space",
                    "shadows",
                    "sizes",
                    "zIndices",
                    "transition",
                    "blur",
                    "breakpoints"
                ];

                function extractTokens(theme) {
                    const _tokens = tokens;
                    return pick(theme, _tokens);
                }

                function extractSemanticTokens(theme) {
                    return theme.semanticTokens;
                }

                function omitVars(rawTheme) {
                    const {
                        __cssMap,
                        __cssVars,
                        __breakpoints,
                        ...cleanTheme
                    } = rawTheme;
                    return cleanTheme;
                }

                // src/create-theme-vars/flatten-tokens.ts
                var isSemanticCondition = (key) => pseudoPropNames.includes(key) || "default" === key;

                function flattenTokens({
                    tokens: tokens2,
                    semanticTokens
                }) {
                    const result = {};
                    walkObject(tokens2, (value, path) => {
                        if (value == null)
                            return;
                        result[path.join(".")] = {
                            isSemantic: false,
                            value
                        };
                    });
                    walkObject(
                        semanticTokens,
                        (value, path) => {
                            if (value == null)
                                return;
                            result[path.join(".")] = {
                                isSemantic: true,
                                value
                            };
                        }, {
                            stop: (value) => Object.keys(value).every(isSemanticCondition)
                        }
                    );
                    return result;
                }

                // src/create-theme-vars/to-css-var.ts
                function toCSSVar(rawTheme) {
                    var _a;
                    const theme = omitVars(rawTheme);
                    const tokens2 = extractTokens(theme);
                    const semanticTokens = extractSemanticTokens(theme);
                    const flatTokens = flattenTokens({
                        tokens: tokens2,
                        semanticTokens
                    });
                    const cssVarPrefix = (_a = theme.config) == null ? void 0 : _a.cssVarPrefix;
                    const {
                        /**
                         * This is more like a dictionary of tokens users will type `green.500`,
                         * and their equivalent css variable.
                         */
                        cssMap,
                        /**
                         * The extracted css variables will be stored here, and used in
                         * the emotion's <Global/> component to attach variables to `:root`
                         */
                        cssVars
                    } = createThemeVars(flatTokens, {
                        cssVarPrefix
                    });
                    const defaultCssVars = {
                        "--chakra-ring-inset": "var(--chakra-empty,/*!*/ /*!*/)",
                        "--chakra-ring-offset-width": "0px",
                        "--chakra-ring-offset-color": "#fff",
                        "--chakra-ring-color": "rgba(66, 153, 225, 0.6)",
                        "--chakra-ring-offset-shadow": "0 0 #0000",
                        "--chakra-ring-shadow": "0 0 #0000",
                        "--chakra-space-x-reverse": "0",
                        "--chakra-space-y-reverse": "0"
                    };
                    Object.assign(theme, {
                        __cssVars: { ...defaultCssVars,
                            ...cssVars
                        },
                        __cssMap: cssMap,
                        __breakpoints: analyzeBreakpoints(theme.breakpoints)
                    });
                    return theme;
                }

                // src/css.ts



                // src/system.ts

                var systemProps = lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__({},
                    background,
                    border,
                    color,
                    flexbox,
                    layout,
                    filter,
                    ring,
                    interactivity,
                    grid,
                    others,
                    position,
                    effect,
                    space,
                    scroll,
                    typography,
                    textDecoration,
                    transform,
                    list,
                    transition
                );
                var layoutSystem = Object.assign({}, space, layout, flexbox, grid, position);
                var layoutPropNames = Object.keys(
                    layoutSystem
                );
                var propNames = [...Object.keys(systemProps), ...pseudoPropNames];
                var styleProps = { ...systemProps,
                    ...pseudoSelectors
                };
                var isStyleProp = (prop) => prop in styleProps;

                // src/utils/expand-responsive.ts

                var expandResponsive = (styles) => (theme) => {
                    if (!theme.__breakpoints)
                        return styles;
                    const {
                        isResponsive,
                        toArrayValue,
                        media: medias
                    } = theme.__breakpoints;
                    const computedStyles = {};
                    for (const key in styles) {
                        let value = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.runIfFn)(styles[key], theme);
                        if (value == null)
                            continue;
                        value = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(value) && isResponsive(value) ? toArrayValue(value) : value;
                        if (!Array.isArray(value)) {
                            computedStyles[key] = value;
                            continue;
                        }
                        const queries = value.slice(0, medias.length).length;
                        for (let index = 0; index < queries; index += 1) {
                            const media = medias == null ? void 0 : medias[index];
                            if (!media) {
                                computedStyles[key] = value[index];
                                continue;
                            }
                            computedStyles[media] = computedStyles[media] || {};
                            if (value[index] == null) {
                                continue;
                            }
                            computedStyles[media][key] = value[index];
                        }
                    }
                    return computedStyles;
                };

                // src/utils/split-by-comma.ts
                function splitByComma(value) {
                    const chunks = [];
                    let chunk = "";
                    let inParens = false;
                    for (let i = 0; i < value.length; i++) {
                        const char = value[i];
                        if (char === "(") {
                            inParens = true;
                            chunk += char;
                        } else if (char === ")") {
                            inParens = false;
                            chunk += char;
                        } else if (char === "," && !inParens) {
                            chunks.push(chunk);
                            chunk = "";
                        } else {
                            chunk += char;
                        }
                    }
                    chunk = chunk.trim();
                    if (chunk) {
                        chunks.push(chunk);
                    }
                    return chunks;
                }

                // src/css.ts
                function isCssVar2(value) {
                    return /^var\(--.+\)$/.test(value);
                }
                var isCSSVariableTokenValue = (key, value) => key.startsWith("--") && typeof value === "string" && !isCssVar2(value);
                var resolveTokenValue = (theme, value) => {
                    var _a, _b;
                    if (value == null)
                        return value;
                    const getVar = (val) => {
                        var _a2, _b2;
                        return (_b2 = (_a2 = theme.__cssMap) == null ? void 0 : _a2[val]) == null ? void 0 : _b2.varRef;
                    };
                    const getValue = (val) => {
                        var _a2;
                        return (_a2 = getVar(val)) != null ? _a2 : val;
                    };
                    const [tokenValue, fallbackValue] = splitByComma(value);
                    value = (_b = (_a = getVar(tokenValue)) != null ? _a : getValue(fallbackValue)) != null ? _b : getValue(value);
                    return value;
                };

                function getCss(options) {
                    const {
                        configs = {}, pseudos = {}, theme
                    } = options;
                    const css2 = (stylesOrFn, nested = false) => {
                        var _a, _b, _c;
                        const _styles = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.runIfFn)(stylesOrFn, theme);
                        const styles = expandResponsive(_styles)(theme);
                        let computedStyles = {};
                        for (let key in styles) {
                            const valueOrFn = styles[key];
                            let value = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.runIfFn)(valueOrFn, theme);
                            if (key in pseudos) {
                                key = pseudos[key];
                            }
                            if (isCSSVariableTokenValue(key, value)) {
                                value = resolveTokenValue(theme, value);
                            }
                            let config = configs[key];
                            if (config === true) {
                                config = {
                                    property: key
                                };
                            }
                            if ((0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(value)) {
                                computedStyles[key] = (_a = computedStyles[key]) != null ? _a : {};
                                computedStyles[key] = lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__({},
                                    computedStyles[key],
                                    css2(value, true)
                                );
                                continue;
                            }
                            let rawValue = (_c = (_b = config == null ? void 0 : config.transform) == null ? void 0 : _b.call(config, value, theme, _styles)) != null ? _c : value;
                            rawValue = (config == null ? void 0 : config.processResult) ? css2(rawValue, true) : rawValue;
                            const configProperty = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.runIfFn)(config == null ? void 0 : config.property, theme);
                            if (!nested && (config == null ? void 0 : config.static)) {
                                const staticStyles = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.runIfFn)(config.static, theme);
                                computedStyles = lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__({}, computedStyles, staticStyles);
                            }
                            if (configProperty && Array.isArray(configProperty)) {
                                for (const property of configProperty) {
                                    computedStyles[property] = rawValue;
                                }
                                continue;
                            }
                            if (configProperty) {
                                if (configProperty === "&" && (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(rawValue)) {
                                    computedStyles = lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__({}, computedStyles, rawValue);
                                } else {
                                    computedStyles[configProperty] = rawValue;
                                }
                                continue;
                            }
                            if ((0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(rawValue)) {
                                computedStyles = lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__({}, computedStyles, rawValue);
                                continue;
                            }
                            computedStyles[key] = rawValue;
                        }
                        return computedStyles;
                    };
                    return css2;
                }
                var css = (styles) => (theme) => {
                    const cssFn = getCss({
                        theme,
                        pseudos: pseudoSelectors,
                        configs: systemProps
                    });
                    return cssFn(styles);
                };

                // src/define-styles.ts
                function defineStyle(styles) {
                    return styles;
                }

                function defineStyleConfig(config) {
                    return config;
                }

                function createMultiStyleConfigHelpers(parts) {
                    return {
                        definePartsStyle(config) {
                            return config;
                        },
                        defineMultiStyleConfig(config) {
                            return {
                                parts,
                                ...config
                            };
                        }
                    };
                }

                // src/style-config.ts


                function normalize2(value, toArray) {
                    if (Array.isArray(value))
                        return value;
                    if ((0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(value))
                        return toArray(value);
                    if (value != null)
                        return [value];
                }

                function getNextIndex(values, i) {
                    for (let j = i + 1; j < values.length; j++) {
                        if (values[j] != null)
                            return j;
                    }
                    return -1;
                }

                function createResolver(theme) {
                    const breakpointUtil = theme.__breakpoints;
                    return function resolver(config, prop, value, props) {
                        var _a, _b;
                        if (!breakpointUtil)
                            return;
                        const result = {};
                        const normalized = normalize2(value, breakpointUtil.toArrayValue);
                        if (!normalized)
                            return result;
                        const len = normalized.length;
                        const isSingle = len === 1;
                        const isMultipart = !!config.parts;
                        for (let i = 0; i < len; i++) {
                            const key = breakpointUtil.details[i];
                            const nextKey = breakpointUtil.details[getNextIndex(normalized, i)];
                            const query = toMediaQueryString(key.minW, nextKey == null ? void 0 : nextKey._minW);
                            const styles = (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.runIfFn)((_a = config[prop]) == null ? void 0 : _a[normalized[i]], props);
                            if (!styles)
                                continue;
                            if (isMultipart) {
                                (_b = config.parts) == null ? void 0 : _b.forEach((part) => {
                                    lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__(result, {
                                        [part]: isSingle ? styles[part] : {
                                            [query]: styles[part]
                                        }
                                    });
                                });
                                continue;
                            }
                            if (!isMultipart) {
                                if (isSingle)
                                    lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__(result, styles);
                                else
                                    result[query] = styles;
                                continue;
                            }
                            result[query] = styles;
                        }
                        return result;
                    };
                }

                function resolveStyleConfig(config) {
                    return (props) => {
                        var _a;
                        const {
                            variant,
                            size,
                            theme
                        } = props;
                        const recipe = createResolver(theme);
                        return lodash_mergewith__WEBPACK_IMPORTED_MODULE_1__({},
                            (0, _chakra_ui_shared_utils__WEBPACK_IMPORTED_MODULE_0__.runIfFn)((_a = config.baseStyle) != null ? _a : {}, props),
                            recipe(config, "sizes", size, props),
                            recipe(config, "variants", variant, props)
                        );
                    };
                }

                // src/get-css-var.ts
                function getCSSVar(theme, scale, value) {
                    var _a, _b, _c;
                    return (_c = (_b = (_a = theme.__cssMap) == null ? void 0 : _a[`${scale}.${value}`]) == null ? void 0 : _b.varRef) != null ? _c : value;
                }

                // src/theming-props.ts
                function omitThemingProps(props) {
                    return omit(props, ["styleConfig", "size", "variant", "colorScheme"]);
                }



                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_chakra-ui_anatomy_dist_chunk-OA3DH5LS_mjs-node_modules_chakra-ui_styled--bf4cd7.9856e15b0268a7ab.js.map