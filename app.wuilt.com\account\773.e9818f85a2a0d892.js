/*! For license information please see 773.e9818f85a2a0d892.js.LICENSE.txt */
(self.webpackChunkaccount = self.webpackChunkaccount || []).push([
    [773, 9500], {
        90773: (e, t, n) => {
            n.r(t), n.d(t, {
                default: () => d,
                getRoutes: () => p
            });
            var a = n(80755),
                l = n(26415),
                o = n(69500);
            const r = (0, l.lazy)((() => Promise.all([n.e(9004), n.e(9434), n.e(8245), n.e(2810), n.e(5927)]).then(n.bind(n, 25927)))),
                s = (0, l.lazy)((() => Promise.all([n.e(9434), n.e(766)]).then(n.bind(n, 30766)).then((e => ({
                    default: e.<PERSON>ontaine<PERSON>
                }))))),
                h = (0, l.lazy)((() => Promise.all([n.e(2214), n.e(9434), n.e(6947), n.e(514), n.e(8245), n.e(4002), n.e(3164)]).then(n.bind(n, 33164)).then((e => ({
                    default: e.AccountSettings
                }))))),
                c = (0, l.lazy)((() => Promise.all([n.e(7737), n.e(2214), n.e(4029), n.e(9434), n.e(6947), n.e(1675), n.e(514), n.e(8245), n.e(4002), n.e(8845)]).then(n.bind(n, 78845)).then((e => ({
                    default: e.Login
                }))))),
                i = (0, l.lazy)((() => Promise.all([n.e(7737), n.e(2214), n.e(4029), n.e(9434), n.e(6947), n.e(1675), n.e(514), n.e(8245), n.e(4002), n.e(8022)]).then(n.bind(n, 98022)).then((e => ({
                    default: e.Signup
                }))))),
                u = (0, l.lazy)((() => Promise.all([n.e(2214), n.e(9434), n.e(6947), n.e(514), n.e(8245), n.e(4002), n.e(1897)]).then(n.bind(n, 31897)).then((e => ({
                    default: e.ChangePhone
                })))));

            function p() {
                return [{
                    path: "/",
                    element: (0, o.jsx)(r, {
                        children: (0, o.jsx)(a.Navigate, {
                            to: "/account"
                        })
                    }),
                    type: "shell"
                }, {
                    path: "/account",
                    element: (0, o.jsx)(s, {
                        children: (0, o.jsx)(r, {
                            children: (0, o.jsx)(h, {})
                        })
                    }),
                    type: "shell"
                }, {
                    path: "/account/*",
                    element: (0, o.jsx)(s, {
                        children: (0, o.jsx)(r, {
                            children: (0, o.jsxs)(a.Routes, {
                                children: [(0, o.jsx)(a.Route, {
                                    path: "login",
                                    element: (0, o.jsx)(c, {})
                                }), (0, o.jsx)(a.Route, {
                                    path: "signup",
                                    element: (0, o.jsx)(i, {})
                                }), (0, o.jsx)(a.Route, {
                                    path: "change-phone",
                                    element: (0, o.jsx)(u, {})
                                }), (0, o.jsx)(a.Route, {
                                    path: "*",
                                    element: (0, o.jsx)(a.Navigate, {
                                        to: "/",
                                        replace: !0
                                    })
                                })]
                            })
                        })
                    }),
                    type: "unauthenticated"
                }]
            }
            const d = p
        },
        68736: (e, t, n) => {
            var a = n(59243),
                l = Symbol.for("react.element"),
                o = Symbol.for("react.fragment"),
                r = Object.prototype.hasOwnProperty,
                s = a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,
                h = {
                    key: !0,
                    ref: !0,
                    __self: !0,
                    __source: !0
                };

            function c(e, t, n) {
                var a, o = {},
                    c = null,
                    i = null;
                for (a in void 0 !== n && (c = "" + n), void 0 !== t.key && (c = "" + t.key), void 0 !== t.ref && (i = t.ref), t) r.call(t, a) && !h.hasOwnProperty(a) && (o[a] = t[a]);
                if (e && e.defaultProps)
                    for (a in t = e.defaultProps) void 0 === o[a] && (o[a] = t[a]);
                return {
                    $$typeof: l,
                    type: e,
                    key: c,
                    ref: i,
                    props: o,
                    _owner: s.current
                }
            }
            t.Fragment = o, t.jsx = c, t.jsxs = c
        },
        69500: (e, t, n) => {
            e.exports = n(68736)
        }
    }
]);
//# sourceMappingURL=773.e9818f85a2a0d892.js.map