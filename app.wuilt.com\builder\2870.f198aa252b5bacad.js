(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [2870], {
        62870: (e, t, r) => {
            r.r(t), r.d(t, {
                customAlphabet: () => a,
                customRandom: () => o,
                nanoid: () => u,
                random: () => l,
                urlAlphabet: () => n
            });
            const n = "useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";
            let l = e => crypto.getRandomValues(new Uint8Array(e)),
                o = (e, t, r) => {
                    let n = (2 << Math.log2(e.length - 1)) - 1,
                        l = -~(1.6 * n * t / e.length);
                    return (o = t) => {
                        let a = "";
                        for (;;) {
                            let t = r(l),
                                u = 0 | l;
                            for (; u--;)
                                if (a += e[t[u] & n] || "", a.length >= o) return a
                        }
                    }
                },
                a = (e, t = 21) => o(e, 0 | t, l),
                u = (e = 21) => {
                    let t = "",
                        r = crypto.getRandomValues(new Uint8Array(e |= 0));
                    for (; e--;) t += n[63 & r[e]];
                    return t
                }
        }
    }
]);
//# sourceMappingURL=2870.f198aa252b5bacad.js.map