(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [3882], {
        23882: (e, r, t) => {
            t.r(r), t.d(r, {
                default: () => v
            });
            var n = function() {
                    function e(e) {
                        var r = this;
                        this._insertTag = function(e) {
                            var t;
                            t = 0 === r.tags.length ? r.insertionPoint ? r.insertionPoint.nextSibling : r.prepend ? r.container.firstChild : r.before : r.tags[r.tags.length - 1].nextSibling, r.container.insertBefore(e, t), r.tags.push(e)
                        }, this.isSpeedy = void 0 === e.speedy || e.speedy, this.tags = [], this.ctr = 0, this.nonce = e.nonce, this.key = e.key, this.container = e.container, this.prepend = e.prepend, this.insertionPoint = e.insertionPoint, this.before = null
                    }
                    var r = e.prototype;
                    return r.hydrate = function(e) {
                        e.forEach(this._insertTag)
                    }, r.insert = function(e) {
                        this.ctr % (this.isSpeedy ? 65e3 : 1) == 0 && this._insertTag(function(e) {
                            var r = document.createElement("style");
                            return r.setAttribute("data-emotion", e.key), void 0 !== e.nonce && r.setAttribute("nonce", e.nonce), r.appendChild(document.createTextNode("")), r.setAttribute("data-s", ""), r
                        }(this));
                        var r = this.tags[this.tags.length - 1];
                        if (this.isSpeedy) {
                            var t = function(e) {
                                if (e.sheet) return e.sheet;
                                for (var r = 0; r < document.styleSheets.length; r++)
                                    if (document.styleSheets[r].ownerNode === e) return document.styleSheets[r]
                            }(r);
                            try {
                                t.insertRule(e, t.cssRules.length)
                            } catch (e) {}
                        } else r.appendChild(document.createTextNode(e));
                        this.ctr++
                    }, r.flush = function() {
                        this.tags.forEach((function(e) {
                            return e.parentNode && e.parentNode.removeChild(e)
                        })), this.tags = [], this.ctr = 0
                    }, e
                }(),
                a = t(35893),
                s = t(36099),
                c = t(81666),
                u = t(94433),
                i = t(96001);

            function o(e) {
                return (0, a.cE)(l("", null, null, null, [""], e = (0, a.un)(e), 0, [0], e))
            }

            function l(e, r, t, n, c, u, i, o, $) {
                for (var h = 0, d = 0, x = i, b = 0, G = 0, v = 0, w = 1, S = 1, k = 1, y = 0, m = "", M = c, O = u, j = n, E = m; S;) switch (v = y, y = (0, a.lp)()) {
                    case 40:
                        if (108 != v && 58 == (0, s.uO)(E, x - 1)) {
                            -1 != (0, s.Cw)(E += (0, s.gx)((0, a.iF)(y), "&", "&\f"), "&\f") && (k = -1);
                            break
                        }
                    case 34:
                    case 39:
                    case 91:
                        E += (0, a.iF)(y);
                        break;
                    case 9:
                    case 10:
                    case 13:
                    case 32:
                        E += (0, a.Qb)(v);
                        break;
                    case 92:
                        E += (0, a.kq)((0, a.Ud)() - 1, 7);
                        continue;
                    case 47:
                        switch ((0, a.fj)()) {
                            case 42:
                            case 47:
                                (0, s.R3)(g((0, a.q6)((0, a.lp)(), (0, a.Ud)()), r, t), $);
                                break;
                            default:
                                E += "/"
                        }
                        break;
                    case 123 * w:
                        o[h++] = (0, s.to)(E) * k;
                    case 125 * w:
                    case 59:
                    case 0:
                        switch (y) {
                            case 0:
                            case 125:
                                S = 0;
                            case 59 + d:
                                -1 == k && (E = (0, s.gx)(E, /\f/g, "")), G > 0 && (0, s.to)(E) - x && (0, s.R3)(G > 32 ? p(E + ";", n, t, x - 1) : p((0, s.gx)(E, " ", "") + ";", n, t, x - 2), $);
                                break;
                            case 59:
                                E += ";";
                            default:
                                if ((0, s.R3)(j = f(E, r, t, h, d, c, o, m, M = [], O = [], x), u), 123 === y)
                                    if (0 === d) l(E, r, j, j, M, u, x, o, O);
                                    else switch (99 === b && 110 === (0, s.uO)(E, 3) ? 100 : b) {
                                        case 100:
                                        case 108:
                                        case 109:
                                        case 115:
                                            l(e, j, j, n && (0, s.R3)(f(e, j, j, 0, 0, c, o, m, c, M = [], x), O), c, O, x, o, n ? M : O);
                                            break;
                                        default:
                                            l(E, j, j, j, [""], O, 0, o, O)
                                    }
                        }
                        h = d = G = 0, w = k = 1, m = E = "", x = i;
                        break;
                    case 58:
                        x = 1 + (0, s.to)(E), G = v;
                    default:
                        if (w < 1)
                            if (123 == y) --w;
                            else if (125 == y && 0 == w++ && 125 == (0, a.mp)()) continue;
                        switch (E += (0, s.Dp)(y), y * w) {
                            case 38:
                                k = d > 0 ? 1 : (E += "\f", -1);
                                break;
                            case 44:
                                o[h++] = ((0, s.to)(E) - 1) * k, k = 1;
                                break;
                            case 64:
                                45 === (0, a.fj)() && (E += (0, a.iF)((0, a.lp)())), b = (0, a.fj)(), d = x = (0, s.to)(m = E += (0, a.QU)((0, a.Ud)())), y++;
                                break;
                            case 45:
                                45 === v && 2 == (0, s.to)(E) && (w = 0)
                        }
                }
                return u
            }

            function f(e, r, t, n, u, i, o, l, f, g, p) {
                for (var $ = u - 1, h = 0 === u ? i : [""], d = (0, s.Ei)(h), x = 0, b = 0, G = 0; x < n; ++x)
                    for (var v = 0, w = (0, s.tb)(e, $ + 1, $ = (0, s.Wn)(b = o[x])), S = e; v < d; ++v)(S = (0, s.fy)(b > 0 ? h[v] + " " + w : (0, s.gx)(w, /&\f/g, h[v]))) && (f[G++] = S);
                return (0, a.dH)(e, r, t, 0 === u ? c.Fr : l, f, g, p)
            }

            function g(e, r, t) {
                return (0, a.dH)(e, r, t, c.Ab, (0, s.Dp)((0, a.Tb)()), (0, s.tb)(e, 2, -2), 0)
            }

            function p(e, r, t, n) {
                return (0, a.dH)(e, r, t, c.h5, (0, s.tb)(e, 0, n), (0, s.tb)(e, n + 1, -1), n)
            }
            var $ = function(e, r, t) {
                    for (var n = 0, s = 0; n = s, s = (0, a.fj)(), 38 === n && 12 === s && (r[t] = 1), !(0, a.r)(s);)(0, a.lp)();
                    return (0, a.tP)(e, a.FK)
                },
                h = new WeakMap,
                d = function(e) {
                    if ("rule" === e.type && e.parent && !(e.length < 1)) {
                        for (var r = e.value, t = e.parent, n = e.column === t.column && e.line === t.line;
                            "rule" !== t.type;)
                            if (!(t = t.parent)) return;
                        if ((1 !== e.props.length || 58 === r.charCodeAt(0) || h.get(t)) && !n) {
                            h.set(e, !0);
                            for (var c = [], u = function(e, r) {
                                    return (0, a.cE)(function(e, r) {
                                        var t = -1,
                                            n = 44;
                                        do {
                                            switch ((0, a.r)(n)) {
                                                case 0:
                                                    38 === n && 12 === (0, a.fj)() && (r[t] = 1), e[t] += $(a.FK - 1, r, t);
                                                    break;
                                                case 2:
                                                    e[t] += (0, a.iF)(n);
                                                    break;
                                                case 4:
                                                    if (44 === n) {
                                                        e[++t] = 58 === (0, a.fj)() ? "&\f" : "", r[t] = e[t].length;
                                                        break
                                                    }
                                                default:
                                                    e[t] += (0, s.Dp)(n)
                                            }
                                        } while (n = (0, a.lp)());
                                        return e
                                    }((0, a.un)(e), r))
                                }(r, c), i = t.props, o = 0, l = 0; o < u.length; o++)
                                for (var f = 0; f < i.length; f++, l++) e.props[l] = c[o] ? u[o].replace(/&\f/g, i[f]) : i[f] + " " + u[o]
                        }
                    }
                },
                x = function(e) {
                    if ("decl" === e.type) {
                        var r = e.value;
                        108 === r.charCodeAt(0) && 98 === r.charCodeAt(2) && (e.return = "", e.value = "")
                    }
                };

            function b(e, r) {
                switch ((0, s.vp)(e, r)) {
                    case 5103:
                        return c.G$ + "print-" + e + e;
                    case 5737:
                    case 4201:
                    case 3177:
                    case 3433:
                    case 1641:
                    case 4457:
                    case 2921:
                    case 5572:
                    case 6356:
                    case 5844:
                    case 3191:
                    case 6645:
                    case 3005:
                    case 6391:
                    case 5879:
                    case 5623:
                    case 6135:
                    case 4599:
                    case 4855:
                    case 4215:
                    case 6389:
                    case 5109:
                    case 5365:
                    case 5621:
                    case 3829:
                        return c.G$ + e + e;
                    case 5349:
                    case 4246:
                    case 4810:
                    case 6968:
                    case 2756:
                        return c.G$ + e + c.uj + e + c.MS + e + e;
                    case 6828:
                    case 4268:
                        return c.G$ + e + c.MS + e + e;
                    case 6165:
                        return c.G$ + e + c.MS + "flex-" + e + e;
                    case 5187:
                        return c.G$ + e + (0, s.gx)(e, /(\w+).+(:[^]+)/, c.G$ + "box-$1$2" + c.MS + "flex-$1$2") + e;
                    case 5443:
                        return c.G$ + e + c.MS + "flex-item-" + (0, s.gx)(e, /flex-|-self/, "") + e;
                    case 4675:
                        return c.G$ + e + c.MS + "flex-line-pack" + (0, s.gx)(e, /align-content|flex-|-self/, "") + e;
                    case 5548:
                        return c.G$ + e + c.MS + (0, s.gx)(e, "shrink", "negative") + e;
                    case 5292:
                        return c.G$ + e + c.MS + (0, s.gx)(e, "basis", "preferred-size") + e;
                    case 6060:
                        return c.G$ + "box-" + (0, s.gx)(e, "-grow", "") + c.G$ + e + c.MS + (0, s.gx)(e, "grow", "positive") + e;
                    case 4554:
                        return c.G$ + (0, s.gx)(e, /([^-])(transform)/g, "$1" + c.G$ + "$2") + e;
                    case 6187:
                        return (0, s.gx)((0, s.gx)((0, s.gx)(e, /(zoom-|grab)/, c.G$ + "$1"), /(image-set)/, c.G$ + "$1"), e, "") + e;
                    case 5495:
                    case 3959:
                        return (0, s.gx)(e, /(image-set\([^]*)/, c.G$ + "$1$`$1");
                    case 4968:
                        return (0, s.gx)((0, s.gx)(e, /(.+:)(flex-)?(.*)/, c.G$ + "box-pack:$3" + c.MS + "flex-pack:$3"), /s.+-b[^;]+/, "justify") + c.G$ + e + e;
                    case 4095:
                    case 3583:
                    case 4068:
                    case 2532:
                        return (0, s.gx)(e, /(.+)-inline(.+)/, c.G$ + "$1$2") + e;
                    case 8116:
                    case 7059:
                    case 5753:
                    case 5535:
                    case 5445:
                    case 5701:
                    case 4933:
                    case 4677:
                    case 5533:
                    case 5789:
                    case 5021:
                    case 4765:
                        if ((0, s.to)(e) - 1 - r > 6) switch ((0, s.uO)(e, r + 1)) {
                            case 109:
                                if (45 !== (0, s.uO)(e, r + 4)) break;
                            case 102:
                                return (0, s.gx)(e, /(.+:)(.+)-([^]+)/, "$1" + c.G$ + "$2-$3$1" + c.uj + (108 == (0, s.uO)(e, r + 3) ? "$3" : "$2-$3")) + e;
                            case 115:
                                return ~(0, s.Cw)(e, "stretch") ? b((0, s.gx)(e, "stretch", "fill-available"), r) + e : e
                        }
                        break;
                    case 4949:
                        if (115 !== (0, s.uO)(e, r + 1)) break;
                    case 6444:
                        switch ((0, s.uO)(e, (0, s.to)(e) - 3 - (~(0, s.Cw)(e, "!important") && 10))) {
                            case 107:
                                return (0, s.gx)(e, ":", ":" + c.G$) + e;
                            case 101:
                                return (0, s.gx)(e, /(.+:)([^;!]+)(;|!.+)?/, "$1" + c.G$ + (45 === (0, s.uO)(e, 14) ? "inline-" : "") + "box$3$1" + c.G$ + "$2$3$1" + c.MS + "$2box$3") + e
                        }
                        break;
                    case 5936:
                        switch ((0, s.uO)(e, r + 11)) {
                            case 114:
                                return c.G$ + e + c.MS + (0, s.gx)(e, /[svh]\w+-[tblr]{2}/, "tb") + e;
                            case 108:
                                return c.G$ + e + c.MS + (0, s.gx)(e, /[svh]\w+-[tblr]{2}/, "tb-rl") + e;
                            case 45:
                                return c.G$ + e + c.MS + (0, s.gx)(e, /[svh]\w+-[tblr]{2}/, "lr") + e
                        }
                        return c.G$ + e + c.MS + e + e
                }
                return e
            }
            var G = [function(e, r, t, n) {
                    if (e.length > -1 && !e.return) switch (e.type) {
                        case c.h5:
                            e.return = b(e.value, e.length);
                            break;
                        case c.lK:
                            return (0, u.q)([(0, a.JG)(e, {
                                value: (0, s.gx)(e.value, "@", "@" + c.G$)
                            })], n);
                        case c.Fr:
                            if (e.length) return (0, s.$e)(e.props, (function(r) {
                                switch ((0, s.EQ)(r, /(::plac\w+|:read-\w+)/)) {
                                    case ":read-only":
                                    case ":read-write":
                                        return (0, u.q)([(0, a.JG)(e, {
                                            props: [(0, s.gx)(r, /:(read-\w+)/, ":" + c.uj + "$1")]
                                        })], n);
                                    case "::placeholder":
                                        return (0, u.q)([(0, a.JG)(e, {
                                            props: [(0, s.gx)(r, /:(plac\w+)/, ":" + c.G$ + "input-$1")]
                                        }), (0, a.JG)(e, {
                                            props: [(0, s.gx)(r, /:(plac\w+)/, ":" + c.uj + "$1")]
                                        }), (0, a.JG)(e, {
                                            props: [(0, s.gx)(r, /:(plac\w+)/, c.MS + "input-$1")]
                                        })], n)
                                }
                                return ""
                            }))
                    }
                }],
                v = function(e) {
                    var r = e.key;
                    if ("css" === r) {
                        var t = document.querySelectorAll("style[data-emotion]:not([data-s])");
                        Array.prototype.forEach.call(t, (function(e) {
                            -1 !== e.getAttribute("data-emotion").indexOf(" ") && (document.head.appendChild(e), e.setAttribute("data-s", ""))
                        }))
                    }
                    var a, s, c = e.stylisPlugins || G,
                        l = {},
                        f = [];
                    a = e.container || document.head, Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="' + r + ' "]'), (function(e) {
                        for (var r = e.getAttribute("data-emotion").split(" "), t = 1; t < r.length; t++) l[r[t]] = !0;
                        f.push(e)
                    }));
                    var g, p = [d, x],
                        $ = [u.P, (0, i.cD)((function(e) {
                            g.insert(e)
                        }))],
                        h = (0, i.qR)(p.concat(c, $));
                    s = function(e, r, t, n) {
                        var a;
                        g = t, a = e ? e + "{" + r.styles + "}" : r.styles, (0, u.q)(o(a), h), n && (b.inserted[r.name] = !0)
                    };
                    var b = {
                        key: r,
                        sheet: new n({
                            key: r,
                            container: a,
                            nonce: e.nonce,
                            speedy: e.speedy,
                            prepend: e.prepend,
                            insertionPoint: e.insertionPoint
                        }),
                        nonce: e.nonce,
                        inserted: l,
                        registered: {},
                        insert: s
                    };
                    return b.sheet.hydrate(f), b
                }
        },
        81666: (e, r, t) => {
            t.d(r, {
                Ab: () => c,
                Fr: () => u,
                G$: () => s,
                JM: () => f,
                K$: () => o,
                MS: () => n,
                h5: () => i,
                lK: () => l,
                uj: () => a
            });
            var n = "-ms-",
                a = "-moz-",
                s = "-webkit-",
                c = "comm",
                u = "rule",
                i = "decl",
                o = "@import",
                l = "@keyframes",
                f = "@layer"
        },
        96001: (e, r, t) => {
            t.d(r, {
                qR: () => i,
                Ji: () => l,
                cD: () => o
            });
            var n = t(81666),
                a = t(36099),
                s = t(35893),
                c = t(94433);

            function u(e, r, t) {
                switch ((0, a.vp)(e, r)) {
                    case 5103:
                        return n.G$ + "print-" + e + e;
                    case 5737:
                    case 4201:
                    case 3177:
                    case 3433:
                    case 1641:
                    case 4457:
                    case 2921:
                    case 5572:
                    case 6356:
                    case 5844:
                    case 3191:
                    case 6645:
                    case 3005:
                    case 6391:
                    case 5879:
                    case 5623:
                    case 6135:
                    case 4599:
                    case 4855:
                    case 4215:
                    case 6389:
                    case 5109:
                    case 5365:
                    case 5621:
                    case 3829:
                        return n.G$ + e + e;
                    case 4789:
                        return n.uj + e + e;
                    case 5349:
                    case 4246:
                    case 4810:
                    case 6968:
                    case 2756:
                        return n.G$ + e + n.uj + e + n.MS + e + e;
                    case 5936:
                        switch ((0, a.uO)(e, r + 11)) {
                            case 114:
                                return n.G$ + e + n.MS + (0, a.gx)(e, /[svh]\w+-[tblr]{2}/, "tb") + e;
                            case 108:
                                return n.G$ + e + n.MS + (0, a.gx)(e, /[svh]\w+-[tblr]{2}/, "tb-rl") + e;
                            case 45:
                                return n.G$ + e + n.MS + (0, a.gx)(e, /[svh]\w+-[tblr]{2}/, "lr") + e
                        }
                    case 6828:
                    case 4268:
                    case 2903:
                        return n.G$ + e + n.MS + e + e;
                    case 6165:
                        return n.G$ + e + n.MS + "flex-" + e + e;
                    case 5187:
                        return n.G$ + e + (0, a.gx)(e, /(\w+).+(:[^]+)/, n.G$ + "box-$1$2" + n.MS + "flex-$1$2") + e;
                    case 5443:
                        return n.G$ + e + n.MS + "flex-item-" + (0, a.gx)(e, /flex-|-self/g, "") + ((0, a.EQ)(e, /flex-|baseline/) ? "" : n.MS + "grid-row-" + (0, a.gx)(e, /flex-|-self/g, "")) + e;
                    case 4675:
                        return n.G$ + e + n.MS + "flex-line-pack" + (0, a.gx)(e, /align-content|flex-|-self/g, "") + e;
                    case 5548:
                        return n.G$ + e + n.MS + (0, a.gx)(e, "shrink", "negative") + e;
                    case 5292:
                        return n.G$ + e + n.MS + (0, a.gx)(e, "basis", "preferred-size") + e;
                    case 6060:
                        return n.G$ + "box-" + (0, a.gx)(e, "-grow", "") + n.G$ + e + n.MS + (0, a.gx)(e, "grow", "positive") + e;
                    case 4554:
                        return n.G$ + (0, a.gx)(e, /([^-])(transform)/g, "$1" + n.G$ + "$2") + e;
                    case 6187:
                        return (0, a.gx)((0, a.gx)((0, a.gx)(e, /(zoom-|grab)/, n.G$ + "$1"), /(image-set)/, n.G$ + "$1"), e, "") + e;
                    case 5495:
                    case 3959:
                        return (0, a.gx)(e, /(image-set\([^]*)/, n.G$ + "$1$`$1");
                    case 4968:
                        return (0, a.gx)((0, a.gx)(e, /(.+:)(flex-)?(.*)/, n.G$ + "box-pack:$3" + n.MS + "flex-pack:$3"), /s.+-b[^;]+/, "justify") + n.G$ + e + e;
                    case 4200:
                        if (!(0, a.EQ)(e, /flex-|baseline/)) return n.MS + "grid-column-align" + (0, a.tb)(e, r) + e;
                        break;
                    case 2592:
                    case 3360:
                        return n.MS + (0, a.gx)(e, "template-", "") + e;
                    case 4384:
                    case 3616:
                        return t && t.some((function(e, t) {
                            return r = t, (0, a.EQ)(e.props, /grid-\w+-end/)
                        })) ? ~(0, a.Cw)(e + (t = t[r].value), "span") ? e : n.MS + (0, a.gx)(e, "-start", "") + e + n.MS + "grid-row-span:" + (~(0, a.Cw)(t, "span") ? (0, a.EQ)(t, /\d+/) : +(0, a.EQ)(t, /\d+/) - +(0, a.EQ)(e, /\d+/)) + ";" : n.MS + (0, a.gx)(e, "-start", "") + e;
                    case 4896:
                    case 4128:
                        return t && t.some((function(e) {
                            return (0, a.EQ)(e.props, /grid-\w+-start/)
                        })) ? e : n.MS + (0, a.gx)((0, a.gx)(e, "-end", "-span"), "span ", "") + e;
                    case 4095:
                    case 3583:
                    case 4068:
                    case 2532:
                        return (0, a.gx)(e, /(.+)-inline(.+)/, n.G$ + "$1$2") + e;
                    case 8116:
                    case 7059:
                    case 5753:
                    case 5535:
                    case 5445:
                    case 5701:
                    case 4933:
                    case 4677:
                    case 5533:
                    case 5789:
                    case 5021:
                    case 4765:
                        if ((0, a.to)(e) - 1 - r > 6) switch ((0, a.uO)(e, r + 1)) {
                            case 109:
                                if (45 !== (0, a.uO)(e, r + 4)) break;
                            case 102:
                                return (0, a.gx)(e, /(.+:)(.+)-([^]+)/, "$1" + n.G$ + "$2-$3$1" + n.uj + (108 == (0, a.uO)(e, r + 3) ? "$3" : "$2-$3")) + e;
                            case 115:
                                return ~(0, a.Cw)(e, "stretch") ? u((0, a.gx)(e, "stretch", "fill-available"), r, t) + e : e
                        }
                        break;
                    case 5152:
                    case 5920:
                        return (0, a.gx)(e, /(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/, (function(r, t, a, s, c, u, i) {
                            return n.MS + t + ":" + a + i + (s ? n.MS + t + "-span:" + (c ? u : +u - +a) + i : "") + e
                        }));
                    case 4949:
                        if (121 === (0, a.uO)(e, r + 6)) return (0, a.gx)(e, ":", ":" + n.G$) + e;
                        break;
                    case 6444:
                        switch ((0, a.uO)(e, 45 === (0, a.uO)(e, 14) ? 18 : 11)) {
                            case 120:
                                return (0, a.gx)(e, /(.+:)([^;\s!]+)(;|(\s+)?!.+)?/, "$1" + n.G$ + (45 === (0, a.uO)(e, 14) ? "inline-" : "") + "box$3$1" + n.G$ + "$2$3$1" + n.MS + "$2box$3") + e;
                            case 100:
                                return (0, a.gx)(e, ":", ":" + n.MS) + e
                        }
                        break;
                    case 5719:
                    case 2647:
                    case 2135:
                    case 3927:
                    case 2391:
                        return (0, a.gx)(e, "scroll-", "scroll-snap-") + e
                }
                return e
            }

            function i(e) {
                var r = (0, a.Ei)(e);
                return function(t, n, a, s) {
                    for (var c = "", u = 0; u < r; u++) c += e[u](t, n, a, s) || "";
                    return c
                }
            }

            function o(e) {
                return function(r) {
                    r.root || (r = r.return) && e(r)
                }
            }

            function l(e, r, t, i) {
                if (e.length > -1 && !e.return) switch (e.type) {
                    case n.h5:
                        return void(e.return = u(e.value, e.length, t));
                    case n.lK:
                        return (0, c.q)([(0, s.JG)(e, {
                            value: (0, a.gx)(e.value, "@", "@" + n.G$)
                        })], i);
                    case n.Fr:
                        if (e.length) return (0, a.$e)(e.props, (function(r) {
                            switch ((0, a.EQ)(r, /(::plac\w+|:read-\w+)/)) {
                                case ":read-only":
                                case ":read-write":
                                    return (0, c.q)([(0, s.JG)(e, {
                                        props: [(0, a.gx)(r, /:(read-\w+)/, ":" + n.uj + "$1")]
                                    })], i);
                                case "::placeholder":
                                    return (0, c.q)([(0, s.JG)(e, {
                                        props: [(0, a.gx)(r, /:(plac\w+)/, ":" + n.G$ + "input-$1")]
                                    }), (0, s.JG)(e, {
                                        props: [(0, a.gx)(r, /:(plac\w+)/, ":" + n.uj + "$1")]
                                    }), (0, s.JG)(e, {
                                        props: [(0, a.gx)(r, /:(plac\w+)/, n.MS + "input-$1")]
                                    })], i)
                            }
                            return ""
                        }))
                }
            }
        },
        94433: (e, r, t) => {
            t.d(r, {
                P: () => c,
                q: () => s
            });
            var n = t(81666),
                a = t(36099);

            function s(e, r) {
                for (var t = "", n = (0, a.Ei)(e), s = 0; s < n; s++) t += r(e[s], s, e, r) || "";
                return t
            }

            function c(e, r, t, c) {
                switch (e.type) {
                    case n.JM:
                        if (e.children.length) break;
                    case n.K$:
                    case n.h5:
                        return e.return = e.return || e.value;
                    case n.Ab:
                        return "";
                    case n.lK:
                        return e.return = e.value + "{" + s(e.children, c) + "}";
                    case n.Fr:
                        e.value = e.props.join(",")
                }
                return (0, a.to)(t = s(e.children, c)) ? e.return = e.value + "{" + t + "}" : ""
            }
        },
        35893: (e, r, t) => {
            t.d(r, {
                FK: () => u,
                JG: () => f,
                QU: () => M,
                Qb: () => S,
                Tb: () => g,
                Ud: () => d,
                cE: () => v,
                dH: () => l,
                fj: () => h,
                iF: () => w,
                kq: () => k,
                lp: () => $,
                mp: () => p,
                q6: () => m,
                r: () => b,
                tP: () => x,
                un: () => G
            });
            var n = t(36099),
                a = 1,
                s = 1,
                c = 0,
                u = 0,
                i = 0,
                o = "";

            function l(e, r, t, n, c, u, i) {
                return {
                    value: e,
                    root: r,
                    parent: t,
                    type: n,
                    props: c,
                    children: u,
                    line: a,
                    column: s,
                    length: i,
                    return: ""
                }
            }

            function f(e, r) {
                return (0, n.f0)(l("", null, null, "", null, null, 0), e, {
                    length: -e.length
                }, r)
            }

            function g() {
                return i
            }

            function p() {
                return i = u > 0 ? (0, n.uO)(o, --u) : 0, s--, 10 === i && (s = 1, a--), i
            }

            function $() {
                return i = u < c ? (0, n.uO)(o, u++) : 0, s++, 10 === i && (s = 1, a++), i
            }

            function h() {
                return (0, n.uO)(o, u)
            }

            function d() {
                return u
            }

            function x(e, r) {
                return (0, n.tb)(o, e, r)
            }

            function b(e) {
                switch (e) {
                    case 0:
                    case 9:
                    case 10:
                    case 13:
                    case 32:
                        return 5;
                    case 33:
                    case 43:
                    case 44:
                    case 47:
                    case 62:
                    case 64:
                    case 126:
                    case 59:
                    case 123:
                    case 125:
                        return 4;
                    case 58:
                        return 3;
                    case 34:
                    case 39:
                    case 40:
                    case 91:
                        return 2;
                    case 41:
                    case 93:
                        return 1
                }
                return 0
            }

            function G(e) {
                return a = s = 1, c = (0, n.to)(o = e), u = 0, []
            }

            function v(e) {
                return o = "", e
            }

            function w(e) {
                return (0, n.fy)(x(u - 1, y(91 === e ? e + 2 : 40 === e ? e + 1 : e)))
            }

            function S(e) {
                for (;
                    (i = h()) && i < 33;) $();
                return b(e) > 2 || b(i) > 3 ? "" : " "
            }

            function k(e, r) {
                for (; --r && $() && !(i < 48 || i > 102 || i > 57 && i < 65 || i > 70 && i < 97););
                return x(e, d() + (r < 6 && 32 == h() && 32 == $()))
            }

            function y(e) {
                for (; $();) switch (i) {
                    case e:
                        return u;
                    case 34:
                    case 39:
                        34 !== e && 39 !== e && y(i);
                        break;
                    case 40:
                        41 === e && y(e);
                        break;
                    case 92:
                        $()
                }
                return u
            }

            function m(e, r) {
                for (; $() && e + i !== 57 && (e + i !== 84 || 47 !== h()););
                return "/*" + x(r, u - 1) + "*" + (0, n.Dp)(47 === e ? e : $())
            }

            function M(e) {
                for (; !b(h());) $();
                return x(e, u)
            }
        },
        36099: (e, r, t) => {
            t.d(r, {
                $e: () => d,
                Cw: () => l,
                Dp: () => a,
                EQ: () => i,
                Ei: () => $,
                R3: () => h,
                Wn: () => n,
                f0: () => s,
                fy: () => u,
                gx: () => o,
                tb: () => g,
                to: () => p,
                uO: () => f,
                vp: () => c
            });
            var n = Math.abs,
                a = String.fromCharCode,
                s = Object.assign;

            function c(e, r) {
                return 45 ^ f(e, 0) ? (((r << 2 ^ f(e, 0)) << 2 ^ f(e, 1)) << 2 ^ f(e, 2)) << 2 ^ f(e, 3) : 0
            }

            function u(e) {
                return e.trim()
            }

            function i(e, r) {
                return (e = r.exec(e)) ? e[0] : e
            }

            function o(e, r, t) {
                return e.replace(r, t)
            }

            function l(e, r) {
                return e.indexOf(r)
            }

            function f(e, r) {
                return 0 | e.charCodeAt(r)
            }

            function g(e, r, t) {
                return e.slice(r, t)
            }

            function p(e) {
                return e.length
            }

            function $(e) {
                return e.length
            }

            function h(e, r) {
                return r.push(e), e
            }

            function d(e, r) {
                return e.map(r).join("")
            }
        }
    }
]);
//# sourceMappingURL=3882.0d4c329fbfcbeb3c.js.map