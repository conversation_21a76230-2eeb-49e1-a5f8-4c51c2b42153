(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [5169], {
        57612: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.createCacheKeyComparator = u, t.defaultEqualityCheck = void 0, t.defaultMemoize = function(e, t) {
                var o, i, c = "object" == typeof t ? t : {
                        equalityCheck: t
                    },
                    a = c.equalityCheck,
                    f = void 0 === a ? n : a,
                    l = c.maxSize,
                    p = void 0 === l ? 1 : l,
                    d = c.resultEqualityCheck,
                    v = u(f),
                    y = 1 === p ? (o = v, {
                        get: function(e) {
                            return i && o(i.key, e) ? i.value : r
                        },
                        put: function(e, t) {
                            i = {
                                key: e,
                                value: t
                            }
                        },
                        getEntries: function() {
                            return i ? [i] : []
                        },
                        clear: function() {
                            i = void 0
                        }
                    }) : function(e, t) {
                        var n = [];

                        function u(e) {
                            var u = n.findIndex((function(r) {
                                return t(e, r.key)
                            }));
                            if (u > -1) {
                                var o = n[u];
                                return u > 0 && (n.splice(u, 1), n.unshift(o)), o.value
                            }
                            return r
                        }
                        return {
                            get: u,
                            put: function(t, o) {
                                u(t) === r && (n.unshift({
                                    key: t,
                                    value: o
                                }), n.length > e && n.pop())
                            },
                            getEntries: function() {
                                return n
                            },
                            clear: function() {
                                n = []
                            }
                        }
                    }(p, v);

                function s() {
                    var t = y.get(arguments);
                    if (t === r) {
                        if (t = e.apply(null, arguments), d) {
                            var n = y.getEntries().find((function(e) {
                                return d(e.value, t)
                            }));
                            n && (t = n.value)
                        }
                        y.put(arguments, t)
                    }
                    return t
                }
                return s.clearCache = function() {
                    return y.clear()
                }, s
            };
            var r = "NOT_FOUND",
                n = function(e, t) {
                    return e === t
                };

            function u(e) {
                return function(t, r) {
                    if (null === t || null === r || t.length !== r.length) return !1;
                    for (var n = t.length, u = 0; u < n; u++)
                        if (!e(t[u], r[u])) return !1;
                    return !0
                }
            }
            t.defaultEqualityCheck = n
        },
        85169: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.createSelector = void 0, t.createSelectorCreator = u, t.createStructuredSelector = void 0, Object.defineProperty(t, "defaultEqualityCheck", {
                enumerable: !0,
                get: function() {
                    return n.defaultEqualityCheck
                }
            }), Object.defineProperty(t, "defaultMemoize", {
                enumerable: !0,
                get: function() {
                    return n.defaultMemoize
                }
            });
            var n = r(57612);

            function u(e) {
                for (var t = arguments.length, r = new Array(t > 1 ? t - 1 : 0), n = 1; n < t; n++) r[n - 1] = arguments[n];
                return function() {
                    for (var t = arguments.length, n = new Array(t), u = 0; u < t; u++) n[u] = arguments[u];
                    var o, i = 0,
                        c = {
                            memoizeOptions: void 0
                        },
                        a = n.pop();
                    if ("object" == typeof a && (c = a, a = n.pop()), "function" != typeof a) throw new Error("createSelector expects an output function after the inputs, but received: [" + typeof a + "]");
                    var f = c.memoizeOptions,
                        l = void 0 === f ? r : f,
                        p = Array.isArray(l) ? l : [l],
                        d = function(e) {
                            var t = Array.isArray(e[0]) ? e[0] : e;
                            if (!t.every((function(e) {
                                    return "function" == typeof e
                                }))) {
                                var r = t.map((function(e) {
                                    return "function" == typeof e ? "function " + (e.name || "unnamed") + "()" : typeof e
                                })).join(", ");
                                throw new Error("createSelector expects all input-selectors to be functions, but received the following types: [" + r + "]")
                            }
                            return t
                        }(n),
                        v = e.apply(void 0, [function() {
                            return i++, a.apply(null, arguments)
                        }].concat(p)),
                        y = e((function() {
                            for (var e = [], t = d.length, r = 0; r < t; r++) e.push(d[r].apply(null, arguments));
                            return o = v.apply(null, e)
                        }));
                    return Object.assign(y, {
                        resultFunc: a,
                        memoizedResultFunc: v,
                        dependencies: d,
                        lastResult: function() {
                            return o
                        },
                        recomputations: function() {
                            return i
                        },
                        resetRecomputations: function() {
                            return i = 0
                        }
                    }), y
                }
            }
            var o = u(n.defaultMemoize);
            t.createSelector = o, t.createStructuredSelector = function(e, t) {
                if (void 0 === t && (t = o), "object" != typeof e) throw new Error("createStructuredSelector expects first argument to be an object where each property is a selector, instead received a " + typeof e);
                var r = Object.keys(e),
                    n = t(r.map((function(t) {
                        return e[t]
                    })), (function() {
                        for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
                        return t.reduce((function(e, t, n) {
                            return e[r[n]] = t, e
                        }), {})
                    }));
                return n
            }
        }
    }
]);
//# sourceMappingURL=5169.f2391255d4e2c997.js.map