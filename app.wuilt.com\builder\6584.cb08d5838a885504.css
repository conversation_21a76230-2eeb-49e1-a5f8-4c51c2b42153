.ExpandButton_sectionControlButtonContainer__gdVPM {
    position: relative;
    width: 40px;
    height: 40px;
    overflow: visible;
    font-size: 12px;
    pointer-events: all
}

.ExpandButton_controlButton__HHvk9 {
    pointer-events: all;
    position: relative;
    transition: all .9s;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    width: 50px;
    line-height: 50px;
    height: 50px;
    border: 0 solid;
    background: #fff;
    box-shadow: 1px 1px 15px rgba(0, 0, 0, .3)
}

.ExpandButton_controlButton__HHvk9 span {
    opacity: 0;
    display: none
}

@media screen and (min-width: 600px) {
    .ExpandButton_controlButton__HHvk9:hover {
        width: auto
    }
    .ExpandButton_controlButton__HHvk9:hover span {
        text-align: right;
        opacity: 1;
        display: block;
        padding: 0 10px
    }
    .ExpandButton_controlButton__HHvk9:hover i {
        padding: 0 5px
    }
}

.ExpandButton_cRtl__2ycGQ .ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--fromCenter__XD0Jn {
    transform: translateX(-50%)
}

.ExpandButton_controlButton__HHvk9 {
    position: relative;
    transition: all .9s;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    width: 50px;
    line-height: 50px;
    height: 50px;
    border: 0 solid;
    background: #fff;
    box-shadow: 1px 1px 15px rgba(0, 0, 0, .3)
}

.ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--fromCenter__XD0Jn {
    left: 50%;
    transform: translateX(-50%)
}

.ExpandButton_controlButton__HHvk9 span {
    opacity: 0;
    display: none
}

@media screen and (min-width: 600px) {
    .ExpandButton_controlButton__HHvk9:hover {
        width: auto
    }
}

.ExpandButton_controlButton__HHvk9:hover span {
    text-align: right;
    opacity: 1;
    display: block;
    padding: 0 10px
}

.ExpandButton_controlButton__HHvk9:hover i {
    padding: 0 5px
}

.ExpandButton_sectionControlButton__w\+W9P span {
    text-align: right;
    width: 0;
    padding: 0 10px;
    height: 100%;
    display: none;
    overflow: hidden;
    transition: width .4s
}

.ExpandButton_cRtl__2ycGQ .ExpandButton_sectionControlButton__w\+W9P span {
    text-align: left
}

.ExpandButton_sectionControlButton__w\+W9P:hover span,
.ExpandButton_sectionControlButton__w\+W9P:focus span {
    display: block;
    width: 250px
}

.ExpandButton_controlButton__icon__wiEFm {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    line-height: 40px;
    width: 40px;
    height: 40px
}

.ExpandButton_controlButton__icon__wiEFm svg {
    width: 25px;
    height: 25px
}

.ExpandButton_sectionControlButton__w\+W9P {
    overflow: hidden;
    position: absolute;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    width: 40px;
    line-height: 40px;
    height: 40px;
    border: 0 solid;
    background: #fff;
    box-shadow: 0 1px 6px 1px rgba(2, 18, 43, .25);
    transition: all .4s;
    flex-wrap: wrap
}

.ExpandButton_sectionControlButton__w\+W9P:hover,
.ExpandButton_sectionControlButton__w\+W9P:focus {
    outline: none
}

@media screen and (min-width: 600px) {
    .ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--expanded__x5sJi,
    .ExpandButton_sectionControlButton__w\+W9P:hover,
    .ExpandButton_sectionControlButton__w\+W9P:focus {
        width: 215px
    }
    .ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--expanded__x5sJi span,
    .ExpandButton_sectionControlButton__w\+W9P:hover span,
    .ExpandButton_sectionControlButton__w\+W9P:focus span {
        display: block;
        width: 250px
    }
}

.ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--expandToRight__kKh8S span {
    text-align: left
}

.uiRtl .ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--expandToRight__kKh8S span {
    text-align: right
}

.ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--iconToLeft__sk0fZ.ExpandButton_--expandToLeft__s61-L span {
    text-align: right
}

.uiRtl .ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--iconToLeft__sk0fZ.ExpandButton_--expandToLeft__s61-L span {
    text-align: left
}

.ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--expandToLeft__s61-L.ExpandButton_--iconToRight__yqbby span {
    text-align: left
}

.uiRtl .ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--expandToLeft__s61-L.ExpandButton_--iconToRight__yqbby span {
    text-align: right
}

.ExpandButton_sectionControlButton__w\+W9P.ExpandButton_--fromCenter__XD0Jn span {
    text-align: center !important
}

.SectionOverlayControls_spacer__Uo1B4 {
    pointer-events: none;
    display: block;
    opacity: 0
}

.SectionOverlayControls_spacer__Uo1B4>* {
    background-color: #000
}

.SectionOverlayControls_SectionControlsOuterContainer__aukKt {
    pointer-events: none;
    position: absolute;
    z-index: 99;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px
}

.SectionOverlayControls_SectionControlsOuterContainer__aukKt.SectionOverlayControls_--active__atU7y {
    z-index: 900;
    border: 1px solid #0e9384
}

.SectionOverlayControls_SectionControlsOuterContainer__aukKt.SectionOverlayControls_--active__atU7y.SectionOverlayControls_--global__CXbl0 {
    border: 1px solid #fac515
}

.SectionOverlayControls_SectionControlsOuterContainer__aukKt .SectionOverlayControls_SectionControls__BURdr {
    border: 1px solid rgba(0, 0, 0, 0);
    opacity: 0
}

.uiRtl .SectionOverlayControls_SectionControlsOuterContainer__aukKt .SectionOverlayControls_SectionControls__BURdr {
    direction: rtl
}

.uiLtr .SectionOverlayControls_SectionControlsOuterContainer__aukKt .SectionOverlayControls_SectionControls__BURdr {
    direction: ltr
}

.SectionOverlayControls_SectionControlsOuterContainer__aukKt .SectionOverlayControls_SectionControls__BURdr.SectionOverlayControls_--global__CXbl0 {
    border: 1px solid rgba(0, 0, 0, 0)
}

.SectionOverlayControls_SectionControlsOuterContainer__aukKt .SectionOverlayControls_SectionControls__BURdr.SectionOverlayControls_--global__CXbl0.SectionOverlayControls_--active__atU7y {
    opacity: 1;
    border-top: 1px solid #fac515
}

.SectionOverlayControls_SectionControlsOuterContainer__aukKt .SectionOverlayControls_SectionControls__BURdr.SectionOverlayControls_--active__atU7y {
    border-top: 1px solid #0e9384;
    opacity: 1
}

.SectionOverlayControls_SectionControlsOuterContainer__aukKt .SectionOverlayControls_SectionControls__BURdr.SectionOverlayControls_--unClickable__Jtqam {
    pointer-events: none
}

.SectionOverlayControls_SectionControls__BURdr button {
    pointer-events: all;
    outline: none
}

.SectionOverlayControls_bottomControls__JObJ6 {
    display: flex;
    position: absolute;
    bottom: 18px;
    transform: translateX(-50%);
    left: 50%
}

.SectionOverlayControls_spaceBetween__b994K {
    width: 220px
}

.SectionOverlayControls_addButton__Xo5Xn {
    position: absolute !important;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 50%);
    border: 0 solid;
    width: 0px;
    cursor: pointer
}

.uiRtl .SectionOverlayControls_addButton__Xo5Xn {
    transform: translate(0, 50%)
}

.SectionOverlayControls_section__oz-rh {
    position: relative
}

.SectionErrorComponent_container__1KIZD {
    background: #202b39;
    min-height: 644px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center
}

.SectionErrorComponent_innerContainer__RvoZ0 {
    text-align: center
}

.SectionErrorComponent_title__5cc8s {
    margin-top: 35px;
    font-size: 29.4px
}

.SectionErrorComponent_description__KQMQ- {
    margin-bottom: 67px;
    opacity: .8;
    font-size: 16.1px
}

.SectionErrorComponent_button__1l6qc {
    margin: 0 6.5px
}

.SiteErrorComponent_container__jBEhX {
    background: #202b39;
    min-height: 644px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center
}

.SiteErrorComponent_innerContainer__QbaBT {
    text-align: center
}

.SiteErrorComponent_title__zFOBJ {
    margin-top: 35px;
    font-size: 29.4px
}

.SiteErrorComponent_description__6KQpM {
    margin-bottom: 67px;
    opacity: .8;
    font-size: 16.1px
}

.SiteErrorComponent_button__aNEvl {
    margin: 0 6.5px
}

.SectionRenderer_extraPaddingForGlobalNavbar__bvAtQ>* {
    padding-top: 160px
}

body {
    overflow-anchor: none
}

.ui,
.ui * {
    font-family: Inter, Almarai;
    font-style: normal;
    letter-spacing: normal
}

.uiRtl .ui,
.uiRtl .ui * {
    direction: rtl;
    text-align: right
}

.uiLtr .ui,
.uiLtr .ui * {
    direction: ltr;
    text-align: left
}

[data-aos] {
    pointer-events: auto
}

.aos-animate {
    pointer-events: auto
}

.aos-animate {
    transform: unset !important
}

.tox-tinymce-inline {
    z-index: 999;
    border: 1px solid #98a2b3 !important;
    box-shadow: 0px 3px 7px 5px rgba(16, 24, 40, .2) !important;
    transform: translate(-10%, -20px);
    max-width: 80%
}

.tox-editor-header {
    max-width: unset !important
}

.tox .tox-menu.tox-collection.tox-collection--list {
    padding: 10px
}

.tox .tox-collection--list .tox-collection__item {
    padding: 10px 0px 0px 0px !important
}

.tox .tox-collection--list .tox-collection__item h1,
.tox .tox-collection--list .tox-collection__item h2 {
    margin-bottom: 25px
}

.tox .tox-collection--list .tox-collection__item h3,
.tox .tox-collection--list .tox-collection__item h4,
.tox .tox-collection--list .tox-collection__item h5,
.tox .tox-collection--list .tox-collection__item h6,
.tox .tox-collection--list .tox-collection__item p {
    margin-bottom: 10px
}

.la,
.las {
    font-family: "Line Awesome Free"
}

._buttonsWrapper_1bbr8_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1bbr8_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1bbr8_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1bbr8_144 {
    margin-bottom: 30px
}

._aboutContainer_1bbr8_165 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_3.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: #fff;
    background-position: top right;
    padding: 70px 0 50px
}

@media (max-width: 992px) {
    ._aboutContainer_1bbr8_165 {
        background-size: cover;
        background-position: right center
    }
}

._aboutContainer_1bbr8_165 ._textContainer_1bbr8_179 {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center
}

@media (max-width: 992px) {
    ._aboutContainer_1bbr8_165 ._textContainer_1bbr8_179 {
        margin-bottom: 50px
    }
}

._aboutContainer_1bbr8_165 ._textContainer_1bbr8_179 h1 {
    color: var(--theme-color-primary);
    margin-bottom: 30px
}

@media (max-width: 992px) {
    ._aboutContainer_1bbr8_165 ._textContainer_1bbr8_179 h1 {
        font-size: 24px
    }
}

._aboutContainer_1bbr8_165 ._textContainer_1bbr8_179 p {
    font-size: 18px;
    line-height: 26px
}

@media (max-width: 992px) {
    ._aboutContainer_1bbr8_165 ._textContainer_1bbr8_179 p {
        font-size: 14px
    }
}

._aboutContainer_1bbr8_165 ._textContainer_1bbr8_179 ._aboutBtn_1bbr8_208 {
    font-size: 14px;
    font-weight: 700;
    display: flex;
    align-items: center
}

._aboutContainer_1bbr8_165 ._imageContainer_1bbr8_214 {
    width: 100%
}

._aboutContainer_1bbr8_165 ._imageContainer_1bbr8_214 ._image_1bbr8_214 {
    box-shadow: 10px 10px var(--theme-color-primary)
}

._buttonsWrapper_s7cws_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_s7cws_138 {
    margin-bottom: 16px
}

._spaceAfter--small_s7cws_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_s7cws_144 {
    margin-bottom: 30px
}

._aboutContainer_s7cws_165 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_3.png);
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-color: #fff;
    background-position: top right;
    padding: 100px 0;
    min-height: 500px
}

@media (max-width: 992px) {
    ._aboutContainer_s7cws_165 {
        background-size: cover;
        background-position: right center
    }
}

._aboutContainer_s7cws_165 ._textContainer_s7cws_180 {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center
}

._aboutContainer_s7cws_165 ._textContainer_s7cws_180 h1 {
    color: var(--theme-color-primary);
    margin-bottom: 30px
}

@media (max-width: 992px) {
    ._aboutContainer_s7cws_165 ._textContainer_s7cws_180 h1 {
        font-size: 24px
    }
}

._aboutContainer_s7cws_165 ._textContainer_s7cws_180 p {
    font-size: 18px;
    line-height: 26px
}

@media (max-width: 992px) {
    ._aboutContainer_s7cws_165 ._textContainer_s7cws_180 p {
        font-size: 14px
    }
}

._buttonsWrapper_1r8ue_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1r8ue_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1r8ue_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1r8ue_144 {
    margin-bottom: 30px
}

._aboutContainer_1r8ue_165 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_3.png);
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: top right;
    padding: 100px 0;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    justify-content: center
}

@media (max-width: 992px) {
    ._aboutContainer_1r8ue_165 {
        background-size: cover;
        background-position: right center
    }
}

._aboutContainer_1r8ue_165 ._textContainer_1r8ue_182 {
    height: 100%
}

._aboutContainer_1r8ue_165 ._textContainer_1r8ue_182 h1 {
    color: var(--theme-color-primary);
    margin-bottom: 30px
}

@media (max-width: 992px) {
    ._aboutContainer_1r8ue_165 ._textContainer_1r8ue_182 h1 {
        font-size: 24px
    }
}

._aboutContainer_1r8ue_165 ._textContainer_1r8ue_182 p {
    font-size: 18px;
    line-height: 26px
}

@media (max-width: 992px) {
    ._aboutContainer_1r8ue_165 ._textContainer_1r8ue_182 p {
        font-size: 14px
    }
}

._aboutContainer_1r8ue_165 ._textContainer_1r8ue_182 ._aboutBtn_1r8ue_203 {
    font-size: 14px;
    font-weight: 700;
    display: flex;
    align-items: center
}

._buttonsWrapper_1jx6v_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1jx6v_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1jx6v_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1jx6v_144 {
    margin-bottom: 30px
}

._sectionContainer_1jx6v_165 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_3.png);
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-color: #fff;
    background-position: top right;
    padding: 70px 0
}

@media (max-width: 992px) {
    ._sectionContainer_1jx6v_165 {
        background-size: cover;
        background-position: right center
    }
}

._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 {
    text-align: left;
    width: 85%
}

._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 ._fieldsHeader_1jx6v_183 h1 {
    color: var(--theme-color-primary)
}

@media (max-width: 992px) {
    ._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 ._fieldsHeader_1jx6v_183 h1 {
        font-size: 24px
    }
}

._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 ._fieldsHeader_1jx6v_183 p {
    font-size: 16px;
    color: #010101;
    margin-bottom: 30px
}

._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 ._textField_1jx6v_196,
._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 ._textareaField_1jx6v_197 {
    color: #585757
}

._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 ._textareaField_1jx6v_197 {
    height: 80px
}

._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 ._submitButton_1jx6v_203 {
    color: #fff;
    background-color: var(--theme-color-primary);
    border-radius: 3px;
    padding: 8px 24px;
    border: none;
    outline: 0;
    cursor: pointer;
    min-width: 150px;
    text-align: center
}

@media (max-width: 992px) {
    ._sectionContainer_1jx6v_165 ._contactFields_1jx6v_179 ._submitButton_1jx6v_203 {
        margin-bottom: 50px
    }
}

._sectionContainer_1jx6v_165 ._mapContainer_1jx6v_219,
._sectionContainer_1jx6v_165 ._mapContainer_1jx6v_219 ._map_1jx6v_219 {
    height: 100%
}

._actionContainer_1jx6v_226 {
    display: flex;
    justify-content: flex-start
}

._footer_1lyrn_1 {
    padding: 50px 0 30px;
    background: var(--theme-color-gradient);
    color: #fff
}

._footer_1lyrn_1 ._footerContent_1lyrn_6 {
    display: flex;
    flex-direction: column;
    align-items: center
}

._footerLinks_1lyrn_12 {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 0;
    margin: 20px 0
}

._footerLinks_1lyrn_12 ._navLink_1lyrn_20 {
    color: #fff;
    font-weight: 600;
    margin: 0 8px
}

._footerCopyright_1lyrn_26 p {
    font-size: 13px;
    color: #969696
}

._socialLinks_1lyrn_31 {
    width: -moz-fit-content;
    width: fit-content
}

._socialLinks_1lyrn_31 ._social_1lyrn_31 {
    display: flex;
    list-style: none;
    padding: 0
}

._socialLinks_1lyrn_31 ._socialItem_1lyrn_39 a {
    color: var(--theme-color-primary);
    margin-right: 5px
}

._socialLinks_1lyrn_31 ._socialItem_1lyrn_39 a i {
    font-size: 24px
}

._buttonsWrapper_1m8ql_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1m8ql_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1m8ql_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1m8ql_144 {
    margin-bottom: 30px
}

._sectionContainer_1m8ql_165 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_2.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: #222120;
    background-position: top left;
    padding: 50px 0 70px
}

@media (max-width: 992px) {
    ._sectionContainer_1m8ql_165 {
        background-size: cover;
        background-position: left center
    }
}

._sectionContainer_1m8ql_165 ._sectionTitle_1m8ql_179 h1 {
    color: var(--theme-color-primary)
}

@media (max-width: 992px) {
    ._sectionContainer_1m8ql_165 ._sectionTitle_1m8ql_179 h1 {
        font-size: 24px
    }
}

._item_1m8ql_188 {
    width: 95%;
    margin: 5% auto
}

._item_1m8ql_188 ._itemImage_1m8ql_192 {
    width: 100%;
    box-shadow: 10px 10px var(--theme-color-primary)
}

._buttonsWrapper_nd6rq_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_nd6rq_138 {
    margin-bottom: 16px
}

._spaceAfter--small_nd6rq_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_nd6rq_144 {
    margin-bottom: 30px
}

._sectionContainer_nd6rq_165 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_1.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: #f1f2ed;
    background-position: top left;
    padding: 50px 0 70px
}

@media (max-width: 992px) {
    ._sectionContainer_nd6rq_165 {
        background-size: auto;
        background-position: left center
    }
}

._sectionContainer_nd6rq_165 ._sectionTitle_nd6rq_179 h1 {
    color: var(--theme-color-primary)
}

@media (max-width: 992px) {
    ._sectionContainer_nd6rq_165 ._sectionTitle_nd6rq_179 h1 {
        font-size: 24px
    }
}

._item_nd6rq_188 {
    margin: 20px 0
}

._item_nd6rq_188 ._itemImage_nd6rq_191 {
    width: 100%;
    box-shadow: 10px 10px var(--theme-color-primary)
}

._buttonsWrapper_17jhq_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_17jhq_138 {
    margin-bottom: 16px
}

._spaceAfter--small_17jhq_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_17jhq_144 {
    margin-bottom: 30px
}

._header_17jhq_165 {
    padding-bottom: 190px
}

._header_17jhq_165 ._headerContent_17jhq_168 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_4.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: #eef1ec;
    background-position: top left;
    width: 100%
}

@media (max-width: 992px) {
    ._header_17jhq_165 ._headerContent_17jhq_168 {
        background-size: auto;
        background-position: left center;
        height: 300px
    }
}

@media (max-width: 300px) {
    ._header_17jhq_165 ._headerContent_17jhq_168 {
        height: 250px
    }
}

._header_17jhq_165 ._headerContent_17jhq_168 h1 {
    color: var(--theme-color-primary);
    margin: 30px 0
}

._header_17jhq_165 ._imageContainer_17jhq_192 {
    width: 100%;
    margin: -120px auto 0;
    transform: translateY(120px)
}

._header_17jhq_165 ._imageContainer_17jhq_192 ._image_17jhq_192 {
    width: 100%;
    box-shadow: 10px 10px var(--theme-color-primary)
}

@media (max-width: 992px) {
    ._header_17jhq_165 ._imageContainer_17jhq_192 ._image_17jhq_192 {
        margin-top: -100px
    }
}

@media (max-width: 300px) {
    ._header_17jhq_165 ._imageContainer_17jhq_192 ._image_17jhq_192 {
        margin-top: -50px
    }
}

._buttonsWrapper_ex59j_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_ex59j_138 {
    margin-bottom: 16px
}

._spaceAfter--small_ex59j_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_ex59j_144 {
    margin-bottom: 30px
}

@media (max-width: 992px) {
    ._heroLogo_ex59j_166 {
        margin: 0 0 0 auto
    }
}

._burger_ex59j_171 {
    padding-left: 0 !important;
    color: var(--theme-color-primary)
}

@media (max-width: 992px) {
    ._burger_ex59j_171 {
        transform: rotateX(180deg);
        transform: rotateY(180deg)
    }
}

._navMenu_ex59j_182 ._navLink_ex59j_182 {
    color: #000;
    font-weight: 600;
    margin: 0 5px
}

._navMenu_ex59j_182 ._navLink_ex59j_182:hover {
    color: #7d7e7c
}

._buttonsWrapper_hd3fb_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_hd3fb_138 {
    margin-bottom: 16px
}

._spaceAfter--small_hd3fb_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_hd3fb_144 {
    margin-bottom: 30px
}

._hero_hd3fb_165 {
    padding-bottom: 190px
}

._hero_hd3fb_165 ._heroContent_hd3fb_168 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_4.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: #eef1ec;
    background-position: top left;
    width: 100%
}

@media (max-width: 992px) {
    ._hero_hd3fb_165 ._heroContent_hd3fb_168 {
        background-size: auto;
        background-position: left center;
        height: 480px
    }
}

._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 30px 0
}

._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 h1 {
    color: var(--theme-color-primary);
    margin-bottom: 30px
}

@media (max-width: 992px) {
    ._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 h1 {
        font-size: 24px
    }
}

._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 p {
    font-weight: 700;
    color: var(--theme-color-primary);
    font-size: 18px
}

@media (max-width: 992px) {
    ._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 p {
        font-size: 14px
    }
}

._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 ._buttonsContainer_hd3fb_209 {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-bottom: 30px
}

@media (max-width: 992px) {
    ._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 ._buttonsContainer_hd3fb_209 {
        flex-direction: column
    }
}

._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 ._buttonsContainer_hd3fb_209 ._primaryButton_hd3fb_221 {
    display: flex;
    align-items: center;
    font-weight: 700
}

._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 ._buttonsContainer_hd3fb_209 ._secondaryButton_hd3fb_226 {
    display: flex;
    align-items: center;
    margin: 0 0 0 20px;
    font-weight: 700
}

@media (max-width: 992px) {
    ._hero_hd3fb_165 ._heroContent_hd3fb_168 ._heroText_hd3fb_183 ._buttonsContainer_hd3fb_209 ._secondaryButton_hd3fb_226 {
        margin: 20px 0 0
    }
}

._hero_hd3fb_165 ._imageContainer_hd3fb_237 {
    width: 100%;
    margin: -120px auto 0;
    transform: translateY(120px)
}

._hero_hd3fb_165 ._imageContainer_hd3fb_237 img {
    aspect-ratio: 2/1;
    max-height: unset !important
}

._hero_hd3fb_165 ._imageContainer_hd3fb_237 ._image_hd3fb_237 {
    width: 100%;
    filter: drop-shadow(0px 0px 1px rgba(26, 32, 36, .32)) drop-shadow(0px 12px 24px rgba(91, 104, 113, .24))
}

@media (max-width: 992px) {
    ._hero_hd3fb_165 ._imageContainer_hd3fb_237 ._image_hd3fb_237 {
        margin-top: -100px
    }
}

@media (max-width: 300px) {
    ._hero_hd3fb_165 ._imageContainer_hd3fb_237 ._image_hd3fb_237 {
        margin-top: -50px
    }
}

._social_hd3fb_262 {
    display: flex;
    list-style: none;
    padding: 0
}

._socialItem_hd3fb_267 a {
    color: var(--theme-color-primary);
    margin-right: 5px
}

._socialItem_hd3fb_267 a i {
    font-size: 24px
}

.rtl ._buttonsContainer_hd3fb_209 ._secondaryButton_hd3fb_226 {
    margin: 0 20px 0 0 !important
}

@media (max-width: 992px) {
    .rtl ._buttonsContainer_hd3fb_209 ._secondaryButton_hd3fb_226 {
        margin: 20px 0 0 !important
    }
}

._buttonsWrapper_1nkba_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1nkba_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1nkba_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1nkba_144 {
    margin-bottom: 30px
}

._sectionContainer_1nkba_165 {
    padding: 70px 0;
    background-color: #fff
}

._sectionContainer_1nkba_165 ._sectionTitle_1nkba_169 h1 {
    color: var(--theme-color-primary)
}

@media (max-width: 992px) {
    ._sectionContainer_1nkba_165 ._sectionTitle_1nkba_169 h1 {
        font-size: 24px
    }
}

._imageContainer_1nkba_178 {
    margin: 30px 0
}

._imageContainer_1nkba_178 img {
    max-width: 100%
}

@media (max-width: 768px) {
    ._imageContainer_1nkba_178 img {
        max-width: 50%
    }
}

._buttonsWrapper_o5zv5_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_o5zv5_138 {
    margin-bottom: 16px
}

._spaceAfter--small_o5zv5_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_o5zv5_144 {
    margin-bottom: 30px
}

._services_o5zv5_165 {
    background: url(https://assets.wuiltsite.com/_azzafahmy/pattern_2.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: #222120;
    background-position: top left;
    padding: 50px 0 70px
}

@media (max-width: 992px) {
    ._services_o5zv5_165 {
        background-size: cover;
        background-position: left center
    }
}

._services_o5zv5_165 h1 {
    color: var(--theme-color-primary)
}

@media (max-width: 992px) {
    ._services_o5zv5_165 h1 {
        font-size: 24px
    }
}

._services_o5zv5_165 ._item_o5zv5_187 {
    margin-top: 50px
}

._services_o5zv5_165 ._item_o5zv5_187 ._itemImage_o5zv5_190 ._image_o5zv5_190 {
    box-shadow: 10px 10px var(--theme-color-primary)
}

._services_o5zv5_165 ._item_o5zv5_187 ._itemText_o5zv5_193 h4 {
    font-size: 26px;
    margin: 30px 0 20px;
    color: var(--theme-color-primary)
}

@media (max-width: 992px) {
    ._services_o5zv5_165 ._item_o5zv5_187 ._itemText_o5zv5_193 h4 {
        font-size: 24px
    }
}

._services_o5zv5_165 ._item_o5zv5_187 ._itemText_o5zv5_193 p {
    color: #fff;
    font-size: 18px;
    line-height: 26px
}

@media (max-width: 992px) {
    ._services_o5zv5_165 ._item_o5zv5_187 ._itemText_o5zv5_193 p {
        font-size: 14px
    }
}

._services_o5zv5_165 ._item_o5zv5_187 ._buttons_o5zv5_132 {
    display: flex
}

._services_o5zv5_165 ._item_o5zv5_187 ._itemBtn1_o5zv5_216,
._services_o5zv5_165 ._item_o5zv5_187 ._itemBtn2_o5zv5_217 {
    display: flex;
    align-items: center
}

._services_o5zv5_165 ._item_o5zv5_187 ._itemBtn1_o5zv5_216 {
    font-size: 14px;
    font-weight: 700;
    margin-inline-end: 10px
}

._services_o5zv5_165 ._item_o5zv5_187 ._itemBtn2_o5zv5_217 {
    font-size: 14px;
    font-weight: 700;
    background-color: transparent
}

._buttonsWrapper_1ozb8_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ozb8_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ozb8_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ozb8_144 {
    margin-bottom: 30px
}

._sectionContainer_1ozb8_165 {
    padding: 50px 0 70px
}

._sectionContainer_1ozb8_165 ._containerImage_1ozb8_168 {
    height: 520px;
    width: 100%;
    border-radius: 0 200px 0 0;
    border: 5px solid var(--theme-color-gradient)
}

._sectionContainer_1ozb8_165 ._sectionText_1ozb8_174 {
    padding: 0 0 0 80px
}

@media (max-width: 992px) {
    ._sectionContainer_1ozb8_165 ._sectionText_1ozb8_174 {
        padding: 0
    }
}

._sectionContainer_1ozb8_165 ._sectionText_1ozb8_174 ._dash_1ozb8_182 {
    width: 44px;
    height: 6px;
    background: var(--theme-color-gradient);
    margin: 0 10px 0 0
}

._sectionContainer_1ozb8_165 ._sectionText_1ozb8_174 h2 {
    color: var(--theme-color-primary);
    margin: 0
}

._sectionContainer_1ozb8_165 ._sectionText_1ozb8_174 p {
    font-size: 16px;
    line-height: 1.75;
    color: var(--theme-color-secondary);
    margin-top: 20px
}

._sectionContainer_1ozb8_165 ._sectionBtn_1ozb8_198 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 300;
    color: var(--theme-color-gradient);
    background-color: var(--theme-color-primary);
    border-radius: 2px;
    width: 50%;
    font-weight: 700;
    margin: 40px 0 0;
    padding: 14px 0
}

@media (max-width: 992px) {
    ._sectionContainer_1ozb8_165 ._sectionBtn_1ozb8_198 {
        width: 100%
    }
}

.rtl ._sectionText_1ozb8_174 ._dash_1ozb8_182 {
    margin: 0 0 0 10px
}

.rtl ._containerImage_1ozb8_168 {
    border-radius: 200px 0 0
}

.rtl ._sectionText_1ozb8_174 {
    padding: 0 80px 0 0
}

@media (max-width: 992px) {
    .rtl ._sectionText_1ozb8_174 {
        padding: 0
    }
}

._buttonsWrapper_vbpxj_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_vbpxj_138 {
    margin-bottom: 16px
}

._spaceAfter--small_vbpxj_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_vbpxj_144 {
    margin-bottom: 30px
}

._sectionContainer_vbpxj_165 {
    padding: 50px 0 70px
}

._sectionContainer_vbpxj_165 ._sectionHeader_vbpxj_168 {
    margin-bottom: 50px
}

._sectionContainer_vbpxj_165 ._sectionHeader_vbpxj_168 ._dash_vbpxj_171 {
    width: 44px;
    height: 6px;
    background: var(--theme-color-gradient);
    margin-right: 10px
}

._sectionContainer_vbpxj_165 ._sectionHeader_vbpxj_168 h2 {
    color: var(--theme-color-primary);
    margin: 0;
    width: 94%
}

._sectionContainer_vbpxj_165 ._sectionHeader_vbpxj_168 p {
    font-size: 16px;
    color: var(--theme-color-secondary);
    margin-top: 10px
}

._sectionContainer_vbpxj_165 ._contactFields_vbpxj_187 {
    text-align: left;
    width: 85%
}

._sectionContainer_vbpxj_165 ._contactFields_vbpxj_187 ._textField_vbpxj_191,
._sectionContainer_vbpxj_165 ._contactFields_vbpxj_187 ._textareaField_vbpxj_192 {
    color: var(--theme-color-secondary);
    border: 1px solid var(--theme-color-gradient);
    border-radius: 4px
}

._sectionContainer_vbpxj_165 ._contactFields_vbpxj_187 ._textareaField_vbpxj_192 {
    height: 80px
}

._sectionContainer_vbpxj_165 ._contactFields_vbpxj_187 ._submitButton_vbpxj_200 {
    display: flex;
    color: var(--theme-color-gradient);
    background-color: var(--theme-color-primary);
    border-radius: 3px;
    padding: 8px 24px;
    border: none;
    outline: 0;
    cursor: pointer
}

._sectionContainer_vbpxj_165 ._contactInfo_vbpxj_210 ._info_vbpxj_210 {
    margin-bottom: 40px
}

._sectionContainer_vbpxj_165 ._contactInfo_vbpxj_210 ._info_vbpxj_210 h5 {
    color: var(--theme-color-primary);
    font-size: 16px
}

._sectionContainer_vbpxj_165 ._contactInfo_vbpxj_210 ._info_vbpxj_210 p {
    font-size: 16px;
    color: var(--theme-color-secondary)
}

._sectionContainer_vbpxj_165 ._contactInfo_vbpxj_210 ._social_vbpxj_221 {
    display: flex;
    list-style: none;
    padding: 0;
    margin-left: -3px
}

._sectionContainer_vbpxj_165 ._contactInfo_vbpxj_210 ._socialItem_vbpxj_227 a {
    color: var(--theme-color-secondary);
    margin: 0 5px 0 0
}

._sectionContainer_vbpxj_165 ._contactInfo_vbpxj_210 ._socialItem_vbpxj_227 a i {
    font-size: 30px !important
}

._sectionContainer_vbpxj_165 ._contactInfo_vbpxj_210 ._socialItem_vbpxj_227 a svg {
    height: 35px !important;
    margin-top: -3px
}

.rtl ._sectionContainer_vbpxj_165 ._sectionHeader_vbpxj_168 ._dash_vbpxj_171 {
    margin: 0 0 0 10px
}

.rtl ._sectionContainer_vbpxj_165 ._contactInfo_vbpxj_210 ._socialItem_vbpxj_227 a {
    margin: 0 0 0 5px
}

._actionContainer_vbpxj_246 {
    display: flex;
    justify-content: flex-start
}

._buttonsWrapper_177em_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_177em_138 {
    margin-bottom: 16px
}

._spaceAfter--small_177em_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_177em_144 {
    margin-bottom: 30px
}

._sectionContainer_177em_165 {
    padding: 50px 0 70px
}

._sectionContainer_177em_165 ._sectionHeader_177em_168 {
    margin-bottom: 50px
}

._sectionContainer_177em_165 ._sectionHeader_177em_168 ._dash_177em_171 {
    width: 44px;
    height: 6px;
    background: var(--theme-color-gradient);
    margin-right: 10px
}

._sectionContainer_177em_165 ._sectionHeader_177em_168 h2 {
    color: var(--theme-color-primary);
    width: 94%
}

._sectionContainer_177em_165 ._sectionHeader_177em_168 p {
    font-size: 16px;
    color: var(--theme-color-secondary);
    margin: 10px 0 0
}

._sectionContainer_177em_165 ._mapContainer_177em_186 {
    position: relative;
    width: 100%;
    height: 600px;
    overflow-y: hidden
}

@media (max-width: 992px) {
    ._sectionContainer_177em_165 ._mapContainer_177em_186 {
        height: 650px
    }
}

._sectionContainer_177em_165 ._mapContainer_177em_186 ._map_177em_186 {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%
}

._sectionContainer_177em_165 ._mapContainer_177em_186 ._contactInfo_177em_205 {
    width: 80%;
    height: -moz-fit-content;
    height: fit-content;
    right: 10%;
    bottom: 20px;
    position: absolute;
    z-index: 2;
    background-color: #fff;
    padding: 20px;
    box-shadow: 0 0 1px #1a202452, 0 4px 8px #5b68713d;
    border-radius: 4px
}

._sectionContainer_177em_165 ._mapContainer_177em_186 ._contactInfo_177em_205 ._info_177em_217 h5 {
    font-size: 16px;
    color: var(--theme-color-primary)
}

._sectionContainer_177em_165 ._mapContainer_177em_186 ._contactInfo_177em_205 ._info_177em_217 p {
    font-size: 16px;
    color: var(--theme-color-secondary)
}

._sectionContainer_177em_165 ._contactFields_177em_225 {
    width: 90%;
    height: -moz-fit-content;
    height: fit-content
}

@media (max-width: 992px) {
    ._sectionContainer_177em_165 ._contactFields_177em_225 {
        width: 100%;
        margin-top: 40px
    }
}

._sectionContainer_177em_165 ._contactFields_177em_225 ._textField_177em_235,
._sectionContainer_177em_165 ._contactFields_177em_225 ._textareaField_177em_236 {
    color: var(--theme-color-secondary)
}

._sectionContainer_177em_165 ._contactFields_177em_225 ._textareaField_177em_236 {
    height: 150px
}

._sectionContainer_177em_165 ._contactFields_177em_225 ._submitButton_177em_242 {
    color: var(--theme-color-gradient);
    background-color: var(--theme-color-primary);
    border-radius: 3px;
    padding: 8px 24px;
    border: none;
    outline: 0;
    cursor: pointer;
    min-width: 180px
}

._sectionContainer_177em_165 ._contactFields_177em_225 h5 {
    font-size: 15px;
    color: var(--theme-color-primary);
    margin-top: 30px
}

._sectionContainer_177em_165 ._contactFields_177em_225 ._social_177em_257 {
    display: flex;
    list-style: none;
    padding: 0;
    margin-left: -3px;
    width: -moz-fit-content;
    width: fit-content
}

._sectionContainer_177em_165 ._contactFields_177em_225 ._socialItem_177em_264 a {
    color: var(--theme-color-secondary);
    margin: 0 5px 0 0
}

._sectionContainer_177em_165 ._contactFields_177em_225 ._socialItem_177em_264 a i {
    font-size: 30px !important
}

._sectionContainer_177em_165 ._contactFields_177em_225 ._socialItem_177em_264 a svg {
    height: 35px !important;
    margin-top: -3px
}

.rtl ._sectionContainer_177em_165 ._sectionHeader_177em_168 ._dash_177em_171 {
    margin: 0 0 0 10px
}

.rtl ._sectionContainer_177em_165 ._contactFields_177em_225 ._socialItem_177em_264 a {
    margin: 0 0 0 5px
}

._actionContainer_177em_283 {
    display: flex;
    justify-content: flex-start
}

._buttonsWrapper_ex0hu_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_ex0hu_138 {
    margin-bottom: 16px
}

._spaceAfter--small_ex0hu_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_ex0hu_144 {
    margin-bottom: 30px
}

._sectionContainer_ex0hu_165 {
    padding: 50px 0 70px
}

._sectionContainer_ex0hu_165 ._sectionHeader_ex0hu_168 {
    margin-bottom: 50px
}

._sectionContainer_ex0hu_165 ._sectionHeader_ex0hu_168 ._dash_ex0hu_171 {
    width: 44px;
    height: 6px;
    background: var(--theme-color-gradient);
    margin: 0 10px 0 0
}

._sectionContainer_ex0hu_165 ._sectionHeader_ex0hu_168 h2 {
    color: var(--theme-color-primary);
    width: 94%
}

._sectionContainer_ex0hu_165 ._sectionHeader_ex0hu_168 p {
    font-size: 16px;
    color: var(--theme-color-secondary);
    margin: 10px 0 0
}

._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186 {
    height: 580px;
    overflow-y: auto;
    background-color: var(--theme-color-gradient);
    margin: 0 0 0 10px
}

@media (max-width: 992px) {
    ._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186 {
        height: 800px
    }
}

._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186::-webkit-scrollbar {
    width: 6px
}

._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186::-webkit-scrollbar-thumb {
    background: #bab5b0;
    border-radius: 5px
}

._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186 h5 {
    color: var(--theme-color-primary);
    font-size: 12px;
    font-weight: 600;
    padding: 20px
}

._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186 ._info_ex0hu_210 {
    cursor: pointer;
    padding: 20px
}

._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186 ._info_ex0hu_210 h4 {
    color: var(--theme-color-primary);
    margin-bottom: 5px
}

._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186 ._info_ex0hu_210 p {
    font-size: 14px;
    color: var(--theme-color-secondary);
    margin-bottom: 5px
}

._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186 ._info_ex0hu_210 h6 {
    color: var(--theme-color-secondary)
}

._sectionContainer_ex0hu_165 ._mapContainer_ex0hu_226 {
    height: 100%;
    width: 100%;
    margin: 0 10px 0 0
}

@media (max-width: 992px) {
    ._sectionContainer_ex0hu_165 ._mapContainer_ex0hu_226 {
        height: 70%
    }
}

._sectionContainer_ex0hu_165 ._mapContainer_ex0hu_226 ._map_ex0hu_226 {
    width: 100%;
    height: 100%
}

._activeLoc_ex0hu_241 {
    background-color: var(--theme-color-primary);
    color: var(--theme-color-gradient)
}

._activeLoc_ex0hu_241 h4 {
    color: #fff !important
}

._activeLoc_ex0hu_241 p,
._activeLoc_ex0hu_241 h6 {
    color: var(--theme-color-gradient) !important
}

.rtl ._sectionContainer_ex0hu_165 ._sectionHeader_ex0hu_168 ._dash_ex0hu_171 {
    margin: 0 0 0 10px
}

.rtl ._sectionContainer_ex0hu_165 ._locationInfo_ex0hu_186 {
    margin: 0 12px 0 0
}

.rtl ._sectionContainer_ex0hu_165 ._mapContainer_ex0hu_226 {
    margin: 0 0 0 10px
}

._buttonsWrapper_1qzdk_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1qzdk_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1qzdk_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1qzdk_144 {
    margin-bottom: 30px
}

._sectionContainer_1qzdk_165 {
    padding: 50px 0 70px
}

._sectionContainer_1qzdk_165 ._sectionTitle_1qzdk_168 {
    border-right: 4px solid var(--theme-color-gradient);
    height: 100%;
    padding: 0 10px 0 0
}

@media (max-width: 768px) {
    ._sectionContainer_1qzdk_165 ._sectionTitle_1qzdk_168 {
        border-right: none;
        border-bottom: 4px solid var(--theme-color-gradient);
        padding-bottom: 10px
    }
}

._sectionContainer_1qzdk_165 ._sectionTitle_1qzdk_168 h1 {
    color: var(--theme-color-primary)
}

._sectionContainer_1qzdk_165 ._sectionText_1qzdk_183 {
    padding: 5px 0 0 30px
}

@media (max-width: 768px) {
    ._sectionContainer_1qzdk_165 ._sectionText_1qzdk_183 {
        padding: 20px 0 0
    }
}

._sectionContainer_1qzdk_165 ._sectionText_1qzdk_183 p {
    font-size: 16px;
    color: var(--theme-color-secondary)
}

.rtl ._sectionTitle_1qzdk_168 {
    border-right: none;
    border-left: 4px solid var(--theme-color-gradient);
    padding: 0 0 0 10px
}

@media (max-width: 768px) {
    .rtl ._sectionTitle_1qzdk_168 {
        border-left: none
    }
}

._buttonsWrapper_kz4fc_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_kz4fc_138 {
    margin-bottom: 16px
}

._spaceAfter--small_kz4fc_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_kz4fc_144 {
    margin-bottom: 30px
}

._sectionContainer_kz4fc_165 {
    padding: 50px 0 70px
}

._sectionContainer_kz4fc_165 ._sectionContent_kz4fc_168 {
    height: 100%;
    width: 100%;
    position: relative
}

._sectionContainer_kz4fc_165 ._sectionContent_kz4fc_168 ._imageContainer_kz4fc_173,
._sectionContainer_kz4fc_165 ._sectionContent_kz4fc_168 ._imageContainer_kz4fc_173 ._image_kz4fc_173 {
    width: 100%;
    height: 555px
}

._sectionContainer_kz4fc_165 ._sectionContent_kz4fc_168 ._sectionText_kz4fc_181 {
    height: -moz-fit-content;
    height: fit-content;
    width: 42%;
    padding: 20px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    right: 0
}

@media (max-width: 992px) {
    ._sectionContainer_kz4fc_165 ._sectionContent_kz4fc_168 ._sectionText_kz4fc_181 {
        width: 100%
    }
}

._sectionContainer_kz4fc_165 ._sectionContent_kz4fc_168 ._sectionText_kz4fc_181 h1 {
    color: var(--theme-color-primary);
    margin-bottom: 10px
}

._sectionContainer_kz4fc_165 ._sectionContent_kz4fc_168 ._sectionText_kz4fc_181 p {
    font-size: 16px;
    color: var(--theme-color-secondary)
}

._footer_1j0qf_1 {
    padding: 50px 0 30px;
    background: var(--theme-color-primary);
    color: var(--theme-color-gradient)
}

._footer_1j0qf_1 ._footerLogo_1j0qf_6 h1 {
    width: -moz-fit-content;
    width: fit-content;
    font-size: 30px
}

._footerLinks_1j0qf_11 {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: 0
}

@media (max-width: 992px) {
    ._footerLinks_1j0qf_11 {
        flex-direction: column;
        align-items: flex-start
    }
}

._footerLinks_1j0qf_11 ._navLink_1j0qf_25 {
    color: var(--theme-color-gradient);
    font-weight: 600;
    margin: 0 8px
}

@media (max-width: 992px) {
    ._footerLinks_1j0qf_11 ._navLink_1j0qf_25 {
        margin: 8px 0
    }
}

._footerCopyright_1j0qf_36 {
    width: -moz-fit-content;
    width: fit-content;
    margin: 0 auto
}

@media (max-width: 992px) {
    ._footerCopyright_1j0qf_36 {
        margin: 0
    }
}

._footerCopyright_1j0qf_36 p {
    font-size: 13px;
    color: var(--theme-color-gradient)
}

._socialLinks_1j0qf_50 {
    width: -moz-fit-content;
    width: fit-content
}

._socialLinks_1j0qf_50 ._social_1j0qf_50 {
    display: flex;
    list-style: none;
    padding: 0
}

._socialLinks_1j0qf_50 ._socialItem_1j0qf_58 a {
    color: var(--theme-color-gradient);
    margin-right: 5px
}

._socialLinks_1j0qf_50 ._socialItem_1j0qf_58 a i {
    font-size: 24px
}

._buttonsWrapper_1rtos_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1rtos_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1rtos_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1rtos_144 {
    margin-bottom: 30px
}

._sectionContainer_1rtos_165 {
    padding: 50px 0 70px;
    background: url(https://assets.wuiltsite.com/_damas/damas_hero_bg.jpg);
    background-size: 100% 100%;
    background-position: center center
}

._sectionContainer_1rtos_165 ._sectionHeader_1rtos_171 {
    margin-bottom: 20px;
    display: flex;
    align-items: center
}

._sectionContainer_1rtos_165 ._sectionHeader_1rtos_171 ._dash_1rtos_176 {
    width: 44px;
    height: 6px;
    background: var(--theme-color-gradient);
    margin: 0 10px 0 0
}

._sectionContainer_1rtos_165 ._sectionHeader_1rtos_171 h2 {
    width: 94%;
    color: var(--theme-color-primary);
    margin: 0
}

._sectionContainer_1rtos_165 ._galleryContainer_1rtos_187 {
    display: grid;
    grid-template-columns: auto auto auto auto auto
}

@media (max-width: 992px) {
    ._sectionContainer_1rtos_165 ._galleryContainer_1rtos_187 {
        grid-template-columns: auto auto auto
    }
}

@media (max-width: 768px) {
    ._sectionContainer_1rtos_165 ._galleryContainer_1rtos_187 {
        grid-template-columns: auto auto
    }
}

._sectionContainer_1rtos_165 ._primaryButton_1rtos_201 {
    display: flex;
    justify-content: center;
    font-weight: 500;
    text-align: center;
    color: var(--theme-color-gradient);
    background-color: var(--theme-color-primary);
    border-radius: 2px;
    padding: 14px 0;
    width: 30%;
    margin: 30px auto 0;
    font-weight: 700
}

@media (max-width: 992px) {
    ._sectionContainer_1rtos_165 ._primaryButton_1rtos_201 {
        width: 100%
    }
}

.rtl ._sectionContainer_1rtos_165 ._sectionHeader_1rtos_171 ._dash_1rtos_176 {
    margin: 0 0 0 10px
}

@media (max-width: 992px) {
    ._heroLogo_1chsu_2 {
        margin: 0 0 0 auto
    }
}

._social_1chsu_7 {
    display: flex;
    list-style: none;
    padding: 0
}

._socialItem_1chsu_12 a {
    color: var(--theme-color-primary);
    margin-right: 5px
}

._socialItem_1chsu_12 a i {
    font-size: 24px
}

._burger_1chsu_20 {
    padding-left: 0 !important
}

@media (max-width: 992px) {
    ._burger_1chsu_20 {
        transform: rotateX(180deg);
        transform: rotateY(180deg)
    }
}

._navMenu_1chsu_30 ._navLink_1chsu_30 {
    color: #452130;
    font-weight: 600;
    margin: 0 5px
}

._navMenu_1chsu_30 ._navLink_1chsu_30:hover {
    color: #452130
}

._background_7p6b1_1 {
    background: url(https://assets.wuiltsite.com/_damas/damas_hero_bg.jpg);
    background-size: 100% 100%
}

._content_7p6b1_6 {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px
}

._content_7p6b1_6 h1 {
    display: inline-block;
    color: var(--theme-color-primary);
    margin-bottom: 40px
}

._hero_1h1m9_1 {
    background: url(https://assets.wuiltsite.com/_damas/damas_hero_bg.jpg);
    background-size: 100% 100%
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 {
    margin-top: 50px
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 h1 {
    font-size: 48px;
    color: var(--theme-color-primary);
    margin-bottom: 30px
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 p {
    font-weight: 700;
    color: var(--theme-color-primary);
    font-size: 14px
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 ._buttonsContainer_1h1m9_25 {
    display: flex;
    width: 100%
}

@media (max-width: 992px) {
    ._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 ._buttonsContainer_1h1m9_25 {
        flex-direction: column
    }
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 ._buttonsContainer_1h1m9_25 ._primaryButton_1h1m9_34 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 300;
    text-align: center;
    color: var(--theme-color-gradient);
    background-color: var(--theme-color-primary);
    border-radius: 2px;
    width: 50%;
    font-weight: 700;
    padding: 16px 0
}

@media (max-width: 992px) {
    ._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 ._buttonsContainer_1h1m9_25 ._primaryButton_1h1m9_34 {
        width: 100%
    }
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 ._buttonsContainer_1h1m9_25 ._primaryButton_1h1m9_34 a {
    color: var(--theme-color-gradient)
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 ._buttonsContainer_1h1m9_25 ._secondaryButton_1h1m9_55 {
    display: flex;
    color: var(--theme-color-primary);
    align-items: center;
    justify-content: center;
    font-weight: 500;
    min-width: 150px;
    width: 30%;
    border: 2px solid var(--theme-color-primary);
    border-radius: 2px;
    padding: 14px 0;
    margin: 0 0 0 20px;
    font-weight: 700
}

@media (max-width: 992px) {
    ._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 ._buttonsContainer_1h1m9_25 ._secondaryButton_1h1m9_55 {
        width: 100%;
        margin: 20px 0 0
    }
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._heroText_1h1m9_8 ._buttonsContainer_1h1m9_25 ._secondaryButton_1h1m9_55 a {
    color: var(--theme-color-primary)
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._imagesContainer_1h1m9_78 {
    display: flex;
    width: 100%;
    height: 100%;
    margin: 30px 0 0
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._imagesContainer_1h1m9_78 ._mainImg_1h1m9_84 {
    width: 100%;
    height: 100%;
    margin: 80px -30% 0 0;
    position: relative;
    z-index: 2
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._imagesContainer_1h1m9_78 ._mainImg_1h1m9_84 ._image_1h1m9_78 {
    border-radius: 200px 200px 0 0;
    border: 5px solid var(--theme-color-gradient)
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._imagesContainer_1h1m9_78 ._subImg_1h1m9_95 {
    width: 100%
}

._hero_1h1m9_1 ._heroContent_1h1m9_5 ._imagesContainer_1h1m9_78 ._subImg_1h1m9_95 img {
    width: 100%;
    height: 85%
}

.rtl ._mainImg_1h1m9_84 {
    margin: 80px 0 0 -30% !important
}

.rtl ._buttonsContainer_1h1m9_25 ._secondaryButton_1h1m9_55 {
    margin: 0 20px 0 0 !important
}

@media (max-width: 992px) {
    .rtl ._buttonsContainer_1h1m9_25 ._secondaryButton_1h1m9_55 {
        margin: 20px 0 0 !important
    }
}

._buttonsWrapper_1u4ks_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1u4ks_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1u4ks_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1u4ks_144 {
    margin-bottom: 30px
}

._sectionContainer_1u4ks_165 {
    padding: 50px 0 70px
}

._sectionContainer_1u4ks_165 ._sectionHeader_1u4ks_168 {
    margin-bottom: 80px;
    display: flex;
    align-items: center
}

._sectionContainer_1u4ks_165 ._sectionHeader_1u4ks_168 ._dash_1u4ks_173 {
    width: 44px;
    height: 6px;
    background: var(--theme-color-gradient);
    margin: 0 10px 0 0
}

._sectionContainer_1u4ks_165 ._sectionHeader_1u4ks_168 h2 {
    color: var(--theme-color-primary);
    margin: 0;
    width: 94%
}

._sectionContainer_1u4ks_165 ._productItem_1u4ks_184 {
    background: url(https://assets.wuiltsite.com/_damas/damas_featured_product_bg.jpg);
    background-size: 100% 100%;
    background-position: center center;
    border-radius: 0 100px 0 0;
    padding: 0 10px 10px;
    display: flex;
    margin-top: 70px
}

._sectionContainer_1u4ks_165 ._productItem_1u4ks_184 ._containerImage_1u4ks_193 {
    height: 100%;
    width: 100%;
    margin: -40px 0 0
}

._sectionContainer_1u4ks_165 ._productItem_1u4ks_184 ._containerImage_1u4ks_193 ._image_1u4ks_198 {
    border: 5px solid var(--theme-color-gradient)
}

._sectionContainer_1u4ks_165 ._productItem_1u4ks_184 ._itemText_1u4ks_201 {
    padding: 20px 10px
}

@media (max-width: 992px) {
    ._sectionContainer_1u4ks_165 ._productItem_1u4ks_184 ._itemText_1u4ks_201 {
        padding-left: 0
    }
}

._sectionContainer_1u4ks_165 ._productItem_1u4ks_184 ._itemText_1u4ks_201 h2,
._sectionContainer_1u4ks_165 ._productItem_1u4ks_184 ._itemText_1u4ks_201 h4 {
    color: var(--theme-color-gradient);
    margin: 20px 0;
    width: -moz-fit-content;
    width: fit-content
}

._sectionContainer_1u4ks_165 ._productItem_1u4ks_184 ._itemText_1u4ks_201 p {
    font-size: 16px;
    line-height: 1.75;
    color: var(--theme-color-gradient)
}

._sectionBtn_1u4ks_221 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 300;
    color: var(--theme-color-gradient);
    background-color: var(--theme-color-primary);
    border-radius: 2px;
    width: 30%;
    font-weight: 700;
    margin: 30px auto 0;
    padding: 14px 0
}

@media (max-width: 992px) {
    ._sectionBtn_1u4ks_221 {
        width: 100%
    }
}

.rtl ._sectionContainer_1u4ks_165 ._sectionHeader_1u4ks_168 ._dash_1u4ks_173 {
    margin: 0 0 0 10px
}

._buttonsWrapper_3wqr0_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_3wqr0_138 {
    margin-bottom: 16px
}

._spaceAfter--small_3wqr0_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_3wqr0_144 {
    margin-bottom: 30px
}

._sectionContainer_3wqr0_165 {
    padding: 50px 0 70px;
    background: url(https://assets.wuiltsite.com/_damas/damas_section_bg.jpg);
    background-size: 100% 100%
}

._sectionContainer_3wqr0_165 ._sectionHeader_3wqr0_170 {
    margin-bottom: 25px;
    display: flex;
    align-items: center
}

._sectionContainer_3wqr0_165 ._sectionHeader_3wqr0_170 ._dash_3wqr0_175 {
    width: 44px;
    height: 6px;
    background: var(--theme-color-gradient);
    margin: 0 10px 0 0
}

._sectionContainer_3wqr0_165 ._sectionHeader_3wqr0_170 h2 {
    width: 94%;
    color: var(--theme-color-primary);
    margin: 0
}

._itemContainer_3wqr0_187 {
    background: var(--theme-color-primary);
    border-radius: 0 100px 0 0;
    margin: 30px 0 10px;
    display: flex;
    flex-direction: column
}

._itemContainer_3wqr0_187 ._imgContainer_3wqr0_194 {
    width: 95%;
    height: -moz-fit-content;
    height: fit-content;
    margin: -20px 0 0 2.5%
}

._itemContainer_3wqr0_187 ._imgContainer_3wqr0_194 ._image_3wqr0_199 {
    border: 5px solid var(--theme-color-gradient)
}

._itemContainer_3wqr0_187 ._btnContainer_3wqr0_202 {
    margin: 10px 0 15px 4.5%;
    display: flex
}

._itemContainer_3wqr0_187 ._btnContainer_3wqr0_202 ._itemBtn_3wqr0_206 {
    font-size: 26px;
    color: var(--theme-color-gradient)
}

.rtl ._sectionContainer_3wqr0_165 ._sectionHeader_3wqr0_170 ._dash_3wqr0_175 {
    margin: 0 0 0 10px
}

.rtl ._itemContainer_3wqr0_187 {
    border-radius: 100px 0 0
}

.rtl ._itemContainer_3wqr0_187 ._imgContainer_3wqr0_194 {
    margin: -20px 2.5% 0 0
}

.rtl ._itemContainer_3wqr0_187 ._btnContainer_3wqr0_202 {
    margin: 10px 4.5% 15px 0
}

._buttonsWrapper_hkdy6_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_hkdy6_138 {
    margin-bottom: 16px
}

._spaceAfter--small_hkdy6_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_hkdy6_144 {
    margin-bottom: 30px
}

._containerabout_hkdy6_165 {
    padding: 80px 24px;
    min-height: auto
}

._centerCol_hkdy6_170 {
    display: flex;
    align-items: center
}

._content_hkdy6_175 {
    margin: 10px 10px 40px;
    width: 100%
}

._ImageStyle_hkdy6_181 {
    display: flex;
    align-items: center
}

._containerImage_hkdy6_186 {
    box-shadow: 0 10px 40px #21252940
}

._containerImage_hkdy6_186 img {
    display: flex;
    align-items: center;
    width: 100%
}

._buttonsWrapper_9tp8c_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_9tp8c_138 {
    margin-bottom: 16px
}

._spaceAfter--small_9tp8c_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_9tp8c_144 {
    margin-bottom: 30px
}

._body_9tp8c_165 {
    padding: 120px 50px
}

._buttonsWrapper_1288g_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1288g_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1288g_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1288g_144 {
    margin-bottom: 30px
}

._outerContainer_1288g_165 {
    position: relative;
    display: flex;
    flex-direction: column-reverse;
    overflow: hidden
}

._container_1288g_172 {
    padding: 80px 0
}

._containerImage_1288g_176 {
    height: 400px;
    width: 100%
}

._containerImage_1288g_176 ._imgWrapper_1288g_180,
._containerImage_1288g_176 span {
    height: 100%;
    width: 100%
}

._containerImage_1288g_176 img {
    display: flex;
    align-items: center;
    width: 100% !important;
    height: 100% !important;
    max-height: unset !important
}

@media (min-width: 768px) {
    ._containerImage_1288g_176 {
        height: 100%;
        position: absolute !important;
        width: 50%;
        height: auto;
        top: 0;
        bottom: 0
    }
}

._rowStyle_1288g_203 {
    justify-content: flex-end;
    margin: 0
}

._content_1288g_208 {
    width: 90%;
    padding: 30px
}

._buttonsWrapper_1m7yl_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1m7yl_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1m7yl_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1m7yl_144 {
    margin-bottom: 30px
}

._containerabout_1m7yl_165 {
    position: relative;
    padding: 100px 0
}

._alignCenter_1m7yl_170 {
    justify-content: center
}

._contentBox_1m7yl_174 {
    --text-color: #000;
    background-color: #fff;
    text-align: center;
    padding: 36px 40px 27px;
    border-radius: 4px;
    margin: 5% 0
}

._contentBox_1m7yl_174 p {
    margin: 0 auto
}

._buttonsWrapper_1sgv4_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1sgv4_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1sgv4_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1sgv4_144 {
    margin-bottom: 30px
}

._containerabout_1sgv4_165 {
    padding: 80px 0
}

._contentCol_1sgv4_169 {
    display: flex;
    align-items: center
}

._content_1sgv4_169 {
    margin-top: 6%
}

@media (min-width: 768px) {
    ._content_1sgv4_169 {
        margin-top: 0
    }
}

._btnStyle_1sgv4_183 {
    background: var(--theme-color-brand1);
    color: #fff;
    padding: 8px 32px;
    margin-top: 8px;
    transition: .5s
}

._btnStyle_1sgv4_183:hover {
    color: #fff;
    opacity: .9;
    box-shadow: 0 2px 14px 2px #0003;
    transform: translateY(-2px);
    transition: .5s
}

._ImageStyle_1sgv4_199 {
    display: flex;
    align-items: center;
    justify-content: center
}

._containerImage_1sgv4_205 {
    position: relative;
    margin: 6%;
    width: 90%
}

._containerImage_1sgv4_205 ._firstImg_1sgv4_210 {
    position: relative;
    width: 100%
}

._containerImage_1sgv4_205 ._firstImg_1sgv4_210 img {
    display: flex;
    align-items: center;
    width: 100%
}

._containerImage_1sgv4_205 ._block_1sgv4_219 {
    position: absolute;
    inset: 0;
    background-image: linear-gradient(240deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    transform: translate(-30px, -30px)
}

.rtl ._containerImage_1sgv4_205 ._block_1sgv4_219 {
    transform: translate(30px, -30px)
}

._buttonsWrapper_1g9at_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1g9at_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1g9at_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1g9at_144 {
    margin-bottom: 30px
}

._container_1g9at_165 {
    padding: 70px 0;
    justify-content: center;
    text-align: center
}

._content_1g9at_171 {
    margin: 20px auto;
    width: 90%;
    max-width: 700px
}

._buttonsWrapper_9dddm_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_9dddm_138 {
    margin-bottom: 16px
}

._spaceAfter--small_9dddm_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_9dddm_144 {
    margin-bottom: 30px
}

._section_9dddm_165 {
    padding: 100px 0;
    position: relative
}

._content_9dddm_170 {
    max-width: 700px;
    text-align: center;
    color: #27272a;
    position: relative;
    z-index: 9
}

._buttonsWrapper_197w3_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_197w3_138 {
    margin-bottom: 16px
}

._spaceAfter--small_197w3_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_197w3_144 {
    margin-bottom: 30px
}

._container_197w3_165 {
    padding: 86px 0
}

._centerCol_197w3_169 {
    display: flex;
    align-items: center
}

._content_197w3_174 {
    width: 80%
}

._imageCol_197w3_178 {
    display: flex;
    align-items: center
}

._containerImage_197w3_183 {
    position: relative;
    margin-top: 6%;
    width: 90%
}

._containerImage_197w3_183 ._secondImg_197w3_188 {
    position: relative;
    width: 100%
}

._containerImage_197w3_183 ._secondImg_197w3_188 img {
    display: flex;
    align-items: center;
    width: 100%
}

._containerImage_197w3_183 ._boxStyle_197w3_197 {
    position: absolute;
    inset: 0;
    background-image: linear-gradient(90deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    transform: translate(30px, 30px)
}

.rtl ._containerImage_197w3_183 ._boxStyle_197w3_197 {
    transform: translate(-30px, 30px);
    background-image: linear-gradient(230deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._buttonsWrapper_1ojop_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ojop_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ojop_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ojop_144 {
    margin-bottom: 30px
}

._container_1ojop_165 {
    padding: 80px 0
}

._body_1ojop_169 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 60%;
    margin: 0 auto
}

._imageRow_1ojop_177 {
    justify-content: center
}

._imageRow_1ojop_177 img {
    display: flex;
    align-items: center;
    width: 100%
}

._buttonsWrapper_bvaj2_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_bvaj2_138 {
    margin-bottom: 16px
}

._spaceAfter--small_bvaj2_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_bvaj2_144 {
    margin-bottom: 30px
}

._container_bvaj2_165 {
    display: flex;
    min-height: 640px;
    justify-content: center;
    align-items: center;
    background-size: auto, cover;
    background-position: 0px 0px, 50% 50%;
    color: #27272a;
    padding: 0 16px
}

@media (min-width: 768px) {
    ._container_bvaj2_165 {
        min-height: 600px;
        padding: 60px 36px
    }
}

._buttonsWrapper_1r6c9_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1r6c9_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1r6c9_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1r6c9_144 {
    margin-bottom: 30px
}

._wrapper_1r6c9_165 {
    padding: 70px 0
}

._section_1r6c9_169 {
    padding-right: 36px;
    padding-left: 36px;
    flex-direction: column;
    align-items: center;
    flex: 1
}

._w-layout-grid_1r6c9_187 {
    display: grid;
    grid-auto-columns: 1fr;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-row-gap: 16px;
    grid-column-gap: 16px
}

._content7grid_1r6c9_199 {
    display: grid;
    width: 100%;
    max-width: 1200px;
    margin-right: auto;
    margin-left: auto;
    grid-auto-flow: row;
    grid-auto-columns: 1fr;
    grid-column-gap: 36px;
    grid-row-gap: 36px;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
    grid-template-rows: auto
}

._wnode33e4c0cdd70ab6116a24_1r6c9_216 {
    grid-row-start: 1;
    -ms-grid-row-span: 1;
    grid-row-end: 2;
    grid-column-start: 6;
    -ms-grid-column-span: 7;
    grid-column-end: 13
}

._content7contentwrap_1r6c9_227 {
    --text-color: #000;
    background-color: #fff;
    position: relative;
    z-index: 1;
    display: flex;
    margin-top: 220px;
    padding: 47px 67px 57px;
    flex-direction: column;
    align-items: flex-start;
    box-shadow: 0 3px 40px #0000000d
}

._size1text_1r6c9_250 {
    margin-top: 20px;
    margin-bottom: 25px;
    font-size: 44px;
    line-height: 52px;
    font-weight: 500;
    white-space: normal
}

._paragraph70_1r6c9_259 {
    margin-bottom: 10px;
    opacity: .7;
    max-width: 100%;
    word-wrap: break-word
}

._wnode33e4c0cdd710b6116a24_1r6c9_266 {
    position: relative;
    -ms-grid-column-span: 7;
    grid-column-end: 8;
    grid-column-start: 1;
    -ms-grid-row-span: 1;
    grid-row-end: 2;
    grid-row-start: 1
}

._content7image_1r6c9_278 {
    width: 100%;
    height: 460px;
    background-position: 50% 50%;
    background-size: cover
}

._content7image_1r6c9_278 img {
    display: flex;
    align-items: center;
    width: 100%
}

@media (max-width: 991px) {
    ._section_1r6c9_169 {
        padding-right: 24px;
        padding-left: 24px
    }
    ._content7grid_1r6c9_199 {
        width: 100%;
        max-width: 738px;
        grid-column-gap: 24px;
        grid-row-gap: 30px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
        grid-template-rows: auto
    }
    ._wnode33e4c0cdd70ab6116a24_1r6c9_216 {
        grid-column-start: 4;
        -ms-grid-column-span: 5;
        grid-column-end: 9;
        grid-row-start: 1;
        -ms-grid-row-span: 1;
        grid-row-end: 2
    }
    ._content7contentwrap_1r6c9_227 {
        margin-top: 188px;
        margin-bottom: 67px
    }
    ._wnode33e4c0cdd710b6116a24_1r6c9_266 {
        -ms-grid-column-span: 6;
        grid-column-end: 6;
        align-self: stretch
    }
    ._content7image_1r6c9_278 {
        height: auto;
        min-height: 460px
    }
}

@media (max-width: 767px) {
    ._section_1r6c9_169 {
        padding-right: 20px;
        padding-left: 20px
    }
    ._content7grid_1r6c9_199 {
        max-width: none;
        grid-column-gap: 20px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr
    }
    ._wnode33e4c0cdd70ab6116a24_1r6c9_216 {
        grid-column-start: span 6;
        -ms-grid-column-span: 6;
        grid-column-end: span 6;
        grid-row-start: span 1;
        -ms-grid-row-span: 1;
        grid-row-end: span 1
    }
    ._content7contentwrap_1r6c9_227 {
        margin-top: 0;
        margin-bottom: 0;
        padding: 0;
        background-color: transparent;
        box-shadow: none
    }
    ._size1text_1r6c9_250 {
        font-size: 34px;
        line-height: 42px
    }
    ._wnode33e4c0cdd710b6116a24_1r6c9_266 {
        grid-column-start: span 6;
        -ms-grid-column-span: 6;
        grid-column-end: span 6;
        grid-row-start: span 1;
        -ms-grid-row-span: 1;
        grid-row-end: span 1
    }
}

@media (max-width: 479px) {
    ._section_1r6c9_169 {
        padding-right: 16px;
        padding-left: 16px
    }
    ._content7grid_1r6c9_199 {
        grid-column-gap: 12px;
        grid-template-columns: 1fr 1fr 1fr 1fr
    }
    ._wnode33e4c0cdd70ab6116a24_1r6c9_216 {
        grid-column-start: span 4;
        -ms-grid-column-span: 4;
        grid-column-end: span 4
    }
    ._content7contentwrap_1r6c9_227 {
        margin-bottom: 0
    }
    ._wnode33e4c0cdd710b6116a24_1r6c9_266 {
        grid-column-start: span 4;
        -ms-grid-column-span: 4;
        grid-column-end: span 4
    }
}

._buttonsWrapper_1g9me_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1g9me_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1g9me_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1g9me_144 {
    margin-bottom: 30px
}

._container_1g9me_165 {
    padding: 80px 24px;
    min-height: auto
}

._centerCol_1g9me_170 {
    display: flex;
    align-items: center
}

._content_1g9me_175 {
    margin: 10px 10px 40px;
    width: 100%
}

@media (min-width: 768px) {
    ._content_1g9me_175 {
        margin: 10px
    }
}

._ImageStyle_1g9me_186 {
    display: flex;
    align-items: center
}

._containerImage_1g9me_191 {
    box-shadow: 0 10px 40px #21252940
}

._containerImage_1g9me_191 img {
    display: flex;
    align-items: center;
    width: 100%
}

._buttonsWrapper_13zjn_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_13zjn_138 {
    margin-bottom: 16px
}

._spaceAfter--small_13zjn_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_13zjn_144 {
    margin-bottom: 30px
}

._outerContainer_13zjn_165 {
    position: relative;
    display: flex;
    flex-direction: column-reverse;
    overflow: hidden
}

._containerImage_13zjn_172 {
    height: 400px;
    width: 100%
}

._containerImage_13zjn_172 ._imgWrapper_13zjn_176,
._containerImage_13zjn_172 span {
    height: 100%;
    width: 100%
}

._containerImage_13zjn_172 img {
    display: flex;
    align-items: center;
    width: 100% !important;
    height: 100% !important;
    max-height: unset !important
}

@media (min-width: 768px) {
    ._containerImage_13zjn_172 {
        height: 100%
    }
}

._content_13zjn_194 {
    width: 90%;
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%
}

._buttonsWrapper_qzmry_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_qzmry_138 {
    margin-bottom: 16px
}

._spaceAfter--small_qzmry_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_qzmry_144 {
    margin-bottom: 30px
}

._wrapper_qzmry_165 {
    padding: 70px 0
}

._section_qzmry_169 {
    padding-right: 36px;
    padding-left: 36px;
    flex-direction: column;
    align-items: center;
    flex: 1
}

._w-layout-grid_qzmry_187 {
    display: grid;
    grid-auto-columns: 1fr;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-row-gap: 16px;
    grid-column-gap: 16px
}

._content7grid_qzmry_199 {
    display: grid;
    width: 100%;
    max-width: 1200px;
    margin-right: auto;
    margin-left: auto;
    grid-auto-flow: row;
    grid-auto-columns: 1fr;
    grid-column-gap: 36px;
    grid-row-gap: 36px;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
    grid-template-rows: auto
}

._wnode33e4c0cdd70ab6116a24_qzmry_216 {
    position: relative;
    grid-row-start: 1;
    -ms-grid-row-span: 1;
    grid-row-end: 2;
    grid-column-start: 6;
    -ms-grid-column-span: 7;
    grid-column-end: 13
}

._content7contentwrap_qzmry_228 {
    --text-color: #000;
    background-color: #fff;
    position: relative;
    z-index: 1;
    display: flex;
    margin-top: 220px;
    padding: 47px 67px 57px;
    flex-direction: column;
    align-items: flex-start;
    box-shadow: 0 3px 40px #0000000d
}

._size1text_qzmry_251 {
    margin-top: 20px;
    margin-bottom: 25px;
    font-size: 44px;
    line-height: 52px;
    font-weight: 500;
    white-space: normal
}

._paragraph70_qzmry_260 {
    margin-bottom: 10px;
    opacity: .7;
    max-width: 100%;
    word-wrap: break-word
}

._wnode33e4c0cdd710b6116a24_qzmry_267 {
    -ms-grid-column-span: 7;
    grid-column-end: 8;
    grid-column-start: 1;
    -ms-grid-row-span: 1;
    grid-row-end: 2;
    grid-row-start: 1
}

._content7image_qzmry_278 {
    background-position: 50% 50%;
    background-size: cover
}

@media (max-width: 991px) {
    ._section_qzmry_169 {
        padding-right: 24px;
        padding-left: 24px
    }
    ._content7grid_qzmry_199 {
        width: 100%;
        max-width: 738px;
        grid-column-gap: 24px;
        grid-row-gap: 30px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
        grid-template-rows: auto
    }
    ._wnode33e4c0cdd70ab6116a24_qzmry_216 {
        grid-column-start: 4;
        -ms-grid-column-span: 5;
        grid-column-end: 9;
        grid-row-start: 1;
        -ms-grid-row-span: 1;
        grid-row-end: 2
    }
    ._content7contentwrap_qzmry_228 {
        margin-top: 188px;
        margin-bottom: 67px
    }
    ._wnode33e4c0cdd710b6116a24_qzmry_267 {
        -ms-grid-column-span: 6;
        grid-column-end: 6;
        align-self: stretch
    }
    ._content7image_qzmry_278 {
        height: auto
    }
}

@media (max-width: 767px) {
    ._section_qzmry_169 {
        padding-right: 20px;
        padding-left: 20px
    }
    ._content7grid_qzmry_199 {
        max-width: none;
        grid-column-gap: 20px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr
    }
    ._wnode33e4c0cdd70ab6116a24_qzmry_216 {
        grid-column-start: span 6;
        -ms-grid-column-span: 6;
        grid-column-end: span 6;
        grid-row-start: span 1;
        -ms-grid-row-span: 1;
        grid-row-end: span 1
    }
    ._content7contentwrap_qzmry_228 {
        margin-top: 0;
        margin-bottom: 0;
        padding: 0;
        background-color: transparent;
        box-shadow: none
    }
    ._size1text_qzmry_251 {
        font-size: 34px;
        line-height: 42px
    }
    ._wnode33e4c0cdd710b6116a24_qzmry_267 {
        grid-column-start: span 6;
        -ms-grid-column-span: 6;
        grid-column-end: span 6;
        grid-row-start: span 1;
        -ms-grid-row-span: 1;
        grid-row-end: span 1
    }
}

@media (max-width: 479px) {
    ._section_qzmry_169 {
        padding-right: 16px;
        padding-left: 16px
    }
    ._content7grid_qzmry_199 {
        grid-column-gap: 12px;
        grid-template-columns: 1fr 1fr 1fr 1fr
    }
    ._wnode33e4c0cdd70ab6116a24_qzmry_216 {
        grid-column-start: span 4;
        -ms-grid-column-span: 4;
        grid-column-end: span 4
    }
    ._content7contentwrap_qzmry_228 {
        margin-bottom: 0
    }
    ._wnode33e4c0cdd710b6116a24_qzmry_267 {
        grid-column-start: span 4;
        -ms-grid-column-span: 4;
        grid-column-end: span 4
    }
}

._buttonsWrapper_f2kan_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_f2kan_138 {
    margin-bottom: 16px
}

._spaceAfter--small_f2kan_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_f2kan_144 {
    margin-bottom: 30px
}

._container_f2kan_165 {
    padding-right: 0rem;
    padding-left: 0rem;
    width: 100%;
    max-width: 48rem;
    margin-right: auto;
    margin-left: auto;
    padding-top: 5%;
    padding-bottom: 5%
}

._text_container_f2kan_176 {
    margin-top: 0rem;
    margin-right: auto;
    margin-left: auto;
    text-align: center;
    max-width: 48rem;
    padding-right: 5%;
    padding-left: 5%
}

._buttonsWrapper_14zk1_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_14zk1_138 {
    margin-bottom: 16px
}

._spaceAfter--small_14zk1_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_14zk1_144 {
    margin-bottom: 30px
}

._alignCenter_14zk1_165 {
    justify-content: center
}

._callaction_14zk1_169 {
    --text-color: #000;
    background-color: #fff;
    margin: 8% 0;
    padding: 50px;
    border-radius: 4px;
    text-align: center
}

._callaction_14zk1_169 p {
    width: 100%
}

@media (min-width: 480px) {
    ._callaction_14zk1_169 p {
        width: auto
    }
}

._buttonsWrapper_6iy90_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_6iy90_138 {
    margin-bottom: 16px
}

._spaceAfter--small_6iy90_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_6iy90_144 {
    margin-bottom: 30px
}

._bk_6iy90_165 {
    padding: 2% 0
}

._callaction_6iy90_169 {
    --text-color: #000;
    background-color: #fff;
    padding: 50px;
    border-radius: 2px;
    text-align: center
}

._callaction_6iy90_169 p {
    width: 100%
}

@media (min-width: 480px) {
    ._callaction_6iy90_169 p {
        width: auto
    }
}

._buttonsWrapper_1spsu_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1spsu_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1spsu_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1spsu_144 {
    margin-bottom: 30px
}

._bk_1spsu_165 {
    padding: 2% 0
}

._callaction_1spsu_169 {
    background-color: #fafbfc;
    padding: 50px;
    border-radius: 4px;
    border: 2px solid #ddd
}

._callaction_1spsu_169 ._service-item_1spsu_175 h2 {
    color: #383535;
    font-weight: inherit;
    margin-top: 1%
}

._buttonsWrapper_63o09_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_63o09_138 {
    margin-bottom: 16px
}

._spaceAfter--small_63o09_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_63o09_144 {
    margin-bottom: 30px
}

._section_63o09_165 {
    padding: 2% 0
}

._alignCenter_63o09_169 {
    justify-content: center
}

._callaction_63o09_173 {
    padding: 50px;
    border-radius: 4px;
    position: relative;
    color: #fff
}

._styleColumn_63o09_180 {
    display: flex;
    flex-direction: column;
    justify-content: center
}

._overlay_63o09_186 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--theme-color-brand1);
    opacity: .5;
    border-radius: 4px
}

._buttonsWrapper_ch87r_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_ch87r_138 {
    margin-bottom: 16px
}

._spaceAfter--small_ch87r_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_ch87r_144 {
    margin-bottom: 30px
}

._section_ch87r_165 {
    padding: 104px 0
}

._content_ch87r_169 {
    max-width: 700px;
    text-align: center;
    color: #27272a;
    position: relative;
    z-index: 9
}

._buttonsWrapper_vpqtm_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_vpqtm_138 {
    margin-bottom: 16px
}

._spaceAfter--small_vpqtm_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_vpqtm_144 {
    margin-bottom: 30px
}

._containerImage_vpqtm_165 {
    padding: 104px 0
}

._alignCenter_vpqtm_169 {
    justify-content: center
}

._content_vpqtm_173 {
    text-align: center;
    color: #27272a;
    position: relative;
    z-index: 9
}

._buttonsWrapper_dtyj1_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_dtyj1_138 {
    margin-bottom: 16px
}

._spaceAfter--small_dtyj1_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_dtyj1_144 {
    margin-bottom: 30px
}

._containerabout_dtyj1_165 {
    padding: 80px 24px;
    min-height: auto
}

._centerCol_dtyj1_170 {
    display: flex;
    align-items: center
}

._content_dtyj1_175 {
    margin-top: 0;
    margin-bottom: 35px;
    width: 100%
}

._content_dtyj1_175 ._servicetitle_dtyj1_180 {
    margin-bottom: 2em
}

._content_dtyj1_175 ._servicetitle_dtyj1_180 h2 {
    line-height: 40px;
    font-weight: 600;
    font-size: 36px
}

._content_dtyj1_175 p {
    line-height: 26px;
    width: auto;
    color: #7d7979;
    font-weight: 400
}

@media (min-width: 768px) {
    ._content_dtyj1_175 {
        width: 85%;
        margin-top: 2%;
        margin-bottom: 5px
    }
}

._ImageStyle_dtyj1_202 {
    display: flex;
    align-items: center
}

._containerImage_dtyj1_207 {
    box-shadow: 0 10px 40px #21252940
}

._containerImage_dtyj1_207 img {
    display: flex;
    align-items: center;
    width: 100%
}

._buttonsWrapper_1f83h_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1f83h_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1f83h_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1f83h_144 {
    margin-bottom: 30px
}

._outercontainer_1f83h_165 {
    padding: 50px
}

._outercontainer_1f83h_165 ._servicetitle_1f83h_168 {
    width: 100%;
    margin-bottom: 2em
}

._outercontainer_1f83h_165 ._servicetitle_1f83h_168 h2 {
    line-height: 42px;
    font-weight: 600;
    font-size: 36px
}

@media (min-width: 768px) {
    ._outercontainer_1f83h_165 ._servicetitle_1f83h_168 {
        margin-bottom: 0
    }
}

@media (min-width: 768px) {
    ._outercontainer_1f83h_165 {
        padding: 120px 50px
    }
}

._aboutContent_1f83h_188 p {
    color: #000;
    font-weight: 300;
    opacity: .75
}

._buttonsWrapper_1cicp_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1cicp_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1cicp_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1cicp_144 {
    margin-bottom: 30px
}

._outerContainer_1cicp_165 {
    position: relative;
    overflow: hidden
}

._container_1cicp_170 {
    padding: 80px 0
}

._containerImage_1cicp_174 {
    height: 400px;
    width: 100%
}

._containerImage_1cicp_174 ._imgWrapper_1cicp_178,
._containerImage_1cicp_174 span {
    height: 100%;
    width: 100%
}

._containerImage_1cicp_174 img {
    display: flex;
    align-items: center;
    width: 100% !important;
    height: 100% !important;
    max-height: unset !important
}

@media (min-width: 768px) {
    ._containerImage_1cicp_174 {
        position: absolute !important;
        width: 50%;
        top: 0;
        bottom: 0;
        height: 100%
    }
}

._rowStyle_1cicp_200 {
    justify-content: flex-end;
    margin: 0 15px
}

._content_1cicp_205 {
    width: auto;
    margin: 4% auto;
    float: right
}

._content_1cicp_205 h2 {
    color: #000;
    line-height: 45px;
    font-weight: 600;
    font-size: 36px
}

._content_1cicp_205 p {
    color: #8a8d90;
    margin-top: 16px;
    border: none;
    padding: 0
}

._containerabout_1mxf6_1 {
    position: relative;
    padding: 100px 0
}

._contentbox_1mxf6_6 {
    background-color: #fff;
    text-align: center;
    padding: 35px 40px;
    border-radius: 4px;
    margin: 5%
}

._contentbox_1mxf6_6 ._sectionContent_1mxf6_13 p {
    width: auto;
    margin: 0 auto;
    color: #5e5f61
}

._btn_1mxf6_19 {
    background-color: var(--theme-color-brand1) !important;
    margin-top: 4%;
    color: #fff;
    padding: 8px 24px
}

._buttonsWrapper_1m4cg_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1m4cg_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1m4cg_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1m4cg_144 {
    margin-bottom: 30px
}

._containerabout_1m4cg_165 {
    padding: 80px 0
}

._contentCol_1m4cg_169 {
    display: flex;
    align-items: center
}

._content_1m4cg_169 {
    margin-left: 0%;
    margin-top: 6%
}

._content_1m4cg_169 ._servicetitle_1m4cg_178 {
    margin-bottom: 1em
}

._content_1m4cg_169 ._servicetitle_1m4cg_178 h2 {
    line-height: 40px;
    font-weight: 600;
    font-size: 36px
}

._content_1m4cg_169 p {
    line-height: 26px;
    color: #7d7979;
    font-weight: 400;
    width: auto
}

@media (min-width: 992px) {
    ._content_1m4cg_169 {
        margin-left: 17%
    }
}

._ImageStyle_1m4cg_198 {
    display: flex;
    align-items: center;
    justify-content: center
}

._containerImage_1m4cg_204 {
    position: relative;
    margin: 6% 0 0 30px;
    width: 90%
}

._containerImage_1m4cg_204 ._firstImg_1m4cg_209 {
    position: relative;
    width: 100%
}

._containerImage_1m4cg_204 ._firstImg_1m4cg_209 img {
    display: flex;
    align-items: center;
    width: 100%
}

._containerImage_1m4cg_204 ._block_1m4cg_218 {
    position: absolute;
    inset: 0;
    background-image: linear-gradient(240deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    transform: translate(-30px, -30px)
}

._container_1tju9_1 {
    padding: 70px 0;
    justify-content: center;
    text-align: center
}

._content_1tju9_7 {
    margin: 20px auto;
    max-width: 700px
}

._content_1tju9_7 h2 {
    color: #27272a;
    line-height: 45px;
    font-weight: 500;
    padding: 10px;
    font-size: 36px
}

._content_1tju9_7 p {
    color: #27272acc;
    padding: 10px
}

._btn_1tju9_23 {
    color: var(--theme-color-brand1);
    background-color: #fff;
    padding-left: 40px;
    padding-right: 40px;
    margin-top: .5rem
}

._section_1lquk_1 {
    padding: 100px 0;
    position: relative
}

._content_1lquk_6 {
    max-width: 700px;
    text-align: center;
    color: #27272a;
    position: relative;
    z-index: 9
}

._content_1lquk_6 h2 {
    font-size: 36px
}

._content_1lquk_6 p {
    margin: 40px auto 0;
    color: #27272ab3
}

._button_1lquk_21 {
    color: #0a0a0a;
    padding: 12px 46px;
    text-align: center;
    min-width: 180px;
    display: inline-block;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 16px #0003
}

._containerabout_12n6z_1 {
    padding: 80px 0 30px
}

._centerCol_12n6z_5 {
    display: flex;
    align-items: center
}

._content_12n6z_10 {
    width: 80%
}

._content_12n6z_10 h2 {
    color: #0a0a0a;
    display: inline-block;
    font-size: 36px
}

._content_12n6z_10 p {
    line-height: 26px;
    color: #000;
    opacity: .5;
    font-weight: 500;
    width: 80%;
    display: inline-flex;
    vertical-align: baseline;
    margin: 30px 10px 0
}

._content_12n6z_10 i {
    color: #f76b1c
}

._aboutbn_12n6z_32 {
    background-image: linear-gradient(130deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    color: #fff;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    border: none;
    padding: 12px 50px;
    margin-top: 30px
}

._aboutbn_12n6z_32 :active {
    background-image: linear-gradient(130deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._containerImage_12n6z_49 {
    position: relative;
    padding-top: 76.25%
}

._containerImage_12n6z_49 ._boxStyle_12n6z_53 {
    width: 90%;
    height: 77%;
    position: absolute;
    left: 10%;
    top: 8%;
    background-image: linear-gradient(90deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

.rtl ._containerImage_12n6z_49 ._boxStyle_12n6z_53 {
    right: 12%;
    left: 0%;
    top: 6%;
    background-image: linear-gradient(230deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._containerImage_12n6z_49 ._secondImg_12n6z_67 {
    width: 95%;
    position: absolute;
    left: 0;
    top: 0
}

.rtl ._containerImage_12n6z_49 ._secondImg_12n6z_67 {
    left: 1%
}

._containerImage_12n6z_49 ._secondImg_12n6z_67 img {
    display: flex;
    align-items: center;
    width: 100%;
    aspect-ratio: 8/5
}

._buttonsWrapper_n7qm6_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_n7qm6_138 {
    margin-bottom: 16px
}

._spaceAfter--small_n7qm6_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_n7qm6_144 {
    margin-bottom: 30px
}

._body_n7qm6_165 {
    padding: 80px 0
}

._body_n7qm6_165 ._serviceTitle_n7qm6_168 {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 50%
}

._body_n7qm6_165 ._serviceTitle_n7qm6_168 h2 {
    line-height: 42px;
    font-weight: 600;
    text-align: center;
    font-size: 36px
}

._body_n7qm6_165 ._serviceTitle_n7qm6_168 p {
    text-align: center;
    width: 90%;
    margin-top: 17px;
    margin-bottom: 50px;
    color: #7b7b7b;
    font-weight: 300
}

@media (min-width: 768px) {
    ._body_n7qm6_165 ._serviceTitle_n7qm6_168 p {
        width: 85%
    }
}

._body_n7qm6_165 ._serviceTitle_n7qm6_168 ._aboutbn_n7qm6_194 {
    border-radius: 3px;
    border: solid 1px #5c58cb;
    color: #5c58cb;
    padding: 8px 50px;
    margin-bottom: 72px
}

._body_n7qm6_165 ._imgMain_n7qm6_201 {
    box-shadow: 0 1px 16px #0003
}

._imageRow_n7qm6_205 {
    justify-content: center
}

._imageRow_n7qm6_205 img {
    display: flex;
    align-items: center;
    width: 100%;
    aspect-ratio: 2/1
}

@media (max-width: 812px) {
    ._body_n7qm6_165 ._serviceTitle_n7qm6_168 {
        width: 80%
    }
}

@media (max-width: 375px) {
    ._body_n7qm6_165 ._serviceTitle_n7qm6_168 {
        width: 100%
    }
}

._buttonsWrapper_1ogb3_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ogb3_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ogb3_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ogb3_144 {
    margin-bottom: 30px
}

._sectioncontent_1ogb3_165 {
    padding: 70px 20px
}

._cont_1ogb3_169 {
    max-width: 1140px;
    margin-right: auto;
    margin-left: auto
}

._cont_1ogb3_169 h2 {
    font-size: 36px
}

._contentsubtitle_1ogb3_178 {
    width: 80%
}

._divider_1ogb3_182 {
    width: 16px;
    height: 1px;
    background-color: #000
}

._contentwrap_1ogb3_188 {
    margin-top: 30px
}

._paragraph_1ogb3_192 {
    -moz-column-count: auto;
    column-count: auto;
    text-align: justify
}

._sectionContent_1wrwz_1 {
    padding: 70px 0
}

._sectionContent_1wrwz_1 ._contentHeader_1wrwz_4 {
    width: 58%;
    margin: 0 auto 70px
}

@media (max-width: 991px) {
    ._sectionContent_1wrwz_1 ._contentHeader_1wrwz_4 {
        width: 100%
    }
}

._sectionContent_1wrwz_1 ._contentHeader_1wrwz_4 h1 {
    font-size: 48px;
    line-height: 60px;
    text-align: center;
    letter-spacing: -1.2px;
    font-weight: 700
}

._sectionContent_1wrwz_1 ._roundedImg_1wrwz_20 {
    border-radius: 8px
}

._sectionContent_1wrwz_1 ._roundedImg_1wrwz_20 img {
    display: flex;
    align-items: center;
    width: 100%
}

._sectionContent_1wrwz_1 ._contentText_1wrwz_28 {
    border-bottom: 1px solid #dbdbdb
}

._buttonsWrapper_15383_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_15383_138 {
    margin-bottom: 16px
}

._spaceAfter--small_15383_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_15383_144 {
    margin-bottom: 30px
}

._sectionContent_15383_165 {
    padding: 50px 0 60px
}

._sectionContent_15383_165 ._contentText_15383_168 h2 {
    color: #1c1c1c;
    line-height: 1.5;
    text-align: center;
    letter-spacing: -1.2px
}

._buttonsWrapper_1qx73_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1qx73_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1qx73_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1qx73_144 {
    margin-bottom: 30px
}

._sectionContent_1qx73_165 {
    padding: 20px 0 60px
}

._sectionContent_1qx73_165 ._contentText_1qx73_168 p {
    font-size: 16px;
    line-height: 30px;
    letter-spacing: -.3px;
    color: #585757
}

._sectionContent_azffh_1 {
    padding: 50px 0
}

._sectionContent_azffh_1 ._contentHeader_azffh_4 {
    width: 58%;
    margin: 0 auto 30px
}

@media (max-width: 991px) {
    ._sectionContent_azffh_1 ._contentHeader_azffh_4 {
        width: 100%
    }
}

._sectionContent_azffh_1 ._contentHeader_azffh_4 h1 {
    font-size: 48px;
    line-height: 60px;
    text-align: center;
    letter-spacing: -1.2px;
    font-weight: 700
}

._sectionContent_azffh_1 ._contentText_azffh_20 {
    width: 58%;
    margin: 0 auto 70px
}

@media (max-width: 991px) {
    ._sectionContent_azffh_1 ._contentText_azffh_20 {
        width: 100%
    }
}

._sectionContent_azffh_1 ._contentText_azffh_20 p {
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: -.3px;
    color: #585757;
    text-align: center
}

._sectionContent_azffh_1 ._buttonsContainer_azffh_38 {
    display: flex;
    justify-content: center
}

._sectionContent_azffh_1 ._buttonsContainer_azffh_38 ._primaryButton_azffh_42 {
    background: #0d9488;
    color: #fff;
    width: 100px;
    height: 48px;
    padding: 10px;
    text-align: center;
    border-radius: 6px;
    font-weight: 700
}

._sectionContent_azffh_1 ._buttonsContainer_azffh_38 ._secondaryButton_azffh_52 {
    color: #0d9488;
    margin-left: 10px;
    width: 100px;
    height: 48px;
    padding: 10px;
    text-align: center;
    font-weight: 700
}

._buttonsWrapper_1f1kh_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1f1kh_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1f1kh_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1f1kh_144 {
    margin-bottom: 30px
}

._container_1f1kh_165 {
    max-width: 900px;
    margin: 0 auto;
    padding: 80px 20px
}

._intro_1f1kh_171 {
    text-align: center;
    margin-bottom: 50px
}

._sectionDescription_1f1kh_176 {
    font-size: 16px;
    color: #585757;
    width: auto;
    margin: 0 auto
}

._sectionTitle_1f1kh_183 {
    font-weight: 700;
    color: #1c1c1c
}

._contactInfo_1f1kh_188 {
    margin-bottom: 40px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around
}

._infoItem_1f1kh_195 {
    display: flex;
    flex: 0 0 100%;
    padding: 10px
}

._infoItem_1f1kh_195 ._label_1f1kh_200 {
    font-weight: 600;
    color: #1c1c1c;
    margin-bottom: 4px
}

._infoItem_1f1kh_195 p {
    font-size: 14px;
    color: #585757
}

._infoItem_1f1kh_195 ._icon_1f1kh_209 {
    flex: 0 0 auto
}

._infoItem_1f1kh_195 ._icon_1f1kh_209 i {
    color: var(--theme-color-brand1);
    background: #f6f6f6;
    padding: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36px
}

._infoItem_1f1kh_195 ._text_1f1kh_221 {
    padding: 0 15px;
    flex: 1 1 auto
}

@media (min-width: 768px) {
    ._infoItem_1f1kh_195 {
        flex: 1 1 30%;
        padding: 0
    }
}

._field_1f1kh_232 {
    padding: 14px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, .4)
}

._textareaField_1f1kh_238 {
    min-height: 150px
}

._actionContainer_1f1kh_242 {
    display: flex;
    justify-content: center
}

._buttonsWrapper_9q0km_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_9q0km_138 {
    margin-bottom: 16px
}

._spaceAfter--small_9q0km_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_9q0km_144 {
    margin-bottom: 30px
}

._outerContainer_9q0km_165 {
    position: relative;
    overflow: hidden
}

._outerContainer_9q0km_165 ._containerImage_9q0km_169 {
    height: 100%;
    width: 100%;
    position: relative
}

._outerContainer_9q0km_165 ._containerImage_9q0km_169 img {
    display: flex;
    align-items: center;
    width: 100%;
    max-height: unset !important;
    height: 550px !important
}

@media (max-width: 768px) {
    ._outerContainer_9q0km_165 ._containerImage_9q0km_169 {
        height: 450px;
        overflow: hidden
    }
}

._intro_9q0km_188 {
    padding: 50px 40px 40px
}

._intro_9q0km_188 p {
    font-size: 16px;
    color: #585757
}

._intro_9q0km_188 h2 {
    font-weight: 700;
    color: #1c1c1c;
    margin-bottom: 16px
}

._contactInfo_9q0km_201 {
    padding: 0 40px 70px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: space-around
}

._infoItem_9q0km_209 {
    display: flex;
    flex: 0 0 100%;
    margin-bottom: 15px
}

._infoItem_9q0km_209 ._label_9q0km_214 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #1c1c1c
}

._infoItem_9q0km_209 p {
    font-size: 14px;
    color: #585757
}

._socialTitle_9q0km_224 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #1c1c1c
}

._socialLinks_9q0km_231 {
    display: flex
}

._socialLinks_9q0km_231 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._socialLinks_9q0km_231 ul li a {
    text-decoration: none;
    margin: 0 14px 0 0;
    background-color: var(--theme-color-brand1);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px
}

.rtl ._socialLinks_9q0km_231 ul li a {
    margin: 0 0 0 14px
}

@media screen and (max-width: 425px) {
    ._outerContainer_9q0km_165 {
        margin-bottom: 46px
    }
    ._socialLinks_9q0km_231 ul li a {
        width: 40px;
        height: 40px
    }
}

@media screen and (max-width: 768px) {
    ._outerContainer_9q0km_165 {
        padding: 0
    }
    ._contactInfo_9q0km_201,
    .rtl ._contactInfo_9q0km_201 {
        margin-left: 12px;
        margin-right: 12px
    }
    ._intro_9q0km_188 {
        margin-left: 12px;
        margin-right: 12px;
        margin-top: 12px
    }
    .rtl ._intro_9q0km_188 {
        margin-left: 12px;
        margin-right: 12px
    }
}

._buttonsWrapper_1ow9k_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ow9k_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ow9k_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ow9k_144 {
    margin-bottom: 30px
}

._outerContainer_1ow9k_165 {
    position: relative;
    padding: 120px 0
}

._intro_1ow9k_170 {
    margin-top: 30px;
    color: #27272a;
    font-weight: 600
}

._sectionTitle_1ow9k_176 {
    font-weight: 700
}

._contactInfo_1ow9k_180 {
    margin: 40px 10px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column
}

._infoItem_1ow9k_187 {
    display: flex;
    flex: 0 0 100%;
    margin: 12px 0
}

._infoItem_1ow9k_187 ._label_1ow9k_192 {
    font-weight: 600;
    color: #27272a;
    margin-bottom: 8px
}

._infoItem_1ow9k_187 ._icon_1ow9k_197 {
    flex: 0 0 auto;
    display: flex;
    align-items: baseline
}

._infoItem_1ow9k_187 ._icon_1ow9k_197 i {
    color: #27272a;
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 30px
}

._infoItem_1ow9k_187 ._text_1ow9k_209 {
    padding: 0 24px;
    flex: 1 1 auto
}

._infoItem_1ow9k_187 ._text_1ow9k_209 ._label_1ow9k_192 {
    font-weight: 600
}

._infoItem_1ow9k_187 ._text_1ow9k_209 p {
    color: #27272a;
    max-width: 319px;
    opacity: .85;
    font-size: 14px
}

@media screen and (min-width: 700px) {
    ._infoItem_1ow9k_187 {
        flex: 1 1 30%;
        padding: 0
    }
}

._formStyle_1ow9k_229 {
    padding: 35px;
    margin-top: -16px
}

._formStyle_1ow9k_229 ._formContainer_1ow9k_233 {
    background: #fff;
    padding: 40px 30px 30px;
    border-radius: 4px
}

._field_1ow9k_239 {
    padding: 14px;
    opacity: .85;
    border-radius: 2px;
    background-color: #fff0;
    border: solid 1px #d8d8d8
}

._textareaField_1ow9k_247 {
    min-height: 150px
}

._actionContainer_1ow9k_251 {
    display: flex;
    justify-content: flex-start
}

._socialLinks_1ow9k_256 {
    display: flex
}

._socialLinks_1ow9k_256 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._socialLinks_1ow9k_256 ul li a {
    text-decoration: none;
    margin: 0 14px 0 0;
    width: 50px;
    height: 50px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-direction: column;
    color: var(--theme-color-brand1)
}

.rtl ._socialLinks_1ow9k_256 ul li a {
    margin: 0 0 0 14px
}

@media screen and (max-width: 786px) {
    ._formStyle_1ow9k_229 {
        padding: 0
    }
    ._intro_1ow9k_170 {
        margin-top: 0
    }
}

@media screen and (max-width: 425px) {
    ._outerContainer_1ow9k_165 {
        padding: 20px 0 0
    }
    ._socialLinks_1ow9k_256 ul li a {
        width: 40px;
        height: 40px
    }
}

._buttonsWrapper_h5u30_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_h5u30_138 {
    margin-bottom: 16px
}

._spaceAfter--small_h5u30_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_h5u30_144 {
    margin-bottom: 30px
}

._outerContainer_h5u30_165 {
    position: relative;
    padding: 120px 0
}

._columnStyle_h5u30_170 {
    display: flex;
    align-items: center;
    justify-content: center
}

._imgConatiner_h5u30_176 {
    box-shadow: 0 2px 14px 7px #0000003d;
    width: 100%
}

._imgConatiner_h5u30_176 img {
    aspect-ratio: 3/4;
    max-height: unset !important
}

._intro_h5u30_185 h2 {
    color: #27272a;
    font-weight: 700
}

._intro_h5u30_185 p {
    margin: 14px 0;
    opacity: .65;
    color: #27272a;
    font-size: 14px
}

._contactInfo_h5u30_196 {
    margin: 46px 0;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column
}

._formContainer_h5u30_203 {
    margin-top: 30px
}

._field_h5u30_207 {
    margin: 46px 0;
    padding: 14px;
    color: #27272a;
    border-radius: 0;
    background-color: transparent
}

:hover ._field_h5u30_207 {
    background-color: transparent
}

:focus ._field_h5u30_207 {
    background-color: transparent
}

._textareaField_h5u30_221 {
    min-height: 150px
}

._actionContainer_h5u30_225 {
    display: flex;
    justify-content: flex-start
}

._socialLinks_h5u30_230 {
    display: flex;
    margin: 16px 0
}

._socialLinks_h5u30_230 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._socialLinks_h5u30_230 ul li a {
    text-decoration: none;
    margin: 0 14px 0 0;
    width: 50px;
    height: 50px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-direction: column;
    color: var(--theme-color-brand1)
}

.rtl ._socialLinks_h5u30_230 ul li a {
    margin: 0 0 0 14px
}

@media screen and (max-width: 425px) {
    ._outerContainer_h5u30_165 {
        padding: 20px 0 0
    }
    ._socialLinks_h5u30_230 ul li a {
        width: 40px;
        height: 40px
    }
}

._outerContainer_1thrt_1 {
    position: relative
}

._outerContainer_1thrt_1 ._containerImage_1thrt_4 {
    padding: 157px 0;
    height: 400px;
    width: 100%;
    position: relative
}

@media screen and (min-width: 768px) {
    ._outerContainer_1thrt_1 ._containerImage_1thrt_4 {
        position: relative;
        width: 100%;
        height: auto;
        top: 0;
        bottom: 0
    }
}

._overlay_1thrt_20 {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    opacity: .7;
    border: solid 1px #979797;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._intro_1thrt_31 {
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 20px
}

._intro_1thrt_31 p {
    opacity: .85;
    color: #031d5b;
    max-width: 400px;
    color: #ffffff96
}

._intro_1thrt_31 h2 {
    display: inline-block;
    font-weight: 600;
    margin-bottom: 16px;
    color: #fff
}

._socialTitle_1thrt_50 {
    color: #fff;
    display: inline-block
}

._socialLinks_1thrt_55 {
    display: flex;
    margin-bottom: 10px
}

._socialLinks_1thrt_55 ul {
    display: flex;
    list-style: none;
    padding: 0
}

._socialLinks_1thrt_55 ul li a {
    text-decoration: none;
    margin: 0 10px 0 0;
    width: 50px;
    height: 50px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-direction: column;
    color: var(--theme-color-brand1)
}

.rtl ._socialLinks_1thrt_55 ul li a {
    margin: 0 0 0 10px
}

._contactInfo_1thrt_81 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    width: 460px;
    height: 424px;
    border-radius: 6px;
    padding: 25px;
    background-color: #fff;
    box-shadow: 0 2px 14px 7px #0003
}

._infoItem_1thrt_95 {
    display: flex;
    flex: 0 0 100%
}

._infoItem_1thrt_95 ._label_1thrt_99 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    display: inline-block
}

._infoItem_1thrt_95 ._text_1thrt_105 {
    display: flex;
    flex-direction: column;
    margin: 6px 16px
}

._infoItem_1thrt_95 p {
    opacity: .85;
    font-weight: 600;
    color: #666;
    max-width: 330px
}

._infoItem_1thrt_95 ._icon_1thrt_116 {
    flex: 0 0 auto
}

._infoItem_1thrt_95 ._icon_1thrt_116 i {
    color: var(--theme-color-brand1);
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 36px
}

@media screen and (max-width: 425px) {
    ._outerContainer_1thrt_1 {
        margin-bottom: 46px
    }
}

@media screen and (max-width: 768px) {
    ._outerContainer_1thrt_1 {
        padding: 0
    }
    ._contactInfo_1thrt_81,
    .rtl ._contactInfo_1thrt_81 {
        margin-left: 12px;
        margin-right: 12px
    }
    ._intro_1thrt_31 {
        margin-left: 12px;
        margin-right: 12px;
        margin-top: 12px
    }
    .rtl ._intro_1thrt_31 {
        margin-left: 12px;
        margin-right: 12px
    }
}

._buttonsWrapper_13tmh_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_13tmh_138 {
    margin-bottom: 16px
}

._spaceAfter--small_13tmh_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_13tmh_144 {
    margin-bottom: 30px
}

._outerContainer_13tmh_165 {
    position: relative;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1));
    padding: 75px 0
}

._intro_13tmh_171 {
    position: relative;
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 40px;
    z-index: 33
}

._intro_13tmh_171 p {
    opacity: .8;
    color: #031d5b;
    max-width: 400px;
    color: #ffffff96
}

._intro_13tmh_171 h2 {
    display: inline-block;
    font-weight: 600;
    margin-bottom: 16px;
    color: #fff;
    width: 30%
}

._contactInfo_13tmh_193 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    min-height: 424px;
    border-radius: 4px;
    padding: 25px;
    margin-top: 15px;
    background-color: #fff;
    box-shadow: 0 2px 14px 7px #0000003d
}

@media (min-width: 768px) {
    ._contactInfo_13tmh_193 {
        margin-top: 0
    }
}

._infoItem_13tmh_212 {
    display: flex;
    flex: 0 0 100%
}

._infoItem_13tmh_212 ._label_13tmh_216 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    display: inline-block
}

._infoItem_13tmh_212 ._text_13tmh_222 {
    display: flex;
    flex-direction: column;
    margin: 6px 16px
}

._infoItem_13tmh_212 p {
    opacity: .85;
    font-weight: 600;
    color: #666;
    max-width: 330px
}

._infoItem_13tmh_212 ._icon_13tmh_233 {
    flex: 0 0 auto
}

._infoItem_13tmh_212 ._icon_13tmh_233 i {
    color: var(--theme-color-brand1);
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 36px
}

._formContainer_13tmh_244 {
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 14px 7px #0000003d;
    width: 100%;
    padding: 22px 47px 54px
}

._field_13tmh_252 {
    margin: 38px 0;
    padding: 14px;
    border: none;
    border-radius: 0;
    border-bottom: solid 1px #5600e7;
    color: #4a4a4a;
    background-color: transparent;
    opacity: .78
}

:hover ._field_13tmh_252 {
    background-color: transparent;
    border-bottom: solid 1px #5600e7
}

:focus ._field_13tmh_252 {
    background-color: transparent;
    border-bottom: solid 1px #5600e7
}

._textareaField_13tmh_271 {
    min-height: 60px
}

._actionContainer_13tmh_275 {
    display: flex;
    justify-content: flex-start
}

._outerContainer_54mu4_1 {
    position: relative;
    padding: 123px 0 146px
}

._intro_54mu4_6 {
    position: relative;
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 40px
}

._intro_54mu4_6 p {
    opacity: .6;
    color: #999;
    max-width: 500px
}

._intro_54mu4_6 h2 {
    display: inline-block;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333
}

._boxContentstyle_54mu4_25 {
    border-radius: 4px;
    border: solid 1px #979797;
    background: var(--theme-color-brand1)
}

._contactInfo_54mu4_31 {
    position: relative;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: 30px
}

._infoItem_54mu4_41 {
    display: flex;
    flex: 0 0 100%;
    margin-bottom: 30px
}

._infoItem_54mu4_41 ._label_54mu4_46 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #fff;
    display: inline-block
}

._infoItem_54mu4_41 ._text_54mu4_52 {
    display: flex;
    flex-direction: column;
    margin: 6px 16px
}

._infoItem_54mu4_41 p {
    opacity: .85;
    font-weight: 600;
    color: #fff;
    max-width: 330px
}

._infoItem_54mu4_41 ._icon_54mu4_63 {
    flex: 0 0 auto
}

._infoItem_54mu4_41 ._icon_54mu4_63 i {
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 36px
}

._formContainer_54mu4_74 {
    max-width: 430px
}

._text_54mu4_52 {
    padding: 0 15px;
    flex: 1 1 auto
}

@media screen and (min-width: 700px) {
    ._text_54mu4_52 {
        flex: 1 1 30%;
        padding: 0
    }
}

._field_54mu4_89 {
    padding: 14px;
    opacity: .85;
    border-radius: 2px;
    background-color: #fff0;
    border: solid 1px #d8d8d8;
    color: #b8c6d3
}

._textareaField_54mu4_98 {
    min-height: 200px
}

._actionContainer_54mu4_102 {
    display: flex;
    margin-top: 48px;
    justify-content: center
}

._socialTitle_54mu4_108 {
    flex: 0 0 100%;
    opacity: .75;
    color: #000;
    display: inline-block
}

._socialLinks_54mu4_115 {
    margin-top: 54px
}

._socialLinks_54mu4_115 ul {
    display: flex;
    list-style: none;
    padding: 0
}

._socialLinks_54mu4_115 ul li {
    margin: 0 8px 0 0
}

._socialLinks_54mu4_115 ul li a {
    text-decoration: none;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid var(--theme-color-secondary1);
    flex-direction: column;
    color: var(--theme-color-brand1)
}

.rtl ._socialLinks_54mu4_115 ul li {
    margin: 0 0 0 8px
}

._outerContainer_a37kk_1 {
    position: relative;
    padding: 100px 0;
    background-image: linear-gradient(121deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._intro_a37kk_7 {
    max-width: 534px;
    margin-bottom: 48px
}

._intro_a37kk_7 h2 {
    color: #fff;
    font-weight: 600;
    margin: 10px
}

._intro_a37kk_7 p {
    margin: 10px;
    opacity: .65;
    color: #fff
}

._sectionDescription_a37kk_22 {
    color: #3c3c3b;
    max-width: 400px;
    margin: 0 auto
}

._sectionTitle_a37kk_28 {
    display: inline-block;
    font-weight: 600
}

._contactInfo_a37kk_33 {
    margin: 46px 0;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    width: 100%;
    justify-content: space-around
}

._infoItem_a37kk_42 {
    display: flex;
    width: 358.8px;
    min-height: 200px;
    background-color: #fff;
    box-shadow: 0 2px 11px 5px #0000003d;
    padding: 0 20px 20px;
    position: relative;
    flex-direction: column;
    justify-content: center;
    align-items: center
}

._infoItem_a37kk_42 ._label_a37kk_55 {
    font-weight: 600;
    color: var(--theme-color-brand1);
    margin-bottom: 8px;
    display: inline-block
}

._infoItem_a37kk_42 ._sepretor_a37kk_61 {
    width: 40px;
    height: 1px;
    opacity: .75;
    margin-top: 18px;
    margin-bottom: 40px;
    border: solid 1px var(--theme-color-brand1)
}

._infoItem_a37kk_42 ._icon_a37kk_69 {
    width: 114px;
    height: 100px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-top: -40px
}

._infoItem_a37kk_42 ._icon_a37kk_69 i {
    color: var(--theme-color-brand1);
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 40px
}

._infoItem_a37kk_42 ._text_a37kk_86 {
    padding: 0 24px;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: center
}

._infoItem_a37kk_42 ._text_a37kk_86 p {
    color: #28334f;
    max-width: 319px;
    text-align: center
}

@media screen and (min-width: 700px) {
    ._infoItem_a37kk_42 {
        display: flex
    }
}

._formStyle_a37kk_104 {
    padding: 35px;
    margin-top: 30px
}

._formStyle_a37kk_104 ._formContainer_a37kk_108 {
    background: #fff;
    padding: 74px 30px 60px
}

._field_a37kk_113 {
    padding: 14px;
    opacity: .85;
    border-radius: 2px;
    background-color: #fff0;
    border: solid 1px #d8d8d8
}

._textareaField_a37kk_125 {
    min-height: 150px
}

._actionContainer_a37kk_130 {
    display: flex;
    justify-content: flex-start
}

._socialTitle_a37kk_135 {
    color: #fff;
    display: inline-block
}

._socialLinks_a37kk_140 {
    display: flex
}

._socialLinks_a37kk_140 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._socialLinks_a37kk_140 ul li {
    padding-right: 8px
}

.rtl ._socialLinks_a37kk_140 ul li {
    padding-right: 0;
    padding-left: 8px
}

._socialLinks_a37kk_140 ul li a {
    text-decoration: none;
    display: flex;
    width: 50px;
    height: 50px;
    color: var(--theme-color-brand1);
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: #fffdf1;
    border-radius: 50%
}

._section_1vg2a_1 {
    position: relative
}

._bkColor_1vg2a_5 {
    background-image: linear-gradient(109deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    height: 504px;
    width: 100%;
    padding: 100px 0 146px
}

._intro_1vg2a_12 {
    position: relative;
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 50px;
    z-index: 33
}

._intro_1vg2a_12 p {
    opacity: .6;
    color: #fff;
    max-width: 500px
}

._intro_1vg2a_12 h2 {
    display: inline-block;
    font-weight: 600;
    margin-bottom: 16px;
    color: #fff
}

._boxRow_1vg2a_32 {
    position: relative;
    background-color: #fff;
    margin-top: -16%;
    box-shadow: 0 2px 24px #00000017;
    margin-bottom: 40px;
    border-radius: 6px
}

._contactInfo_1vg2a_41 {
    padding: 20px;
    position: relative;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    border-radius: 6px
}

._sepretor_1vg2a_52 {
    width: 1px;
    height: 100%;
    opacity: .2;
    border: solid 1px #979797
}

._infoItem_1vg2a_59 {
    display: flex;
    flex: 0 0 100%;
    margin-bottom: 30px
}

._infoItem_1vg2a_59 ._label_1vg2a_64 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #000;
    display: inline-block
}

._infoItem_1vg2a_59 ._text_1vg2a_70 {
    display: flex;
    flex-direction: column;
    margin: 6px 16px
}

._infoItem_1vg2a_59 p {
    opacity: .85;
    font-weight: 600;
    color: #000;
    max-width: 330px
}

._infoItem_1vg2a_59 ._icon_1vg2a_81 {
    flex: 0 0 auto
}

._infoItem_1vg2a_59 ._icon_1vg2a_81 i {
    color: #000;
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 36px
}

._socialTitle_1vg2a_92 {
    display: inline-block;
    color: #fff
}

._socialLinks_1vg2a_97 {
    display: flex
}

._socialLinks_1vg2a_97 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._socialLinks_1vg2a_97 ul li a {
    text-decoration: none;
    margin: 0 8px 0 0;
    background-color: #ffffffc2;
    color: #28334f;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px
}

.rtl ._socialLinks_1vg2a_97 ul li a {
    margin: 0 0 0 8px
}

._formContainer_1vg2a_122 {
    padding: 30px;
    position: relative
}

._text_1vg2a_70 {
    padding: 0 15px;
    flex: 1 1 auto
}

@media screen and (min-width: 700px) {
    ._text_1vg2a_70 {
        flex: 1 1 30%;
        padding: 0
    }
}

._field_1vg2a_138 {
    margin-top: 14px;
    padding: 14px;
    opacity: .5;
    border-radius: 2px;
    background-color: #fff0;
    border: solid 1px #d8d8d8;
    color: #b8c6d3
}

._textareaField_1vg2a_148 {
    min-height: 200px
}

._actionContainer_1vg2a_152 {
    display: flex;
    margin-top: 48px;
    justify-content: center
}

._buttonsWrapper_7zd69_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_7zd69_138 {
    margin-bottom: 16px
}

._spaceAfter--small_7zd69_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_7zd69_144 {
    margin-bottom: 30px
}

._contactUS_7zd69_165 {
    position: relative
}

._container_7zd69_169 {
    max-width: 900px;
    margin: 0 auto;
    padding: 80px 20px;
    z-index: 99
}

._intro_7zd69_176 {
    text-align: center;
    position: relative;
    margin-bottom: 40px
}

._sectionDescription_7zd69_182 {
    color: #27272a99;
    width: auto;
    margin: 10px auto 0;
    font-size: 16px
}

._sectionTitle_7zd69_190 {
    font-weight: 700;
    color: #27272a
}

._contactInfo_7zd69_195 {
    margin: 0 0 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #27272a;
    flex-wrap: wrap
}

._alignCenter_7zd69_204 {
    justify-content: center
}

._infoItem_7zd69_208 {
    display: flex;
    flex: 0 0 100%;
    padding: 0;
    margin-bottom: 15px;
    align-items: center
}

._infoItem_7zd69_208 ._label_7zd69_215 {
    font-weight: 600;
    margin-bottom: 4px
}

._infoItem_7zd69_208 p {
    margin: 0;
    font-size: 14px
}

._infoItem_7zd69_208 ._icon_7zd69_223 {
    position: relative;
    flex: 0 0 auto;
    margin: 0 15px
}

._infoItem_7zd69_208 ._icon_7zd69_223 i {
    color: #27272a;
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 36px
}

@media (min-width: 768px) {
    ._infoItem_7zd69_208 {
        flex: 1 1 30%;
        padding: 0 10px;
        margin-bottom: 0
    }
}

._field_7zd69_243 {
    padding: 26px;
    border-radius: 0;
    border: none;
    box-shadow: 0 2px 4px #0003;
    background-color: #fffc;
    color: #000
}

._textareaField_7zd69_252 {
    min-height: 150px;
    margin-bottom: 50px
}

._actionContainer_7zd69_257 {
    display: flex;
    justify-content: center
}

._overlay_7zd69_262 {
    opacity: .8;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._buttonsWrapper_1htj6_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1htj6_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1htj6_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1htj6_144 {
    margin-bottom: 30px
}

._outerContainer_1htj6_165 {
    display: flex;
    position: relative;
    flex-direction: column;
    padding: 0
}

@media (min-width: 768px) {
    ._outerContainer_1htj6_165 {
        flex-direction: row
    }
}

._mapContainer_1htj6_177 {
    position: relative;
    width: 100%;
    height: 400px
}

@media (min-width: 768px) {
    ._mapContainer_1htj6_177 {
        height: auto;
        flex: 0 0 50%
    }
}

._map_1htj6_177 {
    position: absolute !important;
    width: 100vw;
    height: 100%
}

@media (min-width: 768px) {
    ._map_1htj6_177 {
        width: 50vw
    }
}

._contentContainer_1htj6_200 {
    padding: 60px 60px 80px
}

@media (min-width: 768px) {
    ._contentContainer_1htj6_200 {
        flex: 0 0 50%
    }
}

._intro_1htj6_209 p {
    font-size: 16px;
    color: #585757
}

._intro_1htj6_209 h2 {
    font-weight: 700;
    color: #1c1c1c;
    margin-bottom: 16px
}

@media (min-width: 768px) {
    .rtl ._intro_1htj6_209 {
        margin-left: 12px;
        margin-right: 12px
    }
}

._contactInfo_1htj6_225 {
    margin-top: 60px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: space-around
}

@media (min-width: 768px) {
    .rtl ._contactInfo_1htj6_225 {
        margin-left: 12px;
        margin-right: 12px
    }
}

._infoItem_1htj6_239 {
    display: flex;
    flex: 0 0 100%;
    margin-bottom: 28px
}

._infoItem_1htj6_239 ._label_1htj6_244 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #1c1c1c
}

._infoItem_1htj6_239 p {
    font-size: 14px;
    color: #585757
}

._socialTitle_1htj6_254 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #1c1c1c
}

._socialLinksContainer_1htj6_261 {
    display: flex
}

._socialLinks_1htj6_261 {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._socialLinkItem_1htj6_272 a {
    text-decoration: none;
    margin: 0 7px;
    background-color: var(--theme-color-brand1);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px
}

._buttonsWrapper_gootw_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_gootw_138 {
    margin-bottom: 16px
}

._spaceAfter--small_gootw_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_gootw_144 {
    margin-bottom: 30px
}

._contactx00_gootw_165 {
    padding-bottom: 70px
}

._contactx00_gootw_165 ._contactx01_gootw_168 {
    padding-right: 36px;
    padding-left: 36px;
    align-items: center;
    flex-direction: column;
    flex: 1
}

._contactx00_gootw_165 ._contactx02_gootw_175 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-row-gap: 16px;
    grid-column-gap: 16px;
    display: grid;
    width: 100%;
    max-width: 1200px;
    min-height: 460px;
    margin-right: auto;
    margin-left: auto;
    grid-auto-flow: row;
    grid-auto-columns: 1fr;
    grid-column-gap: 36px;
    grid-row-gap: 36px;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
    grid-template-rows: auto
}

._contactx00_gootw_165 ._contactxmapbox_gootw_195 {
    grid-row-start: 2;
    grid-row-end: 3;
    grid-column-start: 1;
    grid-column-end: 13;
    margin-top: -103px;
    flex-direction: column;
    grid-auto-columns: 1fr;
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    min-height: 400px
}

._contactx00_gootw_165 ._contactxmapbox_gootw_195 ._contactxmap_gootw_195 {
    position: relative;
    line-height: 20px;
    width: 100%;
    height: 400px
}

._contactx00_gootw_165 ._contactx03_gootw_215 {
    grid-row-start: 1;
    grid-row-end: 2;
    grid-column-start: 2;
    grid-column-end: 12;
    position: relative;
    display: grid;
    border-style: solid;
    border-color: #ebedee;
    background-color: #fff;
    border-width: 1px;
    padding: 67px;
    grid-auto-columns: 1fr;
    grid-template-rows: auto;
    grid-column-gap: 50px;
    grid-row-gap: 30px;
    grid-template-columns: 1fr 1fr
}

._contactx00_gootw_165 ._contactx03_gootw_215 ._contactxinfo_gootw_233 ._contactxtitle_gootw_233 {
    margin-bottom: 40px
}

._contactx00_gootw_165 ._contactx03_gootw_215 ._contactxinfo_gootw_233 ._contactxtitle_gootw_233 h2 {
    margin-top: 10px;
    margin-bottom: 15px;
    font-weight: 700;
    color: #1c1c1c
}

._contactx00_gootw_165 ._contactx03_gootw_215 ._contactxinfo_gootw_233 ._contactxtitle_gootw_233 p {
    margin-bottom: 10px;
    font-size: 16px;
    color: #585757
}

._contactx00_gootw_165 ._contactx03_gootw_215 ._contactxitem_gootw_247 {
    margin-bottom: 25px
}

._contactx00_gootw_165 ._contactx03_gootw_215 ._contactxitem_gootw_247 h4 {
    margin-bottom: 10px;
    line-height: 20px;
    font-weight: 600;
    color: #1c1c1c
}

._contactx00_gootw_165 ._contactx03_gootw_215 ._contactxitem_gootw_247 p {
    margin-bottom: 10px;
    font-size: 14px;
    color: #585757;
    line-height: 20px
}

._contactx00_gootw_165 ._contactx04_gootw_262 {
    margin-bottom: 0;
    align-self: center
}

._contactx00_gootw_165 ._contactx04_gootw_262 ._textareaField_gootw_266 {
    width: 100%;
    color: #18181d;
    border-style: none;
    font-size: 14px;
    background-color: #f5f5f5;
    border-radius: 0;
    line-height: 24px;
    font-weight: 400;
    text-transform: none
}

._contactx00_gootw_165 ._contactx04_gootw_262 ._textField_gootw_278 {
    width: 100%;
    margin-bottom: 0;
    padding: 30px 20px;
    align-self: stretch;
    border-style: none;
    color: #18181d;
    background-color: #f5f5f5;
    border-radius: 0;
    font-size: 14px;
    line-height: 24px;
    font-weight: 400;
    text-transform: none
}

._contactx00_gootw_165 ._contactx04_gootw_262 ._textField_gootw_278:hover {
    background-color: #f0f0f0
}

._contactx00_gootw_165 ._contactx04_gootw_262 ._textField_gootw_278:active {
    background-color: #ececec
}

._contactx00_gootw_165 ._contactx04_gootw_262 ._textField_gootw_278:focus {
    background-color: #ececec;
    color: #18181d
}

@media screen and (max-width: 991px) {
    ._contactx00_gootw_165 ._contactx01_gootw_168 {
        padding-right: 24px;
        padding-left: 24px
    }
    ._contactx00_gootw_165 ._contactx02_gootw_175 {
        width: 100%;
        max-width: 738px;
        grid-column-gap: 24px;
        grid-row-gap: 30px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr
    }
    ._contactx00_gootw_165 ._contactxmapbox_gootw_195 {
        grid-row-start: span 1;
        grid-row-end: span 1;
        grid-column-start: span 8;
        grid-column-end: span 8;
        display: flex;
        min-height: 360px;
        margin-top: 0;
        flex-direction: row
    }
    ._contactx00_gootw_165 ._contactxmapbox_gootw_195 ._contactxmap_gootw_195 {
        height: auto
    }
    ._contactx00_gootw_165 ._contactx03_gootw_215 {
        grid-column-start: 1;
        grid-column-end: 9
    }
    ._contactx00_gootw_165 ._contactx03_gootw_215 ._contactxinfo_gootw_233,
    ._contactx00_gootw_165 ._contactx04_gootw_262 {
        grid-row-start: span 1;
        grid-row-end: span 1;
        grid-column-start: span 2;
        grid-column-end: span 2
    }
}

@media screen and (max-width: 767px) {
    ._contactx00_gootw_165 ._contactx01_gootw_168 {
        padding-right: 20px;
        padding-left: 20px
    }
    ._contactx00_gootw_165 ._contactx02_gootw_175 {
        max-width: none;
        grid-column-gap: 20px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr
    }
    ._contactx00_gootw_165 ._contactxmapbox_gootw_195 {
        grid-column-start: span 6;
        grid-column-end: span 6
    }
    ._contactx00_gootw_165 ._contactx03_gootw_215 {
        grid-column-end: 7;
        padding: 36px
    }
}

@media screen and (max-width: 479px) {
    ._contactx00_gootw_165 ._contactx01_gootw_168 {
        padding-right: 16px;
        padding-left: 16px
    }
    ._contactx00_gootw_165 ._contactx02_gootw_175 {
        grid-column-gap: 12px;
        grid-template-columns: 1fr 1fr 1fr 1fr
    }
    ._contactx00_gootw_165 ._contactxmapbox_gootw_195 {
        grid-column-start: span 4;
        grid-column-end: span 4
    }
    ._contactx00_gootw_165 ._contactx03_gootw_215 {
        grid-column-end: 5;
        padding: 0;
        border-style: none;
        border-width: 0px
    }
}

._fileUpload_gootw_385>div {
    background-color: #f5f5f5 !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important
}

._dateClass_gootw_392 {
    background-color: #f5f5f5 !important;
    border: none !important;
    border-radius: 0 !important
}

._dateClass_gootw_392 input {
    background-color: #f5f5f5 !important;
    box-shadow: none !important
}

._timeClass_gootw_402>div {
    background-color: #f5f5f5 !important;
    border: none;
    border-radius: 0
}

._timeClass_gootw_402 input,
._timeClass_gootw_402 select {
    box-shadow: none !important
}

._selectBoxClass_gootw_412 div:nth-child(3) {
    background-color: #f5f5f5 !important;
    border: none !important;
    border-radius: 0 !important
}

._buttonsWrapper_1pl3r_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1pl3r_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1pl3r_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1pl3r_144 {
    margin-bottom: 30px
}

._outerContainer_1pl3r_165 {
    padding: 50px 0 70px
}

._outerContainer_1pl3r_165 ._sectionDescription_1pl3r_168 {
    width: 90%
}

@media (max-width: 992px) {
    ._outerContainer_1pl3r_165 ._sectionDescription_1pl3r_168 {
        width: 100%
    }
}

._outerContainer_1pl3r_165 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._outerContainer_1pl3r_165 ._contactInfo_1pl3r_180 ._infoItem_1pl3r_180 {
    display: flex;
    margin-bottom: 20px
}

._outerContainer_1pl3r_165 ._contactInfo_1pl3r_180 ._infoItem_1pl3r_180 ._icon_1pl3r_184 i {
    font-weight: 600;
    font-size: 20px;
    color: var(--theme-color-brand1)
}

._outerContainer_1pl3r_165 ._contactInfo_1pl3r_180 ._infoItem_1pl3r_180 ._text_1pl3r_189 {
    margin-left: 10px;
    max-width: 85%;
    flex-grow: 1
}

._outerContainer_1pl3r_165 ._contactInfo_1pl3r_180 ._infoItem_1pl3r_180 ._text_1pl3r_189 p {
    font-size: 14px;
    color: #585757
}

._outerContainer_1pl3r_165 ._socialLinks_1pl3r_198 {
    width: 90%;
    margin-left: 20px
}

@media (max-width: 992px) {
    ._outerContainer_1pl3r_165 ._socialLinks_1pl3r_198 {
        margin-left: 0
    }
}

._outerContainer_1pl3r_165 ._socialLinks_1pl3r_198 ._social_1pl3r_198 {
    display: flex;
    list-style: none;
    padding: 0;
    margin-left: -3px
}

._outerContainer_1pl3r_165 ._socialLinks_1pl3r_198 ._socialItem_1pl3r_213 a {
    color: var(--theme-color-brand1);
    margin-right: 5px
}

._outerContainer_1pl3r_165 ._socialLinks_1pl3r_198 ._socialItem_1pl3r_213 a i {
    font-size: 30px !important
}

._outerContainer_1pl3r_165 ._socialLinks_1pl3r_198 ._socialItem_1pl3r_213 a svg {
    height: 35px !important;
    margin-top: -3px
}

._buttonsWrapper_dnmi1_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_dnmi1_138 {
    margin-bottom: 16px
}

._spaceAfter--small_dnmi1_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_dnmi1_144 {
    margin-bottom: 30px
}

._sectionContainer_dnmi1_165 {
    padding: 0 0 70px
}

._sectionContainer_dnmi1_165 ._mapContainer_dnmi1_168 {
    position: relative;
    width: 100%;
    height: 500px;
    overflow-y: hidden
}

@media (max-width: 992px) {
    ._sectionContainer_dnmi1_165 ._mapContainer_dnmi1_168 {
        height: 950px
    }
}

._sectionContainer_dnmi1_165 ._mapContainer_dnmi1_168 ._map_dnmi1_168 {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%
}

._sectionContainer_dnmi1_165 ._mapContainer_dnmi1_168 ._contactInfo_dnmi1_187 {
    width: 25%;
    height: -moz-fit-content;
    height: fit-content;
    right: 65%;
    top: 10%;
    position: absolute;
    z-index: 2;
    background-color: #fff;
    padding: 20px;
    box-shadow: 0 0 1px #1a202452, 0 4px 8px #5b68713d;
    border-radius: 4px
}

@media (max-width: 1200px) {
    ._sectionContainer_dnmi1_165 ._mapContainer_dnmi1_168 ._contactInfo_dnmi1_187 {
        width: 30%
    }
}

@media (max-width: 992px) {
    ._sectionContainer_dnmi1_165 ._mapContainer_dnmi1_168 ._contactInfo_dnmi1_187 {
        width: 90%;
        right: 5%;
        top: 3%
    }
}

._sectionContainer_dnmi1_165 ._mapContainer_dnmi1_168 ._contactInfo_dnmi1_187 ._info_dnmi1_211 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._sectionContainer_dnmi1_165 ._mapContainer_dnmi1_168 ._contactInfo_dnmi1_187 ._info_dnmi1_211 p {
    font-size: 14px;
    color: #585757
}

._sectionContainer_dnmi1_165 ._contactFields_dnmi1_219 {
    width: 70%;
    margin: 20px auto
}

@media (max-width: 992px) {
    ._sectionContainer_dnmi1_165 ._contactFields_dnmi1_219 {
        width: 100%
    }
}

._sectionContainer_dnmi1_165 ._contactFields_dnmi1_219 ._fieldsHeader_dnmi1_228 {
    text-align: center
}

._sectionContainer_dnmi1_165 ._contactFields_dnmi1_219 ._fieldsHeader_dnmi1_228 h2 {
    font-weight: 700;
    color: #1c1c1c
}

._sectionContainer_dnmi1_165 ._contactFields_dnmi1_219 ._fieldsHeader_dnmi1_228 p {
    font-size: 16px;
    color: #585757;
    margin-bottom: 30px
}

._sectionContainer_dnmi1_165 ._contactFields_dnmi1_219 ._textField_dnmi1_240,
._sectionContainer_dnmi1_165 ._contactFields_dnmi1_219 ._textareaField_dnmi1_241 {
    color: #585757
}

._sectionContainer_dnmi1_165 ._contactFields_dnmi1_219 ._textareaField_dnmi1_241 {
    height: 150px
}

._actionContainer_dnmi1_248 {
    display: flex;
    justify-content: center
}

._buttonsWrapper_9nbx8_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_9nbx8_138 {
    margin-bottom: 16px
}

._spaceAfter--small_9nbx8_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_9nbx8_144 {
    margin-bottom: 30px
}

._sectionContainer_9nbx8_165 {
    padding: 50px 0 70px
}

._sectionContainer_9nbx8_165 ._contactFields_9nbx8_168 {
    text-align: left;
    width: 85%
}

._sectionContainer_9nbx8_165 ._contactFields_9nbx8_168 ._fieldsHeader_9nbx8_172 h2 {
    font-weight: 700;
    color: #1c1c1c
}

._sectionContainer_9nbx8_165 ._contactFields_9nbx8_168 ._fieldsHeader_9nbx8_172 p {
    font-size: 16px;
    color: #585757;
    margin-bottom: 30px
}

._sectionContainer_9nbx8_165 ._contactFields_9nbx8_168 ._textField_9nbx8_181,
._sectionContainer_9nbx8_165 ._contactFields_9nbx8_168 ._textareaField_9nbx8_182 {
    color: #585757
}

._sectionContainer_9nbx8_165 ._contactFields_9nbx8_168 ._textareaField_9nbx8_182 {
    height: 80px
}

._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 {
    margin-top: 120px
}

@media (max-width: 992px) {
    ._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 {
        margin-top: 20px
    }
}

._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 ._info_9nbx8_196 {
    margin-bottom: 30px
}

._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 ._info_9nbx8_196 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 ._info_9nbx8_196 p {
    font-size: 14px;
    color: #585757
}

._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 ._social_9nbx8_207 {
    display: flex;
    list-style: none;
    padding: 0;
    margin-left: -3px
}

._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 ._socialItem_9nbx8_213 a {
    color: var(--theme-color-brand1);
    margin-right: 5px
}

._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 ._socialItem_9nbx8_213 a i {
    font-size: 30px !important
}

._sectionContainer_9nbx8_165 ._contactInfo_9nbx8_188 ._socialItem_9nbx8_213 a svg {
    height: 35px !important;
    margin-top: -3px
}

._actionContainer_9nbx8_225 {
    display: flex;
    justify-content: flex-start
}

._buttonsWrapper_g0bal_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_g0bal_138 {
    margin-bottom: 16px
}

._spaceAfter--small_g0bal_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_g0bal_144 {
    margin-bottom: 30px
}

._sectionContainer_g0bal_165 {
    padding: 0 0 70px
}

._sectionContainer_g0bal_165 ._map_g0bal_168 {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%
}

._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 {
    position: relative;
    width: 100%;
    min-height: 800px;
    overflow-y: hidden;
    display: flex;
    justify-content: end
}

@media (max-width: 992px) {
    ._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 {
        min-height: 1300px
    }
}

._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 iframe {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%
}

._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 {
    width: 25%;
    height: -moz-fit-content;
    height: fit-content;
    margin: 40px 100px;
    z-index: 2;
    background-color: #fff;
    padding: 20px;
    box-shadow: 0 0 1px #1a202452, 0 4px 8px #5b68713d;
    border-radius: 4px
}

@media (max-width: 1200px) {
    ._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 {
        width: 30%
    }
}

@media (max-width: 992px) {
    ._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 {
        width: 90%;
        margin: 20px auto
    }
}

._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 ._fieldsHeader_g0bal_218 {
    text-align: left
}

._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 ._fieldsHeader_g0bal_218 h2 {
    font-weight: 700;
    color: #1c1c1c
}

._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 ._fieldsHeader_g0bal_218 p {
    font-size: 16px;
    color: #585757;
    margin-bottom: 20px
}

._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 ._textField_g0bal_230,
._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 ._textareaField_g0bal_231 {
    color: #585757
}

._sectionContainer_g0bal_165 ._mapContainer_g0bal_176 ._contactFields_g0bal_197 ._textareaField_g0bal_231 {
    height: 100px
}

._actionContainer_g0bal_238 {
    display: flex;
    justify-content: flex-start
}

._buttonsWrapper_1gv4v_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1gv4v_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1gv4v_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1gv4v_144 {
    margin-bottom: 30px
}

._sectionContainer_1gv4v_165 {
    width: 100%;
    max-width: 80rem;
    margin-right: auto;
    margin-left: auto;
    padding-top: 5%;
    padding-bottom: 5%
}

@media (max-width: 1024px) {
    ._sectionContainer_1gv4v_165 {
        padding-left: 5%;
        padding-right: 5%
    }
}

._sectionContainer_1gv4v_165 ._contactFields_1gv4v_179 {
    text-align: left;
    margin-bottom: 1.5rem;
    width: 100%
}

._sectionContainer_1gv4v_165 ._contactFields_1gv4v_179 ._fieldsHeader_1gv4v_184 h2 {
    margin-top: 0rem;
    margin-bottom: 0rem;
    font-size: 3rem;
    line-height: 1.2;
    font-weight: 700;
    color: #000
}

@media (max-width: 992px) {
    ._sectionContainer_1gv4v_165 ._contactFields_1gv4v_179 ._fieldsHeader_1gv4v_184 h2 {
        font-size: 24px
    }
}

._sectionContainer_1gv4v_165 ._contactFields_1gv4v_179 ._fieldsHeader_1gv4v_184 p {
    font-size: 1.125rem;
    color: #000
}

._sectionContainer_1gv4v_165 ._contactInfo_1gv4v_201 {
    margin: 0 10px;
    margin-bottom: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column
}

._sectionContainer_1gv4v_165 ._contactInfo_1gv4v_201 ._infoItem_1gv4v_209 {
    display: flex;
    flex: 0 0 100%;
    margin: 0 0 12px
}

._sectionContainer_1gv4v_165 ._contactInfo_1gv4v_201 ._infoItem_1gv4v_209 h4 {
    margin-top: 0rem;
    margin-bottom: 0rem;
    font-size: 1.25rem;
    line-height: 1.4;
    font-weight: 700;
    padding-top: 0;
    margin-bottom: .5rem
}

._sectionContainer_1gv4v_165 ._contactInfo_1gv4v_201 ._infoItem_1gv4v_209 ._icon_1gv4v_224 {
    flex: 0 0 auto;
    display: flex;
    align-items: baseline
}

._sectionContainer_1gv4v_165 ._contactInfo_1gv4v_201 ._infoItem_1gv4v_209 ._icon_1gv4v_224 i {
    color: var(--theme-color-brand1);
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 24px;
    font-weight: 700;
    margin-top: 8px
}

._sectionContainer_1gv4v_165 ._contactInfo_1gv4v_201 ._infoItem_1gv4v_209 ._text_1gv4v_238 {
    padding: 0 24px;
    flex: 1 1 auto;
    word-break: break-all !important
}

._sectionContainer_1gv4v_165 ._contactInfo_1gv4v_201 ._infoItem_1gv4v_209 ._text_1gv4v_238 p {
    color: #000;
    font-size: 1rem;
    line-height: 1.5
}

@media screen and (min-width: 700px) {
    ._sectionContainer_1gv4v_165 ._contactInfo_1gv4v_201 ._infoItem_1gv4v_209 {
        flex: 1 1 30%;
        padding: 0
    }
}

._sectionContainer_1gv4v_165 ._formFields_1gv4v_254 {
    margin-bottom: 1.5rem
}

._sectionContainer_1gv4v_165 ._mapContainer_1gv4v_257,
._sectionContainer_1gv4v_165 ._mapContainer_1gv4v_257 ._map_1gv4v_257 {
    height: 100%
}

.rtl ._fieldsHeader_1gv4v_184 {
    text-align: right
}

._actionContainer_1gv4v_268 {
    display: flex;
    justify-content: flex-start
}

._fileUpload_1gv4v_273>div {
    border-color: #000 !important;
    min-height: 2.75rem;
    border-radius: 0 !important
}

._timeClass_1gv4v_279>div {
    border-color: #000;
    min-height: 2.87rem;
    border-radius: 0
}

._timeClass_1gv4v_279 input {
    border: none
}

._radioClass_1gv4v_288 {
    outline-color: #000 !important
}

._selectBoxClass_1gv4v_292 div:nth-child(3) {
    border-color: #000;
    border-radius: 0;
    padding-top: 0;
    padding-bottom: 0
}

._dateClass_1gv4v_299 {
    border-color: #000 !important;
    border-radius: 0 !important
}

._textField_1gv4v_304 {
    height: auto;
    min-height: 2.75rem;
    margin-bottom: 0;
    padding: .5rem .75rem;
    border: 1px solid #000;
    background-color: #fff;
    color: #000;
    font-size: 1rem;
    line-height: 1.6;
    border-radius: 0
}

._textareaField_1gv4v_317 {
    overflow: auto;
    height: auto;
    min-height: 11.25rem;
    border: 1px solid #000;
    padding-top: .75rem;
    padding-bottom: .75rem;
    color: #585757
}

._embedContainer_pr1ku_1 {
    padding: 50px 0;
    justify-content: center;
    align-items: center
}

._embedText_pr1ku_7 {
    width: 100%
}

._embedText_pr1ku_7 h2 {
    margin-bottom: 10px;
    width: 90%;
    font-style: normal;
    font-weight: 700;
    font-size: 40px;
    line-height: 50px;
    color: #252a31
}

._embedText_pr1ku_7 p {
    width: 90%;
    color: #5f738c
}

._embedContainer_q1tde_1 {
    padding: 50px 0;
    justify-content: center;
    align-items: center
}

._embedText_q1tde_7 h2 {
    margin-bottom: 10px;
    width: 100%;
    font-style: normal;
    font-weight: 700;
    font-size: 40px;
    line-height: 50px;
    color: #252a31
}

._embedText_q1tde_7 p {
    width: 100%;
    color: #5f738c
}

._embedContainer_1ys5k_1 {
    padding: 50px 0;
    justify-content: center;
    align-items: center
}

._servicesContainer_gxsuu_1 {
    padding: 50px 0
}

._body_gxsuu_5 {
    padding: 25px 0;
    min-height: 70vh
}

._item_gxsuu_10 {
    margin: 0 auto 20px
}

._iconContainer_gxsuu_15 {
    color: var(--theme-color-brand1);
    font-size: 48px;
    line-height: 1
}

._itemTitle_gxsuu_21 {
    color: #333;
    margin-bottom: 5px;
    padding: 4px 0
}

._itemInfo_gxsuu_27 {
    margin-bottom: 18px
}

._itemInfo_gxsuu_27 p {
    color: #999
}

._servicesTitle_gxsuu_35 {
    color: #000;
    margin-bottom: 25px
}

._servicesTitle_gxsuu_35 h2 {
    color: #333
}

._servicesTitle_gxsuu_35 p {
    width: 33%;
    color: #333;
    line-height: 24px
}

._containerImage_gxsuu_48 {
    height: 400px;
    width: 100%
}

@media screen and (min-width: 768px) {
    ._containerImage_gxsuu_48 {
        position: absolute;
        height: auto;
        top: 0;
        bottom: 0
    }
}

@media (max-width: 375px) {
    ._servicesTitle_gxsuu_35 {
        text-align: center
    }
    ._item_gxsuu_10 {
        width: 100%;
        text-align: center
    }
}

@media (max-width: 812px) {
    ._servicesTitle_gxsuu_35 p {
        width: 100%;
        margin: 0 auto
    }
    ._containerImage_gxsuu_48 {
        position: static;
        height: 200px
    }
}

._servicesContainer_15ibz_1 {
    padding: 25px 0
}

._imageContainer_15ibz_5 {
    display: inline-block
}

._item_15ibz_9 {
    padding: 33px 0
}

._itemImage_15ibz_13 {
    width: 150px;
    height: 150px;
    border-radius: 50%
}

._itemInfo_15ibz_19 {
    padding: 12px 28px 10px;
    margin: 0 auto
}

._itemInfo_15ibz_19 h4 {
    color: #333
}

._itemInfo_15ibz_19 p {
    color: #666;
    line-height: 24px
}

._btnread_15ibz_33 {
    padding: 0;
    color: var(--theme-color-brand1);
    font-weight: 500
}

._servicesTitle_15ibz_39 {
    margin: 0 auto;
    text-align: center;
    color: #000;
    position: relative;
    padding: 22px 0 33px
}

._servicesTitle_15ibz_39 h1 {
    max-width: 60%;
    display: inline-block;
    color: var(--theme-color-brand1);
    line-height: 46px
}

._servicesTitle_15ibz_39 p {
    width: 50%;
    margin: 0 auto;
    color: #868686;
    line-height: 24px
}

._contactMe_15ibz_61 {
    padding: 0;
    background-color: var(--theme-color-brand1) !important;
    font-weight: 500
}

@media (max-width: 768px) {
    ._servicesTitle_15ibz_39 h1 {
        max-width: 100%
    }
    ._servicesTitle_15ibz_39 p {
        width: 100%;
        margin: 0 auto
    }
}

._buttonsWrapper_11djb_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_11djb_138 {
    margin-bottom: 16px
}

._spaceAfter--small_11djb_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_11djb_144 {
    margin-bottom: 30px
}

._servicesContainer_11djb_165 {
    padding: 50px 0
}

._body_11djb_169 {
    padding: 25px 0;
    min-height: 70vh
}

._item_11djb_174 {
    margin: 0 auto 20px;
    background-color: #efefef8c;
    border-radius: 4px;
    padding: 8px 5px 15px
}

._iconContainer_11djb_182 {
    color: var(--theme-color-brand1);
    font-size: 48px;
    line-height: 1
}

._itemTitle_11djb_188 {
    margin-bottom: 5px;
    padding: 4px 0;
    font-weight: 600;
    color: #1c1c1c
}

._itemInfo_11djb_195 p {
    margin-bottom: 0;
    font-size: 16px;
    color: #585757
}

._servicesTitle_11djb_201 {
    color: #000;
    margin-bottom: 25px
}

._servicesTitle_11djb_201 h2 {
    font-weight: 700;
    color: #1c1c1c
}

._servicesTitle_11djb_201 p {
    width: 33%;
    color: #333;
    line-height: 24px
}

._containerImage_11djb_215 {
    height: 400px;
    width: 100%;
    overflow: hidden
}

@media screen and (min-width: 768px) {
    ._containerImage_11djb_215 {
        position: absolute;
        height: 100%;
        top: 0;
        bottom: 0
    }
}

@media (max-width: 375px) {
    ._servicesTitle_11djb_201 {
        text-align: center
    }
    ._item_11djb_174 {
        width: 100%;
        text-align: center
    }
}

@media (max-width: 812px) {
    ._servicesTitle_11djb_201 p {
        width: 100%;
        margin: 0 auto
    }
    ._containerImage_11djb_215 {
        position: static;
        height: 200px;
        overflow: hidden
    }
    ._iconContainer_11djb_182 {
        font-size: 35px
    }
}

._buttonsWrapper_j0y24_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_j0y24_138 {
    margin-bottom: 16px
}

._spaceAfter--small_j0y24_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_j0y24_144 {
    margin-bottom: 30px
}

._servicesContainer_j0y24_165 {
    background-color: #e9ecef
}

@media (min-width: 768px) {
    ._servicesContainer_j0y24_165 {
        padding: 70px 0
    }
}

._itemImage_j0y24_174 {
    display: block;
    justify-content: flex-start;
    font-size: 47px;
    color: var(--theme-color-brand1)
}

@media (min-width: 768px) {
    ._itemImage_j0y24_174 {
        justify-content: flex-end
    }
}

._itemInfo_j0y24_186 {
    padding: 16px 0 10px;
    display: flex;
    flex-direction: column;
    align-items: flex-start
}

._styleitem_j0y24_193 {
    width: 100%;
    min-height: 154px;
    border-radius: 8px;
    background-color: #fff;
    margin: 0 auto 18px;
    padding: 30px 25px 16px;
    box-shadow: 0 5px 50px #615d5d33
}

._itemInfo_j0y24_186 h4 {
    color: #333;
    margin-bottom: 11px
}

._itemInfo_j0y24_186 p {
    color: #0e054a;
    opacity: .6;
    text-align: justify;
    width: 92%
}

._service_j0y24_165 {
    margin: 0 auto;
    color: #000;
    position: relative;
    padding: 16% 20px 25px
}

._service_j0y24_165 h2 {
    color: #0e054a;
    display: inline-block;
    margin-bottom: 10px
}

._service_j0y24_165 p {
    font-weight: 300;
    color: #050558;
    opacity: .56;
    width: 80%
}

._btnread_j0y24_233 {
    color: var(--theme-color-brand1);
    font-weight: 500;
    padding: 0;
    margin: 12px 0
}

._buttonsWrapper_1o49n_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1o49n_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1o49n_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1o49n_144 {
    margin-bottom: 30px
}

._featuresContainer_1o49n_165 {
    padding: 50px 0 70px
}

._featuresContainer_1o49n_165 h2 {
    font-weight: 700;
    text-align: center;
    color: #1c1c1c;
    margin-bottom: 50px
}

._featureItem_1o49n_175 ._itemIcon_1o49n_175 {
    position: relative;
    z-index: 0;
    height: 64px;
    width: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px
}

._featureItem_1o49n_175 ._itemIcon_1o49n_175:before {
    z-index: -1;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--theme-color-brand1);
    top: 0;
    left: 0;
    opacity: .2
}

._featureItem_1o49n_175 ._itemIcon_1o49n_175 i {
    font-size: 24px;
    color: var(--theme-color-brand1);
    margin-top: 5px
}

._featureItem_1o49n_175 ._itemText_1o49n_202 {
    margin: 0 0 0 20px
}

._featureItem_1o49n_175 ._itemText_1o49n_202 h4 {
    font-weight: 600;
    color: #1c1c1c;
    margin: 20px 0
}

._featureItem_1o49n_175 ._itemText_1o49n_202 p {
    font-size: 16px;
    color: #585757
}

.rtl ._itemText_1o49n_202 {
    margin: 0 20px 0 0
}

._buttonsWrapper_agat0_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_agat0_138 {
    margin-bottom: 16px
}

._spaceAfter--small_agat0_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_agat0_144 {
    margin-bottom: 30px
}

._featuresContainer_agat0_165 {
    padding: 50px 0 70px
}

._featureItem_agat0_169 ._itemIcon_agat0_169 {
    position: relative;
    z-index: 0;
    height: 64px;
    width: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px
}

._featureItem_agat0_169 ._itemIcon_agat0_169:before {
    z-index: -1;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--theme-color-brand1);
    top: 0;
    left: 0;
    opacity: .2
}

._featureItem_agat0_169 ._itemIcon_agat0_169 i {
    font-size: 30px;
    color: var(--theme-color-brand1);
    margin-top: 5px
}

._featureItem_agat0_169 ._itemText_agat0_196 h4 {
    font-weight: 600;
    line-height: 26px;
    letter-spacing: -.2px;
    color: #1c1c1c;
    margin: 20px 0
}

._featureItem_agat0_169 ._itemText_agat0_196 p {
    font-size: 16px;
    line-height: 22px;
    letter-spacing: -.2px;
    color: #585757
}

._buttonsWrapper_15f25_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_15f25_138 {
    margin-bottom: 16px
}

._spaceAfter--small_15f25_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_15f25_144 {
    margin-bottom: 30px
}

._featuresContainer_15f25_165 {
    padding: 50px 0 70px
}

._featureItem_15f25_169 ._itemIcon_15f25_169 {
    position: relative;
    z-index: 0;
    height: 32px;
    width: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px
}

._featureItem_15f25_169 ._itemIcon_15f25_169:before {
    z-index: -1;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--theme-color-brand1);
    top: 0;
    left: 0;
    opacity: .2
}

._featureItem_15f25_169 ._itemIcon_15f25_169 i {
    font-size: 16px;
    color: var(--theme-color-brand1);
    margin-top: 5px
}

._featureItem_15f25_169 ._itemText_15f25_196 h4 {
    font-weight: 600;
    line-height: 26px;
    letter-spacing: -.2px;
    color: #1c1c1c;
    margin: 20px 0
}

._featureItem_15f25_169 ._itemText_15f25_196 p {
    font-size: 14px;
    line-height: 22px;
    letter-spacing: -.2px;
    color: #585757
}

._buttonsWrapper_is0pk_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_is0pk_138 {
    margin-bottom: 16px
}

._spaceAfter--small_is0pk_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_is0pk_144 {
    margin-bottom: 30px
}

._featuresContainer_is0pk_165 {
    padding: 50px 0 70px
}

._featureItemContainer_is0pk_169 {
    border-bottom: 1px solid #dbdbdb;
    border-right: 1px solid #dbdbdb
}

._featureItemContainer_is0pk_169:nth-child(3n) {
    border-right: none
}

@media (max-width: 992px) {
    ._featureItemContainer_is0pk_169 {
        border: none
    }
}

._featureItemContainer_is0pk_169 ._featureItem_is0pk_169 {
    text-align: center;
    width: 100%;
    padding: 20px
}

._featureItemContainer_is0pk_169 ._featureItem_is0pk_169 ._itemIcon_is0pk_186 {
    position: relative;
    z-index: 0;
    height: 96px;
    width: 96px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto
}

._featureItemContainer_is0pk_169 ._featureItem_is0pk_169 ._itemIcon_is0pk_186:before {
    z-index: -1;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--theme-color-brand1);
    top: 0;
    left: 0;
    opacity: .2
}

._featureItemContainer_is0pk_169 ._featureItem_is0pk_169 ._itemIcon_is0pk_186 i {
    font-size: 30px;
    color: var(--theme-color-brand1);
    margin-top: 5px
}

._featureItemContainer_is0pk_169 ._featureItem_is0pk_169 ._itemText_is0pk_213 h4 {
    font-weight: 600;
    line-height: 26px;
    letter-spacing: -.2px;
    color: #1c1c1c;
    margin: 20px 0
}

._featureItemContainer_is0pk_169 ._featureItem_is0pk_169 ._itemText_is0pk_213 p {
    font-size: 16px;
    line-height: 22px;
    letter-spacing: -.2px;
    color: #585757
}

._featureItemContainer_is0pk_169 ._featureItem_is0pk_169 hr {
    display: inline-block;
    width: 100px;
    height: 100px;
    transform: rotate(90deg)
}

.rtl ._featureItemContainer_is0pk_169 {
    border-right: none;
    border-left: 1px solid #dbdbdb
}

.rtl ._featureItemContainer_is0pk_169:nth-child(3n) {
    border-left: none
}

@media (max-width: 992px) {
    .rtl ._featureItemContainer_is0pk_169 {
        border: none
    }
}

._buttonsWrapper_y8zwy_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_y8zwy_138 {
    margin-bottom: 16px
}

._spaceAfter--small_y8zwy_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_y8zwy_144 {
    margin-bottom: 30px
}

._featuresContainer_y8zwy_165 {
    padding: 50px 0 65px
}

._featureItem_y8zwy_169 {
    border: 1px solid #dbdbdb;
    padding: 20px;
    border-radius: 8px;
    height: 100%;
    display: flex;
    flex-direction: column
}

._featureItem_y8zwy_169 ._featureHeader_y8zwy_177 {
    display: flex
}

._featureItem_y8zwy_169 ._featureHeader_y8zwy_177 ._itemIcon_y8zwy_180 {
    position: relative;
    z-index: 0;
    height: 32px;
    width: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px
}

._featureItem_y8zwy_169 ._featureHeader_y8zwy_177 ._itemIcon_y8zwy_180:before {
    z-index: -1;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--theme-color-brand1);
    top: 0;
    left: 0;
    opacity: .2
}

._featureItem_y8zwy_169 ._featureHeader_y8zwy_177 ._itemIcon_y8zwy_180 i {
    font-size: 16px;
    color: var(--theme-color-brand1);
    margin-top: 5px
}

._featureItem_y8zwy_169 ._featureHeader_y8zwy_177 h4 {
    font-weight: 600;
    line-height: 26px;
    letter-spacing: -.2px;
    color: #1c1c1c;
    margin: 23px 0 20px 10px;
    flex-grow: 1;
    max-width: 75%
}

._featureItem_y8zwy_169 ._itemText_y8zwy_216 p {
    font-size: 14px;
    line-height: 22px;
    letter-spacing: -.2px;
    color: #585757
}

._featureItem_y8zwy_169 ._serviceBtn_y8zwy_222 {
    display: flex;
    width: -moz-fit-content;
    width: fit-content;
    color: var(--theme-color-brand1);
    font-weight: 700;
    margin-bottom: 10px
}

.rtl ._featureHeader_y8zwy_177 h4 {
    margin: 23px 10px 20px 0
}

._buttonsWrapper_18k1d_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_18k1d_138 {
    margin-bottom: 16px
}

._spaceAfter--small_18k1d_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_18k1d_144 {
    margin-bottom: 30px
}

._featuresContainer_18k1d_165 ._containerLarge_18k1d_165 {
    width: 100%;
    max-width: 80rem;
    margin-right: auto;
    margin-left: auto
}

._featuresContainer_18k1d_165 ._paddingVerticalXhuge_18k1d_171 {
    padding: 7rem 0rem
}

._featuresContainer_18k1d_165 ._wLayoutGrid_18k1d_174 {
    display: grid;
    grid-auto-columns: 1fr;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-row-gap: 16px;
    grid-column-gap: 16px
}

._featuresContainer_18k1d_165 ._layout175Component_18k1d_185 {
    display: grid;
    justify-items: center;
    align-items: start;
    grid-auto-columns: 1fr;
    grid-column-gap: 4rem;
    grid-row-gap: 4rem;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto
}

@media screen and (max-width: 991px) {
    ._featuresContainer_18k1d_165 h3 {
        font-size: 2.25rem
    }
    ._featuresContainer_18k1d_165 ._paddingXhuge_18k1d_205 {
        padding: 6rem
    }
    ._featuresContainer_18k1d_165 ._paddingVertical_18k1d_171 {
        padding-right: 0rem;
        padding-left: 0rem
    }
    ._featuresContainer_18k1d_165 ._layout175Component_18k1d_185 {
        grid-column-gap: 2rem;
        grid-row-gap: 4rem
    }
}

@media screen and (max-width: 767px) {
    ._featuresContainer_18k1d_165 h3 {
        font-size: 2rem
    }
    ._featuresContainer_18k1d_165 ._paddingXhuge_18k1d_205 {
        padding: 4rem
    }
    ._featuresContainer_18k1d_165 ._paddingVertical_18k1d_171 {
        padding-right: 0rem;
        padding-left: 0rem
    }
    ._featuresContainer_18k1d_165 ._layout175Component_18k1d_185 {
        grid-row-gap: 3rem;
        grid-template-columns: 1fr
    }
}

@media screen and (max-width: 479px) {
    ._featuresContainer_18k1d_165 ._paddingVertical_18k1d_171 {
        padding-right: 0rem;
        padding-left: 0rem
    }
    ._featuresContainer_18k1d_165 ._layout175Component_18k1d_185 {
        grid-template-columns: 1fr
    }
}

._featureItem_18k1d_245 {
    word-break: break-all !important
}

._featureItem_18k1d_245 ._layout175Content_18k1d_248 {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center
}

._featureItem_18k1d_245 ._marginBottomSmall_18k1d_264 {
    margin: 0rem 0rem 1.5rem
}

._featureItem_18k1d_245 ._iconContainer_18k1d_267 {
    font-size: 3rem;
    line-height: 1;
    color: var(--theme-color-brand1)
}

._featureItem_18k1d_245 ._marginTopMedium_18k1d_272 {
    margin: 2rem 0rem 0rem
}

._featureItem_18k1d_245 ._buttonRowCenter_18k1d_275 {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    justify-content: center
}

@media screen and (max-width: 991px) {
    ._featureItem_18k1d_245 ._marginTop_18k1d_272 {
        margin-right: 0rem;
        margin-bottom: 0rem;
        margin-left: 0rem
    }
    ._featureItem_18k1d_245 ._marginBottom_18k1d_264 {
        margin-top: 0rem;
        margin-right: 0rem;
        margin-left: 0rem
    }
}

@media screen and (max-width: 767px) {
    ._featureItem_18k1d_245 ._marginSmall_18k1d_307 {
        margin: 1.25rem
    }
    ._featureItem_18k1d_245 ._marginMedium_18k1d_310 {
        margin: 1.5rem
    }
    ._featureItem_18k1d_245 ._marginTop_18k1d_272 {
        margin-right: 0rem;
        margin-bottom: 0rem;
        margin-left: 0rem
    }
    ._featureItem_18k1d_245 ._marginBottom_18k1d_264 {
        margin-top: 0rem;
        margin-right: 0rem;
        margin-left: 0rem
    }
}

@media screen and (max-width: 479px) {
    ._featureItem_18k1d_245 ._marginTop_18k1d_272 {
        margin-right: 0rem;
        margin-bottom: 0rem;
        margin-left: 0rem
    }
    ._featureItem_18k1d_245 ._marginBottom_18k1d_264 {
        margin-top: 0rem;
        margin-right: 0rem;
        margin-left: 0rem
    }
}

._buttonsWrapper_nh8jj_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_nh8jj_138 {
    margin-bottom: 16px
}

._spaceAfter--small_nh8jj_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_nh8jj_144 {
    margin-bottom: 30px
}

._footer_nh8jj_165 {
    padding: 20px 0;
    color: #000
}

._footerLinks_nh8jj_170 {
    margin: 0 0 20px;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    padding: 0
}

._footerLinks_nh8jj_170 ._linkItem_nh8jj_177 {
    margin: 0;
    padding: 0
}

._footerLinks_nh8jj_170 a {
    padding: 0 13px 0 0;
    font-weight: 700;
    line-height: 36px;
    color: #000
}

@media (min-width: 768px) {
    ._footerLinks_nh8jj_170 {
        text-align: left;
        margin: 0
    }
    .rtl ._footerLinks_nh8jj_170 {
        text-align: right
    }
}

._footerSeparator_nh8jj_197 {
    margin: 20px 0;
    width: 100%;
    height: 1px;
    background: #96999d;
    text-align: center
}

._footerSocial_nh8jj_205 {
    margin: 0 0 20px;
    text-align: center
}

.rtl ._footerSocial_nh8jj_205 {
    text-align: center
}

._footerSocial_nh8jj_205 a {
    padding: 0 10px 0 0;
    line-height: 20px;
    color: #383838
}

@media (min-width: 768px) {
    ._footerSocial_nh8jj_205 {
        text-align: left
    }
    .rtl ._footerSocial_nh8jj_205 {
        text-align: right
    }
}

._footerCopyright_nh8jj_226 {
    line-height: 20px;
    color: #6b6b6b;
    text-align: left
}

.rtl ._footerCopyright_nh8jj_226 {
    text-align: left
}

._buttonsWrapper_qm4xs_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_qm4xs_138 {
    margin-bottom: 16px
}

._spaceAfter--small_qm4xs_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_qm4xs_144 {
    margin-bottom: 30px
}

._footer_qm4xs_165 {
    padding: 65px 0 60px;
    color: #000
}

._footerLogo_qm4xs_170 {
    margin-bottom: 24px;
    display: flex;
    justify-content: center
}

._footerLogo_qm4xs_170 img {
    height: 45px
}

._titleText_qm4xs_179 {
    text-align: center;
    color: #999;
    font-weight: 600;
    width: auto;
    margin: 0 auto 28px
}

._footerSocial_qm4xs_187 {
    display: flex;
    justify-content: center;
    margin: 0 0 20px
}

._footerSocial_qm4xs_187 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._footerSocial_qm4xs_187 ul li {
    margin: 0 10px;
    width: 44px;
    height: 44px;
    padding: 3px;
    border-radius: 16px;
    background-color: #0000000d;
    display: flex;
    justify-content: center;
    align-items: center
}

._footerSocial_qm4xs_187 ul li a {
    text-decoration: none;
    color: #999
}

._footerSeparator_qm4xs_214 {
    margin: 20px 0;
    width: 100%;
    height: 1px;
    background: #96999d;
    text-align: center
}

._footerCopyright_qm4xs_222 {
    line-height: 20px;
    color: #6b6b6b;
    text-align: center
}

._buttonsWrapper_10602_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_10602_138 {
    margin-bottom: 16px
}

._spaceAfter--small_10602_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_10602_144 {
    margin-bottom: 30px
}

._footer_10602_165 {
    padding: 35px 0 30px;
    color: #000
}

._footerLinks_10602_170 {
    list-style: none;
    margin: 20px;
    padding: 0
}

._footerLinks_10602_170 ._linkItem_10602_175 {
    margin: 0;
    padding: 0;
    display: inline-block
}

._footerLinks_10602_170 a {
    padding: 0 0 0 28px;
    font-weight: 700;
    line-height: 36px;
    color: #000
}

@media (min-width: 768px) {
    ._footerLinks_10602_170 {
        margin: 0
    }
}

._footerSocial_10602_192 {
    display: flex;
    justify-content: center
}

._footerSocial_10602_192 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0
}

._footerSocial_10602_192 ul li {
    margin: 0 10px;
    display: flex;
    justify-content: center;
    align-items: center
}

._footerSocial_10602_192 ul li a {
    text-decoration: none;
    font-size: 22px;
    color: #999
}

._footer_1gh5i_1 {
    padding: 65px 0 60px;
    color: #000
}

._footerLogo_1gh5i_6 {
    margin-bottom: 24px;
    display: flex;
    justify-content: center
}

._footerLogo_1gh5i_6 img {
    height: 45px
}

._titleText_1gh5i_15 {
    text-align: center;
    color: #999;
    font-weight: 600;
    max-width: 55%;
    margin: 0 auto
}

._footerSocial_1gh5i_23 {
    display: flex;
    justify-content: center;
    margin-bottom: 24px
}

._footerSocial_1gh5i_23 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._footerSocial_1gh5i_23 ul li {
    margin: 0 10px;
    width: 44px;
    height: 44px;
    border-radius: 16px;
    background-color: #0000000d;
    display: flex;
    justify-content: center;
    align-items: center
}

._footerSocial_1gh5i_23 ul li a {
    text-decoration: none;
    color: #999
}

._footer_1xo2u_1 {
    padding: 35px 0 30px;
    color: #000
}

._footerLogo_1xo2u_6 {
    display: flex;
    justify-content: center
}

._footerLogo_1xo2u_6 img {
    height: 35px
}

._footerLinks_1xo2u_14 {
    list-style: none;
    margin: 0;
    padding: 0;
    display: inline;
    justify-content: center;
    align-items: center;
    background-color: unset
}

._footerLinks_1xo2u_14 ._linkItem_1xo2u_23 {
    margin: 0;
    padding: 0;
    display: inline-block
}

._footerLinks_1xo2u_14 a {
    padding: 0 0 0 28px;
    font-weight: 700;
    line-height: 36px;
    color: #000
}

._footerSocial_1xo2u_35 {
    display: flex;
    justify-content: center
}

._footerSocial_1xo2u_35 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0
}

._footerSocial_1xo2u_35 ul li {
    margin: 0 10px;
    display: flex;
    justify-content: center;
    align-items: center
}

._footerSocial_1xo2u_35 ul li a {
    text-decoration: none;
    font-size: 22px;
    color: #999
}

._footer_ugo6y_1 {
    padding: 65px 0 60px;
    color: #000
}

._footerLogo_ugo6y_6 {
    margin-bottom: 16px;
    display: flex;
    justify-content: center
}

._footerLogo_ugo6y_6 img {
    height: 45px
}

._titleText_ugo6y_15 {
    text-align: center;
    color: #999;
    font-weight: 600;
    max-width: 55%;
    margin: 0 auto
}

._footerLinks_ugo6y_23 {
    list-style: none;
    margin: 0 0 18px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center
}

._footerLinks_ugo6y_23 ._linkItem_ugo6y_31 {
    margin: 0;
    padding: 0;
    display: inline-block
}

._footerLinks_ugo6y_23 a {
    padding: 0 0 0 28px;
    font-weight: 600;
    line-height: 36px;
    color: #000
}

._footerSocial_ugo6y_43 {
    display: flex;
    justify-content: center;
    margin-bottom: 24px
}

._footerSocial_ugo6y_43 ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 16px 0
}

._footerSocial_ugo6y_43 ul li {
    margin: 0 10px;
    width: 34px;
    height: 34px;
    border-radius: 16px;
    background-color: #0000000d;
    display: flex;
    justify-content: center;
    align-items: center
}

._footerSocial_ugo6y_43 ul li a {
    text-decoration: none
}

._footerSocial_ugo6y_43 ul li a i {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #999
}

@media handheld,
only screen and (max-width: 767px) {
    ._footerLogo_ugo6y_6,
    ._footerLinks_ugo6y_23,
    ._footerSocial_ugo6y_43 {
        margin: 0 0 20px;
        text-align: center
    }
    ._footerCopyright_ugo6y_84 {
        text-align: center
    }
    ._footerSeparator_ugo6y_87 {
        display: none
    }
}

._galleryContainer_1anml_1 {
    width: 90%;
    margin: 0 auto
}

._row_1anml_6 {
    justify-content: center
}

._imageCol_1anml_11 {
    padding: 15px
}

._itemImage_1anml_15:hover {
    box-shadow: 0 0 20px #3b000014, 0 10px 20px #3b000014, 0 20px 20px #3b000014
}

._body_1anml_19 {
    padding: 120px 0
}

._body_1anml_19 ._intro_1anml_22 {
    width: auto;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center
}

._body_1anml_19 ._intro_1anml_22 ._text_1anml_30 {
    width: 100%
}

._body_1anml_19 ._intro_1anml_22 h2 {
    line-height: 42px;
    font-weight: 600;
    font-size: 36px
}

._body_1anml_19 ._intro_1anml_22 p {
    text-align: center;
    margin-top: 17px;
    margin-bottom: 50px;
    color: #00000080;
    font-weight: 300
}

._body_1anml_19 ._intro_1anml_22 ._aboutbn_1anml_45 {
    border-radius: 3px;
    border: solid 1px #5c58cb;
    color: #5c58cb;
    padding: 8px 50px;
    margin-bottom: 72px
}

._body_1anml_19 ._imgMain_1anml_52 {
    box-shadow: 0 1px 16px #0003
}

._imageContainer_1anml_56 img {
    display: flex;
    align-items: center;
    width: 100%;
    aspect-ratio: 1/1
}

._galleryContainer_1ximo_1 {
    width: 100%;
    margin: 0 auto
}

._row_1ximo_6 {
    justify-content: center
}

._imageCol_1ximo_11 {
    padding: 10px;
    width: 50%
}

._itemImage_1ximo_16:hover {
    box-shadow: 0 0 20px #3b000014, 0 10px 20px #3b000014, 0 20px 20px #3b000014
}

._body_1ximo_20 {
    padding: 120px 0
}

._body_1ximo_20 ._intro_1ximo_23 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center
}

._body_1ximo_20 ._intro_1ximo_23 ._text_1ximo_30 {
    width: 100%
}

._body_1ximo_20 ._intro_1ximo_23 h2 {
    line-height: 42px;
    font-weight: 600;
    font-size: 36px
}

._body_1ximo_20 ._intro_1ximo_23 p {
    text-align: center;
    margin-top: 17px;
    margin-bottom: 50px;
    color: #00000080;
    font-weight: 300
}

._body_1ximo_20 ._intro_1ximo_23 ._aboutbn_1ximo_45 {
    border-radius: 3px;
    border: solid 1px #5c58cb;
    color: #5c58cb;
    padding: 8px 50px;
    margin-bottom: 72px
}

._body_1ximo_20 ._imgMain_1ximo_52 {
    box-shadow: 0 1px 16px #0003
}

._imageContainer_1ximo_56 img {
    aspect-ratio: 16/9
}

._section_1heqj_1 {
    padding: 70px 20px;
    text-align: center
}

._section_1heqj_1 h1 {
    line-height: 42px;
    font-weight: 600
}

._section_1heqj_1 p {
    text-align: center;
    margin-top: 17px;
    margin-bottom: 50px;
    color: #00000080;
    font-weight: 300
}

._section_1heqj_1 {
    padding: 70px 20px
}

._cont_1heqj_21 {
    max-width: 1140px;
    margin-right: auto;
    margin-left: auto
}

._gallerywrapper_1heqj_27 {
    -moz-column-count: 3;
    column-count: 3;
    -moz-column-gap: 14px;
    column-gap: 14px
}

._imagewrapper_1heqj_34 {
    width: 100%;
    margin-bottom: 14px
}

._imagewrapper_1heqj_34:hover {
    box-shadow: 0 0 20px #3b000014, 0 10px 20px #3b000014, 0 20px 20px #3b000014
}

@media (max-width: 767px) {
    ._gallerywrapper_1heqj_27 {
        -moz-column-count: 1;
        column-count: 1
    }
}

@media (max-width: 479px) {
    ._gallerywrapper_1heqj_27 {
        -moz-column-count: 1;
        column-count: 1
    }
}

._galleryContainer_1po5x_1 {
    width: 100%;
    margin: 0 auto
}

._row_1po5x_6 {
    justify-content: center
}

._imageCol_1po5x_11 {
    padding: 15px
}

._itemImage_1po5x_15 {
    background-position: top left !important
}

._itemImage_1po5x_15:hover {
    box-shadow: 0 0 20px #3b000014, 0 10px 20px #3b000014, 0 20px 20px #3b000014
}

._body_1po5x_22 {
    padding: 120px 0
}

._body_1po5x_22 ._intro_1po5x_25 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center
}

._body_1po5x_22 ._intro_1po5x_25 ._text_1po5x_32 {
    width: 100%
}

._body_1po5x_22 ._intro_1po5x_25 h2 {
    line-height: 42px;
    font-weight: 600;
    font-size: 36px
}

._body_1po5x_22 ._intro_1po5x_25 p {
    text-align: center;
    margin-top: 17px;
    margin-bottom: 50px;
    color: #00000080;
    font-weight: 300
}

._body_1po5x_22 ._intro_1po5x_25 ._aboutbn_1po5x_47 {
    border-radius: 3px;
    border: solid 1px #5c58cb;
    color: #5c58cb;
    padding: 8px 50px;
    margin-bottom: 72px
}

._body_1po5x_22 ._imgMain_1po5x_54 {
    box-shadow: 0 1px 16px #0003
}

._imageContainer_1po5x_58 {
    width: 100%;
    height: 100%
}

._imageContainer_1po5x_58 img {
    aspect-ratio: 16/9;
    max-height: unset !important
}

._imageContainer_1po5x_58 span {
    aspect-ratio: 16/9
}

._buttonsWrapper_1rj9m_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1rj9m_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1rj9m_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1rj9m_144 {
    margin-bottom: 30px
}

._galleryx00_1rj9m_165 {
    padding: 70px 0
}

._galleryx00_1rj9m_165 ._intro_1rj9m_168 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center
}

._galleryx00_1rj9m_165 ._intro_1rj9m_168 ._text_1rj9m_175 {
    width: 100%
}

._galleryx00_1rj9m_165 ._intro_1rj9m_168 h2 {
    white-space: normal;
    margin: 10px 0;
    font-size: 44px;
    line-height: 52px;
    font-weight: 500
}

._galleryx00_1rj9m_165 ._intro_1rj9m_168 p {
    margin-bottom: 10px;
    opacity: .7
}

._galleryx00_1rj9m_165 ._heroxbtn_1rj9m_189 {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 18px 30px;
    background-color: #18181d;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1));
    text-align: center;
    text-decoration: none;
    text-transform: uppercase;
    align-self: center;
    color: #fff;
    font-size: 14px;
    line-height: 24px;
    font-weight: 500;
    border-radius: 4px;
    margin: 30px 0
}

._galleryx00_1rj9m_165 ._heroxbtn_1rj9m_189:hover {
    box-shadow: 1px 5px 10px 10px #3b000014, 0 10px 20px #3b000014, 0 20px 20px #3b000014
}

._galleryx00_1rj9m_165 ._galleryxitem_1rj9m_211 {
    grid-template-rows: auto auto auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 273px;
    grid-auto-columns: 1fr;
    grid-column-gap: 0px;
    grid-row-gap: 0px;
    margin: 0 5px
}

._galleryx00_1rj9m_165 ._galleryxitem_1rj9m_211 ._galleryximg_1rj9m_222 {
    width: 100%;
    height: 100%;
    background-size: cover;
    transition: opacity .2s ease
}

._galleryx00_1rj9m_165 ._galleryxitem_1rj9m_211 ._galleryximg_1rj9m_222 img {
    aspect-ratio: 1/1
}

@media (min-width: 768px) {
    ._galleryx00_1rj9m_165 ._galleryxitem_1rj9m_211 {
        min-height: 360px;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto
    }
}

@media (min-width: 992px) {
    ._galleryx00_1rj9m_165 ._galleryxitem_1rj9m_211 {
        min-height: 170px
    }
}

@media (min-width: 992px) and (min-width: 1200px) {
    ._galleryx00_1rj9m_165 ._galleryxitem_1rj9m_211 {
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        grid-template-rows: auto
    }
}

._galleryContainer_1siuf_1 {
    width: 100%;
    margin: 0 auto
}

._imageCol_1siuf_6 {
    padding: 15px
}

._itemImage_1siuf_10:hover {
    box-shadow: 0 0 20px #3b000014, 0 10px 20px #3b000014, 0 20px 20px #3b000014
}

._imageContainer_1siuf_14 {
    width: 100%;
    height: 100%
}

._imageContainer_1siuf_14 img {
    aspect-ratio: 1/1
}

._body_1siuf_22 {
    padding: 120px 0;
    color: #000;
    font-size: 1rem;
    line-height: 1.5
}

._body_1siuf_22 ._container_1siuf_28 {
    width: 100%;
    max-width: 80rem;
    margin-right: auto;
    margin-left: auto;
    padding-top: 5%;
    padding-bottom: 5%
}

._body_1siuf_22 ._button_1siuf_36 {
    padding: .75rem 1.5rem;
    border-style: solid;
    border-width: 1px;
    border-color: var(--theme-color-brand1);
    background-color: var(--theme-color-brand1);
    color: #fff;
    text-align: center;
    border-radius: 0;
    outline: 0
}

._body_1siuf_22 ._button_row_1siuf_47 {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    grid-column-gap: 1rem;
    grid-row-gap: 1rem
}

._body_1siuf_22 ._margin_top_1siuf_62 {
    margin-right: 0rem;
    margin-bottom: 0rem;
    margin-left: 0rem
}

._body_1siuf_22 ._button_row_1siuf_47._is_button_row_center_1siuf_67 {
    justify-content: center
}

._body_1siuf_22 h2 {
    font-size: 3rem !important;
    font-weight: 700
}

._body_1siuf_22 p {
    font-size: 1.125rem !important
}

._body_1siuf_22 ._text_container_1siuf_80 {
    margin-top: 0rem;
    text-align: center;
    width: 100%;
    max-width: 48rem;
    margin-right: auto;
    margin-left: auto;
    margin-bottom: 5%
}

._header_bzxzr_1 {
    background: var(--theme-color-brand1)
}

._overlay_bzxzr_5 {
    background-image: linear-gradient(180deg, var(--theme-color-brand1) 0%, var(--theme-color-secondary1) 100%);
    opacity: .8
}

._content_bzxzr_10 {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 250px
}

._content_bzxzr_10 h1 {
    display: inline-block;
    color: #fff
}

._header_1web2_1 {
    background: linear-gradient(to right, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._content_1web2_5 {
    min-height: 250px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff
}

._header_34r5f_1 {
    background: var(--theme-color-brand1)
}

._overlay_34r5f_5 {
    background-image: linear-gradient(180deg, var(--theme-color-brand1) 0%, var(--theme-color-secondary1) 100%);
    opacity: .8
}

._content_34r5f_10 {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 350px
}

._content_34r5f_10 h1 {
    display: inline-block;
    color: #fff;
    margin-top: 35px
}

._buttonsWrapper_108gk_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_108gk_138 {
    margin-bottom: 16px
}

._spaceAfter--small_108gk_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_108gk_144 {
    margin-bottom: 30px
}

._header_108gk_165 {
    padding: 24px 0
}

._desktopMenu_108gk_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_108gk_169 {
        display: flex;
        margin-inline-start: 10px
    }
}

._mobileMenuContainer_108gk_179 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_108gk_179 {
        display: none
    }
}

._burger_108gk_188 {
    color: var(--navLinksColor)
}

._logoImg_108gk_192 {
    margin-inline-end: 20px
}

._logoImg_108gk_192 img {
    height: 40px
}

._phone_108gk_199 {
    margin: 0 5px
}

._contactInfo_108gk_203 {
    display: none;
    margin-inline-end: 10px
}

@media (min-width: 992px) {
    ._contactInfo_108gk_203 {
        display: flex
    }
}

._contactInfoItem_108gk_213 {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 0 5px
}

._contactInfoItem_108gk_213 a {
    display: flex;
    align-items: center
}

._contactInfoIcon_108gk_224 {
    margin: 5px
}

._navMenu_108gk_228 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_108gk_228 .nav-item {
    padding: 0
}

._navLink_108gk_236 {
    color: var(--navLinksColor, #000) !important;
    padding: 5px 10px;
    font-size: 16px
}

._navLink_108gk_236:hover {
    color: var(--navLinksColor, #000) !important
}

._buttonLink_108gk_245 {
    padding: 10px 5px !important;
    border-width: 1px !important
}

._languageSwitcher_108gk_250 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_108gk_250 {
        display: flex
    }
}

._buttonsWrapper_ont1i_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_ont1i_138 {
    margin-bottom: 16px
}

._spaceAfter--small_ont1i_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_ont1i_144 {
    margin-bottom: 30px
}

._navBar_ont1i_165 {
    padding: 24px 15px
}

._desktopMenu_ont1i_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_ont1i_169 {
        display: flex;
        margin-inline-start: 10px
    }
}

._mobileMenuContainer_ont1i_179 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_ont1i_179 {
        display: none
    }
}

._navMenu_ont1i_188 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_ont1i_188 .nav-item {
    padding: 0
}

._navLink_ont1i_196 {
    color: var(--navLinksColor, #000) !important;
    padding: 5px 10px;
    font-size: 16px
}

._navLink_ont1i_196:hover {
    color: var(--navLinksColor, #000) !important
}

._logoImg_ont1i_205 {
    margin-inline-end: 20px
}

._logoImg_ont1i_205 img {
    height: 40px
}

._contactInfoItem_ont1i_212 {
    display: none
}

@media (min-width: 992px) {
    ._contactInfoItem_ont1i_212 {
        display: flex;
        align-items: center;
        margin-inline-end: 10px;
        font-weight: 500
    }
    ._contactInfoItem_ont1i_212 a {
        display: flex;
        align-items: center
    }
}

._contactInfoItem_ont1i_212 i {
    padding: 0 5px;
    opacity: .75
}

._contactLink_ont1i_232 {
    color: var(--navLinksColor, #000) !important;
    padding: 0 !important;
    opacity: .6;
    font-size: 16px
}

._contactLink_ont1i_232:hover {
    opacity: 1
}

._contactLink_ont1i_232 a:link,
._contactLink_ont1i_232 a:visited,
._contactLink_ont1i_232 a:hover,
._contactLink_ont1i_232 a:active {
    color: var(--navLinksColor, #000) !important
}

._burger_ont1i_248 {
    color: var(--navLinksColor)
}

._buttonLink_ont1i_252 {
    padding: 10px 5px !important;
    border-width: 1px !important
}

._languageSwitcher_ont1i_257 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_ont1i_257 {
        display: flex
    }
}

._buttonsWrapper_wm9en_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_wm9en_138 {
    margin-bottom: 16px
}

._spaceAfter--small_wm9en_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_wm9en_144 {
    margin-bottom: 30px
}

._navBar_wm9en_165 {
    width: 100%;
    display: flex;
    align-items: center;
    height: -moz-fit-content;
    height: fit-content;
    margin: 0 auto;
    padding: 24px 0
}

@media (min-width: 768px) {
    ._navBar_wm9en_165 {
        margin: 0;
        width: 100%;
        z-index: 999;
        display: flex;
        align-items: center
    }
    ._navBar_wm9en_165 .emptyRowLarge {
        display: none
    }
}

._navMenu_wm9en_186 {
    display: none
}

._navMenu_wm9en_186 .nav-link {
    color: #000000b3;
    padding: 5px 10px;
    font-size: 16px
}

._navMenu_wm9en_186 .nav-link:hover {
    color: #000
}

._navMenu_wm9en_186 .nav-item {
    margin: 0 10px;
    color: #000;
    padding: 0
}

@media (min-width: 992px) {
    ._navMenu_wm9en_186 {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap
    }
}

._logo_wm9en_211 {
    margin-inline-end: 10px
}

._languageSwitcher_wm9en_215 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_wm9en_215 {
        display: flex
    }
}

._desktopMenu_wm9en_224 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_wm9en_224 {
        display: flex;
        margin-inline-start: 10px
    }
}

._mobileMenuContainer_wm9en_234 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_wm9en_234 {
        display: none
    }
}

._burger_wm9en_243 {
    color: var(--navLinksColor)
}

._buttonsWrapper_p8d4j_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_p8d4j_138 {
    margin-bottom: 16px
}

._spaceAfter--small_p8d4j_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_p8d4j_144 {
    margin-bottom: 30px
}

._logo_p8d4j_165 {
    margin-inline-end: 20px
}

._burger_p8d4j_169 {
    color: var(--navLinksColor)
}

._desktopMenu_p8d4j_173 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_p8d4j_173 {
        display: flex;
        margin-inline-start: 10px
    }
}

._navBar_p8d4j_183 {
    display: flex;
    background: #fff;
    align-items: center;
    min-height: 60px;
    padding: 24px 0;
    height: auto;
    margin: 0 auto
}

._navBar_p8d4j_183 a {
    text-decoration: none !important
}

._navMenu_p8d4j_196 {
    display: flex;
    flex-wrap: wrap;
    flex-grow: 1
}

@media (min-width: 768px) {
    ._navMenu_p8d4j_196 {
        display: flex
    }
}

._navMenu_p8d4j_196 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_p8d4j_196 li {
    padding: 0 0 0 16px
}

._navMenu_p8d4j_196 ._navLink_p8d4j_213 {
    color: #000;
    opacity: .6;
    font-weight: 600;
    font-size: 16px
}

._navMenu_p8d4j_196 ._navLink_p8d4j_213:hover {
    color: #000;
    opacity: 1
}

._mobileMenuContainer_p8d4j_224 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_p8d4j_224 {
        display: none
    }
}

._socialLinksContainer_p8d4j_233 {
    display: none
}

@media (min-width: 992px) {
    ._socialLinksContainer_p8d4j_233 {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px
    }
}

._socialIcons_p8d4j_245 {
    display: flex;
    padding: 0;
    margin-bottom: 0;
    margin-inline-end: 10px
}

._socialIcon_p8d4j_245 {
    display: flex;
    margin: 0 0 0 10px
}

.rtl ._socialIcon_p8d4j_245 {
    margin: 0 10px 0 0
}

._languageSwitcher_p8d4j_260 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_p8d4j_260 {
        display: flex
    }
}

._buttonsWrapper_g3bng_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_g3bng_138 {
    margin-bottom: 16px
}

._spaceAfter--small_g3bng_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_g3bng_144 {
    margin-bottom: 30px
}

._navbarBrand_g3bng_165 {
    margin-inline-end: 10px
}

._langSwitcher_g3bng_169 {
    margin: 0 0 0 auto
}

.rtl ._langSwitcher_g3bng_169 {
    margin: 0 auto 0 0
}

._desktopMenu_g3bng_176 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_g3bng_176 {
        display: flex
    }
}

._mobileMenuContainer_g3bng_185 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_g3bng_185 {
        display: none
    }
}

._siteBar_g3bng_194 {
    background: #ffffff;
    padding: 16px 0
}

._navMenu_g3bng_199 {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 0
}

._navMenu_g3bng_199 .nav-item {
    padding: 0
}

._navLink_g3bng_210 {
    color: #000000b3
}

._navLink_g3bng_210:hover {
    color: #000
}

._languageSwitcher_g3bng_217 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_g3bng_217 {
        display: flex
    }
}

._burger_g3bng_226 {
    color: var(--navLinksColor)
}

._buttonsWrapper_5tt20_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_5tt20_138 {
    margin-bottom: 16px
}

._spaceAfter--small_5tt20_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_5tt20_144 {
    margin-bottom: 30px
}

._desktopMenu_5tt20_165 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_5tt20_165 {
        display: flex
    }
}

._navMenu_5tt20_174 {
    display: flex;
    flex-wrap: wrap
}

@media (min-width: 768px) {
    ._navMenu_5tt20_174 {
        display: flex
    }
}

._navMenu_5tt20_174 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_5tt20_174 li {
    padding: 0 0 0 16px
}

._navMenu_5tt20_174 ._navLink_5tt20_190 {
    opacity: .6;
    padding: 5px 10px
}

._navMenu_5tt20_174 ._navLink_5tt20_190:hover {
    opacity: 1
}

._mobileMenuContainer_5tt20_198 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_5tt20_198 {
        display: none
    }
}

._languageSwitcher_5tt20_207 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_5tt20_207 {
        display: flex
    }
}

@media (min-width: 768px) {
    .dropdown-menu a._navLink_5tt20_190 {
        color: #000000fa !important
    }
    .dropdown-menu a._navLink_5tt20_190:hover {
        color: #000 !important
    }
}

._burger_5tt20_224 {
    color: var(--navLinksColor)
}

._navBarRow_5tt20_228 {
    padding: 20px 0;
    margin: 0 10px
}

._logoImg_5tt20_233 {
    margin-inline-end: 10px
}

._mobileMenu_5tt20_198 {
    background-image: linear-gradient(126deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._contactInfoItem_5tt20_241 {
    display: none
}

@media (min-width: 992px) {
    ._contactInfoItem_5tt20_241 {
        display: flex;
        align-items: center;
        font-weight: 500;
        margin-inline-end: 15px
    }
}

._contactInfoItem_5tt20_241 i {
    padding: 0 5px;
    opacity: .75
}

._contactLink_5tt20_257 {
    color: var(--navLinksColor) !important;
    padding: 0;
    opacity: .6
}

._contactLink_5tt20_257:hover {
    opacity: 1
}

._contactLink_5tt20_257 a:link,
._contactLink_5tt20_257 a:visited,
._contactLink_5tt20_257 a:hover,
._contactLink_5tt20_257 a:active {
    color: var(--navLinksColor) !important
}

._buttonsWrapper_12cpo_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_12cpo_138 {
    margin-bottom: 16px
}

._spaceAfter--small_12cpo_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_12cpo_144 {
    margin-bottom: 30px
}

._logoImg_12cpo_165 {
    margin-inline-end: 10px
}

._socialLinksContainer_12cpo_169 {
    display: none
}

@media (min-width: 992px) {
    ._socialLinksContainer_12cpo_169 {
        display: flex;
        justify-content: flex-end
    }
}

._socialIcons_12cpo_179 {
    display: flex;
    padding: 0;
    margin: 0;
    margin-inline-end: 20px;
    align-items: center;
    font-size: 30px
}

._socialIcons_12cpo_179 ._socialIcon_12cpo_179 {
    display: flex;
    justify-content: center
}

._socialIcons_12cpo_179 ._socialIcon_12cpo_179:last-child {
    margin: 0
}

._socialIcons_12cpo_179 ._socialIcon_12cpo_179 a,
._socialIcons_12cpo_179 ._socialIcon_12cpo_179 i,
._socialIcons_12cpo_179 ._socialIcon_12cpo_179 svg {
    text-decoration: none !important
}

@media (min-width: 768px) {
    ._socialIcons_12cpo_179 {
        font-size: 24px
    }
    ._socialIcons_12cpo_179 ._socialIcon_12cpo_179 {
        margin-right: 0
    }
}

._burger_12cpo_208 {
    color: var(--navLinksColor)
}

._navBar_12cpo_212 {
    display: flex;
    align-items: center;
    min-height: 94px;
    width: 92%;
    margin: 0 auto;
    padding: 18px 0
}

@media (min-width: 768px) {
    ._navBar_12cpo_212 {
        width: 100%
    }
}

._navBar_12cpo_212 .emptyRowLarge {
    display: none
}

@media (min-width: 768px) {
    .dropdown-menu a._navLink_12cpo_230 {
        color: #000000fa !important
    }
    .dropdown-menu a._navLink_12cpo_230:hover {
        color: #000 !important
    }
}

._navMenu_12cpo_237 {
    display: flex;
    flex-wrap: wrap
}

@media (min-width: 768px) {
    ._navMenu_12cpo_237 {
        display: flex
    }
}

._navMenu_12cpo_237 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_12cpo_237 li {
    padding: 0 0 0 16px
}

._navMenu_12cpo_237 ._navLink_12cpo_230 {
    color: #fff;
    opacity: .6;
    padding: 5px 10px
}

._navMenu_12cpo_237 ._navLink_12cpo_230:hover {
    color: #fff;
    opacity: 1
}

._navMenu_12cpo_237 .dropdown-menu>._navLink_12cpo_230 {
    color: #000000e6
}

._navMenu_12cpo_237 .dropdown-menu>._navLink_12cpo_230:hover {
    color: #000
}

._desktopMenu_12cpo_269 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_12cpo_269 {
        display: flex
    }
}

._mobileMenuContainer_12cpo_278 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_12cpo_278 {
        display: none
    }
}

._languageSwitcher_12cpo_287 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_12cpo_287 {
        display: flex
    }
}

._buttonsWrapper_1mve4_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1mve4_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1mve4_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1mve4_144 {
    margin-bottom: 30px
}

._desktopMenu_1mve4_165 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1mve4_165 {
        display: flex
    }
}

._navBar_1mve4_174 {
    background: #ffffff;
    z-index: 10;
    display: flex;
    align-items: center;
    min-height: 60px;
    padding: 10px 20px
}

._navMenu_1mve4_183 {
    display: flex;
    flex-wrap: wrap
}

@media (min-width: 768px) {
    ._navMenu_1mve4_183 {
        display: flex
    }
}

._navMenu_1mve4_183 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_1mve4_183 li {
    padding: 0 0 0 16px
}

._navMenu_1mve4_183 ._navLink_1mve4_199 {
    color: #0009;
    font-weight: 600;
    padding: 5px 10px
}

._navMenu_1mve4_183 ._navLink_1mve4_199:hover {
    color: #000
}

._languageSwitcher_1mve4_208 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_1mve4_208 {
        display: flex
    }
}

._mobileMenuContainer_1mve4_217 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1mve4_217 {
        display: none
    }
}

._logo_1mve4_226 {
    margin-inline-end: 20px
}

._burger_1mve4_230 {
    color: var(--navLinksColor)
}

._buttonsWrapper_rv4k3_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_rv4k3_138 {
    margin-bottom: 16px
}

._spaceAfter--small_rv4k3_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_rv4k3_144 {
    margin-bottom: 30px
}

._desktopMenu_rv4k3_165 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_rv4k3_165 {
        display: flex;
        margin-inline-start: 10px
    }
}

._navBar_rv4k3_175 {
    display: flex;
    align-items: center;
    min-height: 60px;
    padding: 19px 15px
}

._navMenu_rv4k3_182 {
    display: flex;
    flex-wrap: wrap
}

@media (min-width: 768px) {
    ._navMenu_rv4k3_182 {
        display: flex
    }
}

._navMenu_rv4k3_182 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_rv4k3_182 li {
    padding: 0 0 0 16px
}

._navMenu_rv4k3_182 ._navLink_rv4k3_198 {
    opacity: .6;
    padding: 5px 10px;
    font-size: 16px
}

._navMenu_rv4k3_182 ._navLink_rv4k3_198:hover {
    opacity: 1
}

@media (min-width: 768px) {
    ._navBar_rv4k3_175 .dropdown-menu a._navLink_rv4k3_198 {
        color: #000000fa !important
    }
    ._navBar_rv4k3_175 .dropdown-menu a._navLink_rv4k3_198:hover {
        color: #000 !important
    }
}

._logo_rv4k3_215 {
    margin-inline-end: 20px
}

._mobileMenuContainer_rv4k3_219 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_rv4k3_219 {
        display: none
    }
}

._contactInfoItem_rv4k3_228 {
    display: none
}

@media (min-width: 992px) {
    ._contactInfoItem_rv4k3_228 {
        display: flex;
        align-items: center;
        font-weight: 500;
        margin-inline-end: 15px
    }
    ._contactInfoItem_rv4k3_228 a {
        display: flex;
        align-items: center
    }
}

._contactInfoItem_rv4k3_228 i {
    padding: 0 5px;
    opacity: .8
}

._contactLink_rv4k3_248 {
    padding: 0 !important;
    opacity: .6;
    color: var(--navLinksColor) !important
}

._contactLink_rv4k3_248:hover {
    opacity: 1
}

._contactLink_rv4k3_248 a:link,
._contactLink_rv4k3_248 a:visited,
._contactLink_rv4k3_248 a:hover,
._contactLink_rv4k3_248 a:active {
    color: var(--navLinksColor) !important
}

._mobileMenu_rv4k3_219 {
    background-image: linear-gradient(126deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._burger_rv4k3_267 {
    color: var(--navLinksColor)
}

._buttonLink_rv4k3_271 {
    padding: 10px 5px !important;
    border-width: 1px !important
}

._languageSwitcher_rv4k3_276 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_rv4k3_276 {
        display: flex
    }
}

._buttonsWrapper_14vf0_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_14vf0_138 {
    margin-bottom: 16px
}

._spaceAfter--small_14vf0_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_14vf0_144 {
    margin-bottom: 30px
}

._logoImg_14vf0_165 {
    margin-inline-end: 20px
}

._logoImg_14vf0_165 img {
    height: 40px
}

._desktopMenu_14vf0_172 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_14vf0_172 {
        display: flex
    }
}

._navBar_14vf0_181 {
    display: flex;
    align-items: center;
    min-height: 60px;
    background: #fff
}

._navMenu_14vf0_188 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_14vf0_188 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_14vf0_188 li {
    padding: 0 0 0 16px
}

._navMenu_14vf0_188 ._navLink_14vf0_199 {
    color: #0009;
    font-weight: 600
}

._navMenu_14vf0_188 ._navLink_14vf0_199:hover {
    color: #000
}

._languageSwitcher_14vf0_207 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_14vf0_207 {
        display: flex
    }
}

._mobileMenuContainer_14vf0_216 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_14vf0_216 {
        display: none
    }
}

._burger_14vf0_225 {
    line-height: 0;
    color: var(--navLinksColor)
}

._buttonsWrapper_1j35q_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1j35q_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1j35q_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1j35q_144 {
    margin-bottom: 30px
}

._logoImg_1j35q_165 img {
    height: 40px
}

._desktopMenu_1j35q_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1j35q_169 {
        display: flex
    }
}

._languageSwitcher_1j35q_178 {
    display: none;
    justify-content: flex-end
}

@media (min-width: 992px) {
    ._languageSwitcher_1j35q_178 {
        display: flex
    }
}

._mobileMenuContainer_1j35q_188 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1j35q_188 {
        display: none
    }
}

._navBar_1j35q_197 {
    display: flex;
    align-items: center;
    min-height: 60px;
    padding: 0 20px;
    background: #fff
}

._burger_1j35q_205 {
    line-height: 0;
    color: var(--navLinksColor)
}

._navMenu_1j35q_210 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_1j35q_210 li {
    padding: 8px
}

._navMenu_1j35q_210 ._navLink_1j35q_217 {
    color: #000;
    opacity: .6;
    font-weight: 600
}

._navMenu_1j35q_210 ._navLink_1j35q_217:hover {
    color: #000;
    opacity: 1
}

@media (min-width: 768px) {
    ._navMenu_1j35q_210 {
        padding: 20px 0 0 !important
    }
}

._buttonsWrapper_1vy7k_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1vy7k_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1vy7k_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1vy7k_144 {
    margin-bottom: 30px
}

._desktopMenu_1vy7k_165 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1vy7k_165 {
        display: flex
    }
}

._navBar_1vy7k_174 {
    display: flex;
    align-items: center;
    min-height: 60px;
    padding: 20px
}

._logoImg_1vy7k_181 {
    margin-inline-end: 20px
}

._navMenu_1vy7k_185 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_1vy7k_185 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_1vy7k_185 li {
    padding: 0 0 0 16px
}

._navMenu_1vy7k_185 ._navLink_1vy7k_196 {
    color: #fff;
    opacity: .6;
    padding: 5px 10px
}

._navMenu_1vy7k_185 ._navLink_1vy7k_196:hover {
    color: #fff;
    opacity: 1
}

@media (min-width: 768px) {
    .dropdown-menu a._navLink_1vy7k_196 {
        color: #000000fa !important
    }
    .dropdown-menu a._navLink_1vy7k_196:hover {
        color: #000 !important
    }
}

._mobileMenu_1vy7k_214 {
    background-image: linear-gradient(126deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._languageSwitcher_1vy7k_218 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_1vy7k_218 {
        display: flex
    }
}

._mobileMenuContainer_1vy7k_227 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1vy7k_227 {
        display: none
    }
}

._burger_1vy7k_236 {
    color: var(--navLinksColor)
}

._contactInfoItem_1vy7k_240 {
    display: none
}

@media (min-width: 992px) {
    ._contactInfoItem_1vy7k_240 {
        display: flex;
        align-items: center;
        font-weight: 500;
        margin-inline-end: 15px
    }
}

._contactInfoItem_1vy7k_240 i {
    padding-inline-end: 8px;
    opacity: .6
}

._contactLink_1vy7k_256 {
    padding: 0;
    opacity: .6;
    color: var(--navLinksColor)
}

._contactLink_1vy7k_256:hover {
    opacity: 1
}

._contactLink_1vy7k_256 a:link,
._contactLink_1vy7k_256 a:visited,
._contactLink_1vy7k_256 a:hover,
._contactLink_1vy7k_256 a:active {
    color: var(--navLinksColor)
}

._buttonsWrapper_1akv1_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1akv1_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1akv1_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1akv1_144 {
    margin-bottom: 30px
}

._burger_1akv1_165 {
    color: var(--navLinksColor)
}

._logoImg_1akv1_169 {
    margin-inline-end: 20px
}

._desktopMenu_1akv1_173 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1akv1_173 {
        display: flex
    }
}

._navBar_1akv1_182 {
    display: flex;
    align-items: center;
    padding: 20px
}

._navMenu_1akv1_188 {
    display: flex;
    flex-wrap: wrap
}

@media (min-width: 768px) {
    ._navMenu_1akv1_188 {
        display: flex
    }
}

._navMenu_1akv1_188 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_1akv1_188 li {
    padding: 0 0 0 16px
}

._navMenu_1akv1_188 ._navLink_1akv1_204 {
    color: #fff;
    padding: 5px 10px
}

._navMenu_1akv1_188 ._navLink_1akv1_204:hover {
    color: #fff;
    opacity: 1
}

@media (min-width: 768px) {
    .dropdown-menu a._navLink_1akv1_204 {
        color: #000000fa !important
    }
    .dropdown-menu a._navLink_1akv1_204:hover {
        color: #000 !important
    }
}

._mobileMenu_1akv1_221 {
    background-image: linear-gradient(126deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._languageSwitcher_1akv1_225 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_1akv1_225 {
        display: flex
    }
}

._mobileMenuContainer_1akv1_234 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1akv1_234 {
        display: none
    }
}

._contactInfoItem_1akv1_243 {
    display: none
}

@media (min-width: 992px) {
    ._contactInfoItem_1akv1_243 {
        display: flex;
        align-items: center;
        font-weight: 500;
        margin-inline-end: 15px
    }
}

._contactInfoItem_1akv1_243 i {
    padding-inline-end: 5px
}

._contactLink_1akv1_258 {
    padding: 0;
    color: var(--navLinksColor)
}

._contactLink_1akv1_258 a:link,
._contactLink_1akv1_258 a:visited,
._contactLink_1akv1_258 a:hover,
._contactLink_1akv1_258 a:active {
    color: var(--navLinksColor)
}

._buttonsWrapper_1n9gc_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1n9gc_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1n9gc_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1n9gc_144 {
    margin-bottom: 30px
}

._topBarContainer_1n9gc_165 {
    padding: 30px 0
}

._desktopMenu_1n9gc_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1n9gc_169 {
        display: flex
    }
}

._navBar_1n9gc_178 {
    width: 92%;
    background-color: #fff;
    padding: 10px 20px;
    border-radius: 3px;
    box-shadow: 0 0 8px #11161a29, 0 4px 8px #11161a14, 0 8px 16px #11161a14
}

@media (min-width: 768px) {
    ._navBar_1n9gc_178 {
        width: 100%
    }
}

._navBarRow_1n9gc_191 {
    min-height: 60px
}

._languageSwitcher_1n9gc_195 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_1n9gc_195 {
        display: flex
    }
}

._mobileMenuContainer_1n9gc_204 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1n9gc_204 {
        display: none
    }
}

._contactInfoItem_1n9gc_213 {
    display: none
}

@media (min-width: 992px) {
    ._contactInfoItem_1n9gc_213 {
        display: flex;
        align-items: center;
        font-weight: 500;
        margin-inline-end: 15px
    }
    ._contactInfoItem_1n9gc_213 a {
        display: flex;
        align-items: center
    }
}

._contactInfoItem_1n9gc_213 i {
    padding-inline-end: 5px;
    opacity: .6
}

._contactLink_1n9gc_233 {
    padding: 0 !important;
    opacity: .6;
    color: var(--navLinksColor) !important
}

._contactLink_1n9gc_233:hover {
    opacity: 1
}

._contactLink_1n9gc_233 a:link,
._contactLink_1n9gc_233 a:visited,
._contactLink_1n9gc_233 a:hover,
._contactLink_1n9gc_233 a:active {
    color: var(--navLinksColor) !important
}

._logoImg_1n9gc_248 {
    margin-inline-end: 10px
}

._navbar_1n9gc_252 {
    padding: 0
}

._navMenu_1n9gc_256 {
    display: flex;
    flex-wrap: wrap
}

@media (min-width: 768px) {
    ._navMenu_1n9gc_256 {
        display: flex
    }
}

._navMenu_1n9gc_256 li {
    padding: 0 16px 0 0
}

.rtl ._navMenu_1n9gc_256 li {
    padding: 0 0 0 16px
}

._navMenu_1n9gc_256 ._navLink_1n9gc_272 {
    color: #000;
    opacity: .6;
    font-weight: 600;
    padding: 5px 10px
}

._navMenu_1n9gc_256 ._navLink_1n9gc_272:hover {
    color: #000;
    opacity: 1
}

._buttonLink_1n9gc_283 {
    padding: 10px 5px !important;
    border-width: 1px !important
}

._burger_1n9gc_288 {
    color: var(--navLinksColor)
}

._buttonsWrapper_pt0yc_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_pt0yc_138 {
    margin-bottom: 16px
}

._spaceAfter--small_pt0yc_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_pt0yc_144 {
    margin-bottom: 30px
}

._contactInfoLink_pt0yc_165 {
    color: #fff9;
    padding: 0;
    display: inline-block
}

._contactInfoLink_pt0yc_165:hover {
    color: #fff
}

._burger_pt0yc_174 {
    color: #fff !important
}

._logoCol_pt0yc_178 {
    padding: 0
}

._logoImg_pt0yc_182 img {
    height: 40px
}

._phone_pt0yc_186 {
    margin: 0 5px
}

._contactInfo_pt0yc_165 {
    display: flex;
    padding: 0 14px
}

._contactInfoItem_pt0yc_195 {
    display: flex;
    align-items: center;
    color: #fff9;
    text-align: center;
    margin: 0 5px
}

._contactInfoIcon_pt0yc_203 {
    margin: 5px
}

._navBar_pt0yc_207 {
    position: relative;
    z-index: 999;
    display: flex;
    align-items: center;
    width: 90%;
    min-height: 94px;
    margin: 0 auto
}

@media (min-width: 768px) {
    ._navBar_pt0yc_207 {
        width: 100%
    }
}

._navBar_pt0yc_207 .emptyRowLarge {
    display: none
}

._navLink_pt0yc_225,
._navLink_pt0yc_225:hover {
    color: #fff
}

._buttonsWrapper_2gsam_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_2gsam_138 {
    margin-bottom: 16px
}

._spaceAfter--small_2gsam_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_2gsam_144 {
    margin-bottom: 30px
}

._siteBar_2gsam_165 {
    background-color: #fff
}

._desktopMenu_2gsam_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_2gsam_169 {
        display: flex
    }
}

._navMenu_2gsam_178 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_2gsam_178 li {
    padding: 0
}

._navLink_2gsam_186 {
    color: #1c1c1c;
    font-size: 15px;
    font-weight: 600;
    padding: 5px 10px
}

._logoImg_2gsam_193 {
    margin-inline-end: 10px
}

._languageSwitcher_2gsam_197 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_2gsam_197 {
        display: flex
    }
}

._mobileMenuContainer_2gsam_206 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_2gsam_206 {
        display: none
    }
}

._burger_2gsam_215 {
    color: var(--navLinksColor)
}

._buttonsWrapper_1alsj_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1alsj_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1alsj_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1alsj_144 {
    margin-bottom: 30px
}

._siteBar_1alsj_165 {
    z-index: 1000;
    width: 100%;
    left: 0
}

._desktopMenu_1alsj_171 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1alsj_171 {
        display: flex
    }
}

._navMenu_1alsj_180 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_1alsj_180 li {
    padding: 0
}

._navLink_1alsj_188 {
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    padding: 5px 10px
}

._logoImg_1alsj_195 {
    margin-inline-end: 15px
}

._languageSwitcher_1alsj_199 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_1alsj_199 {
        display: flex
    }
}

._mobileMenuContainer_1alsj_208 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1alsj_208 {
        display: none
    }
}

._socialLinksContainer_1alsj_217 {
    display: none
}

@media (min-width: 992px) {
    ._socialLinksContainer_1alsj_217 {
        display: flex;
        justify-content: flex-end
    }
}

._social_1alsj_217 {
    list-style: none;
    margin: 0;
    padding: 0 !important;
    display: flex
}

._socialItem_1alsj_233 a {
    color: #fff;
    margin-right: 5px
}

._socialItem_1alsj_233 a i {
    font-size: 24px
}

._burger_1alsj_241 {
    color: var(--navLinksColor)
}

._buttonsWrapper_1o7hw_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1o7hw_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1o7hw_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1o7hw_144 {
    margin-bottom: 30px
}

._siteBar_1o7hw_165 {
    background: white
}

._desktopMenu_1o7hw_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1o7hw_169 {
        display: flex
    }
}

._navMenu_1o7hw_178 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_1o7hw_178 li {
    padding: 0
}

._navLink_1o7hw_186 {
    color: #1c1c1c;
    font-size: 15px;
    font-weight: 600;
    padding: 5px 10px
}

._logoImg_1o7hw_193 {
    margin-inline-end: 15px
}

._languageSwitcher_1o7hw_197 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_1o7hw_197 {
        display: flex
    }
}

._mobileMenuContainer_1o7hw_206 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1o7hw_206 {
        display: none
    }
}

._burger_1o7hw_215 {
    color: var(--navLinksColor)
}

._buttonsWrapper_w0nn2_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_w0nn2_138 {
    margin-bottom: 16px
}

._spaceAfter--small_w0nn2_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_w0nn2_144 {
    margin-bottom: 30px
}

._globalHeader_w0nn2_165 {
    background: #ffffff;
    padding: 16px 0;
    border-bottom: 1px solid rgba(0, 0, 0, .7)
}

._navbarBrand_w0nn2_171 {
    margin-inline-end: 10px
}

._desktopMenu_w0nn2_175 {
    width: 100%;
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_w0nn2_175 {
        display: flex;
        justify-content: center;
        align-items: center
    }
}

._navMenu_w0nn2_187 {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 0
}

._navMenu_w0nn2_187 .nav-item {
    padding: 0
}

._navLink_w0nn2_198 {
    color: #000000b3
}

._navLink_w0nn2_198:hover {
    color: #000
}

._mobileMenuContainer_w0nn2_205 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_w0nn2_205 {
        display: none
    }
}

._socialLinksContainer_w0nn2_214 {
    display: none
}

@media (min-width: 992px) {
    ._socialLinksContainer_w0nn2_214 {
        display: flex;
        justify-content: center;
        margin: 0 10px
    }
}

._socialLinksList_w0nn2_225 {
    list-style: none;
    margin: 0;
    padding: 0 !important;
    display: flex
}

._socialLinksListItem_w0nn2_231 a {
    color: var(--theme-color-brand1);
    margin-right: 5px
}

._socialLinksListItem_w0nn2_231 a i {
    font-size: 24px
}

._languageSwitcher_w0nn2_239 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_w0nn2_239 {
        display: flex
    }
}

._burger_w0nn2_248 {
    color: var(--navLinksColor)
}

._buttonsWrapper_110ge_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_110ge_138 {
    margin-bottom: 16px
}

._spaceAfter--small_110ge_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_110ge_144 {
    margin-bottom: 30px
}

._header_110ge_165 {
    padding: 24px 0
}

._desktopMenu_110ge_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_110ge_169 {
        display: flex;
        margin-inline-start: 10px
    }
}

._mobileMenuContainer_110ge_179 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_110ge_179 {
        display: none
    }
}

._burger_110ge_188 {
    color: var(--navLinksColor)
}

._logoImg_110ge_192 {
    margin-inline-end: 20px
}

._logoImg_110ge_192 img {
    height: 40px
}

._navMenu_110ge_199 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_110ge_199 .nav-item {
    padding: 0
}

._navLink_110ge_207 {
    font-size: 16px;
    padding: 5px 10px
}

._navLink_110ge_207:hover {
    color: #fff
}

._languageSwitcher_110ge_215 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_110ge_215 {
        display: flex
    }
}

._buttonsWrapper_lvmzc_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_lvmzc_138 {
    margin-bottom: 16px
}

._spaceAfter--small_lvmzc_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_lvmzc_144 {
    margin-bottom: 30px
}

._header_lvmzc_165 {
    padding: 24px 0
}

._desktopMenu_lvmzc_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_lvmzc_169 {
        display: flex;
        margin-inline-start: 10px
    }
}

._mobileMenuContainer_lvmzc_179 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_lvmzc_179 {
        display: none
    }
}

._burger_lvmzc_188 {
    color: var(--navLinksColor)
}

._logoImg_lvmzc_192 {
    margin-inline-end: 20px
}

._logoImg_lvmzc_192 img {
    height: 40px
}

._navMenu_lvmzc_199 {
    display: flex;
    flex-wrap: wrap;
    justify-content: center
}

._navMenu_lvmzc_199 .nav-item {
    padding: 0
}

._navLink_lvmzc_208 {
    font-size: 16px;
    padding: 5px 10px
}

._navLink_lvmzc_208:hover {
    color: #fff
}

._socialLinksContainer_lvmzc_216 {
    display: none
}

@media (min-width: 992px) {
    ._socialLinksContainer_lvmzc_216 {
        display: flex;
        justify-content: flex-end;
        align-items: center
    }
}

._socialLinksContainer_lvmzc_216 ul {
    margin-inline: 0px !important
}

._socialIcons_lvmzc_230 {
    display: flex;
    padding: 0;
    margin-bottom: 0;
    justify-content: flex-end
}

._socialIcon_lvmzc_230 {
    display: flex;
    margin: 0 0 0 10px
}

.rtl ._socialIcon_lvmzc_230 {
    margin: 0 10px 0 0
}

._languageSwitcher_lvmzc_245 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_lvmzc_245 {
        display: flex
    }
}

._buttonsWrapper_1abi8_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1abi8_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1abi8_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1abi8_144 {
    margin-bottom: 30px
}

._header_1abi8_165 {
    padding: 24px 0
}

._desktopMenu_1abi8_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1abi8_169 {
        display: flex;
        margin-inline-start: 10px
    }
}

._mobileMenuContainer_1abi8_179 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1abi8_179 {
        display: none
    }
}

._burger_1abi8_188 {
    color: var(--navLinksColor)
}

._logoImg_1abi8_192 {
    margin-inline-end: 20px
}

._logoImg_1abi8_192 img {
    height: 40px
}

._navMenu_1abi8_199 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_1abi8_199 .nav-item {
    padding: 0
}

._navLink_1abi8_207 {
    font-size: 16px;
    padding: 5px 10px
}

._navLink_1abi8_207:hover {
    color: #fff
}

._secondaryButton_1abi8_215 {
    color: #1c1c1c;
    text-align: center;
    font-weight: 600;
    padding-inline: 12px;
    font-size: 15px;
    border: 1px solid #dbdbdb;
    border-radius: 6px;
    display: none;
    align-items: center;
    justify-content: center;
    min-height: 38px;
    min-width: -moz-max-content;
    min-width: max-content;
    box-shadow: 0 1px 3px #0000000d, 0 2px 1px #00000008, 0 1px 1px #0000000a
}

._secondaryButton_1abi8_215 span div {
    padding: 0 !important
}

@media (min-width: 992px) {
    ._secondaryButton_1abi8_215 {
        display: flex
    }
}

._buttonLink_1abi8_239 {
    padding: 10px 16px !important;
    border-width: 1px !important
}

._languageSwitcher_1abi8_244 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_1abi8_244 {
        display: flex
    }
}

._buttonsContainer_1abi8_253 {
    display: none
}

@media (min-width: 992px) {
    ._buttonsContainer_1abi8_253 {
        display: flex
    }
}

._buttonsWrapper_12ybt_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_12ybt_138 {
    margin-bottom: 16px
}

._spaceAfter--small_12ybt_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_12ybt_144 {
    margin-bottom: 30px
}

._header_12ybt_165 {
    padding: 24px 0
}

._desktopMenu_12ybt_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_12ybt_169 {
        display: flex;
        margin-inline-start: 10px
    }
}

._mobileMenuContainer_12ybt_179 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_12ybt_179 {
        display: none
    }
}

._burger_12ybt_188 {
    color: var(--navLinksColor)
}

._logoImg_12ybt_192 {
    margin-inline-end: 20px
}

._logoImg_12ybt_192 img {
    height: 40px
}

._navMenu_12ybt_199 {
    display: flex;
    flex-wrap: wrap;
    justify-content: center
}

._navMenu_12ybt_199 .nav-item {
    padding: 0
}

._navLink_12ybt_208 {
    font-size: 16px;
    padding: 5px 10px
}

._navLink_12ybt_208:hover {
    color: #fff
}

._secondaryButton_12ybt_216 {
    color: #1c1c1c;
    text-align: center;
    font-weight: 600;
    padding-inline: 12px;
    font-size: 15px;
    border: 1px solid #dbdbdb;
    border-radius: 6px;
    display: none;
    align-items: center;
    justify-content: center;
    min-height: 38px;
    min-width: -moz-max-content;
    min-width: max-content;
    box-shadow: 0 1px 3px #0000000d, 0 2px 1px #00000008, 0 1px 1px #0000000a
}

._secondaryButton_12ybt_216 span div {
    padding: 0 !important
}

@media (min-width: 992px) {
    ._secondaryButton_12ybt_216 {
        display: flex
    }
}

._buttonLink_12ybt_240 {
    padding: 10px 16px !important;
    border-width: 1px !important
}

._languageSwitcher_12ybt_245 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_12ybt_245 {
        display: flex
    }
}

._buttonsContainer_12ybt_254 {
    display: none
}

@media (min-width: 992px) {
    ._buttonsContainer_12ybt_254 {
        display: flex;
        justify-content: flex-end
    }
}

._buttonsWrapper_1ss1p_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ss1p_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ss1p_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ss1p_144 {
    margin-bottom: 30px
}

._header_1ss1p_165 {
    padding: 24px 0
}

._desktopMenu_1ss1p_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_1ss1p_169 {
        display: flex;
        margin-inline-start: 10px
    }
}

._navMenu_1ss1p_179 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_1ss1p_179 .nav-item {
    padding: 0
}

._buttonLink_1ss1p_187 {
    padding: 10px 16px !important;
    border-width: 1px !important
}

._navLink_1ss1p_192 {
    padding: 5px 10px;
    font-size: 16px
}

._navLink_1ss1p_192:hover {
    color: #fff
}

._mobileMenuContainer_1ss1p_200 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_1ss1p_200 {
        display: none
    }
}

._buttonsContainer_1ss1p_209 {
    display: none
}

@media (min-width: 992px) {
    ._buttonsContainer_1ss1p_209 {
        display: flex;
        gap: 10px
    }
}

._secondaryButton_1ss1p_219 {
    margin-inline-end: 10px !important
}

._burger_1ss1p_223 {
    color: var(--navLinksColor)
}

._logoImg_1ss1p_227 {
    margin-inline-end: 20px
}

._logoImg_1ss1p_227 img {
    height: 40px
}

._languageSwitcher_1ss1p_234 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_1ss1p_234 {
        display: flex
    }
}

._buttonsWrapper_5dpri_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_5dpri_138 {
    margin-bottom: 16px
}

._spaceAfter--small_5dpri_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_5dpri_144 {
    margin-bottom: 30px
}

._header_5dpri_165 {
    padding: 24px 0
}

._desktopMenu_5dpri_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_5dpri_169 {
        display: flex;
        margin-inline-end: 30px
    }
}

._navMenu_5dpri_179 {
    display: flex;
    flex-wrap: wrap;
    margin-inline-end: auto
}

._navMenu_5dpri_179 .nav-item {
    padding: 0
}

._navLink_5dpri_188 {
    padding: 5px 10px;
    font-size: 16px
}

._navLink_5dpri_188:hover {
    color: #fff
}

._mobileMenuContainer_5dpri_196 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_5dpri_196 {
        display: none
    }
}

._buttonsContainer_5dpri_205 {
    display: none
}

@media (min-width: 992px) {
    ._buttonsContainer_5dpri_205 {
        display: flex
    }
}

._secondaryButton_5dpri_214 {
    color: #1c1c1c;
    text-align: center;
    font-weight: 600;
    padding-inline: 12px;
    font-size: 15px;
    border: 1px solid #dbdbdb;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 38px;
    min-width: -moz-max-content;
    min-width: max-content;
    box-shadow: 0 1px 3px #0000000d, 0 2px 1px #00000008, 0 1px 1px #0000000a
}

._secondaryButton_5dpri_214 span div {
    padding: 0 !important
}

._burger_5dpri_233 {
    color: var(--navLinksColor)
}

._logoImg_5dpri_237 {
    margin-inline-end: 20px
}

._logoImg_5dpri_237 img {
    height: 40px
}

._buttonLink_5dpri_244 {
    padding: 10px 16px !important;
    border-width: 1px !important
}

._languageSwitcher_5dpri_249 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_5dpri_249 {
        display: flex
    }
}

._buttonsWrapper_q8n_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_q8n_138 {
    margin-bottom: 16px
}

._spaceAfter--small_q8n_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_q8n_144 {
    margin-bottom: 30px
}

._header_q8n_165 {
    padding: 24px 0
}

._desktopMenu_q8n_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_q8n_169 {
        display: flex;
        margin-inline-end: 30px
    }
}

._navMenu_q8n_179 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_q8n_179 .nav-item {
    padding: 0
}

._navLink_q8n_187 {
    padding: 5px 10px;
    font-size: 16px
}

._navLink_q8n_187:hover {
    color: #fff
}

._mobileMenuContainer_q8n_195 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_q8n_195 {
        display: none
    }
}

._buttonsContainer_q8n_204 {
    display: none
}

@media (min-width: 992px) {
    ._buttonsContainer_q8n_204 {
        display: flex
    }
}

._primaryButton_q8n_213 {
    color: #fff;
    background-color: var(--theme-color-brand1);
    font-weight: 600;
    font-size: 15px;
    text-align: center;
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 1px 3px #0000000d, 0 2px 1px #00000008, 0 1px 1px #0000000a
}

._primaryButton_q8n_213 span div {
    padding: 0 !important
}

._burger_q8n_227 {
    color: var(--navLinksColor)
}

._logoImg_q8n_231 {
    margin-inline-end: 20px
}

._logoImg_q8n_231 img {
    height: 40px
}

._buttonLink_q8n_238 {
    padding: 10px 16px !important;
    border-width: 1px !important
}

._languageSwitcher_q8n_243 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_q8n_243 {
        display: flex
    }
}

._buttonsWrapper_43a8n_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_43a8n_138 {
    margin-bottom: 16px
}

._spaceAfter--small_43a8n_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_43a8n_144 {
    margin-bottom: 30px
}

._header_43a8n_165 {
    padding: 24px 0
}

._desktopMenu_43a8n_169 {
    display: none
}

@media (min-width: 992px) {
    ._desktopMenu_43a8n_169 {
        display: flex;
        margin-inline-start: 10px
    }
}

._navMenu_43a8n_179 {
    display: flex;
    flex-wrap: wrap
}

._navMenu_43a8n_179 .nav-item {
    padding: 0
}

._navLink_43a8n_187 {
    padding: 5px 10px;
    font-size: 16px
}

._navLink_43a8n_187:hover {
    color: #fff
}

._mobileMenuContainer_43a8n_195 {
    display: flex
}

@media (min-width: 992px) {
    ._mobileMenuContainer_43a8n_195 {
        display: none
    }
}

._buttonsContainer_43a8n_204 {
    display: none
}

@media (min-width: 992px) {
    ._buttonsContainer_43a8n_204 {
        display: flex;
        gap: 10px
    }
}

._secondaryButton_43a8n_214 {
    margin-inline-end: 10px !important
}

._burger_43a8n_218 {
    color: var(--navLinksColor)
}

._logoImg_43a8n_222 {
    margin-inline-end: 20px
}

._logoImg_43a8n_222 img {
    height: 40px
}

._buttonLink_43a8n_229 {
    padding: 10px 16px !important;
    border-width: 1px !important
}

._languageSwitcher_43a8n_234 {
    display: none
}

@media (min-width: 992px) {
    ._languageSwitcher_43a8n_234 {
        display: flex
    }
}

._buttonsWrapper_1n96j_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1n96j_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1n96j_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1n96j_144 {
    margin-bottom: 30px
}

._hero_1n96j_165 {
    --text-color: #fff;
    display: flex;
    flex-direction: column;
    min-height: 600px;
    position: relative;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1))
}

@media (min-width: 480px) {
    ._heroContainer_1n96j_174 {
        padding: 70px 0
    }
}

._socialLinksContainer_1n96j_183 {
    display: flex;
    justify-content: center;
    align-items: baseline;
    margin: 30px 0 0
}

@media (min-width: 768px) {
    ._socialLinksContainer_1n96j_183 {
        margin: 30px 0 0;
        justify-content: flex-start
    }
}

._socialIcons_1n96j_196 {
    display: flex;
    padding: 0;
    margin-bottom: 0;
    justify-content: center;
    align-items: center;
    font-size: 24px
}

._socialIcons_1n96j_196 ._socialIcon_1n96j_196 {
    display: flex;
    margin: 0 28px 0 0;
    align-items: center
}

.rtl ._socialIcons_1n96j_196 ._socialIcon_1n96j_196 {
    margin: 0 0 0 28px
}

._socialIcons_1n96j_196 ._socialIcon_1n96j_196:last-child {
    margin: 0
}

._socialIcons_1n96j_196 ._socialIcon_1n96j_196 a,
._socialIcons_1n96j_196 ._socialIcon_1n96j_196 i,
._socialIcons_1n96j_196 ._socialIcon_1n96j_196 svg {
    color: #fafbfc;
    opacity: .7;
    text-decoration: none !important
}

._heroContent_1n96j_223 {
    margin-top: 62px
}

@media (min-width: 768px) {
    ._heroContent_1n96j_223 {
        margin-top: 70px
    }
    ._heroContent_1n96j_223 p {
        width: 100%
    }
}

._imageColumn_1n96j_235 {
    width: 100%;
    display: block;
    justify-content: flex-end;
    align-items: center
}

@media (min-width: 768px) {
    ._imageColumn_1n96j_235 {
        display: flex
    }
}

._imageContainer_1n96j_247 {
    width: 100%;
    box-shadow: 0 8px 40px #0003;
    display: block;
    margin-top: 25px
}

._imageContainer_1n96j_247 img {
    aspect-ratio: 4/3
}

@media (min-width: 768px) {
    ._imageContainer_1n96j_247 {
        margin: 0
    }
}

._buttonsWrapper_13hjz_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_13hjz_138 {
    margin-bottom: 16px
}

._spaceAfter--small_13hjz_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_13hjz_144 {
    margin-bottom: 30px
}

._hero_13hjz_165 {
    --text-color: #fff;
    position: relative;
    display: flex;
    flex-direction: column;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1))
}

@media (min-width: 768px) {
    ._hero_13hjz_165 {
        padding-top: 5px
    }
}

._heroContent_13hjz_178 {
    width: 100%;
    padding: 80px 0 100px;
    margin: auto;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 620px;
    z-index: 3;
    text-align: center
}

._heroContent_13hjz_178 h1 {
    margin: 0 auto 21px
}

._heroContent_13hjz_178 p {
    margin: 0 auto;
    text-align: center
}

@media (min-width: 1200px) {
    ._heroContent_13hjz_178 {
        width: 80%
    }
}

@media (min-width: 768px) {
    ._heroContent_13hjz_178 {
        width: 90%
    }
}

._socialLinksColumn_13hjz_209 {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px
}

._socialIcons_13hjz_216 {
    display: flex;
    justify-content: center;
    padding: 0;
    margin-bottom: 0;
    font-size: 24px
}

._socialIcon_13hjz_216 {
    display: flex;
    font-size: 30px
}

._socialIcon_13hjz_216 a,
._socialIcon_13hjz_216 i,
._socialIcon_13hjz_216 svg {
    color: #fff;
    text-decoration: none !important
}

@media (min-width: 1200px) {
    ._socialIcon_13hjz_216 {
        font-size: inherit
    }
}

._buttonsWrapper_157er_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_157er_138 {
    margin-bottom: 16px
}

._spaceAfter--small_157er_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_157er_144 {
    margin-bottom: 30px
}

._hero_157er_165 {
    align-items: center
}

._socialLinksColumn_157er_169 {
    display: flex;
    align-items: center;
    justify-content: flex-end
}

._actionCol_157er_175 {
    justify-content: flex-end
}

._content_157er_179 {
    width: 94%;
    margin: 0 auto;
    padding-top: 20px
}

@media (min-width: 768px) {
    ._content_157er_179 {
        padding-top: 40px;
        width: 100%
    }
}

._rowStyle_157er_191 {
    padding-top: 20px;
    padding-bottom: 30px
}

@media (min-width: 768px) {
    ._rowStyle_157er_191 {
        padding-bottom: 60px
    }
}

._imageContainer_157er_201 {
    position: relative
}

._imageContainer_157er_201 img {
    aspect-ratio: 3/1;
    max-height: unset !important
}

._overlay_157er_209 {
    position: absolute;
    inset: 0;
    background-color: #21252999;
    z-index: 2
}

._heroContent_157er_219 h1,
._heroContent_157er_219 p {
    margin-bottom: 20px
}

@media (min-width: 768px) {
    ._heroContent_157er_219 p {
        width: 68%
    }
}

._btnColumn_157er_231 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start
}

@media (min-width: 1200px) {
    ._btnColumn_157er_231 {
        display: flex;
        flex-direction: row
    }
}

._buttonsWrapper_198t4_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_198t4_138 {
    margin-bottom: 16px
}

._spaceAfter--small_198t4_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_198t4_144 {
    margin-bottom: 30px
}

._hero_198t4_165 a {
    text-decoration: none !important
}

._overlay_198t4_169 {
    background: rgba(0, 0, 0, .6)
}

._heroContentContainer_198t4_173 {
    padding: 14px 0
}

@media (min-width: 768px) {
    ._heroContentContainer_198t4_173 {
        padding: 100px 60px
    }
}

._heroContent_198t4_173 {
    --text-color: #000;
    background-color: #fff;
    margin: 80px auto;
    padding: 40px 15px 30px;
    width: 95%;
    text-align: center
}

@media (min-width: 768px) {
    ._heroContent_198t4_173 {
        width: 80%;
        margin: 0 10px;
        min-height: 332px;
        padding: 50px 35px 30px;
        text-align: start;
        display: flex;
        flex-direction: column;
        width: 400px;
        color: #000;
        background-color: #fff
    }
}

._buttonsWrapper_v8if2_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_v8if2_138 {
    margin-bottom: 16px
}

._spaceAfter--small_v8if2_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_v8if2_144 {
    margin-bottom: 30px
}

._hero_v8if2_165 {
    min-height: 600px;
    position: relative;
    overflow: hidden
}

._heroContent_v8if2_171 {
    text-align: center;
    width: 100%;
    max-width: 800px;
    padding: 70px 30px;
    position: relative;
    z-index: 10
}

._heroContent_v8if2_171 h1 {
    margin-bottom: 18px
}

._heroContent_v8if2_171 p {
    max-width: 400px;
    margin: 0 auto 56px
}

._langSwitcher_v8if2_188 {
    margin: 0 0 0 auto
}

.rtl ._langSwitcher_v8if2_188 {
    margin: 0 auto 0 0
}

._block_v8if2_195 {
    position: absolute;
    border-radius: 32px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-image: linear-gradient(173deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    box-shadow: 0 8px 16px #00000026
}

._right_block_v8if2_203 {
    width: 482px;
    height: 482px;
    right: -420px
}

._block_right_back_v8if2_210 {
    transform: rotate(65deg);
    opacity: .4
}

._block_right_front_v8if2_216 {
    transform: rotate(45deg);
    opacity: .6
}

@media (min-width: 768px) {
    ._block_right_front_v8if2_216 {
        opacity: .9
    }
}

._block_bottom_v8if2_227 {
    width: 260px;
    height: 260px;
    opacity: .7;
    transform: rotate(45deg);
    bottom: -224px;
    left: 27px
}

._button_v8if2_132 {
    border-radius: 4px;
    text-align: center;
    min-width: 180px;
    margin: 10px;
    display: inline-block;
    padding: 12px
}

._buttonsWrapper_1j9ta_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1j9ta_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1j9ta_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1j9ta_144 {
    margin-bottom: 30px
}

._hero_1j9ta_165 {
    position: relative;
    align-items: center;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._bk_1j9ta_171 {
    background-image: url(https://assets.wuiltsite.com/assets/beta3a.svg);
    background-size: cover;
    width: 100%;
    position: absolute;
    top: 20%;
    height: 80%;
    left: 0;
    opacity: .8
}

._heroContent_1j9ta_182 {
    --text-color: #fff;
    position: relative;
    z-index: 3;
    width: 100%;
    padding: 70px 0 80px;
    margin: 0 auto;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%
}

._heroContent_1j9ta_182 h1 {
    margin: 0 auto 21px
}

@media (min-width: 768px) {
    ._heroContent_1j9ta_182 {
        width: 720px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100%;
        z-index: 3
    }
}

._buttonsContainer_1j9ta_209 {
    margin: 20px 0 0;
    display: flex;
    flex-direction: column;
    justify-content: center
}

@media (min-width: 768px) {
    ._buttonsContainer_1j9ta_209 {
        flex-direction: row
    }
}

._socialIcons_1j9ta_221 {
    display: flex;
    justify-content: center;
    padding: 0;
    margin-bottom: 0;
    font-size: 24px
}

._socialIcon_1j9ta_221 {
    display: flex;
    margin: 0 14px
}

._socialIcon_1j9ta_221 a,
._socialIcon_1j9ta_221 i,
._socialIcon_1j9ta_221 svg {
    opacity: .75;
    color: #fff;
    text-decoration: none !important
}

._buttonsWrapper_1ci05_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ci05_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ci05_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ci05_144 {
    margin-bottom: 30px
}

._containerImage_1ci05_165 {
    padding: 0 4%
}

@media (min-width: 768px) {
    ._containerImage_1ci05_165 {
        padding: 0
    }
}

._hero_1ci05_174 {
    --text-color: rgb(255, 255, 255);
    display: flex;
    min-height: 700px;
    position: relative;
    align-items: center;
    flex-direction: column;
    justify-content: center
}

._overlay_1ci05_184 {
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, .57));
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0
}

._heroContent_1ci05_197 {
    margin: 50px 0 0;
    width: 100%
}

._heroContent_1ci05_197 p {
    margin-top: 24px;
    margin-bottom: 25px
}

@media (min-width: 768px) {
    ._heroContent_1ci05_197 {
        text-align: center
    }
    ._heroContent_1ci05_197 p {
        margin-bottom: 25px
    }
}

@media (min-width: 992px) {
    ._heroContent_1ci05_197 {
        text-align: left
    }
    .rtl ._heroContent_1ci05_197 {
        text-align: right
    }
}

._ctaContentContainer_1ci05_222 {
    display: flex;
    align-items: center;
    height: 100%
}

._backgroundText_1ci05_228 {
    width: 100%;
    background-image: linear-gradient(98deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    min-height: 150px;
    border-radius: 4px;
    display: block;
    align-items: center;
    padding: 50px 20px;
    margin: 50px 0 70px
}

@media (min-width: 768px) {
    ._backgroundText_1ci05_228 {
        margin: 40px 0 70px;
        padding: 50px 30px;
        display: block
    }
}

._buttonsWrapper_zq0u4_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_zq0u4_138 {
    margin-bottom: 16px
}

._spaceAfter--small_zq0u4_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_zq0u4_144 {
    margin-bottom: 30px
}

._section_zq0u4_165 {
    background-color: var(--theme-color-brand1);
    overflow: hidden
}

._socialLinksColumn_zq0u4_170 {
    display: flex;
    justify-content: start
}

._socialIcons_zq0u4_175 {
    display: flex;
    justify-content: start;
    padding: 0;
    margin: 0;
    font-size: 24px
}

._socialIcon_zq0u4_175 {
    display: flex;
    margin-right: 20px
}

._socialIcon_zq0u4_175 a,
._socialIcon_zq0u4_175 i,
._socialIcon_zq0u4_175 svg {
    color: #ffffffb3;
    text-decoration: none !important
}

.rtl ._socialIcon_zq0u4_175 {
    margin-right: 0;
    margin-left: 20px
}

._hero_zq0u4_198 {
    position: relative;
    align-items: center;
    min-height: 500px;
    display: flex
}

._heroContentContainer_zq0u4_205 {
    z-index: 4;
    padding: 0;
    margin: 30px 0 0
}

._heroContent_zq0u4_205 {
    --text-color: #fff;
    margin: 0 auto
}

._backgroundCol_zq0u4_217 {
    position: absolute;
    top: 0;
    width: 50%;
    bottom: 0;
    right: 0
}

.rtl ._backgroundCol_zq0u4_217 {
    right: auto;
    left: 0
}

._containerImage_zq0u4_229 {
    width: 100%;
    height: 100%
}

._containerImage_zq0u4_229 ._imgWrapper_zq0u4_233,
._containerImage_zq0u4_229 span {
    height: 100%;
    width: 100%
}

._containerImage_zq0u4_229 img {
    display: flex;
    align-items: center;
    width: 100% !important;
    height: 100% !important;
    max-height: unset !important
}

._buttonsWrapper_x46za_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_x46za_138 {
    margin-bottom: 16px
}

._spaceAfter--small_x46za_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_x46za_144 {
    margin-bottom: 30px
}

._hero_x46za_165 {
    position: relative;
    min-height: 600px;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._bk_x46za_171 {
    width: 100%;
    height: 100%;
    pointer-events: none;
    position: absolute;
    top: 0;
    background: url(https://assets.wuiltsite.com/assets/mogacomedy2.svg) no-repeat;
    background-size: contain;
    background-position: top right
}

.rtl ._bk_x46za_171 {
    transform: scaleX(-1)
}

._heroContent_x46za_185 {
    --text-color: #fff;
    width: 95%;
    padding: 100px 0 60px;
    margin: auto;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    min-height: 100%;
    z-index: 3
}

._heroContent_x46za_185 h1 {
    text-align: start
}

@media (min-width: 768px) {
    ._heroContent_x46za_185 h1 {
        width: 70%
    }
}

._heroContent_x46za_185 p {
    text-align: start
}

@media (min-width: 768px) {
    ._heroContent_x46za_185 {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: center;
        min-height: 100%;
        z-index: 3
    }
}

._socialLinksColumn_x46za_218 {
    display: flex;
    align-items: center
}

._socialIcons_x46za_223 {
    display: flex;
    justify-content: center;
    padding: 0;
    margin-bottom: 0;
    font-size: 24px
}

._socialIcon_x46za_223 {
    display: flex;
    margin-right: 20px
}

._socialIcon_x46za_223 a,
._socialIcon_x46za_223 i,
._socialIcon_x46za_223 svg {
    opacity: .8;
    color: #fff;
    text-decoration: none !important
}

.rtl ._socialIcon_x46za_223 {
    margin-right: 0;
    margin-left: 20px
}

._buttonsWrapper_1ukrm_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ukrm_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ukrm_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ukrm_144 {
    margin-bottom: 30px
}

._socialLinksColumn_1ukrm_165 {
    display: flex;
    justify-content: center;
    padding-top: 8px
}

@media (min-width: 768px) {
    ._socialLinksColumn_1ukrm_165 {
        justify-content: flex-start
    }
}

._hero_1ukrm_176 {
    position: relative;
    align-items: center;
    min-height: 560px;
    display: flex
}

._heroContentContainer_1ukrm_183 {
    z-index: 4
}

._heroContent_1ukrm_183 {
    --text-color: #fff;
    padding: 70px 0;
    margin: 14px 0;
    text-align: center
}

@media (min-width: 768px) {
    ._heroContent_1ukrm_183 {
        text-align: start;
        width: 95%;
        color: #fff;
        margin: 110px 0 80px
    }
}

._containerImage_1ukrm_203 {
    position: relative
}

._containerImage_1ukrm_203 ._overlay_1ukrm_206 {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image: linear-gradient(to right, rgba(0, 0, 0, .9), rgba(0, 0, 0, .19))
}

._socialIcons_1ukrm_215 {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    font-size: 24px
}

._socialIcons_1ukrm_215 ._socialIcon_1ukrm_215 {
    display: flex;
    margin-right: 25px
}

._socialIcons_1ukrm_215 ._socialIcon_1ukrm_215 a,
._socialIcons_1ukrm_215 ._socialIcon_1ukrm_215 i,
._socialIcons_1ukrm_215 ._socialIcon_1ukrm_215 svg {
    opacity: .8;
    color: #fff;
    text-decoration: none !important
}

.rtl ._socialIcons_1ukrm_215 ._socialIcon_1ukrm_215 {
    margin-right: 0;
    margin-left: 25px
}

._socialIcons_1ukrm_215 ._socialIcon_1ukrm_215:last-child {
    margin: 0
}

._buttonsWrapper_1fq6e_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1fq6e_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1fq6e_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1fq6e_144 {
    margin-bottom: 30px
}

._socialLinksColumn_1fq6e_165 {
    display: flex;
    justify-content: center;
    padding-top: 8px
}

._socialIcons_1fq6e_171 {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-bottom: 0;
    font-size: 24px
}

._socialIcon_1fq6e_171 {
    display: flex;
    margin: 0 10px
}

._socialIcon_1fq6e_171 a,
._socialIcon_1fq6e_171 i {
    color: #767676;
    text-decoration: none !important
}

._hero_1fq6e_190 {
    --text-color: #fff;
    position: relative;
    align-items: center;
    min-height: 560px;
    display: flex
}

._heroContentContainer_1fq6e_198 {
    z-index: 4
}

._heroContent_1fq6e_198 {
    flex-direction: column;
    padding: 70px 0;
    margin: 14px 0;
    color: #fff;
    text-align: center
}

._heroContent_1fq6e_198 ._subTitle_1fq6e_210 {
    color: #fff;
    opacity: .7;
    max-width: 100%
}

._heroContent_1fq6e_198 p {
    margin: 0 auto;
    width: 100%
}

@media (min-width: 768px) {
    ._heroContent_1fq6e_198 {
        color: #fff;
        margin: 55px 0 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        padding-right: 0;
        padding-left: 0
    }
    ._heroContent_1fq6e_198 h1 {
        width: 100%
    }
    ._heroContent_1fq6e_198 p {
        width: 90%
    }
    ._heroContent_1fq6e_198 ._subTitle_1fq6e_210 {
        max-width: 50%
    }
}

._containerImage_1fq6e_241 {
    position: relative
}

._containerImage_1fq6e_241 ._overlay_1fq6e_244 {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image: linear-gradient(to right, rgba(0, 0, 0, .9), rgba(0, 0, 0, .19))
}

._socialIcons_1fq6e_171 {
    display: flex;
    justify-content: center;
    padding: 0
}

._socialIcon_1fq6e_171 {
    display: flex;
    margin: 0 14px
}

._socialIcon_1fq6e_171 a,
._socialIcon_1fq6e_171 i,
._socialIcon_1fq6e_171 svg {
    color: var(--text-color, #ffffff);
    text-decoration: none !important
}

._buttonsWrapper_492b6_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_492b6_138 {
    margin-bottom: 16px
}

._spaceAfter--small_492b6_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_492b6_144 {
    margin-bottom: 30px
}

._hero_492b6_165 {
    position: relative;
    align-items: center;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._bk_492b6_171 {
    position: absolute;
    pointer-events: none;
    inset: 0;
    background: url(https://assets.wuiltsite.com/assets/mosalas.svg) no-repeat;
    background-size: cover;
    background-position: top right;
    opacity: .6
}

@media (min-width: 768px) {
    ._bk_492b6_171 {
        opacity: 1;
        background-size: contain
    }
}

.rtl ._bk_492b6_171 {
    transform: scaleX(-1)
}

._heroContent_492b6_193 {
    --text-color: #fff;
    position: relative;
    z-index: 1;
    padding: 70px 0;
    margin: auto;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    min-height: 100%;
    z-index: 3
}

@media (min-width: 768px) {
    ._heroContent_492b6_193 h1 {
        width: 70%
    }
}

._socialLinksColumn_492b6_211 {
    display: flex;
    align-items: center
}

._socialIcons_492b6_216 {
    display: flex;
    justify-content: center;
    padding: 0;
    margin-bottom: 0;
    font-size: 24px
}

._socialIcon_492b6_216 {
    display: flex;
    margin-right: 20px
}

.rtl ._socialIcon_492b6_216 {
    margin-right: 0;
    margin-left: 20px
}

._socialIcon_492b6_216 a,
._socialIcon_492b6_216 i,
._socialIcon_492b6_216 svg {
    opacity: .8;
    color: #fff;
    text-decoration: none !important
}

._buttonsWrapper_pdhwt_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_pdhwt_138 {
    margin-bottom: 16px
}

._spaceAfter--small_pdhwt_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_pdhwt_144 {
    margin-bottom: 30px
}

._hero_pdhwt_165 {
    position: relative;
    background-image: linear-gradient(to bottom, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._heroContent_pdhwt_170 {
    --text-color: #fff;
    position: relative;
    margin: auto;
    padding: 70px 0;
    display: flex;
    justify-content: center;
    min-height: 100%;
    z-index: 3
}

._heroContent_pdhwt_170 h1 {
    text-align: start
}

@media (min-width: 768px) {
    ._heroContent_pdhwt_170 h1 {
        width: 70%
    }
}

._heroContent_pdhwt_170 p {
    text-align: start
}

._socialLinksColumn_pdhwt_192 {
    display: flex;
    align-items: center
}

._socialIcons_pdhwt_197 {
    display: flex;
    justify-content: center;
    padding: 0;
    margin-bottom: 0;
    font-size: 24px
}

._socialIcon_pdhwt_197 {
    display: flex;
    margin-right: 20px
}

.rtl ._socialIcon_pdhwt_197 {
    margin-right: 0;
    margin-left: 20px
}

._socialIcon_pdhwt_197 a,
._socialIcon_pdhwt_197 i,
._socialIcon_pdhwt_197 svg {
    opacity: .8;
    color: #fff;
    text-decoration: none !important
}

._bk_pdhwt_221 {
    position: absolute;
    pointer-events: none;
    inset: 0;
    background: url(https://assets.wuiltsite.com/assets/mosalas-bermouza.svg) no-repeat;
    background-size: contain;
    background-position: top right !important
}

.rtl ._bk_pdhwt_221 {
    transform: scaleX(-1)
}

._bk2_pdhwt_236 {
    width: 100%;
    height: 46%;
    position: absolute;
    bottom: 0;
    left: 0;
    background: url(https://assets.wuiltsite.com/assets/kowar-2.svg) no-repeat;
    background-size: contain;
    background-position: top left !important
}

.rtl ._bk2_pdhwt_236 {
    transform: scaleX(-1)
}

._buttonsWrapper_dujkq_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_dujkq_138 {
    margin-bottom: 16px
}

._spaceAfter--small_dujkq_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_dujkq_144 {
    margin-bottom: 30px
}

._socialLinksColumn_dujkq_165 {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 55px
}

._socialIcons_dujkq_172 {
    display: flex;
    justify-content: center;
    padding: 0;
    margin-bottom: 0;
    font-size: 24px
}

._socialIcon_dujkq_172 {
    display: flex;
    margin: 0 14px
}

._socialIcon_dujkq_172 a,
._socialIcon_dujkq_172 i,
._socialIcon_dujkq_172 svg {
    opacity: .75;
    color: #fff;
    text-decoration: none !important
}

._overlay_dujkq_192 {
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0), #000000)
}

._heroContent_dujkq_196 {
    --text-color: #fff;
    position: relative;
    padding: 70px 0;
    margin: auto;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    min-height: 100%;
    z-index: 3
}

@media (min-width: 768px) {
    ._heroContent_dujkq_196 {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100%;
        z-index: 3
    }
}

._buttonsWrapper_182s0_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_182s0_138 {
    margin-bottom: 16px
}

._spaceAfter--small_182s0_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_182s0_144 {
    margin-bottom: 30px
}

._herox00_182s0_165 {
    --text-color: #fff;
    justify-content: center;
    align-items: center;
    text-align: center;
    background-size: auto, cover;
    background-position: 0px 0px, 50% 50%;
    min-height: 60vh;
    padding: 0
}

._herox00_182s0_165 ._herox01_182s0_175 {
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding: 0 16px
}

._herox00_182s0_165 ._herox02_182s0_181 {
    position: relative;
    display: grid;
    grid-template-rows: auto;
    margin-right: auto;
    margin-left: auto;
    grid-auto-flow: row;
    width: 100%;
    max-width: 1200px;
    grid-row-gap: 36px;
    grid-column-gap: 12px;
    grid-template-columns: 1fr 1fr 1fr 1fr
}

._herox00_182s0_165 ._herox03_182s0_194 {
    display: flex;
    flex-direction: column;
    align-items: center;
    grid-column-start: span 4;
    grid-column-end: span 4;
    padding: 50px 0
}

._herox00_182s0_165 ._herox04_182s0_202 {
    flex: 1;
    margin-bottom: 30px
}

@media (min-width: 768px) {
    ._herox00_182s0_165 {
        min-height: 600px;
        padding: 0 36px
    }
    ._herox00_182s0_165 ._herox01_182s0_175 {
        padding: 0 20px
    }
    ._herox00_182s0_165 ._herox02_182s0_181 {
        max-width: 738px;
        grid-column-gap: 24px;
        grid-row-gap: 20px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr
    }
    ._herox00_182s0_165 ._herox03_182s0_194 {
        grid-column-start: span 8;
        grid-column-end: span 8;
        grid-row-start: span 1;
        grid-row-end: span 1
    }
    ._herox00_182s0_165 ._herox04_182s0_202 {
        margin-bottom: 30px;
        flex: 1
    }
    ._herox00_182s0_165 ._herox04_182s0_202 h1 {
        margin: 20px 0 25px
    }
}

@media (min-width: 992px) {
    ._herox00_182s0_165 {
        min-height: 90vh;
        padding: 0 36px
    }
    ._herox00_182s0_165 ._herox01_182s0_175 {
        padding: 0 24px
    }
    ._herox00_182s0_165 ._herox02_182s0_181 {
        width: auto;
        max-width: none;
        grid-column-gap: 24px;
        grid-row-gap: 30px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr
    }
}

@media (min-width: 1200px) {
    ._herox00_182s0_165 ._herox01_182s0_175 {
        padding: 0 36px
    }
    ._herox00_182s0_165 ._herox02_182s0_181 {
        display: grid;
        grid-auto-columns: 1fr;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto;
        grid-row-gap: 16px;
        grid-column-gap: 16px;
        grid-column-gap: 36px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr
    }
    ._herox00_182s0_165 ._herox03_182s0_194 {
        grid-column-start: 3;
        grid-column-end: 11;
        grid-row-start: 1;
        grid-row-end: 2
    }
}

._phone_182s0_272 {
    margin: 0 5px
}

._buttonsWrapper_ndx1d_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_ndx1d_138 {
    margin-bottom: 16px
}

._spaceAfter--small_ndx1d_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_ndx1d_144 {
    margin-bottom: 30px
}

._heroSection_ndx1d_165 {
    padding: 50px 0 70px
}

._heroSection_ndx1d_165 ._heroContent_ndx1d_168 ._heroText_ndx1d_168 {
    width: 95%
}

._heroSection_ndx1d_165 ._heroContent_ndx1d_168 ._heroText_ndx1d_168 h1 {
    margin-bottom: 20px
}

._heroSection_ndx1d_165 ._primaryButton_ndx1d_174 {
    color: var(--theme-color-brand1);
    display: flex;
    width: -moz-fit-content;
    width: fit-content
}

._heroSection_ndx1d_165 ._imageContainer_ndx1d_179 {
    width: 100%;
    height: 450px
}

._heroSection_ndx1d_165 ._imageContainer_ndx1d_179 img {
    border-radius: 8px;
    overflow: hidden;
    max-height: unset !important;
    min-height: 450px
}

._buttonsWrapper_xhu17_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_xhu17_138 {
    margin-bottom: 16px
}

._spaceAfter--small_xhu17_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_xhu17_144 {
    margin-bottom: 30px
}

._heroSection_xhu17_165 {
    padding: 50px 0 70px
}

._heroSection_xhu17_165 ._imageContainer_xhu17_168 {
    width: 100%;
    height: 450px
}

._heroSection_xhu17_165 ._imageContainer_xhu17_168 img {
    border-radius: 8px;
    overflow: hidden;
    max-height: unset !important;
    min-height: 450px
}

._heroSection_xhu17_165 ._heroContent_xhu17_178 {
    padding: 0 0 0 30px
}

@media (max-width: 992px) {
    ._heroSection_xhu17_165 ._heroContent_xhu17_178 {
        padding: 0
    }
}

._heroSection_xhu17_165 ._heroContent_xhu17_178 ._heroText_xhu17_186 h1 {
    margin-bottom: 20px
}

.rtl ._heroContent_xhu17_178 {
    padding: 0 30px 0 0
}

@media (max-width: 992px) {
    .rtl ._heroContent_xhu17_178 {
        padding: 0
    }
}

._buttonsWrapper_ty6w9_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_ty6w9_138 {
    margin-bottom: 16px
}

._spaceAfter--small_ty6w9_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_ty6w9_144 {
    margin-bottom: 30px
}

._heroSection_ty6w9_165 {
    padding: 50px 100px 70px;
    text-align: center
}

@media (max-width: 992px) {
    ._heroSection_ty6w9_165 {
        padding: 50px 0 70px
    }
}

._heroSection_ty6w9_165 ._imageContainer_ty6w9_174 {
    width: 100%;
    height: 450px;
    border-radius: 8px
}

._heroSection_ty6w9_165 ._imageContainer_ty6w9_174 img {
    max-height: unset;
    min-height: 450px
}

._heroSection_ty6w9_165 ._heroContent_ty6w9_183 ._heroText_ty6w9_183 h1 {
    margin: 20px 0
}

._buttonsWrapper_rmdre_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_rmdre_138 {
    margin-bottom: 16px
}

._spaceAfter--small_rmdre_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_rmdre_144 {
    margin-bottom: 30px
}

._heroSection_rmdre_165 {
    padding: 50px 100px 70px;
    text-align: center
}

@media (max-width: 992px) {
    ._heroSection_rmdre_165 {
        padding: 50px 0 70px
    }
}

._heroSection_rmdre_165 ._heroText_rmdre_174 h1 {
    margin: 20px 0
}

._buttonsWrapper_9f3yn_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_9f3yn_138 {
    margin-bottom: 16px
}

._spaceAfter--small_9f3yn_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_9f3yn_144 {
    margin-bottom: 30px
}

._heroSection_9f3yn_165 {
    padding: 50px 0 70px
}

._heroSection_9f3yn_165 ._heroContent_9f3yn_168 ._heroText_9f3yn_168 {
    width: 95%
}

._heroSection_9f3yn_165 ._heroContent_9f3yn_168 ._heroText_9f3yn_168 h1 {
    margin-bottom: 20px
}

._heroSection_9f3yn_165 ._imageContainer_9f3yn_174 {
    width: 100%;
    height: 450px
}

._heroSection_9f3yn_165 ._imageContainer_9f3yn_174 img {
    border-radius: 8px;
    overflow: hidden;
    max-height: unset !important;
    min-height: 450px
}

._buttonsWrapper_19ohk_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_19ohk_138 {
    margin-bottom: 16px
}

._spaceAfter--small_19ohk_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_19ohk_144 {
    margin-bottom: 30px
}

._heroSection_19ohk_165 {
    position: relative;
    height: 95vh
}

@media (max-width: 1200px) {
    ._heroSection_19ohk_165 {
        height: 900px
    }
}

._heroSection_19ohk_165 ._slide_19ohk_174 {
    height: 100%
}

._heroSection_19ohk_165 ._overlay_19ohk_177 {
    background-color: #000;
    opacity: .6
}

._heroSection_19ohk_165 ._navArrows_19ohk_181 {
    background: none;
    font-size: 38px
}

@media (max-width: 992px) {
    ._heroSection_19ohk_165 ._navArrows_19ohk_181 {
        bottom: 10px
    }
}

._heroSection_19ohk_165 ._back_19ohk_190 {
    left: 20px
}

@media (max-width: 992px) {
    ._heroSection_19ohk_165 ._back_19ohk_190 {
        left: 0
    }
}

._heroSection_19ohk_165 ._next_19ohk_198 {
    right: 20px
}

@media (max-width: 992px) {
    ._heroSection_19ohk_165 ._next_19ohk_198 {
        right: 0
    }
}

._heroSection_19ohk_165 ._heroContent_19ohk_206 {
    --text-color: #fff;
    text-align: center;
    position: absolute;
    z-index: 2;
    top: 35%;
    width: 100%;
    height: -moz-fit-content;
    height: fit-content;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

@media (max-width: 992px) {
    ._heroSection_19ohk_165 ._heroContent_19ohk_206 {
        top: 20%
    }
}

._heroSection_19ohk_165 ._heroContent_19ohk_206 ._heroText_19ohk_224 {
    width: 65%;
    color: #fff
}

@media (max-width: 992px) {
    ._heroSection_19ohk_165 ._heroContent_19ohk_206 ._heroText_19ohk_224 {
        width: 80%
    }
}

@media (max-width: 300px) {
    ._heroSection_19ohk_165 ._heroContent_19ohk_206 ._heroText_19ohk_224 {
        width: 70%
    }
}

._heroSection_19ohk_165 ._heroContent_19ohk_206 ._buttonsContainer_19ohk_238 {
    display: flex;
    margin-top: 30px
}

@media (max-width: 992px) {
    ._heroSection_19ohk_165 ._heroContent_19ohk_206 ._buttonsContainer_19ohk_238 {
        flex-direction: column;
        width: 100%
    }
}

@media (max-width: 992px) {
    .rtl ._navArrows_19ohk_181 {
        bottom: 30px
    }
}

.rtl ._next_19ohk_198,
.rtl ._back_19ohk_190 {
    transform: rotateX(180deg);
    transform: rotateY(180deg)
}

._buttonsWrapper_mxa90_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_mxa90_138 {
    margin-bottom: 16px
}

._spaceAfter--small_mxa90_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_mxa90_144 {
    margin-bottom: 30px
}

._heroSection_mxa90_165 {
    position: relative;
    height: 95vh
}

@media (max-width: 1200px) {
    ._heroSection_mxa90_165 {
        min-height: 900px
    }
}

._heroSection_mxa90_165 ._slide_mxa90_174 {
    height: 100%
}

._heroSection_mxa90_165 ._overlay_mxa90_177 {
    background-color: #000;
    opacity: .6
}

._heroSection_mxa90_165 ._navArrows_mxa90_181 {
    background: none;
    font-size: 38px
}

@media (max-width: 992px) {
    ._heroSection_mxa90_165 ._navArrows_mxa90_181 {
        font-size: 26px
    }
}

._heroSection_mxa90_165 ._back_mxa90_190 {
    left: 20px
}

@media (max-width: 992px) {
    ._heroSection_mxa90_165 ._back_mxa90_190 {
        left: 0
    }
}

._heroSection_mxa90_165 ._next_mxa90_198 {
    right: 20px
}

@media (max-width: 992px) {
    ._heroSection_mxa90_165 ._next_mxa90_198 {
        right: 0
    }
}

._heroSection_mxa90_165 ._slideshow_mxa90_206 {
    height: 100%
}

._heroSection_mxa90_165 ._heroContent_mxa90_209 {
    --text-color: #fff;
    text-align: center;
    position: absolute;
    top: 35%;
    z-index: 2;
    width: 100%;
    height: -moz-fit-content;
    height: fit-content;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

._heroSection_mxa90_165 ._heroContent_mxa90_209 ._heroText_mxa90_222 {
    width: 65%
}

@media (max-width: 992px) {
    ._heroSection_mxa90_165 ._heroContent_mxa90_209 ._heroText_mxa90_222 {
        width: 80%
    }
}

@media (max-width: 300px) {
    ._heroSection_mxa90_165 ._heroContent_mxa90_209 ._heroText_mxa90_222 {
        width: 70%
    }
}

.rtl ._next_mxa90_198,
.rtl ._back_mxa90_190 {
    transform: rotateX(180deg);
    transform: rotateY(180deg)
}

._buttonsWrapper_l84ry_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_l84ry_138 {
    margin-bottom: 16px
}

._spaceAfter--small_l84ry_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_l84ry_144 {
    margin-bottom: 30px
}

._containerLarge_l84ry_165 {
    width: 100%;
    max-width: 80rem;
    margin-right: auto;
    margin-left: auto
}

._containerLarge_l84ry_165 img {
    display: inline-block;
    max-width: 100%
}

._paddingXhuge_l84ry_176 {
    padding: 7rem
}

._paddingVertical_l84ry_180,
._paddingvertical_l84ry_185._paddingXhuge_l84ry_176 {
    padding-right: 0rem;
    padding-left: 0rem
}

._header26_component_l84ry_190 {
    display: flex;
    flex-direction: column;
    align-items: center
}

._marginSmall_l84ry_206 {
    margin: 1.5rem
}

._marginXxlarge_l84ry_210 {
    margin: 5rem
}

._marginBottom_l84ry_214,
._marginBottom_l84ry_214._marginXxlarge_l84ry_210 {
    margin-top: 0rem;
    margin-right: 0rem;
    margin-left: 0rem
}

._textAlignCenter_l84ry_226 {
    text-align: center
}

._maxWidthLarge_l84ry_230 {
    width: 100%;
    max-width: 48rem
}

._marginBottom_l84ry_214._marginSmall_l84ry_206 {
    margin-top: 0rem;
    margin-right: 0rem;
    margin-left: 0rem
}

._textSizeMedium_l84ry_241 {
    font-size: 1.125rem
}

._marginMedium_l84ry_245 {
    margin: 2rem
}

._marginTop_l84ry_249,
._marginTop_l84ry_249._marginMedium_l84ry_245 {
    margin-right: 0rem;
    margin-bottom: 0rem;
    margin-left: 0rem
}

._buttonRow_l84ry_261 {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    grid-column-gap: 1rem;
    grid-row-gap: 1rem
}

._buttonRow_l84ry_261._isButtonRowCenter_l84ry_277 {
    justify-content: center
}

._button_l84ry_132 {
    display: block;
    padding: .75rem 1.5rem;
    border-style: solid;
    border-width: 1px;
    border-color: var(--theme-color-brand1);
    background-color: var(--theme-color-brand1);
    color: #fff;
    text-align: center
}

._buttonSecondary_l84ry_295 {
    display: block;
    padding: .75rem 1.5rem;
    border-style: solid;
    border-width: 1px;
    border-color: var(--theme-color-brand1);
    background-color: transparent;
    color: var(--theme-color-brand1);
    text-align: center
}

._imageContainer_l84ry_306 {
    width: 100%;
    margin: 0 auto
}

._imageContainer_l84ry_306 img {
    aspect-ratio: 2/1;
    max-height: unset !important
}

@media screen and (max-width: 991px) {
    ._containerLarge_l84ry_165 h1 {
        font-size: 3.25rem
    }
    ._marginXxlarge_l84ry_210 {
        margin: 4.5rem
    }
    ._paddingXhuge_l84ry_176 {
        padding: 6rem
    }
    ._marginTop_l84ry_249 {
        margin-right: 0rem;
        margin-bottom: 0rem;
        margin-left: 0rem
    }
    ._marginBottom_l84ry_214 {
        margin-top: 0rem;
        margin-right: 0rem;
        margin-left: 0rem
    }
    ._paddingVertical_l84ry_180 {
        padding-right: 0rem;
        padding-left: 0rem
    }
}

@media screen and (max-width: 767px) {
    ._containerLarge_l84ry_165 h1 {
        font-size: 2.5rem
    }
    ._marginSmall_l84ry_206 {
        margin: 1.25rem
    }
    ._marginMedium_l84ry_245 {
        margin: 1.5rem
    }
    ._marginXxlarge_l84ry_210 {
        margin: 3rem
    }
    ._paddingXhuge_l84ry_176 {
        padding: 4rem
    }
    ._marginTop_l84ry_249 {
        margin-right: 0rem;
        margin-bottom: 0rem;
        margin-left: 0rem
    }
    ._marginBottom_l84ry_214 {
        margin-top: 0rem;
        margin-right: 0rem;
        margin-left: 0rem
    }
    ._paddingVertical_l84ry_180 {
        padding-right: 0rem;
        padding-left: 0rem
    }
    ._textSizeMedium_l84ry_241 {
        font-size: 1rem
    }
}

@media screen and (max-width: 479px) {
    ._containerLarge_l84ry_165 h1 {
        font-size: 2.5rem
    }
    ._marginTop_l84ry_249 {
        margin-right: 0rem;
        margin-bottom: 0rem;
        margin-left: 0rem
    }
    ._marginBottom_l84ry_214 {
        margin-top: 0rem;
        margin-right: 0rem;
        margin-left: 0rem
    }
    ._paddingVertical_l84ry_180 {
        padding-right: 0rem;
        padding-left: 0rem
    }
}

._buttonsWrapper_cteir_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_cteir_138 {
    margin-bottom: 16px
}

._spaceAfter--small_cteir_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_cteir_144 {
    margin-bottom: 30px
}

._bk_cteir_165 {
    padding: 75px 0;
    position: relative
}

@media (min-width: 768px) {
    ._bk_cteir_165 {
        padding: 100px 0
    }
}

._imageContainer_cteir_175 {
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center
}

._imageContainer_cteir_175 img {
    width: 100px
}

._clientTitle_cteir_185 {
    text-align: center;
    padding-bottom: 4%;
    position: relative
}

._clientTitle_cteir_185 h2 {
    color: #27272a;
    text-align: center;
    line-height: 46px;
    display: inline-block;
    font-size: 36px
}

._clientTitle_cteir_185 p {
    color: #27272acc;
    text-align: center;
    line-height: 20px;
    width: auto;
    margin: 0 auto
}

._item_cteir_205 {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 0 auto;
    height: 100px;
    transition: .2s all
}

._item_cteir_205:hover {
    background-color: #04040487
}

._space_cteir_138 {
    margin-bottom: 15px
}

._bk_1b1ph_1 {
    padding: 80px
}

._imageContainer_1b1ph_5 {
    width: 100px;
    margin: 20px auto
}

._imageContainer_1b1ph_5 img {
    width: 100px
}

._clienteTitle_1b1ph_13 {
    text-align: center;
    padding-bottom: 4%;
    position: relative
}

._clienteTitle_1b1ph_13 h1 {
    color: #000;
    text-align: center;
    line-height: 46px
}

._clienteTitle_1b1ph_13 p {
    color: #2a2a2a;
    text-align: center;
    line-height: 20px;
    width: 60%;
    margin: 0 auto
}

._space_1b1ph_31 {
    border: 1px solid #ddd;
    padding: 4px
}

@media (max-width: 768px) {
    ._bk_1b1ph_1 {
        margin: 20px
    }
    ._clienteTitle_1b1ph_13 p {
        width: 100%
    }
    ._space_1b1ph_31 {
        border: 1px solid #ddd
    }
}

._buttonsWrapper_1qlbs_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1qlbs_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1qlbs_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1qlbs_144 {
    margin-bottom: 30px
}

._bk_1qlbs_165 {
    padding: 75px 0
}

@media (min-width: 768px) {
    ._bk_1qlbs_165 {
        padding: 100px 0
    }
}

._imageContainer_1qlbs_174 {
    width: 100px;
    display: flex;
    align-items: center
}

._imageContainer_1qlbs_174 img {
    width: 100px
}

._clientTitle_1qlbs_183 {
    text-align: center;
    padding-bottom: 35px;
    position: relative
}

._clientTitle_1qlbs_183 h2 {
    color: #000;
    text-align: center;
    line-height: 46px;
    font-size: 36px
}

._clientTitle_1qlbs_183 p {
    color: #999;
    text-align: center;
    line-height: 20px;
    width: auto;
    margin: 0 auto
}

._item_1qlbs_202 {
    display: flex;
    justify-content: center;
    align-items: center;
    width: auto;
    height: 100px;
    border: 1px solid #ddd;
    margin: 0 auto
}

._space_1qlbs_138 {
    margin: 8px 0
}

._readmore_1qlbs_216 {
    margin: 3% auto 0;
    color: #333;
    padding: 8px 30px;
    border: 1px solid rgba(51, 51, 51, .28)
}

._readmore_1qlbs_216:hover,
._readmore_1qlbs_216:focus {
    margin: 3% auto 0;
    color: #333;
    padding: 8px 30px;
    border: 1px solid rgba(51, 51, 51, .28);
    box-shadow: none
}

._buttonsWrapper_uyujk_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_uyujk_138 {
    margin-bottom: 16px
}

._spaceAfter--small_uyujk_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_uyujk_144 {
    margin-bottom: 30px
}

._section_uyujk_165 {
    text-align: center;
    padding: 75px 0
}

@media (min-width: 768px) {
    ._section_uyujk_165 {
        padding: 100px 0
    }
}

._logoImage_uyujk_175 {
    max-width: 100%
}

._clienteTitle_uyujk_179 {
    text-align: center;
    padding-bottom: 35px;
    position: relative
}

._clienteTitle_uyujk_179 h2 {
    color: #000;
    text-align: center;
    line-height: 46px;
    font-size: 36px
}

._clienteTitle_uyujk_179 p {
    color: #999;
    text-align: center;
    line-height: 20px;
    width: auto;
    margin: 0 auto
}

._imageContainer_uyujk_198 {
    margin: 28px 0
}

._imageContainer_uyujk_198 img {
    filter: grayscale(100%) contrast(100%) brightness(.6);
    width: auto;
    max-height: 52px
}

._logosContainer_uyujk_207 {
    justify-content: center
}

._buttonsWrapper_bj5vz_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_bj5vz_138 {
    margin-bottom: 16px
}

._spaceAfter--small_bj5vz_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_bj5vz_144 {
    margin-bottom: 30px
}

._section_bj5vz_165 {
    padding: 75px 0;
    text-align: center
}

._section_bj5vz_165 ._styleColumn_bj5vz_169 {
    display: flex;
    align-items: center
}

@media (min-width: 768px) {
    ._section_bj5vz_165 {
        padding: 100px 0
    }
}

._clienteTitle_bj5vz_179 {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    align-items: flex-start
}

._clienteTitle_bj5vz_179 h2 {
    color: #333
}

._clienteTitle_bj5vz_179 p {
    color: #666;
    width: auto;
    text-align: left
}

.rtl ._clienteTitle_bj5vz_179 p {
    text-align: right
}

._imageContainer_bj5vz_198 {
    display: flex;
    justify-content: center;
    margin: 28px 0
}

._imageContainer_bj5vz_198 img {
    filter: grayscale(100%) contrast(100%) brightness(.6);
    width: auto;
    max-height: 46px;
    overflow: hidden
}

._logosContainer_bj5vz_210 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap
}

._buttonsWrapper_jy2ie_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_jy2ie_138 {
    margin-bottom: 16px
}

._spaceAfter--small_jy2ie_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_jy2ie_144 {
    margin-bottom: 30px
}

._section_jy2ie_165 {
    text-align: center
}

._section_jy2ie_165 ._styleColumn_jy2ie_168 {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-image: linear-gradient(108deg, var(--theme-color-brand1), var(--theme-color-secondary1)) !important
}

._section_jy2ie_165 ._styleColumn2_jy2ie_175 {
    display: flex;
    align-items: center;
    justify-content: center
}

@media (min-width: 768px) {
    ._section_jy2ie_165 ._styleColumn2_jy2ie_175 {
        padding: 50px 0
    }
}

.rtl ._clienteTitle_jy2ie_186 {
    padding: 110px 0 50px;
    align-items: center;
    width: 90%
}

@media (min-width: 768px) {
    .rtl ._clienteTitle_jy2ie_186 {
        padding: 150px 115px 150px 0
    }
}

._clienteTitle_jy2ie_186 h2 {
    color: #fff;
    margin-bottom: 15px;
    text-align: left;
    width: 100%
}

.rtl ._clienteTitle_jy2ie_186 h2 {
    text-align: right
}

._clienteTitle_jy2ie_186 p {
    color: #ffffffbf;
    text-align: left;
    width: auto
}

.rtl ._clienteTitle_jy2ie_186 p {
    text-align: right
}

._imageContainer_jy2ie_214 {
    display: flex;
    justify-content: center;
    margin: 28px 0
}

._imageContainer_jy2ie_214 img {
    filter: grayscale(100%) contrast(100%) brightness(.4);
    width: auto;
    max-height: 46px;
    overflow: hidden
}

._logosContainer_jy2ie_226 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap
}

._buttonsWrapper_111jy_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_111jy_138 {
    margin-bottom: 16px
}

._spaceAfter--small_111jy_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_111jy_144 {
    margin-bottom: 30px
}

._section_111jy_165 {
    padding: 75px 0;
    text-align: center
}

@media (min-width: 768px) {
    ._section_111jy_165 {
        padding: 100px 0
    }
}

._clientTitle_111jy_175 {
    padding-bottom: 35px;
    position: relative
}

._clientTitle_111jy_175 h2 {
    color: #27272a;
    text-align: center;
    line-height: 46px;
    font-size: 36px
}

._clientTitle_111jy_175 p {
    color: #27272ab3;
    text-align: center;
    line-height: 20px;
    width: auto;
    margin: 0 auto
}

._logoImage_111jy_193 {
    max-width: 100%
}

._imageContainer_111jy_197 {
    margin: 28px 0
}

._imageContainer_111jy_197 img {
    width: auto;
    max-height: 66px;
    overflow: hidden
}

._logosContainer_111jy_206 {
    justify-content: center
}

._buttonsWrapper_jbrji_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_jbrji_138 {
    margin-bottom: 16px
}

._spaceAfter--small_jbrji_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_jbrji_144 {
    margin-bottom: 30px
}

._section_jbrji_165 {
    text-align: center
}

._section_jbrji_165 ._styleColumn_jbrji_168,
._section_jbrji_165 ._styleColumn2_jbrji_173 {
    display: flex;
    align-items: center;
    justify-content: center
}

@media (min-width: 768px) {
    ._section_jbrji_165 ._styleColumn2_jbrji_173 {
        padding: 50px 0
    }
}

._bkStyle_jbrji_184 {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px
}

._bkStyle_jbrji_184 ._overlay_jbrji_193 {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(240deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, .8))
}

.rtl ._clienteTitle_jbrji_202 {
    padding: 110px 0 50px;
    align-items: center;
    width: 90%
}

@media (min-width: 768px) {
    .rtl ._clienteTitle_jbrji_202 {
        padding: 150px 115px 150px 0
    }
}

._clienteTitle_jbrji_202 h2 {
    color: #fff;
    margin-bottom: 15px;
    text-align: left;
    width: 100%
}

.rtl ._clienteTitle_jbrji_202 h2 {
    text-align: right
}

._clienteTitle_jbrji_202 p {
    color: #eee;
    text-align: left
}

.rtl ._clienteTitle_jbrji_202 p {
    text-align: right
}

._imageContainer_jbrji_229 {
    display: flex;
    justify-content: center;
    margin: 28px 0
}

._imageContainer_jbrji_229 img {
    filter: grayscale(100%) contrast(100%) brightness(.4);
    width: auto;
    max-height: 46px;
    overflow: hidden
}

._logosContainer_jbrji_241 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap
}

._buttonsWrapper_i41fa_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_i41fa_138 {
    margin-bottom: 16px
}

._spaceAfter--small_i41fa_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_i41fa_144 {
    margin-bottom: 30px
}

._logosContainer_i41fa_165 h2 {
    margin-top: 0rem;
    margin-bottom: 0rem;
    font-size: 3.5rem;
    line-height: 1.2;
    font-weight: 700
}

._logosContainer_i41fa_165 p {
    margin-bottom: 0rem;
    color: #999
}

._logosContainer_i41fa_165 img {
    display: inline-block;
    max-width: 100%
}

._logosContainer_i41fa_165 ._containerLarge_i41fa_180 {
    width: 100%;
    max-width: 80rem;
    margin-right: auto;
    margin-left: auto
}

._logosContainer_i41fa_165 ._paddingVerticalXxlarge_i41fa_186 {
    padding: 5rem 0rem
}

._logosContainer_i41fa_165 ._marginBottomSmall_i41fa_189 {
    margin: 0rem 0rem 1.5rem
}

._logosContainer_i41fa_165 ._marginBottomLarge_i41fa_192 {
    margin: 0rem 0rem 3rem
}

._logosContainer_i41fa_165 ._textAlignCenter_i41fa_195 {
    text-align: center
}

._logosContainer_i41fa_165 ._maxWidthLarge_i41fa_198 {
    width: 100%;
    max-width: 48rem
}

._logosContainer_i41fa_165 ._alignCenter_i41fa_202 {
    margin-right: auto;
    margin-left: auto
}

._logosContainer_i41fa_165 ._textSizeMedium_i41fa_206 {
    font-size: 1.125rem
}

._logosContainer_i41fa_165 ._wLayoutGrid_i41fa_209 {
    display: grid;
    grid-auto-columns: 1fr;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-row-gap: 16px;
    grid-column-gap: 16px
}

._logosContainer_i41fa_165 ._logo6_list_i41fa_220 {
    grid-column-gap: .5rem;
    grid-row-gap: .5rem;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto
}

@media screen and (max-width: 991px) {
    ._logosContainer_i41fa_165 h2 {
        font-size: 3.25rem
    }
    ._logosContainer_i41fa_165 ._paddingVerticalXxlarge_i41fa_186 {
        padding: 4.5rem 0rem
    }
    ._logosContainer_i41fa_165 ._marginBottomLarge_i41fa_192 {
        margin: 0rem 0rem 2.5rem
    }
}

@media screen and (max-width: 767px) {
    ._logosContainer_i41fa_165 h2 {
        font-size: 2.5rem
    }
    ._logosContainer_i41fa_165 ._paddingVerticalXxlarge_i41fa_186 {
        padding: 3rem 0rem
    }
    ._logosContainer_i41fa_165 ._marginBottomSmall_i41fa_189 {
        margin: 0rem 0rem 1.25rem
    }
    ._logosContainer_i41fa_165 ._marginBottomLarge_i41fa_192 {
        margin: 0rem 0rem 2rem
    }
    ._logosContainer_i41fa_165 ._textSizeMedium_i41fa_206 {
        font-size: 1rem
    }
    ._logosContainer_i41fa_165 ._logo6_list_i41fa_220 {
        grid-template-columns: 1fr 1fr
    }
}

@media screen and (max-width: 479px) {
    ._logosContainer_i41fa_165 h2 {
        font-size: 2.5rem
    }
}

._logoItem_i41fa_266 ._logo6_wrapper_i41fa_266 {
    display: flex;
    width: 100%;
    padding: .875rem;
    justify-content: center;
    align-items: flex-start;
    background-color: #f4f4f4
}

._logoItem_i41fa_266 #_wrapperId_i41fa_1 {
    justify-self: center
}

@media screen and (max-width: 767px) {
    ._logoItem_i41fa_266 ._logo6_wrapper_i41fa_266 {
        padding-right: 1rem;
        padding-bottom: 1rem;
        padding-left: 1rem
    }
}

@media screen and (max-width: 479px) {
    ._logoItem_i41fa_266 ._logo6_wrapper_i41fa_266 {
        justify-content: center;
        align-items: flex-start
    }
}

._container_rv8jh_1 {
    padding: 20px 15px
}

._map_rv8jh_5 {
    width: 100%;
    height: 400px;
    box-shadow: 0 0 8px #11161a29, 0 4px 8px #11161a14, 0 8px 16px #11161a14
}

._alignCenter_rv8jh_11 {
    align-items: center
}

._buttonsWrapper_pote0_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_pote0_138 {
    margin-bottom: 16px
}

._spaceAfter--small_pote0_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_pote0_144 {
    margin-bottom: 30px
}

._sectionContainer_pote0_165 {
    padding: 50px 0 70px
}

._sectionContainer_pote0_165 ._sectionHeader_pote0_168 {
    text-align: center;
    margin-bottom: 30px
}

._sectionContainer_pote0_165 ._sectionHeader_pote0_168 h2 {
    color: #1c1c1c;
    font-weight: 700;
    margin-bottom: 10px
}

._sectionContainer_pote0_165 ._sectionHeader_pote0_168 p {
    font-size: 16px;
    color: #585757
}

._pricingItem_pote0_182 {
    border: 1px solid #dbdbdb;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 20px;
    border-radius: 8px
}

._pricingItem_pote0_182 ._itemHeader_pote0_191 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dbdbdb
}

._pricingItem_pote0_182 ._itemHeader_pote0_191 ._itemHeaderText_pote0_197 {
    flex-grow: 1;
    max-width: 76%
}

._pricingItem_pote0_182 ._itemHeader_pote0_191 ._itemHeaderText_pote0_197 h3 {
    font-weight: 700;
    color: var(--theme-color-brand1)
}

._pricingItem_pote0_182 ._itemHeader_pote0_191 ._itemHeaderText_pote0_197 h6 {
    font-weight: 700;
    font-size: 13px;
    color: #1c1c1c
}

._pricingItem_pote0_182 ._itemHeader_pote0_191 ._itemHeaderIcon_pote0_210 {
    position: relative;
    z-index: 0;
    height: 64px;
    width: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px
}

._pricingItem_pote0_182 ._itemHeader_pote0_191 ._itemHeaderIcon_pote0_210:before {
    z-index: -1;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--theme-color-brand1);
    top: 0;
    left: 0;
    opacity: .2
}

._pricingItem_pote0_182 ._itemHeader_pote0_191 ._itemHeaderIcon_pote0_210 i {
    font-size: 24px;
    color: var(--theme-color-brand1);
    margin-top: 5px
}

._itemText_pote0_238 {
    padding: 20px 0
}

._itemText_pote0_238 h5 {
    font-weight: 600;
    font-size: 15px
}

._itemText_pote0_238 p {
    font-size: 15px;
    color: #585757;
    line-height: 28px;
    padding-left: 8px
}

._itemFooter_pote0_252 ._btn_pote0_252 {
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1c1c1c;
    background-color: #fff;
    border: 1px solid #dbdbdb;
    border-radius: 3px;
    padding: 8px 24px
}

._itemFooter_pote0_252 ._activeBtn_pote0_263 {
    background-color: var(--theme-color-brand1);
    color: #fff
}

._active_pote0_263 {
    border: 1px solid var(--theme-color-brand1) !important
}

._buttonsWrapper_1askm_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1askm_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1askm_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1askm_144 {
    margin-bottom: 30px
}

._sectionContainer_1askm_165 {
    padding: 50px 0
}

._pricingItem_1askm_169 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    width: 100%
}

._pricingItem_1askm_169 ._itemHeader_1askm_180 {
    padding: 0 10px
}

._pricingItem_1askm_169 ._itemHeader_1askm_180 h3 {
    color: var(--theme-color-brand1);
    font-weight: 700;
    max-width: 450px;
    margin: 0 auto
}

@media (max-width: 992px) {
    ._pricingItem_1askm_169 ._itemHeader_1askm_180 h3 {
        max-width: 350px
    }
}

@media (max-width: 500px) {
    ._pricingItem_1askm_169 ._itemHeader_1askm_180 h3 {
        max-width: 250px
    }
}

._pricingItem_1askm_169 ._itemHeader_1askm_180 h6 {
    font-size: 14px;
    color: #1c1c1c;
    font-weight: 700;
    max-width: 450px;
    margin: 0 auto 10px
}

@media (max-width: 992px) {
    ._pricingItem_1askm_169 ._itemHeader_1askm_180 h6 {
        max-width: 350px
    }
}

@media (max-width: 500px) {
    ._pricingItem_1askm_169 ._itemHeader_1askm_180 h6 {
        max-width: 250px
    }
}

._itemContent_1askm_218 {
    padding: 10px
}

._itemContent_1askm_218 p {
    font-size: 15px;
    color: #585757;
    line-height: 22px;
    max-width: 450px
}

@media (max-width: 992px) {
    ._itemContent_1askm_218 p {
        max-width: 350px
    }
}

@media (max-width: 500px) {
    ._itemContent_1askm_218 p {
        max-width: 250px
    }
}

._itemFooter_1askm_238 {
    display: flex;
    flex-direction: column;
    justify-content: space-between
}

._itemFooter_1askm_238 ._btn_1askm_243 {
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background-color: var(--theme-color-brand1);
    border-radius: 3px;
    padding: 8px 24px
}

._itemFooter_1askm_238 ._activeBtn_1askm_253 {
    background-color: var(--theme-color-brand1);
    color: #fff
}

._sectionContainer_1xlpv_1 {
    padding: 50px 0 70px
}

._pricingItem_1xlpv_5 {
    border: 1px solid #dbdbdb;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: center;
    height: 100%;
    width: 100%;
    padding: 20px;
    border-radius: 8px
}

._pricingItem_1xlpv_5 ._itemHeader_1xlpv_16 h3 {
    color: var(--theme-color-brand1);
    font-weight: 700
}

._pricingItem_1xlpv_5 ._itemHeader_1xlpv_16 h6 {
    font-size: 15px;
    color: #1c1c1c;
    font-weight: 700
}

._itemContent_1xlpv_26 p {
    font-size: 15px;
    color: #585757;
    line-height: 28px
}

._itemFooter_1xlpv_32 ._btn_1xlpv_32 {
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1c1c1c;
    background-color: #fff;
    border: 1px solid #dbdbdb;
    border-radius: 3px;
    padding: 8px 24px;
    margin: 10px auto 0
}

._itemFooter_1xlpv_32 ._activeBtn_1xlpv_44 {
    background-color: var(--theme-color-brand1);
    color: #fff
}

._active_1xlpv_44 {
    border: 1px solid var(--theme-color-brand1) !important
}

._sectionContainer_1j15k_1 {
    padding: 50px 0 70px
}

._pricingItem_1j15k_5 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    border-radius: 8px;
    width: 100%;
    text-align: center;
    padding: 0 20px
}

._pricingItem_1j15k_5 ._itemHeader_1j15k_15 h6 {
    font-weight: 700;
    font-size: 13px;
    color: #1c1c1c
}

._pricingItem_1j15k_5 ._itemIcon_1j15k_20 {
    position: relative;
    z-index: 0;
    height: 64px;
    width: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 20px
}

._pricingItem_1j15k_5 ._itemIcon_1j15k_20:before {
    z-index: -1;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--theme-color-brand1);
    top: 0;
    left: 0;
    opacity: .2
}

._pricingItem_1j15k_5 ._itemIcon_1j15k_20 i {
    font-size: 24px;
    color: var(--theme-color-brand1);
    margin-top: 5px
}

._itemContent_1j15k_48 p {
    font-size: 15px;
    color: #585757;
    line-height: 28px
}

._itemFooter_1j15k_54 h3 {
    font-weight: 700;
    color: var(--theme-color-brand1)
}

._itemFooter_1j15k_54 ._btn_1j15k_58 {
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background-color: var(--theme-color-brand1);
    border-radius: 3px;
    padding: 8px 24px;
    margin: 0 auto
}

._itemFooter_1j15k_54 ._activeBtn_1j15k_69 {
    background-color: var(--theme-color-brand1);
    color: #fff
}

._buttonsWrapper_7ucz5_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_7ucz5_138 {
    margin-bottom: 16px
}

._spaceAfter--small_7ucz5_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_7ucz5_144 {
    margin-bottom: 30px
}

._sectionContainer_7ucz5_165 {
    padding: 50px 0 70px
}

._sectionContainer_7ucz5_165 ._sectionHeader_7ucz5_168 {
    text-align: center;
    margin-bottom: 30px
}

._sectionContainer_7ucz5_165 ._sectionHeader_7ucz5_168 h2 {
    color: #1c1c1c;
    font-weight: 700;
    margin-bottom: 10px
}

._sectionContainer_7ucz5_165 ._sectionHeader_7ucz5_168 p {
    font-size: 16px;
    color: #585757
}

._pricingItem_7ucz5_182 {
    display: flex;
    flex-direction: column;
    border: 1px solid #dbdbdb;
    height: 100%;
    border-radius: 8px
}

._pricingItem_7ucz5_182 ._imageContainer_7ucz5_189 {
    width: 100%;
    height: -moz-fit-content;
    height: fit-content
}

._pricingItem_7ucz5_182 ._imageContainer_7ucz5_189 img {
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    aspect-ratio: 1/.67
}

._pricingItem_7ucz5_182 ._itemHeader_7ucz5_198 {
    padding: 20px 20px 0
}

._pricingItem_7ucz5_182 ._itemHeader_7ucz5_198 h5 {
    font-size: 16px;
    font-weight: 700
}

._pricingItem_7ucz5_182 ._itemText_7ucz5_205 {
    flex-grow: 1;
    padding: 0 20px
}

._pricingItem_7ucz5_182 ._itemText_7ucz5_205 p {
    font-size: 15px;
    color: #585757;
    line-height: 28px
}

._pricingItem_7ucz5_182 ._itemFooter_7ucz5_214 {
    padding: 0 20px 20px
}

._pricingItem_7ucz5_182 ._itemFooter_7ucz5_214 h3 {
    color: var(--theme-color-brand1);
    font-weight: 700
}

._pricingItem_7ucz5_182 ._itemFooter_7ucz5_214 ._btn_7ucz5_221 {
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1c1c1c;
    background-color: #fff;
    border: 1px solid #dbdbdb;
    border-radius: 3px;
    padding: 8px 24px
}

._pricingItem_7ucz5_182 ._itemFooter_7ucz5_214 ._activeBtn_7ucz5_232 {
    background-color: var(--theme-color-brand1);
    color: #fff
}

._active_7ucz5_232 {
    border: 1px solid var(--theme-color-brand1) !important
}

._sectionContainer_eqm6j_1 {
    padding: 50px 0 70px
}

._sectionContainer_eqm6j_1 ._paymentTab_eqm6j_4 {
    display: flex;
    width: -moz-fit-content;
    width: fit-content;
    margin: 0 auto 20px;
    border-bottom: 2px solid #dbdbdb;
    justify-content: space-between;
    cursor: pointer
}

._sectionContainer_eqm6j_1 ._paymentTab_eqm6j_4 h3 {
    font-size: 20px;
    font-weight: 600
}

._sectionContainer_eqm6j_1 ._paymentTab_eqm6j_4 ._activeTab_eqm6j_16 {
    color: var(--theme-color-brand1);
    border-bottom: 2px solid var(--theme-color-brand1);
    margin-bottom: -1.5px
}

._pricingItem_eqm6j_22 {
    border: 1px solid #dbdbdb;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 20px;
    border-radius: 8px
}

._pricingItem_eqm6j_22 ._itemHeader_eqm6j_31 {
    border-bottom: 1px solid #dbdbdb;
    padding-bottom: 10px
}

._pricingItem_eqm6j_22 ._itemHeader_eqm6j_31 h3 {
    color: var(--theme-color-brand1)
}

._pricingItem_eqm6j_22 ._itemHeader_eqm6j_31 h3,
._pricingItem_eqm6j_22 ._itemHeader_eqm6j_31 h6 {
    font-weight: 700
}

._pricingItem_eqm6j_22 ._itemHeader_eqm6j_31 h6 {
    font-size: 15px;
    margin-bottom: 10px;
    color: #1c1c1c
}

._itemText_eqm6j_48 {
    margin-top: 20px
}

._itemText_eqm6j_48 p {
    font-size: 15px;
    color: #585757;
    line-height: 28px
}

._itemFooter_eqm6j_57 ._btn_eqm6j_57 {
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1c1c1c;
    background-color: #fff;
    border: 1px solid #dbdbdb;
    border-radius: 3px;
    padding: 8px 24px;
    margin: 10px 0 0
}

._itemFooter_eqm6j_57 ._activeBtn_eqm6j_69 {
    background-color: var(--theme-color-brand1);
    color: #fff
}

._active_eqm6j_16 {
    border: 1px solid var(--theme-color-brand1) !important
}

._buttonsWrapper_1v7xn_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1v7xn_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1v7xn_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1v7xn_144 {
    margin-bottom: 30px
}

._body_1v7xn_165 {
    color: #000;
    font-size: 1rem;
    line-height: 1.5
}

._container_1v7xn_171 {
    width: 100%;
    max-width: 80rem;
    margin-right: auto;
    margin-left: auto;
    padding-top: 5%;
    padding-bottom: 5%
}

@media (max-width: 1024px) {
    ._container_1v7xn_171 {
        padding-left: 5%;
        padding-right: 5%
    }
}

._text_container_1v7xn_186 {
    margin-top: 0rem;
    text-align: center;
    width: 100%;
    max-width: 48rem;
    margin-right: auto;
    margin-left: auto;
    margin-bottom: 2.5rem
}

._text_container_1v7xn_186 h2 {
    font-size: 3rem !important;
    font-weight: 700
}

._text_container_1v7xn_186 p {
    font-size: 1.125rem !important
}

._sectionContainer_1v7xn_203 {
    padding: 50px 0 70px
}

._sectionContainer_1v7xn_203 ._paymentTab_1v7xn_206 {
    display: flex;
    width: -moz-fit-content;
    width: fit-content;
    margin: 0px auto 3rem;
    justify-content: space-between;
    cursor: pointer
}

._sectionContainer_1v7xn_203 ._paymentTab_1v7xn_206 div {
    font-size: 20px;
    font-weight: 600;
    border: 1px solid #000;
    background-color: transparent;
    margin: 0;
    padding: .3rem 1.5rem;
    text-align: center;
    border-radius: 0;
    outline: 0
}

._sectionContainer_1v7xn_203 ._paymentTab_1v7xn_206 div h3 {
    margin-bottom: 0
}

._sectionContainer_1v7xn_203 ._paymentTab_1v7xn_206 div div {
    border: none
}

._sectionContainer_1v7xn_203 ._paymentTab_1v7xn_206 ._activeTab_1v7xn_230 {
    background-color: var(--theme-color-brand1) !important;
    border-color: var(--theme-color-brand1) !important;
    color: #fff !important;
    cursor: pointer
}

@media (max-width: 479px) {
    ._sectionContainer_1v7xn_203 ._paymentTab_1v7xn_206 {
        flex-direction: column
    }
}

._pricingItem_1v7xn_242 {
    border: 1px solid #000;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 20px;
    width: 100%;
    height: 100%
}

._pricingItem_1v7xn_242 ._itemHeader_1v7xn_252 h3 {
    color: var(--theme-color-brand1)
}

._pricingItem_1v7xn_242 ._itemHeader_1v7xn_252 h6 {
    font-size: 15px;
    margin-bottom: 0;
    color: #1c1c1c;
    font-size: 1.25rem;
    line-height: 1.4;
    font-weight: 700
}

._pricingItem_1v7xn_242 ._itemHeader_1v7xn_252 p {
    font-weight: 400;
    font-size: 1rem
}

._pricingItem_1v7xn_242 ._button_row_1v7xn_267 {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    grid-column-gap: 1rem;
    grid-row-gap: 1rem
}

._pricingItem_1v7xn_242 ._margin_top_1v7xn_282 {
    margin-right: 0rem;
    margin-bottom: 0rem;
    margin-left: 0rem
}

._pricingItem_1v7xn_242 ._is_button_row_center_1v7xn_287 {
    justify-content: center
}

._pricingItem_1v7xn_242 ._margin_medium_1v7xn_293 {
    margin-right: 0rem;
    margin-bottom: 0rem;
    margin-left: 0rem
}

._itemText_1v7xn_299 h3 {
    font-size: 3.5rem;
    line-height: 1.2;
    font-weight: 700;
    margin: .5rem 0;
    display: inline-block;
    color: var(--theme-color-brand1);
    word-break: break-all
}

._itemText_1v7xn_299 ._btn_1v7xn_308 {
    display: block;
    padding: .75rem 1.5rem;
    border-style: solid;
    border-width: 1px;
    border-color: var(--theme-color-brand1);
    background-color: var(--theme-color-brand1);
    color: #fff;
    text-align: center;
    width: 100%;
    margin-top: 2rem;
    border-radius: 0
}

._itemText_1v7xn_299 ._heading_medium_span_1v7xn_321 {
    font-size: 2rem;
    line-height: 1.3;
    font-weight: 700
}

._divider_1v7xn_327 {
    height: 1px;
    width: 100%;
    margin-top: 2rem;
    margin-bottom: 2rem;
    background-color: #000
}

._pricing25_divider_1v7xn_335 {
    color: #000;
    line-height: 1.5;
    width: 100%;
    height: 1px;
    margin-top: 2rem;
    margin-bottom: 2rem;
    background-color: #000
}

.rtl ._heading_medium_span_1v7xn_321 {
    display: none
}

._buttonsWrapper_fd1m7_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_fd1m7_138 {
    margin-bottom: 16px
}

._spaceAfter--small_fd1m7_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_fd1m7_144 {
    margin-bottom: 30px
}

._servicesContainer_fd1m7_165 {
    padding: 30px 0 70px
}

._imageContainer_fd1m7_169 {
    display: inline-block
}

._item_fd1m7_173 {
    padding: 33px 0
}

._itemImage_fd1m7_177 ._imageWrapper_fd1m7_177 {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden
}

._itemImage_fd1m7_177 img {
    aspect-ratio: 1/1
}

._itemInfo_fd1m7_187 {
    padding: 20px 28px 10px;
    margin: 0 auto
}

._itemInfo_fd1m7_187 h4 {
    margin-bottom: 12px
}

._itemInfo_fd1m7_187 p {
    font-size: 14px
}

._servicesTitle_fd1m7_200 {
    margin: 0 auto;
    text-align: center;
    position: relative;
    padding: 22px 0 33px
}

._servicesTitle_fd1m7_200 h2 {
    max-width: 100%;
    display: inline-block;
    font-weight: 700
}

@media (min-width: 768px) {
    ._servicesTitle_fd1m7_200 h2 {
        max-width: 60%
    }
}

._servicesTitle_fd1m7_200 p {
    width: 100%;
    margin: 0 auto
}

@media (min-width: 768px) {
    ._servicesTitle_fd1m7_200 p {
        width: 50%
    }
}

._buttonsWrapper_bznzu_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_bznzu_138 {
    margin-bottom: 16px
}

._spaceAfter--small_bznzu_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_bznzu_144 {
    margin-bottom: 30px
}

._body_bznzu_165 {
    padding: 30px 0 70px
}

._itemCol_bznzu_169 {
    padding: 0 20px
}

@media (min-width: 768px) {
    ._itemCol_bznzu_169 {
        padding: 0 20px 20px
    }
}

._item_bznzu_169 {
    width: 100%
}

._itemImage_bznzu_182 {
    margin: 0 auto;
    width: 100%
}

._itemImage_bznzu_182:hover {
    background-color: #0f0f0fcc;
    margin: 0 auto
}

._itemImage_bznzu_182 img {
    max-width: 100%;
    opacity: 1;
    display: block;
    transition: .5s ease;
    backface-visibility: hidden;
    aspect-ratio: 4/3
}

._itemImage_bznzu_182 img:hover {
    opacity: .3
}

._itemInfo_bznzu_205 {
    padding: 0;
    margin: 24px auto 50px
}

@media (min-width: 768px) {
    ._itemInfo_bznzu_205 {
        margin: 24px auto
    }
}

._itemInfo_bznzu_205 p {
    width: 100%;
    font-size: 14px
}

._servicesTitle_bznzu_220 {
    margin: 0 auto;
    text-align: center;
    position: relative;
    padding: 36px 0 0
}

._servicesTitle_bznzu_220 h2 {
    width: 100%;
    display: inline-block;
    text-align: center
}

@media (min-width: 768px) {
    ._servicesTitle_bznzu_220 h2 {
        width: 65%
    }
}

._servicesTitle_bznzu_220 p {
    width: 100%;
    margin: 0 auto;
    text-align: center
}

@media (min-width: 768px) {
    ._servicesTitle_bznzu_220 p {
        width: 50%;
        margin: 0 auto 60px
    }
}

._buttonsWrapper_1gzxp_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1gzxp_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1gzxp_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1gzxp_144 {
    margin-bottom: 30px
}

._servicesContainer_1gzxp_165 {
    padding: 70px 0
}

._styleitem_1gzxp_169 {
    margin-top: 20px;
    margin-bottom: 20px
}

@media (min-width: 992px) {
    ._styleitem_1gzxp_169 {
        margin: auto
    }
}

._item_1gzxp_179 {
    margin-bottom: 42px
}

._itemImage_1gzxp_183 {
    margin-top: 2px;
    margin-bottom: 14px;
    color: var(--theme-color-brand1)
}

._itemImage_1gzxp_183 i {
    font-size: 40px
}

@media (min-width: 992px) {
    ._itemImage_1gzxp_183 {
        margin-bottom: 0
    }
}

._itemInfo_1gzxp_197 {
    padding: 5px 0 10px;
    margin: 0 auto
}

._itemInfo_1gzxp_197 p {
    width: 100%;
    font-size: 14px
}

@media (min-width: 480px) {
    ._itemInfo_1gzxp_197 p {
        width: 85%
    }
}

._service_1gzxp_165 {
    margin: 0 auto 40px;
    position: relative
}

@media (min-width: 992px) {
    ._service_1gzxp_165 {
        margin: 0 auto
    }
}

._service_1gzxp_165 h2 {
    font-weight: 700
}

._service_1gzxp_165 p {
    width: 100%
}

@media (min-width: 992px) {
    ._service_1gzxp_165 p {
        width: 80%
    }
}

._buttonsWrapper_1fswf_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1fswf_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1fswf_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1fswf_144 {
    margin-bottom: 30px
}

._body_1fswf_165 {
    padding: 70px 0
}

._servicesTitle_1fswf_169 {
    margin: 0 auto;
    text-align: center;
    padding: 0 20px
}

._servicesTitle_1fswf_169 h2 {
    display: inline-block;
    width: 100%
}

@media (min-width: 768px) {
    ._servicesTitle_1fswf_169 h2 {
        width: 65%
    }
}

._servicesTitle_1fswf_169 p {
    margin: 0 auto 4%;
    width: 100%
}

@media (min-width: 768px) {
    ._servicesTitle_1fswf_169 p {
        width: 50%
    }
}

._styleColumn_1fswf_193 {
    padding: 25px 22px;
    display: flex;
    flex-direction: column
}

._item_1fswf_199 {
    flex: 0 0 100%;
    display: flex;
    flex-direction: column
}

._itemImage_1fswf_205 {
    flex: 0 0 auto
}

._itemImage_1fswf_205:hover {
    background-color: #0f0f0fcc
}

._itemImage_1fswf_205 img {
    max-width: 100%;
    opacity: 1;
    display: block;
    transition: .5s ease;
    backface-visibility: hidden;
    aspect-ratio: 1/1
}

._itemImage_1fswf_205 img:hover {
    opacity: .3
}

._itemInfo_1fswf_223 {
    flex: 1 0 auto;
    padding: 36px 31px 26px;
    margin: 0 0 18px;
    background-color: #fff;
    box-shadow: 0 6px 50px #0000001a
}

._itemInfo_1fswf_223 h4 {
    margin-bottom: 12px;
    color: #1c1c1c;
    font-weight: 700
}

._itemInfo_1fswf_223 p {
    width: 100%;
    color: #585757;
    font-size: 14px
}

@media (min-width: 480px) {
    ._itemInfo_1fswf_223 p {
        width: auto
    }
}

._btnread_1fswf_246 {
    color: var(--theme-color-brand1);
    text-align: center;
    font-weight: 400;
    padding: 0
}

._btnread_1fswf_246:hover {
    color: var(--theme-color-brand1);
    opacity: .6
}

._buttonsWrapper_1ix44_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ix44_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ix44_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ix44_144 {
    margin-bottom: 30px
}

._body_1ix44_165 {
    padding: 30px 0 70px
}

._item_1ix44_169 {
    padding: 20px
}

@media (min-width: 768px) {
    ._item_1ix44_169 {
        padding: 78px 0
    }
}

._itemInfoCol_1ix44_178 {
    padding: 0;
    margin-top: -44px
}

@media (min-width: 768px) {
    ._itemInfoCol_1ix44_178 {
        padding: 0 50px;
        margin-top: 0;
        margin-left: -44px;
        margin-right: auto
    }
    .rtl ._itemInfoCol_1ix44_178 {
        margin-left: auto;
        margin-right: -44px
    }
    ._item_1ix44_169:nth-child(2n) ._itemInfoCol_1ix44_178 {
        order: 1;
        margin-right: -44px;
        margin-left: auto
    }
    .rtl ._item_1ix44_169:nth-child(2n) ._itemInfoCol_1ix44_178 {
        margin-right: auto;
        margin-left: -44px
    }
}

@media (min-width: 768px) {
    ._itemImageCol_1ix44_205 {
        margin-top: -44px;
        margin-right: -44px;
        margin-left: auto
    }
    .rtl ._itemImageCol_1ix44_205 {
        margin-right: auto;
        margin-left: -44px
    }
    ._item_1ix44_169:nth-child(2n) ._itemImageCol_1ix44_205 {
        order: 2;
        margin-left: -44px;
        margin-right: auto
    }
    .rtl ._item_1ix44_169:nth-child(2n) ._itemImageCol_1ix44_205 {
        margin-left: auto;
        margin-right: -44px
    }
}

._image_1ix44_225 {
    border-radius: 4px;
    overflow: hidden;
    aspect-ratio: 3/2
}

._itemInfo_1ix44_178 {
    padding: 36px 50px 34px;
    background-color: #fff;
    box-shadow: 0 5px 50px #615d5d33;
    border-radius: 4px
}

@media (min-width: 768px) {
    ._itemInfo_1ix44_178 {
        margin: 0
    }
}

._itemInfo_1ix44_178 h4 {
    margin-bottom: 12px;
    color: #1c1c1c;
    font-weight: 700
}

._itemInfo_1ix44_178 p {
    width: 100%;
    color: #585757;
    font-size: 14px
}

@media (min-width: 768px) {
    ._itemInfo_1ix44_178 p {
        width: auto
    }
}

._itemInfo_1ix44_178 ._itemImage_1ix44_205 {
    text-align: center
}

._itemInfo_1ix44_178 ._itemImage_1ix44_205 img {
    width: 100%;
    height: 100%
}

._servicesTitle_1ix44_265 {
    margin: 0 auto 20px;
    text-align: center;
    position: relative;
    padding: 22px 0 0
}

._servicesTitle_1ix44_265 h2 {
    text-align: center;
    display: inline-block;
    font-weight: 700
}

._servicesTitle_1ix44_265 p {
    width: 100%;
    margin: 0
}

@media (min-width: 768px) {
    ._servicesTitle_1ix44_265 p {
        width: 100%
    }
}

._buttonsWrapper_1leu6_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1leu6_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1leu6_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1leu6_144 {
    margin-bottom: 30px
}

._servicesContainer_1leu6_165 {
    position: relative;
    left: 0;
    right: 0;
    top: 0
}

._body_1leu6_172 {
    padding: 70px 0
}

._servicesTitle_1leu6_176 {
    margin: 0 auto;
    text-align: center;
    position: relative
}

._servicesTitle_1leu6_176 h2 {
    text-align: center;
    display: inline-block;
    font-weight: 700
}

._servicesTitle_1leu6_176 p {
    width: auto;
    margin: 0 auto;
    text-align: center
}

._item_1leu6_192 {
    --text-color: #fff;
    position: relative;
    margin-bottom: 30px;
    animation-name: _fadeIn_1leu6_1;
    transition: all .3s ease-in-out;
    border-bottom: 8px solid transparent;
    overflow: hidden
}

._item_1leu6_192:hover {
    z-index: 4
}

._item_1leu6_192 ._overlay_1leu6_204 {
    position: absolute;
    background-color: #000;
    opacity: .6;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2
}

._item_1leu6_192 img {
    margin-bottom: 12px;
    max-height: unset;
    aspect-ratio: 1/1
}

._itemInfo_1leu6_220 {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 3;
    padding: 20px 20px 40px;
    animation-name: _fadein_1leu6_1;
    transition: all .3s ease-in-out;
    overflow: hidden;
    transform: translateY(10%);
    color: #fff
}

._itemInfo_1leu6_220 h4 {
    font-weight: 600;
    margin-bottom: 20px
}

._itemInfo_1leu6_220 p {
    color: #ffffffb3;
    font-size: 14px
}

._item_1leu6_192:hover ._itemInfo_1leu6_220 {
    transform: translateY(0)
}

._button_1leu6_132 {
    visibility: visible;
    animation-name: _fadeIn_1leu6_1
}

@media (min-width: 992px) {
    ._button_1leu6_132 {
        visibility: hidden
    }
}

._item_1leu6_192:hover ._button_1leu6_132 {
    visibility: visible
}

@keyframes _fadeIn_1leu6_1 {
    0% {
        opacity: 0
    }
    to {
        opacity: .8
    }
}

._buttonsWrapper_dfr23_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_dfr23_138 {
    margin-bottom: 16px
}

._spaceAfter--small_dfr23_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_dfr23_144 {
    margin-bottom: 30px
}

._section_dfr23_165 {
    padding: 76px 0
}

._intro_dfr23_169 {
    text-align: center
}

._intro_dfr23_169 h2 {
    font-weight: 700
}

._intro_dfr23_169 p {
    width: auto;
    margin: 0 auto
}

._servicesContainer_dfr23_180 {
    display: flex;
    width: 100%;
    justify-content: space-around;
    flex-wrap: wrap
}

._itemContainer_dfr23_187 {
    flex: 0 0 100%;
    padding: 20px 30px;
    width: 100%
}

@media (min-width: 768px) {
    ._itemContainer_dfr23_187 {
        padding: 10px;
        flex: auto;
        width: 30%
    }
}

._itemContainer_dfr23_187 ._itemTitle_dfr23_199 {
    margin: 15px 0
}

._itemContainer_dfr23_187 ._item_dfr23_187 {
    padding: 40px 28px;
    width: 100%;
    transition: all .2s ease-in-out
}

._itemContainer_dfr23_187 ._serviceIcon_dfr23_207 {
    color: var(--text-color);
    background-image: none;
    font-size: 62px;
    background-image: linear-gradient(90deg, var(--theme-color-brand1) 0%, var(--theme-color-secondary1) 100%);
    -webkit-background-clip: text;
    background-clip: text
}

._itemContainer_dfr23_187:hover ._item_dfr23_187 {
    background: #fff;
    box-shadow: 0 0 8px #11161a29, 0 4px 8px #11161a14, 0 8px 16px #11161a14
}

._itemContainer_dfr23_187:hover ._serviceIcon_dfr23_207 {
    font-size: 62px;
    background-image: linear-gradient(90deg, var(--theme-color-brand1) 0%, var(--theme-color-secondary1) 100%);
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text
}

._itemContainer_dfr23_187:hover ._itemInfo_dfr23_226 h4,
._itemContainer_dfr23_187:hover ._itemInfo_dfr23_226 p {
    color: #000
}

._buttonsWrapper_fel7w_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_fel7w_138 {
    margin-bottom: 16px
}

._spaceAfter--small_fel7w_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_fel7w_144 {
    margin-bottom: 30px
}

._section_fel7w_165 {
    padding: 76px 0
}

._intro_fel7w_169 {
    text-align: center
}

._intro_fel7w_169 p {
    margin: 0 auto
}

@media (min-width: 768px) {
    ._intro_fel7w_169 p {
        width: 50%
    }
}

._servicesContainer_fel7w_181 {
    display: flex;
    width: 100%;
    justify-content: space-around;
    flex-wrap: wrap
}

._itemContainer_fel7w_188 {
    flex: 0 0 100%;
    padding: 10px 30px;
    margin: 10px;
    width: 100%
}

._itemContainer_fel7w_188 ._item_fel7w_188 {
    border-radius: 6px;
    overflow: hidden;
    padding: 0;
    width: 100%;
    background: #fff;
    box-shadow: 0 8px 40px #0000001a;
    height: 100%
}

._itemContainer_fel7w_188 ._item_fel7w_188 img {
    aspect-ratio: 6/5
}

._itemContainer_fel7w_188 ._curve_fel7w_206 {
    width: 100%
}

.rtl ._itemContainer_fel7w_188 ._curve_fel7w_206 {
    transform: scaleX(-1)
}

._itemContainer_fel7w_188 ._infoContainer_fel7w_212 {
    position: relative;
    margin-top: -52px
}

._itemContainer_fel7w_188 ._itemInfo_fel7w_216 {
    background: #fff;
    position: relative;
    padding: 0 40px 40px;
    margin-top: -2px
}

._itemContainer_fel7w_188 ._itemInfo_fel7w_216 h4 {
    color: #1c1c1c;
    font-weight: 700
}

._itemContainer_fel7w_188 ._itemInfo_fel7w_216 p {
    color: #585757;
    font-size: 14px
}

._itemContainer_fel7w_188 ._btnLink_fel7w_230 {
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    color: var(--theme-color-brand1);
    padding: 0
}

@media (min-width: 768px) {
    ._itemContainer_fel7w_188 {
        padding: 0;
        width: 30%;
        flex: 0 0 30%
    }
}

._itemTitle_fel7w_245 {
    margin: 0 0 15px
}

._serviceIcon_fel7w_249 {
    color: #ccc
}

._buttonsWrapper_we9wg_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_we9wg_138 {
    margin-bottom: 16px
}

._spaceAfter--small_we9wg_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_we9wg_144 {
    margin-bottom: 30px
}

@media (min-width: 768px) {
    ._servicesContainer_we9wg_166 {
        padding: 70px 0
    }
}

._itemImage_we9wg_171 {
    display: block;
    justify-content: flex-start;
    font-size: 47px;
    color: var(--theme-color-brand1)
}

@media (min-width: 768px) {
    ._itemImage_we9wg_171 {
        justify-content: flex-end
    }
}

._itemInfo_we9wg_183 {
    padding: 16px 0 10px;
    display: flex;
    flex-direction: column;
    align-items: flex-start
}

._styleitem_we9wg_190 {
    width: 100%;
    min-height: 154px;
    border-radius: 8px;
    background-color: #fff;
    margin: 0 auto 18px;
    padding: 30px 25px 16px;
    box-shadow: 0 5px 50px #615d5d33
}

._itemInfo_we9wg_183 h4 {
    color: #1c1c1c;
    font-weight: 700
}

._itemInfo_we9wg_183 p {
    color: #585757;
    font-size: 14px
}

._service_we9wg_166 {
    margin: 0 auto;
    color: #000;
    position: relative;
    padding: 16% 20px 25px
}

._service_we9wg_166 h2 {
    display: inline-block;
    font-weight: 700
}

._btnread_we9wg_221 {
    color: var(--theme-color-brand1);
    font-weight: 500;
    padding: 0;
    margin: 12px 0
}

._buttonsWrapper_1xqqe_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1xqqe_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1xqqe_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1xqqe_144 {
    margin-bottom: 30px
}

._section_1xqqe_165 {
    padding: 70px 0;
    background-image: linear-gradient(113deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._intro_1xqqe_170 {
    text-align: center;
    color: #fff;
    margin-bottom: 15px
}

._intro_1xqqe_170 h2 {
    font-weight: 700;
    width: 90%;
    margin: 0 auto
}

._intro_1xqqe_170 p {
    width: 90%;
    opacity: .7;
    margin: 5px auto 12px;
    font-weight: 300;
    font-size: 16px
}

@media (min-width: 768px) {
    ._intro_1xqqe_170 p {
        width: 70%
    }
}

._servicesContainer_1xqqe_193 {
    display: flex;
    width: 100%;
    justify-content: space-around;
    flex-wrap: wrap
}

._itemContainer_1xqqe_200 {
    flex: 0 0 95%;
    width: 100%;
    padding: 10px 0;
    margin: 10px 10px 0;
    width: 30%
}

@media (min-width: 768px) {
    ._itemContainer_1xqqe_200 {
        flex: 0 0 30%;
        width: 30%
    }
}

._item_1xqqe_200 {
    margin-bottom: 0;
    padding: 50px 40px 40px;
    transition: border .16s;
    width: 100%;
    background-color: #0006;
    color: #fff;
    border-radius: 4px;
    border-bottom: 6px solid transparent
}

._item_1xqqe_200:hover {
    border-bottom: 6px solid white
}

._item_1xqqe_200 ._itemInfo_1xqqe_227 {
    width: 100%
}

._item_1xqqe_200 ._numbStyle_1xqqe_230 {
    font-weight: 200;
    font-size: 42px;
    margin-bottom: 0;
    display: inline-block
}

._item_1xqqe_200 ._itemTitle_1xqqe_236 {
    font-weight: 700;
    color: #fff
}

._item_1xqqe_200 ._seprator_1xqqe_240 {
    width: 57px;
    height: 1.2px;
    opacity: .6;
    background-color: #fff;
    margin-top: 20px;
    margin-bottom: 34px
}

._item_1xqqe_200 ._itemDesc_1xqqe_248 {
    color: #ffffffb3;
    width: 94%;
    font-size: 14px
}

@media (min-width: 768px) {
    ._item_1xqqe_200 {
        margin-bottom: 0
    }
}

._buttonsWrapper_hf70b_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_hf70b_138 {
    margin-bottom: 16px
}

._spaceAfter--small_hf70b_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_hf70b_144 {
    margin-bottom: 30px
}

._outerContainer_hf70b_165 {
    position: relative;
    overflow: hidden
}

._splitBackground_hf70b_170 {
    display: none
}

@media (min-width: 768px) {
    ._splitBackground_hf70b_170 {
        display: block;
        width: 100%;
        position: absolute !important;
        width: 40%;
        left: 0;
        top: 0;
        bottom: 0
    }
    ._splitBackground_hf70b_170 ._imgWrapper_hf70b_183,
    ._splitBackground_hf70b_170 span {
        height: 100%;
        width: 100%
    }
    ._splitBackground_hf70b_170 img {
        display: flex;
        align-items: center;
        width: 100% !important;
        height: 100% !important;
        max-height: unset !important
    }
    .rtl ._splitBackground_hf70b_170 {
        left: auto;
        right: 0
    }
    ._splitBackground_hf70b_170 ._background_hf70b_199 {
        position: absolute;
        inset: 0
    }
}

._rowStyle_hf70b_208 {
    justify-content: flex-end
}

._content_hf70b_212 {
    margin: 70px 0;
    padding: 0 30px
}

._content_hf70b_212 ._serviceTitle_hf70b_216 {
    font-weight: 700
}

._content_hf70b_212 ._serviceDesc_hf70b_219 {
    width: 100%
}

._content_hf70b_212 ._item_hf70b_222 {
    margin: 10px 0 25px
}

._content_hf70b_212 ._item_hf70b_222 h4 {
    font-weight: 700
}

._content_hf70b_212 ._item_hf70b_222 i {
    font-size: 40px;
    color: var(--theme-color-brand1)
}

._content_hf70b_212 ._item_hf70b_222 p {
    font-size: 14px
}

._containerImage_18423_1 {
    position: relative;
    padding: 80px 0 150px
}

._containerImage_18423_1 ._overlay_18423_5 {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #0a0a0a80
}

._intro_18423_14 {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative
}

._intro_18423_14 h2 {
    color: #fff;
    font-weight: 700
}

._intro_18423_14 p {
    width: auto;
    margin: 0 auto;
    opacity: .75;
    color: #fff;
    font-size: 16px
}

._itemContainer_18423_34 {
    text-align: center;
    padding: 10px 0;
    margin-bottom: 20px
}

._itemContainer_18423_34 ._itemTitle_18423_39 {
    margin: 20px 0 15px
}

._itemContainer_18423_34 ._item_18423_34 {
    padding: 40px 28px;
    width: 100%;
    border-radius: 4px;
    background: #fff;
    box-shadow: 0 2px 8px 2px #0000001f
}

._itemContainer_18423_34 ._serviceIcon_18423_49 {
    font-size: 52px;
    color: #0a0a0a;
    opacity: .9
}

._itemsRow_18423_55 {
    justify-content: center
}

._itemInfo_18423_59 h4 {
    margin-top: 38px;
    color: #1c1c1c;
    font-weight: 700
}

._itemInfo_18423_59 p {
    color: #585757;
    font-size: 14px
}

._items_18423_55 {
    margin-top: -80px;
    padding: 0 0 70px;
    position: relative
}

._buttonsWrapper_mm23z_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_mm23z_138 {
    margin-bottom: 16px
}

._spaceAfter--small_mm23z_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_mm23z_144 {
    margin-bottom: 30px
}

._section_mm23z_165 {
    padding: 100px 0
}

._sectionContent_mm23z_169 {
    margin: 100px 0
}

._sectionContent_mm23z_169 h1 {
    margin-top: 0;
    color: #1c1c1c;
    font-weight: 700
}

._itemPreview_mm23z_179 {
    position: relative;
    overflow: hidden;
    border-radius: 5px;
    margin-bottom: 35px
}

._itemPreview_mm23z_179 img {
    aspect-ratio: 273/412;
    max-height: unset !important
}

._itemDetails_mm23z_190 {
    position: absolute;
    z-index: 10;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffffb3;
    padding: 20px 15px 50px;
    transition: all .2s ease-in-out;
    transform: translateY(100%)
}

._itemPreview_mm23z_179:hover ._itemDetails_mm23z_190 {
    transform: translateY(0)
}

._itemPreview_mm23z_179 ._itemDetails_mm23z_190 h5 {
    font-weight: 700;
    margin-top: 0;
    font-size: 16px;
    color: #1c1c1c
}

._itemPreview_mm23z_179 ._itemDetails_mm23z_190 p {
    color: #585757;
    font-size: 14px
}

._itemPreview_mm23z_179 ._itemDetails_mm23z_190 a {
    color: #1d2129
}

._itemPreview_mm23z_179 ._itemDetails_mm23z_190 a i {
    margin-left: 7px
}

@media screen and (max-width: 812px) {
    ._itemPreview_mm23z_179 ._itemDetails_mm23z_190 {
        bottom: 0
    }
    ._itemPreview_mm23z_179 {
        margin-bottom: 50px
    }
    ._sectionContent_mm23z_169 {
        margin-top: 0;
        margin-bottom: 70px
    }
}

._buttonsWrapper_17wic_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_17wic_138 {
    margin-bottom: 16px
}

._spaceAfter--small_17wic_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_17wic_144 {
    margin-bottom: 30px
}

._section_17wic_165 {
    padding: 100px 0
}

._align_center_17wic_169 {
    justify-content: center
}

._expandedHeadlineContent_17wic_173 {
    text-align: center
}

._expandedHeadlineContent_17wic_173 {
    margin-bottom: 60px
}

._expandedItem_17wic_181 {
    text-align: center;
    position: relative;
    margin-bottom: 80px;
    border-radius: 5px
}

._expandedItem_17wic_181 ._prevImg_17wic_188 {
    overflow: hidden;
    border-radius: 5px;
    margin: 0 auto 20px
}

._expandedItem_17wic_181 ._prevImg_17wic_188 img {
    aspect-ratio: 273/412;
    max-height: unset !important
}

._buttonsWrapper_8uga3_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_8uga3_138 {
    margin-bottom: 16px
}

._spaceAfter--small_8uga3_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_8uga3_144 {
    margin-bottom: 30px
}

._body_8uga3_165 {
    padding: 70px 0
}

._servicesTitle_8uga3_169 {
    margin: 0 auto;
    text-align: center;
    color: #000;
    position: relative
}

._servicesTitle_8uga3_169 h2 {
    text-align: center
}

._servicesTitle_8uga3_169 p {
    width: auto;
    margin: 0 auto
}

._item_8uga3_183 {
    position: relative;
    margin-bottom: 30px;
    border-bottom: 8px solid transparent
}

._item_8uga3_183:hover {
    border-bottom: 8px solid var(--theme-color-brand1);
    z-index: 4;
    overflow: hidden
}

._item_8uga3_183 ._overlay_8uga3_193 {
    position: absolute;
    background-color: #000;
    opacity: .6;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2
}

._itemInfo_8uga3_204 {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 3;
    padding: 70px 40px 0;
    transition: all .3s ease-in-out
}

._itemInfo_8uga3_204 h4 {
    color: #000;
    font-weight: 600
}

._itemInfo_8uga3_204 p {
    color: #000
}

@media (min-width: 768px) {
    ._itemInfo_8uga3_204 {
        padding: 130px 40px 0
    }
}

._item_8uga3_183:hover ._itemInfo_8uga3_204 {
    padding: 100px 40px 0
}

._wlayoutgrid_8uga3_231 {
    display: grid;
    grid-auto-columns: 1fr;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-row-gap: 16px;
    grid-column-gap: 16px
}

._section_8uga3_243 {
    padding: 70px 20px
}

._cont_8uga3_247 {
    max-width: 1140px;
    margin-right: auto;
    margin-left: auto
}

._servicesgrid_8uga3_253 {
    grid-template-rows: repeat(auto-fit, 400px)
}

._serviceBackground_8uga3_257 {
    position: relative;
    display: flex;
    min-height: 400px;
    padding: 20px;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-end;
    align-content: flex-end
}

._serviceWrapper_8uga3_268 {
    --text-color: #1c1c1c;
    display: block;
    width: 92%;
    padding: 25px;
    background-color: #fff;
    box-shadow: 1px 1px 3px #0003;
    position: absolute;
    bottom: 0;
    z-index: 3;
    overflow: hidden;
    margin-bottom: 20px;
    left: 50%;
    transform: translate(-50%)
}

._serviceTitle_8uga3_284 {
    font-weight: 700
}

._serviceDesc_8uga3_288 {
    font-size: 14px;
    margin-bottom: 0
}

._itemPreview_8uga3_293 {
    position: relative;
    height: 400px
}

._itemPreview_8uga3_293 img {
    max-height: unset !important;
    height: 400px !important
}

@media (max-width: 767px) {
    ._servicesgrid_8uga3_253 {
        grid-template-columns: 1fr
    }
}

._buttonsWrapper_flh69_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_flh69_138 {
    margin-bottom: 16px
}

._spaceAfter--small_flh69_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_flh69_144 {
    margin-bottom: 30px
}

._servicesContainer_flh69_165 {
    padding: 50px 0
}

._servicesContainer_flh69_165 ._serviceItem_flh69_168 {
    text-align: center;
    display: flex;
    flex-direction: column;
    height: 100%
}

._servicesContainer_flh69_165 ._serviceItem_flh69_168 ._serviceImage_flh69_174 {
    width: 100%
}

._servicesContainer_flh69_165 ._serviceItem_flh69_168 ._serviceImage_flh69_174 img {
    aspect-ratio: 1/.6
}

._buttonsWrapper_1tdvc_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1tdvc_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1tdvc_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1tdvc_144 {
    margin-bottom: 30px
}

._servicesContainer_1tdvc_165 {
    padding: 50px 0
}

._servicesContainer_1tdvc_165 ._sectionHeader_1tdvc_168 {
    text-align: center
}

._servicesContainer_1tdvc_165 ._serviceItem_1tdvc_171 {
    display: flex;
    flex-direction: column;
    height: 100%
}

._servicesContainer_1tdvc_165 ._serviceItem_1tdvc_171 ._serviceImage_1tdvc_176 {
    width: 100%
}

._servicesContainer_1tdvc_165 ._serviceItem_1tdvc_171 ._serviceImage_1tdvc_176 img {
    aspect-ratio: 1/1;
    max-height: unset !important
}

._servicesContainer_1tdvc_165 ._serviceItem_1tdvc_171 p {
    font-size: 14px
}

._buttonsWrapper_17f79_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_17f79_138 {
    margin-bottom: 16px
}

._spaceAfter--small_17f79_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_17f79_144 {
    margin-bottom: 30px
}

._servicesContainer_17f79_165 {
    padding: 50px 0
}

._servicesContainer_17f79_165 ._sectionHeader_17f79_168 {
    text-align: center;
    margin-bottom: 40px
}

._servicesContainer_17f79_165 ._serviceItem_17f79_172 {
    border: 1px solid #dbdbdb;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: 100%
}

._servicesContainer_17f79_165 ._serviceItem_17f79_172 p {
    font-size: 14px
}

._servicesContainer_17f79_165 ._serviceItem_17f79_172 ._serviceIcon_17f79_183 {
    position: relative;
    z-index: 0;
    height: 64px;
    width: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px
}

._servicesContainer_17f79_165 ._serviceItem_17f79_172 ._serviceIcon_17f79_183:before {
    z-index: -1;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--theme-color-brand1);
    top: 0;
    left: 0;
    opacity: .2
}

._servicesContainer_17f79_165 ._serviceItem_17f79_172 ._serviceIcon_17f79_183 i {
    font-size: 24px;
    color: var(--theme-color-brand1);
    margin-top: 5px
}

.rtl ._serviceItem_17f79_172 {
    text-align: right
}

._buttonsWrapper_1ozm0_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1ozm0_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1ozm0_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1ozm0_144 {
    margin-bottom: 30px
}

._servicesContainer_1ozm0_165 {
    padding: 50px 0
}

._servicesContainer_1ozm0_165 ._sectionHeader_1ozm0_168 {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
    width: 100%
}

@media (max-width: 992px) {
    ._servicesContainer_1ozm0_165 ._sectionHeader_1ozm0_168 {
        flex-direction: column
    }
}

._servicesContainer_1ozm0_165 ._sectionHeader_1ozm0_168 h2 {
    border-bottom: solid 3px var(--theme-color-brand1);
    height: -moz-fit-content;
    height: fit-content;
    margin-bottom: 20px;
    width: -moz-fit-content;
    width: fit-content;
    max-width: 100%
}

._servicesContainer_1ozm0_165 ._sectionHeader_1ozm0_168 p {
    margin-left: auto;
    width: 100%
}

._servicesContainer_1ozm0_165 ._serviceItem_1ozm0_190 {
    display: flex;
    flex-direction: column;
    height: 100%
}

._servicesContainer_1ozm0_165 ._serviceItem_1ozm0_190 ._serviceImage_1ozm0_195 {
    width: 100%;
    margin-bottom: 15px
}

._servicesContainer_1ozm0_165 ._serviceItem_1ozm0_190 ._serviceImage_1ozm0_195 img {
    aspect-ratio: 1/.8
}

._servicesContainer_1ozm0_165 ._serviceItem_1ozm0_190 p {
    font-size: 14px
}

._servicesContainer_1ozm0_165 ._serviceBtn_1ozm0_205 {
    margin: 0 0 10px;
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    color: var(--theme-color-brand1);
    padding: 0
}

.rtl ._servicesContainer_1ozm0_165 ._sectionHeader_1ozm0_168 p {
    text-align: right;
    margin-right: auto;
    margin-left: 0
}

._buttonsWrapper_15fs9_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_15fs9_138 {
    margin-bottom: 16px
}

._spaceAfter--small_15fs9_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_15fs9_144 {
    margin-bottom: 30px
}

._servicesContainer_15fs9_165 {
    padding: 50px 0
}

._servicesContainer_15fs9_165 ._sectionHeader_15fs9_168 {
    display: flex;
    width: 100%;
    margin-bottom: 40px;
    padding-top: 30px;
    position: relative
}

@media (max-width: 992px) {
    ._servicesContainer_15fs9_165 ._sectionHeader_15fs9_168 {
        flex-direction: column
    }
}

._servicesContainer_15fs9_165 ._sectionHeader_15fs9_168:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: gray;
    padding: 0 20px
}

._servicesContainer_15fs9_165 ._sectionHeader_15fs9_168:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 15%;
    height: 3px;
    background-color: var(--theme-color-brand1)
}

._servicesContainer_15fs9_165 ._sectionHeader_15fs9_168 h2,
._servicesContainer_15fs9_165 ._sectionHeader_15fs9_168 p {
    width: 100%
}

._servicesContainer_15fs9_165 ._serviceItem_15fs9_205 {
    display: flex;
    flex-direction: column;
    height: 100%
}

._servicesContainer_15fs9_165 ._serviceItem_15fs9_205 ._serviceImage_15fs9_210 {
    width: 100%;
    margin-bottom: 15px
}

._servicesContainer_15fs9_165 ._serviceItem_15fs9_205 ._serviceImage_15fs9_210 img {
    aspect-ratio: 1/.8
}

._servicesContainer_15fs9_165 ._serviceItem_15fs9_205 p {
    font-size: 14px
}

.rtl ._servicesContainer_15fs9_165 ._sectionHeader_15fs9_168:after {
    right: 0
}

.rtl ._servicesContainer_15fs9_165 ._sectionHeader_15fs9_168 p {
    text-align: right;
    margin-right: auto;
    margin-left: 0
}

._buttonsWrapper_1rvor_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1rvor_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1rvor_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1rvor_144 {
    margin-bottom: 30px
}

._teamContainer_1rvor_165 {
    padding: 80px 0
}

._imgContainer_1rvor_169 {
    width: 100px;
    height: 100px;
    margin: 0 auto
}

._imgContainer_1rvor_169 ._imageWrapper_1rvor_174 {
    border-radius: 50%
}

._imgContainer_1rvor_169 img {
    aspect-ratio: 1/1
}

._teamTitle_1rvor_181 {
    text-align: center
}

._teamTitle_1rvor_181 h2 {
    display: inline-block;
    min-width: 300px
}

._teamTitle_1rvor_181 p {
    width: 60%;
    margin: 0 auto
}

._item_1rvor_193 {
    margin: 6px 0
}

._itemInfo_1rvor_197 {
    width: 95%;
    text-align: center
}

._itemInfo_1rvor_197 h4 {
    display: inline-block;
    min-width: 200px
}

._buttonsWrapper_11k18_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_11k18_138 {
    margin-bottom: 16px
}

._spaceAfter--small_11k18_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_11k18_144 {
    margin-bottom: 30px
}

._servicesContainer_11k18_165 {
    padding: 80px 0
}

._imgContainer_11k18_169 {
    width: 100%;
    max-width: 180px;
    margin: 0 auto;
    padding-top: 25px
}

._imgContainer_11k18_169 img {
    aspect-ratio: 1/1
}

._teamTitle_11k18_179 {
    text-align: center;
    margin-bottom: 50px
}

._teamTitle_11k18_179 h2 {
    display: inline-block
}

._teamTitle_11k18_179 p {
    width: auto;
    margin: 0 auto
}

._item_11k18_191 {
    transition: all .5s;
    padding: 4px 0
}

._item_11k18_191:hover {
    box-shadow: 0 0 8px #11161a29, 0 4px 8px #11161a14, 0 8px 16px #11161a14
}

._itemInfo_11k18_199 {
    text-align: center;
    margin: 6px auto;
    padding: 8px
}

._itemInfo_11k18_199 h4 {
    display: inline-block;
    font-weight: 600
}

._itemInfo_11k18_199 h6 {
    font-weight: 600
}

._itemInfo_11k18_199 i,
._itemInfo_11k18_199 svg {
    margin: 0 6px;
    font-weight: lighter;
    color: var(--theme-color-brand1);
    transition: .5s all
}

._social_11k18_219 {
    opacity: 0;
    transition: opacity .3s;
    display: flex;
    justify-content: center;
    margin: 0;
    padding: 0;
    list-style: none
}

._item_11k18_191:hover ._social_11k18_219 {
    opacity: 1
}

._socialItem_11k18_231 {
    margin: 0 10px;
    font-size: 25px
}

._buttonsWrapper_wtgtw_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_wtgtw_138 {
    margin-bottom: 16px
}

._spaceAfter--small_wtgtw_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_wtgtw_144 {
    margin-bottom: 30px
}

._space_wtgtw_138 {
    margin-bottom: 10px;
    padding: 0 50px
}

._teamContainer_wtgtw_170 {
    padding: 80px 0
}

._imgContainer_wtgtw_174 {
    width: 100%;
    max-width: 150px;
    padding-top: inherit !important;
    margin: 0 auto 15px
}

._imgContainer_wtgtw_174 ._imageWrapper_wtgtw_180 {
    border-radius: 50%
}

._imgContainer_wtgtw_174 img {
    aspect-ratio: 1/1
}

._teamTitle_wtgtw_187 {
    text-align: center;
    margin: 0 0 50px
}

._teamTitle_wtgtw_187 p {
    width: 60%;
    margin: 0 auto
}

._item_wtgtw_196 {
    transition: all .5s;
    padding: 4px 0
}

._item_wtgtw_196:hover {
    box-shadow: 0 0 8px #11161a29, 0 4px 8px #11161a14, 0 8px 16px #11161a14;
    border-radius: 10px
}

._itemInfo_wtgtw_205 {
    width: 85%;
    text-align: center;
    margin: 0 auto
}

._itemInfo_wtgtw_205 i,
._itemInfo_wtgtw_205 svg {
    margin: 0 6px;
    color: var(--theme-color-brand1);
    font-weight: lighter;
    transition: .5s all
}

._social_wtgtw_218 {
    display: flex;
    opacity: 0;
    transition: opacity .3s;
    justify-content: center;
    list-style: none;
    padding: 0
}

._item_wtgtw_196:hover ._social_wtgtw_218 {
    opacity: 1
}

._socialItem_wtgtw_229 {
    margin: 0 10px;
    font-size: 25px
}

._buttonsWrapper_1fmb7_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1fmb7_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1fmb7_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1fmb7_144 {
    margin-bottom: 30px
}

._space_1fmb7_138 {
    margin-bottom: 10px
}

._teamContainer_1fmb7_169 {
    padding: 80px 0
}

._imgContainer_1fmb7_173 {
    margin: 0 auto
}

._imgContainer_1fmb7_173 img {
    aspect-ratio: 1/1
}

._teamTitle_1fmb7_180 {
    text-align: center;
    margin-bottom: 50px
}

._teamTitle_1fmb7_180 h2 {
    display: inline-block
}

._teamTitle_1fmb7_180 p {
    width: auto;
    margin: 0 auto
}

._item_1fmb7_192 {
    padding: 0
}

._item_1fmb7_192 ._itemInfo_1fmb7_195 {
    --text-color: #000;
    text-align: center;
    margin: 0 auto;
    background-color: #fcfcfd;
    padding: 15px 5px;
    box-shadow: 0 5px 50px #615d5d33
}

._item_1fmb7_192 ._itemInfo_1fmb7_195 h4 {
    line-height: 1
}

._item_1fmb7_192 ._itemInfo_1fmb7_195 h6 {
    margin: 0
}

@media (min-width: 1200px) {
    ._item_1fmb7_192 {
        padding: 0 40px 25px
    }
}

._buttonsWrapper_1d8ed_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1d8ed_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1d8ed_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1d8ed_144 {
    margin-bottom: 30px
}

._teamsContainer_1d8ed_165 {
    padding: 50px 0 70px
}

._teamsContainer_1d8ed_165 img {
    aspect-ratio: 1/.8
}

._item_1d8ed_172 {
    width: 90%;
    margin: 10px 0
}

._item_1d8ed_172 ._itemInfo_1d8ed_176 h6 {
    flex-grow: 1
}

._item_1d8ed_172 ._itemInfo_1d8ed_176 i,
._item_1d8ed_172 ._itemInfo_1d8ed_176 svg {
    color: var(--theme-color-brand1)
}

._social_1d8ed_184 {
    display: flex;
    list-style: none;
    padding-left: 0;
    margin-bottom: 0
}

._buttonsWrapper_w826x_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_w826x_138 {
    margin-bottom: 16px
}

._spaceAfter--small_w826x_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_w826x_144 {
    margin-bottom: 30px
}

._teamContainer_w826x_165 {
    padding: 50px 0 70px
}

._item_w826x_169 {
    margin: 10px 0
}

._item_w826x_169 ._imgContainer_w826x_172 {
    width: 100px;
    height: 100px
}

._item_w826x_169 ._imgContainer_w826x_172 ._imageWrapper_w826x_176 {
    border-radius: 50%
}

._item_w826x_169 ._imgContainer_w826x_172 img {
    aspect-ratio: 1/1
}

._buttonsWrapper_r5o71_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_r5o71_138 {
    margin-bottom: 16px
}

._spaceAfter--small_r5o71_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_r5o71_144 {
    margin-bottom: 30px
}

._teamContainer_r5o71_165 {
    padding: 50px 0 70px;
    text-align: center
}

._teamContainer_r5o71_165 ._teamHeader_r5o71_169 {
    width: 60%;
    margin: 0 auto 40px
}

@media (max-width: 992px) {
    ._teamContainer_r5o71_165 ._teamHeader_r5o71_169 {
        width: 100%
    }
}

._teamContainer_r5o71_165 ._teamHeader_r5o71_169 h2 {
    text-align: center
}

._item_r5o71_182 {
    margin: 10px 0
}

._item_r5o71_182 ._itemInfo_r5o71_185 {
    margin: 14px 0
}

._item_r5o71_182 ._itemInfo_r5o71_185 i,
._item_r5o71_182 ._itemInfo_r5o71_185 svg {
    color: var(--theme-color-brand1)
}

._social_r5o71_193 {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0 auto;
    width: -moz-fit-content;
    width: fit-content
}

._imgContainer_r5o71_201 img {
    aspect-ratio: 1/1
}

._buttonsWrapper_1rfpx_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1rfpx_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1rfpx_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1rfpx_144 {
    margin-bottom: 30px
}

._teamContainer_1rfpx_165 {
    padding: 50px 0 70px;
    text-align: center
}

._teamContainer_1rfpx_165 ._teamHeader_1rfpx_169 {
    width: 60%;
    margin: 0 auto 40px
}

@media (max-width: 992px) {
    ._teamContainer_1rfpx_165 ._teamHeader_1rfpx_169 {
        width: 100%
    }
}

._teamContainer_1rfpx_165 ._teamHeader_1rfpx_169 h2 {
    text-align: center
}

._item_1rfpx_182 {
    margin: 10px 0
}

._item_1rfpx_182 ._itemInfo_1rfpx_185 {
    margin: 14px 0
}

._item_1rfpx_182 ._itemInfo_1rfpx_185 h4 {
    min-width: 200px
}

._item_1rfpx_182 ._itemInfo_1rfpx_185 i,
._item_1rfpx_182 ._itemInfo_1rfpx_185 svg {
    color: var(--theme-color-brand1)
}

._social_1rfpx_196 {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0 auto;
    width: -moz-fit-content;
    width: fit-content
}

._imgContainer_1rfpx_204 img {
    aspect-ratio: 1/1
}

._buttonsWrapper_p6lyt_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_p6lyt_138 {
    margin-bottom: 16px
}

._spaceAfter--small_p6lyt_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_p6lyt_144 {
    margin-bottom: 30px
}

._imgContainer_p6lyt_165 {
    width: 72px;
    height: 72px;
    position: relative;
    margin: 0 10px -30px;
    border-radius: 50%;
    border: 4px solid #dfe0e1
}

._imgContainer_p6lyt_165 ._imageWrapper_p6lyt_174 {
    border-radius: 50%
}

._imgContainer_p6lyt_165 img {
    aspect-ratio: 1/1
}

._container_p6lyt_181 {
    padding: 50px 0 70px
}

._testmonilsTitle_p6lyt_185 {
    text-align: center;
    margin-bottom: 45px
}

._testmonilsTitle_p6lyt_185 p {
    width: auto;
    margin: 0 auto
}

._item_p6lyt_194 {
    --text-color: #000
}

._item_p6lyt_194 ._itemInfo_p6lyt_197 {
    line-height: 2;
    margin: 0 auto 40px;
    padding: 30px 30px 16px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 6px 50px #0003
}

._buttonsWrapper_ksioh_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_ksioh_138 {
    margin-bottom: 16px
}

._spaceAfter--small_ksioh_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_ksioh_144 {
    margin-bottom: 30px
}

._container_ksioh_165 {
    padding: 100px 0
}

._imgContainer_ksioh_169 {
    width: 70px;
    height: 70px;
    position: relative;
    margin: 0 10px;
    border: 4px solid #eff0f1;
    border-radius: 50%
}

._imgContainer_ksioh_169 ._imageWrapper_ksioh_177 {
    border-radius: 50%
}

._imgContainer_ksioh_169 img {
    aspect-ratio: 1/1
}

._auther_ksioh_184 h4 {
    font-weight: 600;
    color: #1c1c1c;
    margin: 2px 24px
}

._auther_ksioh_184 h6 {
    margin: 0 24px;
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

._testmonilsTitle_ksioh_196 {
    text-align: center;
    margin-bottom: 50px
}

._testmonilsTitle_ksioh_196 h2 {
    margin-bottom: 10px;
    color: #1c1c1c;
    font-weight: 700
}

._testmonilsTitle_ksioh_196 p {
    width: auto;
    margin: 0 auto;
    color: #585757;
    font-size: 16px
}

._item_ksioh_212 {
    padding: 10px
}

._item_ksioh_212 ._itemInfo_ksioh_215 {
    border-radius: 4px;
    width: 100%;
    margin: 0 auto -10%;
    padding: 30px;
    background-color: #fff;
    box-shadow: 0 6px 50px #0003
}

._item_ksioh_212 ._itemInfo_ksioh_215 p {
    font-size: 14px;
    color: #585757
}

._columnStyle_ksioh_229 {
    margin-bottom: 20px
}

._buttonsWrapper_xlmla_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_xlmla_138 {
    margin-bottom: 16px
}

._spaceAfter--small_xlmla_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_xlmla_144 {
    margin-bottom: 30px
}

._imgContainer_xlmla_165 {
    width: 90px;
    height: 90px;
    position: relative;
    margin: 0 auto;
    padding: 4px
}

._imgContainer_xlmla_165:before {
    border-radius: 50%;
    opacity: .5;
    content: "";
    display: block;
    position: absolute;
    inset: 0
}

._imgContainer_xlmla_165 ._imageWrapper_xlmla_183 {
    border-radius: 50%
}

._imgContainer_xlmla_165 img {
    aspect-ratio: 1/1
}

._container_xlmla_190 {
    padding: 100px 0
}

._testmonilsTitle_xlmla_194 {
    text-align: center;
    margin-bottom: 45px
}

._testmonilsTitle_xlmla_194 h2 {
    margin-bottom: 10px;
    color: #1c1c1c;
    font-weight: 700
}

._testmonilsTitle_xlmla_194 p {
    width: auto;
    margin: 0 auto;
    color: #585757;
    font-size: 16px
}

._item_xlmla_210 {
    padding: 0 15px;
    margin-bottom: 40px
}

._item_xlmla_210 ._itemInfo_xlmla_214 {
    padding: 26px 30px 30px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 6px 50px #0003;
    text-align: center
}

._item_xlmla_210 ._itemInfo_xlmla_214 h4 {
    margin: 5px 0;
    font-weight: 600;
    color: #1c1c1c
}

._item_xlmla_210 ._itemInfo_xlmla_214 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757;
    margin-bottom: 12px
}

._item_xlmla_210 ._itemInfo_xlmla_214 p {
    font-size: 14px;
    color: #585757
}

._buttonsWrapper_onfgf_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_onfgf_138 {
    margin-bottom: 16px
}

._spaceAfter--small_onfgf_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_onfgf_144 {
    margin-bottom: 30px
}

._imgContainer_onfgf_165 {
    width: 70px;
    height: 70px;
    position: relative
}

._imgContainer_onfgf_165 ._imageWrapper_onfgf_170 {
    border-radius: 50%
}

._imgContainer_onfgf_165 img {
    aspect-ratio: 1/1
}

._container_onfgf_177 {
    padding: 100px 0
}

._testmonilsTitle_onfgf_181 {
    text-align: center;
    margin-bottom: 45px
}

._testmonilsTitle_onfgf_181 h2 {
    margin-bottom: 15px;
    font-weight: 700;
    color: #27272a
}

._testmonilsTitle_onfgf_181 p {
    color: #ddd;
    font-size: 16px;
    width: 80%;
    margin: 0 auto
}

._item_onfgf_197 {
    justify-content: center;
    margin-bottom: 25px
}

._item_onfgf_197 ._itemInfo_onfgf_201 {
    padding: 45px 30px 22px;
    border-radius: 5.5px;
    background-color: #fff;
    box-shadow: 0 0 8px #11161a29, 0 4px 8px #11161a14, 0 8px 16px #11161a14
}

._item_onfgf_197 ._itemInfo_onfgf_201 h4 {
    margin: 10px 12px;
    font-weight: 600;
    color: #1c1c1c
}

._item_onfgf_197 ._itemInfo_onfgf_201 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757;
    margin: 10px 0
}

._item_onfgf_197 ._itemInfo_onfgf_201 p {
    font-size: 14px;
    color: #585757;
    width: 90%;
    line-height: 1.5
}

._item_onfgf_197 ._itemInfo_onfgf_201 ._seprator_onfgf_225 {
    height: 1px;
    opacity: .5;
    width: 100%;
    border: solid 1px #ddd;
    margin-top: 40px;
    margin-bottom: 14px
}

._contentLine_onfgf_234 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center
}

._buttonsWrapper_1xwhw_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1xwhw_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1xwhw_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1xwhw_144 {
    margin-bottom: 30px
}

._imgContainer_1xwhw_165 {
    width: 60px;
    height: 60px
}

._imgContainer_1xwhw_165 ._imageWrapper_1xwhw_169 {
    border-radius: 50%
}

._imgContainer_1xwhw_165 img {
    aspect-ratio: 1/1
}

._testmonilsContainer_1xwhw_176 {
    margin-top: -130px;
    padding-bottom: 100px
}

._testmonils_1xwhw_176 {
    height: auto
}

._testmonilsTitle_1xwhw_185 {
    background-color: #fff;
    text-align: center;
    padding: 100px 100px 150px;
    margin-bottom: 50px
}

._testmonilsTitle_1xwhw_185 h2 {
    margin-bottom: 15px;
    color: #1c1c1c;
    font-weight: 700
}

._testmonilsTitle_1xwhw_185 p {
    width: auto;
    margin: 0 auto;
    color: #585757;
    font-size: 16px
}

._item_1xwhw_203 {
    margin-bottom: 35px;
    display: flex;
    width: 100%;
    justify-content: center
}

._item_1xwhw_203 ._itemInfo_1xwhw_209 {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    padding: 50px 30px 30px;
    border-radius: 5.5px;
    -webkit-backdrop-filter: blur(9.2px);
    backdrop-filter: blur(9.2px);
    background-color: #fff;
    box-shadow: 11px 11px 37px #7979794d
}

._item_1xwhw_203 ._itemInfo_1xwhw_209 h4 {
    font-weight: 600;
    color: #1c1c1c;
    margin: 5px 10px 8px 0
}

.rtl ._item_1xwhw_203 ._itemInfo_1xwhw_209 h4 {
    margin: 5px 0 8px 10px
}

._item_1xwhw_203 ._itemInfo_1xwhw_209 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757;
    margin-top: 10px
}

._item_1xwhw_203 ._itemInfo_1xwhw_209 p {
    font-size: 14px;
    color: #585757;
    display: inline-block;
    width: 100%;
    line-height: 25px;
    margin-bottom: 17px
}

._item_1xwhw_203 ._itemInfo_1xwhw_209 i {
    color: #2c925a;
    font-size: 40px;
    margin-bottom: 5px
}

._item_1xwhw_203 ._itemInfo_1xwhw_209 ._contentLine_1xwhw_247 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center
}

._buttonsWrapper_1lr4g_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1lr4g_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1lr4g_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1lr4g_144 {
    margin-bottom: 30px
}

._imgContainer_1lr4g_165 {
    width: 50px;
    height: 60px
}

._imgContainer_1lr4g_165 ._imageWrapper_1lr4g_169 {
    border-radius: 50%
}

._imgContainer_1lr4g_165 img {
    aspect-ratio: 1/1
}

._container_1lr4g_176 {
    padding: 70px 0;
    position: relative
}

._testmonilsTitle_1lr4g_181 {
    text-align: center;
    padding: 0 0 50px
}

._testmonilsTitle_1lr4g_181 h2 {
    margin-bottom: 12px;
    color: #1c1c1c;
    font-weight: 700
}

._testmonilsTitle_1lr4g_181 p {
    width: auto;
    margin: 0 auto;
    color: #585757;
    font-size: 16px
}

._item_1lr4g_197 {
    padding: 10px 26px;
    display: flex;
    justify-content: center;
    position: relative;
    margin: 30px 0
}

._item_1lr4g_197 ._overlay_1lr4g_204 {
    width: 90%;
    height: auto;
    border-radius: 5.5px;
    -webkit-backdrop-filter: blur(9.2px);
    backdrop-filter: blur(9.2px);
    background-image: linear-gradient(120deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    position: absolute;
    top: 47px;
    left: 27px;
    bottom: 0
}

.rtl ._item_1lr4g_197 ._overlay_1lr4g_204 {
    right: 27px;
    left: 0;
    background-image: linear-gradient(320deg, var(--theme-color-brand1), var(--theme-color-secondary1))
}

._item_1lr4g_197 ._itemInfo_1lr4g_221 {
    min-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 50px 30px 30px;
    border-radius: 5.5px;
    -webkit-backdrop-filter: blur(9.2px);
    backdrop-filter: blur(9.2px);
    background-color: #fff;
    box-shadow: 11px 11px 37px #7979794d;
    z-index: 3
}

._item_1lr4g_197 ._itemInfo_1lr4g_221 h4 {
    font-weight: 600;
    color: #1c1c1c;
    margin: -3px 10px 8px 0
}

.rtl ._item_1lr4g_197 ._itemInfo_1lr4g_221 h4 {
    margin: -3px 0 8px 10px
}

._item_1lr4g_197 ._itemInfo_1lr4g_221 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

._item_1lr4g_197 ._itemInfo_1lr4g_221 p {
    display: inline-block;
    width: 92%;
    line-height: 2;
    margin-top: 24px;
    font-size: 14px;
    color: #585757
}

._item_1lr4g_197 ._itemInfo_1lr4g_221 i {
    color: #2c925a;
    font-size: 40px
}

._item_1lr4g_197 ._itemInfo_1lr4g_221 ._contentLine_1lr4g_258 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center
}

._imgContainer_1uorz_1 {
    width: 50px;
    height: 60px;
    position: relative
}

._imgContainer_1uorz_1 ._imageWrapper_1uorz_6 {
    border-radius: 50%
}

._imgContainer_1uorz_1 img {
    aspect-ratio: 1/1
}

._container_1uorz_13 {
    padding: 110px 0;
    position: relative
}

._testmonilsTitle_1uorz_18 {
    position: absolute;
    top: 30%
}

._testmonilsTitle_1uorz_18 h1 {
    margin-bottom: 14px;
    display: inline-block;
    color: #fff;
    font-weight: 300;
    width: 85%
}

._testmonilsTitle_1uorz_18 p {
    width: 85%;
    color: #d9d9d9;
    font-weight: 300
}

._testmonilContainer_1uorz_35 {
    display: flex;
    width: 100%;
    justify-content: space-around;
    flex-wrap: wrap
}

._item_1uorz_43 {
    flex: 0 0 46%;
    padding: 10px 0;
    margin: 10px;
    width: 50%
}

._item_1uorz_43 ._itemInfo_1uorz_49 {
    min-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 30px;
    border-radius: 5.5px;
    -webkit-backdrop-filter: blur(9.2px);
    backdrop-filter: blur(9.2px);
    background-color: #fff;
    box-shadow: 11px 11px 37px #7979794d
}

._item_1uorz_43 ._itemInfo_1uorz_49 h4 {
    color: #031d5b;
    display: inline-block;
    margin: auto 10px
}

.rtl ._item_1uorz_43 ._itemInfo_1uorz_49 h4 {
    margin: auto 10px
}

._item_1uorz_43 ._itemInfo_1uorz_49 h6 {
    color: #031d5b;
    width: 51%;
    margin: -12px 44px 0;
    opacity: .4
}

._item_1uorz_43 ._itemInfo_1uorz_49 p {
    color: #5b6c94;
    display: inline-block;
    width: 92%;
    line-height: 26px;
    opacity: .7
}

._styleColumn_1uorz_82 {
    display: flex
}

@media (max-width: 812px) {
    ._testmonilsTitle_1uorz_18 {
        position: static;
        width: 100%
    }
}

@media (max-width: 768px) {
    ._imgContainer_1uorz_1 {
        width: 55px;
        height: 55px;
        position: relative;
        margin: 6px 26px
    }
    ._testmonilsTitle_1uorz_18 {
        width: 100%
    }
}

@media (max-width: 375px) {
    ._item_1uorz_43 {
        flex: 0 0 90%;
        width: 100%;
        margin-bottom: 0
    }
    ._testmonilsTitle_1uorz_18 p {
        width: 100%;
        margin-bottom: 20px
    }
}

._buttonsWrapper_c31aa_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_c31aa_138 {
    margin-bottom: 16px
}

._spaceAfter--small_c31aa_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_c31aa_144 {
    margin-bottom: 30px
}

._testimonialsContainer_c31aa_165 {
    padding: 136px 0
}

._testmonilsTitle_c31aa_169 {
    text-align: center;
    padding: 0 0 50px
}

._testmonilsTitle_c31aa_169 h2 {
    width: 55%;
    margin: 0 auto 10px;
    color: #1c1c1c;
    font-weight: 700
}

._testmonilsTitle_c31aa_169 p {
    width: 55%;
    margin: 0 auto 20px;
    color: #585757;
    font-size: 16px
}

._item_c31aa_186 {
    padding: 45px 40px;
    background-image: linear-gradient(320deg, var(--theme-color-brand1), var(--theme-color-secondary1));
    box-shadow: 11px 11px 37px #7979794d;
    border-radius: 5.5px;
    margin-bottom: 30px
}

._item_c31aa_186 ._itemInfo_c31aa_193 {
    display: flex;
    flex-direction: column;
    align-items: flex-start
}

._item_c31aa_186 ._itemInfo_c31aa_193 h4 {
    margin: 0 12px 10px 0;
    color: #fff;
    display: inline-block;
    font-weight: 600
}

.rtl ._item_c31aa_186 ._itemInfo_c31aa_193 h4 {
    margin-right: 0;
    margin-left: 12px
}

._item_c31aa_186 ._itemInfo_c31aa_193 h6 {
    color: #c4c4c4;
    font-size: 15px;
    font-weight: 600;
    margin: 10px 0
}

._item_c31aa_186 ._itemInfo_c31aa_193 p {
    color: #fff;
    display: inline-block;
    width: 100%;
    line-height: 1.5;
    margin-bottom: 20px;
    font-size: 14px
}

._item_c31aa_186 ._itemInfo_c31aa_193 i {
    color: #fff;
    font-size: 50px
}

._contentLine_c31aa_229 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center
}

._styleColumn_c31aa_236:nth-child(2n) ._item_c31aa_186 {
    background: #fff none
}

._styleColumn_c31aa_236:nth-child(2n) ._item_c31aa_186 p {
    font-size: 14px;
    color: #585757
}

._styleColumn_c31aa_236:nth-child(2n) ._item_c31aa_186 i {
    background-image: linear-gradient(90deg, var(--theme-color-brand1) 0%, var(--theme-color-secondary1) 100%);
    color: transparent;
    -webkit-background-clip: text
}

._styleColumn_c31aa_236:nth-child(2n) ._item_c31aa_186 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._styleColumn_c31aa_236:nth-child(2n) ._item_c31aa_186 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

._buttonsWrapper_1imvp_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1imvp_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1imvp_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1imvp_144 {
    margin-bottom: 30px
}

._testimonialsContainer_1imvp_165 {
    text-align: center;
    padding: 50px 0
}

._testimonialsContainer_1imvp_165 ._sectionHeader_1imvp_169 {
    text-align: center;
    margin-bottom: 40px
}

._testimonialsContainer_1imvp_165 ._sectionHeader_1imvp_169 h2 {
    color: #1c1c1c;
    font-weight: 700
}

._testimonialsContainer_1imvp_165 ._sectionHeader_1imvp_169 p {
    color: #585757;
    font-size: 16px
}

._item_1imvp_182 {
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    height: 100%
}

._item_1imvp_182 i {
    font-size: 48px;
    font-weight: 700;
    color: var(--theme-color-brand1)
}

._item_1imvp_182 p {
    font-size: 14px;
    color: #585757;
    flex-grow: 1
}

._item_1imvp_182 ._imageContainer_1imvp_198 {
    width: 150px;
    margin: 10px auto 15px
}

._item_1imvp_182 ._imageContainer_1imvp_198 img {
    aspect-ratio: 2/1
}

._item_1imvp_182 h4 {
    font-weight: 600;
    color: #1c1c1c;
    margin-bottom: 10px
}

._item_1imvp_182 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

._buttonsWrapper_anh0n_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_anh0n_138 {
    margin-bottom: 16px
}

._spaceAfter--small_anh0n_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_anh0n_144 {
    margin-bottom: 30px
}

._sectionContainer_anh0n_165 {
    padding: 50px 0
}

._sectionContainer_anh0n_165 ._sectionHeader_anh0n_168 {
    text-align: center;
    margin-bottom: 40px
}

._sectionContainer_anh0n_165 ._sectionHeader_anh0n_168 h2 {
    color: #1c1c1c;
    font-weight: 700
}

._sectionContainer_anh0n_165 ._sectionHeader_anh0n_168 p {
    color: #585757;
    font-size: 16px
}

._item_anh0n_181 {
    margin: 10px 0
}

._item_anh0n_181 i {
    font-size: 48px;
    font-weight: 700;
    color: var(--theme-color-brand1)
}

._item_anh0n_181 p {
    font-weight: 700;
    font-size: 26px;
    color: #1c1c1c;
    line-height: 30px;
    letter-spacing: -.8px;
    width: 90%;
    margin-bottom: 30px
}

._item_anh0n_181 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._item_anh0n_181 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

._buttonsWrapper_117hk_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_117hk_138 {
    margin-bottom: 16px
}

._spaceAfter--small_117hk_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_117hk_144 {
    margin-bottom: 30px
}

._testimonialsContainer_117hk_165 {
    padding: 50px 0
}

._testimonialsContainer_117hk_165 ._sectionHeader_117hk_168 {
    text-align: center;
    margin-bottom: 40px
}

._testimonialsContainer_117hk_165 ._sectionHeader_117hk_168 h2 {
    color: #1c1c1c;
    font-weight: 700
}

._testimonialsContainer_117hk_165 ._sectionHeader_117hk_168 p {
    color: #585757;
    font-size: 16px
}

._itemContainer_117hk_181 {
    height: 100%;
    padding: 10px 0
}

._itemContainer_117hk_181 ._item_117hk_181 {
    border: 1px solid #dbdbdb;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between
}

._itemContainer_117hk_181 ._item_117hk_181 ._itemHeader_117hk_194 p {
    font-size: 14px;
    color: #585757
}

._itemContainer_117hk_181 ._item_117hk_181 ._itemFooter_117hk_198 {
    display: flex
}

._itemContainer_117hk_181 ._item_117hk_181 ._itemFooter_117hk_198 ._imgContainer_117hk_201 {
    width: 60px;
    height: 60px;
    margin: 0 10px 0 0
}

._itemContainer_117hk_181 ._item_117hk_181 ._itemFooter_117hk_198 ._imgContainer_117hk_201 ._imageWrapper_117hk_206 {
    border-radius: 50%
}

._itemContainer_117hk_181 ._item_117hk_181 ._itemFooter_117hk_198 ._imgContainer_117hk_201 img {
    aspect-ratio: 1/1
}

._itemContainer_117hk_181 ._item_117hk_181 ._itemFooter_117hk_198 ._footerContent_117hk_212 {
    flex-grow: 1;
    max-width: 75%
}

._itemContainer_117hk_181 ._item_117hk_181 ._itemFooter_117hk_198 ._footerContent_117hk_212 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._itemContainer_117hk_181 ._item_117hk_181 ._itemFooter_117hk_198 ._footerContent_117hk_212 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

.rtl ._imgContainer_117hk_201 {
    margin: 0 0 0 10px !important
}

._buttonsWrapper_1jqrd_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_1jqrd_138 {
    margin-bottom: 16px
}

._spaceAfter--small_1jqrd_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_1jqrd_144 {
    margin-bottom: 30px
}

._testimonialsContainer_1jqrd_165 {
    text-align: center;
    padding: 50px 0
}

._testimonialsContainer_1jqrd_165 ._sectionHeader_1jqrd_169 {
    text-align: center;
    margin-bottom: 40px
}

._testimonialsContainer_1jqrd_165 ._sectionHeader_1jqrd_169 h2 {
    color: #1c1c1c;
    font-weight: 700
}

._testimonialsContainer_1jqrd_165 ._sectionHeader_1jqrd_169 p {
    color: #585757;
    font-size: 16px
}

._itemContainer_1jqrd_182 {
    height: 100%;
    padding: 10px 0
}

._itemContainer_1jqrd_182 ._item_1jqrd_182 {
    border: 1px solid #dbdbdb;
    border-radius: 8px;
    padding: 20px;
    height: 100%
}

._itemContainer_1jqrd_182 ._item_1jqrd_182 ._imgContainer_1jqrd_192 {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px
}

._itemContainer_1jqrd_182 ._item_1jqrd_182 ._imgContainer_1jqrd_192 ._imageWrapper_1jqrd_197 {
    border-radius: 50%
}

._itemContainer_1jqrd_182 ._item_1jqrd_182 ._imgContainer_1jqrd_192 img {
    aspect-ratio: 1/1
}

._itemContainer_1jqrd_182 ._item_1jqrd_182 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._itemContainer_1jqrd_182 ._item_1jqrd_182 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

._itemContainer_1jqrd_182 ._item_1jqrd_182 p {
    font-size: 14px;
    color: #585757;
    margin-bottom: 0
}

._buttonsWrapper_iya2i_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_iya2i_138 {
    margin-bottom: 16px
}

._spaceAfter--small_iya2i_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_iya2i_144 {
    margin-bottom: 30px
}

._testimonialsContainer_iya2i_165 {
    padding: 50px 0;
    height: -moz-fit-content;
    height: fit-content
}

._testimonialsContainer_iya2i_165 ._sectionHeader_iya2i_169 {
    text-align: center;
    margin-bottom: 40px
}

._testimonialsContainer_iya2i_165 ._sectionHeader_iya2i_169 h2 {
    color: #1c1c1c;
    font-weight: 700
}

._testimonialsContainer_iya2i_165 ._sectionHeader_iya2i_169 p {
    color: #585757;
    font-size: 16px
}

._itemContainer_iya2i_182 {
    height: 100%;
    padding: 10px 0
}

._itemContainer_iya2i_182 ._item_iya2i_182 {
    border: 1px solid #dbdbdb;
    border-radius: 8px;
    height: 100%;
    padding: 20px
}

._itemContainer_iya2i_182 ._item_iya2i_182 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._itemContainer_iya2i_182 ._item_iya2i_182 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

._itemContainer_iya2i_182 ._item_iya2i_182 p {
    font-size: 14px;
    color: #585757
}

._buttonsWrapper_exyv6_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_exyv6_138 {
    margin-bottom: 16px
}

._spaceAfter--small_exyv6_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_exyv6_144 {
    margin-bottom: 30px
}

._testimonialsContainer_exyv6_165 {
    padding: 50px 0 65px
}

._testimonialsContainer_exyv6_165 ._sectionHeader_exyv6_168 {
    text-align: center;
    margin-bottom: 40px
}

._testimonialsContainer_exyv6_165 ._sectionHeader_exyv6_168 h2 {
    color: #1c1c1c;
    font-weight: 700
}

._testimonialsContainer_exyv6_165 ._sectionHeader_exyv6_168 p {
    color: #585757;
    font-size: 16px
}

._testimonialsContainer_exyv6_165 ._itemContainer_exyv6_180 {
    text-align: left
}

._item_exyv6_180 {
    width: 75%;
    margin: 10px auto;
    padding: 10px 20px
}

@media (max-width: 992px) {
    ._item_exyv6_180 {
        width: 100%
    }
}

._item_exyv6_180 i {
    font-size: 48px;
    font-weight: 700;
    color: var(--theme-color-brand1)
}

._item_exyv6_180 p {
    font-weight: 700;
    font-size: 26px;
    color: #1c1c1c;
    line-height: 30px;
    letter-spacing: -.8px;
    margin-bottom: 30px
}

._item_exyv6_180 h4 {
    font-weight: 600;
    color: #1c1c1c
}

._item_exyv6_180 h6 {
    font-size: 15px;
    font-weight: 600;
    color: #585757
}

.rtl ._itemContainer_exyv6_180 {
    text-align: right
}

._imgContainer_10u7q_1 {
    width: 100px;
    height: 100px;
    display: flex;
    flex-direction: column;
    margin-bottom: 8px
}

._imgContainer_10u7q_1 ._autherImage_10u7q_8 {
    border-radius: 50%
}

._container_10u7q_12 {
    padding: 120px 0;
    background-color: #fafbfc
}

._testmonilsTitle_10u7q_17 {
    text-align: center;
    padding: 0 0 50px
}

._testmonilsTitle_10u7q_17 h1 {
    margin-bottom: 10px
}

._testmonilsTitle_10u7q_17 p {
    width: 55%;
    margin: 0 auto;
    color: #999
}

._item_10u7q_30 {
    padding: 12px 0;
    margin: 18px
}

@media (max-width: 700px) {
    ._item_10u7q_30 {
        border: 1px solid rgba(188, 188, 188, .13);
        padding: 20px;
        margin: 12px 0;
        text-align: justify
    }
}

._item_10u7q_30 ._itemInfo_10u7q_42 {
    width: 90%;
    display: flex;
    flex-direction: column
}

._item_10u7q_30 ._itemInfo_10u7q_42 p {
    color: #666;
    margin-bottom: 10px
}

._item_10u7q_30 ._itemInfo_10u7q_42 h4 {
    color: #333;
    margin: 4px 0 12px;
    display: inline-block
}

._item_10u7q_30 ._itemInfo_10u7q_42 h6 {
    color: #999;
    font-weight: 400;
    display: table
}

._auther_1pgdt_1 {
    margin: 12px 0
}

._auther_1pgdt_1 ._imgContainer_1pgdt_4 {
    width: 80px;
    height: 80px;
    padding-top: inherit !important;
    margin: 0 auto
}

._auther_1pgdt_1 ._imgContainer_1pgdt_4 ._autherImage_1pgdt_10 {
    border-radius: 50%
}

._auther_1pgdt_1 h4 {
    text-align: center;
    color: #333;
    margin: 8px 0
}

._auther_1pgdt_1 h6 {
    text-align: center;
    color: #999;
    width: 58%;
    margin: 0 auto;
    line-height: 1.5
}

@media (max-width: 700px) {
    ._auther_1pgdt_1 h6 {
        width: 100%
    }
}

._container_1pgdt_31 {
    padding: 120px 0;
    background-color: #fafbfc
}

._testmonilsTitle_1pgdt_36 {
    text-align: center;
    padding: 0 0 50px
}

._testmonilsTitle_1pgdt_36 h1 {
    margin-bottom: 10px
}

._testmonilsTitle_1pgdt_36 p {
    width: 55%;
    margin: 0 auto;
    color: #999
}

._item_1pgdt_49 {
    padding: 12px 0
}

@media (max-width: 700px) {
    ._item_1pgdt_49 {
        border: 1px solid rgba(188, 188, 188, .13);
        margin: 10px 0;
        text-align: justify
    }
}

._itemInfo_1pgdt_60 {
    width: 70%;
    margin: 0 auto 4%;
    line-height: 2
}

._itemInfo_1pgdt_60 p {
    color: #666;
    text-align: center
}

._auther_1jogr_1 {
    margin: 12px 0
}

._auther_1jogr_1 ._imgContainer_1jogr_4 {
    width: 80px;
    height: 80px;
    padding-top: inherit !important;
    margin: 0 auto
}

._auther_1jogr_1 ._imgContainer_1jogr_4 ._autherImage_1jogr_10 {
    border-radius: 50%
}

._auther_1jogr_1 h4 {
    text-align: center;
    color: #333;
    margin: 6px
}

._auther_1jogr_1 h6 {
    text-align: center;
    color: #999
}

._container_1jogr_23 {
    padding: 80px 0;
    background-color: #fafbfc
}

._testmonilsTitle_1jogr_28 {
    text-align: center;
    padding: 0 0 50px
}

._testmonilsTitle_1jogr_28 h1 {
    margin-bottom: 10px;
    display: inline-block;
    padding: 5px
}

._testmonilsTitle_1jogr_28 p {
    width: 55%;
    margin: 0 auto;
    color: #999
}

._item_1jogr_43 {
    margin: 20px 0
}

._itemInfo_1jogr_47 {
    width: 90%;
    margin: 0 auto 2%;
    line-height: 2;
    padding: 14px 14px 0;
    border: 1px solid #dedede
}

._itemInfo_1jogr_47 p {
    color: #666;
    text-align: center
}

._sectionBody_1sft3_1 {
    padding-bottom: 104px;
    position: relative
}

._section_1sft3_1 {
    height: 508px
}

._infoDiv_1sft3_10 {
    width: 374px;
    min-height: 434px;
    position: absolute;
    right: 139px;
    top: 116px;
    padding-left: 25px;
    padding-top: 35px;
    padding-bottom: 85px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 8px 38px #0000001a
}

._infoDiv_1sft3_10 h1 {
    margin-top: 10px;
    margin-bottom: 17px;
    color: #0e054a;
    display: inline-block;
    width: 80%
}

._infoDiv_1sft3_10 p {
    opacity: .6;
    color: #0e054a;
    width: 80%
}

:root {
    --wt-brand-color: var(--theme-color-primary, #fda942);
    --wt-light-shade-color: #fff;
    --wt-btn-secondary-active-color: #ac9394;
    --wt-btn-secondary-active-color-light: #ac9394;
    --wt-btn-secondary-active-bg-color: #fff;
    --wt-btn-secondary-active-bg-color-light: #fff;
    --wt-btn-secondary-active-bg-gradient-colors: transparent, transparent;
    --wt-btn-secondary-active-bg-gradient-colors-light: transparent, transparent;
    --wt-btn-secondary-active-font-weight: 700;
    --wt-btn-secondary-active-shadow-color: rgba(160, 148, 147, .15);
    --wt-btn-secondary-active-shadow-color-light: rgba(160, 148, 147, .15);
    --wt-btn-secondary-active-shadow-spread: 0;
    --wt-btn-secondary-active-shadow-blur: 12px;
    --wt-btn-secondary-active-shadow-y: 6px;
    --wt-btn-secondary-active-shadow-x: 0;
    --wt-btn-secondary-active-border-color: transparent;
    --wt-btn-secondary-active-border-color-light: transparent;
    --wt-btn-secondary-active-border-style: solid;
    --wt-btn-secondary-hover-color: #fda942;
    --wt-btn-secondary-hover-color-light: #fda942;
    --wt-btn-secondary-hover-bg-color: #fff;
    --wt-btn-secondary-hover-bg-color-light: #fff;
    --wt-btn-secondary-hover-bg-gradient-colors: transparent, transparent;
    --wt-btn-secondary-hover-bg-gradient-colors-light: transparent, transparent;
    --wt-btn-secondary-hover-font-weight: 700;
    --wt-btn-secondary-hover-shadow-color: rgba(160, 148, 147, .15);
    --wt-btn-secondary-hover-shadow-color-light: rgba(160, 148, 147, .15);
    --wt-btn-secondary-hover-shadow-spread: 0;
    --wt-btn-secondary-hover-shadow-blur: 12px;
    --wt-btn-secondary-hover-shadow-y: 6px;
    --wt-btn-secondary-hover-shadow-x: 0;
    --wt-btn-secondary-hover-border-color: transparent;
    --wt-btn-secondary-hover-border-color-light: transparent;
    --wt-btn-secondary-hover-border-style: solid;
    --wt-btn-secondary-color: #ac9394;
    --wt-btn-secondary-color-light: #ac9394;
    --wt-btn-secondary-bg-color: #fff;
    --wt-btn-secondary-bg-color-light: #fff;
    --wt-btn-secondary-bg-gradient-colors: transparent, transparent;
    --wt-btn-secondary-bg-gradient-colors-light: transparent, transparent;
    --wt-btn-secondary-font-weight: 700;
    --wt-btn-secondary-shadow-color: rgba(160, 148, 147, .15);
    --wt-btn-secondary-shadow-color-light: rgba(160, 148, 147, .15);
    --wt-btn-secondary-shadow-spread: 0;
    --wt-btn-secondary-shadow-blur: 12px;
    --wt-btn-secondary-shadow-y: 6px;
    --wt-btn-secondary-shadow-x: 0;
    --wt-btn-secondary-border-color: transparent;
    --wt-btn-secondary-border-color-light: transparent;
    --wt-btn-secondary-border-style: solid;
    --wt-btn-primary-active-color: #fff;
    --wt-btn-primary-active-color-light: #fff;
    --wt-btn-primary-active-bg-color: #fda942;
    --wt-btn-primary-active-bg-color-light: #fda942;
    --wt-btn-primary-active-bg-gradient-colors: transparent, transparent;
    --wt-btn-primary-active-bg-gradient-colors-light: transparent, transparent;
    --wt-btn-primary-active-font-weight: 700;
    --wt-btn-primary-active-shadow-color: rgba(128, 79, 71, .24);
    --wt-btn-primary-active-shadow-color-light: rgba(128, 79, 71, .24);
    --wt-btn-primary-active-shadow-spread: 0;
    --wt-btn-primary-active-shadow-blur: 12px;
    --wt-btn-primary-active-shadow-y: 6px;
    --wt-btn-primary-active-shadow-x: 0;
    --wt-btn-primary-active-border-color: transparent;
    --wt-btn-primary-active-border-color-light: transparent;
    --wt-btn-primary-active-border-style: solid;
    --wt-btn-primary-hover-color: #fff;
    --wt-btn-primary-hover-color-light: #fff;
    --wt-btn-primary-hover-bg-color: #fbf8ef;
    --wt-btn-primary-hover-bg-color-light: #fbf8ef;
    --wt-btn-primary-hover-bg-gradient-colors: transparent, transparent;
    --wt-btn-primary-hover-bg-gradient-colors-light: transparent, transparent;
    --wt-btn-primary-hover-font-weight: 700;
    --wt-btn-primary-hover-shadow-color: rgba(128, 79, 71, .24);
    --wt-btn-primary-hover-shadow-color-light: rgba(128, 79, 71, .24);
    --wt-btn-primary-hover-shadow-spread: 0;
    --wt-btn-primary-hover-shadow-blur: 12px;
    --wt-btn-primary-hover-shadow-y: 6px;
    --wt-btn-primary-hover-shadow-x: 0;
    --wt-btn-primary-hover-border-color: transparent;
    --wt-btn-primary-hover-border-color-light: transparent;
    --wt-btn-primary-hover-border-style: solid;
    --wt-btn-primary-color: var(--wt-light-shade-color);
    --wt-btn-primary-bg-color: var(--wt-brand-color, #fda942);
    --wt-btn-primary-color-light: #fff;
    --wt-btn-primary-bg-color-light: #fda942;
    --wt-btn-primary-bg-gradient-colors: transparent, transparent;
    --wt-btn-primary-bg-gradient-colors-light: transparent, transparent;
    --wt-btn-primary-font-weight: 700;
    --wt-btn-primary-shadow-color: rgba(128, 79, 71, .24);
    --wt-btn-primary-shadow-color-light: rgba(128, 79, 71, .24);
    --wt-btn-primary-shadow-spread: 0;
    --wt-btn-primary-shadow-blur: 12px;
    --wt-btn-primary-shadow-y: 6px;
    --wt-btn-primary-shadow-x: 0;
    --wt-btn-primary-border-color: transparent;
    --wt-btn-primary-border-color-light: transparent;
    --wt-btn-primary-border-style: solid;
    --wt-btn-font-family: "Hind", sans-serif;
    --wt-btn-font-style: initial;
    --wt-btn-text-transform: none;
    --wt-btn-letter-spacing: 0em;
    --wt-btn-sm-padding: 8px 24px;
    --wt-btn-sm-font-size: 14px;
    --wt-btn-sm-border-width: 0px;
    --wt-btn-sm-border-radius: 4px;
    --wt-btn-sm-line-height: 1.2;
    --wt-btn-md-padding: 10px 26px;
    --wt-btn-md-font-size: 14px;
    --wt-btn-md-border-width: 0px;
    --wt-btn-md-border-radius: 8px;
    --wt-btn-md-line-height: 1.2;
    --wt-btn-lg-padding: 12px 28px;
    --wt-btn-lg-font-size: 16px;
    --wt-btn-lg-border-width: 0px;
    --wt-btn-lg-border-radius: 8px;
    --wt-btn-lg-line-height: 1.2;
    --wt-btn-lg-min-height: 49.6px;
    --wt-btn-md-min-height: 42.6px;
    --wt-btn-sm-min-height: 38.6px
}

body {
    --wt-brand-color: #fda942;
    --wt-dark-accent-color: #f26522;
    --wt-dark-shade-color: #373737;
    --wt-light-accent-color: #fbf8ef;
    --wt-light-shade-color: #fff
}

._buttonsWrapper_x7vbr_132 {
    display: inline-flex;
    gap: 12px;
    flex-wrap: wrap
}

._spaceAfter--xSmall_x7vbr_138 {
    margin-bottom: 16px
}

._spaceAfter--small_x7vbr_141 {
    margin-bottom: 24px
}

._spaceAfter--medium_x7vbr_144 {
    margin-bottom: 30px
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: var(--text-color)
}

h1 {
    font-size: 48px
}

h2 {
    font-size: 36px
}

._section_x7vbr_165 {
    padding: 80px 0
}

._video_x7vbr_169 {
    box-shadow: 0 5px 17px #0003;
    width: 100%
}

._contentCol_x7vbr_174 {
    margin: 0 auto
}

._contentCol_x7vbr_174 h2 {
    font-size: 36px
}

._videoItemCol_x7vbr_181 {
    padding: 20px;
    margin: 30px 0;
    height: 100%
}

._button_x7vbr_132 {
    display: inline-block;
    padding: 10px 20px;
    background: #000;
    color: #fff
}

._video_section_pnnfs_1 {
    padding: 80px 0
}

._section_header_pnnfs_5 {
    text-align: center;
    margin: 0 auto 50px
}

._section_header_pnnfs_5 h2 {
    margin-bottom: 15px;
    margin-top: 0;
    color: var(--theme-color-brand1);
    font-weight: 700
}

._section_header_pnnfs_5 p {
    color: #9fa9c1;
    margin-bottom: 0
}

._videos_container_pnnfs_20 {
    margin-bottom: 10px
}

._video_pnnfs_1 {
    box-shadow: 0 5px 17px #0003;
    width: 100%;
    margin-bottom: 35px
}

._section_cta_pnnfs_30 {
    text-align: center
}

._button_pnnfs_34 {
    padding: 10px 30px;
    background-color: #000;
    color: #fff !important;
    text-transform: uppercase;
    font-weight: 600;
    display: inline-block;
    font-size: 15px
}

._video_section_vmy6f_1 {
    padding: 80px 0
}

._section_header_vmy6f_5 {
    margin: 0 auto
}

._section_header_vmy6f_5 h2 {
    margin-bottom: 15px;
    margin-top: 0;
    color: var(--theme-color-brand1);
    font-weight: 700
}

._section_header_vmy6f_5 p {
    color: #9fa9c1;
    margin-bottom: 25px
}

._video_vmy6f_1 {
    box-shadow: 0 5px 17px #0003;
    width: 100%;
    margin-bottom: 15px
}

._button_vmy6f_25 {
    padding: 10px 20px;
    background-color: #000;
    color: #fff !important;
    text-transform: uppercase;
    font-weight: 600;
    display: inline-block;
    font-size: 15px
}

._video_section_1cy7j_1 {
    padding: 80px 0
}

._section_header_1cy7j_5 {
    text-align: center;
    margin: 0 auto 60px
}

._section_header_1cy7j_5 h2 {
    margin-bottom: 15px;
    margin-top: 0;
    color: var(--theme-color-brand1);
    font-weight: 700
}

._section_header_1cy7j_5 p {
    color: #9fa9c1;
    margin-bottom: 0
}

._video_card_1cy7j_20 {
    margin-bottom: 35px;
    text-align: center
}

._video_card_1cy7j_20 ._video_1cy7j_1 {
    box-shadow: 0 5px 17px #0003;
    width: 100%;
    margin-bottom: 20px
}

._video_card_1cy7j_20 h3 {
    color: #1d2129;
    margin-bottom: 10px;
    font-weight: 700;
    font-size: 18px
}

._video_card_1cy7j_20 p {
    color: #1d212999;
    width: 90%;
    margin: 0 auto
}

._video_section_dn1bt_1 {
    padding: 80px 0
}

._section_header_dn1bt_5 {
    margin: 0 auto
}

._section_header_dn1bt_5 h2 {
    margin-bottom: 15px;
    margin-top: 0;
    color: var(--theme-color-brand1);
    font-weight: 700
}

._section_header_dn1bt_5 p {
    color: #9fa9c1;
    margin-bottom: 25px
}

@media (max-width: 812px) {
    ._section_header_dn1bt_5 p {
        width: 100%
    }
}

._video_dn1bt_1 {
    box-shadow: 0 5px 17px #0003;
    width: 100%;
    margin-bottom: 15px
}

._button_dn1bt_30 {
    padding: 10px 30px;
    background-color: #000;
    color: #fff !important;
    text-transform: uppercase;
    font-weight: 600;
    display: inline-block;
    font-size: 15px
}

._video_section_ybjli_1 {
    padding: 80px 0;
    text-align: center
}

._video_ybjli_1 {
    box-shadow: 0 5px 17px #0003;
    width: 100%;
    margin-bottom: 45px
}

._video_section_content_ybjli_12 {
    margin: 0 auto
}

._video_section_content_ybjli_12 h2 {
    margin-bottom: 15px;
    margin-top: 0;
    color: var(--theme-color-brand1);
    font-weight: 700
}

._video_section_content_ybjli_12 p {
    color: #949db4;
    width: 60%;
    margin: 0 auto 40px
}

._button_ybjli_27 {
    padding: 10px 30px;
    background-color: #000;
    color: #fff !important;
    text-transform: uppercase;
    font-weight: 600;
    display: inline-block;
    font-size: 15px
}

._video_section_1e5q5_1 {
    padding: 80px 0;
    text-align: center
}

._video_1e5q5_1 {
    box-shadow: 0 8px 28px #0003;
    width: 100%;
    margin-bottom: 45px
}

._video_section_content_1e5q5_12 {
    margin: 0 auto
}

._section_header_1e5q5_16 {
    text-align: center;
    width: 60%;
    margin: 0 auto 45px
}

._section_header_1e5q5_16 h2 {
    margin-bottom: 15px;
    margin-top: 0;
    color: var(--theme-color-brand1);
    font-weight: 700
}

._section_header_1e5q5_16 p {
    color: #9fa9c1;
    margin-bottom: 0
}

@media (max-width: 812px) {
    ._section_header_1e5q5_16 {
        width: 75%
    }
}

@media (max-width: 375px) {
    ._section_header_1e5q5_16 {
        width: 90%
    }
}

._button_1e5q5_42 {
    padding: 10px 30px;
    background-color: #000;
    color: #fff !important;
    text-transform: uppercase;
    font-weight: 600;
    display: inline-block;
    font-size: 15px
}

._video_section_c09bf_1 {
    padding: 80px 0
}

._section_header_c09bf_5 {
    text-align: center;
    margin: 0 auto 50px
}

._section_header_c09bf_5 h2 {
    margin-bottom: 15px;
    margin-top: 0;
    color: var(--theme-color-brand1);
    font-weight: 700
}

._section_header_c09bf_5 p {
    color: #9fa9c1;
    margin-bottom: 0
}

._video_c09bf_1 {
    box-shadow: 0 4px 14px #0003;
    width: 100%;
    margin-bottom: 35px
}

._section_cta_c09bf_26 {
    text-align: center
}

._button_c09bf_30 {
    padding: 10px 30px;
    background-color: #000;
    color: #fff !important;
    text-transform: uppercase;
    font-weight: 600;
    display: inline-block;
    font-size: 15px
}

._video_section_1k4xu_1 {
    padding: 80px 0
}

._section_header_1k4xu_5 {
    text-align: center;
    width: 60%;
    margin: 0 auto 50px
}

._section_header_1k4xu_5 h2 {
    margin-bottom: 15px;
    margin-top: 0;
    color: var(--theme-color-brand1);
    font-weight: 700
}

._section_header_1k4xu_5 p {
    color: #9fa9c1;
    margin-bottom: 0
}

@media (max-width: 812px) {
    ._section_header_1k4xu_5 {
        width: 75%
    }
}

@media (max-width: 375px) {
    ._section_header_1k4xu_5 {
        width: 90%
    }
}

._video_card_1k4xu_31 {
    margin-bottom: 35px;
    text-align: center
}

._video_card_1k4xu_31 ._video_1k4xu_1 {
    box-shadow: 0 5px 17px #0003;
    width: 100%;
    margin-bottom: 20px
}

._video_card_1k4xu_31 h3 {
    color: #1d2129;
    margin-bottom: 10px;
    font-weight: 700;
    font-size: 18px
}

._video_card_1k4xu_31 p {
    color: #1d212999;
    width: 90%;
    margin: 0 auto
}

._button_qgnvj_1 {
    font-family: var(--wt-button-font-family, var(--wt-btn-font-family));
    font-style: var(--wt-button-font-style, var(--wt-btn-font-style));
    text-transform: var(--wt-button-text-transform, var(--wt-btn-text-transform, none));
    width: auto;
    max-width: 100%;
    word-break: break-all;
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
    text-decoration: none;
    width: -moz-fit-content;
    width: fit-content;
    min-width: -moz-fit-content;
    min-width: fit-content;
    display: inline-block;
    cursor: pointer;
    opacity: .9
}

@supports (word-break: break-word) {
    ._button_qgnvj_1 {
        word-break: break-word
    }
}

._button_qgnvj_1:hover {
    opacity: 1
}

._button_qgnvj_1:focus,
._button_qgnvj_1:active {
    outline: none
}

._button_qgnvj_1:active {
    transform: scale(.95)
}

._button_qgnvj_1:disabled {
    background-color: #ccc;
    color: #666;
    cursor: not-allowed
}

._button_qgnvj_1._Small_qgnvj_38 {
    padding: var(--wt-btn-sm-padding, 8px 24px);
    border-radius: var(--wt-btn-sm-border-radius);
    font-size: var(--wt-button-size, var(--wt-btn-sm-font-size));
    line-height: calc(var(--wt-button-line-height) * 1em);
    letter-spacing: var(--wt-btn-letter-spacing);
    letter-spacing: calc(var(--wt-button-letter-spacing) * 1em);
    min-height: var(--wt-btn-sm-min-height, 38.6px)
}

._button_qgnvj_1._Medium_qgnvj_47 {
    padding: var(--wt-btn-md-padding, 10px 26px);
    border-radius: var(--wt-btn-md-border-radius);
    font-size: var(--wt-button-size, var(--wt-btn-md-font-size));
    line-height: calc(var(--wt-button-line-height) * 1em);
    letter-spacing: var(--wt-btn-letter-spacing);
    letter-spacing: calc(var(--wt-button-letter-spacing) * 1em);
    min-height: var(--wt-btn-md-min-height, 42.6px)
}

._button_qgnvj_1._Large_qgnvj_56 {
    padding: var(--wt-btn-lg-padding, 12px 28px);
    border-radius: var(--wt-btn-lg-border-radius);
    font-size: var(--wt-button-size, var(--wt-btn-lg-font-size));
    line-height: calc(var(--wt-button-line-height) * 1em);
    letter-spacing: var(--wt-btn-letter-spacing);
    letter-spacing: calc(var(--wt-button-letter-spacing) * 1em);
    min-height: var(--wt-btn-lg-min-height, 49.6px)
}

._button_qgnvj_1._Primary_qgnvj_65 {
    font-weight: var(--wt-btn-primary-font-weight);
    color: var(--wt-btn-primary-color);
    box-shadow: var(--wt-btn-primary-shadow-x) var(--wt-btn-primary-shadow-y) var(--wt-btn-primary-shadow-blur) var(--wt-btn-primary-shadow-spread) var(--wt-btn-primary-shadow-color);
    background-color: var(--wt-btn-primary-bg-color);
    background-image: linear-gradient(var(--wt-btn-primary-bg-gradient-angle), var(--wt-btn-primary-bg-gradient-colors))
}

._button_qgnvj_1._Primary_qgnvj_65:visited {
    color: #fff
}

._button_qgnvj_1._Secondary_qgnvj_75 {
    color: var(--theme-color-primary);
    background-color: #fff;
    border: 1px solid var(--theme-color-primary)
}

._button_qgnvj_1._Secondary_qgnvj_75:visited {
    color: var(--theme-color-primary)
}

._button_qgnvj_1._Link_qgnvj_83 {
    color: var(--theme-color-primary);
    background-color: transparent;
    border: none;
    border-radius: 0
}

._button_qgnvj_1._Link_qgnvj_83:visited {
    color: var(--theme-color-primary)
}

._globalButtonStyle_qgnvj_93 {
    max-width: 100%;
    word-break: break-all;
    margin: 0;
    text-decoration: none;
    width: -moz-fit-content;
    width: fit-content;
    height: -moz-fit-content;
    height: fit-content;
    display: inline-block;
    cursor: pointer
}

@supports (word-break: break-word) {
    ._globalButtonStyle_qgnvj_93 {
        word-break: break-word
    }
}

._globalButtonStyle_qgnvj_93:disabled {
    background-color: #ccc;
    color: #666;
    cursor: not-allowed
}

._text_1kq8d_1 h1,
._text_1kq8d_1 h2,
._text_1kq8d_1 h3,
._text_1kq8d_1 h4,
._text_1kq8d_1 h5,
._text_1kq8d_1 h6,
._text_1kq8d_1 p {
    margin: 0
}

._text_1kq8d_1 strong,
._text_1kq8d_1 strong * {
    font-weight: 700 !important
}

._text_1kq8d_1 em,
._text_1kq8d_1 em * {
    font-style: italic !important
}

._text_1kq8d_1 s,
._text_1kq8d_1 s * {
    text-decoration: line-through !important
}

._text_1kq8d_1 s span[style*="text-decoration: underline;"] {
    text-decoration: underline !important
}

._text_1kq8d_1 s,
._text_1kq8d_1 em,
._text_1kq8d_1 strong,
._text_1kq8d_1 span:not([style^=color]):not([style*=" color"]),
._text_1kq8d_1 span[style^=color] *:not([style^=color]):not([style*=" color"]),
._text_1kq8d_1 span[style*=" color"] *:not([style^=color]):not([style*=" color"]) {
    color: inherit !important
}

._text_1kq8d_1 s,
._text_1kq8d_1 em,
._text_1kq8d_1 strong,
._text_1kq8d_1 span:not([style*=font-size]),
._text_1kq8d_1 span[style*=font-size] *:not([style*=font-size]) {
    font-size: inherit !important
}

._wrapper_1k8do_1 {
    position: relative;
    overflow: hidden
}

._videoPlayer_1k8do_6 {
    position: absolute;
    min-width: 100%;
    min-height: 100%;
    pointer-events: none
}

._sectionContainer_1f6wa_1 {
    padding: 0 !important
}

._column_1mtba_1 {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    position: relative;
    overflow: hidden
}

._desktop_iyjbk_1 {
    display: block
}

@media (max-width: 600px) {
    ._desktop_iyjbk_1 {
        display: none
    }
}

._tablet_iyjbk_10 {
    display: none
}

@media (max-width: 768px) {
    ._tablet_iyjbk_10 {
        display: block
    }
}

._mobile_iyjbk_19 {
    display: none
}

@media (max-width: 600px) {
    ._mobile_iyjbk_19 {
        display: block
    }
}

._overlay_iyjbk_28 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none
}

._row_iyjbk_37 {
    display: grid;
    position: relative;
    margin-bottom: var(--marginBottom);
    grid-template-columns: var(--gridColumnTemplates)
}

@media (max-width: 992px) {
    ._row_iyjbk_37 {
        grid-template-columns: 1fr
    }
}

._element_1c7zx_1 {
    margin-bottom: 20px
}

._element_1c7zx_1:last-child {
    margin-bottom: 0
}

._desktop_qcg8m_1 {
    display: block
}

@media (max-width: 600px) {
    ._desktop_qcg8m_1 {
        display: none
    }
}

._tablet_qcg8m_10 {
    display: none
}

@media (max-width: 768px) {
    ._tablet_qcg8m_10 {
        display: block
    }
}

._mobile_qcg8m_19 {
    display: none
}

@media (max-width: 600px) {
    ._mobile_qcg8m_19 {
        display: block
    }
}

._desktop_1ry7l_1 {
    display: block
}

@media (max-width: 600px) {
    ._desktop_1ry7l_1 {
        display: none
    }
}

._tablet_1ry7l_10 {
    display: none
}

@media (max-width: 768px) {
    ._tablet_1ry7l_10 {
        display: block
    }
}

._mobile_1ry7l_19 {
    display: none
}

@media (max-width: 600px) {
    ._mobile_1ry7l_19 {
        display: block
    }
}

._overlay_1ry7l_28 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none
}

._desktop_i9ate_1 {
    display: block
}

@media (max-width: 600px) {
    ._desktop_i9ate_1 {
        display: none
    }
}

._tablet_i9ate_10 {
    display: none
}

@media (max-width: 768px) {
    ._tablet_i9ate_10 {
        display: block
    }
}

._mobile_i9ate_19 {
    display: none
}

@media (max-width: 600px) {
    ._mobile_i9ate_19 {
        display: block
    }
}

._oldStyles_i9ate_28 {
    border: 1px solid #d0d5dd;
    box-shadow: 0 1px 2px #1018280d;
    padding: 0 12px;
    border-radius: 4px;
    background-color: #fff;
    color: #101828
}

._generalInputWrapper_i9ate_37 {
    margin-bottom: 4px;
    font-size: 16px;
    position: relative;
    display: flex;
    align-items: center;
    cursor: text;
    font-family: inherit;
    font-size: 14px;
    line-height: 34px
}

@media (max-width: 600px) {
    ._generalInputWrapper_i9ate_37 {
        font-size: 16px
    }
}

._generalInputWrapper_i9ate_37 select {
    border: none
}

._generalInputWrapper_i9ate_37 input,
._generalInputWrapper_i9ate_37 textarea,
._generalInputWrapper_i9ate_37 select {
    padding: 0;
    line-height: 34px;
    border: none;
    outline: none;
    color: inherit;
    font-family: inherit;
    background-color: transparent;
    width: 100%;
    resize: vertical;
    -moz-appearance: textfield;
    font-size: 14px
}

._generalInputWrapper_i9ate_37 input::-moz-placeholder,
._generalInputWrapper_i9ate_37 textarea::-moz-placeholder,
._generalInputWrapper_i9ate_37 select::-moz-placeholder {
    color: #667085;
    opacity: .5
}

._generalInputWrapper_i9ate_37 input::placeholder,
._generalInputWrapper_i9ate_37 textarea::placeholder,
._generalInputWrapper_i9ate_37 select::placeholder {
    color: #667085;
    opacity: .5
}

._generalInputWrapper_i9ate_37 input::-webkit-input-placeholder,
._generalInputWrapper_i9ate_37 textarea::-webkit-input-placeholder,
._generalInputWrapper_i9ate_37 select::-webkit-input-placeholder {
    color: gray;
    opacity: .5
}

._generalInputWrapper_i9ate_37 input:-moz-placeholder,
._generalInputWrapper_i9ate_37 textarea:-moz-placeholder,
._generalInputWrapper_i9ate_37 select:-moz-placeholder {
    color: gray;
    opacity: .5
}

._generalInputWrapper_i9ate_37 input:-ms-input-placeholder,
._generalInputWrapper_i9ate_37 textarea:-ms-input-placeholder,
._generalInputWrapper_i9ate_37 select:-ms-input-placeholder {
    color: gray;
    opacity: .5
}

._generalInputWrapper_i9ate_37 input::-webkit-inner-spin-button,
._generalInputWrapper_i9ate_37 input ::-webkit-outer-spin-button,
._generalInputWrapper_i9ate_37 textarea::-webkit-inner-spin-button,
._generalInputWrapper_i9ate_37 textarea ::-webkit-outer-spin-button,
._generalInputWrapper_i9ate_37 select::-webkit-inner-spin-button,
._generalInputWrapper_i9ate_37 select ::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

@media (max-width: 600px) {
    ._generalInputWrapper_i9ate_37 input,
    ._generalInputWrapper_i9ate_37 textarea,
    ._generalInputWrapper_i9ate_37 select {
        font-size: 16px
    }
}

._error_i9ate_112 {
    border-color: #fda29b !important
}

._error_i9ate_112 select {
    color: #fda29b !important
}

._error_i9ate_112:focus-within {
    border: 2px solid #fda29b !important
}

._description_n50b4_1 {
    display: block;
    margin-bottom: 6px;
    color: #667085
}

._description_n50b4_1 p {
    font-size: 12px
}

._error_n50b4_10 {
    color: #fda29b
}

._error_14vxa_1 {
    color: #d92d20;
    font-size: 14px
}

._label_165vs_1 {
    display: block;
    margin-bottom: 6px;
    font-size: 14px
}

._error_165vs_7 {
    color: #d92d20
}

.dateContainer {
    display: flex;
    align-items: center;
    width: -moz-max-content;
    width: max-content;
    gap: 10px;
    border-radius: 6px;
    background: none;
    width: 100%;
    min-height: 35.6px
}

.dateContainer .react-datepicker__input-container input {
    border: none;
    border-radius: 6px;
    width: 100%;
    box-shadow: none;
    background: none
}

.dateContainer .react-datepicker__navigation-icon:before {
    top: 20px;
    border-color: #344054;
    border-width: 2px 2px 0 0
}

.dateContainer .react-datepicker__day--keyboard-selected,
.dateContainer .react-datepicker__day--selected {
    background: #0e9384;
    border-radius: 100%
}

.dateContainer .react-datepicker__day:hover {
    border-radius: 100%
}

.dateContainer .react-datepicker__header {
    background-color: #fff;
    border-bottom: none
}

.dateContainer .react-datepicker__input-container {
    width: 100%;
    position: relative
}

.dateContainer .react-datepicker__input-container input:focus-visible {
    border: none;
    outline: none
}

.dateContainer .react-datepicker__current-month {
    color: #344054;
    padding-top: 10px;
    padding-bottom: 10px
}

.dateContainer .react-datepicker {
    box-shadow: 0 20px 24px -4px #10182814, 0 8px 8px -4px #10182808;
    border: 1px solid #f2f4f7;
    background: #ffffff;
    display: flex;
    flex-direction: column
}

.dateContainer .react-datepicker__navigation {
    outline: none
}

.dateContainer .react-datepicker__day--keyboard-selected {
    background: #ffffff;
    border-radius: 100%;
    color: #344054
}

.dateContainer .react-datepicker__input-container input::-moz-placeholder {
    color: #d0d5dd
}

.dateContainer .react-datepicker__input-container input::placeholder {
    color: #d0d5dd
}

.dateContainer .react-datepicker__day--today {
    font-weight: 400
}

.dateContainer .react-datepicker__day--outside-month {
    color: #667085
}

.dateContainer .react-datepicker__day-names {
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    color: #344054
}

.error {
    border-color: #fda29b !important
}

.error:focus-within {
    border: 2px solid #fda29b !important
}

.calenderStyle {
    margin-inline-end: -50px;
    margin-bottom: 15px
}

._checkboxWrapper_f4rll_1 {
    display: flex;
    flex-direction: column
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 {
    display: flex;
    align-items: center;
    position: relative;
    margin: 0 0 15px
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    z-index: 2;
    height: 16px;
    width: 16px;
    cursor: pointer
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input+label {
    display: flex;
    align-items: center;
    position: relative;
    line-height: 14px;
    margin: 0;
    padding-inline-start: 25px;
    font-size: 14px;
    color: #1d2939
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input+label:before {
    position: absolute;
    display: block;
    left: 0;
    top: 0;
    content: "";
    background: transparent;
    width: 16px;
    height: 16px;
    border: 1px solid #d0d5dd;
    border-radius: 4px;
    transition: .2s ease-in-out all
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input+label:after {
    position: absolute;
    display: block;
    top: 3px;
    left: 6.5px;
    content: "";
    width: 4px;
    height: 8px;
    border-right: 2px solid transparent;
    border-bottom: 2px solid transparent;
    transform: rotate(45deg);
    transition: .2s ease-in-out all
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input:checked+label:before {
    border: 1px solid #475467;
    background: white
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input:checked+label:after {
    border-color: #1d2939
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input:disabled+label:before {
    background: #f2f2f2;
    box-shadow: none
}

._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input:disabled+label:after {
    border-color: transparent
}

.rtl ._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input {
    right: 0
}

.rtl ._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input+label:before {
    right: 0
}

.rtl ._checkboxWrapper_f4rll_1 ._checkboxInput_f4rll_5 input+label:after {
    left: 0;
    right: 6.5px
}

._radioWrapper_6fjy4_1 {
    display: flex;
    flex-direction: column
}

._radioWrapper_6fjy4_1 ._radioInput_6fjy4_5 {
    display: flex;
    align-items: center;
    margin: 0 0 15px
}

._radioWrapper_6fjy4_1 ._radioInput_6fjy4_5 input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 50%;
    border: 3px solid #fff;
    width: 14px;
    height: 14px;
    cursor: pointer;
    outline: 1px solid #d0d5dd
}

._radioWrapper_6fjy4_1 ._radioInput_6fjy4_5 input:checked {
    background: #475467;
    outline: 1px solid #475467
}

._radioWrapper_6fjy4_1 ._radioInput_6fjy4_5 label {
    margin-inline-start: 9px;
    margin-bottom: 0
}

._timeInputContainer_12833_1 {
    display: flex;
    align-items: center;
    gap: 6px
}

._hours_12833_7,
._minutes_12833_8 {
    width: 50px !important;
    border: 1px solid #d0d5dd;
    gap: 10px;
    box-shadow: 0 1px 2px #1018280d;
    background: #ffffff
}

._selectPmAm_12833_16 {
    border: 1px solid #d0d5dd;
    gap: 10px;
    border-radius: 6px;
    box-shadow: 0 1px 2px #1018280d;
    background: #ffffff;
    height: 35.6px;
    padding: 8px
}

._fileUploadContainer_17g2u_1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    cursor: pointer
}

._fileUploadContainer_17g2u_1>div:first-of-type {
    width: 100%;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    cursor: pointer;
    justify-content: space-between
}

._fileUploadContainer_17g2u_1 ._NoFileSelectedText_17g2u_15 {
    font-size: 16px;
    font-weight: 400;
    color: #667085;
    opacity: .5
}

._fileUploadContainer_17g2u_1 ._fileName_17g2u_21 {
    color: #101828;
    font-size: 14px
}

._fileUploadContainer_17g2u_1 ._fileUploadInput_17g2u_25 {
    display: none
}

._fileUploadContainer_17g2u_1 ._fileUploadButton_17g2u_28 {
    display: flex;
    gap: 5px;
    font-family: inherit;
    flex: 1;
    align-self: stretch;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
    margin-bottom: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    cursor: pointer;
    width: -moz-max-content;
    width: max-content;
    border: 1px solid #d0d5dd;
    border-left: none
}

._fileUploadContainer_17g2u_1 ._inputContainer_17g2u_45 {
    width: 100%;
    height: 35.6px
}

._error_17g2u_50 {
    color: #d92d20;
    font-size: 14px
}

.rtl ._fileUploadContainer_17g2u_1>div:first-of-type {
    border-right-width: 1px;
    border-radius: 0 4px 4px 0
}

.rtl ._fileUploadContainer_17g2u_1 ._fileUploadButton_17g2u_28 {
    border-right: none;
    border-left: 1px solid #d0d5dd;
    border-radius: 4px 0 0 4px
}

._loader_q39p5_1 {
    margin: 0 5px;
    display: inline-block;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 13px;
    height: 13px;
    animation: _spin_q39p5_1 1s linear infinite
}

@keyframes _spin_q39p5_1 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(360deg)
    }
}

._feedbackMessage_qzhtz_1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-radius: 8px;
    margin: 10px 0;
    border: 2px solid
}

._success_qzhtz_11 {
    background-color: #ecf8f7;
    border-color: #c0e8e4;
    color: #0f766e
}

._fail_qzhtz_17 {
    background-color: #faeaea;
    border-color: #eeb9b9;
    color: #970c0c
}

._closeButton_qzhtz_23 {
    padding: 0;
    background-color: transparent;
    border: none;
    cursor: pointer;
    opacity: .5
}

._closeButton_qzhtz_23:hover {
    opacity: 1
}

._lds-ring_fe9qv_1 {
    display: inline-block;
    position: relative;
    width: 32px;
    height: 32px
}

._lds-ring_fe9qv_1 div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 32px;
    height: 32px;
    margin: 8px;
    border: 4px solid var(--theme-color-primary);
    border-radius: 50%;
    animation: _lds-ring_fe9qv_1 1.2s cubic-bezier(.5, 0, .5, 1) infinite;
    border-color: var(--theme-color-primary) transparent transparent transparent
}

._lds-ring_fe9qv_1 div:nth-child(1) {
    animation-delay: -.45s
}

._lds-ring_fe9qv_1 div:nth-child(2) {
    animation-delay: -.3s
}

._lds-ring_fe9qv_1 div:nth-child(3) {
    animation-delay: -.15s
}

@keyframes _lds-ring_fe9qv_1 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(360deg)
    }
}

._FormFieldsContainer_mq3sp_1 {
    display: flex;
    flex-direction: column
}

._FormFieldsContainer_mq3sp_1>* {
    margin-bottom: 16px
}

._FormFieldsContainer_mq3sp_1>*:last-child {
    margin: 0
}

._linkContainer_1wfr1_1 {
    pointer-events: auto
}

._linkContainer_1wfr1_1 a {
    color: inherit
}

._desktop_1c2da_1 {
    display: block
}

@media (max-width: 600px) {
    ._desktop_1c2da_1 {
        display: none
    }
}

._tablet_1c2da_10 {
    display: none
}

@media (max-width: 768px) {
    ._tablet_1c2da_10 {
        display: block
    }
}

._mobile_1c2da_19 {
    display: none
}

@media (max-width: 600px) {
    ._mobile_1c2da_19 {
        display: block
    }
}

._overlay_1c2da_28 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none
}

._video_1c2da_37 {
    display: inline-block;
    width: var(--width)
}

@media (max-width: 600px) {
    ._video_1c2da_37 {
        width: 100%
    }
}

/*# sourceMappingURL=6584.cb08d5838a885504.css.map*/