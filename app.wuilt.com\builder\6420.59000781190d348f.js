(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [6420], {
        72764: e => {
            function t(e, t) {
                e.onload = function() {
                    this.onerror = this.onload = null, t(null, e)
                }, e.onerror = function() {
                    this.onerror = this.onload = null, t(new Error("Failed to load " + this.src), e)
                }
            }

            function n(e, t) {
                e.onreadystatechange = function() {
                    "complete" != this.readyState && "loaded" != this.readyState || (this.onreadystatechange = null, t(null, e))
                }
            }
            e.exports = function(e, r, o) {
                var a = document.head || document.getElementsByTagName("head")[0],
                    i = document.createElement("script");
                "function" == typeof r && (o = r, r = {}), r = r || {}, o = o || function() {}, i.type = r.type || "text/javascript", i.charset = r.charset || "utf8", i.async = !("async" in r) || !!r.async, i.src = e, r.attrs && function(e, t) {
                    for (var n in t) e.setAttribute(n, t[n])
                }(i, r.attrs), r.text && (i.text = "" + r.text), ("onload" in i ? t : n)(i, o), i.onload || t(i, o), a.appendChild(i)
            }
        },
        65059: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o, a = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = c();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                i = (o = n(45145)) && o.__esModule ? o : {
                    default: o
                },
                u = n(43482),
                l = n(64534);

            function c() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return c = function() {
                    return e
                }, e
            }

            function s() {
                return s = Object.assign || function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var n = arguments[t];
                        for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r])
                    }
                    return e
                }, s.apply(this, arguments)
            }

            function f(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function p(e, t) {
                return p = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, p(e, t)
            }

            function y(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function d(e) {
                return d = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, d(e)
            }

            function h(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var b = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && p(e, t)
                }(b, e);
                var t, n, o, u, c = (o = b, u = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = d(o);
                    if (u) {
                        var n = d(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? y(e) : t
                    }(this, e)
                });

                function b() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, b);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return h(y(e = c.call.apply(c, [this].concat(n))), "mounted", !1), h(y(e), "isReady", !1), h(y(e), "isPlaying", !1), h(y(e), "isLoading", !0), h(y(e), "loadOnReady", null), h(y(e), "startOnPlay", !0), h(y(e), "seekOnPlay", null), h(y(e), "onDurationCalled", !1), h(y(e), "handlePlayerMount", (function(t) {
                        e.player || (e.player = t, e.player.load(e.props.url)), e.progress()
                    })), h(y(e), "getInternalPlayer", (function(t) {
                        return e.player ? e.player[t] : null
                    })), h(y(e), "progress", (function() {
                        if (e.props.url && e.player && e.isReady) {
                            var t = e.getCurrentTime() || 0,
                                n = e.getSecondsLoaded(),
                                r = e.getDuration();
                            if (r) {
                                var o = {
                                    playedSeconds: t,
                                    played: t / r
                                };
                                null !== n && (o.loadedSeconds = n, o.loaded = n / r), o.playedSeconds === e.prevPlayed && o.loadedSeconds === e.prevLoaded || e.props.onProgress(o), e.prevPlayed = o.playedSeconds, e.prevLoaded = o.loadedSeconds
                            }
                        }
                        e.progressTimeout = setTimeout(e.progress, e.props.progressFrequency || e.props.progressInterval)
                    })), h(y(e), "handleReady", (function() {
                        if (e.mounted) {
                            e.isReady = !0, e.isLoading = !1;
                            var t = e.props,
                                n = t.onReady,
                                r = t.playing,
                                o = t.volume,
                                a = t.muted;
                            n(), a || null === o || e.player.setVolume(o), e.loadOnReady ? (e.player.load(e.loadOnReady, !0), e.loadOnReady = null) : r && e.player.play(), e.handleDurationCheck()
                        }
                    })), h(y(e), "handlePlay", (function() {
                        e.isPlaying = !0, e.isLoading = !1;
                        var t = e.props,
                            n = t.onStart,
                            r = t.onPlay,
                            o = t.playbackRate;
                        e.startOnPlay && (e.player.setPlaybackRate && 1 !== o && e.player.setPlaybackRate(o), n(), e.startOnPlay = !1), r(), e.seekOnPlay && (e.seekTo(e.seekOnPlay), e.seekOnPlay = null), e.handleDurationCheck()
                    })), h(y(e), "handlePause", (function(t) {
                        e.isPlaying = !1, e.isLoading || e.props.onPause(t)
                    })), h(y(e), "handleEnded", (function() {
                        var t = e.props,
                            n = t.activePlayer,
                            r = t.loop,
                            o = t.onEnded;
                        n.loopOnEnded && r && e.seekTo(0), r || (e.isPlaying = !1, o())
                    })), h(y(e), "handleError", (function() {
                        var t;
                        e.isLoading = !1, (t = e.props).onError.apply(t, arguments)
                    })), h(y(e), "handleDurationCheck", (function() {
                        clearTimeout(e.durationCheckTimeout);
                        var t = e.getDuration();
                        t ? e.onDurationCalled || (e.props.onDuration(t), e.onDurationCalled = !0) : e.durationCheckTimeout = setTimeout(e.handleDurationCheck, 100)
                    })), h(y(e), "handleLoaded", (function() {
                        e.isLoading = !1
                    })), e
                }
                return t = b, n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.mounted = !0
                    }
                }, {
                    key: "componentWillUnmount",
                    value: function() {
                        clearTimeout(this.progressTimeout), clearTimeout(this.durationCheckTimeout), this.isReady && this.props.stopOnUnmount && (this.player.stop(), this.player.disablePIP && this.player.disablePIP()), this.mounted = !1
                    }
                }, {
                    key: "componentDidUpdate",
                    value: function(e) {
                        var t = this;
                        if (this.player) {
                            var n = this.props,
                                r = n.url,
                                o = n.playing,
                                a = n.volume,
                                u = n.muted,
                                c = n.playbackRate,
                                s = n.pip,
                                f = n.loop,
                                p = n.activePlayer,
                                y = n.disableDeferredLoading;
                            if (!(0, i.default)(e.url, r)) {
                                if (this.isLoading && !p.forceLoad && !y && !(0, l.isMediaStream)(r)) return console.warn("ReactPlayer: the attempt to load ".concat(r, " is being deferred until the player has loaded")), void(this.loadOnReady = r);
                                this.isLoading = !0, this.startOnPlay = !0, this.onDurationCalled = !1, this.player.load(r, this.isReady)
                            }
                            e.playing || !o || this.isPlaying || this.player.play(), e.playing && !o && this.isPlaying && this.player.pause(), !e.pip && s && this.player.enablePIP && this.player.enablePIP(), e.pip && !s && this.player.disablePIP && this.player.disablePIP(), e.volume !== a && null !== a && this.player.setVolume(a), e.muted !== u && (u ? this.player.mute() : (this.player.unmute(), null !== a && setTimeout((function() {
                                return t.player.setVolume(a)
                            })))), e.playbackRate !== c && this.player.setPlaybackRate && this.player.setPlaybackRate(c), e.loop !== f && this.player.setLoop && this.player.setLoop(f)
                        }
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.isReady ? this.player.getDuration() : null
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.isReady ? this.player.getCurrentTime() : null
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return this.isReady ? this.player.getSecondsLoaded() : null
                    }
                }, {
                    key: "seekTo",
                    value: function(e, t) {
                        var n = this;
                        if (this.isReady) {
                            if (t ? "fraction" === t : e > 0 && e < 1) {
                                var r = this.player.getDuration();
                                return r ? void this.player.seekTo(r * e) : void console.warn("ReactPlayer: could not seek using fraction – duration not yet available")
                            }
                            this.player.seekTo(e)
                        } else 0 !== e && (this.seekOnPlay = e, setTimeout((function() {
                            n.seekOnPlay = null
                        }), 5e3))
                    }
                }, {
                    key: "render",
                    value: function() {
                        var e = this.props.activePlayer;
                        return e ? a.default.createElement(e, s({}, this.props, {
                            onMount: this.handlePlayerMount,
                            onReady: this.handleReady,
                            onPlay: this.handlePlay,
                            onPause: this.handlePause,
                            onEnded: this.handleEnded,
                            onLoaded: this.handleLoaded,
                            onError: this.handleError
                        })) : null
                    }
                }], n && f(t.prototype, n), b
            }(a.Component);
            t.default = b, h(b, "displayName", "Player"), h(b, "propTypes", u.propTypes), h(b, "defaultProps", u.defaultProps)
        },
        64403: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                if (e && e.__esModule) return e;
                if (null === e || "object" !== r(e) && "function" != typeof e) return {
                    default: e
                };
                var t = a();
                if (t && t.has(e)) return t.get(e);
                var n = {},
                    o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                for (var i in e)
                    if (Object.prototype.hasOwnProperty.call(e, i)) {
                        var u = o ? Object.getOwnPropertyDescriptor(e, i) : null;
                        u && (u.get || u.set) ? Object.defineProperty(n, i, u) : n[i] = e[i]
                    }
                return n.default = e, t && t.set(e, n), n
            }(n(12196));

            function a() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return a = function() {
                    return e
                }, e
            }

            function i(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function u(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var n = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? i(Object(n), !0).forEach((function(t) {
                        p(e, t, n[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : i(Object(n)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                    }))
                }
                return e
            }

            function l(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function c(e, t) {
                return c = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, c(e, t)
            }

            function s(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function f(e) {
                return f = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, f(e)
            }

            function p(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var y = "64px",
                d = {},
                h = function(e) {
                    ! function(e, t) {
                        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                        e.prototype = Object.create(t && t.prototype, {
                            constructor: {
                                value: e,
                                writable: !0,
                                configurable: !0
                            }
                        }), t && c(e, t)
                    }(b, e);
                    var t, n, a, i, h = (a = b, i = function() {
                        if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                        if (Reflect.construct.sham) return !1;
                        if ("function" == typeof Proxy) return !0;
                        try {
                            return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                        } catch (e) {
                            return !1
                        }
                    }(), function() {
                        var e, t = f(a);
                        if (i) {
                            var n = f(this).constructor;
                            e = Reflect.construct(t, arguments, n)
                        } else e = t.apply(this, arguments);
                        return function(e, t) {
                            return !t || "object" !== r(t) && "function" != typeof t ? s(e) : t
                        }(this, e)
                    });

                    function b() {
                        var e;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                        }(this, b);
                        for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                        return p(s(e = h.call.apply(h, [this].concat(n))), "mounted", !1), p(s(e), "state", {
                            image: null
                        }), p(s(e), "handleKeyPress", (function(t) {
                            "Enter" !== t.key && " " !== t.key || e.props.onClick()
                        })), e
                    }
                    return t = b, (n = [{
                        key: "componentDidMount",
                        value: function() {
                            this.mounted = !0, this.fetchImage(this.props)
                        }
                    }, {
                        key: "componentDidUpdate",
                        value: function(e) {
                            var t = this.props,
                                n = t.url,
                                r = t.light;
                            e.url === n && e.light === r || this.fetchImage(this.props)
                        }
                    }, {
                        key: "componentWillUnmount",
                        value: function() {
                            this.mounted = !1
                        }
                    }, {
                        key: "fetchImage",
                        value: function(e) {
                            var t = this,
                                n = e.url,
                                r = e.light,
                                a = e.oEmbedUrl;
                            if (!o.default.isValidElement(r))
                                if ("string" != typeof r) {
                                    if (!d[n]) return this.setState({
                                        image: null
                                    }), window.fetch(a.replace("{url}", n)).then((function(e) {
                                        return e.json()
                                    })).then((function(e) {
                                        if (e.thumbnail_url && t.mounted) {
                                            var r = e.thumbnail_url.replace("height=100", "height=480").replace("-d_295x166", "-d_640");
                                            t.setState({
                                                image: r
                                            }), d[n] = r
                                        }
                                    }));
                                    this.setState({
                                        image: d[n]
                                    })
                                } else this.setState({
                                    image: r
                                })
                        }
                    }, {
                        key: "render",
                        value: function() {
                            var e = this.props,
                                t = e.light,
                                n = e.onClick,
                                r = e.playIcon,
                                a = e.previewTabIndex,
                                i = this.state.image,
                                l = o.default.isValidElement(t),
                                c = {
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center"
                                },
                                s = {
                                    preview: u({
                                        width: "100%",
                                        height: "100%",
                                        backgroundImage: i && !l ? "url(".concat(i, ")") : void 0,
                                        backgroundSize: "cover",
                                        backgroundPosition: "center",
                                        cursor: "pointer"
                                    }, c),
                                    shadow: u({
                                        background: "radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)",
                                        borderRadius: y,
                                        width: y,
                                        height: y,
                                        position: l ? "absolute" : void 0
                                    }, c),
                                    playIcon: {
                                        borderStyle: "solid",
                                        borderWidth: "16px 0 16px 26px",
                                        borderColor: "transparent transparent transparent white",
                                        marginLeft: "7px"
                                    }
                                },
                                f = o.default.createElement("div", {
                                    style: s.shadow,
                                    className: "react-player__shadow"
                                }, o.default.createElement("div", {
                                    style: s.playIcon,
                                    className: "react-player__play-icon"
                                }));
                            return o.default.createElement("div", {
                                style: s.preview,
                                className: "react-player__preview",
                                onClick: n,
                                tabIndex: a,
                                onKeyPress: this.handleKeyPress
                            }, l ? t : null, r || f)
                        }
                    }]) && l(t.prototype, n), b
                }(o.Component);
            t.default = h
        },
        47392: (e, t, n) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.createReactPlayer = void 0;
            var r = k(n(12196)),
                o = s(n(51758)),
                a = s(n(72185)),
                i = s(n(45145)),
                u = n(43482),
                l = n(64534),
                c = s(n(65059));

            function s(e) {
                return e && e.__esModule ? e : {
                    default: e
                }
            }

            function f(e) {
                return f = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, f(e)
            }

            function p(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function y(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var n = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? p(Object(n), !0).forEach((function(t) {
                        O(e, t, n[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : p(Object(n)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                    }))
                }
                return e
            }

            function d() {
                return d = Object.assign || function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var n = arguments[t];
                        for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r])
                    }
                    return e
                }, d.apply(this, arguments)
            }

            function h(e) {
                return function(e) {
                    if (Array.isArray(e)) return b(e)
                }(e) || function(e) {
                    if ("undefined" != typeof Symbol && Symbol.iterator in Object(e)) return Array.from(e)
                }(e) || function(e, t) {
                    if (e) {
                        if ("string" == typeof e) return b(e, t);
                        var n = Object.prototype.toString.call(e).slice(8, -1);
                        return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? b(e, t) : void 0
                    }
                }(e) || function() {
                    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }()
            }

            function b(e, t) {
                (null == t || t > e.length) && (t = e.length);
                for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
                return r
            }

            function v(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function m(e, t) {
                return m = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, m(e, t)
            }

            function P(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function g(e) {
                return g = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, g(e)
            }

            function O(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }

            function w() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return w = function() {
                    return e
                }, e
            }

            function k(e) {
                if (e && e.__esModule) return e;
                if (null === e || "object" !== f(e) && "function" != typeof e) return {
                    default: e
                };
                var t = w();
                if (t && t.has(e)) return t.get(e);
                var n = {},
                    r = Object.defineProperty && Object.getOwnPropertyDescriptor;
                for (var o in e)
                    if (Object.prototype.hasOwnProperty.call(e, o)) {
                        var a = r ? Object.getOwnPropertyDescriptor(e, o) : null;
                        a && (a.get || a.set) ? Object.defineProperty(n, o, a) : n[o] = e[o]
                    }
                return n.default = e, t && t.set(e, n), n
            }
            var j = (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return k(n(64403))
                    }))
                })),
                S = "undefined" != typeof window && window.document,
                _ = void 0 !== n.g && n.g.window && n.g.window.document,
                E = Object.keys(u.propTypes),
                D = S || _ ? r.Suspense : function() {
                    return null
                },
                R = [];
            t.createReactPlayer = function(e, t) {
                var n, s;
                return s = n = function(n) {
                    ! function(e, t) {
                        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                        e.prototype = Object.create(t && t.prototype, {
                            constructor: {
                                value: e,
                                writable: !0,
                                configurable: !0
                            }
                        }), t && m(e, t)
                    }(S, n);
                    var s, p, b, w, k = (b = S, w = function() {
                        if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                        if (Reflect.construct.sham) return !1;
                        if ("function" == typeof Proxy) return !0;
                        try {
                            return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                        } catch (e) {
                            return !1
                        }
                    }(), function() {
                        var e, t = g(b);
                        if (w) {
                            var n = g(this).constructor;
                            e = Reflect.construct(t, arguments, n)
                        } else e = t.apply(this, arguments);
                        return function(e, t) {
                            return !t || "object" !== f(t) && "function" != typeof t ? P(e) : t
                        }(this, e)
                    });

                    function S() {
                        var n;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                        }(this, S);
                        for (var i = arguments.length, s = new Array(i), f = 0; f < i; f++) s[f] = arguments[f];
                        return O(P(n = k.call.apply(k, [this].concat(s))), "state", {
                            showPreview: !!n.props.light
                        }), O(P(n), "references", {
                            wrapper: function(e) {
                                n.wrapper = e
                            },
                            player: function(e) {
                                n.player = e
                            }
                        }), O(P(n), "handleClickPreview", (function(e) {
                            n.setState({
                                showPreview: !1
                            }), n.props.onClickPreview(e)
                        })), O(P(n), "showPreview", (function() {
                            n.setState({
                                showPreview: !0
                            })
                        })), O(P(n), "getDuration", (function() {
                            return n.player ? n.player.getDuration() : null
                        })), O(P(n), "getCurrentTime", (function() {
                            return n.player ? n.player.getCurrentTime() : null
                        })), O(P(n), "getSecondsLoaded", (function() {
                            return n.player ? n.player.getSecondsLoaded() : null
                        })), O(P(n), "getInternalPlayer", (function() {
                            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "player";
                            return n.player ? n.player.getInternalPlayer(e) : null
                        })), O(P(n), "seekTo", (function(e, t) {
                            if (!n.player) return null;
                            n.player.seekTo(e, t)
                        })), O(P(n), "handleReady", (function() {
                            n.props.onReady(P(n))
                        })), O(P(n), "getActivePlayer", (0, a.default)((function(n) {
                            for (var r = 0, o = [].concat(R, h(e)); r < o.length; r++) {
                                var a = o[r];
                                if (a.canPlay(n)) return a
                            }
                            return t || null
                        }))), O(P(n), "getConfig", (0, a.default)((function(e, t) {
                            var r = n.props.config;
                            return o.default.all([u.defaultProps.config, u.defaultProps.config[t] || {}, r, r[t] || {}])
                        }))), O(P(n), "getAttributes", (0, a.default)((function(e) {
                            return (0, l.omit)(n.props, E)
                        }))), O(P(n), "renderActivePlayer", (function(e) {
                            if (!e) return null;
                            var t = n.getActivePlayer(e);
                            if (!t) return null;
                            var o = n.getConfig(e, t.key);
                            return r.default.createElement(c.default, d({}, n.props, {
                                key: t.key,
                                ref: n.references.player,
                                config: o,
                                activePlayer: t.lazyPlayer || t,
                                onReady: n.handleReady
                            }))
                        })), n
                    }
                    return s = S, p = [{
                        key: "shouldComponentUpdate",
                        value: function(e, t) {
                            return !(0, i.default)(this.props, e) || !(0, i.default)(this.state, t)
                        }
                    }, {
                        key: "componentDidUpdate",
                        value: function(e) {
                            var t = this.props.light;
                            !e.light && t && this.setState({
                                showPreview: !0
                            }), e.light && !t && this.setState({
                                showPreview: !1
                            })
                        }
                    }, {
                        key: "renderPreview",
                        value: function(e) {
                            if (!e) return null;
                            var t = this.props,
                                n = t.light,
                                o = t.playIcon,
                                a = t.previewTabIndex,
                                i = t.oEmbedUrl;
                            return r.default.createElement(j, {
                                url: e,
                                light: n,
                                playIcon: o,
                                previewTabIndex: a,
                                oEmbedUrl: i,
                                onClick: this.handleClickPreview
                            })
                        }
                    }, {
                        key: "render",
                        value: function() {
                            var e = this.props,
                                t = e.url,
                                n = e.style,
                                o = e.width,
                                a = e.height,
                                i = e.fallback,
                                u = e.wrapper,
                                l = this.state.showPreview,
                                c = this.getAttributes(t),
                                s = "string" == typeof u ? this.references.wrapper : void 0;
                            return r.default.createElement(u, d({
                                ref: s,
                                style: y(y({}, n), {}, {
                                    width: o,
                                    height: a
                                })
                            }, c), r.default.createElement(D, {
                                fallback: i
                            }, l ? this.renderPreview(t) : this.renderActivePlayer(t)))
                        }
                    }], p && v(s.prototype, p), S
                }(r.Component), O(n, "displayName", "ReactPlayer"), O(n, "propTypes", u.propTypes), O(n, "defaultProps", u.defaultProps), O(n, "addCustomPlayer", (function(e) {
                    R.push(e)
                })), O(n, "removeCustomPlayers", (function() {
                    R.length = 0
                })), O(n, "canPlay", (function(t) {
                    for (var n = 0, r = [].concat(R, h(e)); n < r.length; n++)
                        if (r[n].canPlay(t)) return !0;
                    return !1
                })), O(n, "canEnablePIP", (function(t) {
                    for (var n = 0, r = [].concat(R, h(e)); n < r.length; n++) {
                        var o = r[n];
                        if (o.canEnablePIP && o.canEnablePIP(t)) return !0
                    }
                    return !1
                })), s
            }
        },
        76420: (e, t, n) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var r, o = (r = n(16792)) && r.__esModule ? r : {
                    default: r
                },
                a = n(47392),
                i = o.default[o.default.length - 1],
                u = (0, a.createReactPlayer)(o.default, i);
            t.default = u
        },
        52839: (e, t, n) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.canPlay = t.FLV_EXTENSIONS = t.DASH_EXTENSIONS = t.HLS_EXTENSIONS = t.VIDEO_EXTENSIONS = t.AUDIO_EXTENSIONS = t.MATCH_URL_KALTURA = t.MATCH_URL_VIDYARD = t.MATCH_URL_MIXCLOUD = t.MATCH_URL_DAILYMOTION = t.MATCH_URL_TWITCH_CHANNEL = t.MATCH_URL_TWITCH_VIDEO = t.MATCH_URL_WISTIA = t.MATCH_URL_STREAMABLE = t.MATCH_URL_FACEBOOK_WATCH = t.MATCH_URL_FACEBOOK = t.MATCH_URL_VIMEO = t.MATCH_URL_SOUNDCLOUD = t.MATCH_URL_YOUTUBE = void 0;
            var r = n(64534);

            function o(e, t) {
                (null == t || t > e.length) && (t = e.length);
                for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
                return r
            }
            var a = /(?:youtu\.be\/|youtube(?:-nocookie)?\.com\/(?:embed\/|v\/|watch\/|watch\?v=|watch\?.+&v=|shorts\/|live\/))((\w|-){11})|youtube\.com\/playlist\?list=|youtube\.com\/user\//;
            t.MATCH_URL_YOUTUBE = a;
            var i = /(?:soundcloud\.com|snd\.sc)\/[^.]+$/;
            t.MATCH_URL_SOUNDCLOUD = i;
            var u = /vimeo\.com\/(?!progressive_redirect).+/;
            t.MATCH_URL_VIMEO = u;
            var l = /^https?:\/\/(www\.)?facebook\.com.*\/(video(s)?|watch|story)(\.php?|\/).+$/;
            t.MATCH_URL_FACEBOOK = l;
            var c = /^https?:\/\/fb\.watch\/.+$/;
            t.MATCH_URL_FACEBOOK_WATCH = c;
            var s = /streamable\.com\/([a-z0-9]+)$/;
            t.MATCH_URL_STREAMABLE = s;
            var f = /(?:wistia\.(?:com|net)|wi\.st)\/(?:medias|embed)\/(?:iframe\/)?(.*)$/;
            t.MATCH_URL_WISTIA = f;
            var p = /(?:www\.|go\.)?twitch\.tv\/videos\/(\d+)($|\?)/;
            t.MATCH_URL_TWITCH_VIDEO = p;
            var y = /(?:www\.|go\.)?twitch\.tv\/([a-zA-Z0-9_]+)($|\?)/;
            t.MATCH_URL_TWITCH_CHANNEL = y;
            var d = /^(?:(?:https?):)?(?:\/\/)?(?:www\.)?(?:(?:dailymotion\.com(?:\/embed)?\/video)|dai\.ly)\/([a-zA-Z0-9]+)(?:_[\w_-]+)?(?:[\w.#_-]+)?/;
            t.MATCH_URL_DAILYMOTION = d;
            var h = /mixcloud\.com\/([^/]+\/[^/]+)/;
            t.MATCH_URL_MIXCLOUD = h;
            var b = /vidyard.com\/(?:watch\/)?([a-zA-Z0-9-_]+)/;
            t.MATCH_URL_VIDYARD = b;
            var v = /^https?:\/\/[a-zA-Z]+\.kaltura.(com|org)\/p\/([0-9]+)\/sp\/([0-9]+)00\/embedIframeJs\/uiconf_id\/([0-9]+)\/partner_id\/([0-9]+)(.*)entry_id.([a-zA-Z0-9-_].*)$/;
            t.MATCH_URL_KALTURA = v;
            var m = /\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\?)/i;
            t.AUDIO_EXTENSIONS = m;
            var P = /\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\d+]+)?($|\?)/i;
            t.VIDEO_EXTENSIONS = P;
            var g = /\.(m3u8)($|\?)/i;
            t.HLS_EXTENSIONS = g;
            var O = /\.(mpd)($|\?)/i;
            t.DASH_EXTENSIONS = O;
            var w = /\.(flv)($|\?)/i;
            t.FLV_EXTENSIONS = w;
            var k = {
                youtube: function(e) {
                    return e instanceof Array ? e.every((function(e) {
                        return a.test(e)
                    })) : a.test(e)
                },
                soundcloud: function(e) {
                    return i.test(e) && !m.test(e)
                },
                vimeo: function(e) {
                    return u.test(e) && !P.test(e) && !g.test(e)
                },
                facebook: function(e) {
                    return l.test(e) || c.test(e)
                },
                streamable: function(e) {
                    return s.test(e)
                },
                wistia: function(e) {
                    return f.test(e)
                },
                twitch: function(e) {
                    return p.test(e) || y.test(e)
                },
                dailymotion: function(e) {
                    return d.test(e)
                },
                mixcloud: function(e) {
                    return h.test(e)
                },
                vidyard: function(e) {
                    return b.test(e)
                },
                kaltura: function(e) {
                    return v.test(e)
                },
                file: function e(t) {
                    if (t instanceof Array) {
                        var n, a = function(e, t) {
                            var n;
                            if ("undefined" == typeof Symbol || null == e[Symbol.iterator]) {
                                if (Array.isArray(e) || (n = function(e, t) {
                                        if (e) {
                                            if ("string" == typeof e) return o(e, t);
                                            var n = Object.prototype.toString.call(e).slice(8, -1);
                                            return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? o(e, t) : void 0
                                        }
                                    }(e)) || t && e && "number" == typeof e.length) {
                                    n && (e = n);
                                    var r = 0,
                                        a = function() {};
                                    return {
                                        s: a,
                                        n: function() {
                                            return r >= e.length ? {
                                                done: !0
                                            } : {
                                                done: !1,
                                                value: e[r++]
                                            }
                                        },
                                        e: function(e) {
                                            throw e
                                        },
                                        f: a
                                    }
                                }
                                throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                            }
                            var i, u = !0,
                                l = !1;
                            return {
                                s: function() {
                                    n = e[Symbol.iterator]()
                                },
                                n: function() {
                                    var e = n.next();
                                    return u = e.done, e
                                },
                                e: function(e) {
                                    l = !0, i = e
                                },
                                f: function() {
                                    try {
                                        u || null == n.return || n.return()
                                    } finally {
                                        if (l) throw i
                                    }
                                }
                            }
                        }(t);
                        try {
                            for (a.s(); !(n = a.n()).done;) {
                                var i = n.value;
                                if ("string" == typeof i && e(i)) return !0;
                                if (e(i.src)) return !0
                            }
                        } catch (e) {
                            a.e(e)
                        } finally {
                            a.f()
                        }
                        return !1
                    }
                    return !(!(0, r.isMediaStream)(t) && !(0, r.isBlobUrl)(t)) || m.test(t) || P.test(t) || g.test(t) || O.test(t) || w.test(t)
                }
            };
            t.canPlay = k
        },
        41763: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function c(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var n = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? l(Object(n), !0).forEach((function(t) {
                        h(e, t, n[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                    }))
                }
                return e
            }

            function s(e, t) {
                (null == t || t > e.length) && (t = e.length);
                for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
                return r
            }

            function f(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function p(e, t) {
                return p = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, p(e, t)
            }

            function y(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function d(e) {
                return d = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, d(e)
            }

            function h(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var b = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && p(e, t)
                }(v, e);
                var t, n, u, l, b = (u = v, l = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = d(u);
                    if (l) {
                        var n = d(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? y(e) : t
                    }(this, e)
                });

                function v() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, v);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return h(y(e = b.call.apply(b, [this].concat(n))), "callPlayer", a.callPlayer), h(y(e), "onDurationChange", (function() {
                        var t = e.getDuration();
                        e.props.onDuration(t)
                    })), h(y(e), "mute", (function() {
                        e.callPlayer("setMuted", !0)
                    })), h(y(e), "unmute", (function() {
                        e.callPlayer("setMuted", !1)
                    })), h(y(e), "ref", (function(t) {
                        e.container = t
                    })), e
                }
                return t = v, (n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e) {
                        var t, n, r = this,
                            o = this.props,
                            u = o.controls,
                            l = o.config,
                            f = o.onError,
                            p = o.playing,
                            y = (t = e.match(i.MATCH_URL_DAILYMOTION), n = 2, function(e) {
                                if (Array.isArray(e)) return e
                            }(t) || function(e, t) {
                                if ("undefined" != typeof Symbol && Symbol.iterator in Object(e)) {
                                    var n = [],
                                        r = !0,
                                        o = !1,
                                        a = void 0;
                                    try {
                                        for (var i, u = e[Symbol.iterator](); !(r = (i = u.next()).done) && (n.push(i.value), !t || n.length !== t); r = !0);
                                    } catch (e) {
                                        o = !0, a = e
                                    } finally {
                                        try {
                                            r || null == u.return || u.return()
                                        } finally {
                                            if (o) throw a
                                        }
                                    }
                                    return n
                                }
                            }(t, n) || function(e, t) {
                                if (e) {
                                    if ("string" == typeof e) return s(e, t);
                                    var n = Object.prototype.toString.call(e).slice(8, -1);
                                    return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? s(e, t) : void 0
                                }
                            }(t, n) || function() {
                                throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                            }())[1];
                        this.player ? this.player.load(y, {
                            start: (0, a.parseStartTime)(e),
                            autoplay: p
                        }) : (0, a.getSDK)("https://api.dmcdn.net/all.js", "DM", "dmAsyncInit", (function(e) {
                            return e.player
                        })).then((function(t) {
                            if (r.container) {
                                var n = t.player;
                                r.player = new n(r.container, {
                                    width: "100%",
                                    height: "100%",
                                    video: y,
                                    params: c({
                                        controls: u,
                                        autoplay: r.props.playing,
                                        mute: r.props.muted,
                                        start: (0, a.parseStartTime)(e),
                                        origin: window.location.origin
                                    }, l.params),
                                    events: {
                                        apiready: r.props.onReady,
                                        seeked: function() {
                                            return r.props.onSeek(r.player.currentTime)
                                        },
                                        video_end: r.props.onEnded,
                                        durationchange: r.onDurationChange,
                                        pause: r.props.onPause,
                                        playing: r.props.onPlay,
                                        waiting: r.props.onBuffer,
                                        error: function(e) {
                                            return f(e)
                                        }
                                    }
                                })
                            }
                        }), f)
                    }
                }, {
                    key: "play",
                    value: function() {
                        this.callPlayer("play")
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {}
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("seek", e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {
                        this.callPlayer("setVolume", e)
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.player.duration || null
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.player.currentTime
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return this.player.bufferedTime
                    }
                }, {
                    key: "render",
                    value: function() {
                        var e = {
                            width: "100%",
                            height: "100%",
                            display: this.props.display
                        };
                        return o.default.createElement("div", {
                            style: e
                        }, o.default.createElement("div", {
                            ref: this.ref
                        }))
                    }
                }]) && f(t.prototype, n), v
            }(o.Component);
            t.default = b, h(b, "displayName", "DailyMotion"), h(b, "canPlay", i.canPlay.dailymotion), h(b, "loopOnEnded", !0)
        },
        66636: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l() {
                return l = Object.assign || function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var n = arguments[t];
                        for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r])
                    }
                    return e
                }, l.apply(this, arguments)
            }

            function c(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function s(e, t) {
                return s = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, s(e, t)
            }

            function f(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function p(e) {
                return p = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, p(e)
            }

            function y(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var d = "https://connect.facebook.net/en_US/sdk.js",
                h = "fbAsyncInit",
                b = function(e) {
                    ! function(e, t) {
                        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                        e.prototype = Object.create(t && t.prototype, {
                            constructor: {
                                value: e,
                                writable: !0,
                                configurable: !0
                            }
                        }), t && s(e, t)
                    }(v, e);
                    var t, n, i, u, b = (i = v, u = function() {
                        if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                        if (Reflect.construct.sham) return !1;
                        if ("function" == typeof Proxy) return !0;
                        try {
                            return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                        } catch (e) {
                            return !1
                        }
                    }(), function() {
                        var e, t = p(i);
                        if (u) {
                            var n = p(this).constructor;
                            e = Reflect.construct(t, arguments, n)
                        } else e = t.apply(this, arguments);
                        return function(e, t) {
                            return !t || "object" !== r(t) && "function" != typeof t ? f(e) : t
                        }(this, e)
                    });

                    function v() {
                        var e;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                        }(this, v);
                        for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                        return y(f(e = b.call.apply(b, [this].concat(n))), "callPlayer", a.callPlayer), y(f(e), "playerID", e.props.config.playerId || "".concat("facebook-player-").concat((0, a.randomString)())), y(f(e), "mute", (function() {
                            e.callPlayer("mute")
                        })), y(f(e), "unmute", (function() {
                            e.callPlayer("unmute")
                        })), e
                    }
                    return t = v, (n = [{
                        key: "componentDidMount",
                        value: function() {
                            this.props.onMount && this.props.onMount(this)
                        }
                    }, {
                        key: "load",
                        value: function(e, t) {
                            var n = this;
                            t ? (0, a.getSDK)(d, "FB", h).then((function(e) {
                                return e.XFBML.parse()
                            })) : (0, a.getSDK)(d, "FB", h).then((function(e) {
                                e.init({
                                    appId: n.props.config.appId,
                                    xfbml: !0,
                                    version: n.props.config.version
                                }), e.Event.subscribe("xfbml.render", (function(e) {
                                    n.props.onLoaded()
                                })), e.Event.subscribe("xfbml.ready", (function(e) {
                                    "video" === e.type && e.id === n.playerID && (n.player = e.instance, n.player.subscribe("startedPlaying", n.props.onPlay), n.player.subscribe("paused", n.props.onPause), n.player.subscribe("finishedPlaying", n.props.onEnded), n.player.subscribe("startedBuffering", n.props.onBuffer), n.player.subscribe("finishedBuffering", n.props.onBufferEnd), n.player.subscribe("error", n.props.onError), n.props.muted ? n.callPlayer("mute") : n.callPlayer("unmute"), n.props.onReady(), document.getElementById(n.playerID).querySelector("iframe").style.visibility = "visible")
                                }))
                            }))
                        }
                    }, {
                        key: "play",
                        value: function() {
                            this.callPlayer("play")
                        }
                    }, {
                        key: "pause",
                        value: function() {
                            this.callPlayer("pause")
                        }
                    }, {
                        key: "stop",
                        value: function() {}
                    }, {
                        key: "seekTo",
                        value: function(e) {
                            this.callPlayer("seek", e)
                        }
                    }, {
                        key: "setVolume",
                        value: function(e) {
                            this.callPlayer("setVolume", e)
                        }
                    }, {
                        key: "getDuration",
                        value: function() {
                            return this.callPlayer("getDuration")
                        }
                    }, {
                        key: "getCurrentTime",
                        value: function() {
                            return this.callPlayer("getCurrentPosition")
                        }
                    }, {
                        key: "getSecondsLoaded",
                        value: function() {
                            return null
                        }
                    }, {
                        key: "render",
                        value: function() {
                            var e = this.props.config.attributes;
                            return o.default.createElement("div", l({
                                style: {
                                    width: "100%",
                                    height: "100%"
                                },
                                id: this.playerID,
                                className: "fb-video",
                                "data-href": this.props.url,
                                "data-autoplay": this.props.playing ? "true" : "false",
                                "data-allowfullscreen": "true",
                                "data-controls": this.props.controls ? "true" : "false"
                            }, e))
                        }
                    }]) && c(t.prototype, n), v
                }(o.Component);
            t.default = b, y(b, "displayName", "Facebook"), y(b, "canPlay", i.canPlay.facebook), y(b, "loopOnEnded", !0)
        },
        50982: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l() {
                return l = Object.assign || function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var n = arguments[t];
                        for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r])
                    }
                    return e
                }, l.apply(this, arguments)
            }

            function c(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function s(e, t) {
                return s = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, s(e, t)
            }

            function f(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function p(e) {
                return p = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, p(e)
            }

            function y(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var d = "undefined" != typeof navigator,
                h = d && "MacIntel" === navigator.platform && navigator.maxTouchPoints > 1,
                b = d && (/iPad|iPhone|iPod/.test(navigator.userAgent) || h) && !window.MSStream,
                v = d && /^((?!chrome|android).)*safari/i.test(navigator.userAgent) && !window.MSStream,
                m = /www\.dropbox\.com\/.+/,
                P = /https:\/\/watch\.cloudflarestream\.com\/([a-z0-9]+)/,
                g = function(e) {
                    ! function(e, t) {
                        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                        e.prototype = Object.create(t && t.prototype, {
                            constructor: {
                                value: e,
                                writable: !0,
                                configurable: !0
                            }
                        }), t && s(e, t)
                    }(g, e);
                    var t, n, u, d, h = (u = g, d = function() {
                        if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                        if (Reflect.construct.sham) return !1;
                        if ("function" == typeof Proxy) return !0;
                        try {
                            return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                        } catch (e) {
                            return !1
                        }
                    }(), function() {
                        var e, t = p(u);
                        if (d) {
                            var n = p(this).constructor;
                            e = Reflect.construct(t, arguments, n)
                        } else e = t.apply(this, arguments);
                        return function(e, t) {
                            return !t || "object" !== r(t) && "function" != typeof t ? f(e) : t
                        }(this, e)
                    });

                    function g() {
                        var e;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                        }(this, g);
                        for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                        return y(f(e = h.call.apply(h, [this].concat(n))), "onReady", (function() {
                            var t;
                            return (t = e.props).onReady.apply(t, arguments)
                        })), y(f(e), "onPlay", (function() {
                            var t;
                            return (t = e.props).onPlay.apply(t, arguments)
                        })), y(f(e), "onBuffer", (function() {
                            var t;
                            return (t = e.props).onBuffer.apply(t, arguments)
                        })), y(f(e), "onBufferEnd", (function() {
                            var t;
                            return (t = e.props).onBufferEnd.apply(t, arguments)
                        })), y(f(e), "onPause", (function() {
                            var t;
                            return (t = e.props).onPause.apply(t, arguments)
                        })), y(f(e), "onEnded", (function() {
                            var t;
                            return (t = e.props).onEnded.apply(t, arguments)
                        })), y(f(e), "onError", (function() {
                            var t;
                            return (t = e.props).onError.apply(t, arguments)
                        })), y(f(e), "onPlayBackRateChange", (function(t) {
                            return e.props.onPlaybackRateChange(t.target.playbackRate)
                        })), y(f(e), "onEnablePIP", (function() {
                            var t;
                            return (t = e.props).onEnablePIP.apply(t, arguments)
                        })), y(f(e), "onDisablePIP", (function(t) {
                            var n = e.props,
                                r = n.onDisablePIP,
                                o = n.playing;
                            r(t), o && e.play()
                        })), y(f(e), "onPresentationModeChange", (function(t) {
                            if (e.player && (0, a.supportsWebKitPresentationMode)(e.player)) {
                                var n = e.player.webkitPresentationMode;
                                "picture-in-picture" === n ? e.onEnablePIP(t) : "inline" === n && e.onDisablePIP(t)
                            }
                        })), y(f(e), "onSeek", (function(t) {
                            e.props.onSeek(t.target.currentTime)
                        })), y(f(e), "mute", (function() {
                            e.player.muted = !0
                        })), y(f(e), "unmute", (function() {
                            e.player.muted = !1
                        })), y(f(e), "renderSourceElement", (function(e, t) {
                            return "string" == typeof e ? o.default.createElement("source", {
                                key: t,
                                src: e
                            }) : o.default.createElement("source", l({
                                key: t
                            }, e))
                        })), y(f(e), "renderTrack", (function(e, t) {
                            return o.default.createElement("track", l({
                                key: t
                            }, e))
                        })), y(f(e), "ref", (function(t) {
                            e.player && (e.prevPlayer = e.player), e.player = t
                        })), e
                    }
                    return t = g, (n = [{
                        key: "componentDidMount",
                        value: function() {
                            this.props.onMount && this.props.onMount(this), this.addListeners(this.player), this.player.src = this.getSource(this.props.url), b && this.player.load()
                        }
                    }, {
                        key: "componentDidUpdate",
                        value: function(e) {
                            this.shouldUseAudio(this.props) !== this.shouldUseAudio(e) && (this.removeListeners(this.prevPlayer, e.url), this.addListeners(this.player)), this.props.url === e.url || (0, a.isMediaStream)(this.props.url) || (this.player.srcObject = null)
                        }
                    }, {
                        key: "componentWillUnmount",
                        value: function() {
                            this.player.src = "", this.removeListeners(this.player), this.hls && this.hls.destroy()
                        }
                    }, {
                        key: "addListeners",
                        value: function(e) {
                            var t = this.props,
                                n = t.url,
                                r = t.playsinline;
                            e.addEventListener("play", this.onPlay), e.addEventListener("waiting", this.onBuffer), e.addEventListener("playing", this.onBufferEnd), e.addEventListener("pause", this.onPause), e.addEventListener("seeked", this.onSeek), e.addEventListener("ended", this.onEnded), e.addEventListener("error", this.onError), e.addEventListener("ratechange", this.onPlayBackRateChange), e.addEventListener("enterpictureinpicture", this.onEnablePIP), e.addEventListener("leavepictureinpicture", this.onDisablePIP), e.addEventListener("webkitpresentationmodechanged", this.onPresentationModeChange), this.shouldUseHLS(n) || e.addEventListener("canplay", this.onReady), r && (e.setAttribute("playsinline", ""), e.setAttribute("webkit-playsinline", ""), e.setAttribute("x5-playsinline", ""))
                        }
                    }, {
                        key: "removeListeners",
                        value: function(e, t) {
                            e.removeEventListener("canplay", this.onReady), e.removeEventListener("play", this.onPlay), e.removeEventListener("waiting", this.onBuffer), e.removeEventListener("playing", this.onBufferEnd), e.removeEventListener("pause", this.onPause), e.removeEventListener("seeked", this.onSeek), e.removeEventListener("ended", this.onEnded), e.removeEventListener("error", this.onError), e.removeEventListener("ratechange", this.onPlayBackRateChange), e.removeEventListener("enterpictureinpicture", this.onEnablePIP), e.removeEventListener("leavepictureinpicture", this.onDisablePIP), e.removeEventListener("webkitpresentationmodechanged", this.onPresentationModeChange), this.shouldUseHLS(t) || e.removeEventListener("canplay", this.onReady)
                        }
                    }, {
                        key: "shouldUseAudio",
                        value: function(e) {
                            return !e.config.forceVideo && !e.config.attributes.poster && (i.AUDIO_EXTENSIONS.test(e.url) || e.config.forceAudio)
                        }
                    }, {
                        key: "shouldUseHLS",
                        value: function(e) {
                            return !!this.props.config.forceHLS || !(!v || !this.props.config.forceSafariHLS) || !b && (i.HLS_EXTENSIONS.test(e) || P.test(e))
                        }
                    }, {
                        key: "shouldUseDASH",
                        value: function(e) {
                            return i.DASH_EXTENSIONS.test(e) || this.props.config.forceDASH
                        }
                    }, {
                        key: "shouldUseFLV",
                        value: function(e) {
                            return i.FLV_EXTENSIONS.test(e) || this.props.config.forceFLV
                        }
                    }, {
                        key: "load",
                        value: function(e) {
                            var t = this,
                                n = this.props.config,
                                r = n.hlsVersion,
                                o = n.hlsOptions,
                                i = n.dashVersion,
                                u = n.flvVersion;
                            if (this.hls && this.hls.destroy(), this.dash && this.dash.reset(), this.shouldUseHLS(e) && (0, a.getSDK)("https://cdn.jsdelivr.net/npm/hls.js@VERSION/dist/hls.min.js".replace("VERSION", r), "Hls").then((function(n) {
                                    if (t.hls = new n(o), t.hls.on(n.Events.MANIFEST_PARSED, (function() {
                                            t.props.onReady()
                                        })), t.hls.on(n.Events.ERROR, (function(e, r) {
                                            t.props.onError(e, r, t.hls, n)
                                        })), P.test(e)) {
                                        var r = e.match(P)[1];
                                        t.hls.loadSource("https://videodelivery.net/{id}/manifest/video.m3u8".replace("{id}", r))
                                    } else t.hls.loadSource(e);
                                    t.hls.attachMedia(t.player), t.props.onLoaded()
                                })), this.shouldUseDASH(e) && (0, a.getSDK)("https://cdnjs.cloudflare.com/ajax/libs/dashjs/VERSION/dash.all.min.js".replace("VERSION", i), "dashjs").then((function(n) {
                                    t.dash = n.MediaPlayer().create(), t.dash.initialize(t.player, e, t.props.playing), t.dash.on("error", t.props.onError), parseInt(i) < 3 ? t.dash.getDebug().setLogToBrowserConsole(!1) : t.dash.updateSettings({
                                        debug: {
                                            logLevel: n.Debug.LOG_LEVEL_NONE
                                        }
                                    }), t.props.onLoaded()
                                })), this.shouldUseFLV(e) && (0, a.getSDK)("https://cdn.jsdelivr.net/npm/flv.js@VERSION/dist/flv.min.js".replace("VERSION", u), "flvjs").then((function(n) {
                                    t.flv = n.createPlayer({
                                        type: "flv",
                                        url: e
                                    }), t.flv.attachMediaElement(t.player), t.flv.on(n.Events.ERROR, (function(e, r) {
                                        t.props.onError(e, r, t.flv, n)
                                    })), t.flv.load(), t.props.onLoaded()
                                })), e instanceof Array) this.player.load();
                            else if ((0, a.isMediaStream)(e)) try {
                                this.player.srcObject = e
                            } catch (t) {
                                this.player.src = window.URL.createObjectURL(e)
                            }
                        }
                    }, {
                        key: "play",
                        value: function() {
                            var e = this.player.play();
                            e && e.catch(this.props.onError)
                        }
                    }, {
                        key: "pause",
                        value: function() {
                            this.player.pause()
                        }
                    }, {
                        key: "stop",
                        value: function() {
                            this.player.removeAttribute("src"), this.dash && this.dash.reset()
                        }
                    }, {
                        key: "seekTo",
                        value: function(e) {
                            this.player.currentTime = e
                        }
                    }, {
                        key: "setVolume",
                        value: function(e) {
                            this.player.volume = e
                        }
                    }, {
                        key: "enablePIP",
                        value: function() {
                            this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player ? this.player.requestPictureInPicture() : (0, a.supportsWebKitPresentationMode)(this.player) && "picture-in-picture" !== this.player.webkitPresentationMode && this.player.webkitSetPresentationMode("picture-in-picture")
                        }
                    }, {
                        key: "disablePIP",
                        value: function() {
                            document.exitPictureInPicture && document.pictureInPictureElement === this.player ? document.exitPictureInPicture() : (0, a.supportsWebKitPresentationMode)(this.player) && "inline" !== this.player.webkitPresentationMode && this.player.webkitSetPresentationMode("inline")
                        }
                    }, {
                        key: "setPlaybackRate",
                        value: function(e) {
                            try {
                                this.player.playbackRate = e
                            } catch (e) {
                                this.props.onError(e)
                            }
                        }
                    }, {
                        key: "getDuration",
                        value: function() {
                            if (!this.player) return null;
                            var e = this.player,
                                t = e.duration,
                                n = e.seekable;
                            return t === 1 / 0 && n.length > 0 ? n.end(n.length - 1) : t
                        }
                    }, {
                        key: "getCurrentTime",
                        value: function() {
                            return this.player ? this.player.currentTime : null
                        }
                    }, {
                        key: "getSecondsLoaded",
                        value: function() {
                            if (!this.player) return null;
                            var e = this.player.buffered;
                            if (0 === e.length) return 0;
                            var t = e.end(e.length - 1),
                                n = this.getDuration();
                            return t > n ? n : t
                        }
                    }, {
                        key: "getSource",
                        value: function(e) {
                            var t = this.shouldUseHLS(e),
                                n = this.shouldUseDASH(e),
                                r = this.shouldUseFLV(e);
                            if (!(e instanceof Array || (0, a.isMediaStream)(e) || t || n || r)) return m.test(e) ? e.replace("www.dropbox.com", "dl.dropboxusercontent.com") : e
                        }
                    }, {
                        key: "render",
                        value: function() {
                            var e = this.props,
                                t = e.url,
                                n = e.playing,
                                r = e.loop,
                                a = e.controls,
                                i = e.muted,
                                u = e.config,
                                c = e.width,
                                s = e.height,
                                f = this.shouldUseAudio(this.props) ? "audio" : "video",
                                p = {
                                    width: "auto" === c ? c : "100%",
                                    height: "auto" === s ? s : "100%"
                                };
                            return o.default.createElement(f, l({
                                ref: this.ref,
                                src: this.getSource(t),
                                style: p,
                                preload: "auto",
                                autoPlay: n || void 0,
                                controls: a,
                                muted: i,
                                loop: r
                            }, u.attributes), t instanceof Array && t.map(this.renderSourceElement), u.tracks.map(this.renderTrack))
                        }
                    }]) && c(t.prototype, n), g
                }(o.Component);
            t.default = g, y(g, "displayName", "FilePlayer"), y(g, "canPlay", i.canPlay.file)
        },
        63110: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function c(e, t) {
                return c = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, c(e, t)
            }

            function s(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function f(e) {
                return f = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, f(e)
            }

            function p(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var y = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && c(e, t)
                }(d, e);
                var t, n, i, u, y = (i = d, u = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = f(i);
                    if (u) {
                        var n = f(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? s(e) : t
                    }(this, e)
                });

                function d() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, d);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return p(s(e = y.call.apply(y, [this].concat(n))), "callPlayer", a.callPlayer), p(s(e), "duration", null), p(s(e), "currentTime", null), p(s(e), "secondsLoaded", null), p(s(e), "mute", (function() {
                        e.callPlayer("mute")
                    })), p(s(e), "unmute", (function() {
                        e.callPlayer("unmute")
                    })), p(s(e), "ref", (function(t) {
                        e.iframe = t
                    })), e
                }
                return t = d, (n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e) {
                        var t = this;
                        (0, a.getSDK)("https://cdn.embed.ly/player-0.1.0.min.js", "playerjs").then((function(e) {
                            t.iframe && (t.player = new e.Player(t.iframe), t.player.on("ready", (function() {
                                setTimeout((function() {
                                    t.player.isReady = !0, t.player.setLoop(t.props.loop), t.props.muted && t.player.mute(), t.addListeners(t.player, t.props), t.props.onReady()
                                }), 500)
                            })))
                        }), this.props.onError)
                    }
                }, {
                    key: "addListeners",
                    value: function(e, t) {
                        var n = this;
                        e.on("play", t.onPlay), e.on("pause", t.onPause), e.on("ended", t.onEnded), e.on("error", t.onError), e.on("timeupdate", (function(e) {
                            var t = e.duration,
                                r = e.seconds;
                            n.duration = t, n.currentTime = r
                        }))
                    }
                }, {
                    key: "play",
                    value: function() {
                        this.callPlayer("play")
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {}
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("setCurrentTime", e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {
                        this.callPlayer("setVolume", e)
                    }
                }, {
                    key: "setLoop",
                    value: function(e) {
                        this.callPlayer("setLoop", e)
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.duration
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.currentTime
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return this.secondsLoaded
                    }
                }, {
                    key: "render",
                    value: function() {
                        return o.default.createElement("iframe", {
                            ref: this.ref,
                            src: this.props.url,
                            frameBorder: "0",
                            scrolling: "no",
                            style: {
                                width: "100%",
                                height: "100%"
                            },
                            allow: "encrypted-media; autoplay; fullscreen;",
                            referrerPolicy: "no-referrer-when-downgrade"
                        })
                    }
                }]) && l(t.prototype, n), d
            }(o.Component);
            t.default = y, p(y, "displayName", "Kaltura"), p(y, "canPlay", i.canPlay.kaltura)
        },
        72042: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function c(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var n = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? l(Object(n), !0).forEach((function(t) {
                        d(e, t, n[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                    }))
                }
                return e
            }

            function s(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function f(e, t) {
                return f = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, f(e, t)
            }

            function p(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function y(e) {
                return y = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, y(e)
            }

            function d(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var h = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && f(e, t)
                }(b, e);
                var t, n, u, l, h = (u = b, l = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = y(u);
                    if (l) {
                        var n = y(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? p(e) : t
                    }(this, e)
                });

                function b() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, b);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return d(p(e = h.call.apply(h, [this].concat(n))), "callPlayer", a.callPlayer), d(p(e), "duration", null), d(p(e), "currentTime", null), d(p(e), "secondsLoaded", null), d(p(e), "mute", (function() {})), d(p(e), "unmute", (function() {})), d(p(e), "ref", (function(t) {
                        e.iframe = t
                    })), e
                }
                return t = b, n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e) {
                        var t = this;
                        (0, a.getSDK)("https://widget.mixcloud.com/media/js/widgetApi.js", "Mixcloud").then((function(e) {
                            t.player = e.PlayerWidget(t.iframe), t.player.ready.then((function() {
                                t.player.events.play.on(t.props.onPlay), t.player.events.pause.on(t.props.onPause), t.player.events.ended.on(t.props.onEnded), t.player.events.error.on(t.props.error), t.player.events.progress.on((function(e, n) {
                                    t.currentTime = e, t.duration = n
                                })), t.props.onReady()
                            }))
                        }), this.props.onError)
                    }
                }, {
                    key: "play",
                    value: function() {
                        this.callPlayer("play")
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {}
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("seek", e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {}
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.duration
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.currentTime
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return null
                    }
                }, {
                    key: "render",
                    value: function() {
                        var e = this.props,
                            t = e.url,
                            n = e.config,
                            r = t.match(i.MATCH_URL_MIXCLOUD)[1],
                            u = (0, a.queryString)(c(c({}, n.options), {}, {
                                feed: "/".concat(r, "/")
                            }));
                        return o.default.createElement("iframe", {
                            key: r,
                            ref: this.ref,
                            style: {
                                width: "100%",
                                height: "100%"
                            },
                            src: "https://www.mixcloud.com/widget/iframe/?".concat(u),
                            frameBorder: "0",
                            allow: "autoplay"
                        })
                    }
                }], n && s(t.prototype, n), b
            }(o.Component);
            t.default = h, d(h, "displayName", "Mixcloud"), d(h, "canPlay", i.canPlay.mixcloud), d(h, "loopOnEnded", !0)
        },
        92006: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function c(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var n = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? l(Object(n), !0).forEach((function(t) {
                        d(e, t, n[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                    }))
                }
                return e
            }

            function s(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function f(e, t) {
                return f = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, f(e, t)
            }

            function p(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function y(e) {
                return y = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, y(e)
            }

            function d(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var h = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && f(e, t)
                }(h, e);
                var t, n, i, u, l = (i = h, u = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = y(i);
                    if (u) {
                        var n = y(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? p(e) : t
                    }(this, e)
                });

                function h() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, h);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return d(p(e = l.call.apply(l, [this].concat(n))), "callPlayer", a.callPlayer), d(p(e), "duration", null), d(p(e), "currentTime", null), d(p(e), "fractionLoaded", null), d(p(e), "mute", (function() {
                        e.setVolume(0)
                    })), d(p(e), "unmute", (function() {
                        null !== e.props.volume && e.setVolume(e.props.volume)
                    })), d(p(e), "ref", (function(t) {
                        e.iframe = t
                    })), e
                }
                return t = h, (n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e, t) {
                        var n = this;
                        (0, a.getSDK)("https://w.soundcloud.com/player/api.js", "SC").then((function(r) {
                            if (n.iframe) {
                                var o = r.Widget.Events,
                                    a = o.PLAY,
                                    i = o.PLAY_PROGRESS,
                                    u = o.PAUSE,
                                    l = o.FINISH,
                                    s = o.ERROR;
                                t || (n.player = r.Widget(n.iframe), n.player.bind(a, n.props.onPlay), n.player.bind(u, (function() {
                                    n.duration - n.currentTime < .05 || n.props.onPause()
                                })), n.player.bind(i, (function(e) {
                                    n.currentTime = e.currentPosition / 1e3, n.fractionLoaded = e.loadedProgress
                                })), n.player.bind(l, (function() {
                                    return n.props.onEnded()
                                })), n.player.bind(s, (function(e) {
                                    return n.props.onError(e)
                                }))), n.player.load(e, c(c({}, n.props.config.options), {}, {
                                    callback: function() {
                                        n.player.getDuration((function(e) {
                                            n.duration = e / 1e3, n.props.onReady()
                                        }))
                                    }
                                }))
                            }
                        }))
                    }
                }, {
                    key: "play",
                    value: function() {
                        this.callPlayer("play")
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {}
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("seekTo", 1e3 * e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {
                        this.callPlayer("setVolume", 100 * e)
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.duration
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.currentTime
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return this.fractionLoaded * this.duration
                    }
                }, {
                    key: "render",
                    value: function() {
                        var e = {
                            width: "100%",
                            height: "100%",
                            display: this.props.display
                        };
                        return o.default.createElement("iframe", {
                            ref: this.ref,
                            src: "https://w.soundcloud.com/player/?url=".concat(encodeURIComponent(this.props.url)),
                            style: e,
                            frameBorder: 0,
                            allow: "autoplay"
                        })
                    }
                }]) && s(t.prototype, n), h
            }(o.Component);
            t.default = h, d(h, "displayName", "SoundCloud"), d(h, "canPlay", i.canPlay.soundcloud), d(h, "loopOnEnded", !0)
        },
        51573: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function c(e, t) {
                return c = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, c(e, t)
            }

            function s(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function f(e) {
                return f = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, f(e)
            }

            function p(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var y = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && c(e, t)
                }(h, e);
                var t, n, u, y, d = (u = h, y = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = f(u);
                    if (y) {
                        var n = f(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? s(e) : t
                    }(this, e)
                });

                function h() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, h);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return p(s(e = d.call.apply(d, [this].concat(n))), "callPlayer", a.callPlayer), p(s(e), "duration", null), p(s(e), "currentTime", null), p(s(e), "secondsLoaded", null), p(s(e), "mute", (function() {
                        e.callPlayer("mute")
                    })), p(s(e), "unmute", (function() {
                        e.callPlayer("unmute")
                    })), p(s(e), "ref", (function(t) {
                        e.iframe = t
                    })), e
                }
                return t = h, (n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e) {
                        var t = this;
                        (0, a.getSDK)("https://cdn.embed.ly/player-0.1.0.min.js", "playerjs").then((function(e) {
                            t.iframe && (t.player = new e.Player(t.iframe), t.player.setLoop(t.props.loop), t.player.on("ready", t.props.onReady), t.player.on("play", t.props.onPlay), t.player.on("pause", t.props.onPause), t.player.on("seeked", t.props.onSeek), t.player.on("ended", t.props.onEnded), t.player.on("error", t.props.onError), t.player.on("timeupdate", (function(e) {
                                var n = e.duration,
                                    r = e.seconds;
                                t.duration = n, t.currentTime = r
                            })), t.player.on("buffered", (function(e) {
                                var n = e.percent;
                                t.duration && (t.secondsLoaded = t.duration * n)
                            })), t.props.muted && t.player.mute())
                        }), this.props.onError)
                    }
                }, {
                    key: "play",
                    value: function() {
                        this.callPlayer("play")
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {}
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("setCurrentTime", e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {
                        this.callPlayer("setVolume", 100 * e)
                    }
                }, {
                    key: "setLoop",
                    value: function(e) {
                        this.callPlayer("setLoop", e)
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.duration
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.currentTime
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return this.secondsLoaded
                    }
                }, {
                    key: "render",
                    value: function() {
                        var e = this.props.url.match(i.MATCH_URL_STREAMABLE)[1];
                        return o.default.createElement("iframe", {
                            ref: this.ref,
                            src: "https://streamable.com/o/".concat(e),
                            frameBorder: "0",
                            scrolling: "no",
                            style: {
                                width: "100%",
                                height: "100%"
                            },
                            allow: "encrypted-media; autoplay; fullscreen;"
                        })
                    }
                }]) && l(t.prototype, n), h
            }(o.Component);
            t.default = y, p(y, "displayName", "Streamable"), p(y, "canPlay", i.canPlay.streamable)
        },
        60318: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function c(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function s(e, t) {
                return s = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, s(e, t)
            }

            function f(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function p(e) {
                return p = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, p(e)
            }

            function y(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var d = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && s(e, t)
                }(b, e);
                var t, n, u, d, h = (u = b, d = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = p(u);
                    if (d) {
                        var n = p(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? f(e) : t
                    }(this, e)
                });

                function b() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, b);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return y(f(e = h.call.apply(h, [this].concat(n))), "callPlayer", a.callPlayer), y(f(e), "playerID", e.props.config.playerId || "".concat("twitch-player-").concat((0, a.randomString)())), y(f(e), "mute", (function() {
                        e.callPlayer("setMuted", !0)
                    })), y(f(e), "unmute", (function() {
                        e.callPlayer("setMuted", !1)
                    })), e
                }
                return t = b, n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e, t) {
                        var n = this,
                            r = this.props,
                            o = r.playsinline,
                            u = r.onError,
                            c = r.config,
                            s = r.controls,
                            f = i.MATCH_URL_TWITCH_CHANNEL.test(e),
                            p = f ? e.match(i.MATCH_URL_TWITCH_CHANNEL)[1] : e.match(i.MATCH_URL_TWITCH_VIDEO)[1];
                        t ? f ? this.player.setChannel(p) : this.player.setVideo("v" + p) : (0, a.getSDK)("https://player.twitch.tv/js/embed/v1.js", "Twitch").then((function(t) {
                            n.player = new t.Player(n.playerID, function(e) {
                                for (var t = 1; t < arguments.length; t++) {
                                    var n = null != arguments[t] ? arguments[t] : {};
                                    t % 2 ? l(Object(n), !0).forEach((function(t) {
                                        y(e, t, n[t])
                                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach((function(t) {
                                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                                    }))
                                }
                                return e
                            }({
                                video: f ? "" : p,
                                channel: f ? p : "",
                                height: "100%",
                                width: "100%",
                                playsinline: o,
                                autoplay: n.props.playing,
                                muted: n.props.muted,
                                controls: !!f || s,
                                time: (0, a.parseStartTime)(e)
                            }, c.options));
                            var r = t.Player,
                                i = r.READY,
                                u = r.PLAYING,
                                d = r.PAUSE,
                                h = r.ENDED,
                                b = r.ONLINE,
                                v = r.OFFLINE,
                                m = r.SEEK;
                            n.player.addEventListener(i, n.props.onReady), n.player.addEventListener(u, n.props.onPlay), n.player.addEventListener(d, n.props.onPause), n.player.addEventListener(h, n.props.onEnded), n.player.addEventListener(m, n.props.onSeek), n.player.addEventListener(b, n.props.onLoaded), n.player.addEventListener(v, n.props.onLoaded)
                        }), u)
                    }
                }, {
                    key: "play",
                    value: function() {
                        this.callPlayer("play")
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("seek", e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {
                        this.callPlayer("setVolume", e)
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.callPlayer("getDuration")
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.callPlayer("getCurrentTime")
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return null
                    }
                }, {
                    key: "render",
                    value: function() {
                        return o.default.createElement("div", {
                            style: {
                                width: "100%",
                                height: "100%"
                            },
                            id: this.playerID
                        })
                    }
                }], n && c(t.prototype, n), b
            }(o.Component);
            t.default = d, y(d, "displayName", "Twitch"), y(d, "canPlay", i.canPlay.twitch), y(d, "loopOnEnded", !0)
        },
        97783: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function c(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function s(e, t) {
                return s = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, s(e, t)
            }

            function f(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function p(e) {
                return p = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, p(e)
            }

            function y(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var d = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && s(e, t)
                }(b, e);
                var t, n, u, d, h = (u = b, d = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = p(u);
                    if (d) {
                        var n = p(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? f(e) : t
                    }(this, e)
                });

                function b() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, b);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return y(f(e = h.call.apply(h, [this].concat(n))), "callPlayer", a.callPlayer), y(f(e), "mute", (function() {
                        e.setVolume(0)
                    })), y(f(e), "unmute", (function() {
                        null !== e.props.volume && e.setVolume(e.props.volume)
                    })), y(f(e), "ref", (function(t) {
                        e.container = t
                    })), e
                }
                return t = b, n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e) {
                        var t = this,
                            n = this.props,
                            r = n.playing,
                            o = n.config,
                            u = n.onError,
                            c = n.onDuration,
                            s = e && e.match(i.MATCH_URL_VIDYARD)[1];
                        this.player && this.stop(), (0, a.getSDK)("https://play.vidyard.com/embed/v4.js", "VidyardV4", "onVidyardAPI").then((function(e) {
                            t.container && (e.api.addReadyListener((function(e, n) {
                                t.player || (t.player = n, t.player.on("ready", t.props.onReady), t.player.on("play", t.props.onPlay), t.player.on("pause", t.props.onPause), t.player.on("seek", t.props.onSeek), t.player.on("playerComplete", t.props.onEnded))
                            }), s), e.api.renderPlayer(function(e) {
                                for (var t = 1; t < arguments.length; t++) {
                                    var n = null != arguments[t] ? arguments[t] : {};
                                    t % 2 ? l(Object(n), !0).forEach((function(t) {
                                        y(e, t, n[t])
                                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach((function(t) {
                                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                                    }))
                                }
                                return e
                            }({
                                uuid: s,
                                container: t.container,
                                autoplay: r ? 1 : 0
                            }, o.options)), e.api.getPlayerMetadata(s).then((function(e) {
                                t.duration = e.length_in_seconds, c(e.length_in_seconds)
                            })))
                        }), u)
                    }
                }, {
                    key: "play",
                    value: function() {
                        this.callPlayer("play")
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {
                        window.VidyardV4.api.destroyPlayer(this.player)
                    }
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("seek", e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {
                        this.callPlayer("setVolume", e)
                    }
                }, {
                    key: "setPlaybackRate",
                    value: function(e) {
                        this.callPlayer("setPlaybackSpeed", e)
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.duration
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.callPlayer("currentTime")
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return null
                    }
                }, {
                    key: "render",
                    value: function() {
                        var e = {
                            width: "100%",
                            height: "100%",
                            display: this.props.display
                        };
                        return o.default.createElement("div", {
                            style: e
                        }, o.default.createElement("div", {
                            ref: this.ref
                        }))
                    }
                }], n && c(t.prototype, n), b
            }(o.Component);
            t.default = d, y(d, "displayName", "Vidyard"), y(d, "canPlay", i.canPlay.vidyard)
        },
        86164: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function c(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function s(e, t) {
                return s = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, s(e, t)
            }

            function f(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function p(e) {
                return p = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, p(e)
            }

            function y(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var d = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && s(e, t)
                }(h, e);
                var t, n, i, u, d = (i = h, u = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = p(i);
                    if (u) {
                        var n = p(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? f(e) : t
                    }(this, e)
                });

                function h() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, h);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return y(f(e = d.call.apply(d, [this].concat(n))), "callPlayer", a.callPlayer), y(f(e), "duration", null), y(f(e), "currentTime", null), y(f(e), "secondsLoaded", null), y(f(e), "mute", (function() {
                        e.setMuted(!0)
                    })), y(f(e), "unmute", (function() {
                        e.setMuted(!1)
                    })), y(f(e), "ref", (function(t) {
                        e.container = t
                    })), e
                }
                return t = h, n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e) {
                        var t = this;
                        this.duration = null, (0, a.getSDK)("https://player.vimeo.com/api/player.js", "Vimeo").then((function(n) {
                            if (t.container) {
                                var r = t.props.config,
                                    o = r.playerOptions,
                                    a = r.title;
                                t.player = new n.Player(t.container, function(e) {
                                    for (var t = 1; t < arguments.length; t++) {
                                        var n = null != arguments[t] ? arguments[t] : {};
                                        t % 2 ? l(Object(n), !0).forEach((function(t) {
                                            y(e, t, n[t])
                                        })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach((function(t) {
                                            Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                                        }))
                                    }
                                    return e
                                }({
                                    url: e,
                                    autoplay: t.props.playing,
                                    muted: t.props.muted,
                                    loop: t.props.loop,
                                    playsinline: t.props.playsinline,
                                    controls: t.props.controls
                                }, o)), t.player.ready().then((function() {
                                    var e = t.container.querySelector("iframe");
                                    e.style.width = "100%", e.style.height = "100%", a && (e.title = a)
                                })).catch(t.props.onError), t.player.on("loaded", (function() {
                                    t.props.onReady(), t.refreshDuration()
                                })), t.player.on("play", (function() {
                                    t.props.onPlay(), t.refreshDuration()
                                })), t.player.on("pause", t.props.onPause), t.player.on("seeked", (function(e) {
                                    return t.props.onSeek(e.seconds)
                                })), t.player.on("ended", t.props.onEnded), t.player.on("error", t.props.onError), t.player.on("timeupdate", (function(e) {
                                    var n = e.seconds;
                                    t.currentTime = n
                                })), t.player.on("progress", (function(e) {
                                    var n = e.seconds;
                                    t.secondsLoaded = n
                                })), t.player.on("bufferstart", t.props.onBuffer), t.player.on("bufferend", t.props.onBufferEnd), t.player.on("playbackratechange", (function(e) {
                                    return t.props.onPlaybackRateChange(e.playbackRate)
                                }))
                            }
                        }), this.props.onError)
                    }
                }, {
                    key: "refreshDuration",
                    value: function() {
                        var e = this;
                        this.player.getDuration().then((function(t) {
                            e.duration = t
                        }))
                    }
                }, {
                    key: "play",
                    value: function() {
                        var e = this.callPlayer("play");
                        e && e.catch(this.props.onError)
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {
                        this.callPlayer("unload")
                    }
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("setCurrentTime", e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {
                        this.callPlayer("setVolume", e)
                    }
                }, {
                    key: "setMuted",
                    value: function(e) {
                        this.callPlayer("setMuted", e)
                    }
                }, {
                    key: "setLoop",
                    value: function(e) {
                        this.callPlayer("setLoop", e)
                    }
                }, {
                    key: "setPlaybackRate",
                    value: function(e) {
                        this.callPlayer("setPlaybackRate", e)
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.duration
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.currentTime
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return this.secondsLoaded
                    }
                }, {
                    key: "render",
                    value: function() {
                        var e = {
                            width: "100%",
                            height: "100%",
                            overflow: "hidden",
                            display: this.props.display
                        };
                        return o.default.createElement("div", {
                            key: this.props.url,
                            ref: this.ref,
                            style: e
                        })
                    }
                }], n && c(t.prototype, n), h
            }(o.Component);
            t.default = d, y(d, "displayName", "Vimeo"), y(d, "canPlay", i.canPlay.vimeo), y(d, "forceLoad", !0)
        },
        65151: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function c(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var n = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? l(Object(n), !0).forEach((function(t) {
                        d(e, t, n[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                    }))
                }
                return e
            }

            function s(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function f(e, t) {
                return f = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, f(e, t)
            }

            function p(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function y(e) {
                return y = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, y(e)
            }

            function d(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var h = function(e) {
                ! function(e, t) {
                    if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && f(e, t)
                }(b, e);
                var t, n, u, l, h = (u = b, l = function() {
                    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                    if (Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                    } catch (e) {
                        return !1
                    }
                }(), function() {
                    var e, t = y(u);
                    if (l) {
                        var n = y(this).constructor;
                        e = Reflect.construct(t, arguments, n)
                    } else e = t.apply(this, arguments);
                    return function(e, t) {
                        return !t || "object" !== r(t) && "function" != typeof t ? p(e) : t
                    }(this, e)
                });

                function b() {
                    var e;
                    ! function(e, t) {
                        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                    }(this, b);
                    for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                    return d(p(e = h.call.apply(h, [this].concat(n))), "callPlayer", a.callPlayer), d(p(e), "playerID", e.props.config.playerId || "".concat("wistia-player-").concat((0, a.randomString)())), d(p(e), "onPlay", (function() {
                        var t;
                        return (t = e.props).onPlay.apply(t, arguments)
                    })), d(p(e), "onPause", (function() {
                        var t;
                        return (t = e.props).onPause.apply(t, arguments)
                    })), d(p(e), "onSeek", (function() {
                        var t;
                        return (t = e.props).onSeek.apply(t, arguments)
                    })), d(p(e), "onEnded", (function() {
                        var t;
                        return (t = e.props).onEnded.apply(t, arguments)
                    })), d(p(e), "onPlaybackRateChange", (function() {
                        var t;
                        return (t = e.props).onPlaybackRateChange.apply(t, arguments)
                    })), d(p(e), "mute", (function() {
                        e.callPlayer("mute")
                    })), d(p(e), "unmute", (function() {
                        e.callPlayer("unmute")
                    })), e
                }
                return t = b, n = [{
                    key: "componentDidMount",
                    value: function() {
                        this.props.onMount && this.props.onMount(this)
                    }
                }, {
                    key: "load",
                    value: function(e) {
                        var t = this,
                            n = this.props,
                            r = n.playing,
                            o = n.muted,
                            i = n.controls,
                            u = n.onReady,
                            l = n.config,
                            s = n.onError;
                        (0, a.getSDK)("https://fast.wistia.com/assets/external/E-v1.js", "Wistia").then((function(e) {
                            l.customControls && l.customControls.forEach((function(t) {
                                return e.defineControl(t)
                            })), window._wq = window._wq || [], window._wq.push({
                                id: t.playerID,
                                options: c({
                                    autoPlay: r,
                                    silentAutoPlay: "allow",
                                    muted: o,
                                    controlsVisibleOnLoad: i,
                                    fullscreenButton: i,
                                    playbar: i,
                                    playbackRateControl: i,
                                    qualityControl: i,
                                    volumeControl: i,
                                    settingsControl: i,
                                    smallPlayButton: i
                                }, l.options),
                                onReady: function(e) {
                                    t.player = e, t.unbind(), t.player.bind("play", t.onPlay), t.player.bind("pause", t.onPause), t.player.bind("seek", t.onSeek), t.player.bind("end", t.onEnded), t.player.bind("playbackratechange", t.onPlaybackRateChange), u()
                                }
                            })
                        }), s)
                    }
                }, {
                    key: "unbind",
                    value: function() {
                        this.player.unbind("play", this.onPlay), this.player.unbind("pause", this.onPause), this.player.unbind("seek", this.onSeek), this.player.unbind("end", this.onEnded), this.player.unbind("playbackratechange", this.onPlaybackRateChange)
                    }
                }, {
                    key: "play",
                    value: function() {
                        this.callPlayer("play")
                    }
                }, {
                    key: "pause",
                    value: function() {
                        this.callPlayer("pause")
                    }
                }, {
                    key: "stop",
                    value: function() {
                        this.unbind(), this.callPlayer("remove")
                    }
                }, {
                    key: "seekTo",
                    value: function(e) {
                        this.callPlayer("time", e)
                    }
                }, {
                    key: "setVolume",
                    value: function(e) {
                        this.callPlayer("volume", e)
                    }
                }, {
                    key: "setPlaybackRate",
                    value: function(e) {
                        this.callPlayer("playbackRate", e)
                    }
                }, {
                    key: "getDuration",
                    value: function() {
                        return this.callPlayer("duration")
                    }
                }, {
                    key: "getCurrentTime",
                    value: function() {
                        return this.callPlayer("time")
                    }
                }, {
                    key: "getSecondsLoaded",
                    value: function() {
                        return null
                    }
                }, {
                    key: "render",
                    value: function() {
                        var e = this.props.url,
                            t = e && e.match(i.MATCH_URL_WISTIA)[1],
                            n = "wistia_embed wistia_async_".concat(t);
                        return o.default.createElement("div", {
                            id: this.playerID,
                            key: t,
                            className: n,
                            style: {
                                width: "100%",
                                height: "100%"
                            }
                        })
                    }
                }], n && s(t.prototype, n), b
            }(o.Component);
            t.default = h, d(h, "displayName", "Wistia"), d(h, "canPlay", i.canPlay.wistia), d(h, "loopOnEnded", !0)
        },
        68293: (e, t, n) => {
            function r(e) {
                return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, r(e)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var o = function(e) {
                    if (e && e.__esModule) return e;
                    if (null === e || "object" !== r(e) && "function" != typeof e) return {
                        default: e
                    };
                    var t = u();
                    if (t && t.has(e)) return t.get(e);
                    var n = {},
                        o = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var a in e)
                        if (Object.prototype.hasOwnProperty.call(e, a)) {
                            var i = o ? Object.getOwnPropertyDescriptor(e, a) : null;
                            i && (i.get || i.set) ? Object.defineProperty(n, a, i) : n[a] = e[a]
                        }
                    return n.default = e, t && t.set(e, n), n
                }(n(12196)),
                a = n(64534),
                i = n(52839);

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var r = Object.getOwnPropertySymbols(e);
                    t && (r = r.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, r)
                }
                return n
            }

            function c(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var n = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? l(Object(n), !0).forEach((function(t) {
                        b(e, t, n[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                    }))
                }
                return e
            }

            function s(e, t) {
                return function(e) {
                    if (Array.isArray(e)) return e
                }(e) || function(e, t) {
                    if ("undefined" != typeof Symbol && Symbol.iterator in Object(e)) {
                        var n = [],
                            r = !0,
                            o = !1,
                            a = void 0;
                        try {
                            for (var i, u = e[Symbol.iterator](); !(r = (i = u.next()).done) && (n.push(i.value), !t || n.length !== t); r = !0);
                        } catch (e) {
                            o = !0, a = e
                        } finally {
                            try {
                                r || null == u.return || u.return()
                            } finally {
                                if (o) throw a
                            }
                        }
                        return n
                    }
                }(e, t) || function(e, t) {
                    if (e) {
                        if ("string" == typeof e) return f(e, t);
                        var n = Object.prototype.toString.call(e).slice(8, -1);
                        return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? f(e, t) : void 0
                    }
                }(e, t) || function() {
                    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }()
            }

            function f(e, t) {
                (null == t || t > e.length) && (t = e.length);
                for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
                return r
            }

            function p(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var r = t[n];
                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                }
            }

            function y(e, t) {
                return y = Object.setPrototypeOf || function(e, t) {
                    return e.__proto__ = t, e
                }, y(e, t)
            }

            function d(e) {
                if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return e
            }

            function h(e) {
                return h = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                    return e.__proto__ || Object.getPrototypeOf(e)
                }, h(e)
            }

            function b(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }
            var v = /[?&](?:list|channel)=([a-zA-Z0-9_-]+)/,
                m = /user\/([a-zA-Z0-9_-]+)\/?/,
                P = /youtube-nocookie\.com/,
                g = function(e) {
                    ! function(e, t) {
                        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                        e.prototype = Object.create(t && t.prototype, {
                            constructor: {
                                value: e,
                                writable: !0,
                                configurable: !0
                            }
                        }), t && y(e, t)
                    }(g, e);
                    var t, n, u, l, f = (u = g, l = function() {
                        if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
                        if (Reflect.construct.sham) return !1;
                        if ("function" == typeof Proxy) return !0;
                        try {
                            return Date.prototype.toString.call(Reflect.construct(Date, [], (function() {}))), !0
                        } catch (e) {
                            return !1
                        }
                    }(), function() {
                        var e, t = h(u);
                        if (l) {
                            var n = h(this).constructor;
                            e = Reflect.construct(t, arguments, n)
                        } else e = t.apply(this, arguments);
                        return function(e, t) {
                            return !t || "object" !== r(t) && "function" != typeof t ? d(e) : t
                        }(this, e)
                    });

                    function g() {
                        var e;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                        }(this, g);
                        for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
                        return b(d(e = f.call.apply(f, [this].concat(n))), "callPlayer", a.callPlayer), b(d(e), "parsePlaylist", (function(t) {
                            return t instanceof Array ? {
                                listType: "playlist",
                                playlist: t.map(e.getID).join(",")
                            } : v.test(t) ? {
                                listType: "playlist",
                                list: s(t.match(v), 2)[1].replace(/^UC/, "UU")
                            } : m.test(t) ? {
                                listType: "user_uploads",
                                list: s(t.match(m), 2)[1]
                            } : {}
                        })), b(d(e), "onStateChange", (function(t) {
                            var n = t.data,
                                r = e.props,
                                o = r.onPlay,
                                a = r.onPause,
                                i = r.onBuffer,
                                u = r.onBufferEnd,
                                l = r.onEnded,
                                c = r.onReady,
                                s = r.loop,
                                f = r.config,
                                p = f.playerVars,
                                y = f.onUnstarted,
                                d = window.YT.PlayerState,
                                h = d.UNSTARTED,
                                b = d.PLAYING,
                                v = d.PAUSED,
                                m = d.BUFFERING,
                                P = d.ENDED,
                                g = d.CUED;
                            if (n === h && y(), n === b && (o(), u()), n === v && a(), n === m && i(), n === P) {
                                var O = !!e.callPlayer("getPlaylist");
                                s && !O && (p.start ? e.seekTo(p.start) : e.play()), l()
                            }
                            n === g && c()
                        })), b(d(e), "mute", (function() {
                            e.callPlayer("mute")
                        })), b(d(e), "unmute", (function() {
                            e.callPlayer("unMute")
                        })), b(d(e), "ref", (function(t) {
                            e.container = t
                        })), e
                    }
                    return t = g, (n = [{
                        key: "componentDidMount",
                        value: function() {
                            this.props.onMount && this.props.onMount(this)
                        }
                    }, {
                        key: "getID",
                        value: function(e) {
                            return !e || e instanceof Array || v.test(e) ? null : e.match(i.MATCH_URL_YOUTUBE)[1]
                        }
                    }, {
                        key: "load",
                        value: function(e, t) {
                            var n = this,
                                r = this.props,
                                o = r.playing,
                                i = r.muted,
                                u = r.playsinline,
                                l = r.controls,
                                s = r.loop,
                                f = r.config,
                                p = r.onError,
                                y = f.playerVars,
                                d = f.embedOptions,
                                h = this.getID(e);
                            if (t) return v.test(e) || m.test(e) || e instanceof Array ? void this.player.loadPlaylist(this.parsePlaylist(e)) : void this.player.cueVideoById({
                                videoId: h,
                                startSeconds: (0, a.parseStartTime)(e) || y.start,
                                endSeconds: (0, a.parseEndTime)(e) || y.end
                            });
                            (0, a.getSDK)("https://www.youtube.com/iframe_api", "YT", "onYouTubeIframeAPIReady", (function(e) {
                                return e.loaded
                            })).then((function(t) {
                                n.container && (n.player = new t.Player(n.container, c({
                                    width: "100%",
                                    height: "100%",
                                    videoId: h,
                                    playerVars: c(c({
                                        autoplay: o ? 1 : 0,
                                        mute: i ? 1 : 0,
                                        controls: l ? 1 : 0,
                                        start: (0, a.parseStartTime)(e),
                                        end: (0, a.parseEndTime)(e),
                                        origin: window.location.origin,
                                        playsinline: u ? 1 : 0
                                    }, n.parsePlaylist(e)), y),
                                    events: {
                                        onReady: function() {
                                            s && n.player.setLoop(!0), n.props.onReady()
                                        },
                                        onPlaybackRateChange: function(e) {
                                            return n.props.onPlaybackRateChange(e.data)
                                        },
                                        onStateChange: n.onStateChange,
                                        onError: function(e) {
                                            return p(e.data)
                                        }
                                    },
                                    host: P.test(e) ? "https://www.youtube-nocookie.com" : void 0
                                }, d)))
                            }), p), d.events && console.warn("Using `embedOptions.events` will likely break things. Use ReactPlayer’s callback props instead, eg onReady, onPlay, onPause")
                        }
                    }, {
                        key: "play",
                        value: function() {
                            this.callPlayer("playVideo")
                        }
                    }, {
                        key: "pause",
                        value: function() {
                            this.callPlayer("pauseVideo")
                        }
                    }, {
                        key: "stop",
                        value: function() {
                            document.body.contains(this.callPlayer("getIframe")) && this.callPlayer("stopVideo")
                        }
                    }, {
                        key: "seekTo",
                        value: function(e) {
                            this.callPlayer("seekTo", e), this.props.playing || this.pause()
                        }
                    }, {
                        key: "setVolume",
                        value: function(e) {
                            this.callPlayer("setVolume", 100 * e)
                        }
                    }, {
                        key: "setPlaybackRate",
                        value: function(e) {
                            this.callPlayer("setPlaybackRate", e)
                        }
                    }, {
                        key: "setLoop",
                        value: function(e) {
                            this.callPlayer("setLoop", e)
                        }
                    }, {
                        key: "getDuration",
                        value: function() {
                            return this.callPlayer("getDuration")
                        }
                    }, {
                        key: "getCurrentTime",
                        value: function() {
                            return this.callPlayer("getCurrentTime")
                        }
                    }, {
                        key: "getSecondsLoaded",
                        value: function() {
                            return this.callPlayer("getVideoLoadedFraction") * this.getDuration()
                        }
                    }, {
                        key: "render",
                        value: function() {
                            var e = {
                                width: "100%",
                                height: "100%",
                                display: this.props.display
                            };
                            return o.default.createElement("div", {
                                style: e
                            }, o.default.createElement("div", {
                                ref: this.ref
                            }))
                        }
                    }]) && p(t.prototype, n), g
                }(o.Component);
            t.default = g, b(g, "displayName", "YouTube"), b(g, "canPlay", i.canPlay.youtube)
        },
        16792: (e, t, n) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var r = n(12196),
                o = n(64534),
                a = n(52839);

            function i(e) {
                return i = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, i(e)
            }

            function u() {
                if ("function" != typeof WeakMap) return null;
                var e = new WeakMap;
                return u = function() {
                    return e
                }, e
            }

            function l(e) {
                if (e && e.__esModule) return e;
                if (null === e || "object" !== i(e) && "function" != typeof e) return {
                    default: e
                };
                var t = u();
                if (t && t.has(e)) return t.get(e);
                var n = {},
                    r = Object.defineProperty && Object.getOwnPropertyDescriptor;
                for (var o in e)
                    if (Object.prototype.hasOwnProperty.call(e, o)) {
                        var a = r ? Object.getOwnPropertyDescriptor(e, o) : null;
                        a && (a.get || a.set) ? Object.defineProperty(n, o, a) : n[o] = e[o]
                    }
                return n.default = e, t && t.set(e, n), n
            }
            var c = [{
                key: "youtube",
                name: "YouTube",
                canPlay: a.canPlay.youtube,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(68293))
                    }))
                }))
            }, {
                key: "soundcloud",
                name: "SoundCloud",
                canPlay: a.canPlay.soundcloud,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(92006))
                    }))
                }))
            }, {
                key: "vimeo",
                name: "Vimeo",
                canPlay: a.canPlay.vimeo,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(86164))
                    }))
                }))
            }, {
                key: "facebook",
                name: "Facebook",
                canPlay: a.canPlay.facebook,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(66636))
                    }))
                }))
            }, {
                key: "streamable",
                name: "Streamable",
                canPlay: a.canPlay.streamable,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(51573))
                    }))
                }))
            }, {
                key: "wistia",
                name: "Wistia",
                canPlay: a.canPlay.wistia,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(65151))
                    }))
                }))
            }, {
                key: "twitch",
                name: "Twitch",
                canPlay: a.canPlay.twitch,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(60318))
                    }))
                }))
            }, {
                key: "dailymotion",
                name: "DailyMotion",
                canPlay: a.canPlay.dailymotion,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(41763))
                    }))
                }))
            }, {
                key: "mixcloud",
                name: "Mixcloud",
                canPlay: a.canPlay.mixcloud,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(72042))
                    }))
                }))
            }, {
                key: "vidyard",
                name: "Vidyard",
                canPlay: a.canPlay.vidyard,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(97783))
                    }))
                }))
            }, {
                key: "kaltura",
                name: "Kaltura",
                canPlay: a.canPlay.kaltura,
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(63110))
                    }))
                }))
            }, {
                key: "file",
                name: "FilePlayer",
                canPlay: a.canPlay.file,
                canEnablePIP: function(e) {
                    return a.canPlay.file(e) && (document.pictureInPictureEnabled || (0, o.supportsWebKitPresentationMode)()) && !a.AUDIO_EXTENSIONS.test(e)
                },
                lazyPlayer: (0, r.lazy)((function() {
                    return Promise.resolve().then((function() {
                        return l(n(50982))
                    }))
                }))
            }];
            t.default = c
        },
        43482: (e, t, n) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.defaultProps = t.propTypes = void 0;
            var r, o = (r = n(61605)) && r.__esModule ? r : {
                    default: r
                },
                a = o.default.string,
                i = o.default.bool,
                u = o.default.number,
                l = o.default.array,
                c = o.default.oneOfType,
                s = o.default.shape,
                f = o.default.object,
                p = o.default.func,
                y = o.default.node,
                d = {
                    url: c([a, l, f]),
                    playing: i,
                    loop: i,
                    controls: i,
                    volume: u,
                    muted: i,
                    playbackRate: u,
                    width: c([a, u]),
                    height: c([a, u]),
                    style: f,
                    progressInterval: u,
                    playsinline: i,
                    pip: i,
                    stopOnUnmount: i,
                    light: c([i, a, f]),
                    playIcon: y,
                    previewTabIndex: u,
                    fallback: y,
                    oEmbedUrl: a,
                    wrapper: c([a, p, s({
                        render: p.isRequired
                    })]),
                    config: s({
                        soundcloud: s({
                            options: f
                        }),
                        youtube: s({
                            playerVars: f,
                            embedOptions: f,
                            onUnstarted: p
                        }),
                        facebook: s({
                            appId: a,
                            version: a,
                            playerId: a,
                            attributes: f
                        }),
                        dailymotion: s({
                            params: f
                        }),
                        vimeo: s({
                            playerOptions: f,
                            title: a
                        }),
                        file: s({
                            attributes: f,
                            tracks: l,
                            forceVideo: i,
                            forceAudio: i,
                            forceHLS: i,
                            forceSafariHLS: i,
                            forceDASH: i,
                            forceFLV: i,
                            hlsOptions: f,
                            hlsVersion: a,
                            dashVersion: a,
                            flvVersion: a
                        }),
                        wistia: s({
                            options: f,
                            playerId: a,
                            customControls: l
                        }),
                        mixcloud: s({
                            options: f
                        }),
                        twitch: s({
                            options: f,
                            playerId: a
                        }),
                        vidyard: s({
                            options: f
                        })
                    }),
                    onReady: p,
                    onStart: p,
                    onPlay: p,
                    onPause: p,
                    onBuffer: p,
                    onBufferEnd: p,
                    onEnded: p,
                    onError: p,
                    onDuration: p,
                    onSeek: p,
                    onPlaybackRateChange: p,
                    onProgress: p,
                    onClickPreview: p,
                    onEnablePIP: p,
                    onDisablePIP: p
                };
            t.propTypes = d;
            var h = function() {},
                b = {
                    playing: !1,
                    loop: !1,
                    controls: !1,
                    volume: null,
                    muted: !1,
                    playbackRate: 1,
                    width: "640px",
                    height: "360px",
                    style: {},
                    progressInterval: 1e3,
                    playsinline: !1,
                    pip: !1,
                    stopOnUnmount: !0,
                    light: !1,
                    fallback: null,
                    wrapper: "div",
                    previewTabIndex: 0,
                    oEmbedUrl: "https://noembed.com/embed?url={url}",
                    config: {
                        soundcloud: {
                            options: {
                                visual: !0,
                                buying: !1,
                                liking: !1,
                                download: !1,
                                sharing: !1,
                                show_comments: !1,
                                show_playcount: !1
                            }
                        },
                        youtube: {
                            playerVars: {
                                playsinline: 1,
                                showinfo: 0,
                                rel: 0,
                                iv_load_policy: 3,
                                modestbranding: 1
                            },
                            embedOptions: {},
                            onUnstarted: h
                        },
                        facebook: {
                            appId: "1309697205772819",
                            version: "v3.3",
                            playerId: null,
                            attributes: {}
                        },
                        dailymotion: {
                            params: {
                                api: 1,
                                "endscreen-enable": !1
                            }
                        },
                        vimeo: {
                            playerOptions: {
                                autopause: !1,
                                byline: !1,
                                portrait: !1,
                                title: !1
                            },
                            title: null
                        },
                        file: {
                            attributes: {},
                            tracks: [],
                            forceVideo: !1,
                            forceAudio: !1,
                            forceHLS: !1,
                            forceDASH: !1,
                            forceFLV: !1,
                            hlsOptions: {},
                            hlsVersion: "1.1.4",
                            dashVersion: "3.1.3",
                            flvVersion: "1.5.0"
                        },
                        wistia: {
                            options: {},
                            playerId: null,
                            customControls: null
                        },
                        mixcloud: {
                            options: {
                                hide_cover: 1
                            }
                        },
                        twitch: {
                            options: {},
                            playerId: null
                        },
                        vidyard: {
                            options: {}
                        }
                    },
                    onReady: h,
                    onStart: h,
                    onPlay: h,
                    onPause: h,
                    onBuffer: h,
                    onBufferEnd: h,
                    onEnded: h,
                    onError: h,
                    onDuration: h,
                    onSeek: h,
                    onPlaybackRateChange: h,
                    onProgress: h,
                    onClickPreview: h,
                    onEnablePIP: h,
                    onDisablePIP: h
                };
            t.defaultProps = b
        },
        64534: (e, t, n) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.parseStartTime = function(e) {
                return f(e, u)
            }, t.parseEndTime = function(e) {
                return f(e, l)
            }, t.randomString = function() {
                return Math.random().toString(36).substr(2, 5)
            }, t.queryString = function(e) {
                return Object.keys(e).map((function(t) {
                    return "".concat(t, "=").concat(e[t])
                })).join("&")
            }, t.getSDK = function(e, t) {
                var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : null,
                    o = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : function() {
                        return !0
                    },
                    a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : r.default,
                    i = p(t);
                return i && o(i) ? Promise.resolve(i) : new Promise((function(r, o) {
                    if (y[e]) y[e].push({
                        resolve: r,
                        reject: o
                    });
                    else {
                        y[e] = [{
                            resolve: r,
                            reject: o
                        }];
                        var i = function(t) {
                            y[e].forEach((function(e) {
                                return e.resolve(t)
                            }))
                        };
                        if (n) {
                            var u = window[n];
                            window[n] = function() {
                                u && u(), i(p(t))
                            }
                        }
                        a(e, (function(r) {
                            r ? (y[e].forEach((function(e) {
                                return e.reject(r)
                            })), y[e] = null) : n || i(p(t))
                        }))
                    }
                }))
            }, t.getConfig = function(e, t) {
                return (0, o.default)(t.config, e.config)
            }, t.omit = function(e) {
                for (var t, n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), o = 1; o < n; o++) r[o - 1] = arguments[o];
                for (var a = (t = []).concat.apply(t, r), i = {}, u = 0, l = Object.keys(e); u < l.length; u++) {
                    var c = l[u]; - 1 === a.indexOf(c) && (i[c] = e[c])
                }
                return i
            }, t.callPlayer = function(e) {
                var t;
                if (!this.player || !this.player[e]) {
                    var n = "ReactPlayer: ".concat(this.constructor.displayName, " player could not call %c").concat(e, "%c – ");
                    return this.player ? this.player[e] || (n += "The method was not available") : n += "The player was not available", console.warn(n, "font-weight: bold", ""), null
                }
                for (var r = arguments.length, o = new Array(r > 1 ? r - 1 : 0), a = 1; a < r; a++) o[a - 1] = arguments[a];
                return (t = this.player)[e].apply(t, o)
            }, t.isMediaStream = function(e) {
                return "undefined" != typeof window && void 0 !== window.MediaStream && e instanceof window.MediaStream
            }, t.isBlobUrl = function(e) {
                return /^blob:/.test(e)
            }, t.supportsWebKitPresentationMode = function() {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : document.createElement("video"),
                    t = !1 === /iPhone|iPod/.test(navigator.userAgent);
                return e.webkitSupportsPresentationMode && "function" == typeof e.webkitSetPresentationMode && t
            };
            var r = a(n(72764)),
                o = a(n(51758));

            function a(e) {
                return e && e.__esModule ? e : {
                    default: e
                }
            }

            function i(e, t) {
                (null == t || t > e.length) && (t = e.length);
                for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
                return r
            }
            var u = /[?&#](?:start|t)=([0-9hms]+)/,
                l = /[?&#]end=([0-9hms]+)/,
                c = /(\d+)(h|m|s)/g,
                s = /^\d+$/;

            function f(e, t) {
                if (!(e instanceof Array)) {
                    var n = e.match(t);
                    if (n) {
                        var r = n[1];
                        if (r.match(c)) return function(e) {
                            for (var t, n, r = 0, o = c.exec(e); null !== o;) {
                                var a = (n = 3, function(e) {
                                        if (Array.isArray(e)) return e
                                    }(t = o) || function(e, t) {
                                        if ("undefined" != typeof Symbol && Symbol.iterator in Object(e)) {
                                            var n = [],
                                                r = !0,
                                                o = !1,
                                                a = void 0;
                                            try {
                                                for (var i, u = e[Symbol.iterator](); !(r = (i = u.next()).done) && (n.push(i.value), !t || n.length !== t); r = !0);
                                            } catch (e) {
                                                o = !0, a = e
                                            } finally {
                                                try {
                                                    r || null == u.return || u.return()
                                                } finally {
                                                    if (o) throw a
                                                }
                                            }
                                            return n
                                        }
                                    }(t, n) || function(e, t) {
                                        if (e) {
                                            if ("string" == typeof e) return i(e, t);
                                            var n = Object.prototype.toString.call(e).slice(8, -1);
                                            return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? i(e, t) : void 0
                                        }
                                    }(t, n) || function() {
                                        throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                                    }()),
                                    u = a[1],
                                    l = a[2];
                                "h" === l && (r += 60 * parseInt(u, 10) * 60), "m" === l && (r += 60 * parseInt(u, 10)), "s" === l && (r += parseInt(u, 10)), o = c.exec(e)
                            }
                            return r
                        }(r);
                        if (s.test(r)) return parseInt(r)
                    }
                }
            }

            function p(e) {
                return window[e] ? window[e] : window.exports && window.exports[e] ? window.exports[e] : window.module && window.module.exports && window.module.exports[e] ? window.module.exports[e] : null
            }
            var y = {}
        },
        72185: e => {
            var t = Number.isNaN || function(e) {
                return "number" == typeof e && e != e
            };

            function n(e, n) {
                if (e.length !== n.length) return !1;
                for (var r = 0; r < e.length; r++)
                    if (!((o = e[r]) === (a = n[r]) || t(o) && t(a))) return !1;
                var o, a;
                return !0
            }
            e.exports = function(e, t) {
                var r;
                void 0 === t && (t = n);
                var o, a = [],
                    i = !1;
                return function() {
                    for (var n = [], u = 0; u < arguments.length; u++) n[u] = arguments[u];
                    return i && r === this && t(n, a) || (o = e.apply(this, n), i = !0, r = this, a = n), o
                }
            }
        }
    }
]);
//# sourceMappingURL=6420.59000781190d348f.js.map