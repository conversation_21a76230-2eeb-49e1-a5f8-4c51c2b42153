(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_apollo_client_link_core_ApolloLink_js"], {

        /***/
        "../../node_modules/@apollo/client/link/core/ApolloLink.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    ApolloLink: () => ( /* binding */ ApolloLink)
                    /* harmony export */
                });
                /* harmony import */
                var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/tslib/tslib.es6.js");
                /* harmony import */
                var _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/index.js");
                /* harmony import */
                var _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/zen-observable-ts/module.js");
                /* harmony import */
                var _utils_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@apollo/client/link/utils/createOperation.js");
                /* harmony import */
                var _utils_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/@apollo/client/link/utils/transformOperation.js");
                /* harmony import */
                var _utils_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/@apollo/client/link/utils/validateOperation.js");




                function passthrough(op, forward) {
                    return (forward ? forward(op) : _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of());
                }

                function toLink(handler) {
                    return typeof handler === 'function' ? new ApolloLink(handler) : handler;
                }

                function isTerminating(link) {
                    return link.request.length <= 1;
                }
                var LinkError = (function(_super) {
                    (0, tslib__WEBPACK_IMPORTED_MODULE_2__.__extends)(LinkError, _super);

                    function LinkError(message, link) {
                        var _this = _super.call(this, message) || this;
                        _this.link = link;
                        return _this;
                    }
                    return LinkError;
                }(Error));
                var ApolloLink = (function() {
                    function ApolloLink(request) {
                        if (request)
                            this.request = request;
                    }
                    ApolloLink.empty = function() {
                        return new ApolloLink(function() {
                            return _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of();
                        });
                    };
                    ApolloLink.from = function(links) {
                        if (links.length === 0)
                            return ApolloLink.empty();
                        return links.map(toLink).reduce(function(x, y) {
                            return x.concat(y);
                        });
                    };
                    ApolloLink.split = function(test, left, right) {
                        var leftLink = toLink(left);
                        var rightLink = toLink(right || new ApolloLink(passthrough));
                        if (isTerminating(leftLink) && isTerminating(rightLink)) {
                            return new ApolloLink(function(operation) {
                                return test(operation) ?
                                    leftLink.request(operation) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of() :
                                    rightLink.request(operation) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of();
                            });
                        } else {
                            return new ApolloLink(function(operation, forward) {
                                return test(operation) ?
                                    leftLink.request(operation, forward) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of() :
                                    rightLink.request(operation, forward) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of();
                            });
                        }
                    };
                    ApolloLink.execute = function(link, operation) {
                        return (link.request((0, _utils_index_js__WEBPACK_IMPORTED_MODULE_3__.createOperation)(operation.context, (0, _utils_index_js__WEBPACK_IMPORTED_MODULE_4__.transformOperation)((0, _utils_index_js__WEBPACK_IMPORTED_MODULE_5__.validateOperation)(operation)))) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of());
                    };
                    ApolloLink.concat = function(first, second) {
                        var firstLink = toLink(first);
                        if (isTerminating(firstLink)) {
                            __DEV__ && _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant.warn(new LinkError("You are calling concat on a terminating link, which will have no effect", firstLink));
                            return firstLink;
                        }
                        var nextLink = toLink(second);
                        if (isTerminating(nextLink)) {
                            return new ApolloLink(function(operation) {
                                return firstLink.request(operation, function(op) {
                                    return nextLink.request(op) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of();
                                }) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of();
                            });
                        } else {
                            return new ApolloLink(function(operation, forward) {
                                return (firstLink.request(operation, function(op) {
                                    return nextLink.request(op, forward) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of();
                                }) || _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.Observable.of());
                            });
                        }
                    };
                    ApolloLink.prototype.split = function(test, left, right) {
                        return this.concat(ApolloLink.split(test, left, right || new ApolloLink(passthrough)));
                    };
                    ApolloLink.prototype.concat = function(next) {
                        return ApolloLink.concat(this, next);
                    };
                    ApolloLink.prototype.request = function(operation, forward) {
                        throw __DEV__ ? new _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError('request is not implemented') : new _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError(22);
                    };
                    ApolloLink.prototype.onError = function(error, observer) {
                        if (observer && observer.error) {
                            observer.error(error);
                            return false;
                        }
                        throw error;
                    };
                    ApolloLink.prototype.setOnError = function(fn) {
                        this.onError = fn;
                        return this;
                    };
                    return ApolloLink;
                }());



                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/link/utils/createOperation.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    createOperation: () => ( /* binding */ createOperation)
                    /* harmony export */
                });
                /* harmony import */
                var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/tslib/tslib.es6.js");

                function createOperation(starting, operation) {
                    var context = (0, tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, starting);
                    var setContext = function(next) {
                        if (typeof next === 'function') {
                            context = (0, tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0, tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, context), next(context));
                        } else {
                            context = (0, tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0, tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, context), next);
                        }
                    };
                    var getContext = function() {
                        return ((0, tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, context));
                    };
                    Object.defineProperty(operation, 'setContext', {
                        enumerable: false,
                        value: setContext,
                    });
                    Object.defineProperty(operation, 'getContext', {
                        enumerable: false,
                        value: getContext,
                    });
                    return operation;
                }


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/link/utils/transformOperation.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    transformOperation: () => ( /* binding */ transformOperation)
                    /* harmony export */
                });
                /* harmony import */
                var _utilities_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/utilities/graphql/getFromAST.js");

                function transformOperation(operation) {
                    var transformedOperation = {
                        variables: operation.variables || {},
                        extensions: operation.extensions || {},
                        operationName: operation.operationName,
                        query: operation.query,
                    };
                    if (!transformedOperation.operationName) {
                        transformedOperation.operationName =
                            typeof transformedOperation.query !== 'string' ?
                            (0, _utilities_index_js__WEBPACK_IMPORTED_MODULE_0__.getOperationName)(transformedOperation.query) || undefined :
                            '';
                    }
                    return transformedOperation;
                }


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/link/utils/validateOperation.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    validateOperation: () => ( /* binding */ validateOperation)
                    /* harmony export */
                });
                /* harmony import */
                var _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/index.js");

                function validateOperation(operation) {
                    var OPERATION_FIELDS = [
                        'query',
                        'operationName',
                        'variables',
                        'extensions',
                        'context',
                    ];
                    for (var _i = 0, _a = Object.keys(operation); _i < _a.length; _i++) {
                        var key = _a[_i];
                        if (OPERATION_FIELDS.indexOf(key) < 0) {
                            throw __DEV__ ? new _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError("illegal argument: ".concat(key)) : new _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError(27);
                        }
                    }
                    return operation;
                }


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/common/objects.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    isNonNullObject: () => ( /* binding */ isNonNullObject)
                    /* harmony export */
                });

                function isNonNullObject(obj) {
                    return obj !== null && typeof obj === 'object';
                }


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/globals/DEV.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _global_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/global.js");
                /* harmony import */
                var _maybe_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/maybe.js");


                var __ = "__";
                var GLOBAL_KEY = [__, __].join("DEV");

                function getDEV() {
                    try {
                        return Boolean(__DEV__);
                    } catch (_a) {
                        Object.defineProperty(_global_js__WEBPACK_IMPORTED_MODULE_0__["default"], GLOBAL_KEY, {
                            value: (0, _maybe_js__WEBPACK_IMPORTED_MODULE_1__.maybe)(function() {
                                return "development";
                            }) !== "production",
                            enumerable: false,
                            configurable: true,
                            writable: true,
                        });
                        return _global_js__WEBPACK_IMPORTED_MODULE_0__["default"][GLOBAL_KEY];
                    }
                }
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (getDEV());


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/globals/fix-graphql.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    removeTemporaryGlobals: () => ( /* binding */ removeTemporaryGlobals)
                    /* harmony export */
                });
                /* harmony import */
                var ts_invariant_process_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/ts-invariant/process/index.js");
                /* harmony import */
                var graphql__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/graphql/language/source.mjs");


                function removeTemporaryGlobals() {
                    return typeof graphql__WEBPACK_IMPORTED_MODULE_1__.Source === "function" ? (0, ts_invariant_process_index_js__WEBPACK_IMPORTED_MODULE_0__.remove)() : (0, ts_invariant_process_index_js__WEBPACK_IMPORTED_MODULE_0__.remove)();
                }


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/globals/global.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _maybe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/maybe.js");

                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = ((0, _maybe_js__WEBPACK_IMPORTED_MODULE_0__.maybe)(function() {
                        return globalThis;
                    }) ||
                    (0, _maybe_js__WEBPACK_IMPORTED_MODULE_0__.maybe)(function() {
                        return window;
                    }) ||
                    (0, _maybe_js__WEBPACK_IMPORTED_MODULE_0__.maybe)(function() {
                        return self;
                    }) ||
                    (0, _maybe_js__WEBPACK_IMPORTED_MODULE_0__.maybe)(function() {
                        return __webpack_require__.g;
                    }) || (0, _maybe_js__WEBPACK_IMPORTED_MODULE_0__.maybe)(function() {
                        return _maybe_js__WEBPACK_IMPORTED_MODULE_0__.maybe.constructor("return this")();
                    }));


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/globals/index.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    DEV: () => ( /* reexport safe */ _DEV_js__WEBPACK_IMPORTED_MODULE_1__["default"]),
                    /* harmony export */
                    InvariantError: () => ( /* reexport safe */ ts_invariant__WEBPACK_IMPORTED_MODULE_0__.InvariantError),
                    /* harmony export */
                    checkDEV: () => ( /* binding */ checkDEV),
                    /* harmony export */
                    global: () => ( /* reexport safe */ _global_js__WEBPACK_IMPORTED_MODULE_4__["default"]),
                    /* harmony export */
                    invariant: () => ( /* reexport safe */ ts_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant),
                    /* harmony export */
                    maybe: () => ( /* reexport safe */ _maybe_js__WEBPACK_IMPORTED_MODULE_3__.maybe)
                    /* harmony export */
                });
                /* harmony import */
                var ts_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/ts-invariant/lib/invariant.js");
                /* harmony import */
                var _DEV_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/DEV.js");
                /* harmony import */
                var _fix_graphql_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/fix-graphql.js");
                /* harmony import */
                var _maybe_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/maybe.js");
                /* harmony import */
                var _global_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/global.js");



                function checkDEV() {
                    __DEV__ ? (0, ts_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)("boolean" === typeof _DEV_js__WEBPACK_IMPORTED_MODULE_1__["default"], _DEV_js__WEBPACK_IMPORTED_MODULE_1__["default"]) : (0, ts_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)("boolean" === typeof _DEV_js__WEBPACK_IMPORTED_MODULE_1__["default"], 39);
                }

                (0, _fix_graphql_js__WEBPACK_IMPORTED_MODULE_2__.removeTemporaryGlobals)();



                checkDEV();


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/globals/maybe.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    maybe: () => ( /* binding */ maybe)
                    /* harmony export */
                });

                function maybe(thunk) {
                    try {
                        return thunk();
                    } catch (_a) {}
                }


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/graphql/fragments.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    createFragmentMap: () => ( /* binding */ createFragmentMap),
                    /* harmony export */
                    getFragmentFromSelection: () => ( /* binding */ getFragmentFromSelection),
                    /* harmony export */
                    getFragmentQueryDocument: () => ( /* binding */ getFragmentQueryDocument)
                    /* harmony export */
                });
                /* harmony import */
                var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/tslib/tslib.es6.js");
                /* harmony import */
                var _globals_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/index.js");


                function getFragmentQueryDocument(document, fragmentName) {
                    var actualFragmentName = fragmentName;
                    var fragments = [];
                    document.definitions.forEach(function(definition) {
                        if (definition.kind === 'OperationDefinition') {
                            throw __DEV__ ? new _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError("Found a ".concat(definition.operation, " operation").concat(definition.name ? " named '".concat(definition.name.value, "'") : '', ". ") +
                                'No operations are allowed when using a fragment as a query. Only fragments are allowed.') : new _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError(44);
                        }
                        if (definition.kind === 'FragmentDefinition') {
                            fragments.push(definition);
                        }
                    });
                    if (typeof actualFragmentName === 'undefined') {
                        __DEV__ ? (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(fragments.length === 1, "Found ".concat(fragments.length, " fragments. `fragmentName` must be provided when there is not exactly 1 fragment.")) : (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(fragments.length === 1, 45);
                        actualFragmentName = fragments[0].name.value;
                    }
                    var query = (0, tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0, tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, document), {
                        definitions: (0, tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([{
                            kind: 'OperationDefinition',
                            operation: 'query',
                            selectionSet: {
                                kind: 'SelectionSet',
                                selections: [{
                                    kind: 'FragmentSpread',
                                    name: {
                                        kind: 'Name',
                                        value: actualFragmentName,
                                    },
                                }, ],
                            },
                        }], document.definitions, true)
                    });
                    return query;
                }

                function createFragmentMap(fragments) {
                    if (fragments === void 0) {
                        fragments = [];
                    }
                    var symTable = {};
                    fragments.forEach(function(fragment) {
                        symTable[fragment.name.value] = fragment;
                    });
                    return symTable;
                }

                function getFragmentFromSelection(selection, fragmentMap) {
                    switch (selection.kind) {
                        case 'InlineFragment':
                            return selection;
                        case 'FragmentSpread':
                            {
                                var fragmentName = selection.name.value;
                                if (typeof fragmentMap === "function") {
                                    return fragmentMap(fragmentName);
                                }
                                var fragment = fragmentMap && fragmentMap[fragmentName];
                                __DEV__ ? (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(fragment, "No fragment named ".concat(fragmentName)) : (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(fragment, 46);
                                return fragment || null;
                            }
                        default:
                            return null;
                    }
                }


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/graphql/getFromAST.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    checkDocument: () => ( /* binding */ checkDocument),
                    /* harmony export */
                    getDefaultValues: () => ( /* binding */ getDefaultValues),
                    /* harmony export */
                    getFragmentDefinition: () => ( /* binding */ getFragmentDefinition),
                    /* harmony export */
                    getFragmentDefinitions: () => ( /* binding */ getFragmentDefinitions),
                    /* harmony export */
                    getMainDefinition: () => ( /* binding */ getMainDefinition),
                    /* harmony export */
                    getOperationDefinition: () => ( /* binding */ getOperationDefinition),
                    /* harmony export */
                    getOperationName: () => ( /* binding */ getOperationName),
                    /* harmony export */
                    getQueryDefinition: () => ( /* binding */ getQueryDefinition)
                    /* harmony export */
                });
                /* harmony import */
                var _globals_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/index.js");
                /* harmony import */
                var _storeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@apollo/client/utilities/graphql/storeUtils.js");


                function checkDocument(doc) {
                    __DEV__ ? (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(doc && doc.kind === 'Document', "Expecting a parsed GraphQL document. Perhaps you need to wrap the query string in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql") : (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(doc && doc.kind === 'Document', 47);
                    var operations = doc.definitions
                        .filter(function(d) {
                            return d.kind !== 'FragmentDefinition';
                        })
                        .map(function(definition) {
                            if (definition.kind !== 'OperationDefinition') {
                                throw __DEV__ ? new _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError("Schema type definitions not allowed in queries. Found: \"".concat(definition.kind, "\"")) : new _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError(48);
                            }
                            return definition;
                        });
                    __DEV__ ? (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(operations.length <= 1, "Ambiguous GraphQL document: contains ".concat(operations.length, " operations")) : (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(operations.length <= 1, 49);
                    return doc;
                }

                function getOperationDefinition(doc) {
                    checkDocument(doc);
                    return doc.definitions.filter(function(definition) {
                        return definition.kind === 'OperationDefinition';
                    })[0];
                }

                function getOperationName(doc) {
                    return (doc.definitions
                        .filter(function(definition) {
                            return definition.kind === 'OperationDefinition' && !!definition.name;
                        })
                        .map(function(x) {
                            return x.name.value;
                        })[0] || null);
                }

                function getFragmentDefinitions(doc) {
                    return doc.definitions.filter(function(definition) {
                        return definition.kind === 'FragmentDefinition';
                    });
                }

                function getQueryDefinition(doc) {
                    var queryDef = getOperationDefinition(doc);
                    __DEV__ ? (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(queryDef && queryDef.operation === 'query', 'Must contain a query definition.') : (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(queryDef && queryDef.operation === 'query', 50);
                    return queryDef;
                }

                function getFragmentDefinition(doc) {
                    __DEV__ ? (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(doc.kind === 'Document', "Expecting a parsed GraphQL document. Perhaps you need to wrap the query string in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql") : (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(doc.kind === 'Document', 51);
                    __DEV__ ? (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(doc.definitions.length <= 1, 'Fragment must have exactly one definition.') : (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(doc.definitions.length <= 1, 52);
                    var fragmentDef = doc.definitions[0];
                    __DEV__ ? (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(fragmentDef.kind === 'FragmentDefinition', 'Must be a fragment definition.') : (0, _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.invariant)(fragmentDef.kind === 'FragmentDefinition', 53);
                    return fragmentDef;
                }

                function getMainDefinition(queryDoc) {
                    checkDocument(queryDoc);
                    var fragmentDefinition;
                    for (var _i = 0, _a = queryDoc.definitions; _i < _a.length; _i++) {
                        var definition = _a[_i];
                        if (definition.kind === 'OperationDefinition') {
                            var operation = definition.operation;
                            if (operation === 'query' ||
                                operation === 'mutation' ||
                                operation === 'subscription') {
                                return definition;
                            }
                        }
                        if (definition.kind === 'FragmentDefinition' && !fragmentDefinition) {
                            fragmentDefinition = definition;
                        }
                    }
                    if (fragmentDefinition) {
                        return fragmentDefinition;
                    }
                    throw __DEV__ ? new _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError('Expected a parsed GraphQL query with a query, mutation, subscription, or a fragment.') : new _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError(54);
                }

                function getDefaultValues(definition) {
                    var defaultValues = Object.create(null);
                    var defs = definition && definition.variableDefinitions;
                    if (defs && defs.length) {
                        defs.forEach(function(def) {
                            if (def.defaultValue) {
                                (0, _storeUtils_js__WEBPACK_IMPORTED_MODULE_1__.valueToObjectRepresentation)(defaultValues, def.variable.name, def.defaultValue);
                            }
                        });
                    }
                    return defaultValues;
                }


                /***/
            }),

        /***/
        "../../node_modules/@apollo/client/utilities/graphql/storeUtils.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    argumentsObjectFromField: () => ( /* binding */ argumentsObjectFromField),
                    /* harmony export */
                    getStoreKeyName: () => ( /* binding */ getStoreKeyName),
                    /* harmony export */
                    getTypenameFromResult: () => ( /* binding */ getTypenameFromResult),
                    /* harmony export */
                    isDocumentNode: () => ( /* binding */ isDocumentNode),
                    /* harmony export */
                    isField: () => ( /* binding */ isField),
                    /* harmony export */
                    isInlineFragment: () => ( /* binding */ isInlineFragment),
                    /* harmony export */
                    isReference: () => ( /* binding */ isReference),
                    /* harmony export */
                    makeReference: () => ( /* binding */ makeReference),
                    /* harmony export */
                    resultKeyNameFromField: () => ( /* binding */ resultKeyNameFromField),
                    /* harmony export */
                    storeKeyNameFromField: () => ( /* binding */ storeKeyNameFromField),
                    /* harmony export */
                    valueToObjectRepresentation: () => ( /* binding */ valueToObjectRepresentation)
                    /* harmony export */
                });
                /* harmony import */
                var _globals_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/utilities/globals/index.js");
                /* harmony import */
                var _common_objects_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@apollo/client/utilities/common/objects.js");
                /* harmony import */
                var _fragments_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@apollo/client/utilities/graphql/fragments.js");



                function makeReference(id) {
                    return {
                        __ref: String(id)
                    };
                }

                function isReference(obj) {
                    return Boolean(obj && typeof obj === 'object' && typeof obj.__ref === 'string');
                }

                function isDocumentNode(value) {
                    return ((0, _common_objects_js__WEBPACK_IMPORTED_MODULE_1__.isNonNullObject)(value) &&
                        value.kind === "Document" &&
                        Array.isArray(value.definitions));
                }

                function isStringValue(value) {
                    return value.kind === 'StringValue';
                }

                function isBooleanValue(value) {
                    return value.kind === 'BooleanValue';
                }

                function isIntValue(value) {
                    return value.kind === 'IntValue';
                }

                function isFloatValue(value) {
                    return value.kind === 'FloatValue';
                }

                function isVariable(value) {
                    return value.kind === 'Variable';
                }

                function isObjectValue(value) {
                    return value.kind === 'ObjectValue';
                }

                function isListValue(value) {
                    return value.kind === 'ListValue';
                }

                function isEnumValue(value) {
                    return value.kind === 'EnumValue';
                }

                function isNullValue(value) {
                    return value.kind === 'NullValue';
                }

                function valueToObjectRepresentation(argObj, name, value, variables) {
                    if (isIntValue(value) || isFloatValue(value)) {
                        argObj[name.value] = Number(value.value);
                    } else if (isBooleanValue(value) || isStringValue(value)) {
                        argObj[name.value] = value.value;
                    } else if (isObjectValue(value)) {
                        var nestedArgObj_1 = {};
                        value.fields.map(function(obj) {
                            return valueToObjectRepresentation(nestedArgObj_1, obj.name, obj.value, variables);
                        });
                        argObj[name.value] = nestedArgObj_1;
                    } else if (isVariable(value)) {
                        var variableValue = (variables || {})[value.name.value];
                        argObj[name.value] = variableValue;
                    } else if (isListValue(value)) {
                        argObj[name.value] = value.values.map(function(listValue) {
                            var nestedArgArrayObj = {};
                            valueToObjectRepresentation(nestedArgArrayObj, name, listValue, variables);
                            return nestedArgArrayObj[name.value];
                        });
                    } else if (isEnumValue(value)) {
                        argObj[name.value] = value.value;
                    } else if (isNullValue(value)) {
                        argObj[name.value] = null;
                    } else {
                        throw __DEV__ ? new _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError("The inline argument \"".concat(name.value, "\" of kind \"").concat(value.kind, "\"") +
                            'is not supported. Use variables instead of inline arguments to ' +
                            'overcome this limitation.') : new _globals_index_js__WEBPACK_IMPORTED_MODULE_0__.InvariantError(55);
                    }
                }

                function storeKeyNameFromField(field, variables) {
                    var directivesObj = null;
                    if (field.directives) {
                        directivesObj = {};
                        field.directives.forEach(function(directive) {
                            directivesObj[directive.name.value] = {};
                            if (directive.arguments) {
                                directive.arguments.forEach(function(_a) {
                                    var name = _a.name,
                                        value = _a.value;
                                    return valueToObjectRepresentation(directivesObj[directive.name.value], name, value, variables);
                                });
                            }
                        });
                    }
                    var argObj = null;
                    if (field.arguments && field.arguments.length) {
                        argObj = {};
                        field.arguments.forEach(function(_a) {
                            var name = _a.name,
                                value = _a.value;
                            return valueToObjectRepresentation(argObj, name, value, variables);
                        });
                    }
                    return getStoreKeyName(field.name.value, argObj, directivesObj);
                }
                var KNOWN_DIRECTIVES = [
                    'connection',
                    'include',
                    'skip',
                    'client',
                    'rest',
                    'export',
                ];
                var getStoreKeyName = Object.assign(function(fieldName, args, directives) {
                    if (args &&
                        directives &&
                        directives['connection'] &&
                        directives['connection']['key']) {
                        if (directives['connection']['filter'] &&
                            directives['connection']['filter'].length > 0) {
                            var filterKeys = directives['connection']['filter'] ?
                                directives['connection']['filter'] :
                                [];
                            filterKeys.sort();
                            var filteredArgs_1 = {};
                            filterKeys.forEach(function(key) {
                                filteredArgs_1[key] = args[key];
                            });
                            return "".concat(directives['connection']['key'], "(").concat(stringify(filteredArgs_1), ")");
                        } else {
                            return directives['connection']['key'];
                        }
                    }
                    var completeFieldName = fieldName;
                    if (args) {
                        var stringifiedArgs = stringify(args);
                        completeFieldName += "(".concat(stringifiedArgs, ")");
                    }
                    if (directives) {
                        Object.keys(directives).forEach(function(key) {
                            if (KNOWN_DIRECTIVES.indexOf(key) !== -1)
                                return;
                            if (directives[key] && Object.keys(directives[key]).length) {
                                completeFieldName += "@".concat(key, "(").concat(stringify(directives[key]), ")");
                            } else {
                                completeFieldName += "@".concat(key);
                            }
                        });
                    }
                    return completeFieldName;
                }, {
                    setStringify: function(s) {
                        var previous = stringify;
                        stringify = s;
                        return previous;
                    },
                });
                var stringify = function defaultStringify(value) {
                    return JSON.stringify(value, stringifyReplacer);
                };

                function stringifyReplacer(_key, value) {
                    if ((0, _common_objects_js__WEBPACK_IMPORTED_MODULE_1__.isNonNullObject)(value) && !Array.isArray(value)) {
                        value = Object.keys(value).sort().reduce(function(copy, key) {
                            copy[key] = value[key];
                            return copy;
                        }, {});
                    }
                    return value;
                }

                function argumentsObjectFromField(field, variables) {
                    if (field.arguments && field.arguments.length) {
                        var argObj_1 = {};
                        field.arguments.forEach(function(_a) {
                            var name = _a.name,
                                value = _a.value;
                            return valueToObjectRepresentation(argObj_1, name, value, variables);
                        });
                        return argObj_1;
                    }
                    return null;
                }

                function resultKeyNameFromField(field) {
                    return field.alias ? field.alias.value : field.name.value;
                }

                function getTypenameFromResult(result, selectionSet, fragmentMap) {
                    if (typeof result.__typename === 'string') {
                        return result.__typename;
                    }
                    for (var _i = 0, _a = selectionSet.selections; _i < _a.length; _i++) {
                        var selection = _a[_i];
                        if (isField(selection)) {
                            if (selection.name.value === '__typename') {
                                return result[resultKeyNameFromField(selection)];
                            }
                        } else {
                            var typename = getTypenameFromResult(result, (0, _fragments_js__WEBPACK_IMPORTED_MODULE_2__.getFragmentFromSelection)(selection, fragmentMap).selectionSet, fragmentMap);
                            if (typeof typename === 'string') {
                                return typename;
                            }
                        }
                    }
                }

                function isField(selection) {
                    return selection.kind === 'Field';
                }

                function isInlineFragment(selection) {
                    return selection.kind === 'InlineFragment';
                }


                /***/
            }),

        /***/
        "../../node_modules/ts-invariant/lib/invariant.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    InvariantError: () => ( /* binding */ InvariantError),
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    invariant: () => ( /* binding */ invariant),
                    /* harmony export */
                    setVerbosity: () => ( /* binding */ setVerbosity)
                    /* harmony export */
                });
                /* harmony import */
                var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/tslib/tslib.es6.js");

                var genericMessage = "Invariant Violation";
                var _a = Object.setPrototypeOf,
                    setPrototypeOf = _a === void 0 ? function(obj, proto) {
                        obj.__proto__ = proto;
                        return obj;
                    } : _a;
                var InvariantError = /** @class */ (function(_super) {
                    (0, tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvariantError, _super);

                    function InvariantError(message) {
                        if (message === void 0) {
                            message = genericMessage;
                        }
                        var _this = _super.call(this, typeof message === "number" ?
                            genericMessage + ": " + message + " (see https://github.com/apollographql/invariant-packages)" :
                            message) || this;
                        _this.framesToPop = 1;
                        _this.name = genericMessage;
                        setPrototypeOf(_this, InvariantError.prototype);
                        return _this;
                    }
                    return InvariantError;
                }(Error));

                function invariant(condition, message) {
                    if (!condition) {
                        throw new InvariantError(message);
                    }
                }
                var verbosityLevels = ["debug", "log", "warn", "error", "silent"];
                var verbosityLevel = verbosityLevels.indexOf("log");

                function wrapConsoleMethod(name) {
                    return function() {
                        if (verbosityLevels.indexOf(name) >= verbosityLevel) {
                            // Default to console.log if this host environment happens not to provide
                            // all the console.* methods we need.
                            var method = console[name] || console.log;
                            return method.apply(console, arguments);
                        }
                    };
                }
                (function(invariant) {
                    invariant.debug = wrapConsoleMethod("debug");
                    invariant.log = wrapConsoleMethod("log");
                    invariant.warn = wrapConsoleMethod("warn");
                    invariant.error = wrapConsoleMethod("error");
                })(invariant || (invariant = {}));

                function setVerbosity(level) {
                    var old = verbosityLevels[verbosityLevel];
                    verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));
                    return old;
                }
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (invariant);


                /***/
            }),

        /***/
        "../../node_modules/ts-invariant/process/index.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    install: () => ( /* binding */ install),
                    /* harmony export */
                    remove: () => ( /* binding */ remove)
                    /* harmony export */
                });

                function maybe(thunk) {
                    try {
                        return thunk()
                    } catch (_) {}
                }

                var safeGlobal = (
                    maybe(function() {
                        return globalThis
                    }) ||
                    maybe(function() {
                        return window
                    }) ||
                    maybe(function() {
                        return self
                    }) ||
                    maybe(function() {
                        return __webpack_require__.g
                    }) ||
                    // We don't expect the Function constructor ever to be invoked at runtime, as
                    // long as at least one of globalThis, window, self, or global is defined, so
                    // we are under no obligation to make it easy for static analysis tools to
                    // detect syntactic usage of the Function constructor. If you think you can
                    // improve your static analysis to detect this obfuscation, think again. This
                    // is an arms race you cannot win, at least not in JavaScript.
                    maybe(function() {
                        return maybe.constructor("return this")()
                    })
                );

                var needToRemove = false;

                function install() {
                    if (safeGlobal &&
                        !maybe(function() {
                            return "development"
                        }) &&
                        !maybe(function() {
                            return process
                        })) {
                        Object.defineProperty(safeGlobal, "process", {
                            value: {
                                env: {
                                    // This default needs to be "production" instead of "development", to
                                    // avoid the problem https://github.com/graphql/graphql-js/pull/2894
                                    // will eventually solve, once merged and released.
                                    NODE_ENV: "production",
                                },
                            },
                            // Let anyone else change global.process as they see fit, but hide it from
                            // Object.keys(global) enumeration.
                            configurable: true,
                            enumerable: false,
                            writable: true,
                        });
                        needToRemove = true;
                    }
                }

                // Call install() at least once, when this module is imported.
                install();

                function remove() {
                    if (needToRemove) {
                        delete safeGlobal.process;
                        needToRemove = false;
                    }
                }


                /***/
            }),

        /***/
        "../../node_modules/zen-observable-ts/module.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    Observable: () => ( /* binding */ Observable)
                    /* harmony export */
                });

                function _createForOfIteratorHelperLoose(o, allowArrayLike) {
                    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
                    if (it) return (it = it.call(o)).next.bind(it);
                    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
                        if (it) o = it;
                        var i = 0;
                        return function() {
                            if (i >= o.length) return {
                                done: true
                            };
                            return {
                                done: false,
                                value: o[i++]
                            };
                        };
                    }
                    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
                }

                function _unsupportedIterableToArray(o, minLen) {
                    if (!o) return;
                    if (typeof o === "string") return _arrayLikeToArray(o, minLen);
                    var n = Object.prototype.toString.call(o).slice(8, -1);
                    if (n === "Object" && o.constructor) n = o.constructor.name;
                    if (n === "Map" || n === "Set") return Array.from(o);
                    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
                }

                function _arrayLikeToArray(arr, len) {
                    if (len == null || len > arr.length) len = arr.length;
                    for (var i = 0, arr2 = new Array(len); i < len; i++) {
                        arr2[i] = arr[i];
                    }
                    return arr2;
                }

                function _defineProperties(target, props) {
                    for (var i = 0; i < props.length; i++) {
                        var descriptor = props[i];
                        descriptor.enumerable = descriptor.enumerable || false;
                        descriptor.configurable = true;
                        if ("value" in descriptor) descriptor.writable = true;
                        Object.defineProperty(target, descriptor.key, descriptor);
                    }
                }

                function _createClass(Constructor, protoProps, staticProps) {
                    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
                    if (staticProps) _defineProperties(Constructor, staticProps);
                    Object.defineProperty(Constructor, "prototype", {
                        writable: false
                    });
                    return Constructor;
                }

                // === Symbol Support ===
                var hasSymbols = function() {
                    return typeof Symbol === 'function';
                };

                var hasSymbol = function(name) {
                    return hasSymbols() && Boolean(Symbol[name]);
                };

                var getSymbol = function(name) {
                    return hasSymbol(name) ? Symbol[name] : '@@' + name;
                };

                if (hasSymbols() && !hasSymbol('observable')) {
                    Symbol.observable = Symbol('observable');
                }

                var SymbolIterator = getSymbol('iterator');
                var SymbolObservable = getSymbol('observable');
                var SymbolSpecies = getSymbol('species'); // === Abstract Operations ===

                function getMethod(obj, key) {
                    var value = obj[key];
                    if (value == null) return undefined;
                    if (typeof value !== 'function') throw new TypeError(value + ' is not a function');
                    return value;
                }

                function getSpecies(obj) {
                    var ctor = obj.constructor;

                    if (ctor !== undefined) {
                        ctor = ctor[SymbolSpecies];

                        if (ctor === null) {
                            ctor = undefined;
                        }
                    }

                    return ctor !== undefined ? ctor : Observable;
                }

                function isObservable(x) {
                    return x instanceof Observable; // SPEC: Brand check
                }

                function hostReportError(e) {
                    if (hostReportError.log) {
                        hostReportError.log(e);
                    } else {
                        setTimeout(function() {
                            throw e;
                        });
                    }
                }

                function enqueue(fn) {
                    Promise.resolve().then(function() {
                        try {
                            fn();
                        } catch (e) {
                            hostReportError(e);
                        }
                    });
                }

                function cleanupSubscription(subscription) {
                    var cleanup = subscription._cleanup;
                    if (cleanup === undefined) return;
                    subscription._cleanup = undefined;

                    if (!cleanup) {
                        return;
                    }

                    try {
                        if (typeof cleanup === 'function') {
                            cleanup();
                        } else {
                            var unsubscribe = getMethod(cleanup, 'unsubscribe');

                            if (unsubscribe) {
                                unsubscribe.call(cleanup);
                            }
                        }
                    } catch (e) {
                        hostReportError(e);
                    }
                }

                function closeSubscription(subscription) {
                    subscription._observer = undefined;
                    subscription._queue = undefined;
                    subscription._state = 'closed';
                }

                function flushSubscription(subscription) {
                    var queue = subscription._queue;

                    if (!queue) {
                        return;
                    }

                    subscription._queue = undefined;
                    subscription._state = 'ready';

                    for (var i = 0; i < queue.length; ++i) {
                        notifySubscription(subscription, queue[i].type, queue[i].value);
                        if (subscription._state === 'closed') break;
                    }
                }

                function notifySubscription(subscription, type, value) {
                    subscription._state = 'running';
                    var observer = subscription._observer;

                    try {
                        var m = getMethod(observer, type);

                        switch (type) {
                            case 'next':
                                if (m) m.call(observer, value);
                                break;

                            case 'error':
                                closeSubscription(subscription);
                                if (m) m.call(observer, value);
                                else throw value;
                                break;

                            case 'complete':
                                closeSubscription(subscription);
                                if (m) m.call(observer);
                                break;
                        }
                    } catch (e) {
                        hostReportError(e);
                    }

                    if (subscription._state === 'closed') cleanupSubscription(subscription);
                    else if (subscription._state === 'running') subscription._state = 'ready';
                }

                function onNotify(subscription, type, value) {
                    if (subscription._state === 'closed') return;

                    if (subscription._state === 'buffering') {
                        subscription._queue.push({
                            type: type,
                            value: value
                        });

                        return;
                    }

                    if (subscription._state !== 'ready') {
                        subscription._state = 'buffering';
                        subscription._queue = [{
                            type: type,
                            value: value
                        }];
                        enqueue(function() {
                            return flushSubscription(subscription);
                        });
                        return;
                    }

                    notifySubscription(subscription, type, value);
                }

                var Subscription = /*#__PURE__*/ function() {
                    function Subscription(observer, subscriber) {
                        // ASSERT: observer is an object
                        // ASSERT: subscriber is callable
                        this._cleanup = undefined;
                        this._observer = observer;
                        this._queue = undefined;
                        this._state = 'initializing';
                        var subscriptionObserver = new SubscriptionObserver(this);

                        try {
                            this._cleanup = subscriber.call(undefined, subscriptionObserver);
                        } catch (e) {
                            subscriptionObserver.error(e);
                        }

                        if (this._state === 'initializing') this._state = 'ready';
                    }

                    var _proto = Subscription.prototype;

                    _proto.unsubscribe = function unsubscribe() {
                        if (this._state !== 'closed') {
                            closeSubscription(this);
                            cleanupSubscription(this);
                        }
                    };

                    _createClass(Subscription, [{
                        key: "closed",
                        get: function() {
                            return this._state === 'closed';
                        }
                    }]);

                    return Subscription;
                }();

                var SubscriptionObserver = /*#__PURE__*/ function() {
                    function SubscriptionObserver(subscription) {
                        this._subscription = subscription;
                    }

                    var _proto2 = SubscriptionObserver.prototype;

                    _proto2.next = function next(value) {
                        onNotify(this._subscription, 'next', value);
                    };

                    _proto2.error = function error(value) {
                        onNotify(this._subscription, 'error', value);
                    };

                    _proto2.complete = function complete() {
                        onNotify(this._subscription, 'complete');
                    };

                    _createClass(SubscriptionObserver, [{
                        key: "closed",
                        get: function() {
                            return this._subscription._state === 'closed';
                        }
                    }]);

                    return SubscriptionObserver;
                }();

                var Observable = /*#__PURE__*/ function() {
                    function Observable(subscriber) {
                        if (!(this instanceof Observable)) throw new TypeError('Observable cannot be called as a function');
                        if (typeof subscriber !== 'function') throw new TypeError('Observable initializer must be a function');
                        this._subscriber = subscriber;
                    }

                    var _proto3 = Observable.prototype;

                    _proto3.subscribe = function subscribe(observer) {
                        if (typeof observer !== 'object' || observer === null) {
                            observer = {
                                next: observer,
                                error: arguments[1],
                                complete: arguments[2]
                            };
                        }

                        return new Subscription(observer, this._subscriber);
                    };

                    _proto3.forEach = function forEach(fn) {
                        var _this = this;

                        return new Promise(function(resolve, reject) {
                            if (typeof fn !== 'function') {
                                reject(new TypeError(fn + ' is not a function'));
                                return;
                            }

                            function done() {
                                subscription.unsubscribe();
                                resolve();
                            }

                            var subscription = _this.subscribe({
                                next: function(value) {
                                    try {
                                        fn(value, done);
                                    } catch (e) {
                                        reject(e);
                                        subscription.unsubscribe();
                                    }
                                },
                                error: reject,
                                complete: resolve
                            });
                        });
                    };

                    _proto3.map = function map(fn) {
                        var _this2 = this;

                        if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');
                        var C = getSpecies(this);
                        return new C(function(observer) {
                            return _this2.subscribe({
                                next: function(value) {
                                    try {
                                        value = fn(value);
                                    } catch (e) {
                                        return observer.error(e);
                                    }

                                    observer.next(value);
                                },
                                error: function(e) {
                                    observer.error(e);
                                },
                                complete: function() {
                                    observer.complete();
                                }
                            });
                        });
                    };

                    _proto3.filter = function filter(fn) {
                        var _this3 = this;

                        if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');
                        var C = getSpecies(this);
                        return new C(function(observer) {
                            return _this3.subscribe({
                                next: function(value) {
                                    try {
                                        if (!fn(value)) return;
                                    } catch (e) {
                                        return observer.error(e);
                                    }

                                    observer.next(value);
                                },
                                error: function(e) {
                                    observer.error(e);
                                },
                                complete: function() {
                                    observer.complete();
                                }
                            });
                        });
                    };

                    _proto3.reduce = function reduce(fn) {
                        var _this4 = this;

                        if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');
                        var C = getSpecies(this);
                        var hasSeed = arguments.length > 1;
                        var hasValue = false;
                        var seed = arguments[1];
                        var acc = seed;
                        return new C(function(observer) {
                            return _this4.subscribe({
                                next: function(value) {
                                    var first = !hasValue;
                                    hasValue = true;

                                    if (!first || hasSeed) {
                                        try {
                                            acc = fn(acc, value);
                                        } catch (e) {
                                            return observer.error(e);
                                        }
                                    } else {
                                        acc = value;
                                    }
                                },
                                error: function(e) {
                                    observer.error(e);
                                },
                                complete: function() {
                                    if (!hasValue && !hasSeed) return observer.error(new TypeError('Cannot reduce an empty sequence'));
                                    observer.next(acc);
                                    observer.complete();
                                }
                            });
                        });
                    };

                    _proto3.concat = function concat() {
                        var _this5 = this;

                        for (var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++) {
                            sources[_key] = arguments[_key];
                        }

                        var C = getSpecies(this);
                        return new C(function(observer) {
                            var subscription;
                            var index = 0;

                            function startNext(next) {
                                subscription = next.subscribe({
                                    next: function(v) {
                                        observer.next(v);
                                    },
                                    error: function(e) {
                                        observer.error(e);
                                    },
                                    complete: function() {
                                        if (index === sources.length) {
                                            subscription = undefined;
                                            observer.complete();
                                        } else {
                                            startNext(C.from(sources[index++]));
                                        }
                                    }
                                });
                            }

                            startNext(_this5);
                            return function() {
                                if (subscription) {
                                    subscription.unsubscribe();
                                    subscription = undefined;
                                }
                            };
                        });
                    };

                    _proto3.flatMap = function flatMap(fn) {
                        var _this6 = this;

                        if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');
                        var C = getSpecies(this);
                        return new C(function(observer) {
                            var subscriptions = [];

                            var outer = _this6.subscribe({
                                next: function(value) {
                                    if (fn) {
                                        try {
                                            value = fn(value);
                                        } catch (e) {
                                            return observer.error(e);
                                        }
                                    }

                                    var inner = C.from(value).subscribe({
                                        next: function(value) {
                                            observer.next(value);
                                        },
                                        error: function(e) {
                                            observer.error(e);
                                        },
                                        complete: function() {
                                            var i = subscriptions.indexOf(inner);
                                            if (i >= 0) subscriptions.splice(i, 1);
                                            completeIfDone();
                                        }
                                    });
                                    subscriptions.push(inner);
                                },
                                error: function(e) {
                                    observer.error(e);
                                },
                                complete: function() {
                                    completeIfDone();
                                }
                            });

                            function completeIfDone() {
                                if (outer.closed && subscriptions.length === 0) observer.complete();
                            }

                            return function() {
                                subscriptions.forEach(function(s) {
                                    return s.unsubscribe();
                                });
                                outer.unsubscribe();
                            };
                        });
                    };

                    _proto3[SymbolObservable] = function() {
                        return this;
                    };

                    Observable.from = function from(x) {
                        var C = typeof this === 'function' ? this : Observable;
                        if (x == null) throw new TypeError(x + ' is not an object');
                        var method = getMethod(x, SymbolObservable);

                        if (method) {
                            var observable = method.call(x);
                            if (Object(observable) !== observable) throw new TypeError(observable + ' is not an object');
                            if (isObservable(observable) && observable.constructor === C) return observable;
                            return new C(function(observer) {
                                return observable.subscribe(observer);
                            });
                        }

                        if (hasSymbol('iterator')) {
                            method = getMethod(x, SymbolIterator);

                            if (method) {
                                return new C(function(observer) {
                                    enqueue(function() {
                                        if (observer.closed) return;

                                        for (var _iterator = _createForOfIteratorHelperLoose(method.call(x)), _step; !(_step = _iterator()).done;) {
                                            var item = _step.value;
                                            observer.next(item);
                                            if (observer.closed) return;
                                        }

                                        observer.complete();
                                    });
                                });
                            }
                        }

                        if (Array.isArray(x)) {
                            return new C(function(observer) {
                                enqueue(function() {
                                    if (observer.closed) return;

                                    for (var i = 0; i < x.length; ++i) {
                                        observer.next(x[i]);
                                        if (observer.closed) return;
                                    }

                                    observer.complete();
                                });
                            });
                        }

                        throw new TypeError(x + ' is not observable');
                    };

                    Observable.of = function of () {
                        for (var _len2 = arguments.length, items = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
                            items[_key2] = arguments[_key2];
                        }

                        var C = typeof this === 'function' ? this : Observable;
                        return new C(function(observer) {
                            enqueue(function() {
                                if (observer.closed) return;

                                for (var i = 0; i < items.length; ++i) {
                                    observer.next(items[i]);
                                    if (observer.closed) return;
                                }

                                observer.complete();
                            });
                        });
                    };

                    _createClass(Observable, null, [{
                        key: SymbolSpecies,
                        get: function() {
                            return this;
                        }
                    }]);

                    return Observable;
                }();

                if (hasSymbols()) {
                    Object.defineProperty(Observable, Symbol('extensions'), {
                        value: {
                            symbol: SymbolObservable,
                            hostReportError: hostReportError
                        },
                        configurable: true
                    });
                }




                /***/
            }),

        /***/
        "../../node_modules/graphql/jsutils/devAssert.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    devAssert: () => ( /* binding */ devAssert)
                    /* harmony export */
                });

                function devAssert(condition, message) {
                    const booleanCondition = Boolean(condition);

                    if (!booleanCondition) {
                        throw new Error(message);
                    }
                }


                /***/
            }),

        /***/
        "../../node_modules/graphql/jsutils/inspect.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    inspect: () => ( /* binding */ inspect)
                    /* harmony export */
                });
                const MAX_ARRAY_LENGTH = 10;
                const MAX_RECURSIVE_DEPTH = 2;
                /**
                 * Used to print values in error messages.
                 */

                function inspect(value) {
                    return formatValue(value, []);
                }

                function formatValue(value, seenValues) {
                    switch (typeof value) {
                        case 'string':
                            return JSON.stringify(value);

                        case 'function':
                            return value.name ? `[function ${value.name}]` : '[function]';

                        case 'object':
                            return formatObjectValue(value, seenValues);

                        default:
                            return String(value);
                    }
                }

                function formatObjectValue(value, previouslySeenValues) {
                    if (value === null) {
                        return 'null';
                    }

                    if (previouslySeenValues.includes(value)) {
                        return '[Circular]';
                    }

                    const seenValues = [...previouslySeenValues, value];

                    if (isJSONable(value)) {
                        const jsonValue = value.toJSON(); // check for infinite recursion

                        if (jsonValue !== value) {
                            return typeof jsonValue === 'string' ?
                                jsonValue :
                                formatValue(jsonValue, seenValues);
                        }
                    } else if (Array.isArray(value)) {
                        return formatArray(value, seenValues);
                    }

                    return formatObject(value, seenValues);
                }

                function isJSONable(value) {
                    return typeof value.toJSON === 'function';
                }

                function formatObject(object, seenValues) {
                    const entries = Object.entries(object);

                    if (entries.length === 0) {
                        return '{}';
                    }

                    if (seenValues.length > MAX_RECURSIVE_DEPTH) {
                        return '[' + getObjectTag(object) + ']';
                    }

                    const properties = entries.map(
                        ([key, value]) => key + ': ' + formatValue(value, seenValues),
                    );
                    return '{ ' + properties.join(', ') + ' }';
                }

                function formatArray(array, seenValues) {
                    if (array.length === 0) {
                        return '[]';
                    }

                    if (seenValues.length > MAX_RECURSIVE_DEPTH) {
                        return '[Array]';
                    }

                    const len = Math.min(MAX_ARRAY_LENGTH, array.length);
                    const remaining = array.length - len;
                    const items = [];

                    for (let i = 0; i < len; ++i) {
                        items.push(formatValue(array[i], seenValues));
                    }

                    if (remaining === 1) {
                        items.push('... 1 more item');
                    } else if (remaining > 1) {
                        items.push(`... ${remaining} more items`);
                    }

                    return '[' + items.join(', ') + ']';
                }

                function getObjectTag(object) {
                    const tag = Object.prototype.toString
                        .call(object)
                        .replace(/^\[object /, '')
                        .replace(/]$/, '');

                    if (tag === 'Object' && typeof object.constructor === 'function') {
                        const name = object.constructor.name;

                        if (typeof name === 'string' && name !== '') {
                            return name;
                        }
                    }

                    return tag;
                }


                /***/
            }),

        /***/
        "../../node_modules/graphql/jsutils/instanceOf.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    instanceOf: () => ( /* binding */ instanceOf)
                    /* harmony export */
                });
                /* harmony import */
                var _inspect_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/graphql/jsutils/inspect.mjs");

                /**
                 * A replacement for instanceof which includes an error warning when multi-realm
                 * constructors are detected.
                 * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production
                 * See: https://webpack.js.org/guides/production/
                 */

                const instanceOf =
                    /* c8 ignore next 6 */
                    // FIXME: https://github.com/graphql/graphql-js/issues/2317
                    // eslint-disable-next-line no-undef
                    false ?
                    0 :
                    function instanceOf(value, constructor) {
                        if (value instanceof constructor) {
                            return true;
                        }

                        if (typeof value === 'object' && value !== null) {
                            var _value$constructor;

                            // Prefer Symbol.toStringTag since it is immune to minification.
                            const className = constructor.prototype[Symbol.toStringTag];
                            const valueClassName = // We still need to support constructor's name to detect conflicts with older versions of this library.
                                Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009
                                ?
                                value[Symbol.toStringTag] :
                                (_value$constructor = value.constructor) === null ||
                                _value$constructor === void 0 ?
                                void 0 :
                                _value$constructor.name;

                            if (className === valueClassName) {
                                const stringifiedValue = (0, _inspect_mjs__WEBPACK_IMPORTED_MODULE_0__.inspect)(value);
                                throw new Error(`Cannot use ${className} "${stringifiedValue}" from another module or realm.

Ensure that there is only one instance of "graphql" in the node_modules
directory. If different versions of "graphql" are the dependencies of other
relied on modules, use "resolutions" to ensure only one version is installed.

https://yarnpkg.com/en/docs/selective-version-resolutions

Duplicate "graphql" modules cannot be used at the same time since different
versions may have different capabilities and behavior. The data from one
version used in the function from another could produce confusing and
spurious results.`);
                            }
                        }

                        return false;
                    };


                /***/
            }),

        /***/
        "../../node_modules/graphql/language/source.mjs":
            /***/
            ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    Source: () => ( /* binding */ Source),
                    /* harmony export */
                    isSource: () => ( /* binding */ isSource)
                    /* harmony export */
                });
                /* harmony import */
                var _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/graphql/jsutils/devAssert.mjs");
                /* harmony import */
                var _jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/graphql/jsutils/inspect.mjs");
                /* harmony import */
                var _jsutils_instanceOf_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/graphql/jsutils/instanceOf.mjs");




                /**
                 * A representation of source input to GraphQL. The `name` and `locationOffset` parameters are
                 * optional, but they are useful for clients who store GraphQL documents in source files.
                 * For example, if the GraphQL input starts at line 40 in a file named `Foo.graphql`, it might
                 * be useful for `name` to be `"Foo.graphql"` and location to be `{ line: 40, column: 1 }`.
                 * The `line` and `column` properties in `locationOffset` are 1-indexed.
                 */
                class Source {
                    constructor(
                        body,
                        name = 'GraphQL request',
                        locationOffset = {
                            line: 1,
                            column: 1,
                        },
                    ) {
                        typeof body === 'string' ||
                            (0, _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(false, `Body must be a string. Received: ${(0,_jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_1__.inspect)(body)}.`);
                        this.body = body;
                        this.name = name;
                        this.locationOffset = locationOffset;
                        this.locationOffset.line > 0 ||
                            (0, _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(
                                false,
                                'line in locationOffset is 1-indexed and must be positive.',
                            );
                        this.locationOffset.column > 0 ||
                            (0, _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(
                                false,
                                'column in locationOffset is 1-indexed and must be positive.',
                            );
                    }

                    get[Symbol.toStringTag]() {
                        return 'Source';
                    }
                }
                /**
                 * Test if the given value is a Source object.
                 *
                 * @internal
                 */

                function isSource(source) {
                    return (0, _jsutils_instanceOf_mjs__WEBPACK_IMPORTED_MODULE_2__.instanceOf)(source, Source);
                }


                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_apollo_client_link_core_ApolloLink_js.215a15896d5f7a33.js.map