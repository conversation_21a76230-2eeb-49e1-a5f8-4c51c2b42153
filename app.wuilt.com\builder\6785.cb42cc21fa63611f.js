(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [6785, 5145], {
        76396: (e, t) => {
            function n(e) {
                if (null == e) return window;
                if ("[object Window]" !== e.toString()) {
                    var t = e.ownerDocument;
                    return t && t.defaultView || window
                }
                return e
            }

            function r(e) {
                return e instanceof n(e).Element || e instanceof Element
            }

            function o(e) {
                return e instanceof n(e).HTMLElement || e instanceof HTMLElement
            }

            function i(e) {
                return "undefined" != typeof ShadowRoot && (e instanceof n(e).ShadowRoot || e instanceof ShadowRoot)
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var a = Math.max,
                f = Math.min,
                s = Math.round;

            function c() {
                var e = navigator.userAgentData;
                return null != e && e.brands && Array.isArray(e.brands) ? e.brands.map((function(e) {
                    return e.brand + "/" + e.version
                })).join(" ") : navigator.userAgent
            }

            function u() {
                return !/^((?!chrome|android).)*safari/i.test(c())
            }

            function p(e, t, i) {
                void 0 === t && (t = !1), void 0 === i && (i = !1);
                var a = e.getBoundingClientRect(),
                    f = 1,
                    c = 1;
                t && o(e) && (f = e.offsetWidth > 0 && s(a.width) / e.offsetWidth || 1, c = e.offsetHeight > 0 && s(a.height) / e.offsetHeight || 1);
                var p = (r(e) ? n(e) : window).visualViewport,
                    l = !u() && i,
                    d = (a.left + (l && p ? p.offsetLeft : 0)) / f,
                    h = (a.top + (l && p ? p.offsetTop : 0)) / c,
                    m = a.width / f,
                    v = a.height / c;
                return {
                    width: m,
                    height: v,
                    top: h,
                    right: d + m,
                    bottom: h + v,
                    left: d,
                    x: d,
                    y: h
                }
            }

            function l(e) {
                var t = n(e);
                return {
                    scrollLeft: t.pageXOffset,
                    scrollTop: t.pageYOffset
                }
            }

            function d(e) {
                return e ? (e.nodeName || "").toLowerCase() : null
            }

            function h(e) {
                return ((r(e) ? e.ownerDocument : e.document) || window.document).documentElement
            }

            function m(e) {
                return p(h(e)).left + l(e).scrollLeft
            }

            function v(e) {
                return n(e).getComputedStyle(e)
            }

            function y(e) {
                var t = v(e),
                    n = t.overflow,
                    r = t.overflowX,
                    o = t.overflowY;
                return /auto|scroll|overlay|hidden/.test(n + o + r)
            }

            function g(e, t, r) {
                void 0 === r && (r = !1);
                var i, a, f = o(t),
                    c = o(t) && function(e) {
                        var t = e.getBoundingClientRect(),
                            n = s(t.width) / e.offsetWidth || 1,
                            r = s(t.height) / e.offsetHeight || 1;
                        return 1 !== n || 1 !== r
                    }(t),
                    u = h(t),
                    v = p(e, c, r),
                    g = {
                        scrollLeft: 0,
                        scrollTop: 0
                    },
                    b = {
                        x: 0,
                        y: 0
                    };
                return (f || !f && !r) && (("body" !== d(t) || y(u)) && (g = (i = t) !== n(i) && o(i) ? {
                    scrollLeft: (a = i).scrollLeft,
                    scrollTop: a.scrollTop
                } : l(i)), o(t) ? ((b = p(t, !0)).x += t.clientLeft, b.y += t.clientTop) : u && (b.x = m(u))), {
                    x: v.left + g.scrollLeft - b.x,
                    y: v.top + g.scrollTop - b.y,
                    width: v.width,
                    height: v.height
                }
            }

            function b(e) {
                var t = p(e),
                    n = e.offsetWidth,
                    r = e.offsetHeight;
                return Math.abs(t.width - n) <= 1 && (n = t.width), Math.abs(t.height - r) <= 1 && (r = t.height), {
                    x: e.offsetLeft,
                    y: e.offsetTop,
                    width: n,
                    height: r
                }
            }

            function w(e) {
                return "html" === d(e) ? e : e.assignedSlot || e.parentNode || (i(e) ? e.host : null) || h(e)
            }

            function x(e) {
                return ["html", "body", "#document"].indexOf(d(e)) >= 0 ? e.ownerDocument.body : o(e) && y(e) ? e : x(w(e))
            }

            function O(e, t) {
                var r;
                void 0 === t && (t = []);
                var o = x(e),
                    i = o === (null == (r = e.ownerDocument) ? void 0 : r.body),
                    a = n(o),
                    f = i ? [a].concat(a.visualViewport || [], y(o) ? o : []) : o,
                    s = t.concat(f);
                return i ? s : s.concat(O(w(f)))
            }

            function j(e) {
                return ["table", "td", "th"].indexOf(d(e)) >= 0
            }

            function E(e) {
                return o(e) && "fixed" !== v(e).position ? e.offsetParent : null
            }

            function A(e) {
                for (var t = n(e), r = E(e); r && j(r) && "static" === v(r).position;) r = E(r);
                return r && ("html" === d(r) || "body" === d(r) && "static" === v(r).position) ? t : r || function(e) {
                    var t = /firefox/i.test(c());
                    if (/Trident/i.test(c()) && o(e) && "fixed" === v(e).position) return null;
                    var n = w(e);
                    for (i(n) && (n = n.host); o(n) && ["html", "body"].indexOf(d(n)) < 0;) {
                        var r = v(n);
                        if ("none" !== r.transform || "none" !== r.perspective || "paint" === r.contain || -1 !== ["transform", "perspective"].indexOf(r.willChange) || t && "filter" === r.willChange || t && r.filter && "none" !== r.filter) return n;
                        n = n.parentNode
                    }
                    return null
                }(e) || t
            }
            var D = "top",
                k = "bottom",
                M = "right",
                L = "left",
                P = "auto",
                S = [D, k, M, L],
                B = "start",
                W = "end",
                H = "clippingParents",
                R = "viewport",
                T = "popper",
                V = "reference",
                C = S.reduce((function(e, t) {
                    return e.concat([t + "-" + B, t + "-" + W])
                }), []),
                q = [].concat(S, [P]).reduce((function(e, t) {
                    return e.concat([t, t + "-" + B, t + "-" + W])
                }), []),
                _ = ["beforeRead", "read", "afterRead", "beforeMain", "main", "afterMain", "beforeWrite", "write", "afterWrite"];

            function z(e) {
                var t = new Map,
                    n = new Set,
                    r = [];

                function o(e) {
                    n.add(e.name), [].concat(e.requires || [], e.requiresIfExists || []).forEach((function(e) {
                        if (!n.has(e)) {
                            var r = t.get(e);
                            r && o(r)
                        }
                    })), r.push(e)
                }
                return e.forEach((function(e) {
                    t.set(e.name, e)
                })), e.forEach((function(e) {
                    n.has(e.name) || o(e)
                })), r
            }

            function N(e, t) {
                var n = t.getRootNode && t.getRootNode();
                if (e.contains(t)) return !0;
                if (n && i(n)) {
                    var r = t;
                    do {
                        if (r && e.isSameNode(r)) return !0;
                        r = r.parentNode || r.host
                    } while (r)
                }
                return !1
            }

            function I(e) {
                return Object.assign({}, e, {
                    left: e.x,
                    top: e.y,
                    right: e.x + e.width,
                    bottom: e.y + e.height
                })
            }

            function F(e, t, o) {
                return t === R ? I(function(e, t) {
                    var r = n(e),
                        o = h(e),
                        i = r.visualViewport,
                        a = o.clientWidth,
                        f = o.clientHeight,
                        s = 0,
                        c = 0;
                    if (i) {
                        a = i.width, f = i.height;
                        var p = u();
                        (p || !p && "fixed" === t) && (s = i.offsetLeft, c = i.offsetTop)
                    }
                    return {
                        width: a,
                        height: f,
                        x: s + m(e),
                        y: c
                    }
                }(e, o)) : r(t) ? function(e, t) {
                    var n = p(e, !1, "fixed" === t);
                    return n.top = n.top + e.clientTop, n.left = n.left + e.clientLeft, n.bottom = n.top + e.clientHeight, n.right = n.left + e.clientWidth, n.width = e.clientWidth, n.height = e.clientHeight, n.x = n.left, n.y = n.top, n
                }(t, o) : I(function(e) {
                    var t, n = h(e),
                        r = l(e),
                        o = null == (t = e.ownerDocument) ? void 0 : t.body,
                        i = a(n.scrollWidth, n.clientWidth, o ? o.scrollWidth : 0, o ? o.clientWidth : 0),
                        f = a(n.scrollHeight, n.clientHeight, o ? o.scrollHeight : 0, o ? o.clientHeight : 0),
                        s = -r.scrollLeft + m(e),
                        c = -r.scrollTop;
                    return "rtl" === v(o || n).direction && (s += a(n.clientWidth, o ? o.clientWidth : 0) - i), {
                        width: i,
                        height: f,
                        x: s,
                        y: c
                    }
                }(h(e)))
            }

            function U(e) {
                return e.split("-")[0]
            }

            function X(e) {
                return e.split("-")[1]
            }

            function Y(e) {
                return ["top", "bottom"].indexOf(e) >= 0 ? "x" : "y"
            }

            function $(e) {
                var t, n = e.reference,
                    r = e.element,
                    o = e.placement,
                    i = o ? U(o) : null,
                    a = o ? X(o) : null,
                    f = n.x + n.width / 2 - r.width / 2,
                    s = n.y + n.height / 2 - r.height / 2;
                switch (i) {
                    case D:
                        t = {
                            x: f,
                            y: n.y - r.height
                        };
                        break;
                    case k:
                        t = {
                            x: f,
                            y: n.y + n.height
                        };
                        break;
                    case M:
                        t = {
                            x: n.x + n.width,
                            y: s
                        };
                        break;
                    case L:
                        t = {
                            x: n.x - r.width,
                            y: s
                        };
                        break;
                    default:
                        t = {
                            x: n.x,
                            y: n.y
                        }
                }
                var c = i ? Y(i) : null;
                if (null != c) {
                    var u = "y" === c ? "height" : "width";
                    switch (a) {
                        case B:
                            t[c] = t[c] - (n[u] / 2 - r[u] / 2);
                            break;
                        case W:
                            t[c] = t[c] + (n[u] / 2 - r[u] / 2)
                    }
                }
                return t
            }

            function G(e) {
                return Object.assign({}, {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0
                }, e)
            }

            function J(e, t) {
                return t.reduce((function(t, n) {
                    return t[n] = e, t
                }), {})
            }

            function K(e, t) {
                void 0 === t && (t = {});
                var n = t,
                    i = n.placement,
                    s = void 0 === i ? e.placement : i,
                    c = n.strategy,
                    u = void 0 === c ? e.strategy : c,
                    l = n.boundary,
                    m = void 0 === l ? H : l,
                    y = n.rootBoundary,
                    g = void 0 === y ? R : y,
                    b = n.elementContext,
                    x = void 0 === b ? T : b,
                    j = n.altBoundary,
                    E = void 0 !== j && j,
                    L = n.padding,
                    P = void 0 === L ? 0 : L,
                    B = G("number" != typeof P ? P : J(P, S)),
                    W = x === T ? V : T,
                    C = e.rects.popper,
                    q = e.elements[E ? W : x],
                    _ = function(e, t, n, i) {
                        var s = "clippingParents" === t ? function(e) {
                                var t = O(w(e)),
                                    n = ["absolute", "fixed"].indexOf(v(e).position) >= 0 && o(e) ? A(e) : e;
                                return r(n) ? t.filter((function(e) {
                                    return r(e) && N(e, n) && "body" !== d(e)
                                })) : []
                            }(e) : [].concat(t),
                            c = [].concat(s, [n]),
                            u = c[0],
                            p = c.reduce((function(t, n) {
                                var r = F(e, n, i);
                                return t.top = a(r.top, t.top), t.right = f(r.right, t.right), t.bottom = f(r.bottom, t.bottom), t.left = a(r.left, t.left), t
                            }), F(e, u, i));
                        return p.width = p.right - p.left, p.height = p.bottom - p.top, p.x = p.left, p.y = p.top, p
                    }(r(q) ? q : q.contextElement || h(e.elements.popper), m, g, u),
                    z = p(e.elements.reference),
                    U = $({
                        reference: z,
                        element: C,
                        strategy: "absolute",
                        placement: s
                    }),
                    X = I(Object.assign({}, C, U)),
                    Y = x === T ? X : z,
                    K = {
                        top: _.top - Y.top + B.top,
                        bottom: Y.bottom - _.bottom + B.bottom,
                        left: _.left - Y.left + B.left,
                        right: Y.right - _.right + B.right
                    },
                    Q = e.modifiersData.offset;
                if (x === T && Q) {
                    var Z = Q[s];
                    Object.keys(K).forEach((function(e) {
                        var t = [M, k].indexOf(e) >= 0 ? 1 : -1,
                            n = [D, k].indexOf(e) >= 0 ? "y" : "x";
                        K[e] += Z[n] * t
                    }))
                }
                return K
            }
            var Q = {
                placement: "bottom",
                modifiers: [],
                strategy: "absolute"
            };

            function Z() {
                for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
                return !t.some((function(e) {
                    return !(e && "function" == typeof e.getBoundingClientRect)
                }))
            }

            function ee(e) {
                void 0 === e && (e = {});
                var t = e,
                    n = t.defaultModifiers,
                    o = void 0 === n ? [] : n,
                    i = t.defaultOptions,
                    a = void 0 === i ? Q : i;
                return function(e, t, n) {
                    void 0 === n && (n = a);
                    var i, f, s = {
                            placement: "bottom",
                            orderedModifiers: [],
                            options: Object.assign({}, Q, a),
                            modifiersData: {},
                            elements: {
                                reference: e,
                                popper: t
                            },
                            attributes: {},
                            styles: {}
                        },
                        c = [],
                        u = !1,
                        p = {
                            state: s,
                            setOptions: function(n) {
                                var i = "function" == typeof n ? n(s.options) : n;
                                l(), s.options = Object.assign({}, a, s.options, i), s.scrollParents = {
                                    reference: r(e) ? O(e) : e.contextElement ? O(e.contextElement) : [],
                                    popper: O(t)
                                };
                                var f, u, d = function(e) {
                                    var t = z(e);
                                    return _.reduce((function(e, n) {
                                        return e.concat(t.filter((function(e) {
                                            return e.phase === n
                                        })))
                                    }), [])
                                }((f = [].concat(o, s.options.modifiers), u = f.reduce((function(e, t) {
                                    var n = e[t.name];
                                    return e[t.name] = n ? Object.assign({}, n, t, {
                                        options: Object.assign({}, n.options, t.options),
                                        data: Object.assign({}, n.data, t.data)
                                    }) : t, e
                                }), {}), Object.keys(u).map((function(e) {
                                    return u[e]
                                }))));
                                return s.orderedModifiers = d.filter((function(e) {
                                    return e.enabled
                                })), s.orderedModifiers.forEach((function(e) {
                                    var t = e.name,
                                        n = e.options,
                                        r = void 0 === n ? {} : n,
                                        o = e.effect;
                                    if ("function" == typeof o) {
                                        var i = o({
                                            state: s,
                                            name: t,
                                            instance: p,
                                            options: r
                                        });
                                        c.push(i || function() {})
                                    }
                                })), p.update()
                            },
                            forceUpdate: function() {
                                if (!u) {
                                    var e = s.elements,
                                        t = e.reference,
                                        n = e.popper;
                                    if (Z(t, n)) {
                                        s.rects = {
                                            reference: g(t, A(n), "fixed" === s.options.strategy),
                                            popper: b(n)
                                        }, s.reset = !1, s.placement = s.options.placement, s.orderedModifiers.forEach((function(e) {
                                            return s.modifiersData[e.name] = Object.assign({}, e.data)
                                        }));
                                        for (var r = 0; r < s.orderedModifiers.length; r++)
                                            if (!0 !== s.reset) {
                                                var o = s.orderedModifiers[r],
                                                    i = o.fn,
                                                    a = o.options,
                                                    f = void 0 === a ? {} : a,
                                                    c = o.name;
                                                "function" == typeof i && (s = i({
                                                    state: s,
                                                    options: f,
                                                    name: c,
                                                    instance: p
                                                }) || s)
                                            } else s.reset = !1, r = -1
                                    }
                                }
                            },
                            update: (i = function() {
                                return new Promise((function(e) {
                                    p.forceUpdate(), e(s)
                                }))
                            }, function() {
                                return f || (f = new Promise((function(e) {
                                    Promise.resolve().then((function() {
                                        f = void 0, e(i())
                                    }))
                                }))), f
                            }),
                            destroy: function() {
                                l(), u = !0
                            }
                        };
                    if (!Z(e, t)) return p;

                    function l() {
                        c.forEach((function(e) {
                            return e()
                        })), c = []
                    }
                    return p.setOptions(n).then((function(e) {
                        !u && n.onFirstUpdate && n.onFirstUpdate(e)
                    })), p
                }
            }
            var te = {
                    passive: !0
                },
                ne = {
                    name: "eventListeners",
                    enabled: !0,
                    phase: "write",
                    fn: function() {},
                    effect: function(e) {
                        var t = e.state,
                            r = e.instance,
                            o = e.options,
                            i = o.scroll,
                            a = void 0 === i || i,
                            f = o.resize,
                            s = void 0 === f || f,
                            c = n(t.elements.popper),
                            u = [].concat(t.scrollParents.reference, t.scrollParents.popper);
                        return a && u.forEach((function(e) {
                                e.addEventListener("scroll", r.update, te)
                            })), s && c.addEventListener("resize", r.update, te),
                            function() {
                                a && u.forEach((function(e) {
                                    e.removeEventListener("scroll", r.update, te)
                                })), s && c.removeEventListener("resize", r.update, te)
                            }
                    },
                    data: {}
                },
                re = {
                    name: "popperOffsets",
                    enabled: !0,
                    phase: "read",
                    fn: function(e) {
                        var t = e.state,
                            n = e.name;
                        t.modifiersData[n] = $({
                            reference: t.rects.reference,
                            element: t.rects.popper,
                            strategy: "absolute",
                            placement: t.placement
                        })
                    },
                    data: {}
                },
                oe = {
                    top: "auto",
                    right: "auto",
                    bottom: "auto",
                    left: "auto"
                };

            function ie(e) {
                var t, r = e.popper,
                    o = e.popperRect,
                    i = e.placement,
                    a = e.variation,
                    f = e.offsets,
                    c = e.position,
                    u = e.gpuAcceleration,
                    p = e.adaptive,
                    l = e.roundOffsets,
                    d = e.isFixed,
                    m = f.x,
                    y = void 0 === m ? 0 : m,
                    g = f.y,
                    b = void 0 === g ? 0 : g,
                    w = "function" == typeof l ? l({
                        x: y,
                        y: b
                    }) : {
                        x: y,
                        y: b
                    };
                y = w.x, b = w.y;
                var x = f.hasOwnProperty("x"),
                    O = f.hasOwnProperty("y"),
                    j = L,
                    E = D,
                    P = window;
                if (p) {
                    var S = A(r),
                        B = "clientHeight",
                        H = "clientWidth";
                    S === n(r) && "static" !== v(S = h(r)).position && "absolute" === c && (B = "scrollHeight", H = "scrollWidth"), (i === D || (i === L || i === M) && a === W) && (E = k, b -= (d && S === P && P.visualViewport ? P.visualViewport.height : S[B]) - o.height, b *= u ? 1 : -1), i !== L && (i !== D && i !== k || a !== W) || (j = M, y -= (d && S === P && P.visualViewport ? P.visualViewport.width : S[H]) - o.width, y *= u ? 1 : -1)
                }
                var R, T = Object.assign({
                        position: c
                    }, p && oe),
                    V = !0 === l ? function(e, t) {
                        var n = e.x,
                            r = e.y,
                            o = t.devicePixelRatio || 1;
                        return {
                            x: s(n * o) / o || 0,
                            y: s(r * o) / o || 0
                        }
                    }({
                        x: y,
                        y: b
                    }, n(r)) : {
                        x: y,
                        y: b
                    };
                return y = V.x, b = V.y, u ? Object.assign({}, T, ((R = {})[E] = O ? "0" : "", R[j] = x ? "0" : "", R.transform = (P.devicePixelRatio || 1) <= 1 ? "translate(" + y + "px, " + b + "px)" : "translate3d(" + y + "px, " + b + "px, 0)", R)) : Object.assign({}, T, ((t = {})[E] = O ? b + "px" : "", t[j] = x ? y + "px" : "", t.transform = "", t))
            }
            var ae = {
                    name: "computeStyles",
                    enabled: !0,
                    phase: "beforeWrite",
                    fn: function(e) {
                        var t = e.state,
                            n = e.options,
                            r = n.gpuAcceleration,
                            o = void 0 === r || r,
                            i = n.adaptive,
                            a = void 0 === i || i,
                            f = n.roundOffsets,
                            s = void 0 === f || f,
                            c = {
                                placement: U(t.placement),
                                variation: X(t.placement),
                                popper: t.elements.popper,
                                popperRect: t.rects.popper,
                                gpuAcceleration: o,
                                isFixed: "fixed" === t.options.strategy
                            };
                        null != t.modifiersData.popperOffsets && (t.styles.popper = Object.assign({}, t.styles.popper, ie(Object.assign({}, c, {
                            offsets: t.modifiersData.popperOffsets,
                            position: t.options.strategy,
                            adaptive: a,
                            roundOffsets: s
                        })))), null != t.modifiersData.arrow && (t.styles.arrow = Object.assign({}, t.styles.arrow, ie(Object.assign({}, c, {
                            offsets: t.modifiersData.arrow,
                            position: "absolute",
                            adaptive: !1,
                            roundOffsets: s
                        })))), t.attributes.popper = Object.assign({}, t.attributes.popper, {
                            "data-popper-placement": t.placement
                        })
                    },
                    data: {}
                },
                fe = {
                    name: "applyStyles",
                    enabled: !0,
                    phase: "write",
                    fn: function(e) {
                        var t = e.state;
                        Object.keys(t.elements).forEach((function(e) {
                            var n = t.styles[e] || {},
                                r = t.attributes[e] || {},
                                i = t.elements[e];
                            o(i) && d(i) && (Object.assign(i.style, n), Object.keys(r).forEach((function(e) {
                                var t = r[e];
                                !1 === t ? i.removeAttribute(e) : i.setAttribute(e, !0 === t ? "" : t)
                            })))
                        }))
                    },
                    effect: function(e) {
                        var t = e.state,
                            n = {
                                popper: {
                                    position: t.options.strategy,
                                    left: "0",
                                    top: "0",
                                    margin: "0"
                                },
                                arrow: {
                                    position: "absolute"
                                },
                                reference: {}
                            };
                        return Object.assign(t.elements.popper.style, n.popper), t.styles = n, t.elements.arrow && Object.assign(t.elements.arrow.style, n.arrow),
                            function() {
                                Object.keys(t.elements).forEach((function(e) {
                                    var r = t.elements[e],
                                        i = t.attributes[e] || {},
                                        a = Object.keys(t.styles.hasOwnProperty(e) ? t.styles[e] : n[e]).reduce((function(e, t) {
                                            return e[t] = "", e
                                        }), {});
                                    o(r) && d(r) && (Object.assign(r.style, a), Object.keys(i).forEach((function(e) {
                                        r.removeAttribute(e)
                                    })))
                                }))
                            }
                    },
                    requires: ["computeStyles"]
                },
                se = {
                    name: "offset",
                    enabled: !0,
                    phase: "main",
                    requires: ["popperOffsets"],
                    fn: function(e) {
                        var t = e.state,
                            n = e.options,
                            r = e.name,
                            o = n.offset,
                            i = void 0 === o ? [0, 0] : o,
                            a = q.reduce((function(e, n) {
                                return e[n] = function(e, t, n) {
                                    var r = U(e),
                                        o = [L, D].indexOf(r) >= 0 ? -1 : 1,
                                        i = "function" == typeof n ? n(Object.assign({}, t, {
                                            placement: e
                                        })) : n,
                                        a = i[0],
                                        f = i[1];
                                    return a = a || 0, f = (f || 0) * o, [L, M].indexOf(r) >= 0 ? {
                                        x: f,
                                        y: a
                                    } : {
                                        x: a,
                                        y: f
                                    }
                                }(n, t.rects, i), e
                            }), {}),
                            f = a[t.placement],
                            s = f.x,
                            c = f.y;
                        null != t.modifiersData.popperOffsets && (t.modifiersData.popperOffsets.x += s, t.modifiersData.popperOffsets.y += c), t.modifiersData[r] = a
                    }
                },
                ce = {
                    left: "right",
                    right: "left",
                    bottom: "top",
                    top: "bottom"
                };

            function ue(e) {
                return e.replace(/left|right|bottom|top/g, (function(e) {
                    return ce[e]
                }))
            }
            var pe = {
                start: "end",
                end: "start"
            };

            function le(e) {
                return e.replace(/start|end/g, (function(e) {
                    return pe[e]
                }))
            }

            function de(e, t) {
                void 0 === t && (t = {});
                var n = t,
                    r = n.placement,
                    o = n.boundary,
                    i = n.rootBoundary,
                    a = n.padding,
                    f = n.flipVariations,
                    s = n.allowedAutoPlacements,
                    c = void 0 === s ? q : s,
                    u = X(r),
                    p = u ? f ? C : C.filter((function(e) {
                        return X(e) === u
                    })) : S,
                    l = p.filter((function(e) {
                        return c.indexOf(e) >= 0
                    }));
                0 === l.length && (l = p);
                var d = l.reduce((function(t, n) {
                    return t[n] = K(e, {
                        placement: n,
                        boundary: o,
                        rootBoundary: i,
                        padding: a
                    })[U(n)], t
                }), {});
                return Object.keys(d).sort((function(e, t) {
                    return d[e] - d[t]
                }))
            }
            var he = {
                name: "flip",
                enabled: !0,
                phase: "main",
                fn: function(e) {
                    var t = e.state,
                        n = e.options,
                        r = e.name;
                    if (!t.modifiersData[r]._skip) {
                        for (var o = n.mainAxis, i = void 0 === o || o, a = n.altAxis, f = void 0 === a || a, s = n.fallbackPlacements, c = n.padding, u = n.boundary, p = n.rootBoundary, l = n.altBoundary, d = n.flipVariations, h = void 0 === d || d, m = n.allowedAutoPlacements, v = t.options.placement, y = U(v), g = s || (y !== v && h ? function(e) {
                                if (U(e) === P) return [];
                                var t = ue(e);
                                return [le(e), t, le(t)]
                            }(v) : [ue(v)]), b = [v].concat(g).reduce((function(e, n) {
                                return e.concat(U(n) === P ? de(t, {
                                    placement: n,
                                    boundary: u,
                                    rootBoundary: p,
                                    padding: c,
                                    flipVariations: h,
                                    allowedAutoPlacements: m
                                }) : n)
                            }), []), w = t.rects.reference, x = t.rects.popper, O = new Map, j = !0, E = b[0], A = 0; A < b.length; A++) {
                            var S = b[A],
                                W = U(S),
                                H = X(S) === B,
                                R = [D, k].indexOf(W) >= 0,
                                T = R ? "width" : "height",
                                V = K(t, {
                                    placement: S,
                                    boundary: u,
                                    rootBoundary: p,
                                    altBoundary: l,
                                    padding: c
                                }),
                                C = R ? H ? M : L : H ? k : D;
                            w[T] > x[T] && (C = ue(C));
                            var q = ue(C),
                                _ = [];
                            if (i && _.push(V[W] <= 0), f && _.push(V[C] <= 0, V[q] <= 0), _.every((function(e) {
                                    return e
                                }))) {
                                E = S, j = !1;
                                break
                            }
                            O.set(S, _)
                        }
                        if (j)
                            for (var z = function(e) {
                                    var t = b.find((function(t) {
                                        var n = O.get(t);
                                        if (n) return n.slice(0, e).every((function(e) {
                                            return e
                                        }))
                                    }));
                                    if (t) return E = t, "break"
                                }, N = h ? 3 : 1; N > 0 && "break" !== z(N); N--);
                        t.placement !== E && (t.modifiersData[r]._skip = !0, t.placement = E, t.reset = !0)
                    }
                },
                requiresIfExists: ["offset"],
                data: {
                    _skip: !1
                }
            };

            function me(e, t, n) {
                return a(e, f(t, n))
            }
            var ve = {
                    name: "preventOverflow",
                    enabled: !0,
                    phase: "main",
                    fn: function(e) {
                        var t = e.state,
                            n = e.options,
                            r = e.name,
                            o = n.mainAxis,
                            i = void 0 === o || o,
                            s = n.altAxis,
                            c = void 0 !== s && s,
                            u = n.boundary,
                            p = n.rootBoundary,
                            l = n.altBoundary,
                            d = n.padding,
                            h = n.tether,
                            m = void 0 === h || h,
                            v = n.tetherOffset,
                            y = void 0 === v ? 0 : v,
                            g = K(t, {
                                boundary: u,
                                rootBoundary: p,
                                padding: d,
                                altBoundary: l
                            }),
                            w = U(t.placement),
                            x = X(t.placement),
                            O = !x,
                            j = Y(w),
                            E = "x" === j ? "y" : "x",
                            P = t.modifiersData.popperOffsets,
                            S = t.rects.reference,
                            W = t.rects.popper,
                            H = "function" == typeof y ? y(Object.assign({}, t.rects, {
                                placement: t.placement
                            })) : y,
                            R = "number" == typeof H ? {
                                mainAxis: H,
                                altAxis: H
                            } : Object.assign({
                                mainAxis: 0,
                                altAxis: 0
                            }, H),
                            T = t.modifiersData.offset ? t.modifiersData.offset[t.placement] : null,
                            V = {
                                x: 0,
                                y: 0
                            };
                        if (P) {
                            if (i) {
                                var C, q = "y" === j ? D : L,
                                    _ = "y" === j ? k : M,
                                    z = "y" === j ? "height" : "width",
                                    N = P[j],
                                    I = N + g[q],
                                    F = N - g[_],
                                    $ = m ? -W[z] / 2 : 0,
                                    G = x === B ? S[z] : W[z],
                                    J = x === B ? -W[z] : -S[z],
                                    Q = t.elements.arrow,
                                    Z = m && Q ? b(Q) : {
                                        width: 0,
                                        height: 0
                                    },
                                    ee = t.modifiersData["arrow#persistent"] ? t.modifiersData["arrow#persistent"].padding : {
                                        top: 0,
                                        right: 0,
                                        bottom: 0,
                                        left: 0
                                    },
                                    te = ee[q],
                                    ne = ee[_],
                                    re = me(0, S[z], Z[z]),
                                    oe = O ? S[z] / 2 - $ - re - te - R.mainAxis : G - re - te - R.mainAxis,
                                    ie = O ? -S[z] / 2 + $ + re + ne + R.mainAxis : J + re + ne + R.mainAxis,
                                    ae = t.elements.arrow && A(t.elements.arrow),
                                    fe = ae ? "y" === j ? ae.clientTop || 0 : ae.clientLeft || 0 : 0,
                                    se = null != (C = null == T ? void 0 : T[j]) ? C : 0,
                                    ce = N + ie - se,
                                    ue = me(m ? f(I, N + oe - se - fe) : I, N, m ? a(F, ce) : F);
                                P[j] = ue, V[j] = ue - N
                            }
                            if (c) {
                                var pe, le = "x" === j ? D : L,
                                    de = "x" === j ? k : M,
                                    he = P[E],
                                    ve = "y" === E ? "height" : "width",
                                    ye = he + g[le],
                                    ge = he - g[de],
                                    be = -1 !== [D, L].indexOf(w),
                                    we = null != (pe = null == T ? void 0 : T[E]) ? pe : 0,
                                    xe = be ? ye : he - S[ve] - W[ve] - we + R.altAxis,
                                    Oe = be ? he + S[ve] + W[ve] - we - R.altAxis : ge,
                                    je = m && be ? function(e, t, n) {
                                        var r = me(e, t, n);
                                        return r > n ? n : r
                                    }(xe, he, Oe) : me(m ? xe : ye, he, m ? Oe : ge);
                                P[E] = je, V[E] = je - he
                            }
                            t.modifiersData[r] = V
                        }
                    },
                    requiresIfExists: ["offset"]
                },
                ye = {
                    name: "arrow",
                    enabled: !0,
                    phase: "main",
                    fn: function(e) {
                        var t, n = e.state,
                            r = e.name,
                            o = e.options,
                            i = n.elements.arrow,
                            a = n.modifiersData.popperOffsets,
                            f = U(n.placement),
                            s = Y(f),
                            c = [L, M].indexOf(f) >= 0 ? "height" : "width";
                        if (i && a) {
                            var u = function(e, t) {
                                    return G("number" != typeof(e = "function" == typeof e ? e(Object.assign({}, t.rects, {
                                        placement: t.placement
                                    })) : e) ? e : J(e, S))
                                }(o.padding, n),
                                p = b(i),
                                l = "y" === s ? D : L,
                                d = "y" === s ? k : M,
                                h = n.rects.reference[c] + n.rects.reference[s] - a[s] - n.rects.popper[c],
                                m = a[s] - n.rects.reference[s],
                                v = A(i),
                                y = v ? "y" === s ? v.clientHeight || 0 : v.clientWidth || 0 : 0,
                                g = h / 2 - m / 2,
                                w = u[l],
                                x = y - p[c] - u[d],
                                O = y / 2 - p[c] / 2 + g,
                                j = me(w, O, x),
                                E = s;
                            n.modifiersData[r] = ((t = {})[E] = j, t.centerOffset = j - O, t)
                        }
                    },
                    effect: function(e) {
                        var t = e.state,
                            n = e.options.element,
                            r = void 0 === n ? "[data-popper-arrow]" : n;
                        null != r && ("string" != typeof r || (r = t.elements.popper.querySelector(r))) && N(t.elements.popper, r) && (t.elements.arrow = r)
                    },
                    requires: ["popperOffsets"],
                    requiresIfExists: ["preventOverflow"]
                };

            function ge(e, t, n) {
                return void 0 === n && (n = {
                    x: 0,
                    y: 0
                }), {
                    top: e.top - t.height - n.y,
                    right: e.right - t.width + n.x,
                    bottom: e.bottom - t.height + n.y,
                    left: e.left - t.width - n.x
                }
            }

            function be(e) {
                return [D, M, k, L].some((function(t) {
                    return e[t] >= 0
                }))
            }
            var we = {
                    name: "hide",
                    enabled: !0,
                    phase: "main",
                    requiresIfExists: ["preventOverflow"],
                    fn: function(e) {
                        var t = e.state,
                            n = e.name,
                            r = t.rects.reference,
                            o = t.rects.popper,
                            i = t.modifiersData.preventOverflow,
                            a = K(t, {
                                elementContext: "reference"
                            }),
                            f = K(t, {
                                altBoundary: !0
                            }),
                            s = ge(a, r),
                            c = ge(f, o, i),
                            u = be(s),
                            p = be(c);
                        t.modifiersData[n] = {
                            referenceClippingOffsets: s,
                            popperEscapeOffsets: c,
                            isReferenceHidden: u,
                            hasPopperEscaped: p
                        }, t.attributes.popper = Object.assign({}, t.attributes.popper, {
                            "data-popper-reference-hidden": u,
                            "data-popper-escaped": p
                        })
                    }
                },
                xe = ee({
                    defaultModifiers: [ne, re, ae, fe]
                }),
                Oe = [ne, re, ae, fe, se, he, ve, ye, we],
                je = ee({
                    defaultModifiers: Oe
                });
            t.applyStyles = fe, t.arrow = ye, t.computeStyles = ae, t.createPopper = je, t.createPopperLite = xe, t.defaultModifiers = Oe, t.detectOverflow = K, t.eventListeners = ne, t.flip = he, t.hide = we, t.offset = se, t.popperGenerator = ee, t.popperOffsets = re, t.preventOverflow = ve
        },
        45145: e => {
            var t = "undefined" != typeof Element,
                n = "function" == typeof Map,
                r = "function" == typeof Set,
                o = "function" == typeof ArrayBuffer && !!ArrayBuffer.isView;

            function i(e, a) {
                if (e === a) return !0;
                if (e && a && "object" == typeof e && "object" == typeof a) {
                    if (e.constructor !== a.constructor) return !1;
                    var f, s, c, u;
                    if (Array.isArray(e)) {
                        if ((f = e.length) != a.length) return !1;
                        for (s = f; 0 != s--;)
                            if (!i(e[s], a[s])) return !1;
                        return !0
                    }
                    if (n && e instanceof Map && a instanceof Map) {
                        if (e.size !== a.size) return !1;
                        for (u = e.entries(); !(s = u.next()).done;)
                            if (!a.has(s.value[0])) return !1;
                        for (u = e.entries(); !(s = u.next()).done;)
                            if (!i(s.value[1], a.get(s.value[0]))) return !1;
                        return !0
                    }
                    if (r && e instanceof Set && a instanceof Set) {
                        if (e.size !== a.size) return !1;
                        for (u = e.entries(); !(s = u.next()).done;)
                            if (!a.has(s.value[0])) return !1;
                        return !0
                    }
                    if (o && ArrayBuffer.isView(e) && ArrayBuffer.isView(a)) {
                        if ((f = e.length) != a.length) return !1;
                        for (s = f; 0 != s--;)
                            if (e[s] !== a[s]) return !1;
                        return !0
                    }
                    if (e.constructor === RegExp) return e.source === a.source && e.flags === a.flags;
                    if (e.valueOf !== Object.prototype.valueOf && "function" == typeof e.valueOf && "function" == typeof a.valueOf) return e.valueOf() === a.valueOf();
                    if (e.toString !== Object.prototype.toString && "function" == typeof e.toString && "function" == typeof a.toString) return e.toString() === a.toString();
                    if ((f = (c = Object.keys(e)).length) !== Object.keys(a).length) return !1;
                    for (s = f; 0 != s--;)
                        if (!Object.prototype.hasOwnProperty.call(a, c[s])) return !1;
                    if (t && e instanceof Element) return !1;
                    for (s = f; 0 != s--;)
                        if (("_owner" !== c[s] && "__v" !== c[s] && "__o" !== c[s] || !e.$$typeof) && !i(e[c[s]], a[c[s]])) return !1;
                    return !0
                }
                return e != e && a != a
            }
            e.exports = function(e, t) {
                try {
                    return i(e, t)
                } catch (e) {
                    if ((e.message || "").match(/stack|recursion/i)) return console.warn("react-fast-compare cannot handle circular refs"), !1;
                    throw e
                }
            }
        }
    }
]);
//# sourceMappingURL=6785.cb42cc21fa63611f.js.map