window['twitter-adsDeps'] = ["/integrations/vendor/commons.59560acdd69ed701c941.js"];window['twitter-adsLoader'] = function() { return window["twitter-adsIntegration"]=function(t){function e(e){for(var r,a,s=e[0],c=e[1],p=e[2],d=0,f=[];d<s.length;d++)a=s[d],Object.prototype.hasOwnProperty.call(i,a)&&i[a]&&f.push(i[a][0]),i[a]=0;for(r in c)Object.prototype.hasOwnProperty.call(c,r)&&(t[r]=c[r]);for(u&&u(e);f.length;)f.shift()();return o.push.apply(o,p||[]),n()}function n(){for(var t,e=0;e<o.length;e++){for(var n=o[e],r=!0,s=1;s<n.length;s++){var c=n[s];0!==i[c]&&(r=!1)}r&&(o.splice(e--,1),t=a(a.s=n[0]))}return t}var r={},i={142:0},o=[];function a(e){if(r[e])return r[e].exports;var n=r[e]={i:e,l:!1,exports:{}};return t[e].call(n.exports,n,n.exports,a),n.l=!0,n.exports}a.m=t,a.c=r,a.d=function(t,e,n){a.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},a.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},a.t=function(t,e){if(1&e&&(t=a(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)a.d(n,r,function(e){return t[e]}.bind(null,r));return n},a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,"a",e),e},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},a.p="";var s=window.webpackJsonp_name_Integration=window.webpackJsonp_name_Integration||[],c=s.push.bind(s);s.push=e,s=s.slice();for(var p=0;p<s.length;p++)e(s[p]);var u=c;return o.push(["qU88",0]),n()}({qU88:function(t,e,n){"use strict";function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var o=function(t,e){var n=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){i(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},e);for(var o in t)void 0!==t[o]&&(n[o]=t[o]);return n},a=n("hjHq"),s=n("LUFQ"),c=n("IWyO"),p=n("WiAo"),u=n("NGGi").Track,d=n("ge09"),f=t.exports=a("Twitter Ads").option("page","").option("universalTagPixelId","").option("identifier","productId").tag("singleTag",'<img src="//analytics.twitter.com/i/adsct?txn_id={{ pixelId }}&p_id=Twitter&tw_sale_amount={{ revenue }}&tw_order_quantity={{ quantity }}"/>').tag("universalTag",'<script src="//static.ads-twitter.com/uwt.js">').mapping("events");function l(t){return t.status?{status:t.status}:{}}f.prototype.initialize=function(){var t,e,n,r,i,o,a=this;this.options.universalTagPixelId?(t=window,e=document,n="script",t.twq||((r=t.twq=function(){r.exe?r.exe.apply(r,arguments):r.queue.push(arguments)}).version="1.1",r.queue=[],(i=e.createElement(n)).async=!0,i.src="https://static.ads-twitter.com/uwt.js",(o=e.getElementsByTagName(n)[0]).parentNode.insertBefore(i,o)),this.load("universalTag",(function(){window.twq("config",a.options.universalTagPixelId),a.ready()}))):this.ready()},f.prototype.page=function(t){this.options.universalTagPixelId&&window.twq("track","PageView"),this.options.page&&this.load("singleTag",{pixelId:this.options.page,revenue:0,quantity:0})},f.prototype.track=function(t){this.fireLegacyConversionTags(t)},f.prototype.productsSearched=function(t){if(this.fireLegacyConversionTags(t),this.options.universalTagPixelId){var e=l(t.properties());window.twq("track","Search",e)}},f.prototype.productViewed=function(t){if(this.fireLegacyConversionTags(t),this.options.universalTagPixelId){var e=t.properties(),n={content_ids:[t[this.options.identifier]()],content_type:"product",content_name:t.name(),content_category:t.category()};n=d(n,l(e)),window.twq("track","ViewContent",n)}},f.prototype.productAdded=function(t){if(this.fireLegacyConversionTags(t),this.options.universalTagPixelId){var e=t.properties(),n={content_ids:[t[this.options.identifier]()],content_type:"product",content_name:t.name()};n=d(n,l(e)),window.twq("track","AddToCart",n)}},f.prototype.orderCompleted=function(t){var e=this.options.identifier,n=s((function(t,e){return t+(p(e,"quantity")||0)}),0,t.products());if(this.fireLegacyConversionTags(t,{quantity:n}),this.options.universalTagPixelId){var r={currency:t.currency(),content_type:"product",order_id:t.orderId(),num_items:n.toString()};t.revenue()&&(r.value=t.revenue().toFixed(2)),r=d(r,l(t.properties()));var i=s((function(t,n){var r=new u({properties:n}),i=r[e]();return t.ids.push(i),t.names.push(r.name()),t}),{ids:[],names:[]},t.products());r.content_ids=i.ids.sort(),r.content_name=i.names.sort().join(", "),window.twq("track","Purchase",r)}},f.prototype.productAddedToWishlist=function(t){if(this.fireLegacyConversionTags(t),this.options.universalTagPixelId){var e=t.properties(),n=this.options.identifier,r={content_name:t.name(),content_category:t.category(),content_ids:[t[n]()]};r=d(r,l(e)),window.twq("track","AddToWishlist",r)}},f.prototype.checkoutStarted=function(t){var e=s((function(t,e){return t+(p(e,"quantity")||0)}),0,t.products());if(this.fireLegacyConversionTags(t,{quantity:e}),this.options.universalTagPixelId){var n=this.options.identifier,r=s((function(t,e){var r=new u({properties:e}),i=r[n]();return t.ids.push(i),t.names.push(r.name()),t.categories.push(r.category()),t}),{ids:[],names:[],categories:[]},t.products()),i={content_ids:r.ids.sort(),content_name:r.names.sort().join(", "),content_category:r.categories.join(", ")};i=d(i,l(t.properties())),window.twq("track","InitiateCheckout",i)}},f.prototype.paymentInfoEntered=function(t){this.fireLegacyConversionTags(t);var e=d({},l(t.properties()));this.options.universalTagPixelId&&window.twq("track","AddPaymentInfo",e)},f.prototype.fireLegacyConversionTags=function(t,e){var n=this.events(t.event()),r=this;c(n,(function(n){var i={pixelId:n,quantity:t.proxy("properties.quantity")||0,revenue:t.revenue()||0};e&&(i=o(e,i)),r.load("singleTag",i)}))}}});
//# sourceMappingURL=twitter-ads.js.map
};