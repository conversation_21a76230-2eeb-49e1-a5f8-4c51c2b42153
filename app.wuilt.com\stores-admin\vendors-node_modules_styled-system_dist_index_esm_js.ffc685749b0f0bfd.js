(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_styled-system_dist_index_esm_js"], {

        /***/
        "../../node_modules/@styled-system/background/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    background: () => ( /* binding */ background),
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var config = {
                    background: true,
                    backgroundImage: true,
                    backgroundSize: true,
                    backgroundPosition: true,
                    backgroundRepeat: true
                };
                config.bgImage = config.backgroundImage;
                config.bgSize = config.backgroundSize;
                config.bgPosition = config.backgroundPosition;
                config.bgRepeat = config.backgroundRepeat;
                var background = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(config);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (background);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/border/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    border: () => ( /* binding */ border),
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var config = {
                    border: {
                        property: 'border',
                        scale: 'borders'
                    },
                    borderWidth: {
                        property: 'borderWidth',
                        scale: 'borderWidths'
                    },
                    borderStyle: {
                        property: 'borderStyle',
                        scale: 'borderStyles'
                    },
                    borderColor: {
                        property: 'borderColor',
                        scale: 'colors'
                    },
                    borderRadius: {
                        property: 'borderRadius',
                        scale: 'radii'
                    },
                    borderTop: {
                        property: 'borderTop',
                        scale: 'borders'
                    },
                    borderTopLeftRadius: {
                        property: 'borderTopLeftRadius',
                        scale: 'radii'
                    },
                    borderTopRightRadius: {
                        property: 'borderTopRightRadius',
                        scale: 'radii'
                    },
                    borderRight: {
                        property: 'borderRight',
                        scale: 'borders'
                    },
                    borderBottom: {
                        property: 'borderBottom',
                        scale: 'borders'
                    },
                    borderBottomLeftRadius: {
                        property: 'borderBottomLeftRadius',
                        scale: 'radii'
                    },
                    borderBottomRightRadius: {
                        property: 'borderBottomRightRadius',
                        scale: 'radii'
                    },
                    borderLeft: {
                        property: 'borderLeft',
                        scale: 'borders'
                    },
                    borderX: {
                        properties: ['borderLeft', 'borderRight'],
                        scale: 'borders'
                    },
                    borderY: {
                        properties: ['borderTop', 'borderBottom'],
                        scale: 'borders'
                    }
                };
                config.borderTopWidth = {
                    property: 'borderTopWidth',
                    scale: 'borderWidths'
                };
                config.borderTopColor = {
                    property: 'borderTopColor',
                    scale: 'colors'
                };
                config.borderTopStyle = {
                    property: 'borderTopStyle',
                    scale: 'borderStyles'
                };
                config.borderTopLeftRadius = {
                    property: 'borderTopLeftRadius',
                    scale: 'radii'
                };
                config.borderTopRightRadius = {
                    property: 'borderTopRightRadius',
                    scale: 'radii'
                };
                config.borderBottomWidth = {
                    property: 'borderBottomWidth',
                    scale: 'borderWidths'
                };
                config.borderBottomColor = {
                    property: 'borderBottomColor',
                    scale: 'colors'
                };
                config.borderBottomStyle = {
                    property: 'borderBottomStyle',
                    scale: 'borderStyles'
                };
                config.borderBottomLeftRadius = {
                    property: 'borderBottomLeftRadius',
                    scale: 'radii'
                };
                config.borderBottomRightRadius = {
                    property: 'borderBottomRightRadius',
                    scale: 'radii'
                };
                config.borderLeftWidth = {
                    property: 'borderLeftWidth',
                    scale: 'borderWidths'
                };
                config.borderLeftColor = {
                    property: 'borderLeftColor',
                    scale: 'colors'
                };
                config.borderLeftStyle = {
                    property: 'borderLeftStyle',
                    scale: 'borderStyles'
                };
                config.borderRightWidth = {
                    property: 'borderRightWidth',
                    scale: 'borderWidths'
                };
                config.borderRightColor = {
                    property: 'borderRightColor',
                    scale: 'colors'
                };
                config.borderRightStyle = {
                    property: 'borderRightStyle',
                    scale: 'borderStyles'
                };
                var border = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(config);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (border);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/color/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    color: () => ( /* binding */ color),
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var config = {
                    color: {
                        property: 'color',
                        scale: 'colors'
                    },
                    backgroundColor: {
                        property: 'backgroundColor',
                        scale: 'colors'
                    },
                    opacity: true
                };
                config.bg = config.backgroundColor;
                var color = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(config);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (color);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/core/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    compose: () => ( /* binding */ compose),
                    /* harmony export */
                    createParser: () => ( /* binding */ createParser),
                    /* harmony export */
                    createStyleFunction: () => ( /* binding */ createStyleFunction),
                    /* harmony export */
                    get: () => ( /* binding */ get),
                    /* harmony export */
                    merge: () => ( /* binding */ merge),
                    /* harmony export */
                    system: () => ( /* binding */ system)
                    /* harmony export */
                });
                /* harmony import */
                var object_assign__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/object-assign/index.js");
                /* harmony import */
                var object_assign__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/ __webpack_require__.n(object_assign__WEBPACK_IMPORTED_MODULE_0__);

                var merge = function merge(a, b) {
                    var result = object_assign__WEBPACK_IMPORTED_MODULE_0___default()({}, a, b);

                    for (var key in a) {
                        var _assign;

                        if (!a[key] || typeof b[key] !== 'object') continue;
                        object_assign__WEBPACK_IMPORTED_MODULE_0___default()(result, (_assign = {}, _assign[key] = object_assign__WEBPACK_IMPORTED_MODULE_0___default()(a[key], b[key]), _assign));
                    }

                    return result;
                }; // sort object-value responsive styles

                var sort = function sort(obj) {
                    var next = {};
                    Object.keys(obj).sort(function(a, b) {
                        return a.localeCompare(b, undefined, {
                            numeric: true,
                            sensitivity: 'base'
                        });
                    }).forEach(function(key) {
                        next[key] = obj[key];
                    });
                    return next;
                };

                var defaults = {
                    breakpoints: [40, 52, 64].map(function(n) {
                        return n + 'em';
                    })
                };

                var createMediaQuery = function createMediaQuery(n) {
                    return "@media screen and (min-width: " + n + ")";
                };

                var getValue = function getValue(n, scale) {
                    return get(scale, n, n);
                };

                var get = function get(obj, key, def, p, undef) {
                    key = key && key.split ? key.split('.') : [key];

                    for (p = 0; p < key.length; p++) {
                        obj = obj ? obj[key[p]] : undef;
                    }

                    return obj === undef ? def : obj;
                };
                var createParser = function createParser(config) {
                    var cache = {};

                    var parse = function parse(props) {
                        var styles = {};
                        var shouldSort = false;
                        var isCacheDisabled = props.theme && props.theme.disableStyledSystemCache;

                        for (var key in props) {
                            if (!config[key]) continue;
                            var sx = config[key];
                            var raw = props[key];
                            var scale = get(props.theme, sx.scale, sx.defaults);

                            if (typeof raw === 'object') {
                                cache.breakpoints = !isCacheDisabled && cache.breakpoints || get(props.theme, 'breakpoints', defaults.breakpoints);

                                if (Array.isArray(raw)) {
                                    cache.media = !isCacheDisabled && cache.media || [null].concat(cache.breakpoints.map(createMediaQuery));
                                    styles = merge(styles, parseResponsiveStyle(cache.media, sx, scale, raw, props));
                                    continue;
                                }

                                if (raw !== null) {
                                    styles = merge(styles, parseResponsiveObject(cache.breakpoints, sx, scale, raw, props));
                                    shouldSort = true;
                                }

                                continue;
                            }

                            object_assign__WEBPACK_IMPORTED_MODULE_0___default()(styles, sx(raw, scale, props));
                        } // sort object-based responsive styles


                        if (shouldSort) {
                            styles = sort(styles);
                        }

                        return styles;
                    };

                    parse.config = config;
                    parse.propNames = Object.keys(config);
                    parse.cache = cache;
                    var keys = Object.keys(config).filter(function(k) {
                        return k !== 'config';
                    });

                    if (keys.length > 1) {
                        keys.forEach(function(key) {
                            var _createParser;

                            parse[key] = createParser((_createParser = {}, _createParser[key] = config[key], _createParser));
                        });
                    }

                    return parse;
                };

                var parseResponsiveStyle = function parseResponsiveStyle(mediaQueries, sx, scale, raw, _props) {
                    var styles = {};
                    raw.slice(0, mediaQueries.length).forEach(function(value, i) {
                        var media = mediaQueries[i];
                        var style = sx(value, scale, _props);

                        if (!media) {
                            object_assign__WEBPACK_IMPORTED_MODULE_0___default()(styles, style);
                        } else {
                            var _assign2;

                            object_assign__WEBPACK_IMPORTED_MODULE_0___default()(styles, (_assign2 = {}, _assign2[media] = object_assign__WEBPACK_IMPORTED_MODULE_0___default()({}, styles[media], style), _assign2));
                        }
                    });
                    return styles;
                };

                var parseResponsiveObject = function parseResponsiveObject(breakpoints, sx, scale, raw, _props) {
                    var styles = {};

                    for (var key in raw) {
                        var breakpoint = breakpoints[key];
                        var value = raw[key];
                        var style = sx(value, scale, _props);

                        if (!breakpoint) {
                            object_assign__WEBPACK_IMPORTED_MODULE_0___default()(styles, style);
                        } else {
                            var _assign3;

                            var media = createMediaQuery(breakpoint);
                            object_assign__WEBPACK_IMPORTED_MODULE_0___default()(styles, (_assign3 = {}, _assign3[media] = object_assign__WEBPACK_IMPORTED_MODULE_0___default()({}, styles[media], style), _assign3));
                        }
                    }

                    return styles;
                };

                var createStyleFunction = function createStyleFunction(_ref) {
                    var properties = _ref.properties,
                        property = _ref.property,
                        scale = _ref.scale,
                        _ref$transform = _ref.transform,
                        transform = _ref$transform === void 0 ? getValue : _ref$transform,
                        defaultScale = _ref.defaultScale;
                    properties = properties || [property];

                    var sx = function sx(value, scale, _props) {
                        var result = {};
                        var n = transform(value, scale, _props);
                        if (n === null) return;
                        properties.forEach(function(prop) {
                            result[prop] = n;
                        });
                        return result;
                    };

                    sx.scale = scale;
                    sx.defaults = defaultScale;
                    return sx;
                }; // new v5 API

                var system = function system(args) {
                    if (args === void 0) {
                        args = {};
                    }

                    var config = {};
                    Object.keys(args).forEach(function(key) {
                        var conf = args[key];

                        if (conf === true) {
                            // shortcut definition
                            config[key] = createStyleFunction({
                                property: key,
                                scale: key
                            });
                            return;
                        }

                        if (typeof conf === 'function') {
                            config[key] = conf;
                            return;
                        }

                        config[key] = createStyleFunction(conf);
                    });
                    var parser = createParser(config);
                    return parser;
                };
                var compose = function compose() {
                    var config = {};

                    for (var _len = arguments.length, parsers = new Array(_len), _key = 0; _key < _len; _key++) {
                        parsers[_key] = arguments[_key];
                    }

                    parsers.forEach(function(parser) {
                        if (!parser || !parser.config) return;
                        object_assign__WEBPACK_IMPORTED_MODULE_0___default()(config, parser.config);
                    });
                    var parser = createParser(config);
                    return parser;
                };


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/css/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    css: () => ( /* binding */ css),
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    get: () => ( /* binding */ get),
                    /* harmony export */
                    responsive: () => ( /* binding */ responsive)
                    /* harmony export */
                });

                function _extends() {
                    _extends = Object.assign || function(target) {
                        for (var i = 1; i < arguments.length; i++) {
                            var source = arguments[i];
                            for (var key in source) {
                                if (Object.prototype.hasOwnProperty.call(source, key)) {
                                    target[key] = source[key];
                                }
                            }
                        }
                        return target;
                    };
                    return _extends.apply(this, arguments);
                }

                // based on https://github.com/developit/dlv
                var get = function get(obj, key, def, p, undef) {
                    key = key && key.split ? key.split('.') : [key];

                    for (p = 0; p < key.length; p++) {
                        obj = obj ? obj[key[p]] : undef;
                    }

                    return obj === undef ? def : obj;
                };
                var defaultBreakpoints = [40, 52, 64].map(function(n) {
                    return n + 'em';
                });
                var defaultTheme = {
                    space: [0, 4, 8, 16, 32, 64, 128, 256, 512],
                    fontSizes: [12, 14, 16, 20, 24, 32, 48, 64, 72]
                };
                var aliases = {
                    bg: 'backgroundColor',
                    m: 'margin',
                    mt: 'marginTop',
                    mr: 'marginRight',
                    mb: 'marginBottom',
                    ml: 'marginLeft',
                    mx: 'marginX',
                    my: 'marginY',
                    p: 'padding',
                    pt: 'paddingTop',
                    pr: 'paddingRight',
                    pb: 'paddingBottom',
                    pl: 'paddingLeft',
                    px: 'paddingX',
                    py: 'paddingY'
                };
                var multiples = {
                    marginX: ['marginLeft', 'marginRight'],
                    marginY: ['marginTop', 'marginBottom'],
                    paddingX: ['paddingLeft', 'paddingRight'],
                    paddingY: ['paddingTop', 'paddingBottom'],
                    size: ['width', 'height']
                };
                var scales = {
                    color: 'colors',
                    backgroundColor: 'colors',
                    borderColor: 'colors',
                    margin: 'space',
                    marginTop: 'space',
                    marginRight: 'space',
                    marginBottom: 'space',
                    marginLeft: 'space',
                    marginX: 'space',
                    marginY: 'space',
                    padding: 'space',
                    paddingTop: 'space',
                    paddingRight: 'space',
                    paddingBottom: 'space',
                    paddingLeft: 'space',
                    paddingX: 'space',
                    paddingY: 'space',
                    top: 'space',
                    right: 'space',
                    bottom: 'space',
                    left: 'space',
                    gridGap: 'space',
                    gridColumnGap: 'space',
                    gridRowGap: 'space',
                    gap: 'space',
                    columnGap: 'space',
                    rowGap: 'space',
                    fontFamily: 'fonts',
                    fontSize: 'fontSizes',
                    fontWeight: 'fontWeights',
                    lineHeight: 'lineHeights',
                    letterSpacing: 'letterSpacings',
                    border: 'borders',
                    borderTop: 'borders',
                    borderRight: 'borders',
                    borderBottom: 'borders',
                    borderLeft: 'borders',
                    borderWidth: 'borderWidths',
                    borderStyle: 'borderStyles',
                    borderRadius: 'radii',
                    borderTopRightRadius: 'radii',
                    borderTopLeftRadius: 'radii',
                    borderBottomRightRadius: 'radii',
                    borderBottomLeftRadius: 'radii',
                    borderTopWidth: 'borderWidths',
                    borderTopColor: 'colors',
                    borderTopStyle: 'borderStyles',
                    borderBottomWidth: 'borderWidths',
                    borderBottomColor: 'colors',
                    borderBottomStyle: 'borderStyles',
                    borderLeftWidth: 'borderWidths',
                    borderLeftColor: 'colors',
                    borderLeftStyle: 'borderStyles',
                    borderRightWidth: 'borderWidths',
                    borderRightColor: 'colors',
                    borderRightStyle: 'borderStyles',
                    outlineColor: 'colors',
                    boxShadow: 'shadows',
                    textShadow: 'shadows',
                    zIndex: 'zIndices',
                    width: 'sizes',
                    minWidth: 'sizes',
                    maxWidth: 'sizes',
                    height: 'sizes',
                    minHeight: 'sizes',
                    maxHeight: 'sizes',
                    flexBasis: 'sizes',
                    size: 'sizes',
                    // svg
                    fill: 'colors',
                    stroke: 'colors'
                };

                var positiveOrNegative = function positiveOrNegative(scale, value) {
                    if (typeof value !== 'number' || value >= 0) {
                        return get(scale, value, value);
                    }

                    var absolute = Math.abs(value);
                    var n = get(scale, absolute, absolute);
                    if (typeof n === 'string') return '-' + n;
                    return n * -1;
                };

                var transforms = ['margin', 'marginTop', 'marginRight', 'marginBottom', 'marginLeft', 'marginX', 'marginY', 'top', 'bottom', 'left', 'right'].reduce(function(acc, curr) {
                    var _extends2;

                    return _extends({}, acc, (_extends2 = {}, _extends2[curr] = positiveOrNegative, _extends2));
                }, {});
                var responsive = function responsive(styles) {
                    return function(theme) {
                        var next = {};
                        var breakpoints = get(theme, 'breakpoints', defaultBreakpoints);
                        var mediaQueries = [null].concat(breakpoints.map(function(n) {
                            return "@media screen and (min-width: " + n + ")";
                        }));

                        for (var key in styles) {
                            var value = typeof styles[key] === 'function' ? styles[key](theme) : styles[key];
                            if (value == null) continue;

                            if (!Array.isArray(value)) {
                                next[key] = value;
                                continue;
                            }

                            for (var i = 0; i < value.slice(0, mediaQueries.length).length; i++) {
                                var media = mediaQueries[i];

                                if (!media) {
                                    next[key] = value[i];
                                    continue;
                                }

                                next[media] = next[media] || {};
                                if (value[i] == null) continue;
                                next[media][key] = value[i];
                            }
                        }

                        return next;
                    };
                };
                var css = function css(args) {
                    return function(props) {
                        if (props === void 0) {
                            props = {};
                        }

                        var theme = _extends({}, defaultTheme, {}, props.theme || props);

                        var result = {};
                        var obj = typeof args === 'function' ? args(theme) : args;
                        var styles = responsive(obj)(theme);

                        for (var key in styles) {
                            var x = styles[key];
                            var val = typeof x === 'function' ? x(theme) : x;

                            if (key === 'variant') {
                                var variant = css(get(theme, val))(theme);
                                result = _extends({}, result, {}, variant);
                                continue;
                            }

                            if (val && typeof val === 'object') {
                                result[key] = css(val)(theme);
                                continue;
                            }

                            var prop = get(aliases, key, key);
                            var scaleName = get(scales, prop);
                            var scale = get(theme, scaleName, get(theme, prop, {}));
                            var transform = get(transforms, prop, get);
                            var value = transform(scale, val, val);

                            if (multiples[prop]) {
                                var dirs = multiples[prop];

                                for (var i = 0; i < dirs.length; i++) {
                                    result[dirs[i]] = value;
                                }
                            } else {
                                result[prop] = value;
                            }
                        }

                        return result;
                    };
                };
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (css);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/flexbox/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    flexbox: () => ( /* binding */ flexbox)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var config = {
                    alignItems: true,
                    alignContent: true,
                    justifyItems: true,
                    justifyContent: true,
                    flexWrap: true,
                    flexDirection: true,
                    // item
                    flex: true,
                    flexGrow: true,
                    flexShrink: true,
                    flexBasis: true,
                    justifySelf: true,
                    alignSelf: true,
                    order: true
                };
                var flexbox = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(config);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (flexbox);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/grid/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    grid: () => ( /* binding */ grid)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var defaults = {
                    space: [0, 4, 8, 16, 32, 64, 128, 256, 512]
                };
                var config = {
                    gridGap: {
                        property: 'gridGap',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    gridColumnGap: {
                        property: 'gridColumnGap',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    gridRowGap: {
                        property: 'gridRowGap',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    gridColumn: true,
                    gridRow: true,
                    gridAutoFlow: true,
                    gridAutoColumns: true,
                    gridAutoRows: true,
                    gridTemplateColumns: true,
                    gridTemplateRows: true,
                    gridTemplateAreas: true,
                    gridArea: true
                };
                var grid = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(config);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (grid);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/layout/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    layout: () => ( /* binding */ layout)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");


                var isNumber = function isNumber(n) {
                    return typeof n === 'number' && !isNaN(n);
                };

                var getWidth = function getWidth(n, scale) {
                    return (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.get)(scale, n, !isNumber(n) || n > 1 ? n : n * 100 + '%');
                };

                var config = {
                    width: {
                        property: 'width',
                        scale: 'sizes',
                        transform: getWidth
                    },
                    height: {
                        property: 'height',
                        scale: 'sizes'
                    },
                    minWidth: {
                        property: 'minWidth',
                        scale: 'sizes'
                    },
                    minHeight: {
                        property: 'minHeight',
                        scale: 'sizes'
                    },
                    maxWidth: {
                        property: 'maxWidth',
                        scale: 'sizes'
                    },
                    maxHeight: {
                        property: 'maxHeight',
                        scale: 'sizes'
                    },
                    size: {
                        properties: ['width', 'height'],
                        scale: 'sizes'
                    },
                    overflow: true,
                    overflowX: true,
                    overflowY: true,
                    display: true,
                    verticalAlign: true
                };
                var layout = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(config);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (layout);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/position/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    position: () => ( /* binding */ position)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var defaults = {
                    space: [0, 4, 8, 16, 32, 64, 128, 256, 512]
                };
                var config = {
                    position: true,
                    zIndex: {
                        property: 'zIndex',
                        scale: 'zIndices'
                    },
                    top: {
                        property: 'top',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    right: {
                        property: 'right',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    bottom: {
                        property: 'bottom',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    left: {
                        property: 'left',
                        scale: 'space',
                        defaultScale: defaults.space
                    }
                };
                var position = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(config);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (position);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/shadow/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    shadow: () => ( /* binding */ shadow)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var shadow = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)({
                    boxShadow: {
                        property: 'boxShadow',
                        scale: 'shadows'
                    },
                    textShadow: {
                        property: 'textShadow',
                        scale: 'shadows'
                    }
                });
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (shadow);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/space/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    margin: () => ( /* binding */ margin),
                    /* harmony export */
                    padding: () => ( /* binding */ padding),
                    /* harmony export */
                    space: () => ( /* binding */ space)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var defaults = {
                    space: [0, 4, 8, 16, 32, 64, 128, 256, 512]
                };

                var isNumber = function isNumber(n) {
                    return typeof n === 'number' && !isNaN(n);
                };

                var getMargin = function getMargin(n, scale) {
                    if (!isNumber(n)) {
                        return (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.get)(scale, n, n);
                    }

                    var isNegative = n < 0;
                    var absolute = Math.abs(n);
                    var value = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.get)(scale, absolute, absolute);

                    if (!isNumber(value)) {
                        return isNegative ? '-' + value : value;
                    }

                    return value * (isNegative ? -1 : 1);
                };

                var configs = {};
                configs.margin = {
                    margin: {
                        property: 'margin',
                        scale: 'space',
                        transform: getMargin,
                        defaultScale: defaults.space
                    },
                    marginTop: {
                        property: 'marginTop',
                        scale: 'space',
                        transform: getMargin,
                        defaultScale: defaults.space
                    },
                    marginRight: {
                        property: 'marginRight',
                        scale: 'space',
                        transform: getMargin,
                        defaultScale: defaults.space
                    },
                    marginBottom: {
                        property: 'marginBottom',
                        scale: 'space',
                        transform: getMargin,
                        defaultScale: defaults.space
                    },
                    marginLeft: {
                        property: 'marginLeft',
                        scale: 'space',
                        transform: getMargin,
                        defaultScale: defaults.space
                    },
                    marginX: {
                        properties: ['marginLeft', 'marginRight'],
                        scale: 'space',
                        transform: getMargin,
                        defaultScale: defaults.space
                    },
                    marginY: {
                        properties: ['marginTop', 'marginBottom'],
                        scale: 'space',
                        transform: getMargin,
                        defaultScale: defaults.space
                    }
                };
                configs.margin.m = configs.margin.margin;
                configs.margin.mt = configs.margin.marginTop;
                configs.margin.mr = configs.margin.marginRight;
                configs.margin.mb = configs.margin.marginBottom;
                configs.margin.ml = configs.margin.marginLeft;
                configs.margin.mx = configs.margin.marginX;
                configs.margin.my = configs.margin.marginY;
                configs.padding = {
                    padding: {
                        property: 'padding',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    paddingTop: {
                        property: 'paddingTop',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    paddingRight: {
                        property: 'paddingRight',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    paddingBottom: {
                        property: 'paddingBottom',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    paddingLeft: {
                        property: 'paddingLeft',
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    paddingX: {
                        properties: ['paddingLeft', 'paddingRight'],
                        scale: 'space',
                        defaultScale: defaults.space
                    },
                    paddingY: {
                        properties: ['paddingTop', 'paddingBottom'],
                        scale: 'space',
                        defaultScale: defaults.space
                    }
                };
                configs.padding.p = configs.padding.padding;
                configs.padding.pt = configs.padding.paddingTop;
                configs.padding.pr = configs.padding.paddingRight;
                configs.padding.pb = configs.padding.paddingBottom;
                configs.padding.pl = configs.padding.paddingLeft;
                configs.padding.px = configs.padding.paddingX;
                configs.padding.py = configs.padding.paddingY;
                var margin = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(configs.margin);
                var padding = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(configs.padding);
                var space = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.compose)(margin, padding);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (space);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/typography/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    typography: () => ( /* binding */ typography)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");

                var defaults = {
                    fontSizes: [12, 14, 16, 20, 24, 32, 48, 64, 72]
                };
                var config = {
                    fontFamily: {
                        property: 'fontFamily',
                        scale: 'fonts'
                    },
                    fontSize: {
                        property: 'fontSize',
                        scale: 'fontSizes',
                        defaultScale: defaults.fontSizes
                    },
                    fontWeight: {
                        property: 'fontWeight',
                        scale: 'fontWeights'
                    },
                    lineHeight: {
                        property: 'lineHeight',
                        scale: 'lineHeights'
                    },
                    letterSpacing: {
                        property: 'letterSpacing',
                        scale: 'letterSpacings'
                    },
                    textAlign: true,
                    fontStyle: true
                };
                var typography = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system)(config);
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (typography);


                /***/
            }),

        /***/
        "../../node_modules/@styled-system/variant/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    buttonStyle: () => ( /* binding */ buttonStyle),
                    /* harmony export */
                    colorStyle: () => ( /* binding */ colorStyle),
                    /* harmony export */
                    "default": () => (__WEBPACK_DEFAULT_EXPORT__),
                    /* harmony export */
                    textStyle: () => ( /* binding */ textStyle),
                    /* harmony export */
                    variant: () => ( /* binding */ variant)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");
                /* harmony import */
                var _styled_system_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@styled-system/css/dist/index.esm.js");


                var variant = function variant(_ref) {
                    var _config;

                    var scale = _ref.scale,
                        _ref$prop = _ref.prop,
                        prop = _ref$prop === void 0 ? 'variant' : _ref$prop,
                        _ref$variants = _ref.variants,
                        variants = _ref$variants === void 0 ? {} : _ref$variants,
                        key = _ref.key;
                    var sx;

                    if (Object.keys(variants).length) {
                        sx = function sx(value, scale, props) {
                            return (0, _styled_system_css__WEBPACK_IMPORTED_MODULE_1__["default"])((0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.get)(scale, value, null))(props.theme);
                        };
                    } else {
                        sx = function sx(value, scale) {
                            return (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.get)(scale, value, null);
                        };
                    }

                    sx.scale = scale || key;
                    sx.defaults = variants;
                    var config = (_config = {}, _config[prop] = sx, _config);
                    var parser = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.createParser)(config);
                    return parser;
                };
                /* harmony default export */
                const __WEBPACK_DEFAULT_EXPORT__ = (variant);
                var buttonStyle = variant({
                    key: 'buttons'
                });
                var textStyle = variant({
                    key: 'textStyles',
                    prop: 'textStyle'
                });
                var colorStyle = variant({
                    key: 'colorStyles',
                    prop: 'colors'
                });


                /***/
            }),

        /***/
        "../../node_modules/object-assign/index.js":
            /***/
            ((module) => {

                /*
                object-assign
                (c) Sindre Sorhus
                @license MIT
                */


                /* eslint-disable no-unused-vars */
                var getOwnPropertySymbols = Object.getOwnPropertySymbols;
                var hasOwnProperty = Object.prototype.hasOwnProperty;
                var propIsEnumerable = Object.prototype.propertyIsEnumerable;

                function toObject(val) {
                    if (val === null || val === undefined) {
                        throw new TypeError('Object.assign cannot be called with null or undefined');
                    }

                    return Object(val);
                }

                function shouldUseNative() {
                    try {
                        if (!Object.assign) {
                            return false;
                        }

                        // Detect buggy property enumeration order in older V8 versions.

                        // https://bugs.chromium.org/p/v8/issues/detail?id=4118
                        var test1 = new String('abc'); // eslint-disable-line no-new-wrappers
                        test1[5] = 'de';
                        if (Object.getOwnPropertyNames(test1)[0] === '5') {
                            return false;
                        }

                        // https://bugs.chromium.org/p/v8/issues/detail?id=3056
                        var test2 = {};
                        for (var i = 0; i < 10; i++) {
                            test2['_' + String.fromCharCode(i)] = i;
                        }
                        var order2 = Object.getOwnPropertyNames(test2).map(function(n) {
                            return test2[n];
                        });
                        if (order2.join('') !== '0123456789') {
                            return false;
                        }

                        // https://bugs.chromium.org/p/v8/issues/detail?id=3056
                        var test3 = {};
                        'abcdefghijklmnopqrst'.split('').forEach(function(letter) {
                            test3[letter] = letter;
                        });
                        if (Object.keys(Object.assign({}, test3)).join('') !==
                            'abcdefghijklmnopqrst') {
                            return false;
                        }

                        return true;
                    } catch (err) {
                        // We don't expect any of the above to throw, but better to be safe.
                        return false;
                    }
                }

                module.exports = shouldUseNative() ? Object.assign : function(target, source) {
                    var from;
                    var to = toObject(target);
                    var symbols;

                    for (var s = 1; s < arguments.length; s++) {
                        from = Object(arguments[s]);

                        for (var key in from) {
                            if (hasOwnProperty.call(from, key)) {
                                to[key] = from[key];
                            }
                        }

                        if (getOwnPropertySymbols) {
                            symbols = getOwnPropertySymbols(from);
                            for (var i = 0; i < symbols.length; i++) {
                                if (propIsEnumerable.call(from, symbols[i])) {
                                    to[symbols[i]] = from[symbols[i]];
                                }
                            }
                        }
                    }

                    return to;
                };


                /***/
            }),

        /***/
        "../../node_modules/styled-system/dist/index.esm.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    alignContent: () => ( /* binding */ alignContent),
                    /* harmony export */
                    alignItems: () => ( /* binding */ alignItems),
                    /* harmony export */
                    alignSelf: () => ( /* binding */ alignSelf),
                    /* harmony export */
                    background: () => ( /* reexport safe */ _styled_system_background__WEBPACK_IMPORTED_MODULE_7__.background),
                    /* harmony export */
                    backgroundImage: () => ( /* binding */ backgroundImage),
                    /* harmony export */
                    backgroundPosition: () => ( /* binding */ backgroundPosition),
                    /* harmony export */
                    backgroundRepeat: () => ( /* binding */ backgroundRepeat),
                    /* harmony export */
                    backgroundSize: () => ( /* binding */ backgroundSize),
                    /* harmony export */
                    border: () => ( /* reexport safe */ _styled_system_border__WEBPACK_IMPORTED_MODULE_6__.border),
                    /* harmony export */
                    borderBottom: () => ( /* binding */ borderBottom),
                    /* harmony export */
                    borderColor: () => ( /* binding */ borderColor),
                    /* harmony export */
                    borderLeft: () => ( /* binding */ borderLeft),
                    /* harmony export */
                    borderRadius: () => ( /* binding */ borderRadius),
                    /* harmony export */
                    borderRight: () => ( /* binding */ borderRight),
                    /* harmony export */
                    borderStyle: () => ( /* binding */ borderStyle),
                    /* harmony export */
                    borderTop: () => ( /* binding */ borderTop),
                    /* harmony export */
                    borderWidth: () => ( /* binding */ borderWidth),
                    /* harmony export */
                    borders: () => ( /* reexport safe */ _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"]),
                    /* harmony export */
                    bottom: () => ( /* binding */ bottom),
                    /* harmony export */
                    boxShadow: () => ( /* reexport safe */ _styled_system_shadow__WEBPACK_IMPORTED_MODULE_10__["default"]),
                    /* harmony export */
                    buttonStyle: () => ( /* reexport safe */ _styled_system_variant__WEBPACK_IMPORTED_MODULE_11__.buttonStyle),
                    /* harmony export */
                    color: () => ( /* reexport safe */ _styled_system_color__WEBPACK_IMPORTED_MODULE_2__.color),
                    /* harmony export */
                    colorStyle: () => ( /* reexport safe */ _styled_system_variant__WEBPACK_IMPORTED_MODULE_11__.colorStyle),
                    /* harmony export */
                    compose: () => ( /* reexport safe */ _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.compose),
                    /* harmony export */
                    createParser: () => ( /* reexport safe */ _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.createParser),
                    /* harmony export */
                    createStyleFunction: () => ( /* reexport safe */ _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.createStyleFunction),
                    /* harmony export */
                    display: () => ( /* binding */ display),
                    /* harmony export */
                    flex: () => ( /* binding */ flex),
                    /* harmony export */
                    flexBasis: () => ( /* binding */ flexBasis),
                    /* harmony export */
                    flexDirection: () => ( /* binding */ flexDirection),
                    /* harmony export */
                    flexGrow: () => ( /* binding */ flexGrow),
                    /* harmony export */
                    flexShrink: () => ( /* binding */ flexShrink),
                    /* harmony export */
                    flexWrap: () => ( /* binding */ flexWrap),
                    /* harmony export */
                    flexbox: () => ( /* reexport safe */ _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__.flexbox),
                    /* harmony export */
                    fontFamily: () => ( /* binding */ fontFamily),
                    /* harmony export */
                    fontSize: () => ( /* binding */ fontSize),
                    /* harmony export */
                    fontStyle: () => ( /* binding */ fontStyle),
                    /* harmony export */
                    fontWeight: () => ( /* binding */ fontWeight),
                    /* harmony export */
                    get: () => ( /* reexport safe */ _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.get),
                    /* harmony export */
                    grid: () => ( /* reexport safe */ _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__.grid),
                    /* harmony export */
                    gridArea: () => ( /* binding */ gridArea),
                    /* harmony export */
                    gridAutoColumns: () => ( /* binding */ gridAutoColumns),
                    /* harmony export */
                    gridAutoFlow: () => ( /* binding */ gridAutoFlow),
                    /* harmony export */
                    gridAutoRows: () => ( /* binding */ gridAutoRows),
                    /* harmony export */
                    gridColumn: () => ( /* binding */ gridColumn),
                    /* harmony export */
                    gridColumnGap: () => ( /* binding */ gridColumnGap),
                    /* harmony export */
                    gridGap: () => ( /* binding */ gridGap),
                    /* harmony export */
                    gridRow: () => ( /* binding */ gridRow),
                    /* harmony export */
                    gridRowGap: () => ( /* binding */ gridRowGap),
                    /* harmony export */
                    gridTemplateAreas: () => ( /* binding */ gridTemplateAreas),
                    /* harmony export */
                    gridTemplateColumns: () => ( /* binding */ gridTemplateColumns),
                    /* harmony export */
                    gridTemplateRows: () => ( /* binding */ gridTemplateRows),
                    /* harmony export */
                    height: () => ( /* binding */ height),
                    /* harmony export */
                    justifyContent: () => ( /* binding */ justifyContent),
                    /* harmony export */
                    justifyItems: () => ( /* binding */ justifyItems),
                    /* harmony export */
                    justifySelf: () => ( /* binding */ justifySelf),
                    /* harmony export */
                    layout: () => ( /* reexport safe */ _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__.layout),
                    /* harmony export */
                    left: () => ( /* binding */ left),
                    /* harmony export */
                    letterSpacing: () => ( /* binding */ letterSpacing),
                    /* harmony export */
                    lineHeight: () => ( /* binding */ lineHeight),
                    /* harmony export */
                    margin: () => ( /* reexport safe */ _styled_system_space__WEBPACK_IMPORTED_MODULE_9__.margin),
                    /* harmony export */
                    maxHeight: () => ( /* binding */ maxHeight),
                    /* harmony export */
                    maxWidth: () => ( /* binding */ maxWidth),
                    /* harmony export */
                    minHeight: () => ( /* binding */ minHeight),
                    /* harmony export */
                    minWidth: () => ( /* binding */ minWidth),
                    /* harmony export */
                    opacity: () => ( /* binding */ opacity),
                    /* harmony export */
                    order: () => ( /* binding */ order),
                    /* harmony export */
                    overflow: () => ( /* binding */ overflow),
                    /* harmony export */
                    overflowX: () => ( /* binding */ overflowX),
                    /* harmony export */
                    overflowY: () => ( /* binding */ overflowY),
                    /* harmony export */
                    padding: () => ( /* reexport safe */ _styled_system_space__WEBPACK_IMPORTED_MODULE_9__.padding),
                    /* harmony export */
                    position: () => ( /* reexport safe */ _styled_system_position__WEBPACK_IMPORTED_MODULE_8__.position),
                    /* harmony export */
                    right: () => ( /* binding */ right),
                    /* harmony export */
                    shadow: () => ( /* reexport safe */ _styled_system_shadow__WEBPACK_IMPORTED_MODULE_10__.shadow),
                    /* harmony export */
                    size: () => ( /* binding */ size),
                    /* harmony export */
                    space: () => ( /* reexport safe */ _styled_system_space__WEBPACK_IMPORTED_MODULE_9__.space),
                    /* harmony export */
                    style: () => ( /* binding */ style),
                    /* harmony export */
                    system: () => ( /* reexport safe */ _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.system),
                    /* harmony export */
                    textAlign: () => ( /* binding */ textAlign),
                    /* harmony export */
                    textShadow: () => ( /* reexport safe */ _styled_system_shadow__WEBPACK_IMPORTED_MODULE_10__["default"]),
                    /* harmony export */
                    textStyle: () => ( /* reexport safe */ _styled_system_variant__WEBPACK_IMPORTED_MODULE_11__.textStyle),
                    /* harmony export */
                    top: () => ( /* binding */ top),
                    /* harmony export */
                    typography: () => ( /* reexport safe */ _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__.typography),
                    /* harmony export */
                    variant: () => ( /* reexport safe */ _styled_system_variant__WEBPACK_IMPORTED_MODULE_11__.variant),
                    /* harmony export */
                    verticalAlign: () => ( /* binding */ verticalAlign),
                    /* harmony export */
                    width: () => ( /* binding */ width),
                    /* harmony export */
                    zIndex: () => ( /* binding */ zIndex)
                    /* harmony export */
                });
                /* harmony import */
                var _styled_system_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@styled-system/core/dist/index.esm.js");
                /* harmony import */
                var _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@styled-system/layout/dist/index.esm.js");
                /* harmony import */
                var _styled_system_color__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@styled-system/color/dist/index.esm.js");
                /* harmony import */
                var _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@styled-system/typography/dist/index.esm.js");
                /* harmony import */
                var _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/@styled-system/flexbox/dist/index.esm.js");
                /* harmony import */
                var _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/@styled-system/grid/dist/index.esm.js");
                /* harmony import */
                var _styled_system_border__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__("../../node_modules/@styled-system/border/dist/index.esm.js");
                /* harmony import */
                var _styled_system_background__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__("../../node_modules/@styled-system/background/dist/index.esm.js");
                /* harmony import */
                var _styled_system_position__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__("../../node_modules/@styled-system/position/dist/index.esm.js");
                /* harmony import */
                var _styled_system_space__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__("../../node_modules/@styled-system/space/dist/index.esm.js");
                /* harmony import */
                var _styled_system_shadow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__("../../node_modules/@styled-system/shadow/dist/index.esm.js");
                /* harmony import */
                var _styled_system_variant__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__("../../node_modules/@styled-system/variant/dist/index.esm.js");
                // v4 api shims






















                var width = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].width,
                    height = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].height,
                    minWidth = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].minWidth,
                    minHeight = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].minHeight,
                    maxWidth = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].maxWidth,
                    maxHeight = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].maxHeight,
                    size = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].size,
                    verticalAlign = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].verticalAlign,
                    display = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].display,
                    overflow = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].overflow,
                    overflowX = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].overflowX,
                    overflowY = _styled_system_layout__WEBPACK_IMPORTED_MODULE_1__["default"].overflowY;
                var opacity = _styled_system_color__WEBPACK_IMPORTED_MODULE_2__["default"].opacity;
                var fontSize = _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__["default"].fontSize,
                    fontFamily = _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__["default"].fontFamily,
                    fontWeight = _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__["default"].fontWeight,
                    lineHeight = _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__["default"].lineHeight,
                    textAlign = _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__["default"].textAlign,
                    fontStyle = _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__["default"].fontStyle,
                    letterSpacing = _styled_system_typography__WEBPACK_IMPORTED_MODULE_3__["default"].letterSpacing;
                var alignItems = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].alignItems,
                    alignContent = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].alignContent,
                    justifyItems = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].justifyItems,
                    justifyContent = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].justifyContent,
                    flexWrap = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].flexWrap,
                    flexDirection = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].flexDirection,
                    flex = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].flex,
                    flexGrow = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].flexGrow,
                    flexShrink = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].flexShrink,
                    flexBasis = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].flexBasis,
                    justifySelf = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].justifySelf,
                    alignSelf = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].alignSelf,
                    order = _styled_system_flexbox__WEBPACK_IMPORTED_MODULE_4__["default"].order;
                var gridGap = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridGap,
                    gridColumnGap = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridColumnGap,
                    gridRowGap = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridRowGap,
                    gridColumn = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridColumn,
                    gridRow = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridRow,
                    gridAutoFlow = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridAutoFlow,
                    gridAutoColumns = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridAutoColumns,
                    gridAutoRows = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridAutoRows,
                    gridTemplateColumns = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridTemplateColumns,
                    gridTemplateRows = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridTemplateRows,
                    gridTemplateAreas = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridTemplateAreas,
                    gridArea = _styled_system_grid__WEBPACK_IMPORTED_MODULE_5__["default"].gridArea;
                var borderWidth = _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"].borderWidth,
                    borderStyle = _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"].borderStyle,
                    borderColor = _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"].borderColor,
                    borderTop = _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"].borderTop,
                    borderRight = _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"].borderRight,
                    borderBottom = _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"].borderBottom,
                    borderLeft = _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"].borderLeft,
                    borderRadius = _styled_system_border__WEBPACK_IMPORTED_MODULE_6__["default"].borderRadius;
                var backgroundImage = _styled_system_background__WEBPACK_IMPORTED_MODULE_7__["default"].backgroundImage,
                    backgroundSize = _styled_system_background__WEBPACK_IMPORTED_MODULE_7__["default"].backgroundSize,
                    backgroundPosition = _styled_system_background__WEBPACK_IMPORTED_MODULE_7__["default"].backgroundPosition,
                    backgroundRepeat = _styled_system_background__WEBPACK_IMPORTED_MODULE_7__["default"].backgroundRepeat;
                var zIndex = _styled_system_position__WEBPACK_IMPORTED_MODULE_8__["default"].zIndex,
                    top = _styled_system_position__WEBPACK_IMPORTED_MODULE_8__["default"].top,
                    right = _styled_system_position__WEBPACK_IMPORTED_MODULE_8__["default"].right,
                    bottom = _styled_system_position__WEBPACK_IMPORTED_MODULE_8__["default"].bottom,
                    left = _styled_system_position__WEBPACK_IMPORTED_MODULE_8__["default"].left;

                // v4 style API shim

                var style = function style(_ref) {
                    var prop = _ref.prop,
                        cssProperty = _ref.cssProperty,
                        alias = _ref.alias,
                        key = _ref.key,
                        transformValue = _ref.transformValue,
                        scale = _ref.scale,
                        properties = _ref.properties;
                    var config = {};
                    config[prop] = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.createStyleFunction)({
                        properties: properties,
                        property: cssProperty || prop,
                        scale: key,
                        defaultScale: scale,
                        transform: transformValue
                    });
                    if (alias) config[alias] = config[prop];
                    var parse = (0, _styled_system_core__WEBPACK_IMPORTED_MODULE_0__.createParser)(config);
                    return parse;
                };


                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_styled-system_dist_index_esm_js.ffc685749b0f0bfd.js.map