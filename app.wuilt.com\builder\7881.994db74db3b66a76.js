/*! For license information please see 7881.994db74db3b66a76.js.LICENSE.txt */
(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [7881, 3188, 221], {
        99279: (e, r, o) => {
            var t = o(69151),
                n = Symbol.for("react.element"),
                f = Symbol.for("react.fragment"),
                _ = Object.prototype.hasOwnProperty,
                a = t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,
                l = {
                    key: !0,
                    ref: !0,
                    __self: !0,
                    __source: !0
                };

            function p(e, r, o) {
                var t, f = {},
                    p = null,
                    s = null;
                for (t in void 0 !== o && (p = "" + o), void 0 !== r.key && (p = "" + r.key), void 0 !== r.ref && (s = r.ref), r) _.call(r, t) && !l.hasOwnProperty(t) && (f[t] = r[t]);
                if (e && e.defaultProps)
                    for (t in r = e.defaultProps) void 0 === f[t] && (f[t] = r[t]);
                return {
                    $$typeof: n,
                    type: e,
                    key: p,
                    ref: s,
                    props: f,
                    _owner: a.current
                }
            }
            r.Fragment = f, r.jsx = p, r.jsxs = p
        },
        43188: (e, r, o) => {
            e.exports = o(99279)
        }
    }
]);
//# sourceMappingURL=7881.994db74db3b66a76.js.map