(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [8059], {
        42514: (i, e, r) => {
            r.d(e, {
                Z: () => t
            });
            var n = r(20793),
                o = r(17880);

            function t(i) {
                return function(i) {
                    if (Array.isArray(i)) return (0, n.Z)(i)
                }(i) || function(i) {
                    if ("undefined" != typeof Symbol && null != i[Symbol.iterator] || null != i["@@iterator"]) return Array.from(i)
                }(i) || (0, o.Z)(i) || function() {
                    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }()
            }
        },
        17654: (i, e, r) => {
            (n = r(4677)) && "object" == typeof n && "default" in n && n.default;
            var n, o = r(37706),
                t = new o,
                a = t.getBrowser(),
                s = (t.getCPU(), t.getDevice()),
                u = t.getEngine(),
                b = t.getOS(),
                w = t.getUA();
            var c = "mobile",
                d = "tablet",
                l = "Chrome",
                m = "Firefox",
                p = "Opera",
                f = "Yandex",
                v = "Safari",
                h = "Internet Explorer",
                g = "Edge",
                x = "Chromium",
                y = "IE",
                k = "Mobile Safari",
                S = "MIUI Browser",
                _ = "Samsung Browser",
                T = "iOS",
                A = "Android",
                N = "Windows Phone",
                q = "Windows",
                E = "Mac OS",
                P = function(i) {
                    return i || (arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "none")
                },
                O = function() {
                    return !("undefined" == typeof window || !window.navigator && !navigator) && (window.navigator || navigator)
                },
                z = function(i) {
                    var e = O();
                    return e && e.platform && (-1 !== e.platform.indexOf(i) || "MacIntel" === e.platform && e.maxTouchPoints > 1 && !window.MSStream)
                },
                C = function(i) {
                    return i.type === c
                },
                U = function(i) {
                    return i.type === d
                },
                M = function(i) {
                    var e = i.type;
                    return e === c || e === d
                },
                j = function(i) {
                    return "smarttv" === i.type
                },
                I = function(i) {
                    return undefined === i.type
                },
                B = function(i) {
                    return "wearable" === i.type
                },
                R = function(i) {
                    return "console" === i.type
                },
                V = function(i) {
                    return "embedded" === i.type
                },
                D = function(i) {
                    var e = i.vendor;
                    return P(e)
                },
                W = function(i) {
                    var e = i.model;
                    return P(e)
                },
                Z = function(i) {
                    var e = i.type;
                    return P(e, "browser")
                },
                F = function(i) {
                    return i.name === A
                },
                L = function(i) {
                    return i.name === q
                },
                G = function(i) {
                    return i.name === E
                },
                H = function(i) {
                    return i.name === N
                },
                X = function(i) {
                    return i.name === T
                },
                $ = function(i) {
                    var e = i.version;
                    return P(e)
                },
                Q = function(i) {
                    var e = i.name;
                    return P(e)
                },
                Y = function(i) {
                    return i.name === l
                },
                K = function(i) {
                    return i.name === m
                },
                J = function(i) {
                    return i.name === x
                },
                ii = function(i) {
                    return i.name === g
                },
                ei = function(i) {
                    return i.name === f
                },
                ri = function(i) {
                    var e = i.name;
                    return e === v || e === k
                },
                ni = function(i) {
                    return i.name === k
                },
                oi = function(i) {
                    return i.name === p
                },
                ti = function(i) {
                    var e = i.name;
                    return e === h || e === y
                },
                ai = function(i) {
                    return i.name === S
                },
                si = function(i) {
                    return i.name === _
                },
                ui = function(i) {
                    var e = i.version;
                    return P(e)
                },
                bi = function(i) {
                    var e = i.major;
                    return P(e)
                },
                wi = function(i) {
                    var e = i.name;
                    return P(e)
                },
                ci = function(i) {
                    var e = i.name;
                    return P(e)
                },
                di = function(i) {
                    var e = i.version;
                    return P(e)
                },
                li = function() {
                    var i = O(),
                        e = i && i.userAgent && i.userAgent.toLowerCase();
                    return "string" == typeof e && /electron/.test(e)
                },
                mi = function(i) {
                    return "string" == typeof i && -1 !== i.indexOf("Edg/")
                },
                pi = function() {
                    var i = O();
                    return i && (/iPad|iPhone|iPod/.test(i.platform) || "MacIntel" === i.platform && i.maxTouchPoints > 1) && !window.MSStream
                },
                fi = function() {
                    return z("iPad")
                },
                vi = function() {
                    return z("iPhone")
                },
                hi = function() {
                    return z("iPod")
                },
                gi = function(i) {
                    return P(i)
                };
            j(s), R(s), B(s), V(s), ni(a) || fi(), J(a);
            var xi = M(s) || fi(),
                yi = (C(s), U(s) || fi(), I(s), I(s), F(b), H(b), X(b) || fi()),
                ki = (Y(a), K(a), ri(a), oi(a), ti(a), $(b), Q(b), ui(a), bi(a), wi(a), D(s), W(s), ci(u), di(u), gi(w), ii(a) || mi(w), ei(a), Z(s), pi(), fi(), vi(), hi(), li(), mi(w), ii(a) && mi(w), L(b), G(b));
            ai(a), si(a);
            e.gn = yi, e.Q5 = ki, e.tq = xi
        },
        37706: function(i, e, r) {
            var n;
            ! function(o, t) {
                var a = "function",
                    s = "undefined",
                    u = "object",
                    b = "string",
                    w = "major",
                    c = "model",
                    d = "name",
                    l = "type",
                    m = "vendor",
                    p = "version",
                    f = "architecture",
                    v = "console",
                    h = "mobile",
                    g = "tablet",
                    x = "smarttv",
                    y = "wearable",
                    k = "embedded",
                    S = "Amazon",
                    _ = "Apple",
                    T = "ASUS",
                    A = "BlackBerry",
                    N = "Browser",
                    q = "Chrome",
                    E = "Firefox",
                    P = "Google",
                    O = "Huawei",
                    z = "LG",
                    C = "Microsoft",
                    U = "Motorola",
                    M = "Opera",
                    j = "Samsung",
                    I = "Sharp",
                    B = "Sony",
                    R = "Xiaomi",
                    V = "Zebra",
                    D = "Facebook",
                    W = "Chromium OS",
                    Z = "Mac OS",
                    F = function(i) {
                        for (var e = {}, r = 0; r < i.length; r++) e[i[r].toUpperCase()] = i[r];
                        return e
                    },
                    L = function(i, e) {
                        return typeof i === b && -1 !== G(e).indexOf(G(i))
                    },
                    G = function(i) {
                        return i.toLowerCase()
                    },
                    H = function(i, e) {
                        if (typeof i === b) return i = i.replace(/^\s\s*/, ""), typeof e === s ? i : i.substring(0, 350)
                    },
                    X = function(i, e) {
                        for (var r, n, o, s, b, w, c = 0; c < e.length && !b;) {
                            var d = e[c],
                                l = e[c + 1];
                            for (r = n = 0; r < d.length && !b && d[r];)
                                if (b = d[r++].exec(i))
                                    for (o = 0; o < l.length; o++) w = b[++n], typeof(s = l[o]) === u && s.length > 0 ? 2 === s.length ? typeof s[1] == a ? this[s[0]] = s[1].call(this, w) : this[s[0]] = s[1] : 3 === s.length ? typeof s[1] !== a || s[1].exec && s[1].test ? this[s[0]] = w ? w.replace(s[1], s[2]) : t : this[s[0]] = w ? s[1].call(this, w, s[2]) : t : 4 === s.length && (this[s[0]] = w ? s[3].call(this, w.replace(s[1], s[2])) : t) : this[s] = w || t;
                            c += 2
                        }
                    },
                    $ = function(i, e) {
                        for (var r in e)
                            if (typeof e[r] === u && e[r].length > 0) {
                                for (var n = 0; n < e[r].length; n++)
                                    if (L(e[r][n], i)) return "?" === r ? t : r
                            } else if (L(e[r], i)) return "?" === r ? t : r;
                        return i
                    },
                    Q = {
                        ME: "4.90",
                        "NT 3.11": "NT3.51",
                        "NT 4.0": "NT4.0",
                        2e3: "NT 5.0",
                        XP: ["NT 5.1", "NT 5.2"],
                        Vista: "NT 6.0",
                        7: "NT 6.1",
                        8: "NT 6.2",
                        8.1: "NT 6.3",
                        10: ["NT 6.4", "NT 10.0"],
                        RT: "ARM"
                    },
                    Y = {
                        browser: [
                            [/\b(?:crmo|crios)\/([\w\.]+)/i],
                            [p, [d, "Chrome"]],
                            [/edg(?:e|ios|a)?\/([\w\.]+)/i],
                            [p, [d, "Edge"]],
                            [/(opera mini)\/([-\w\.]+)/i, /(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i, /(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],
                            [d, p],
                            [/opios[\/ ]+([\w\.]+)/i],
                            [p, [d, M + " Mini"]],
                            [/\bopr\/([\w\.]+)/i],
                            [p, [d, M]],
                            [/(kindle)\/([\w\.]+)/i, /(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i, /(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i, /(ba?idubrowser)[\/ ]?([\w\.]+)/i, /(?:ms|\()(ie) ([\w\.]+)/i, /(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i, /(heytap|ovi)browser\/([\d\.]+)/i, /(weibo)__([\d\.]+)/i],
                            [d, p],
                            [/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],
                            [p, [d, "UC" + N]],
                            [/microm.+\bqbcore\/([\w\.]+)/i, /\bqbcore\/([\w\.]+).+microm/i],
                            [p, [d, "WeChat(Win) Desktop"]],
                            [/micromessenger\/([\w\.]+)/i],
                            [p, [d, "WeChat"]],
                            [/konqueror\/([\w\.]+)/i],
                            [p, [d, "Konqueror"]],
                            [/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],
                            [p, [d, "IE"]],
                            [/ya(?:search)?browser\/([\w\.]+)/i],
                            [p, [d, "Yandex"]],
                            [/(avast|avg)\/([\w\.]+)/i],
                            [
                                [d, /(.+)/, "$1 Secure " + N], p
                            ],
                            [/\bfocus\/([\w\.]+)/i],
                            [p, [d, E + " Focus"]],
                            [/\bopt\/([\w\.]+)/i],
                            [p, [d, M + " Touch"]],
                            [/coc_coc\w+\/([\w\.]+)/i],
                            [p, [d, "Coc Coc"]],
                            [/dolfin\/([\w\.]+)/i],
                            [p, [d, "Dolphin"]],
                            [/coast\/([\w\.]+)/i],
                            [p, [d, M + " Coast"]],
                            [/miuibrowser\/([\w\.]+)/i],
                            [p, [d, "MIUI " + N]],
                            [/fxios\/([-\w\.]+)/i],
                            [p, [d, E]],
                            [/\bqihu|(qi?ho?o?|360)browser/i],
                            [
                                [d, "360 " + N]
                            ],
                            [/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],
                            [
                                [d, /(.+)/, "$1 " + N], p
                            ],
                            [/(comodo_dragon)\/([\w\.]+)/i],
                            [
                                [d, /_/g, " "], p
                            ],
                            [/(electron)\/([\w\.]+) safari/i, /(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i, /m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],
                            [d, p],
                            [/(metasr)[\/ ]?([\w\.]+)/i, /(lbbrowser)/i, /\[(linkedin)app\]/i],
                            [d],
                            [/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],
                            [
                                [d, D], p
                            ],
                            [/(kakao(?:talk|story))[\/ ]([\w\.]+)/i, /(naver)\(.*?(\d+\.[\w\.]+).*\)/i, /safari (line)\/([\w\.]+)/i, /\b(line)\/([\w\.]+)\/iab/i, /(chromium|instagram)[\/ ]([-\w\.]+)/i],
                            [d, p],
                            [/\bgsa\/([\w\.]+) .*safari\//i],
                            [p, [d, "GSA"]],
                            [/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],
                            [p, [d, "TikTok"]],
                            [/headlesschrome(?:\/([\w\.]+)| )/i],
                            [p, [d, q + " Headless"]],
                            [/ wv\).+(chrome)\/([\w\.]+)/i],
                            [
                                [d, q + " WebView"], p
                            ],
                            [/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],
                            [p, [d, "Android " + N]],
                            [/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],
                            [d, p],
                            [/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],
                            [p, [d, "Mobile Safari"]],
                            [/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],
                            [p, d],
                            [/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],
                            [d, [p, $, {
                                "1.0": "/8",
                                1.2: "/1",
                                1.3: "/3",
                                "2.0": "/412",
                                "2.0.2": "/416",
                                "2.0.3": "/417",
                                "2.0.4": "/419",
                                "?": "/"
                            }]],
                            [/(webkit|khtml)\/([\w\.]+)/i],
                            [d, p],
                            [/(navigator|netscape\d?)\/([-\w\.]+)/i],
                            [
                                [d, "Netscape"], p
                            ],
                            [/mobile vr; rv:([\w\.]+)\).+firefox/i],
                            [p, [d, E + " Reality"]],
                            [/ekiohf.+(flow)\/([\w\.]+)/i, /(swiftfox)/i, /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i, /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i, /(firefox)\/([\w\.]+)/i, /(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i, /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i, /(links) \(([\w\.]+)/i, /panasonic;(viera)/i],
                            [d, p],
                            [/(cobalt)\/([\w\.]+)/i],
                            [d, [p, /master.|lts./, ""]]
                        ],
                        cpu: [
                            [/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],
                            [
                                [f, "amd64"]
                            ],
                            [/(ia32(?=;))/i],
                            [
                                [f, G]
                            ],
                            [/((?:i[346]|x)86)[;\)]/i],
                            [
                                [f, "ia32"]
                            ],
                            [/\b(aarch64|arm(v?8e?l?|_?64))\b/i],
                            [
                                [f, "arm64"]
                            ],
                            [/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],
                            [
                                [f, "armhf"]
                            ],
                            [/windows (ce|mobile); ppc;/i],
                            [
                                [f, "arm"]
                            ],
                            [/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],
                            [
                                [f, /ower/, "", G]
                            ],
                            [/(sun4\w)[;\)]/i],
                            [
                                [f, "sparc"]
                            ],
                            [/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],
                            [
                                [f, G]
                            ]
                        ],
                        device: [
                            [/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],
                            [c, [m, j],
                                [l, g]
                            ],
                            [/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i, /samsung[- ]([-\w]+)/i, /sec-(sgh\w+)/i],
                            [c, [m, j],
                                [l, h]
                            ],
                            [/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],
                            [c, [m, _],
                                [l, h]
                            ],
                            [/\((ipad);[-\w\),; ]+apple/i, /applecoremedia\/[\w\.]+ \((ipad)/i, /\b(ipad)\d\d?,\d\d?[;\]].+ios/i],
                            [c, [m, _],
                                [l, g]
                            ],
                            [/(macintosh);/i],
                            [c, [m, _]],
                            [/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],
                            [c, [m, I],
                                [l, h]
                            ],
                            [/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],
                            [c, [m, O],
                                [l, g]
                            ],
                            [/(?:huawei|honor)([-\w ]+)[;\)]/i, /\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],
                            [c, [m, O],
                                [l, h]
                            ],
                            [/\b(poco[\w ]+)(?: bui|\))/i, /\b; (\w+) build\/hm\1/i, /\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i, /\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i, /\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],
                            [
                                [c, /_/g, " "],
                                [m, R],
                                [l, h]
                            ],
                            [/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],
                            [
                                [c, /_/g, " "],
                                [m, R],
                                [l, g]
                            ],
                            [/; (\w+) bui.+ oppo/i, /\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],
                            [c, [m, "OPPO"],
                                [l, h]
                            ],
                            [/vivo (\w+)(?: bui|\))/i, /\b(v[12]\d{3}\w?[at])(?: bui|;)/i],
                            [c, [m, "Vivo"],
                                [l, h]
                            ],
                            [/\b(rmx[12]\d{3})(?: bui|;|\))/i],
                            [c, [m, "Realme"],
                                [l, h]
                            ],
                            [/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i, /\bmot(?:orola)?[- ](\w*)/i, /((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],
                            [c, [m, U],
                                [l, h]
                            ],
                            [/\b(mz60\d|xoom[2 ]{0,2}) build\//i],
                            [c, [m, U],
                                [l, g]
                            ],
                            [/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],
                            [c, [m, z],
                                [l, g]
                            ],
                            [/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i, /\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i, /\blg-?([\d\w]+) bui/i],
                            [c, [m, z],
                                [l, h]
                            ],
                            [/(ideatab[-\w ]+)/i, /lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],
                            [c, [m, "Lenovo"],
                                [l, g]
                            ],
                            [/(?:maemo|nokia).*(n900|lumia \d+)/i, /nokia[-_ ]?([-\w\.]*)/i],
                            [
                                [c, /_/g, " "],
                                [m, "Nokia"],
                                [l, h]
                            ],
                            [/(pixel c)\b/i],
                            [c, [m, P],
                                [l, g]
                            ],
                            [/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],
                            [c, [m, P],
                                [l, h]
                            ],
                            [/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],
                            [c, [m, B],
                                [l, h]
                            ],
                            [/sony tablet [ps]/i, /\b(?:sony)?sgp\w+(?: bui|\))/i],
                            [
                                [c, "Xperia Tablet"],
                                [m, B],
                                [l, g]
                            ],
                            [/ (kb2005|in20[12]5|be20[12][59])\b/i, /(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],
                            [c, [m, "OnePlus"],
                                [l, h]
                            ],
                            [/(alexa)webm/i, /(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i, /(kf[a-z]+)( bui|\)).+silk\//i],
                            [c, [m, S],
                                [l, g]
                            ],
                            [/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],
                            [
                                [c, /(.+)/g, "Fire Phone $1"],
                                [m, S],
                                [l, h]
                            ],
                            [/(playbook);[-\w\),; ]+(rim)/i],
                            [c, m, [l, g]],
                            [/\b((?:bb[a-f]|st[hv])100-\d)/i, /\(bb10; (\w+)/i],
                            [c, [m, A],
                                [l, h]
                            ],
                            [/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],
                            [c, [m, T],
                                [l, g]
                            ],
                            [/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],
                            [c, [m, T],
                                [l, h]
                            ],
                            [/(nexus 9)/i],
                            [c, [m, "HTC"],
                                [l, g]
                            ],
                            [/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i, /(zte)[- ]([\w ]+?)(?: bui|\/|\))/i, /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],
                            [m, [c, /_/g, " "],
                                [l, h]
                            ],
                            [/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],
                            [c, [m, "Acer"],
                                [l, g]
                            ],
                            [/droid.+; (m[1-5] note) bui/i, /\bmz-([-\w]{2,})/i],
                            [c, [m, "Meizu"],
                                [l, h]
                            ],
                            [/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i, /(hp) ([\w ]+\w)/i, /(asus)-?(\w+)/i, /(microsoft); (lumia[\w ]+)/i, /(lenovo)[-_ ]?([-\w]+)/i, /(jolla)/i, /(oppo) ?([\w ]+) bui/i],
                            [m, c, [l, h]],
                            [/(kobo)\s(ereader|touch)/i, /(archos) (gamepad2?)/i, /(hp).+(touchpad(?!.+tablet)|tablet)/i, /(kindle)\/([\w\.]+)/i, /(nook)[\w ]+build\/(\w+)/i, /(dell) (strea[kpr\d ]*[\dko])/i, /(le[- ]+pan)[- ]+(\w{1,9}) bui/i, /(trinity)[- ]*(t\d{3}) bui/i, /(gigaset)[- ]+(q\w{1,9}) bui/i, /(vodafone) ([\w ]+)(?:\)| bui)/i],
                            [m, c, [l, g]],
                            [/(surface duo)/i],
                            [c, [m, C],
                                [l, g]
                            ],
                            [/droid [\d\.]+; (fp\du?)(?: b|\))/i],
                            [c, [m, "Fairphone"],
                                [l, h]
                            ],
                            [/(u304aa)/i],
                            [c, [m, "AT&T"],
                                [l, h]
                            ],
                            [/\bsie-(\w*)/i],
                            [c, [m, "Siemens"],
                                [l, h]
                            ],
                            [/\b(rct\w+) b/i],
                            [c, [m, "RCA"],
                                [l, g]
                            ],
                            [/\b(venue[\d ]{2,7}) b/i],
                            [c, [m, "Dell"],
                                [l, g]
                            ],
                            [/\b(q(?:mv|ta)\w+) b/i],
                            [c, [m, "Verizon"],
                                [l, g]
                            ],
                            [/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],
                            [c, [m, "Barnes & Noble"],
                                [l, g]
                            ],
                            [/\b(tm\d{3}\w+) b/i],
                            [c, [m, "NuVision"],
                                [l, g]
                            ],
                            [/\b(k88) b/i],
                            [c, [m, "ZTE"],
                                [l, g]
                            ],
                            [/\b(nx\d{3}j) b/i],
                            [c, [m, "ZTE"],
                                [l, h]
                            ],
                            [/\b(gen\d{3}) b.+49h/i],
                            [c, [m, "Swiss"],
                                [l, h]
                            ],
                            [/\b(zur\d{3}) b/i],
                            [c, [m, "Swiss"],
                                [l, g]
                            ],
                            [/\b((zeki)?tb.*\b) b/i],
                            [c, [m, "Zeki"],
                                [l, g]
                            ],
                            [/\b([yr]\d{2}) b/i, /\b(dragon[- ]+touch |dt)(\w{5}) b/i],
                            [
                                [m, "Dragon Touch"], c, [l, g]
                            ],
                            [/\b(ns-?\w{0,9}) b/i],
                            [c, [m, "Insignia"],
                                [l, g]
                            ],
                            [/\b((nxa|next)-?\w{0,9}) b/i],
                            [c, [m, "NextBook"],
                                [l, g]
                            ],
                            [/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],
                            [
                                [m, "Voice"], c, [l, h]
                            ],
                            [/\b(lvtel\-)?(v1[12]) b/i],
                            [
                                [m, "LvTel"], c, [l, h]
                            ],
                            [/\b(ph-1) /i],
                            [c, [m, "Essential"],
                                [l, h]
                            ],
                            [/\b(v(100md|700na|7011|917g).*\b) b/i],
                            [c, [m, "Envizen"],
                                [l, g]
                            ],
                            [/\b(trio[-\w\. ]+) b/i],
                            [c, [m, "MachSpeed"],
                                [l, g]
                            ],
                            [/\btu_(1491) b/i],
                            [c, [m, "Rotor"],
                                [l, g]
                            ],
                            [/(shield[\w ]+) b/i],
                            [c, [m, "Nvidia"],
                                [l, g]
                            ],
                            [/(sprint) (\w+)/i],
                            [m, c, [l, h]],
                            [/(kin\.[onetw]{3})/i],
                            [
                                [c, /\./g, " "],
                                [m, C],
                                [l, h]
                            ],
                            [/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],
                            [c, [m, V],
                                [l, g]
                            ],
                            [/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],
                            [c, [m, V],
                                [l, h]
                            ],
                            [/smart-tv.+(samsung)/i],
                            [m, [l, x]],
                            [/hbbtv.+maple;(\d+)/i],
                            [
                                [c, /^/, "SmartTV"],
                                [m, j],
                                [l, x]
                            ],
                            [/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],
                            [
                                [m, z],
                                [l, x]
                            ],
                            [/(apple) ?tv/i],
                            [m, [c, _ + " TV"],
                                [l, x]
                            ],
                            [/crkey/i],
                            [
                                [c, q + "cast"],
                                [m, P],
                                [l, x]
                            ],
                            [/droid.+aft(\w)( bui|\))/i],
                            [c, [m, S],
                                [l, x]
                            ],
                            [/\(dtv[\);].+(aquos)/i, /(aquos-tv[\w ]+)\)/i],
                            [c, [m, I],
                                [l, x]
                            ],
                            [/(bravia[\w ]+)( bui|\))/i],
                            [c, [m, B],
                                [l, x]
                            ],
                            [/(mitv-\w{5}) bui/i],
                            [c, [m, R],
                                [l, x]
                            ],
                            [/Hbbtv.*(technisat) (.*);/i],
                            [m, c, [l, x]],
                            [/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i, /hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],
                            [
                                [m, H],
                                [c, H],
                                [l, x]
                            ],
                            [/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],
                            [
                                [l, x]
                            ],
                            [/(ouya)/i, /(nintendo) ([wids3utch]+)/i],
                            [m, c, [l, v]],
                            [/droid.+; (shield) bui/i],
                            [c, [m, "Nvidia"],
                                [l, v]
                            ],
                            [/(playstation [345portablevi]+)/i],
                            [c, [m, B],
                                [l, v]
                            ],
                            [/\b(xbox(?: one)?(?!; xbox))[\); ]/i],
                            [c, [m, C],
                                [l, v]
                            ],
                            [/((pebble))app/i],
                            [m, c, [l, y]],
                            [/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],
                            [c, [m, _],
                                [l, y]
                            ],
                            [/droid.+; (glass) \d/i],
                            [c, [m, P],
                                [l, y]
                            ],
                            [/droid.+; (wt63?0{2,3})\)/i],
                            [c, [m, V],
                                [l, y]
                            ],
                            [/(quest( 2| pro)?)/i],
                            [c, [m, D],
                                [l, y]
                            ],
                            [/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],
                            [m, [l, k]],
                            [/(aeobc)\b/i],
                            [c, [m, S],
                                [l, k]
                            ],
                            [/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],
                            [c, [l, h]],
                            [/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],
                            [c, [l, g]],
                            [/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],
                            [
                                [l, g]
                            ],
                            [/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],
                            [
                                [l, h]
                            ],
                            [/(android[-\w\. ]{0,9});.+buil/i],
                            [c, [m, "Generic"]]
                        ],
                        engine: [
                            [/windows.+ edge\/([\w\.]+)/i],
                            [p, [d, "EdgeHTML"]],
                            [/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],
                            [p, [d, "Blink"]],
                            [/(presto)\/([\w\.]+)/i, /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i, /ekioh(flow)\/([\w\.]+)/i, /(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i, /(icab)[\/ ]([23]\.[\d\.]+)/i, /\b(libweb)/i],
                            [d, p],
                            [/rv\:([\w\.]{1,9})\b.+(gecko)/i],
                            [p, d]
                        ],
                        os: [
                            [/microsoft (windows) (vista|xp)/i],
                            [d, p],
                            [/(windows) nt 6\.2; (arm)/i, /(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i, /(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],
                            [d, [p, $, Q]],
                            [/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],
                            [
                                [d, "Windows"],
                                [p, $, Q]
                            ],
                            [/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i, /ios;fbsv\/([\d\.]+)/i, /cfnetwork\/.+darwin/i],
                            [
                                [p, /_/g, "."],
                                [d, "iOS"]
                            ],
                            [/(mac os x) ?([\w\. ]*)/i, /(macintosh|mac_powerpc\b)(?!.+haiku)/i],
                            [
                                [d, Z],
                                [p, /_/g, "."]
                            ],
                            [/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],
                            [p, d],
                            [/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i, /(blackberry)\w*\/([\w\.]*)/i, /(tizen|kaios)[\/ ]([\w\.]+)/i, /\((series40);/i],
                            [d, p],
                            [/\(bb(10);/i],
                            [p, [d, A]],
                            [/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],
                            [p, [d, "Symbian"]],
                            [/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],
                            [p, [d, E + " OS"]],
                            [/web0s;.+rt(tv)/i, /\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],
                            [p, [d, "webOS"]],
                            [/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],
                            [p, [d, "watchOS"]],
                            [/crkey\/([\d\.]+)/i],
                            [p, [d, q + "cast"]],
                            [/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],
                            [
                                [d, W], p
                            ],
                            [/panasonic;(viera)/i, /(netrange)mmh/i, /(nettv)\/(\d+\.[\w\.]+)/i, /(nintendo|playstation) ([wids345portablevuch]+)/i, /(xbox); +xbox ([^\);]+)/i, /\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i, /(mint)[\/\(\) ]?(\w*)/i, /(mageia|vectorlinux)[; ]/i, /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i, /(hurd|linux) ?([\w\.]*)/i, /(gnu) ?([\w\.]*)/i, /\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i, /(haiku) (\w+)/i],
                            [d, p],
                            [/(sunos) ?([\w\.\d]*)/i],
                            [
                                [d, "Solaris"], p
                            ],
                            [/((?:open)?solaris)[-\/ ]?([\w\.]*)/i, /(aix) ((\d)(?=\.|\)| )[\w\.])*/i, /\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, /(unix) ?([\w\.]*)/i],
                            [d, p]
                        ]
                    },
                    K = function(i, e) {
                        if (typeof i === u && (e = i, i = t), !(this instanceof K)) return new K(i, e).getResult();
                        var r = typeof o !== s && o.navigator ? o.navigator : t,
                            n = i || (r && r.userAgent ? r.userAgent : ""),
                            v = r && r.userAgentData ? r.userAgentData : t,
                            x = e ? function(i, e) {
                                var r = {};
                                for (var n in i) e[n] && e[n].length % 2 == 0 ? r[n] = e[n].concat(i[n]) : r[n] = i[n];
                                return r
                            }(Y, e) : Y,
                            y = r && r.userAgent == n;
                        return this.getBrowser = function() {
                            var i, e = {};
                            return e[d] = t, e[p] = t, X.call(e, n, x.browser), e[w] = typeof(i = e[p]) === b ? i.replace(/[^\d\.]/g, "").split(".")[0] : t, y && r && r.brave && typeof r.brave.isBrave == a && (e[d] = "Brave"), e
                        }, this.getCPU = function() {
                            var i = {};
                            return i[f] = t, X.call(i, n, x.cpu), i
                        }, this.getDevice = function() {
                            var i = {};
                            return i[m] = t, i[c] = t, i[l] = t, X.call(i, n, x.device), y && !i[l] && v && v.mobile && (i[l] = h), y && "Macintosh" == i[c] && r && typeof r.standalone !== s && r.maxTouchPoints && r.maxTouchPoints > 2 && (i[c] = "iPad", i[l] = g), i
                        }, this.getEngine = function() {
                            var i = {};
                            return i[d] = t, i[p] = t, X.call(i, n, x.engine), i
                        }, this.getOS = function() {
                            var i = {};
                            return i[d] = t, i[p] = t, X.call(i, n, x.os), y && !i[d] && v && "Unknown" != v.platform && (i[d] = v.platform.replace(/chrome os/i, W).replace(/macos/i, Z)), i
                        }, this.getResult = function() {
                            return {
                                ua: this.getUA(),
                                browser: this.getBrowser(),
                                engine: this.getEngine(),
                                os: this.getOS(),
                                device: this.getDevice(),
                                cpu: this.getCPU()
                            }
                        }, this.getUA = function() {
                            return n
                        }, this.setUA = function(i) {
                            return n = typeof i === b && i.length > 350 ? H(i, 350) : i, this
                        }, this.setUA(n), this
                    };
                K.VERSION = "1.0.35", K.BROWSER = F([d, p, w]), K.CPU = F([f]), K.DEVICE = F([c, m, l, v, h, x, g, y, k]), K.ENGINE = K.OS = F([d, p]), typeof e !== s ? (i.exports && (e = i.exports = K), e.UAParser = K) : r.amdO ? (n = function() {
                    return K
                }.call(e, r, e, i)) === t || (i.exports = n) : typeof o !== s && (o.UAParser = K);
                var J = typeof o !== s && (o.jQuery || o.Zepto);
                if (J && !J.ua) {
                    var ii = new K;
                    J.ua = ii.getResult(), J.ua.get = function() {
                        return ii.getUA()
                    }, J.ua.set = function(i) {
                        ii.setUA(i);
                        var e = ii.getResult();
                        for (var r in e) J.ua[r] = e[r]
                    }
                }
            }("object" == typeof window ? window : this)
        },
        55335: (i, e, r) => {
            r.d(e, {
                X: () => t
            });
            var n = r(65006),
                o = r(90236);

            function t(i, e, r) {
                return void 0 === e && (e = {}), void 0 === r && (r = {}),
                    function(t, a, s) {
                        try {
                            return Promise.resolve(function(o, u) {
                                try {
                                    var b = (e.context, Promise.resolve(i["sync" === r.mode ? "validateSync" : "validate"](t, Object.assign({
                                        abortEarly: !1
                                    }, e, {
                                        context: a
                                    }))).then((function(i) {
                                        return s.shouldUseNativeValidation && (0, n.validateFieldsNatively)({}, s), {
                                            values: r.raw ? t : i,
                                            errors: {}
                                        }
                                    })))
                                } catch (i) {
                                    return u(i)
                                }
                                return b && b.then ? b.then(void 0, u) : b
                            }(0, (function(i) {
                                if (!i.inner) throw i;
                                return {
                                    values: {},
                                    errors: (0, n.toNestErrors)((e = i, r = !s.shouldUseNativeValidation && "all" === s.criteriaMode, (e.inner || []).reduce((function(i, e) {
                                        if (i[e.path] || (i[e.path] = {
                                                message: e.message,
                                                type: e.type
                                            }), r) {
                                            var n = i[e.path].types,
                                                t = n && n[e.type];
                                            i[e.path] = (0, o.appendErrors)(e.path, r, i, e.type, t ? [].concat(t, e.message) : e.message)
                                        }
                                        return i
                                    }), {})), s)
                                };
                                var e, r
                            })))
                        } catch (i) {
                            return Promise.reject(i)
                        }
                    }
            }
        }
    }
]);
//# sourceMappingURL=8059.6ed01b288becd81e.js.map