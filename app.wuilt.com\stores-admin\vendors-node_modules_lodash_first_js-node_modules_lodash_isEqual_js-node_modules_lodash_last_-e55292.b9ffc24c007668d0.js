(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["vendors-node_modules_lodash_first_js-node_modules_lodash_isEqual_js-node_modules_lodash_last_-e55292"], {

        /***/
        "../../node_modules/lodash/_apply.js":
            /***/
            ((module) => {

                /**
                 * A faster alternative to `Function#apply`, this function invokes `func`
                 * with the `this` binding of `thisArg` and the arguments of `args`.
                 *
                 * @private
                 * @param {Function} func The function to invoke.
                 * @param {*} thisArg The `this` binding of `func`.
                 * @param {Array} args The arguments to invoke `func` with.
                 * @returns {*} Returns the result of `func`.
                 */
                function apply(func, thisArg, args) {
                    switch (args.length) {
                        case 0:
                            return func.call(thisArg);
                        case 1:
                            return func.call(thisArg, args[0]);
                        case 2:
                            return func.call(thisArg, args[0], args[1]);
                        case 3:
                            return func.call(thisArg, args[0], args[1], args[2]);
                    }
                    return func.apply(thisArg, args);
                }

                module.exports = apply;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseEach.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var baseForOwn = __webpack_require__("../../node_modules/lodash/_baseForOwn.js"),
                    createBaseEach = __webpack_require__("../../node_modules/lodash/_createBaseEach.js");

                /**
                 * The base implementation of `_.forEach` without support for iteratee shorthands.
                 *
                 * @private
                 * @param {Array|Object} collection The collection to iterate over.
                 * @param {Function} iteratee The function invoked per iteration.
                 * @returns {Array|Object} Returns `collection`.
                 */
                var baseEach = createBaseEach(baseForOwn);

                module.exports = baseEach;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseFlatten.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var arrayPush = __webpack_require__("../../node_modules/lodash/_arrayPush.js"),
                    isFlattenable = __webpack_require__("../../node_modules/lodash/_isFlattenable.js");

                /**
                 * The base implementation of `_.flatten` with support for restricting flattening.
                 *
                 * @private
                 * @param {Array} array The array to flatten.
                 * @param {number} depth The maximum recursion depth.
                 * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.
                 * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.
                 * @param {Array} [result=[]] The initial result value.
                 * @returns {Array} Returns the new flattened array.
                 */
                function baseFlatten(array, depth, predicate, isStrict, result) {
                    var index = -1,
                        length = array.length;

                    predicate || (predicate = isFlattenable);
                    result || (result = []);

                    while (++index < length) {
                        var value = array[index];
                        if (depth > 0 && predicate(value)) {
                            if (depth > 1) {
                                // Recursively flatten arrays (susceptible to call stack limits).
                                baseFlatten(value, depth - 1, predicate, isStrict, result);
                            } else {
                                arrayPush(result, value);
                            }
                        } else if (!isStrict) {
                            result[result.length] = value;
                        }
                    }
                    return result;
                }

                module.exports = baseFlatten;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseFor.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var createBaseFor = __webpack_require__("../../node_modules/lodash/_createBaseFor.js");

                /**
                 * The base implementation of `baseForOwn` which iterates over `object`
                 * properties returned by `keysFunc` and invokes `iteratee` for each property.
                 * Iteratee functions may exit iteration early by explicitly returning `false`.
                 *
                 * @private
                 * @param {Object} object The object to iterate over.
                 * @param {Function} iteratee The function invoked per iteration.
                 * @param {Function} keysFunc The function to get the keys of `object`.
                 * @returns {Object} Returns `object`.
                 */
                var baseFor = createBaseFor();

                module.exports = baseFor;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseForOwn.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var baseFor = __webpack_require__("../../node_modules/lodash/_baseFor.js"),
                    keys = __webpack_require__("../../node_modules/lodash/keys.js");

                /**
                 * The base implementation of `_.forOwn` without support for iteratee shorthands.
                 *
                 * @private
                 * @param {Object} object The object to iterate over.
                 * @param {Function} iteratee The function invoked per iteration.
                 * @returns {Object} Returns `object`.
                 */
                function baseForOwn(object, iteratee) {
                    return object && baseFor(object, iteratee, keys);
                }

                module.exports = baseForOwn;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseMap.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var baseEach = __webpack_require__("../../node_modules/lodash/_baseEach.js"),
                    isArrayLike = __webpack_require__("../../node_modules/lodash/isArrayLike.js");

                /**
                 * The base implementation of `_.map` without support for iteratee shorthands.
                 *
                 * @private
                 * @param {Array|Object} collection The collection to iterate over.
                 * @param {Function} iteratee The function invoked per iteration.
                 * @returns {Array} Returns the new mapped array.
                 */
                function baseMap(collection, iteratee) {
                    var index = -1,
                        result = isArrayLike(collection) ? Array(collection.length) : [];

                    baseEach(collection, function(value, key, collection) {
                        result[++index] = iteratee(value, key, collection);
                    });
                    return result;
                }

                module.exports = baseMap;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseOrderBy.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var arrayMap = __webpack_require__("../../node_modules/lodash/_arrayMap.js"),
                    baseGet = __webpack_require__("../../node_modules/lodash/_baseGet.js"),
                    baseIteratee = __webpack_require__("../../node_modules/lodash/_baseIteratee.js"),
                    baseMap = __webpack_require__("../../node_modules/lodash/_baseMap.js"),
                    baseSortBy = __webpack_require__("../../node_modules/lodash/_baseSortBy.js"),
                    baseUnary = __webpack_require__("../../node_modules/lodash/_baseUnary.js"),
                    compareMultiple = __webpack_require__("../../node_modules/lodash/_compareMultiple.js"),
                    identity = __webpack_require__("../../node_modules/lodash/identity.js"),
                    isArray = __webpack_require__("../../node_modules/lodash/isArray.js");

                /**
                 * The base implementation of `_.orderBy` without param guards.
                 *
                 * @private
                 * @param {Array|Object} collection The collection to iterate over.
                 * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.
                 * @param {string[]} orders The sort orders of `iteratees`.
                 * @returns {Array} Returns the new sorted array.
                 */
                function baseOrderBy(collection, iteratees, orders) {
                    if (iteratees.length) {
                        iteratees = arrayMap(iteratees, function(iteratee) {
                            if (isArray(iteratee)) {
                                return function(value) {
                                    return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);
                                }
                            }
                            return iteratee;
                        });
                    } else {
                        iteratees = [identity];
                    }

                    var index = -1;
                    iteratees = arrayMap(iteratees, baseUnary(baseIteratee));

                    var result = baseMap(collection, function(value, key, collection) {
                        var criteria = arrayMap(iteratees, function(iteratee) {
                            return iteratee(value);
                        });
                        return {
                            'criteria': criteria,
                            'index': ++index,
                            'value': value
                        };
                    });

                    return baseSortBy(result, function(object, other) {
                        return compareMultiple(object, other, orders);
                    });
                }

                module.exports = baseOrderBy;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseRest.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var identity = __webpack_require__("../../node_modules/lodash/identity.js"),
                    overRest = __webpack_require__("../../node_modules/lodash/_overRest.js"),
                    setToString = __webpack_require__("../../node_modules/lodash/_setToString.js");

                /**
                 * The base implementation of `_.rest` which doesn't validate or coerce arguments.
                 *
                 * @private
                 * @param {Function} func The function to apply a rest parameter to.
                 * @param {number} [start=func.length-1] The start position of the rest parameter.
                 * @returns {Function} Returns the new function.
                 */
                function baseRest(func, start) {
                    return setToString(overRest(func, start, identity), func + '');
                }

                module.exports = baseRest;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseSetToString.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var constant = __webpack_require__("../../node_modules/lodash/constant.js"),
                    defineProperty = __webpack_require__("../../node_modules/lodash/_defineProperty.js"),
                    identity = __webpack_require__("../../node_modules/lodash/identity.js");

                /**
                 * The base implementation of `setToString` without support for hot loop shorting.
                 *
                 * @private
                 * @param {Function} func The function to modify.
                 * @param {Function} string The `toString` result.
                 * @returns {Function} Returns `func`.
                 */
                var baseSetToString = !defineProperty ? identity : function(func, string) {
                    return defineProperty(func, 'toString', {
                        'configurable': true,
                        'enumerable': false,
                        'value': constant(string),
                        'writable': true
                    });
                };

                module.exports = baseSetToString;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_baseSortBy.js":
            /***/
            ((module) => {

                /**
                 * The base implementation of `_.sortBy` which uses `comparer` to define the
                 * sort order of `array` and replaces criteria objects with their corresponding
                 * values.
                 *
                 * @private
                 * @param {Array} array The array to sort.
                 * @param {Function} comparer The function to define sort order.
                 * @returns {Array} Returns `array`.
                 */
                function baseSortBy(array, comparer) {
                    var length = array.length;

                    array.sort(comparer);
                    while (length--) {
                        array[length] = array[length].value;
                    }
                    return array;
                }

                module.exports = baseSortBy;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_compareAscending.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var isSymbol = __webpack_require__("../../node_modules/lodash/isSymbol.js");

                /**
                 * Compares values to sort them in ascending order.
                 *
                 * @private
                 * @param {*} value The value to compare.
                 * @param {*} other The other value to compare.
                 * @returns {number} Returns the sort order indicator for `value`.
                 */
                function compareAscending(value, other) {
                    if (value !== other) {
                        var valIsDefined = value !== undefined,
                            valIsNull = value === null,
                            valIsReflexive = value === value,
                            valIsSymbol = isSymbol(value);

                        var othIsDefined = other !== undefined,
                            othIsNull = other === null,
                            othIsReflexive = other === other,
                            othIsSymbol = isSymbol(other);

                        if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||
                            (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||
                            (valIsNull && othIsDefined && othIsReflexive) ||
                            (!valIsDefined && othIsReflexive) ||
                            !valIsReflexive) {
                            return 1;
                        }
                        if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||
                            (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||
                            (othIsNull && valIsDefined && valIsReflexive) ||
                            (!othIsDefined && valIsReflexive) ||
                            !othIsReflexive) {
                            return -1;
                        }
                    }
                    return 0;
                }

                module.exports = compareAscending;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_compareMultiple.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var compareAscending = __webpack_require__("../../node_modules/lodash/_compareAscending.js");

                /**
                 * Used by `_.orderBy` to compare multiple properties of a value to another
                 * and stable sort them.
                 *
                 * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,
                 * specify an order of "desc" for descending or "asc" for ascending sort order
                 * of corresponding values.
                 *
                 * @private
                 * @param {Object} object The object to compare.
                 * @param {Object} other The other object to compare.
                 * @param {boolean[]|string[]} orders The order to sort by for each property.
                 * @returns {number} Returns the sort order indicator for `object`.
                 */
                function compareMultiple(object, other, orders) {
                    var index = -1,
                        objCriteria = object.criteria,
                        othCriteria = other.criteria,
                        length = objCriteria.length,
                        ordersLength = orders.length;

                    while (++index < length) {
                        var result = compareAscending(objCriteria[index], othCriteria[index]);
                        if (result) {
                            if (index >= ordersLength) {
                                return result;
                            }
                            var order = orders[index];
                            return result * (order == 'desc' ? -1 : 1);
                        }
                    }
                    // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications
                    // that causes it, under certain circumstances, to provide the same value for
                    // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247
                    // for more details.
                    //
                    // This also ensures a stable sort in V8 and other engines.
                    // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.
                    return object.index - other.index;
                }

                module.exports = compareMultiple;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_createBaseEach.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var isArrayLike = __webpack_require__("../../node_modules/lodash/isArrayLike.js");

                /**
                 * Creates a `baseEach` or `baseEachRight` function.
                 *
                 * @private
                 * @param {Function} eachFunc The function to iterate over a collection.
                 * @param {boolean} [fromRight] Specify iterating from right to left.
                 * @returns {Function} Returns the new base function.
                 */
                function createBaseEach(eachFunc, fromRight) {
                    return function(collection, iteratee) {
                        if (collection == null) {
                            return collection;
                        }
                        if (!isArrayLike(collection)) {
                            return eachFunc(collection, iteratee);
                        }
                        var length = collection.length,
                            index = fromRight ? length : -1,
                            iterable = Object(collection);

                        while ((fromRight ? index-- : ++index < length)) {
                            if (iteratee(iterable[index], index, iterable) === false) {
                                break;
                            }
                        }
                        return collection;
                    };
                }

                module.exports = createBaseEach;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_createBaseFor.js":
            /***/
            ((module) => {

                /**
                 * Creates a base function for methods like `_.forIn` and `_.forOwn`.
                 *
                 * @private
                 * @param {boolean} [fromRight] Specify iterating from right to left.
                 * @returns {Function} Returns the new base function.
                 */
                function createBaseFor(fromRight) {
                    return function(object, iteratee, keysFunc) {
                        var index = -1,
                            iterable = Object(object),
                            props = keysFunc(object),
                            length = props.length;

                        while (length--) {
                            var key = props[fromRight ? length : ++index];
                            if (iteratee(iterable[key], key, iterable) === false) {
                                break;
                            }
                        }
                        return object;
                    };
                }

                module.exports = createBaseFor;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_isFlattenable.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var Symbol = __webpack_require__("../../node_modules/lodash/_Symbol.js"),
                    isArguments = __webpack_require__("../../node_modules/lodash/isArguments.js"),
                    isArray = __webpack_require__("../../node_modules/lodash/isArray.js");

                /** Built-in value references. */
                var spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;

                /**
                 * Checks if `value` is a flattenable `arguments` object or array.
                 *
                 * @private
                 * @param {*} value The value to check.
                 * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.
                 */
                function isFlattenable(value) {
                    return isArray(value) || isArguments(value) ||
                        !!(spreadableSymbol && value && value[spreadableSymbol]);
                }

                module.exports = isFlattenable;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_isIterateeCall.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var eq = __webpack_require__("../../node_modules/lodash/eq.js"),
                    isArrayLike = __webpack_require__("../../node_modules/lodash/isArrayLike.js"),
                    isIndex = __webpack_require__("../../node_modules/lodash/_isIndex.js"),
                    isObject = __webpack_require__("../../node_modules/lodash/isObject.js");

                /**
                 * Checks if the given arguments are from an iteratee call.
                 *
                 * @private
                 * @param {*} value The potential iteratee value argument.
                 * @param {*} index The potential iteratee index or key argument.
                 * @param {*} object The potential iteratee object argument.
                 * @returns {boolean} Returns `true` if the arguments are from an iteratee call,
                 *  else `false`.
                 */
                function isIterateeCall(value, index, object) {
                    if (!isObject(object)) {
                        return false;
                    }
                    var type = typeof index;
                    if (type == 'number' ?
                        (isArrayLike(object) && isIndex(index, object.length)) :
                        (type == 'string' && index in object)
                    ) {
                        return eq(object[index], value);
                    }
                    return false;
                }

                module.exports = isIterateeCall;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_overRest.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var apply = __webpack_require__("../../node_modules/lodash/_apply.js");

                /* Built-in method references for those with the same name as other `lodash` methods. */
                var nativeMax = Math.max;

                /**
                 * A specialized version of `baseRest` which transforms the rest array.
                 *
                 * @private
                 * @param {Function} func The function to apply a rest parameter to.
                 * @param {number} [start=func.length-1] The start position of the rest parameter.
                 * @param {Function} transform The rest array transform.
                 * @returns {Function} Returns the new function.
                 */
                function overRest(func, start, transform) {
                    start = nativeMax(start === undefined ? (func.length - 1) : start, 0);
                    return function() {
                        var args = arguments,
                            index = -1,
                            length = nativeMax(args.length - start, 0),
                            array = Array(length);

                        while (++index < length) {
                            array[index] = args[start + index];
                        }
                        index = -1;
                        var otherArgs = Array(start + 1);
                        while (++index < start) {
                            otherArgs[index] = args[index];
                        }
                        otherArgs[start] = transform(array);
                        return apply(func, this, otherArgs);
                    };
                }

                module.exports = overRest;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_setToString.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var baseSetToString = __webpack_require__("../../node_modules/lodash/_baseSetToString.js"),
                    shortOut = __webpack_require__("../../node_modules/lodash/_shortOut.js");

                /**
                 * Sets the `toString` method of `func` to return `string`.
                 *
                 * @private
                 * @param {Function} func The function to modify.
                 * @param {Function} string The `toString` result.
                 * @returns {Function} Returns `func`.
                 */
                var setToString = shortOut(baseSetToString);

                module.exports = setToString;


                /***/
            }),

        /***/
        "../../node_modules/lodash/_shortOut.js":
            /***/
            ((module) => {

                /** Used to detect hot functions by number of calls within a span of milliseconds. */
                var HOT_COUNT = 800,
                    HOT_SPAN = 16;

                /* Built-in method references for those with the same name as other `lodash` methods. */
                var nativeNow = Date.now;

                /**
                 * Creates a function that'll short out and invoke `identity` instead
                 * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`
                 * milliseconds.
                 *
                 * @private
                 * @param {Function} func The function to restrict.
                 * @returns {Function} Returns the new shortable function.
                 */
                function shortOut(func) {
                    var count = 0,
                        lastCalled = 0;

                    return function() {
                        var stamp = nativeNow(),
                            remaining = HOT_SPAN - (stamp - lastCalled);

                        lastCalled = stamp;
                        if (remaining > 0) {
                            if (++count >= HOT_COUNT) {
                                return arguments[0];
                            }
                        } else {
                            count = 0;
                        }
                        return func.apply(undefined, arguments);
                    };
                }

                module.exports = shortOut;


                /***/
            }),

        /***/
        "../../node_modules/lodash/constant.js":
            /***/
            ((module) => {

                /**
                 * Creates a function that returns `value`.
                 *
                 * @static
                 * @memberOf _
                 * @since 2.4.0
                 * @category Util
                 * @param {*} value The value to return from the new function.
                 * @returns {Function} Returns the new constant function.
                 * @example
                 *
                 * var objects = _.times(2, _.constant({ 'a': 1 }));
                 *
                 * console.log(objects);
                 * // => [{ 'a': 1 }, { 'a': 1 }]
                 *
                 * console.log(objects[0] === objects[1]);
                 * // => true
                 */
                function constant(value) {
                    return function() {
                        return value;
                    };
                }

                module.exports = constant;


                /***/
            }),

        /***/
        "../../node_modules/lodash/first.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                module.exports = __webpack_require__("../../node_modules/lodash/head.js");


                /***/
            }),

        /***/
        "../../node_modules/lodash/head.js":
            /***/
            ((module) => {

                /**
                 * Gets the first element of `array`.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @alias first
                 * @category Array
                 * @param {Array} array The array to query.
                 * @returns {*} Returns the first element of `array`.
                 * @example
                 *
                 * _.head([1, 2, 3]);
                 * // => 1
                 *
                 * _.head([]);
                 * // => undefined
                 */
                function head(array) {
                    return (array && array.length) ? array[0] : undefined;
                }

                module.exports = head;


                /***/
            }),

        /***/
        "../../node_modules/lodash/isEqual.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var baseIsEqual = __webpack_require__("../../node_modules/lodash/_baseIsEqual.js");

                /**
                 * Performs a deep comparison between two values to determine if they are
                 * equivalent.
                 *
                 * **Note:** This method supports comparing arrays, array buffers, booleans,
                 * date objects, error objects, maps, numbers, `Object` objects, regexes,
                 * sets, strings, symbols, and typed arrays. `Object` objects are compared
                 * by their own, not inherited, enumerable properties. Functions and DOM
                 * nodes are compared by strict equality, i.e. `===`.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Lang
                 * @param {*} value The value to compare.
                 * @param {*} other The other value to compare.
                 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
                 * @example
                 *
                 * var object = { 'a': 1 };
                 * var other = { 'a': 1 };
                 *
                 * _.isEqual(object, other);
                 * // => true
                 *
                 * object === other;
                 * // => false
                 */
                function isEqual(value, other) {
                    return baseIsEqual(value, other);
                }

                module.exports = isEqual;


                /***/
            }),

        /***/
        "../../node_modules/lodash/last.js":
            /***/
            ((module) => {

                /**
                 * Gets the last element of `array`.
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Array
                 * @param {Array} array The array to query.
                 * @returns {*} Returns the last element of `array`.
                 * @example
                 *
                 * _.last([1, 2, 3]);
                 * // => 3
                 */
                function last(array) {
                    var length = array == null ? 0 : array.length;
                    return length ? array[length - 1] : undefined;
                }

                module.exports = last;


                /***/
            }),

        /***/
        "../../node_modules/lodash/sortBy.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var baseFlatten = __webpack_require__("../../node_modules/lodash/_baseFlatten.js"),
                    baseOrderBy = __webpack_require__("../../node_modules/lodash/_baseOrderBy.js"),
                    baseRest = __webpack_require__("../../node_modules/lodash/_baseRest.js"),
                    isIterateeCall = __webpack_require__("../../node_modules/lodash/_isIterateeCall.js");

                /**
                 * Creates an array of elements, sorted in ascending order by the results of
                 * running each element in a collection thru each iteratee. This method
                 * performs a stable sort, that is, it preserves the original sort order of
                 * equal elements. The iteratees are invoked with one argument: (value).
                 *
                 * @static
                 * @memberOf _
                 * @since 0.1.0
                 * @category Collection
                 * @param {Array|Object} collection The collection to iterate over.
                 * @param {...(Function|Function[])} [iteratees=[_.identity]]
                 *  The iteratees to sort by.
                 * @returns {Array} Returns the new sorted array.
                 * @example
                 *
                 * var users = [
                 *   { 'user': 'fred',   'age': 48 },
                 *   { 'user': 'barney', 'age': 36 },
                 *   { 'user': 'fred',   'age': 30 },
                 *   { 'user': 'barney', 'age': 34 }
                 * ];
                 *
                 * _.sortBy(users, [function(o) { return o.user; }]);
                 * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 30]]
                 *
                 * _.sortBy(users, ['user', 'age']);
                 * // => objects for [['barney', 34], ['barney', 36], ['fred', 30], ['fred', 48]]
                 */
                var sortBy = baseRest(function(collection, iteratees) {
                    if (collection == null) {
                        return [];
                    }
                    var length = iteratees.length;
                    if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {
                        iteratees = [];
                    } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {
                        iteratees = [iteratees[0]];
                    }
                    return baseOrderBy(collection, baseFlatten(iteratees, 1), []);
                });

                module.exports = sortBy;


                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_lodash_first_js-node_modules_lodash_isEqual_js-node_modules_lodash_last_-e55292.b9ffc24c007668d0.js.map