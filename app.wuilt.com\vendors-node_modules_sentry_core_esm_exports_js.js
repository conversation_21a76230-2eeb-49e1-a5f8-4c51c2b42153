(self["webpackChunkapp_shell"] = self["webpackChunkapp_shell"] || []).push([
    ["vendors-node_modules_sentry_core_esm_exports_js"], {

        /***/
        "../../node_modules/@sentry/core/esm/constants.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    DEFAULT_ENVIRONMENT: () => ( /* binding */ DEFAULT_ENVIRONMENT)
                    /* harmony export */
                });
                const DEFAULT_ENVIRONMENT = 'production';




                /***/
            }),

        /***/
        "../../node_modules/@sentry/core/esm/exports.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    addBreadcrumb: () => ( /* binding */ addBreadcrumb),
                    /* harmony export */
                    captureCheckIn: () => ( /* binding */ captureCheckIn),
                    /* harmony export */
                    captureEvent: () => ( /* binding */ captureEvent),
                    /* harmony export */
                    captureException: () => ( /* binding */ captureException),
                    /* harmony export */
                    captureMessage: () => ( /* binding */ captureMessage),
                    /* harmony export */
                    close: () => ( /* binding */ close),
                    /* harmony export */
                    configureScope: () => ( /* binding */ configureScope),
                    /* harmony export */
                    flush: () => ( /* binding */ flush),
                    /* harmony export */
                    lastEventId: () => ( /* binding */ lastEventId),
                    /* harmony export */
                    setContext: () => ( /* binding */ setContext),
                    /* harmony export */
                    setExtra: () => ( /* binding */ setExtra),
                    /* harmony export */
                    setExtras: () => ( /* binding */ setExtras),
                    /* harmony export */
                    setTag: () => ( /* binding */ setTag),
                    /* harmony export */
                    setTags: () => ( /* binding */ setTags),
                    /* harmony export */
                    setUser: () => ( /* binding */ setUser),
                    /* harmony export */
                    startTransaction: () => ( /* binding */ startTransaction),
                    /* harmony export */
                    withScope: () => ( /* binding */ withScope)
                    /* harmony export */
                });
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@sentry/utils/esm/logger.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@sentry/utils/esm/misc.js");
                /* harmony import */
                var _hub_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/core/esm/hub.js");



                // Note: All functions in this file are typed with a return value of `ReturnType<Hub[HUB_FUNCTION]>`,
                // where HUB_FUNCTION is some method on the Hub class.
                //
                // This is done to make sure the top level SDK methods stay in sync with the hub methods.
                // Although every method here has an explicit return type, some of them (that map to void returns) do not
                // contain `return` keywords. This is done to save on bundle size, as `return` is not minifiable.

                /**
                 * Captures an exception event and sends it to Sentry.
                 *
                 * @param exception An exception-like object.
                 * @param captureContext Additional scope data to apply to exception event.
                 * @returns The generated eventId.
                 */
                // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types
                function captureException(exception, captureContext) {
                    return (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().captureException(exception, {
                        captureContext
                    });
                }

                /**
                 * Captures a message event and sends it to Sentry.
                 *
                 * @param message The message to send to Sentry.
                 * @param Severity Define the level of the message.
                 * @returns The generated eventId.
                 */
                function captureMessage(
                    message,
                    // eslint-disable-next-line deprecation/deprecation
                    captureContext,
                ) {
                    // This is necessary to provide explicit scopes upgrade, without changing the original
                    // arity of the `captureMessage(message, level)` method.
                    const level = typeof captureContext === 'string' ? captureContext : undefined;
                    const context = typeof captureContext !== 'string' ? {
                        captureContext
                    } : undefined;
                    return (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().captureMessage(message, level, context);
                }

                /**
                 * Captures a manually created event and sends it to Sentry.
                 *
                 * @param event The event to send to Sentry.
                 * @returns The generated eventId.
                 */
                function captureEvent(event, hint) {
                    return (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().captureEvent(event, hint);
                }

                /**
                 * Callback to set context information onto the scope.
                 * @param callback Callback function that receives Scope.
                 */
                function configureScope(callback) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().configureScope(callback);
                }

                /**
                 * Records a new breadcrumb which will be attached to future events.
                 *
                 * Breadcrumbs will be added to subsequent events to provide more context on
                 * user's actions prior to an error or crash.
                 *
                 * @param breadcrumb The breadcrumb to record.
                 */
                function addBreadcrumb(breadcrumb) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().addBreadcrumb(breadcrumb);
                }

                /**
                 * Sets context data with the given name.
                 * @param name of the context
                 * @param context Any kind of data. This data will be normalized.
                 */
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                function setContext(name, context) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().setContext(name, context);
                }

                /**
                 * Set an object that will be merged sent as extra data with the event.
                 * @param extras Extras object to merge into current context.
                 */
                function setExtras(extras) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().setExtras(extras);
                }

                /**
                 * Set key:value that will be sent as extra data with the event.
                 * @param key String of extra
                 * @param extra Any kind of data. This data will be normalized.
                 */
                function setExtra(key, extra) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().setExtra(key, extra);
                }

                /**
                 * Set an object that will be merged sent as tags data with the event.
                 * @param tags Tags context object to merge into current context.
                 */
                function setTags(tags) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().setTags(tags);
                }

                /**
                 * Set key:value that will be sent as tags data with the event.
                 *
                 * Can also be used to unset a tag, by passing `undefined`.
                 *
                 * @param key String key of tag
                 * @param value Value of tag
                 */
                function setTag(key, value) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().setTag(key, value);
                }

                /**
                 * Updates user context information for future events.
                 *
                 * @param user User context object to be set in the current context. Pass `null` to unset the user.
                 */
                function setUser(user) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().setUser(user);
                }

                /**
                 * Creates a new scope with and executes the given operation within.
                 * The scope is automatically removed once the operation
                 * finishes or throws.
                 *
                 * This is essentially a convenience function for:
                 *
                 *     pushScope();
                 *     callback();
                 *     popScope();
                 *
                 * @param callback that will be enclosed into push/popScope.
                 */
                function withScope(callback) {
                    (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().withScope(callback);
                }

                /**
                 * Starts a new `Transaction` and returns it. This is the entry point to manual tracing instrumentation.
                 *
                 * A tree structure can be built by adding child spans to the transaction, and child spans to other spans. To start a
                 * new child span within the transaction or any span, call the respective `.startChild()` method.
                 *
                 * Every child span must be finished before the transaction is finished, otherwise the unfinished spans are discarded.
                 *
                 * The transaction must be finished with a call to its `.finish()` method, at which point the transaction with all its
                 * finished child spans will be sent to Sentry.
                 *
                 * NOTE: This function should only be used for *manual* instrumentation. Auto-instrumentation should call
                 * `startTransaction` directly on the hub.
                 *
                 * @param context Properties of the new `Transaction`.
                 * @param customSamplingContext Information given to the transaction sampling function (along with context-dependent
                 * default values). See {@link Options.tracesSampler}.
                 *
                 * @returns The transaction which was just started
                 */
                function startTransaction(
                    context,
                    customSamplingContext,
                ) {
                    return (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().startTransaction({ ...context
                    }, customSamplingContext);
                }

                /**
                 * Create a cron monitor check in and send it to Sentry.
                 *
                 * @param checkIn An object that describes a check in.
                 * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want
                 * to create a monitor automatically when sending a check in.
                 */
                function captureCheckIn(checkIn, upsertMonitorConfig) {
                    const hub = (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)();
                    const scope = hub.getScope();
                    const client = hub.getClient();
                    if (!client) {
                        (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.logger.warn('Cannot capture check-in. No client defined.');
                    } else if (!client.captureCheckIn) {
                        (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.logger.warn('Cannot capture check-in. Client does not support sending check-ins.');
                    } else {
                        return client.captureCheckIn(checkIn, upsertMonitorConfig, scope);
                    }

                    return (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_2__.uuid4)();
                }

                /**
                 * Call `flush()` on the current client, if there is one. See {@link Client.flush}.
                 *
                 * @param timeout Maximum time in ms the client should wait to flush its event queue. Omitting this parameter will cause
                 * the client to wait until all events are sent before resolving the promise.
                 * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it
                 * doesn't (or if there's no client defined).
                 */
                async function flush(timeout) {
                    const client = (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().getClient();
                    if (client) {
                        return client.flush(timeout);
                    }
                    (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.logger.warn('Cannot flush events. No client defined.');
                    return Promise.resolve(false);
                }

                /**
                 * Call `close()` on the current client, if there is one. See {@link Client.close}.
                 *
                 * @param timeout Maximum time in ms the client should wait to flush its event queue before shutting down. Omitting this
                 * parameter will cause the client to wait until all events are sent before disabling itself.
                 * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it
                 * doesn't (or if there's no client defined).
                 */
                async function close(timeout) {
                    const client = (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().getClient();
                    if (client) {
                        return client.close(timeout);
                    }
                    (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.logger.warn('Cannot flush events and disable SDK. No client defined.');
                    return Promise.resolve(false);
                }

                /**
                 * This is the getter for lastEventId.
                 *
                 * @returns The last event id of a captured event.
                 */
                function lastEventId() {
                    return (0, _hub_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentHub)().lastEventId();
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/core/esm/hub.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    API_VERSION: () => ( /* binding */ API_VERSION),
                    /* harmony export */
                    Hub: () => ( /* binding */ Hub),
                    /* harmony export */
                    ensureHubOnCarrier: () => ( /* binding */ ensureHubOnCarrier),
                    /* harmony export */
                    getCurrentHub: () => ( /* binding */ getCurrentHub),
                    /* harmony export */
                    getHubFromCarrier: () => ( /* binding */ getHubFromCarrier),
                    /* harmony export */
                    getMainCarrier: () => ( /* binding */ getMainCarrier),
                    /* harmony export */
                    makeMain: () => ( /* binding */ makeMain),
                    /* harmony export */
                    runWithAsyncContext: () => ( /* binding */ runWithAsyncContext),
                    /* harmony export */
                    setAsyncContextStrategy: () => ( /* binding */ setAsyncContextStrategy),
                    /* harmony export */
                    setHubOnCarrier: () => ( /* binding */ setHubOnCarrier)
                    /* harmony export */
                });
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@sentry/utils/esm/misc.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@sentry/utils/esm/time.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@sentry/utils/esm/logger.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__("../../node_modules/@sentry/utils/esm/worldwide.js");
                /* harmony import */
                var _constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/@sentry/core/esm/constants.js");
                /* harmony import */
                var _scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/core/esm/scope.js");
                /* harmony import */
                var _session_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/@sentry/core/esm/session.js");





                /**
                 * API compatibility version of this hub.
                 *
                 * WARNING: This number should only be increased when the global interface
                 * changes and new methods are introduced.
                 *
                 * @hidden
                 */
                const API_VERSION = 4;

                /**
                 * Default maximum number of breadcrumbs added to an event. Can be overwritten
                 * with {@link Options.maxBreadcrumbs}.
                 */
                const DEFAULT_BREADCRUMBS = 100;

                /**
                 * @inheritDoc
                 */
                class Hub {
                    /** Is a {@link Layer}[] containing the client and scope */

                    /** Contains the last event id of a captured event.  */

                    /**
                     * Creates a new instance of the hub, will push one {@link Layer} into the
                     * internal stack on creation.
                     *
                     * @param client bound to the hub.
                     * @param scope bound to the hub.
                     * @param version number, higher number means higher priority.
                     */
                    constructor(client, scope = new _scope_js__WEBPACK_IMPORTED_MODULE_0__.Scope(), _version = API_VERSION) {
                        this._version = _version;
                        this._stack = [{
                            scope
                        }];
                        if (client) {
                            this.bindClient(client);
                        }
                    }

                    /**
                     * @inheritDoc
                     */
                    isOlderThan(version) {
                        return this._version < version;
                    }

                    /**
                     * @inheritDoc
                     */
                    bindClient(client) {
                        const top = this.getStackTop();
                        top.client = client;
                        if (client && client.setupIntegrations) {
                            client.setupIntegrations();
                        }
                    }

                    /**
                     * @inheritDoc
                     */
                    pushScope() {
                        // We want to clone the content of prev scope
                        const scope = _scope_js__WEBPACK_IMPORTED_MODULE_0__.Scope.clone(this.getScope());
                        this.getStack().push({
                            client: this.getClient(),
                            scope,
                        });
                        return scope;
                    }

                    /**
                     * @inheritDoc
                     */
                    popScope() {
                        if (this.getStack().length <= 1) return false;
                        return !!this.getStack().pop();
                    }

                    /**
                     * @inheritDoc
                     */
                    withScope(callback) {
                        const scope = this.pushScope();
                        try {
                            callback(scope);
                        } finally {
                            this.popScope();
                        }
                    }

                    /**
                     * @inheritDoc
                     */
                    getClient() {
                        return this.getStackTop().client;
                    }

                    /** Returns the scope of the top stack. */
                    getScope() {
                        return this.getStackTop().scope;
                    }

                    /** Returns the scope stack for domains or the process. */
                    getStack() {
                        return this._stack;
                    }

                    /** Returns the topmost scope layer in the order domain > local > process. */
                    getStackTop() {
                        return this._stack[this._stack.length - 1];
                    }

                    /**
                     * @inheritDoc
                     */
                    captureException(exception, hint) {
                        const eventId = (this._lastEventId = hint && hint.event_id ? hint.event_id : (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.uuid4)());
                        const syntheticException = new Error('Sentry syntheticException');
                        this._withClient((client, scope) => {
                            client.captureException(
                                exception, {
                                    originalException: exception,
                                    syntheticException,
                                    ...hint,
                                    event_id: eventId,
                                },
                                scope,
                            );
                        });
                        return eventId;
                    }

                    /**
                     * @inheritDoc
                     */
                    captureMessage(
                        message,
                        // eslint-disable-next-line deprecation/deprecation
                        level,
                        hint,
                    ) {
                        const eventId = (this._lastEventId = hint && hint.event_id ? hint.event_id : (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.uuid4)());
                        const syntheticException = new Error(message);
                        this._withClient((client, scope) => {
                            client.captureMessage(
                                message,
                                level, {
                                    originalException: message,
                                    syntheticException,
                                    ...hint,
                                    event_id: eventId,
                                },
                                scope,
                            );
                        });
                        return eventId;
                    }

                    /**
                     * @inheritDoc
                     */
                    captureEvent(event, hint) {
                        const eventId = hint && hint.event_id ? hint.event_id : (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.uuid4)();
                        if (!event.type) {
                            this._lastEventId = eventId;
                        }

                        this._withClient((client, scope) => {
                            client.captureEvent(event, { ...hint,
                                event_id: eventId
                            }, scope);
                        });
                        return eventId;
                    }

                    /**
                     * @inheritDoc
                     */
                    lastEventId() {
                        return this._lastEventId;
                    }

                    /**
                     * @inheritDoc
                     */
                    addBreadcrumb(breadcrumb, hint) {
                        const {
                            scope,
                            client
                        } = this.getStackTop();

                        if (!client) return;

                        const {
                            beforeBreadcrumb = null, maxBreadcrumbs = DEFAULT_BREADCRUMBS
                        } =
                        (client.getOptions && client.getOptions()) || {};

                        if (maxBreadcrumbs <= 0) return;

                        const timestamp = (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_2__.dateTimestampInSeconds)();
                        const mergedBreadcrumb = {
                            timestamp,
                            ...breadcrumb
                        };
                        const finalBreadcrumb = beforeBreadcrumb ?
                            ((0, _sentry_utils__WEBPACK_IMPORTED_MODULE_3__.consoleSandbox)(() => beforeBreadcrumb(mergedBreadcrumb, hint))) :
                            mergedBreadcrumb;

                        if (finalBreadcrumb === null) return;

                        if (client.emit) {
                            client.emit('beforeAddBreadcrumb', finalBreadcrumb, hint);
                        }

                        scope.addBreadcrumb(finalBreadcrumb, maxBreadcrumbs);
                    }

                    /**
                     * @inheritDoc
                     */
                    setUser(user) {
                        this.getScope().setUser(user);
                    }

                    /**
                     * @inheritDoc
                     */
                    setTags(tags) {
                        this.getScope().setTags(tags);
                    }

                    /**
                     * @inheritDoc
                     */
                    setExtras(extras) {
                        this.getScope().setExtras(extras);
                    }

                    /**
                     * @inheritDoc
                     */
                    setTag(key, value) {
                        this.getScope().setTag(key, value);
                    }

                    /**
                     * @inheritDoc
                     */
                    setExtra(key, extra) {
                        this.getScope().setExtra(key, extra);
                    }

                    /**
                     * @inheritDoc
                     */
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    setContext(name, context) {
                        this.getScope().setContext(name, context);
                    }

                    /**
                     * @inheritDoc
                     */
                    configureScope(callback) {
                        const {
                            scope,
                            client
                        } = this.getStackTop();
                        if (client) {
                            callback(scope);
                        }
                    }

                    /**
                     * @inheritDoc
                     */
                    run(callback) {
                        const oldHub = makeMain(this);
                        try {
                            callback(this);
                        } finally {
                            makeMain(oldHub);
                        }
                    }

                    /**
                     * @inheritDoc
                     */
                    getIntegration(integration) {
                        const client = this.getClient();
                        if (!client) return null;
                        try {
                            return client.getIntegration(integration);
                        } catch (_oO) {
                            (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && _sentry_utils__WEBPACK_IMPORTED_MODULE_3__.logger.warn(`Cannot retrieve integration ${integration.id} from the current Hub`);
                            return null;
                        }
                    }

                    /**
                     * @inheritDoc
                     */
                    startTransaction(context, customSamplingContext) {
                        const result = this._callExtensionMethod('startTransaction', context, customSamplingContext);

                        if ((typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && !result) {
                            const client = this.getClient();
                            if (!client) {
                                // eslint-disable-next-line no-console
                                console.warn(
                                    "Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'",
                                );
                            } else {
                                // eslint-disable-next-line no-console
                                console.warn(`Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':
Sentry.addTracingExtensions();
Sentry.init({...});
`);
                            }
                        }

                        return result;
                    }

                    /**
                     * @inheritDoc
                     */
                    traceHeaders() {
                        return this._callExtensionMethod('traceHeaders');
                    }

                    /**
                     * @inheritDoc
                     */
                    captureSession(endSession = false) {
                        // both send the update and pull the session from the scope
                        if (endSession) {
                            return this.endSession();
                        }

                        // only send the update
                        this._sendSessionUpdate();
                    }

                    /**
                     * @inheritDoc
                     */
                    endSession() {
                        const layer = this.getStackTop();
                        const scope = layer.scope;
                        const session = scope.getSession();
                        if (session) {
                            (0, _session_js__WEBPACK_IMPORTED_MODULE_4__.closeSession)(session);
                        }
                        this._sendSessionUpdate();

                        // the session is over; take it off of the scope
                        scope.setSession();
                    }

                    /**
                     * @inheritDoc
                     */
                    startSession(context) {
                        const {
                            scope,
                            client
                        } = this.getStackTop();
                        const {
                            release,
                            environment = _constants_js__WEBPACK_IMPORTED_MODULE_5__.DEFAULT_ENVIRONMENT
                        } = (client && client.getOptions()) || {};

                        // Will fetch userAgent if called from browser sdk
                        const {
                            userAgent
                        } = _sentry_utils__WEBPACK_IMPORTED_MODULE_6__.GLOBAL_OBJ.navigator || {};

                        const session = (0, _session_js__WEBPACK_IMPORTED_MODULE_4__.makeSession)({
                            release,
                            environment,
                            user: scope.getUser(),
                            ...(userAgent && {
                                userAgent
                            }),
                            ...context,
                        });

                        // End existing session if there's one
                        const currentSession = scope.getSession && scope.getSession();
                        if (currentSession && currentSession.status === 'ok') {
                            (0, _session_js__WEBPACK_IMPORTED_MODULE_4__.updateSession)(currentSession, {
                                status: 'exited'
                            });
                        }
                        this.endSession();

                        // Afterwards we set the new session on the scope
                        scope.setSession(session);

                        return session;
                    }

                    /**
                     * Returns if default PII should be sent to Sentry and propagated in ourgoing requests
                     * when Tracing is used.
                     */
                    shouldSendDefaultPii() {
                        const client = this.getClient();
                        const options = client && client.getOptions();
                        return Boolean(options && options.sendDefaultPii);
                    }

                    /**
                     * Sends the current Session on the scope
                     */
                    _sendSessionUpdate() {
                        const {
                            scope,
                            client
                        } = this.getStackTop();

                        const session = scope.getSession();
                        if (session && client && client.captureSession) {
                            client.captureSession(session);
                        }
                    }

                    /**
                     * Internal helper function to call a method on the top client if it exists.
                     *
                     * @param method The method to call on the client.
                     * @param args Arguments to pass to the client function.
                     */
                    _withClient(callback) {
                        const {
                            scope,
                            client
                        } = this.getStackTop();
                        if (client) {
                            callback(client, scope);
                        }
                    }

                    /**
                     * Calls global extension method and binding current instance to the function call
                     */
                    // @ts-ignore Function lacks ending return statement and return type does not include 'undefined'. ts(2366)
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    _callExtensionMethod(method, ...args) {
                        const carrier = getMainCarrier();
                        const sentry = carrier.__SENTRY__;
                        if (sentry && sentry.extensions && typeof sentry.extensions[method] === 'function') {
                            return sentry.extensions[method].apply(this, args);
                        }
                        (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && _sentry_utils__WEBPACK_IMPORTED_MODULE_3__.logger.warn(`Extension method ${method} couldn't be found, doing nothing.`);
                    }
                }

                /**
                 * Returns the global shim registry.
                 *
                 * FIXME: This function is problematic, because despite always returning a valid Carrier,
                 * it has an optional `__SENTRY__` property, which then in turn requires us to always perform an unnecessary check
                 * at the call-site. We always access the carrier through this function, so we can guarantee that `__SENTRY__` is there.
                 **/
                function getMainCarrier() {
                    _sentry_utils__WEBPACK_IMPORTED_MODULE_6__.GLOBAL_OBJ.__SENTRY__ = _sentry_utils__WEBPACK_IMPORTED_MODULE_6__.GLOBAL_OBJ.__SENTRY__ || {
                        extensions: {},
                        hub: undefined,
                    };
                    return _sentry_utils__WEBPACK_IMPORTED_MODULE_6__.GLOBAL_OBJ;
                }

                /**
                 * Replaces the current main hub with the passed one on the global object
                 *
                 * @returns The old replaced hub
                 */
                function makeMain(hub) {
                    const registry = getMainCarrier();
                    const oldHub = getHubFromCarrier(registry);
                    setHubOnCarrier(registry, hub);
                    return oldHub;
                }

                /**
                 * Returns the default hub instance.
                 *
                 * If a hub is already registered in the global carrier but this module
                 * contains a more recent version, it replaces the registered version.
                 * Otherwise, the currently registered hub will be returned.
                 */
                function getCurrentHub() {
                    // Get main carrier (global for every environment)
                    const registry = getMainCarrier();

                    if (registry.__SENTRY__ && registry.__SENTRY__.acs) {
                        const hub = registry.__SENTRY__.acs.getCurrentHub();

                        if (hub) {
                            return hub;
                        }
                    }

                    // Return hub that lives on a global object
                    return getGlobalHub(registry);
                }

                function getGlobalHub(registry = getMainCarrier()) {
                    // If there's no hub, or its an old API, assign a new one
                    if (!hasHubOnCarrier(registry) || getHubFromCarrier(registry).isOlderThan(API_VERSION)) {
                        setHubOnCarrier(registry, new Hub());
                    }

                    // Return hub that lives on a global object
                    return getHubFromCarrier(registry);
                }

                /**
                 * @private Private API with no semver guarantees!
                 *
                 * If the carrier does not contain a hub, a new hub is created with the global hub client and scope.
                 */
                function ensureHubOnCarrier(carrier, parent = getGlobalHub()) {
                    // If there's no hub on current domain, or it's an old API, assign a new one
                    if (!hasHubOnCarrier(carrier) || getHubFromCarrier(carrier).isOlderThan(API_VERSION)) {
                        const globalHubTopStack = parent.getStackTop();
                        setHubOnCarrier(carrier, new Hub(globalHubTopStack.client, _scope_js__WEBPACK_IMPORTED_MODULE_0__.Scope.clone(globalHubTopStack.scope)));
                    }
                }

                /**
                 * @private Private API with no semver guarantees!
                 *
                 * Sets the global async context strategy
                 */
                function setAsyncContextStrategy(strategy) {
                    // Get main carrier (global for every environment)
                    const registry = getMainCarrier();
                    registry.__SENTRY__ = registry.__SENTRY__ || {};
                    registry.__SENTRY__.acs = strategy;
                }

                /**
                 * Runs the supplied callback in its own async context. Async Context strategies are defined per SDK.
                 *
                 * @param callback The callback to run in its own async context
                 * @param options Options to pass to the async context strategy
                 * @returns The result of the callback
                 */
                function runWithAsyncContext(callback, options = {}) {
                    const registry = getMainCarrier();

                    if (registry.__SENTRY__ && registry.__SENTRY__.acs) {
                        return registry.__SENTRY__.acs.runWithAsyncContext(callback, options);
                    }

                    // if there was no strategy, fallback to just calling the callback
                    return callback();
                }

                /**
                 * This will tell whether a carrier has a hub on it or not
                 * @param carrier object
                 */
                function hasHubOnCarrier(carrier) {
                    return !!(carrier && carrier.__SENTRY__ && carrier.__SENTRY__.hub);
                }

                /**
                 * This will create a new {@link Hub} and add to the passed object on
                 * __SENTRY__.hub.
                 * @param carrier object
                 * @hidden
                 */
                function getHubFromCarrier(carrier) {
                    return (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_6__.getGlobalSingleton)('hub', () => new Hub(), carrier);
                }

                /**
                 * This will set passed {@link Hub} on the passed object's __SENTRY__.hub attribute
                 * @param carrier object
                 * @param hub Hub
                 * @returns A boolean indicating success or failure
                 */
                function setHubOnCarrier(carrier, hub) {
                    if (!carrier) return false;
                    const __SENTRY__ = (carrier.__SENTRY__ = carrier.__SENTRY__ || {});
                    __SENTRY__.hub = hub;
                    return true;
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/core/esm/scope.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    Scope: () => ( /* binding */ Scope),
                    /* harmony export */
                    addGlobalEventProcessor: () => ( /* binding */ addGlobalEventProcessor)
                    /* harmony export */
                });
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@sentry/utils/esm/is.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@sentry/utils/esm/time.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("../../node_modules/@sentry/utils/esm/syncpromise.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("../../node_modules/@sentry/utils/esm/logger.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("../../node_modules/@sentry/utils/esm/misc.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__("../../node_modules/@sentry/utils/esm/worldwide.js");
                /* harmony import */
                var _session_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/core/esm/session.js");



                /**
                 * Default value for maximum number of breadcrumbs added to an event.
                 */
                const DEFAULT_MAX_BREADCRUMBS = 100;

                /**
                 * Holds additional event information. {@link Scope.applyToEvent} will be
                 * called by the client before an event will be sent.
                 */
                class Scope {
                    /** Flag if notifying is happening. */

                    /** Callback for client to receive scope changes. */

                    /** Callback list that will be called after {@link applyToEvent}. */

                    /** Array of breadcrumbs. */

                    /** User */

                    /** Tags */

                    /** Extra */

                    /** Contexts */

                    /** Attachments */

                    /** Propagation Context for distributed tracing */

                    /**
                     * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get
                     * sent to Sentry
                     */

                    /** Fingerprint */

                    /** Severity */
                    // eslint-disable-next-line deprecation/deprecation

                    /** Transaction Name */

                    /** Span */

                    /** Session */

                    /** Request Mode Session Status */

                    // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.

                    constructor() {
                        this._notifyingListeners = false;
                        this._scopeListeners = [];
                        this._eventProcessors = [];
                        this._breadcrumbs = [];
                        this._attachments = [];
                        this._user = {};
                        this._tags = {};
                        this._extra = {};
                        this._contexts = {};
                        this._sdkProcessingMetadata = {};
                        this._propagationContext = generatePropagationContext();
                    }

                    /**
                     * Inherit values from the parent scope.
                     * @param scope to clone.
                     */
                    static clone(scope) {
                        const newScope = new Scope();
                        if (scope) {
                            newScope._breadcrumbs = [...scope._breadcrumbs];
                            newScope._tags = { ...scope._tags
                            };
                            newScope._extra = { ...scope._extra
                            };
                            newScope._contexts = { ...scope._contexts
                            };
                            newScope._user = scope._user;
                            newScope._level = scope._level;
                            newScope._span = scope._span;
                            newScope._session = scope._session;
                            newScope._transactionName = scope._transactionName;
                            newScope._fingerprint = scope._fingerprint;
                            newScope._eventProcessors = [...scope._eventProcessors];
                            newScope._requestSession = scope._requestSession;
                            newScope._attachments = [...scope._attachments];
                            newScope._sdkProcessingMetadata = { ...scope._sdkProcessingMetadata
                            };
                            newScope._propagationContext = { ...scope._propagationContext
                            };
                        }
                        return newScope;
                    }

                    /**
                     * Add internal on change listener. Used for sub SDKs that need to store the scope.
                     * @hidden
                     */
                    addScopeListener(callback) {
                        this._scopeListeners.push(callback);
                    }

                    /**
                     * @inheritDoc
                     */
                    addEventProcessor(callback) {
                        this._eventProcessors.push(callback);
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setUser(user) {
                        this._user = user || {};
                        if (this._session) {
                            (0, _session_js__WEBPACK_IMPORTED_MODULE_0__.updateSession)(this._session, {
                                user
                            });
                        }
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    getUser() {
                        return this._user;
                    }

                    /**
                     * @inheritDoc
                     */
                    getRequestSession() {
                        return this._requestSession;
                    }

                    /**
                     * @inheritDoc
                     */
                    setRequestSession(requestSession) {
                        this._requestSession = requestSession;
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setTags(tags) {
                        this._tags = {
                            ...this._tags,
                            ...tags,
                        };
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setTag(key, value) {
                        this._tags = { ...this._tags,
                            [key]: value
                        };
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setExtras(extras) {
                        this._extra = {
                            ...this._extra,
                            ...extras,
                        };
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setExtra(key, extra) {
                        this._extra = { ...this._extra,
                            [key]: extra
                        };
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setFingerprint(fingerprint) {
                        this._fingerprint = fingerprint;
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setLevel(
                        // eslint-disable-next-line deprecation/deprecation
                        level,
                    ) {
                        this._level = level;
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setTransactionName(name) {
                        this._transactionName = name;
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setContext(key, context) {
                        if (context === null) {
                            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
                            delete this._contexts[key];
                        } else {
                            this._contexts[key] = context;
                        }

                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setSpan(span) {
                        this._span = span;
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    getSpan() {
                        return this._span;
                    }

                    /**
                     * @inheritDoc
                     */
                    getTransaction() {
                        // Often, this span (if it exists at all) will be a transaction, but it's not guaranteed to be. Regardless, it will
                        // have a pointer to the currently-active transaction.
                        const span = this.getSpan();
                        return span && span.transaction;
                    }

                    /**
                     * @inheritDoc
                     */
                    setSession(session) {
                        if (!session) {
                            delete this._session;
                        } else {
                            this._session = session;
                        }
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    getSession() {
                        return this._session;
                    }

                    /**
                     * @inheritDoc
                     */
                    update(captureContext) {
                        if (!captureContext) {
                            return this;
                        }

                        if (typeof captureContext === 'function') {
                            const updatedScope = (captureContext)(this);
                            return updatedScope instanceof Scope ? updatedScope : this;
                        }

                        if (captureContext instanceof Scope) {
                            this._tags = { ...this._tags,
                                ...captureContext._tags
                            };
                            this._extra = { ...this._extra,
                                ...captureContext._extra
                            };
                            this._contexts = { ...this._contexts,
                                ...captureContext._contexts
                            };
                            if (captureContext._user && Object.keys(captureContext._user).length) {
                                this._user = captureContext._user;
                            }
                            if (captureContext._level) {
                                this._level = captureContext._level;
                            }
                            if (captureContext._fingerprint) {
                                this._fingerprint = captureContext._fingerprint;
                            }
                            if (captureContext._requestSession) {
                                this._requestSession = captureContext._requestSession;
                            }
                            if (captureContext._propagationContext) {
                                this._propagationContext = captureContext._propagationContext;
                            }
                        } else if ((0, _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(captureContext)) {
                            // eslint-disable-next-line no-param-reassign
                            captureContext = captureContext;
                            this._tags = { ...this._tags,
                                ...captureContext.tags
                            };
                            this._extra = { ...this._extra,
                                ...captureContext.extra
                            };
                            this._contexts = { ...this._contexts,
                                ...captureContext.contexts
                            };
                            if (captureContext.user) {
                                this._user = captureContext.user;
                            }
                            if (captureContext.level) {
                                this._level = captureContext.level;
                            }
                            if (captureContext.fingerprint) {
                                this._fingerprint = captureContext.fingerprint;
                            }
                            if (captureContext.requestSession) {
                                this._requestSession = captureContext.requestSession;
                            }
                            if (captureContext.propagationContext) {
                                this._propagationContext = captureContext.propagationContext;
                            }
                        }

                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    clear() {
                        this._breadcrumbs = [];
                        this._tags = {};
                        this._extra = {};
                        this._user = {};
                        this._contexts = {};
                        this._level = undefined;
                        this._transactionName = undefined;
                        this._fingerprint = undefined;
                        this._requestSession = undefined;
                        this._span = undefined;
                        this._session = undefined;
                        this._notifyScopeListeners();
                        this._attachments = [];
                        this._propagationContext = generatePropagationContext();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    addBreadcrumb(breadcrumb, maxBreadcrumbs) {
                        const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;

                        // No data has been changed, so don't notify scope listeners
                        if (maxCrumbs <= 0) {
                            return this;
                        }

                        const mergedBreadcrumb = {
                            timestamp: (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_2__.dateTimestampInSeconds)(),
                            ...breadcrumb,
                        };
                        this._breadcrumbs = [...this._breadcrumbs, mergedBreadcrumb].slice(-maxCrumbs);
                        this._notifyScopeListeners();

                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    getLastBreadcrumb() {
                        return this._breadcrumbs[this._breadcrumbs.length - 1];
                    }

                    /**
                     * @inheritDoc
                     */
                    clearBreadcrumbs() {
                        this._breadcrumbs = [];
                        this._notifyScopeListeners();
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    addAttachment(attachment) {
                        this._attachments.push(attachment);
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    getAttachments() {
                        return this._attachments;
                    }

                    /**
                     * @inheritDoc
                     */
                    clearAttachments() {
                        this._attachments = [];
                        return this;
                    }

                    /**
                     * Applies data from the scope to the event and runs all event processors on it.
                     *
                     * @param event Event
                     * @param hint Object containing additional information about the original exception, for use by the event processors.
                     * @hidden
                     */
                    applyToEvent(event, hint = {}) {
                        if (this._extra && Object.keys(this._extra).length) {
                            event.extra = { ...this._extra,
                                ...event.extra
                            };
                        }
                        if (this._tags && Object.keys(this._tags).length) {
                            event.tags = { ...this._tags,
                                ...event.tags
                            };
                        }
                        if (this._user && Object.keys(this._user).length) {
                            event.user = { ...this._user,
                                ...event.user
                            };
                        }
                        if (this._contexts && Object.keys(this._contexts).length) {
                            event.contexts = { ...this._contexts,
                                ...event.contexts
                            };
                        }
                        if (this._level) {
                            event.level = this._level;
                        }
                        if (this._transactionName) {
                            event.transaction = this._transactionName;
                        }

                        // We want to set the trace context for normal events only if there isn't already
                        // a trace context on the event. There is a product feature in place where we link
                        // errors with transaction and it relies on that.
                        if (this._span) {
                            event.contexts = {
                                trace: this._span.getTraceContext(),
                                ...event.contexts
                            };
                            const transaction = this._span.transaction;
                            if (transaction) {
                                event.sdkProcessingMetadata = {
                                    dynamicSamplingContext: transaction.getDynamicSamplingContext(),
                                    ...event.sdkProcessingMetadata,
                                };
                                const transactionName = transaction.name;
                                if (transactionName) {
                                    event.tags = {
                                        transaction: transactionName,
                                        ...event.tags
                                    };
                                }
                            }
                        }

                        this._applyFingerprint(event);

                        event.breadcrumbs = [...(event.breadcrumbs || []), ...this._breadcrumbs];
                        event.breadcrumbs = event.breadcrumbs.length > 0 ? event.breadcrumbs : undefined;

                        event.sdkProcessingMetadata = {
                            ...event.sdkProcessingMetadata,
                            ...this._sdkProcessingMetadata,
                            propagationContext: this._propagationContext,
                        };

                        return this._notifyEventProcessors([...getGlobalEventProcessors(), ...this._eventProcessors], event, hint);
                    }

                    /**
                     * Add data which will be accessible during event processing but won't get sent to Sentry
                     */
                    setSDKProcessingMetadata(newData) {
                        this._sdkProcessingMetadata = { ...this._sdkProcessingMetadata,
                            ...newData
                        };

                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    setPropagationContext(context) {
                        this._propagationContext = context;
                        return this;
                    }

                    /**
                     * @inheritDoc
                     */
                    getPropagationContext() {
                        return this._propagationContext;
                    }

                    /**
                     * This will be called after {@link applyToEvent} is finished.
                     */
                    _notifyEventProcessors(
                        processors,
                        event,
                        hint,
                        index = 0,
                    ) {
                        return new _sentry_utils__WEBPACK_IMPORTED_MODULE_3__.SyncPromise((resolve, reject) => {
                            const processor = processors[index];
                            if (event === null || typeof processor !== 'function') {
                                resolve(event);
                            } else {
                                const result = processor({ ...event
                                }, hint);

                                (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) &&
                                processor.id &&
                                    result === null &&
                                    _sentry_utils__WEBPACK_IMPORTED_MODULE_4__.logger.log(`Event processor "${processor.id}" dropped event`);

                                if ((0, _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.isThenable)(result)) {
                                    void result
                                        .then(final => this._notifyEventProcessors(processors, final, hint, index + 1).then(resolve))
                                        .then(null, reject);
                                } else {
                                    void this._notifyEventProcessors(processors, result, hint, index + 1)
                                        .then(resolve)
                                        .then(null, reject);
                                }
                            }
                        });
                    }

                    /**
                     * This will be called on every set call.
                     */
                    _notifyScopeListeners() {
                        // We need this check for this._notifyingListeners to be able to work on scope during updates
                        // If this check is not here we'll produce endless recursion when something is done with the scope
                        // during the callback.
                        if (!this._notifyingListeners) {
                            this._notifyingListeners = true;
                            this._scopeListeners.forEach(callback => {
                                callback(this);
                            });
                            this._notifyingListeners = false;
                        }
                    }

                    /**
                     * Applies fingerprint from the scope to the event if there's one,
                     * uses message if there's one instead or get rid of empty fingerprint
                     */
                    _applyFingerprint(event) {
                        // Make sure it's an array first and we actually have something in place
                        event.fingerprint = event.fingerprint ? (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_5__.arrayify)(event.fingerprint) : [];

                        // If we have something on the scope, then merge it with event
                        if (this._fingerprint) {
                            event.fingerprint = event.fingerprint.concat(this._fingerprint);
                        }

                        // If we have no data at all, remove empty array default
                        if (event.fingerprint && !event.fingerprint.length) {
                            delete event.fingerprint;
                        }
                    }
                }

                /**
                 * Returns the global event processors.
                 */
                function getGlobalEventProcessors() {
                    return (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_6__.getGlobalSingleton)('globalEventProcessors', () => []);
                }

                /**
                 * Add a EventProcessor to be kept globally.
                 * @param callback EventProcessor to add
                 */
                function addGlobalEventProcessor(callback) {
                    getGlobalEventProcessors().push(callback);
                }

                function generatePropagationContext() {
                    return {
                        traceId: (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_5__.uuid4)(),
                        spanId: (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_5__.uuid4)().substring(16),
                    };
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/core/esm/session.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    closeSession: () => ( /* binding */ closeSession),
                    /* harmony export */
                    makeSession: () => ( /* binding */ makeSession),
                    /* harmony export */
                    updateSession: () => ( /* binding */ updateSession)
                    /* harmony export */
                });
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/time.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@sentry/utils/esm/misc.js");
                /* harmony import */
                var _sentry_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@sentry/utils/esm/object.js");


                /**
                 * Creates a new `Session` object by setting certain default parameters. If optional @param context
                 * is passed, the passed properties are applied to the session object.
                 *
                 * @param context (optional) additional properties to be applied to the returned session object
                 *
                 * @returns a new `Session` object
                 */
                function makeSession(context) {
                    // Both timestamp and started are in seconds since the UNIX epoch.
                    const startingTime = (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_0__.timestampInSeconds)();

                    const session = {
                        sid: (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.uuid4)(),
                        init: true,
                        timestamp: startingTime,
                        started: startingTime,
                        duration: 0,
                        status: 'ok',
                        errors: 0,
                        ignoreDuration: false,
                        toJSON: () => sessionToJSON(session),
                    };

                    if (context) {
                        updateSession(session, context);
                    }

                    return session;
                }

                /**
                 * Updates a session object with the properties passed in the context.
                 *
                 * Note that this function mutates the passed object and returns void.
                 * (Had to do this instead of returning a new and updated session because closing and sending a session
                 * makes an update to the session after it was passed to the sending logic.
                 * @see BaseClient.captureSession )
                 *
                 * @param session the `Session` to update
                 * @param context the `SessionContext` holding the properties that should be updated in @param session
                 */
                // eslint-disable-next-line complexity
                function updateSession(session, context = {}) {
                    if (context.user) {
                        if (!session.ipAddress && context.user.ip_address) {
                            session.ipAddress = context.user.ip_address;
                        }

                        if (!session.did && !context.did) {
                            session.did = context.user.id || context.user.email || context.user.username;
                        }
                    }

                    session.timestamp = context.timestamp || (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_0__.timestampInSeconds)();

                    if (context.ignoreDuration) {
                        session.ignoreDuration = context.ignoreDuration;
                    }
                    if (context.sid) {
                        // Good enough uuid validation. — Kamil
                        session.sid = context.sid.length === 32 ? context.sid : (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_1__.uuid4)();
                    }
                    if (context.init !== undefined) {
                        session.init = context.init;
                    }
                    if (!session.did && context.did) {
                        session.did = `${context.did}`;
                    }
                    if (typeof context.started === 'number') {
                        session.started = context.started;
                    }
                    if (session.ignoreDuration) {
                        session.duration = undefined;
                    } else if (typeof context.duration === 'number') {
                        session.duration = context.duration;
                    } else {
                        const duration = session.timestamp - session.started;
                        session.duration = duration >= 0 ? duration : 0;
                    }
                    if (context.release) {
                        session.release = context.release;
                    }
                    if (context.environment) {
                        session.environment = context.environment;
                    }
                    if (!session.ipAddress && context.ipAddress) {
                        session.ipAddress = context.ipAddress;
                    }
                    if (!session.userAgent && context.userAgent) {
                        session.userAgent = context.userAgent;
                    }
                    if (typeof context.errors === 'number') {
                        session.errors = context.errors;
                    }
                    if (context.status) {
                        session.status = context.status;
                    }
                }

                /**
                 * Closes a session by setting its status and updating the session object with it.
                 * Internally calls `updateSession` to update the passed session object.
                 *
                 * Note that this function mutates the passed session (@see updateSession for explanation).
                 *
                 * @param session the `Session` object to be closed
                 * @param status the `SessionStatus` with which the session was closed. If you don't pass a status,
                 *               this function will keep the previously set status, unless it was `'ok'` in which case
                 *               it is changed to `'exited'`.
                 */
                function closeSession(session, status) {
                    let context = {};
                    if (status) {
                        context = {
                            status
                        };
                    } else if (session.status === 'ok') {
                        context = {
                            status: 'exited'
                        };
                    }

                    updateSession(session, context);
                }

                /**
                 * Serializes a passed session object to a JSON object with a slightly different structure.
                 * This is necessary because the Sentry backend requires a slightly different schema of a session
                 * than the one the JS SDKs use internally.
                 *
                 * @param session the session to be converted
                 *
                 * @returns a JSON object of the passed session
                 */
                function sessionToJSON(session) {
                    return (0, _sentry_utils__WEBPACK_IMPORTED_MODULE_2__.dropUndefinedKeys)({
                        sid: `${session.sid}`,
                        init: session.init,
                        // Make sure that sec is converted to ms for date constructor
                        started: new Date(session.started * 1000).toISOString(),
                        timestamp: new Date(session.timestamp * 1000).toISOString(),
                        status: session.status,
                        errors: session.errors,
                        did: typeof session.did === 'number' || typeof session.did === 'string' ? `${session.did}` : undefined,
                        duration: session.duration,
                        attrs: {
                            release: session.release,
                            environment: session.environment,
                            ip_address: session.ipAddress,
                            user_agent: session.userAgent,
                        },
                    });
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/browser.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    getDomElement: () => ( /* binding */ getDomElement),
                    /* harmony export */
                    getLocationHref: () => ( /* binding */ getLocationHref),
                    /* harmony export */
                    htmlTreeAsString: () => ( /* binding */ htmlTreeAsString)
                    /* harmony export */
                });
                /* harmony import */
                var _is_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@sentry/utils/esm/is.js");
                /* harmony import */
                var _worldwide_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/worldwide.js");



                // eslint-disable-next-line deprecation/deprecation
                const WINDOW = (0, _worldwide_js__WEBPACK_IMPORTED_MODULE_0__.getGlobalObject)();

                const DEFAULT_MAX_STRING_LENGTH = 80;

                /**
                 * Given a child DOM element, returns a query-selector statement describing that
                 * and its ancestors
                 * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]
                 * @returns generated DOM path
                 */
                function htmlTreeAsString(
                    elem,
                    options = {},
                ) {

                    // try/catch both:
                    // - accessing event.target (see getsentry/raven-js#838, #768)
                    // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly
                    // - can throw an exception in some circumstances.
                    try {
                        let currentElem = elem;
                        const MAX_TRAVERSE_HEIGHT = 5;
                        const out = [];
                        let height = 0;
                        let len = 0;
                        const separator = ' > ';
                        const sepLength = separator.length;
                        let nextStr;
                        const keyAttrs = Array.isArray(options) ? options : options.keyAttrs;
                        const maxStringLength = (!Array.isArray(options) && options.maxStringLength) || DEFAULT_MAX_STRING_LENGTH;

                        while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {
                            nextStr = _htmlElementAsString(currentElem, keyAttrs);
                            // bail out if
                            // - nextStr is the 'html' element
                            // - the length of the string that would be created exceeds maxStringLength
                            //   (ignore this limit if we are on the first iteration)
                            if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= maxStringLength)) {
                                break;
                            }

                            out.push(nextStr);

                            len += nextStr.length;
                            currentElem = currentElem.parentNode;
                        }

                        return out.reverse().join(separator);
                    } catch (_oO) {
                        return '<unknown>';
                    }
                }

                /**
                 * Returns a simple, query-selector representation of a DOM element
                 * e.g. [HTMLElement] => input#foo.btn[name=baz]
                 * @returns generated DOM path
                 */
                function _htmlElementAsString(el, keyAttrs) {
                    const elem = el

                    ;

                    const out = [];
                    let className;
                    let classes;
                    let key;
                    let attr;
                    let i;

                    if (!elem || !elem.tagName) {
                        return '';
                    }

                    out.push(elem.tagName.toLowerCase());

                    // Pairs of attribute keys defined in `serializeAttribute` and their values on element.
                    const keyAttrPairs =
                        keyAttrs && keyAttrs.length ?
                        keyAttrs.filter(keyAttr => elem.getAttribute(keyAttr)).map(keyAttr => [keyAttr, elem.getAttribute(keyAttr)]) :
                        null;

                    if (keyAttrPairs && keyAttrPairs.length) {
                        keyAttrPairs.forEach(keyAttrPair => {
                            out.push(`[${keyAttrPair[0]}="${keyAttrPair[1]}"]`);
                        });
                    } else {
                        if (elem.id) {
                            out.push(`#${elem.id}`);
                        }

                        // eslint-disable-next-line prefer-const
                        className = elem.className;
                        if (className && (0, _is_js__WEBPACK_IMPORTED_MODULE_1__.isString)(className)) {
                            classes = className.split(/\s+/);
                            for (i = 0; i < classes.length; i++) {
                                out.push(`.${classes[i]}`);
                            }
                        }
                    }
                    const allowedAttrs = ['aria-label', 'type', 'name', 'title', 'alt'];
                    for (i = 0; i < allowedAttrs.length; i++) {
                        key = allowedAttrs[i];
                        attr = elem.getAttribute(key);
                        if (attr) {
                            out.push(`[${key}="${attr}"]`);
                        }
                    }
                    return out.join('');
                }

                /**
                 * A safe form of location.href
                 */
                function getLocationHref() {
                    try {
                        return WINDOW.document.location.href;
                    } catch (oO) {
                        return '';
                    }
                }

                /**
                 * Gets a DOM element by using document.querySelector.
                 *
                 * This wrapper will first check for the existance of the function before
                 * actually calling it so that we don't have to take care of this check,
                 * every time we want to access the DOM.
                 *
                 * Reason: DOM/querySelector is not available in all environments.
                 *
                 * We have to cast to any because utils can be consumed by a variety of environments,
                 * and we don't want to break TS users. If you know what element will be selected by
                 * `document.querySelector`, specify it as part of the generic call. For example,
                 * `const element = getDomElement<Element>('selector');`
                 *
                 * @param selector the selector string passed on to document.querySelector
                 */
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                function getDomElement(selector) {
                    if (WINDOW.document && WINDOW.document.querySelector) {
                        return WINDOW.document.querySelector(selector);
                    }
                    return null;
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/env.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    getSDKSource: () => ( /* binding */ getSDKSource),
                    /* harmony export */
                    isBrowserBundle: () => ( /* binding */ isBrowserBundle)
                    /* harmony export */
                });
                /*
                 * This module exists for optimizations in the build process through rollup and terser.  We define some global
                 * constants, which can be overridden during build. By guarding certain pieces of code with functions that return these
                 * constants, we can control whether or not they appear in the final bundle. (Any code guarded by a false condition will
                 * never run, and will hence be dropped during treeshaking.) The two primary uses for this are stripping out calls to
                 * `logger` and preventing node-related code from appearing in browser bundles.
                 *
                 * Attention:
                 * This file should not be used to define constants/flags that are intended to be used for tree-shaking conducted by
                 * users. These flags should live in their respective packages, as we identified user tooling (specifically webpack)
                 * having issues tree-shaking these constants across package boundaries.
                 * An example for this is the __SENTRY_DEBUG__ constant. It is declared in each package individually because we want
                 * users to be able to shake away expressions that it guards.
                 */

                /**
                 * Figures out if we're building a browser bundle.
                 *
                 * @returns true if this is a browser bundle build.
                 */
                function isBrowserBundle() {
                    return typeof __SENTRY_BROWSER_BUNDLE__ !== 'undefined' && !!__SENTRY_BROWSER_BUNDLE__;
                }

                /**
                 * Get source of SDK.
                 */
                function getSDKSource() {
                    // @ts-ignore "npm" is injected by rollup during build process
                    return "npm";
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/is.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    isDOMError: () => ( /* binding */ isDOMError),
                    /* harmony export */
                    isDOMException: () => ( /* binding */ isDOMException),
                    /* harmony export */
                    isElement: () => ( /* binding */ isElement),
                    /* harmony export */
                    isError: () => ( /* binding */ isError),
                    /* harmony export */
                    isErrorEvent: () => ( /* binding */ isErrorEvent),
                    /* harmony export */
                    isEvent: () => ( /* binding */ isEvent),
                    /* harmony export */
                    isInstanceOf: () => ( /* binding */ isInstanceOf),
                    /* harmony export */
                    isNaN: () => ( /* binding */ isNaN),
                    /* harmony export */
                    isPlainObject: () => ( /* binding */ isPlainObject),
                    /* harmony export */
                    isPrimitive: () => ( /* binding */ isPrimitive),
                    /* harmony export */
                    isRegExp: () => ( /* binding */ isRegExp),
                    /* harmony export */
                    isString: () => ( /* binding */ isString),
                    /* harmony export */
                    isSyntheticEvent: () => ( /* binding */ isSyntheticEvent),
                    /* harmony export */
                    isThenable: () => ( /* binding */ isThenable)
                    /* harmony export */
                });
                // eslint-disable-next-line @typescript-eslint/unbound-method
                const objectToString = Object.prototype.toString;

                /**
                 * Checks whether given value's type is one of a few Error or Error-like
                 * {@link isError}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isError(wat) {
                    switch (objectToString.call(wat)) {
                        case '[object Error]':
                        case '[object Exception]':
                        case '[object DOMException]':
                            return true;
                        default:
                            return isInstanceOf(wat, Error);
                    }
                }
                /**
                 * Checks whether given value is an instance of the given built-in class.
                 *
                 * @param wat The value to be checked
                 * @param className
                 * @returns A boolean representing the result.
                 */
                function isBuiltin(wat, className) {
                    return objectToString.call(wat) === `[object ${className}]`;
                }

                /**
                 * Checks whether given value's type is ErrorEvent
                 * {@link isErrorEvent}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isErrorEvent(wat) {
                    return isBuiltin(wat, 'ErrorEvent');
                }

                /**
                 * Checks whether given value's type is DOMError
                 * {@link isDOMError}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isDOMError(wat) {
                    return isBuiltin(wat, 'DOMError');
                }

                /**
                 * Checks whether given value's type is DOMException
                 * {@link isDOMException}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isDOMException(wat) {
                    return isBuiltin(wat, 'DOMException');
                }

                /**
                 * Checks whether given value's type is a string
                 * {@link isString}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isString(wat) {
                    return isBuiltin(wat, 'String');
                }

                /**
                 * Checks whether given value is a primitive (undefined, null, number, boolean, string, bigint, symbol)
                 * {@link isPrimitive}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isPrimitive(wat) {
                    return wat === null || (typeof wat !== 'object' && typeof wat !== 'function');
                }

                /**
                 * Checks whether given value's type is an object literal
                 * {@link isPlainObject}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isPlainObject(wat) {
                    return isBuiltin(wat, 'Object');
                }

                /**
                 * Checks whether given value's type is an Event instance
                 * {@link isEvent}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isEvent(wat) {
                    return typeof Event !== 'undefined' && isInstanceOf(wat, Event);
                }

                /**
                 * Checks whether given value's type is an Element instance
                 * {@link isElement}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isElement(wat) {
                    return typeof Element !== 'undefined' && isInstanceOf(wat, Element);
                }

                /**
                 * Checks whether given value's type is an regexp
                 * {@link isRegExp}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isRegExp(wat) {
                    return isBuiltin(wat, 'RegExp');
                }

                /**
                 * Checks whether given value has a then function.
                 * @param wat A value to be checked.
                 */
                function isThenable(wat) {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    return Boolean(wat && wat.then && typeof wat.then === 'function');
                }

                /**
                 * Checks whether given value's type is a SyntheticEvent
                 * {@link isSyntheticEvent}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isSyntheticEvent(wat) {
                    return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;
                }

                /**
                 * Checks whether given value is NaN
                 * {@link isNaN}.
                 *
                 * @param wat A value to be checked.
                 * @returns A boolean representing the result.
                 */
                function isNaN(wat) {
                    return typeof wat === 'number' && wat !== wat;
                }

                /**
                 * Checks whether given value's type is an instance of provided constructor.
                 * {@link isInstanceOf}.
                 *
                 * @param wat A value to be checked.
                 * @param base A constructor to be used in a check.
                 * @returns A boolean representing the result.
                 */
                function isInstanceOf(wat, base) {
                    try {
                        return wat instanceof base;
                    } catch (_e) {
                        return false;
                    }
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/logger.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    CONSOLE_LEVELS: () => ( /* binding */ CONSOLE_LEVELS),
                    /* harmony export */
                    consoleSandbox: () => ( /* binding */ consoleSandbox),
                    /* harmony export */
                    logger: () => ( /* binding */ logger)
                    /* harmony export */
                });
                /* harmony import */
                var _worldwide_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/worldwide.js");


                /** Prefix for logging strings */
                const PREFIX = 'Sentry Logger ';

                const CONSOLE_LEVELS = ['debug', 'info', 'warn', 'error', 'log', 'assert', 'trace'];

                /**
                 * Temporarily disable sentry console instrumentations.
                 *
                 * @param callback The function to run against the original `console` messages
                 * @returns The results of the callback
                 */
                function consoleSandbox(callback) {
                    if (!("console" in _worldwide_js__WEBPACK_IMPORTED_MODULE_0__.GLOBAL_OBJ)) {
                        return callback();
                    }

                    const originalConsole = _worldwide_js__WEBPACK_IMPORTED_MODULE_0__.GLOBAL_OBJ.console;
                    const wrappedLevels = {};

                    // Restore all wrapped console methods
                    CONSOLE_LEVELS.forEach(level => {
                        // TODO(v7): Remove this check as it's only needed for Node 6
                        const originalWrappedFunc =
                            originalConsole[level] && (originalConsole[level]).__sentry_original__;
                        if (level in originalConsole && originalWrappedFunc) {
                            wrappedLevels[level] = originalConsole[level];
                            originalConsole[level] = originalWrappedFunc;
                        }
                    });

                    try {
                        return callback();
                    } finally {
                        // Revert restoration to wrapped state
                        Object.keys(wrappedLevels).forEach(level => {
                            originalConsole[level] = wrappedLevels[level];
                        });
                    }
                }

                function makeLogger() {
                    let enabled = false;
                    const logger = {
                        enable: () => {
                            enabled = true;
                        },
                        disable: () => {
                            enabled = false;
                        },
                    };

                    if ((typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__)) {
                        CONSOLE_LEVELS.forEach(name => {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            logger[name] = (...args) => {
                                if (enabled) {
                                    consoleSandbox(() => {
                                        _worldwide_js__WEBPACK_IMPORTED_MODULE_0__.GLOBAL_OBJ.console[name](`${PREFIX}[${name}]:`, ...args);
                                    });
                                }
                            };
                        });
                    } else {
                        CONSOLE_LEVELS.forEach(name => {
                            logger[name] = () => undefined;
                        });
                    }

                    return logger;
                }

                // Ensure we only have a single logger instance, even if multiple versions of @sentry/utils are being used
                let logger;
                if ((typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__)) {
                    logger = (0, _worldwide_js__WEBPACK_IMPORTED_MODULE_0__.getGlobalSingleton)('logger', makeLogger);
                } else {
                    logger = makeLogger();
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/misc.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    addContextToFrame: () => ( /* binding */ addContextToFrame),
                    /* harmony export */
                    addExceptionMechanism: () => ( /* binding */ addExceptionMechanism),
                    /* harmony export */
                    addExceptionTypeValue: () => ( /* binding */ addExceptionTypeValue),
                    /* harmony export */
                    arrayify: () => ( /* binding */ arrayify),
                    /* harmony export */
                    checkOrSetAlreadyCaught: () => ( /* binding */ checkOrSetAlreadyCaught),
                    /* harmony export */
                    getEventDescription: () => ( /* binding */ getEventDescription),
                    /* harmony export */
                    parseSemver: () => ( /* binding */ parseSemver),
                    /* harmony export */
                    uuid4: () => ( /* binding */ uuid4)
                    /* harmony export */
                });
                /* harmony import */
                var _object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@sentry/utils/esm/object.js");
                /* harmony import */
                var _string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@sentry/utils/esm/string.js");
                /* harmony import */
                var _worldwide_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/worldwide.js");




                /**
                 * UUID4 generator
                 *
                 * @returns string Generated UUID4.
                 */
                function uuid4() {
                    const gbl = _worldwide_js__WEBPACK_IMPORTED_MODULE_0__.GLOBAL_OBJ;
                    const crypto = gbl.crypto || gbl.msCrypto;

                    if (crypto && crypto.randomUUID) {
                        return crypto.randomUUID().replace(/-/g, '');
                    }

                    const getRandomByte =
                        crypto && crypto.getRandomValues ? () => crypto.getRandomValues(new Uint8Array(1))[0] : () => Math.random() * 16;

                    // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523
                    // Concatenating the following numbers as strings results in '10000000100040008000100000000000'
                    return (([1e7]) + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, c =>
                        // eslint-disable-next-line no-bitwise
                        ((c) ^ ((getRandomByte() & 15) >> ((c) / 4))).toString(16),
                    );
                }

                function getFirstException(event) {
                    return event.exception && event.exception.values ? event.exception.values[0] : undefined;
                }

                /**
                 * Extracts either message or type+value from an event that can be used for user-facing logs
                 * @returns event's description
                 */
                function getEventDescription(event) {
                    const {
                        message,
                        event_id: eventId
                    } = event;
                    if (message) {
                        return message;
                    }

                    const firstException = getFirstException(event);
                    if (firstException) {
                        if (firstException.type && firstException.value) {
                            return `${firstException.type}: ${firstException.value}`;
                        }
                        return firstException.type || firstException.value || eventId || '<unknown>';
                    }
                    return eventId || '<unknown>';
                }

                /**
                 * Adds exception values, type and value to an synthetic Exception.
                 * @param event The event to modify.
                 * @param value Value of the exception.
                 * @param type Type of the exception.
                 * @hidden
                 */
                function addExceptionTypeValue(event, value, type) {
                    const exception = (event.exception = event.exception || {});
                    const values = (exception.values = exception.values || []);
                    const firstException = (values[0] = values[0] || {});
                    if (!firstException.value) {
                        firstException.value = value || '';
                    }
                    if (!firstException.type) {
                        firstException.type = type || 'Error';
                    }
                }

                /**
                 * Adds exception mechanism data to a given event. Uses defaults if the second parameter is not passed.
                 *
                 * @param event The event to modify.
                 * @param newMechanism Mechanism data to add to the event.
                 * @hidden
                 */
                function addExceptionMechanism(event, newMechanism) {
                    const firstException = getFirstException(event);
                    if (!firstException) {
                        return;
                    }

                    const defaultMechanism = {
                        type: 'generic',
                        handled: true
                    };
                    const currentMechanism = firstException.mechanism;
                    firstException.mechanism = { ...defaultMechanism,
                        ...currentMechanism,
                        ...newMechanism
                    };

                    if (newMechanism && 'data' in newMechanism) {
                        const mergedData = { ...(currentMechanism && currentMechanism.data),
                            ...newMechanism.data
                        };
                        firstException.mechanism.data = mergedData;
                    }
                }

                // https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string
                const SEMVER_REGEXP =
                    /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;

                /**
                 * Represents Semantic Versioning object
                 */

                /**
                 * Parses input into a SemVer interface
                 * @param input string representation of a semver version
                 */
                function parseSemver(input) {
                    const match = input.match(SEMVER_REGEXP) || [];
                    const major = parseInt(match[1], 10);
                    const minor = parseInt(match[2], 10);
                    const patch = parseInt(match[3], 10);
                    return {
                        buildmetadata: match[5],
                        major: isNaN(major) ? undefined : major,
                        minor: isNaN(minor) ? undefined : minor,
                        patch: isNaN(patch) ? undefined : patch,
                        prerelease: match[4],
                    };
                }

                /**
                 * This function adds context (pre/post/line) lines to the provided frame
                 *
                 * @param lines string[] containing all lines
                 * @param frame StackFrame that will be mutated
                 * @param linesOfContext number of context lines we want to add pre/post
                 */
                function addContextToFrame(lines, frame, linesOfContext = 5) {
                    // When there is no line number in the frame, attaching context is nonsensical and will even break grouping
                    if (frame.lineno === undefined) {
                        return;
                    }

                    const maxLines = lines.length;
                    const sourceLine = Math.max(Math.min(maxLines - 1, frame.lineno - 1), 0);

                    frame.pre_context = lines
                        .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)
                        .map((line) => (0, _string_js__WEBPACK_IMPORTED_MODULE_1__.snipLine)(line, 0));

                    frame.context_line = (0, _string_js__WEBPACK_IMPORTED_MODULE_1__.snipLine)(lines[Math.min(maxLines - 1, sourceLine)], frame.colno || 0);

                    frame.post_context = lines
                        .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)
                        .map((line) => (0, _string_js__WEBPACK_IMPORTED_MODULE_1__.snipLine)(line, 0));
                }

                /**
                 * Checks whether or not we've already captured the given exception (note: not an identical exception - the very object
                 * in question), and marks it captured if not.
                 *
                 * This is useful because it's possible for an error to get captured by more than one mechanism. After we intercept and
                 * record an error, we rethrow it (assuming we've intercepted it before it's reached the top-level global handlers), so
                 * that we don't interfere with whatever effects the error might have had were the SDK not there. At that point, because
                 * the error has been rethrown, it's possible for it to bubble up to some other code we've instrumented. If it's not
                 * caught after that, it will bubble all the way up to the global handlers (which of course we also instrument). This
                 * function helps us ensure that even if we encounter the same error more than once, we only record it the first time we
                 * see it.
                 *
                 * Note: It will ignore primitives (always return `false` and not mark them as seen), as properties can't be set on
                 * them. {@link: Object.objectify} can be used on exceptions to convert any that are primitives into their equivalent
                 * object wrapper forms so that this check will always work. However, because we need to flag the exact object which
                 * will get rethrown, and because that rethrowing happens outside of the event processing pipeline, the objectification
                 * must be done before the exception captured.
                 *
                 * @param A thrown exception to check or flag as having been seen
                 * @returns `true` if the exception has already been captured, `false` if not (with the side effect of marking it seen)
                 */
                function checkOrSetAlreadyCaught(exception) {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    if (exception && (exception).__sentry_captured__) {
                        return true;
                    }

                    try {
                        // set it this way rather than by assignment so that it's not ennumerable and therefore isn't recorded by the
                        // `ExtraErrorData` integration
                        (0, _object_js__WEBPACK_IMPORTED_MODULE_2__.addNonEnumerableProperty)(exception, '__sentry_captured__', true);
                    } catch (err) {
                        // `exception` is a primitive, so we can't mark it seen
                    }

                    return false;
                }

                /**
                 * Checks whether the given input is already an array, and if it isn't, wraps it in one.
                 *
                 * @param maybeArray Input to turn into an array, if necessary
                 * @returns The input, if already an array, or an array with the input as the only element, if not
                 */
                function arrayify(maybeArray) {
                    return Array.isArray(maybeArray) ? maybeArray : [maybeArray];
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/node.js":
            /***/
            ((module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    dynamicRequire: () => ( /* binding */ dynamicRequire),
                    /* harmony export */
                    isNodeEnv: () => ( /* binding */ isNodeEnv),
                    /* harmony export */
                    loadModule: () => ( /* binding */ loadModule)
                    /* harmony export */
                });
                /* harmony import */
                var _env_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/env.js");
                /* module decorator */
                module = __webpack_require__.hmd(module);


                /**
                 * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,
                 * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.
                 */

                /**
                 * Checks whether we're in the Node.js or Browser environment
                 *
                 * @returns Answer to given question
                 */
                function isNodeEnv() {
                    // explicitly check for browser bundles as those can be optimized statically
                    // by terser/rollup.
                    return (!(0, _env_js__WEBPACK_IMPORTED_MODULE_0__.isBrowserBundle)() &&
                        Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'
                    );
                }

                /**
                 * Requires a module which is protected against bundler minification.
                 *
                 * @param request The module path to resolve
                 */
                // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types, @typescript-eslint/no-explicit-any
                function dynamicRequire(mod, request) {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    return mod.require(request);
                }

                /**
                 * Helper for dynamically loading module that should work with linked dependencies.
                 * The problem is that we _should_ be using `require(require.resolve(moduleName, { paths: [cwd()] }))`
                 * However it's _not possible_ to do that with Webpack, as it has to know all the dependencies during
                 * build time. `require.resolve` is also not available in any other way, so we cannot create,
                 * a fake helper like we do with `dynamicRequire`.
                 *
                 * We always prefer to use local package, thus the value is not returned early from each `try/catch` block.
                 * That is to mimic the behavior of `require.resolve` exactly.
                 *
                 * @param moduleName module name to require
                 * @returns possibly required module
                 */
                function loadModule(moduleName) {
                    let mod;

                    try {
                        mod = dynamicRequire(module, moduleName);
                    } catch (e) {
                        // no-empty
                    }

                    try {
                        const {
                            cwd
                        } = dynamicRequire(module, 'process');
                        mod = dynamicRequire(module, `${cwd()}/node_modules/${moduleName}`);
                    } catch (e) {
                        // no-empty
                    }

                    return mod;
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/object.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    addNonEnumerableProperty: () => ( /* binding */ addNonEnumerableProperty),
                    /* harmony export */
                    convertToPlainObject: () => ( /* binding */ convertToPlainObject),
                    /* harmony export */
                    dropUndefinedKeys: () => ( /* binding */ dropUndefinedKeys),
                    /* harmony export */
                    extractExceptionKeysForMessage: () => ( /* binding */ extractExceptionKeysForMessage),
                    /* harmony export */
                    fill: () => ( /* binding */ fill),
                    /* harmony export */
                    getOriginalFunction: () => ( /* binding */ getOriginalFunction),
                    /* harmony export */
                    markFunctionWrapped: () => ( /* binding */ markFunctionWrapped),
                    /* harmony export */
                    objectify: () => ( /* binding */ objectify),
                    /* harmony export */
                    urlEncode: () => ( /* binding */ urlEncode)
                    /* harmony export */
                });
                /* harmony import */
                var _browser_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@sentry/utils/esm/browser.js");
                /* harmony import */
                var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/is.js");
                /* harmony import */
                var _string_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/@sentry/utils/esm/string.js");




                /**
                 * Replace a method in an object with a wrapped version of itself.
                 *
                 * @param source An object that contains a method to be wrapped.
                 * @param name The name of the method to be wrapped.
                 * @param replacementFactory A higher-order function that takes the original version of the given method and returns a
                 * wrapped version. Note: The function returned by `replacementFactory` needs to be a non-arrow function, in order to
                 * preserve the correct value of `this`, and the original method must be called using `origMethod.call(this, <other
                 * args>)` or `origMethod.apply(this, [<other args>])` (rather than being called directly), again to preserve `this`.
                 * @returns void
                 */
                function fill(source, name, replacementFactory) {
                    if (!(name in source)) {
                        return;
                    }

                    const original = source[name];
                    const wrapped = replacementFactory(original);

                    // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work
                    // otherwise it'll throw "TypeError: Object.defineProperties called on non-object"
                    if (typeof wrapped === 'function') {
                        try {
                            markFunctionWrapped(wrapped, original);
                        } catch (_Oo) {
                            // This can throw if multiple fill happens on a global object like XMLHttpRequest
                            // Fixes https://github.com/getsentry/sentry-javascript/issues/2043
                        }
                    }

                    source[name] = wrapped;
                }

                /**
                 * Defines a non-enumerable property on the given object.
                 *
                 * @param obj The object on which to set the property
                 * @param name The name of the property to be set
                 * @param value The value to which to set the property
                 */
                function addNonEnumerableProperty(obj, name, value) {
                    Object.defineProperty(obj, name, {
                        // enumerable: false, // the default, so we can save on bundle size by not explicitly setting it
                        value: value,
                        writable: true,
                        configurable: true,
                    });
                }

                /**
                 * Remembers the original function on the wrapped function and
                 * patches up the prototype.
                 *
                 * @param wrapped the wrapper function
                 * @param original the original function that gets wrapped
                 */
                function markFunctionWrapped(wrapped, original) {
                    const proto = original.prototype || {};
                    wrapped.prototype = original.prototype = proto;
                    addNonEnumerableProperty(wrapped, '__sentry_original__', original);
                }

                /**
                 * This extracts the original function if available.  See
                 * `markFunctionWrapped` for more information.
                 *
                 * @param func the function to unwrap
                 * @returns the unwrapped version of the function if available.
                 */
                function getOriginalFunction(func) {
                    return func.__sentry_original__;
                }

                /**
                 * Encodes given object into url-friendly format
                 *
                 * @param object An object that contains serializable values
                 * @returns string Encoded
                 */
                function urlEncode(object) {
                    return Object.keys(object)
                        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(object[key])}`)
                        .join('&');
                }

                /**
                 * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their
                 * non-enumerable properties attached.
                 *
                 * @param value Initial source that we have to transform in order for it to be usable by the serializer
                 * @returns An Event or Error turned into an object - or the value argurment itself, when value is neither an Event nor
                 *  an Error.
                 */
                function convertToPlainObject(value)

                {
                    if ((0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isError)(value)) {
                        return {
                            message: value.message,
                            name: value.name,
                            stack: value.stack,
                            ...getOwnProperties(value),
                        };
                    } else if ((0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isEvent)(value)) {
                        const newObj

                        = {
                            type: value.type,
                            target: serializeEventTarget(value.target),
                            currentTarget: serializeEventTarget(value.currentTarget),
                            ...getOwnProperties(value),
                        };

                        if (typeof CustomEvent !== 'undefined' && (0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isInstanceOf)(value, CustomEvent)) {
                            newObj.detail = value.detail;
                        }

                        return newObj;
                    } else {
                        return value;
                    }
                }

                /** Creates a string representation of the target of an `Event` object */
                function serializeEventTarget(target) {
                    try {
                        return (0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isElement)(target) ? (0, _browser_js__WEBPACK_IMPORTED_MODULE_1__.htmlTreeAsString)(target) : Object.prototype.toString.call(target);
                    } catch (_oO) {
                        return '<unknown>';
                    }
                }

                /** Filters out all but an object's own properties */
                function getOwnProperties(obj) {
                    if (typeof obj === 'object' && obj !== null) {
                        const extractedProps = {};
                        for (const property in obj) {
                            if (Object.prototype.hasOwnProperty.call(obj, property)) {
                                extractedProps[property] = (obj)[property];
                            }
                        }
                        return extractedProps;
                    } else {
                        return {};
                    }
                }

                /**
                 * Given any captured exception, extract its keys and create a sorted
                 * and truncated list that will be used inside the event message.
                 * eg. `Non-error exception captured with keys: foo, bar, baz`
                 */
                function extractExceptionKeysForMessage(exception, maxLength = 40) {
                    const keys = Object.keys(convertToPlainObject(exception));
                    keys.sort();

                    if (!keys.length) {
                        return '[object has no keys]';
                    }

                    if (keys[0].length >= maxLength) {
                        return (0, _string_js__WEBPACK_IMPORTED_MODULE_2__.truncate)(keys[0], maxLength);
                    }

                    for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {
                        const serialized = keys.slice(0, includedKeys).join(', ');
                        if (serialized.length > maxLength) {
                            continue;
                        }
                        if (includedKeys === keys.length) {
                            return serialized;
                        }
                        return (0, _string_js__WEBPACK_IMPORTED_MODULE_2__.truncate)(serialized, maxLength);
                    }

                    return '';
                }

                /**
                 * Given any object, return a new object having removed all fields whose value was `undefined`.
                 * Works recursively on objects and arrays.
                 *
                 * Attention: This function keeps circular references in the returned object.
                 */
                function dropUndefinedKeys(inputValue) {
                    // This map keeps track of what already visited nodes map to.
                    // Our Set - based memoBuilder doesn't work here because we want to the output object to have the same circular
                    // references as the input object.
                    const memoizationMap = new Map();

                    // This function just proxies `_dropUndefinedKeys` to keep the `memoBuilder` out of this function's API
                    return _dropUndefinedKeys(inputValue, memoizationMap);
                }

                function _dropUndefinedKeys(inputValue, memoizationMap) {
                    if ((0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(inputValue)) {
                        // If this node has already been visited due to a circular reference, return the object it was mapped to in the new object
                        const memoVal = memoizationMap.get(inputValue);
                        if (memoVal !== undefined) {
                            return memoVal;
                        }

                        const returnValue = {};
                        // Store the mapping of this value in case we visit it again, in case of circular data
                        memoizationMap.set(inputValue, returnValue);

                        for (const key of Object.keys(inputValue)) {
                            if (typeof inputValue[key] !== 'undefined') {
                                returnValue[key] = _dropUndefinedKeys(inputValue[key], memoizationMap);
                            }
                        }

                        return returnValue;
                    }

                    if (Array.isArray(inputValue)) {
                        // If this node has already been visited due to a circular reference, return the array it was mapped to in the new object
                        const memoVal = memoizationMap.get(inputValue);
                        if (memoVal !== undefined) {
                            return memoVal;
                        }

                        const returnValue = [];
                        // Store the mapping of this value in case we visit it again, in case of circular data
                        memoizationMap.set(inputValue, returnValue);

                        inputValue.forEach((item) => {
                            returnValue.push(_dropUndefinedKeys(item, memoizationMap));
                        });

                        return returnValue;
                    }

                    return inputValue;
                }

                /**
                 * Ensure that something is an object.
                 *
                 * Turns `undefined` and `null` into `String`s and all other primitives into instances of their respective wrapper
                 * classes (String, Boolean, Number, etc.). Acts as the identity function on non-primitives.
                 *
                 * @param wat The subject of the objectification
                 * @returns A version of `wat` which can safely be used with `Object` class methods
                 */
                function objectify(wat) {
                    let objectified;
                    switch (true) {
                        case wat === undefined || wat === null:
                            objectified = new String(wat);
                            break;

                            // Though symbols and bigints do have wrapper classes (`Symbol` and `BigInt`, respectively), for whatever reason
                            // those classes don't have constructors which can be used with the `new` keyword. We therefore need to cast each as
                            // an object in order to wrap it.
                        case typeof wat === 'symbol' || typeof wat === 'bigint':
                            objectified = Object(wat);
                            break;

                            // this will catch the remaining primitives: `String`, `Number`, and `Boolean`
                        case (0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isPrimitive)(wat):
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                            objectified = new(wat).constructor(wat);
                            break;

                            // by process of elimination, at this point we know that `wat` must already be an object
                        default:
                            objectified = wat;
                            break;
                    }
                    return objectified;
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/string.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    isMatchingPattern: () => ( /* binding */ isMatchingPattern),
                    /* harmony export */
                    safeJoin: () => ( /* binding */ safeJoin),
                    /* harmony export */
                    snipLine: () => ( /* binding */ snipLine),
                    /* harmony export */
                    stringMatchesSomePattern: () => ( /* binding */ stringMatchesSomePattern),
                    /* harmony export */
                    truncate: () => ( /* binding */ truncate)
                    /* harmony export */
                });
                /* harmony import */
                var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/is.js");


                /**
                 * Truncates given string to the maximum characters count
                 *
                 * @param str An object that contains serializable values
                 * @param max Maximum number of characters in truncated string (0 = unlimited)
                 * @returns string Encoded
                 */
                function truncate(str, max = 0) {
                    if (typeof str !== 'string' || max === 0) {
                        return str;
                    }
                    return str.length <= max ? str : `${str.slice(0, max)}...`;
                }

                /**
                 * This is basically just `trim_line` from
                 * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67
                 *
                 * @param str An object that contains serializable values
                 * @param max Maximum number of characters in truncated string
                 * @returns string Encoded
                 */
                function snipLine(line, colno) {
                    let newLine = line;
                    const lineLength = newLine.length;
                    if (lineLength <= 150) {
                        return newLine;
                    }
                    if (colno > lineLength) {
                        // eslint-disable-next-line no-param-reassign
                        colno = lineLength;
                    }

                    let start = Math.max(colno - 60, 0);
                    if (start < 5) {
                        start = 0;
                    }

                    let end = Math.min(start + 140, lineLength);
                    if (end > lineLength - 5) {
                        end = lineLength;
                    }
                    if (end === lineLength) {
                        start = Math.max(end - 140, 0);
                    }

                    newLine = newLine.slice(start, end);
                    if (start > 0) {
                        newLine = `'{snip} ${newLine}`;
                    }
                    if (end < lineLength) {
                        newLine += ' {snip}';
                    }

                    return newLine;
                }

                /**
                 * Join values in array
                 * @param input array of values to be joined together
                 * @param delimiter string to be placed in-between values
                 * @returns Joined values
                 */
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                function safeJoin(input, delimiter) {
                    if (!Array.isArray(input)) {
                        return '';
                    }

                    const output = [];
                    // eslint-disable-next-line @typescript-eslint/prefer-for-of
                    for (let i = 0; i < input.length; i++) {
                        const value = input[i];
                        try {
                            output.push(String(value));
                        } catch (e) {
                            output.push('[value cannot be serialized]');
                        }
                    }

                    return output.join(delimiter);
                }

                /**
                 * Checks if the given value matches a regex or string
                 *
                 * @param value The string to test
                 * @param pattern Either a regex or a string against which `value` will be matched
                 * @param requireExactStringMatch If true, `value` must match `pattern` exactly. If false, `value` will match
                 * `pattern` if it contains `pattern`. Only applies to string-type patterns.
                 */
                function isMatchingPattern(
                    value,
                    pattern,
                    requireExactStringMatch = false,
                ) {
                    if (!(0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isString)(value)) {
                        return false;
                    }

                    if ((0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isRegExp)(pattern)) {
                        return pattern.test(value);
                    }
                    if ((0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isString)(pattern)) {
                        return requireExactStringMatch ? value === pattern : value.includes(pattern);
                    }

                    return false;
                }

                /**
                 * Test the given string against an array of strings and regexes. By default, string matching is done on a
                 * substring-inclusion basis rather than a strict equality basis
                 *
                 * @param testString The string to test
                 * @param patterns The patterns against which to test the string
                 * @param requireExactStringMatch If true, `testString` must match one of the given string patterns exactly in order to
                 * count. If false, `testString` will match a string pattern if it contains that pattern.
                 * @returns
                 */
                function stringMatchesSomePattern(
                    testString,
                    patterns = [],
                    requireExactStringMatch = false,
                ) {
                    return patterns.some(pattern => isMatchingPattern(testString, pattern, requireExactStringMatch));
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/syncpromise.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    SyncPromise: () => ( /* binding */ SyncPromise),
                    /* harmony export */
                    rejectedSyncPromise: () => ( /* binding */ rejectedSyncPromise),
                    /* harmony export */
                    resolvedSyncPromise: () => ( /* binding */ resolvedSyncPromise)
                    /* harmony export */
                });
                /* harmony import */
                var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/is.js");


                /* eslint-disable @typescript-eslint/explicit-function-return-type */

                /** SyncPromise internal states */
                var States;
                (function(States) {
                    /** Pending */
                    const PENDING = 0;
                    States[States["PENDING"] = PENDING] = "PENDING";
                    /** Resolved / OK */
                    const RESOLVED = 1;
                    States[States["RESOLVED"] = RESOLVED] = "RESOLVED";
                    /** Rejected / Error */
                    const REJECTED = 2;
                    States[States["REJECTED"] = REJECTED] = "REJECTED";
                })(States || (States = {}));

                // Overloads so we can call resolvedSyncPromise without arguments and generic argument

                /**
                 * Creates a resolved sync promise.
                 *
                 * @param value the value to resolve the promise with
                 * @returns the resolved sync promise
                 */
                function resolvedSyncPromise(value) {
                    return new SyncPromise(resolve => {
                        resolve(value);
                    });
                }

                /**
                 * Creates a rejected sync promise.
                 *
                 * @param value the value to reject the promise with
                 * @returns the rejected sync promise
                 */
                function rejectedSyncPromise(reason) {
                    return new SyncPromise((_, reject) => {
                        reject(reason);
                    });
                }

                /**
                 * Thenable class that behaves like a Promise and follows it's interface
                 * but is not async internally
                 */
                class SyncPromise {

                    constructor(
                        executor,
                    ) {
                        SyncPromise.prototype.__init.call(this);
                        SyncPromise.prototype.__init2.call(this);
                        SyncPromise.prototype.__init3.call(this);
                        SyncPromise.prototype.__init4.call(this);
                        this._state = States.PENDING;
                        this._handlers = [];

                        try {
                            executor(this._resolve, this._reject);
                        } catch (e) {
                            this._reject(e);
                        }
                    }

                    /** JSDoc */
                    then(
                        onfulfilled,
                        onrejected,
                    ) {
                        return new SyncPromise((resolve, reject) => {
                            this._handlers.push([
                                false,
                                result => {
                                    if (!onfulfilled) {
                                        // TODO: ¯\_(ツ)_/¯
                                        // TODO: FIXME
                                        resolve(result);
                                    } else {
                                        try {
                                            resolve(onfulfilled(result));
                                        } catch (e) {
                                            reject(e);
                                        }
                                    }
                                },
                                reason => {
                                    if (!onrejected) {
                                        reject(reason);
                                    } else {
                                        try {
                                            resolve(onrejected(reason));
                                        } catch (e) {
                                            reject(e);
                                        }
                                    }
                                },
                            ]);
                            this._executeHandlers();
                        });
                    }

                    /** JSDoc */
                    catch (
                        onrejected,
                    ) {
                        return this.then(val => val, onrejected);
                    }

                    /** JSDoc */
                    finally(onfinally) {
                        return new SyncPromise((resolve, reject) => {
                            let val;
                            let isRejected;

                            return this.then(
                                value => {
                                    isRejected = false;
                                    val = value;
                                    if (onfinally) {
                                        onfinally();
                                    }
                                },
                                reason => {
                                    isRejected = true;
                                    val = reason;
                                    if (onfinally) {
                                        onfinally();
                                    }
                                },
                            ).then(() => {
                                if (isRejected) {
                                    reject(val);
                                    return;
                                }

                                resolve(val);
                            });
                        });
                    }

                    /** JSDoc */
                    __init() {
                        this._resolve = (value) => {
                            this._setResult(States.RESOLVED, value);
                        };
                    }

                    /** JSDoc */
                    __init2() {
                        this._reject = (reason) => {
                            this._setResult(States.REJECTED, reason);
                        };
                    }

                    /** JSDoc */
                    __init3() {
                        this._setResult = (state, value) => {
                            if (this._state !== States.PENDING) {
                                return;
                            }

                            if ((0, _is_js__WEBPACK_IMPORTED_MODULE_0__.isThenable)(value)) {
                                void(value).then(this._resolve, this._reject);
                                return;
                            }

                            this._state = state;
                            this._value = value;

                            this._executeHandlers();
                        };
                    }

                    /** JSDoc */
                    __init4() {
                        this._executeHandlers = () => {
                            if (this._state === States.PENDING) {
                                return;
                            }

                            const cachedHandlers = this._handlers.slice();
                            this._handlers = [];

                            cachedHandlers.forEach(handler => {
                                if (handler[0]) {
                                    return;
                                }

                                if (this._state === States.RESOLVED) {
                                    // eslint-disable-next-line @typescript-eslint/no-floating-promises
                                    handler[1](this._value);
                                }

                                if (this._state === States.REJECTED) {
                                    handler[2](this._value);
                                }

                                handler[0] = true;
                            });
                        };
                    }
                }




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/time.js":
            /***/
            ((module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    _browserPerformanceTimeOriginMode: () => ( /* binding */ _browserPerformanceTimeOriginMode),
                    /* harmony export */
                    browserPerformanceTimeOrigin: () => ( /* binding */ browserPerformanceTimeOrigin),
                    /* harmony export */
                    dateTimestampInSeconds: () => ( /* binding */ dateTimestampInSeconds),
                    /* harmony export */
                    timestampInSeconds: () => ( /* binding */ timestampInSeconds),
                    /* harmony export */
                    timestampWithMs: () => ( /* binding */ timestampWithMs),
                    /* harmony export */
                    usingPerformanceAPI: () => ( /* binding */ usingPerformanceAPI)
                    /* harmony export */
                });
                /* harmony import */
                var _node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/@sentry/utils/esm/node.js");
                /* harmony import */
                var _worldwide_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@sentry/utils/esm/worldwide.js");
                /* module decorator */
                module = __webpack_require__.hmd(module);



                // eslint-disable-next-line deprecation/deprecation
                const WINDOW = (0, _worldwide_js__WEBPACK_IMPORTED_MODULE_0__.getGlobalObject)();

                /**
                 * An object that can return the current timestamp in seconds since the UNIX epoch.
                 */

                /**
                 * A TimestampSource implementation for environments that do not support the Performance Web API natively.
                 *
                 * Note that this TimestampSource does not use a monotonic clock. A call to `nowSeconds` may return a timestamp earlier
                 * than a previously returned value. We do not try to emulate a monotonic behavior in order to facilitate debugging. It
                 * is more obvious to explain "why does my span have negative duration" than "why my spans have zero duration".
                 */
                const dateTimestampSource = {
                    nowSeconds: () => Date.now() / 1000,
                };

                /**
                 * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}
                 * for accessing a high-resolution monotonic clock.
                 */

                /**
                 * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not
                 * support the API.
                 *
                 * Wrapping the native API works around differences in behavior from different browsers.
                 */
                function getBrowserPerformance() {
                    const {
                        performance
                    } = WINDOW;
                    if (!performance || !performance.now) {
                        return undefined;
                    }

                    // Replace performance.timeOrigin with our own timeOrigin based on Date.now().
                    //
                    // This is a partial workaround for browsers reporting performance.timeOrigin such that performance.timeOrigin +
                    // performance.now() gives a date arbitrarily in the past.
                    //
                    // Additionally, computing timeOrigin in this way fills the gap for browsers where performance.timeOrigin is
                    // undefined.
                    //
                    // The assumption that performance.timeOrigin + performance.now() ~= Date.now() is flawed, but we depend on it to
                    // interact with data coming out of performance entries.
                    //
                    // Note that despite recommendations against it in the spec, browsers implement the Performance API with a clock that
                    // might stop when the computer is asleep (and perhaps under other circumstances). Such behavior causes
                    // performance.timeOrigin + performance.now() to have an arbitrary skew over Date.now(). In laptop computers, we have
                    // observed skews that can be as long as days, weeks or months.
                    //
                    // See https://github.com/getsentry/sentry-javascript/issues/2590.
                    //
                    // BUG: despite our best intentions, this workaround has its limitations. It mostly addresses timings of pageload
                    // transactions, but ignores the skew built up over time that can aversely affect timestamps of navigation
                    // transactions of long-lived web pages.
                    const timeOrigin = Date.now() - performance.now();

                    return {
                        now: () => performance.now(),
                        timeOrigin,
                    };
                }

                /**
                 * Returns the native Performance API implementation from Node.js. Returns undefined in old Node.js versions that don't
                 * implement the API.
                 */
                function getNodePerformance() {
                    try {
                        const perfHooks = (0, _node_js__WEBPACK_IMPORTED_MODULE_1__.dynamicRequire)(module, 'perf_hooks');
                        return perfHooks.performance;
                    } catch (_) {
                        return undefined;
                    }
                }

                /**
                 * The Performance API implementation for the current platform, if available.
                 */
                const platformPerformance = (0, _node_js__WEBPACK_IMPORTED_MODULE_1__.isNodeEnv)() ? getNodePerformance() : getBrowserPerformance();

                const timestampSource =
                    platformPerformance === undefined ?
                    dateTimestampSource :
                    {
                        nowSeconds: () => (platformPerformance.timeOrigin + platformPerformance.now()) / 1000,
                    };

                /**
                 * Returns a timestamp in seconds since the UNIX epoch using the Date API.
                 */
                const dateTimestampInSeconds = dateTimestampSource.nowSeconds.bind(dateTimestampSource);

                /**
                 * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the
                 * availability of the Performance API.
                 *
                 * See `usingPerformanceAPI` to test whether the Performance API is used.
                 *
                 * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is
                 * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The
                 * skew can grow to arbitrary amounts like days, weeks or months.
                 * See https://github.com/getsentry/sentry-javascript/issues/2590.
                 */
                const timestampInSeconds = timestampSource.nowSeconds.bind(timestampSource);

                /**
                 * Re-exported with an old name for backwards-compatibility.
                 * TODO (v8): Remove this
                 *
                 * @deprecated Use `timestampInSeconds` instead.
                 */
                const timestampWithMs = timestampInSeconds;

                /**
                 * A boolean that is true when timestampInSeconds uses the Performance API to produce monotonic timestamps.
                 */
                const usingPerformanceAPI = platformPerformance !== undefined;

                /**
                 * Internal helper to store what is the source of browserPerformanceTimeOrigin below. For debugging only.
                 */
                let _browserPerformanceTimeOriginMode;

                /**
                 * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the
                 * performance API is available.
                 */
                const browserPerformanceTimeOrigin = (() => {
                    // Unfortunately browsers may report an inaccurate time origin data, through either performance.timeOrigin or
                    // performance.timing.navigationStart, which results in poor results in performance data. We only treat time origin
                    // data as reliable if they are within a reasonable threshold of the current time.

                    const {
                        performance
                    } = WINDOW;
                    if (!performance || !performance.now) {
                        _browserPerformanceTimeOriginMode = 'none';
                        return undefined;
                    }

                    const threshold = 3600 * 1000;
                    const performanceNow = performance.now();
                    const dateNow = Date.now();

                    // if timeOrigin isn't available set delta to threshold so it isn't used
                    const timeOriginDelta = performance.timeOrigin ?
                        Math.abs(performance.timeOrigin + performanceNow - dateNow) :
                        threshold;
                    const timeOriginIsReliable = timeOriginDelta < threshold;

                    // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin
                    // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.
                    // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always
                    // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the
                    // Date API.
                    // eslint-disable-next-line deprecation/deprecation
                    const navigationStart = performance.timing && performance.timing.navigationStart;
                    const hasNavigationStart = typeof navigationStart === 'number';
                    // if navigationStart isn't available set delta to threshold so it isn't used
                    const navigationStartDelta = hasNavigationStart ? Math.abs(navigationStart + performanceNow - dateNow) : threshold;
                    const navigationStartIsReliable = navigationStartDelta < threshold;

                    if (timeOriginIsReliable || navigationStartIsReliable) {
                        // Use the more reliable time origin
                        if (timeOriginDelta <= navigationStartDelta) {
                            _browserPerformanceTimeOriginMode = 'timeOrigin';
                            return performance.timeOrigin;
                        } else {
                            _browserPerformanceTimeOriginMode = 'navigationStart';
                            return navigationStart;
                        }
                    }

                    // Either both timeOrigin and navigationStart are skewed or neither is available, fallback to Date.
                    _browserPerformanceTimeOriginMode = 'dateNow';
                    return dateNow;
                })();




                /***/
            }),

        /***/
        "../../node_modules/@sentry/utils/esm/worldwide.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    GLOBAL_OBJ: () => ( /* binding */ GLOBAL_OBJ),
                    /* harmony export */
                    getGlobalObject: () => ( /* binding */ getGlobalObject),
                    /* harmony export */
                    getGlobalSingleton: () => ( /* binding */ getGlobalSingleton)
                    /* harmony export */
                });
                /** Internal global with common properties and Sentry extensions  */

                // The code below for 'isGlobalObj' and 'GLOBAL_OBJ' was copied from core-js before modification
                // https://github.com/zloirock/core-js/blob/1b944df55282cdc99c90db5f49eb0b6eda2cc0a3/packages/core-js/internals/global.js
                // core-js has the following licence:
                //
                // Copyright (c) 2014-2022 Denis Pushkarev
                //
                // Permission is hereby granted, free of charge, to any person obtaining a copy
                // of this software and associated documentation files (the "Software"), to deal
                // in the Software without restriction, including without limitation the rights
                // to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
                // copies of the Software, and to permit persons to whom the Software is
                // furnished to do so, subject to the following conditions:
                //
                // The above copyright notice and this permission notice shall be included in
                // all copies or substantial portions of the Software.
                //
                // THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
                // IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
                // FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
                // AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
                // LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
                // OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
                // THE SOFTWARE.

                /** Returns 'obj' if it's the global object, otherwise returns undefined */
                function isGlobalObj(obj) {
                    return obj && obj.Math == Math ? obj : undefined;
                }

                /** Get's the global object for the current JavaScript runtime */
                const GLOBAL_OBJ =
                    (typeof globalThis == 'object' && isGlobalObj(globalThis)) ||
                    // eslint-disable-next-line no-restricted-globals
                    (typeof window == 'object' && isGlobalObj(window)) ||
                    (typeof self == 'object' && isGlobalObj(self)) ||
                    (typeof __webpack_require__.g == 'object' && isGlobalObj(__webpack_require__.g)) ||
                    (function() {
                        return this;
                    })() || {};

                /**
                 * @deprecated Use GLOBAL_OBJ instead or WINDOW from @sentry/browser. This will be removed in v8
                 */
                function getGlobalObject() {
                    return GLOBAL_OBJ;
                }

                /**
                 * Returns a global singleton contained in the global `__SENTRY__` object.
                 *
                 * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory
                 * function and added to the `__SENTRY__` object.
                 *
                 * @param name name of the global singleton on __SENTRY__
                 * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`
                 * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value
                 * @returns the singleton
                 */
                function getGlobalSingleton(name, creator, obj) {
                    const gbl = (obj || GLOBAL_OBJ);
                    const __SENTRY__ = (gbl.__SENTRY__ = gbl.__SENTRY__ || {});
                    const singleton = __SENTRY__[name] || (__SENTRY__[name] = creator());
                    return singleton;
                }




                /***/
            })

    }
])
//# sourceMappingURL=vendors-node_modules_sentry_core_esm_exports_js.js.map