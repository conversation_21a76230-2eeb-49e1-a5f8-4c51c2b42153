(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["node_modules_apollo_client_link_context_index_js"], {

        /***/
        "../../node_modules/@apollo/client/link/context/index.js":
            /***/
            ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

                __webpack_require__.r(__webpack_exports__);
                /* harmony export */
                __webpack_require__.d(__webpack_exports__, {
                    /* harmony export */
                    setContext: () => ( /* binding */ setContext)
                    /* harmony export */
                });
                /* harmony import */
                var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("../../node_modules/tslib/tslib.es6.js");
                /* harmony import */
                var _core_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("../../node_modules/@apollo/client/link/core/ApolloLink.js");
                /* harmony import */
                var _utilities_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("../../node_modules/zen-observable-ts/module.js");



                function setContext(setter) {
                    return new _core_index_js__WEBPACK_IMPORTED_MODULE_0__.ApolloLink(function(operation, forward) {
                        var request = (0, tslib__WEBPACK_IMPORTED_MODULE_1__.__rest)(operation, []);
                        return new _utilities_index_js__WEBPACK_IMPORTED_MODULE_2__.Observable(function(observer) {
                            var handle;
                            var closed = false;
                            Promise.resolve(request)
                                .then(function(req) {
                                    return setter(req, operation.getContext());
                                })
                                .then(operation.setContext)
                                .then(function() {
                                    if (closed)
                                        return;
                                    handle = forward(operation).subscribe({
                                        next: observer.next.bind(observer),
                                        error: observer.error.bind(observer),
                                        complete: observer.complete.bind(observer),
                                    });
                                })
                                .catch(observer.error.bind(observer));
                            return function() {
                                closed = true;
                                if (handle)
                                    handle.unsubscribe();
                            };
                        });
                    });
                }


                /***/
            })

    }
])
//# sourceMappingURL=node_modules_apollo_client_link_context_index_js.140d788105a35b55.js.map