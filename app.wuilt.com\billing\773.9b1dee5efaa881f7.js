/*! For license information please see 773.9b1dee5efaa881f7.js.LICENSE.txt */
(self.webpackChunkbilling = self.webpackChunkbilling || []).push([
    [773], {
        90773: (e, r, t) => {
            t.r(r), t.d(r, {
                default: () => f,
                getRoutes: () => i
            });
            var n = t(26415),
                l = t.n(n),
                o = t(80755),
                a = t(69500);
            const s = l().lazy((() => Promise.all([t.e(2053), t.e(1117), t.e(4068)]).then(t.bind(t, 41117))));

            function i() {
                return [{
                    path: "/",
                    element: (0, a.jsx)(o.Navigate, {
                        to: "/billing"
                    })
                }, {
                    path: "/billing/*",
                    element: (0, a.jsx)(s, {}),
                    type: "shell"
                }]
            }
            const f = i
        },
        68736: (e, r, t) => {
            var n = t(59243),
                l = Symbol.for("react.element"),
                o = Symbol.for("react.fragment"),
                a = Object.prototype.hasOwnProperty,
                s = n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,
                i = {
                    key: !0,
                    ref: !0,
                    __self: !0,
                    __source: !0
                };

            function f(e, r, t) {
                var n, o = {},
                    f = null,
                    p = null;
                for (n in void 0 !== t && (f = "" + t), void 0 !== r.key && (f = "" + r.key), void 0 !== r.ref && (p = r.ref), r) a.call(r, n) && !i.hasOwnProperty(n) && (o[n] = r[n]);
                if (e && e.defaultProps)
                    for (n in r = e.defaultProps) void 0 === o[n] && (o[n] = r[n]);
                return {
                    $$typeof: l,
                    type: e,
                    key: f,
                    ref: p,
                    props: o,
                    _owner: s.current
                }
            }
            r.Fragment = o, r.jsx = f, r.jsxs = f
        },
        69500: (e, r, t) => {
            e.exports = t(68736)
        }
    }
]);
//# sourceMappingURL=773.9b1dee5efaa881f7.js.map