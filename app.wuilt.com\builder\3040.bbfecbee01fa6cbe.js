(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [3040], {
        13040: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = "undefined" != typeof window ? window : void 0,
                i = "undefined" != typeof globalThis ? globalThis : n,
                r = Array.prototype,
                s = r.forEach,
                o = r.indexOf,
                a = null == i ? void 0 : i.navigator,
                u = null == i ? void 0 : i.document,
                l = null == i ? void 0 : i.location,
                c = null == i ? void 0 : i.fetch,
                d = null != i && i.XMLHttpRequest && "withCredentials" in new i.XMLHttpRequest ? i.XMLHttpRequest : void 0,
                h = null == i ? void 0 : i.AbortController,
                f = null == a ? void 0 : a.userAgent,
                v = null != n ? n : {},
                p = {
                    DEBUG: !1,
                    LIB_VERSION: "1.166.1"
                },
                g = Array.isArray,
                _ = Object.prototype,
                m = _.hasOwnProperty,
                y = _.toString,
                b = g || function(e) {
                    return "[object Array]" === y.call(e)
                },
                k = function(e) {
                    return "function" == typeof e
                },
                w = function(e) {
                    return e === Object(e) && !b(e)
                },
                S = function(e) {
                    if (w(e)) {
                        for (var t in e)
                            if (m.call(e, t)) return !1;
                        return !0
                    }
                    return !1
                },
                E = function(e) {
                    return void 0 === e
                },
                x = function(e) {
                    return "[object String]" == y.call(e)
                },
                I = function(e) {
                    return x(e) && 0 === e.trim().length
                },
                F = function(e) {
                    return null === e
                },
                P = function(e) {
                    return E(e) || F(e)
                },
                R = function(e) {
                    return "[object Number]" == y.call(e)
                },
                T = function(e) {
                    return "[object Boolean]" === y.call(e)
                },
                C = function(e) {
                    return e instanceof FormData
                },
                M = "[PostHog.js]",
                $ = {
                    _log: function(e) {
                        if (n && (p.DEBUG || v.POSTHOG_DEBUG) && !E(n.console) && n.console) {
                            for (var t = ("__rrweb_original__" in n.console[e] ? n.console[e].__rrweb_original__ : n.console[e]), i = arguments.length, r = new Array(i > 1 ? i - 1 : 0), s = 1; s < i; s++) r[s - 1] = arguments[s];
                            t.apply(void 0, [M].concat(r))
                        }
                    },
                    info: function() {
                        for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
                        $._log.apply($, ["log"].concat(t))
                    },
                    warn: function() {
                        for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
                        $._log.apply($, ["warn"].concat(t))
                    },
                    error: function() {
                        for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
                        $._log.apply($, ["error"].concat(t))
                    },
                    critical: function() {
                        for (var e, t = arguments.length, n = new Array(t), i = 0; i < t; i++) n[i] = arguments[i];
                        (e = console).error.apply(e, [M].concat(n))
                    },
                    uninitializedWarning: function(e) {
                        $.error("You must initialize PostHog before calling ".concat(e))
                    }
                },
                O = function(e, t, n) {
                    if (e.config.disable_external_dependency_loading) return $.warn("".concat(t, " was requested but loading of external scripts is disabled.")), n("Loading of external scripts is disabled");
                    var i = function() {
                        if (!u) return n("document not found");
                        var e = u.createElement("script");
                        e.type = "text/javascript", e.src = t, e.onload = function(e) {
                            return n(void 0, e)
                        }, e.onerror = function(e) {
                            return n(e)
                        };
                        var i, r = u.querySelectorAll("body > script");
                        r.length > 0 ? null === (i = r[0].parentNode) || void 0 === i || i.insertBefore(e, r[0]) : u.body.appendChild(e)
                    };
                    null != u && u.body ? i() : null == u || u.addEventListener("DOMContentLoaded", i)
                };

            function A(e, t) {
                var n = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var i = Object.getOwnPropertySymbols(e);
                    t && (i = i.filter((function(t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable
                    }))), n.push.apply(n, i)
                }
                return n
            }

            function L(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var n = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? A(Object(n), !0).forEach((function(t) {
                        H(e, t, n[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : A(Object(n)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                    }))
                }
                return e
            }

            function D(e) {
                return D = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                    return typeof e
                } : function(e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                }, D(e)
            }

            function N(e, t) {
                if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
            }

            function q(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var i = t[n];
                    i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
                }
            }

            function B(e, t, n) {
                return t && q(e.prototype, t), n && q(e, n), Object.defineProperty(e, "prototype", {
                    writable: !1
                }), e
            }

            function H(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n, e
            }

            function U(e, t) {
                return function(e) {
                    if (Array.isArray(e)) return e
                }(e) || function(e, t) {
                    var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
                    if (null != n) {
                        var i, r, s = [],
                            o = !0,
                            a = !1;
                        try {
                            for (n = n.call(e); !(o = (i = n.next()).done) && (s.push(i.value), !t || s.length !== t); o = !0);
                        } catch (e) {
                            a = !0, r = e
                        } finally {
                            try {
                                o || null == n.return || n.return()
                            } finally {
                                if (a) throw r
                            }
                        }
                        return s
                    }
                }(e, t) || W(e, t) || function() {
                    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }()
            }

            function j(e) {
                return function(e) {
                    if (Array.isArray(e)) return z(e)
                }(e) || function(e) {
                    if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e)
                }(e) || W(e) || function() {
                    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }()
            }

            function W(e, t) {
                if (e) {
                    if ("string" == typeof e) return z(e, t);
                    var n = Object.prototype.toString.call(e).slice(8, -1);
                    return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? z(e, t) : void 0
                }
            }

            function z(e, t) {
                (null == t || t > e.length) && (t = e.length);
                for (var n = 0, i = new Array(t); n < t; n++) i[n] = e[n];
                return i
            }

            function V(e, t) {
                var n = "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
                if (!n) {
                    if (Array.isArray(e) || (n = W(e)) || t && e && "number" == typeof e.length) {
                        n && (e = n);
                        var i = 0,
                            r = function() {};
                        return {
                            s: r,
                            n: function() {
                                return i >= e.length ? {
                                    done: !0
                                } : {
                                    done: !1,
                                    value: e[i++]
                                }
                            },
                            e: function(e) {
                                throw e
                            },
                            f: r
                        }
                    }
                    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }
                var s, o = !0,
                    a = !1;
                return {
                    s: function() {
                        n = n.call(e)
                    },
                    n: function() {
                        var e = n.next();
                        return o = e.done, e
                    },
                    e: function(e) {
                        a = !0, s = e
                    },
                    f: function() {
                        try {
                            o || null == n.return || n.return()
                        } finally {
                            if (a) throw s
                        }
                    }
                }
            }
            v.__PosthogExtensions__ = v.__PosthogExtensions__ || {}, v.__PosthogExtensions__.loadExternalDependency = function(e, t, n) {
                var i = "/static/".concat(t, ".js") + "?v=".concat(e.version);
                if ("toolbar" === t) {
                    var r = 3e5,
                        s = Math.floor(Date.now() / r) * r;
                    i = "".concat(i, "?&=").concat(s)
                }
                var o = e.requestRouter.endpointFor("assets", i);
                O(e, o, n)
            }, v.__PosthogExtensions__.loadSiteApp = function(e, t, n) {
                var i = e.requestRouter.endpointFor("api", t);
                O(e, i, n)
            };
            var G = {},
                Q = function(e) {
                    return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "")
                };

            function J(e, t, n) {
                if (b(e))
                    if (s && e.forEach === s) e.forEach(t, n);
                    else if ("length" in e && e.length === +e.length)
                    for (var i = 0, r = e.length; i < r; i++)
                        if (i in e && t.call(n, e[i], i) === G) return
            }

            function Y(e, t, n) {
                if (!P(e)) {
                    if (b(e)) return J(e, t, n);
                    if (C(e)) {
                        var i, r = V(e.entries());
                        try {
                            for (r.s(); !(i = r.n()).done;) {
                                var s = i.value;
                                if (t.call(n, s[1], s[0]) === G) return
                            }
                        } catch (e) {
                            r.e(e)
                        } finally {
                            r.f()
                        }
                    } else
                        for (var o in e)
                            if (m.call(e, o) && t.call(n, e[o], o) === G) return
                }
            }
            var X = function(e) {
                for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), i = 1; i < t; i++) n[i - 1] = arguments[i];
                return J(n, (function(t) {
                    for (var n in t) void 0 !== t[n] && (e[n] = t[n])
                })), e
            };

            function K(e, t) {
                return -1 !== e.indexOf(t)
            }

            function Z(e) {
                for (var t = Object.keys(e), n = t.length, i = new Array(n); n--;) i[n] = [t[n], e[t[n]]];
                return i
            }
            var ee, te = function(e) {
                    try {
                        return e()
                    } catch (e) {
                        return
                    }
                },
                ne = function(e) {
                    return function() {
                        try {
                            for (var t = arguments.length, n = new Array(t), i = 0; i < t; i++) n[i] = arguments[i];
                            return e.apply(this, n)
                        } catch (e) {
                            $.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."), $.critical(e)
                        }
                    }
                },
                ie = function(e) {
                    var t = {};
                    return Y(e, (function(e, n) {
                        x(e) && e.length > 0 && (t[n] = e)
                    })), t
                },
                re = function(e) {
                    return e.replace(/^\$/, "")
                },
                se = function() {
                    function e(t) {
                        return t && (t.preventDefault = e.preventDefault, t.stopPropagation = e.stopPropagation), t
                    }
                    return e.preventDefault = function() {
                            this.returnValue = !1
                        }, e.stopPropagation = function() {
                            this.cancelBubble = !0
                        },
                        function(t, i, r, s, o) {
                            if (t)
                                if (t.addEventListener && !s) t.addEventListener(i, r, !!o);
                                else {
                                    var a = "on" + i,
                                        u = t[a];
                                    t[a] = function(t, i, r) {
                                        return function(s) {
                                            if (s = s || e(null == n ? void 0 : n.event)) {
                                                var o, a = !0;
                                                k(r) && (o = r(s));
                                                var u = i.call(t, s);
                                                return !1 !== o && !1 !== u || (a = !1), a
                                            }
                                        }
                                    }(t, r, u)
                                }
                            else $.error("No valid element provided to register_event")
                        }
                }();

            function oe(e, t) {
                for (var n = 0; n < e.length; n++)
                    if (t(e[n])) return e[n]
            }
            t.Compression = void 0, (ee = t.Compression || (t.Compression = {})).GZipJS = "gzip-js", ee.Base64 = "base64";
            var ae = "$people_distinct_id",
                ue = "__alias",
                le = "__timers",
                ce = "$autocapture_disabled_server_side",
                de = "$heatmaps_enabled_server_side",
                he = "$exception_capture_enabled_server_side",
                fe = "$exception_capture_endpoint_suffix",
                ve = "$web_vitals_enabled_server_side",
                pe = "$web_vitals_allowed_metrics",
                ge = "$session_recording_enabled_server_side",
                _e = "$console_log_recording_enabled_server_side",
                me = "$session_recording_network_payload_capture",
                ye = "$session_recording_canvas_recording",
                be = "$replay_sample_rate",
                ke = "$replay_minimum_duration",
                we = "$sesid",
                Se = "$session_is_sampled",
                Ee = "$enabled_feature_flags",
                xe = "$early_access_features",
                Ie = "$stored_person_properties",
                Fe = "$stored_group_properties",
                Pe = "$surveys",
                Re = "$surveys_activated",
                Te = "$flag_call_reported",
                Ce = "$user_state",
                Me = "$client_session_props",
                $e = "$capture_rate_limit",
                Oe = "$initial_campaign_params",
                Ae = "$initial_referrer_info",
                Le = "$initial_person_info",
                De = "$epp",
                Ne = "__POSTHOG_TOOLBAR__",
                qe = [ae, ue, "__cmpns", le, ge, de, we, Ee, Ce, xe, Fe, Ie, Pe, Te, Me, $e, Oe, Ae, De],
                Be = "$active_feature_flags",
                He = "$override_feature_flags",
                Ue = "$feature_flag_payloads",
                je = function(e) {
                    var t, n = {},
                        i = V(Z(e || {}));
                    try {
                        for (i.s(); !(t = i.n()).done;) {
                            var r = U(t.value, 2),
                                s = r[0],
                                o = r[1];
                            o && (n[s] = o)
                        }
                    } catch (e) {
                        i.e(e)
                    } finally {
                        i.f()
                    }
                    return n
                },
                We = function() {
                    function e(t) {
                        N(this, e), this.instance = t, this._override_warning = !1, this.featureFlagEventHandlers = [], this.reloadFeatureFlagsQueued = !1, this.reloadFeatureFlagsInAction = !1
                    }
                    return B(e, [{
                        key: "getFlags",
                        value: function() {
                            return Object.keys(this.getFlagVariants())
                        }
                    }, {
                        key: "getFlagVariants",
                        value: function() {
                            var e = this.instance.get_property(Ee),
                                t = this.instance.get_property(He);
                            if (!t) return e || {};
                            for (var n = X({}, e), i = Object.keys(t), r = 0; r < i.length; r++) n[i[r]] = t[i[r]];
                            return this._override_warning || ($.warn(" Overriding feature flags!", {
                                enabledFlags: e,
                                overriddenFlags: t,
                                finalFlags: n
                            }), this._override_warning = !0), n
                        }
                    }, {
                        key: "getFlagPayloads",
                        value: function() {
                            return this.instance.get_property(Ue) || {}
                        }
                    }, {
                        key: "reloadFeatureFlags",
                        value: function() {
                            this.reloadFeatureFlagsQueued || (this.reloadFeatureFlagsQueued = !0, this._startReloadTimer())
                        }
                    }, {
                        key: "setAnonymousDistinctId",
                        value: function(e) {
                            this.$anon_distinct_id = e
                        }
                    }, {
                        key: "setReloadingPaused",
                        value: function(e) {
                            this.reloadFeatureFlagsInAction = e
                        }
                    }, {
                        key: "resetRequestQueue",
                        value: function() {
                            this.reloadFeatureFlagsQueued = !1
                        }
                    }, {
                        key: "_startReloadTimer",
                        value: function() {
                            var e = this;
                            this.reloadFeatureFlagsQueued && !this.reloadFeatureFlagsInAction && setTimeout((function() {
                                !e.reloadFeatureFlagsInAction && e.reloadFeatureFlagsQueued && (e.reloadFeatureFlagsQueued = !1, e._reloadFeatureFlagsRequest())
                            }), 5)
                        }
                    }, {
                        key: "_reloadFeatureFlagsRequest",
                        value: function() {
                            var e = this;
                            if (!this.instance.config.advanced_disable_feature_flags) {
                                this.setReloadingPaused(!0);
                                var n = this.instance.config.token,
                                    i = this.instance.get_property(Ie),
                                    r = this.instance.get_property(Fe),
                                    s = {
                                        token: n,
                                        distinct_id: this.instance.get_distinct_id(),
                                        groups: this.instance.getGroups(),
                                        $anon_distinct_id: this.$anon_distinct_id,
                                        person_properties: i,
                                        group_properties: r,
                                        disable_flags: this.instance.config.advanced_disable_feature_flags || void 0
                                    };
                                this.instance._send_request({
                                    method: "POST",
                                    url: this.instance.requestRouter.endpointFor("api", "/decide/?v=3"),
                                    data: s,
                                    compression: this.instance.config.disable_compression ? void 0 : t.Compression.Base64,
                                    timeout: this.instance.config.feature_flag_request_timeout_ms,
                                    callback: function(t) {
                                        var n;
                                        e.setReloadingPaused(!1);
                                        var i = !0;
                                        200 === t.statusCode && (e.$anon_distinct_id = void 0, i = !1), e.receivedFeatureFlags(null !== (n = t.json) && void 0 !== n ? n : {}, i), e._startReloadTimer()
                                    }
                                })
                            }
                        }
                    }, {
                        key: "getFeatureFlag",
                        value: function(e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                            if (this.instance.decideEndpointWasHit || this.getFlags() && this.getFlags().length > 0) {
                                var n, i = this.getFlagVariants()[e],
                                    r = "".concat(i),
                                    s = this.instance.get_property(Te) || {};
                                return !t.send_event && "send_event" in t || e in s && s[e].includes(r) || (b(s[e]) ? s[e].push(r) : s[e] = [r], null === (n = this.instance.persistence) || void 0 === n || n.register(H({}, Te, s)), this.instance.capture("$feature_flag_called", {
                                    $feature_flag: e,
                                    $feature_flag_response: i
                                })), i
                            }
                            $.warn('getFeatureFlag for key "' + e + "\" failed. Feature flags didn't load in time.")
                        }
                    }, {
                        key: "getFeatureFlagPayload",
                        value: function(e) {
                            return this.getFlagPayloads()[e]
                        }
                    }, {
                        key: "isFeatureEnabled",
                        value: function(e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                            if (this.instance.decideEndpointWasHit || this.getFlags() && this.getFlags().length > 0) return !!this.getFeatureFlag(e, t);
                            $.warn('isFeatureEnabled for key "' + e + "\" failed. Feature flags didn't load in time.")
                        }
                    }, {
                        key: "addFeatureFlagsHandler",
                        value: function(e) {
                            this.featureFlagEventHandlers.push(e)
                        }
                    }, {
                        key: "removeFeatureFlagsHandler",
                        value: function(e) {
                            this.featureFlagEventHandlers = this.featureFlagEventHandlers.filter((function(t) {
                                return t !== e
                            }))
                        }
                    }, {
                        key: "receivedFeatureFlags",
                        value: function(e, t) {
                            if (this.instance.persistence) {
                                this.instance.decideEndpointWasHit = !0;
                                var n = this.getFlagVariants(),
                                    i = this.getFlagPayloads();
                                ! function(e, t) {
                                    var n, i = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
                                        r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {},
                                        s = e.featureFlags,
                                        o = e.featureFlagPayloads;
                                    if (s)
                                        if (b(s)) {
                                            var a, u = {};
                                            if (s)
                                                for (var l = 0; l < s.length; l++) u[s[l]] = !0;
                                            t && t.register((H(a = {}, Be, s), H(a, Ee, u), a))
                                        } else {
                                            var c = s,
                                                d = o;
                                            e.errorsWhileComputingFlags && (c = L(L({}, i), c), d = L(L({}, r), d)), t && t.register((H(n = {}, Be, Object.keys(je(c))), H(n, Ee, c || {}), H(n, Ue, d || {}), n))
                                        }
                                }(e, this.instance.persistence, n, i), this._fireFeatureFlagsCallbacks(t)
                            }
                        }
                    }, {
                        key: "override",
                        value: function(e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                            if (!this.instance.__loaded || !this.instance.persistence) return $.uninitializedWarning("posthog.feature_flags.override");
                            if (this._override_warning = t, !1 === e) this.instance.persistence.unregister(He);
                            else if (b(e)) {
                                for (var n = {}, i = 0; i < e.length; i++) n[e[i]] = !0;
                                this.instance.persistence.register(H({}, He, n))
                            } else this.instance.persistence.register(H({}, He, e))
                        }
                    }, {
                        key: "onFeatureFlags",
                        value: function(e) {
                            var t = this;
                            if (this.addFeatureFlagsHandler(e), this.instance.decideEndpointWasHit) {
                                var n = this._prepareFeatureFlagsForCallbacks(),
                                    i = n.flags,
                                    r = n.flagVariants;
                                e(i, r)
                            }
                            return function() {
                                return t.removeFeatureFlagsHandler(e)
                            }
                        }
                    }, {
                        key: "updateEarlyAccessFeatureEnrollment",
                        value: function(e, t) {
                            var n, i, r = H({}, "$feature_enrollment/".concat(e), t);
                            this.instance.capture("$feature_enrollment_update", {
                                $feature_flag: e,
                                $feature_enrollment: t,
                                $set: r
                            }), this.setPersonPropertiesForFlags(r, !1);
                            var s = L(L({}, this.getFlagVariants()), {}, H({}, e, t));
                            null === (n = this.instance.persistence) || void 0 === n || n.register((H(i = {}, Be, Object.keys(je(s))), H(i, Ee, s), i)), this._fireFeatureFlagsCallbacks()
                        }
                    }, {
                        key: "getEarlyAccessFeatures",
                        value: function(e) {
                            var t = this,
                                n = arguments.length > 1 && void 0 !== arguments[1] && arguments[1],
                                i = this.instance.get_property(xe);
                            if (i && !n) return e(i);
                            this.instance._send_request({
                                transport: "XHR",
                                url: this.instance.requestRouter.endpointFor("api", "/api/early_access_features/?token=".concat(this.instance.config.token)),
                                method: "GET",
                                callback: function(n) {
                                    var i;
                                    if (n.json) {
                                        var r = n.json.earlyAccessFeatures;
                                        return null === (i = t.instance.persistence) || void 0 === i || i.register(H({}, xe, r)), e(r)
                                    }
                                }
                            })
                        }
                    }, {
                        key: "_prepareFeatureFlagsForCallbacks",
                        value: function() {
                            var e = this.getFlags(),
                                t = this.getFlagVariants();
                            return {
                                flags: e.filter((function(e) {
                                    return t[e]
                                })),
                                flagVariants: Object.keys(t).filter((function(e) {
                                    return t[e]
                                })).reduce((function(e, n) {
                                    return e[n] = t[n], e
                                }), {})
                            }
                        }
                    }, {
                        key: "_fireFeatureFlagsCallbacks",
                        value: function(e) {
                            var t = this._prepareFeatureFlagsForCallbacks(),
                                n = t.flags,
                                i = t.flagVariants;
                            this.featureFlagEventHandlers.forEach((function(t) {
                                return t(n, i, {
                                    errorsLoading: e
                                })
                            }))
                        }
                    }, {
                        key: "setPersonPropertiesForFlags",
                        value: function(e) {
                            var t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1],
                                n = this.instance.get_property(Ie) || {};
                            this.instance.register(H({}, Ie, L(L({}, n), e))), t && this.instance.reloadFeatureFlags()
                        }
                    }, {
                        key: "resetPersonPropertiesForFlags",
                        value: function() {
                            this.instance.unregister(Ie)
                        }
                    }, {
                        key: "setGroupPropertiesForFlags",
                        value: function(e) {
                            var t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1],
                                n = this.instance.get_property(Fe) || {};
                            0 !== Object.keys(n).length && Object.keys(n).forEach((function(t) {
                                n[t] = L(L({}, n[t]), e[t]), delete e[t]
                            })), this.instance.register(H({}, Fe, L(L({}, n), e))), t && this.instance.reloadFeatureFlags()
                        }
                    }, {
                        key: "resetGroupPropertiesForFlags",
                        value: function(e) {
                            if (e) {
                                var t = this.instance.get_property(Fe) || {};
                                this.instance.register(H({}, Fe, L(L({}, t), {}, H({}, e, {}))))
                            } else this.instance.unregister(Fe)
                        }
                    }]), e
                }();
            Math.trunc || (Math.trunc = function(e) {
                return e < 0 ? Math.ceil(e) : Math.floor(e)
            }), Number.isInteger || (Number.isInteger = function(e) {
                return R(e) && isFinite(e) && Math.floor(e) === e
            });
            var ze = "0123456789abcdef",
                Ve = function() {
                    function e(t) {
                        if (N(this, e), this.bytes = t, 16 !== t.length) throw new TypeError("not 128-bit length")
                    }
                    return B(e, [{
                        key: "toString",
                        value: function() {
                            for (var e = "", t = 0; t < this.bytes.length; t++) e = e + ze.charAt(this.bytes[t] >>> 4) + ze.charAt(15 & this.bytes[t]), 3 !== t && 5 !== t && 7 !== t && 9 !== t || (e += "-");
                            if (36 !== e.length) throw new Error("Invalid UUIDv7 was generated");
                            return e
                        }
                    }, {
                        key: "clone",
                        value: function() {
                            return new e(this.bytes.slice(0))
                        }
                    }, {
                        key: "equals",
                        value: function(e) {
                            return 0 === this.compareTo(e)
                        }
                    }, {
                        key: "compareTo",
                        value: function(e) {
                            for (var t = 0; t < 16; t++) {
                                var n = this.bytes[t] - e.bytes[t];
                                if (0 !== n) return Math.sign(n)
                            }
                            return 0
                        }
                    }], [{
                        key: "fromFieldsV7",
                        value: function(t, n, i, r) {
                            if (!Number.isInteger(t) || !Number.isInteger(n) || !Number.isInteger(i) || !Number.isInteger(r) || t < 0 || n < 0 || i < 0 || r < 0 || t > 0xffffffffffff || n > 4095 || i > 1073741823 || r > 4294967295) throw new RangeError("invalid field value");
                            var s = new Uint8Array(16);
                            return s[0] = t / Math.pow(2, 40), s[1] = t / Math.pow(2, 32), s[2] = t / Math.pow(2, 24), s[3] = t / Math.pow(2, 16), s[4] = t / Math.pow(2, 8), s[5] = t, s[6] = 112 | n >>> 8, s[7] = n, s[8] = 128 | i >>> 24, s[9] = i >>> 16, s[10] = i >>> 8, s[11] = i, s[12] = r >>> 24, s[13] = r >>> 16, s[14] = r >>> 8, s[15] = r, new e(s)
                        }
                    }]), e
                }(),
                Ge = function() {
                    function e() {
                        N(this, e), H(this, "timestamp", 0), H(this, "counter", 0), H(this, "random", new Xe)
                    }
                    return B(e, [{
                        key: "generate",
                        value: function() {
                            var e = this.generateOrAbort();
                            if (E(e)) {
                                this.timestamp = 0;
                                var t = this.generateOrAbort();
                                if (E(t)) throw new Error("Could not generate UUID after timestamp reset");
                                return t
                            }
                            return e
                        }
                    }, {
                        key: "generateOrAbort",
                        value: function() {
                            var e = Date.now();
                            if (e > this.timestamp) this.timestamp = e, this.resetCounter();
                            else {
                                if (!(e + 1e4 > this.timestamp)) return;
                                this.counter++, this.counter > 4398046511103 && (this.timestamp++, this.resetCounter())
                            }
                            return Ve.fromFieldsV7(this.timestamp, Math.trunc(this.counter / Math.pow(2, 30)), this.counter & Math.pow(2, 30) - 1, this.random.nextUint32())
                        }
                    }, {
                        key: "resetCounter",
                        value: function() {
                            this.counter = 1024 * this.random.nextUint32() + (1023 & this.random.nextUint32())
                        }
                    }]), e
                }(),
                Qe = function(e) {
                    if ("undefined" != typeof UUIDV7_DENY_WEAK_RNG && UUIDV7_DENY_WEAK_RNG) throw new Error("no cryptographically strong RNG available");
                    for (var t = 0; t < e.length; t++) e[t] = 65536 * Math.trunc(65536 * Math.random()) + Math.trunc(65536 * Math.random());
                    return e
                };
            n && !E(n.crypto) && crypto.getRandomValues && (Qe = function(e) {
                return crypto.getRandomValues(e)
            });
            var Je, Ye, Xe = function() {
                    function e() {
                        N(this, e), H(this, "buffer", new Uint32Array(8)), H(this, "cursor", 1 / 0)
                    }
                    return B(e, [{
                        key: "nextUint32",
                        value: function() {
                            return this.cursor >= this.buffer.length && (Qe(this.buffer), this.cursor = 0), this.buffer[this.cursor++]
                        }
                    }]), e
                }(),
                Ke = function() {
                    return Ze().toString()
                },
                Ze = function() {
                    return (Je || (Je = new Ge)).generate()
                },
                et = "",
                tt = /[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i,
                nt = {
                    is_supported: function() {
                        return !!u
                    },
                    error: function(e) {
                        $.error("cookieStore error: " + e)
                    },
                    get: function(e) {
                        if (u) {
                            try {
                                for (var t = e + "=", n = u.cookie.split(";").filter((function(e) {
                                        return e.length
                                    })), i = 0; i < n.length; i++) {
                                    for (var r = n[i];
                                        " " == r.charAt(0);) r = r.substring(1, r.length);
                                    if (0 === r.indexOf(t)) return decodeURIComponent(r.substring(t.length, r.length))
                                }
                            } catch (e) {}
                            return null
                        }
                    },
                    parse: function(e) {
                        var t;
                        try {
                            t = JSON.parse(nt.get(e)) || {}
                        } catch (e) {}
                        return t
                    },
                    set: function(e, t, n, i, r) {
                        if (u) try {
                            var s = "",
                                o = "",
                                a = function(e, t) {
                                    if (t) {
                                        var n = function(e) {
                                            var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : u;
                                            if (et) return et;
                                            if (!t) return "";
                                            if (["localhost", "127.0.0.1"].includes(e)) return "";
                                            for (var n = e.split("."), i = Math.min(n.length, 8), r = "dmn_chk_" + Ke(), s = new RegExp("(^|;)\\s*" + r + "=1"); !et && i--;) {
                                                var o = n.slice(i).join("."),
                                                    a = r + "=1;domain=." + o;
                                                t.cookie = a, s.test(t.cookie) && (t.cookie = a + ";expires=Thu, 01 Jan 1970 00:00:00 GMT", et = o)
                                            }
                                            return et
                                        }(e);
                                        if (!n) {
                                            var i = function(e) {
                                                var t = e.match(tt);
                                                return t ? t[0] : ""
                                            }(e);
                                            i !== n && $.info("Warning: cookie subdomain discovery mismatch", i, n), n = i
                                        }
                                        return n ? "; domain=." + n : ""
                                    }
                                    return ""
                                }(u.location.hostname, i);
                            if (n) {
                                var l = new Date;
                                l.setTime(l.getTime() + 24 * n * 60 * 60 * 1e3), s = "; expires=" + l.toUTCString()
                            }
                            r && (o = "; secure");
                            var c = e + "=" + encodeURIComponent(JSON.stringify(t)) + s + "; SameSite=Lax; path=/" + a + o;
                            return c.length > 3686.4 && $.warn("cookieStore warning: large cookie, len=" + c.length), u.cookie = c, c
                        } catch (e) {
                            return
                        }
                    },
                    remove: function(e, t) {
                        try {
                            nt.set(e, "", -1, t)
                        } catch (e) {
                            return
                        }
                    }
                },
                it = null,
                rt = {
                    is_supported: function() {
                        if (!F(it)) return it;
                        var e = !0;
                        if (E(n)) e = !1;
                        else try {
                            var t = "__mplssupport__";
                            rt.set(t, "xyz"), '"xyz"' !== rt.get(t) && (e = !1), rt.remove(t)
                        } catch (t) {
                            e = !1
                        }
                        return e || $.error("localStorage unsupported; falling back to cookie store"), it = e, e
                    },
                    error: function(e) {
                        $.error("localStorage error: " + e)
                    },
                    get: function(e) {
                        try {
                            return null == n ? void 0 : n.localStorage.getItem(e)
                        } catch (e) {
                            rt.error(e)
                        }
                        return null
                    },
                    parse: function(e) {
                        try {
                            return JSON.parse(rt.get(e)) || {}
                        } catch (e) {}
                        return null
                    },
                    set: function(e, t) {
                        try {
                            null == n || n.localStorage.setItem(e, JSON.stringify(t))
                        } catch (e) {
                            rt.error(e)
                        }
                    },
                    remove: function(e) {
                        try {
                            null == n || n.localStorage.removeItem(e)
                        } catch (e) {
                            rt.error(e)
                        }
                    }
                },
                st = ["distinct_id", we, Se, De],
                ot = L(L({}, rt), {}, {
                    parse: function(e) {
                        try {
                            var t = {};
                            try {
                                t = nt.parse(e) || {}
                            } catch (e) {}
                            var n = X(t, JSON.parse(rt.get(e) || "{}"));
                            return rt.set(e, n), n
                        } catch (e) {}
                        return null
                    },
                    set: function(e, t, n, i, r, s) {
                        try {
                            rt.set(e, t, void 0, void 0, s);
                            var o = {};
                            st.forEach((function(e) {
                                t[e] && (o[e] = t[e])
                            })), Object.keys(o).length && nt.set(e, o, n, i, r, s)
                        } catch (e) {
                            rt.error(e)
                        }
                    },
                    remove: function(e, t) {
                        try {
                            null == n || n.localStorage.removeItem(e), nt.remove(e, t)
                        } catch (e) {
                            rt.error(e)
                        }
                    }
                }),
                at = {},
                ut = {
                    is_supported: function() {
                        return !0
                    },
                    error: function(e) {
                        $.error("memoryStorage error: " + e)
                    },
                    get: function(e) {
                        return at[e] || null
                    },
                    parse: function(e) {
                        return at[e] || null
                    },
                    set: function(e, t) {
                        at[e] = t
                    },
                    remove: function(e) {
                        delete at[e]
                    }
                },
                lt = null,
                ct = {
                    is_supported: function() {
                        if (!F(lt)) return lt;
                        if (lt = !0, E(n)) lt = !1;
                        else try {
                            var e = "__support__";
                            ct.set(e, "xyz"), '"xyz"' !== ct.get(e) && (lt = !1), ct.remove(e)
                        } catch (e) {
                            lt = !1
                        }
                        return lt
                    },
                    error: function(e) {
                        $.error("sessionStorage error: ", e)
                    },
                    get: function(e) {
                        try {
                            return null == n ? void 0 : n.sessionStorage.getItem(e)
                        } catch (e) {
                            ct.error(e)
                        }
                        return null
                    },
                    parse: function(e) {
                        try {
                            return JSON.parse(ct.get(e)) || null
                        } catch (e) {}
                        return null
                    },
                    set: function(e, t) {
                        try {
                            null == n || n.sessionStorage.setItem(e, JSON.stringify(t))
                        } catch (e) {
                            ct.error(e)
                        }
                    },
                    remove: function(e) {
                        try {
                            null == n || n.sessionStorage.removeItem(e)
                        } catch (e) {
                            ct.error(e)
                        }
                    }
                },
                dt = ["localhost", "127.0.0.1"],
                ht = function(e) {
                    var t = null == u ? void 0 : u.createElement("a");
                    return E(t) ? null : (t.href = e, t)
                },
                ft = function(e, t) {
                    return !! function(e) {
                        try {
                            new RegExp(e)
                        } catch (e) {
                            return !1
                        }
                        return !0
                    }(t) && new RegExp(t).test(e)
                },
                vt = function(e, t) {
                    for (var n, i = ((e.split("#")[0] || "").split("?")[1] || "").split("&"), r = 0; r < i.length; r++) {
                        var s = i[r].split("=");
                        if (s[0] === t) {
                            n = s;
                            break
                        }
                    }
                    if (!b(n) || n.length < 2) return "";
                    var o = n[1];
                    try {
                        o = decodeURIComponent(o)
                    } catch (e) {
                        $.error("Skipping decoding for malformed query param: " + o)
                    }
                    return o.replace(/\+/g, " ")
                },
                pt = function(e, t) {
                    var n = e.match(new RegExp(t + "=([^&]*)"));
                    return n ? n[1] : null
                },
                gt = "Mobile",
                _t = "iOS",
                mt = "Android",
                yt = "Tablet",
                bt = mt + " " + yt,
                kt = "iPad",
                wt = "Apple",
                St = wt + " Watch",
                Et = "Safari",
                xt = "BlackBerry",
                It = "Samsung",
                Ft = It + "Browser",
                Pt = It + " Internet",
                Rt = "Chrome",
                Tt = Rt + " OS",
                Ct = Rt + " " + _t,
                Mt = "Internet Explorer",
                $t = Mt + " " + gt,
                Ot = "Opera",
                At = Ot + " Mini",
                Lt = "Edge",
                Dt = "Microsoft " + Lt,
                Nt = "Firefox",
                qt = Nt + " " + _t,
                Bt = "Nintendo",
                Ht = "PlayStation",
                Ut = "Xbox",
                jt = mt + " " + gt,
                Wt = gt + " " + Et,
                zt = "Windows",
                Vt = zt + " Phone",
                Gt = "Nokia",
                Qt = "Ouya",
                Jt = "Generic",
                Yt = Jt + " " + gt.toLowerCase(),
                Xt = Jt + " " + yt.toLowerCase(),
                Kt = "Konqueror",
                Zt = "(\\d+(\\.\\d+)?)",
                en = new RegExp("Version/" + Zt),
                tn = new RegExp(Ut, "i"),
                nn = new RegExp(Ht + " \\w+", "i"),
                rn = new RegExp(Bt + " \\w+", "i"),
                sn = new RegExp(xt + "|PlayBook|BB10", "i"),
                on = {
                    "NT3.51": "NT 3.11",
                    "NT4.0": "NT 4.0",
                    "5.0": "2000",
                    5.1: "XP",
                    5.2: "XP",
                    "6.0": "Vista",
                    6.1: "7",
                    6.2: "8",
                    6.3: "8.1",
                    6.4: "10",
                    "10.0": "10"
                },
                an = function(e, t) {
                    return t = t || "", K(e, " OPR/") && K(e, "Mini") ? At : K(e, " OPR/") ? Ot : sn.test(e) ? xt : K(e, "IE" + gt) || K(e, "WPDesktop") ? $t : K(e, Ft) ? Pt : K(e, Lt) || K(e, "Edg/") ? Dt : K(e, "FBIOS") ? "Facebook " + gt : K(e, "UCWEB") || K(e, "UCBrowser") ? "UC Browser" : K(e, "CriOS") ? Ct : K(e, "CrMo") ? Rt : K(e, mt) && K(e, Et) ? jt : K(e, Rt) ? Rt : K(e, "FxiOS") ? qt : K(e.toLowerCase(), Kt.toLowerCase()) ? Kt : function(e, t) {
                        return t && K(t, wt) || function(e) {
                            return K(e, Et) && !K(e, Rt) && !K(e, mt)
                        }(e)
                    }(e, t) ? K(e, gt) ? Wt : Et : K(e, Nt) ? Nt : K(e, "MSIE") || K(e, "Trident/") ? Mt : K(e, "Gecko") ? Nt : ""
                },
                un = (H(Ye = {}, $t, [new RegExp("rv:" + Zt)]), H(Ye, Dt, [new RegExp(Lt + "?\\/" + Zt)]), H(Ye, Rt, [new RegExp("(" + Rt + "|CrMo)\\/" + Zt)]), H(Ye, Ct, [new RegExp("CriOS\\/" + Zt)]), H(Ye, "UC Browser", [new RegExp("(UCBrowser|UCWEB)\\/" + Zt)]), H(Ye, Et, [en]), H(Ye, Wt, [en]), H(Ye, Ot, [new RegExp("(Opera|OPR)\\/" + Zt)]), H(Ye, Nt, [new RegExp(Nt + "\\/" + Zt)]), H(Ye, qt, [new RegExp("FxiOS\\/" + Zt)]), H(Ye, Kt, [new RegExp("Konqueror[:/]?" + Zt, "i")]), H(Ye, xt, [new RegExp(xt + " " + Zt), en]), H(Ye, jt, [new RegExp("android\\s" + Zt, "i")]), H(Ye, Pt, [new RegExp(Ft + "\\/" + Zt)]), H(Ye, Mt, [new RegExp("(rv:|MSIE )" + Zt)]), H(Ye, "Mozilla", [new RegExp("rv:" + Zt)]), Ye),
                ln = [
                    [new RegExp(Ut + "; " + Ut + " (.*?)[);]", "i"), function(e) {
                        return [Ut, e && e[1] || ""]
                    }],
                    [new RegExp(Bt, "i"), [Bt, ""]],
                    [new RegExp(Ht, "i"), [Ht, ""]],
                    [sn, [xt, ""]],
                    [new RegExp(zt, "i"), function(e, t) {
                        if (/Phone/.test(t) || /WPDesktop/.test(t)) return [Vt, ""];
                        if (new RegExp(gt).test(t) && !/IEMobile\b/.test(t)) return [zt + " " + gt, ""];
                        var n = /Windows NT ([0-9.]+)/i.exec(t);
                        if (n && n[1]) {
                            var i = n[1],
                                r = on[i] || "";
                            return /arm/i.test(t) && (r = "RT"), [zt, r]
                        }
                        return [zt, ""]
                    }],
                    [/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/, function(e) {
                        if (e && e[3]) {
                            var t = [e[3], e[4], e[5] || "0"];
                            return [_t, t.join(".")]
                        }
                        return [_t, ""]
                    }],
                    [/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i, function(e) {
                        var t = "";
                        return e && e.length >= 3 && (t = E(e[2]) ? e[3] : e[2]), ["watchOS", t]
                    }],
                    [new RegExp("(" + mt + " (\\d+)\\.(\\d+)\\.?(\\d+)?|" + mt + ")", "i"), function(e) {
                        if (e && e[2]) {
                            var t = [e[2], e[3], e[4] || "0"];
                            return [mt, t.join(".")]
                        }
                        return [mt, ""]
                    }],
                    [/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i, function(e) {
                        var t = ["Mac OS X", ""];
                        if (e && e[1]) {
                            var n = [e[1], e[2], e[3] || "0"];
                            t[1] = n.join(".")
                        }
                        return t
                    }],
                    [/Mac/i, ["Mac OS X", ""]],
                    [/CrOS/, [Tt, ""]],
                    [/Linux|debian/i, ["Linux", ""]]
                ],
                cn = function(e) {
                    return rn.test(e) ? Bt : nn.test(e) ? Ht : tn.test(e) ? Ut : new RegExp(Qt, "i").test(e) ? Qt : new RegExp("(" + Vt + "|WPDesktop)", "i").test(e) ? Vt : /iPad/.test(e) ? kt : /iPod/.test(e) ? "iPod Touch" : /iPhone/.test(e) ? "iPhone" : /(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e) ? St : sn.test(e) ? xt : /(kobo)\s(ereader|touch)/i.test(e) ? "Kobo" : new RegExp(Gt, "i").test(e) ? Gt : /(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e) || /(kf[a-z]+)( bui|\)).+silk\//i.test(e) ? "Kindle Fire" : /(Android|ZTE)/i.test(e) ? !new RegExp(gt).test(e) || /(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e) ? /pixel[\daxl ]{1,6}/i.test(e) && !/pixel c/i.test(e) || /(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e) || /lmy47v/i.test(e) && !/QTAQZ3/i.test(e) ? mt : bt : mt : new RegExp("(pda|" + gt + ")", "i").test(e) ? Yt : new RegExp(yt, "i").test(e) && !new RegExp(yt + " pc", "i").test(e) ? Xt : ""
                },
                dn = "https?://(.*)",
                hn = ["utm_source", "utm_medium", "utm_campaign", "utm_content", "utm_term", "gclid", "gad_source", "gclsrc", "dclid", "gbraid", "wbraid", "fbclid", "msclkid", "twclid", "li_fat_id", "mc_cid", "igshid", "ttclid", "rdt_cid"],
                fn = {
                    campaignParams: function(e) {
                        return u ? this._campaignParamsFromUrl(u.URL, e) : {}
                    },
                    _campaignParamsFromUrl: function(e, t) {
                        var n = hn.concat(t || []),
                            i = {};
                        return Y(n, (function(t) {
                            var n = vt(e, t);
                            n && (i[t] = n)
                        })), i
                    },
                    _searchEngine: function(e) {
                        return e ? 0 === e.search(dn + "google.([^/?]*)") ? "google" : 0 === e.search(dn + "bing.com") ? "bing" : 0 === e.search(dn + "yahoo.com") ? "yahoo" : 0 === e.search(dn + "duckduckgo.com") ? "duckduckgo" : null : null
                    },
                    _searchInfoFromReferrer: function(e) {
                        var t = fn._searchEngine(e),
                            n = "yahoo" != t ? "q" : "p",
                            i = {};
                        if (!F(t)) {
                            i.$search_engine = t;
                            var r = u ? vt(u.referrer, n) : "";
                            r.length && (i.ph_keyword = r)
                        }
                        return i
                    },
                    searchInfo: function() {
                        var e = null == u ? void 0 : u.referrer;
                        return e ? this._searchInfoFromReferrer(e) : {}
                    },
                    browser: an,
                    browserVersion: function(e, t) {
                        var n = an(e, t),
                            i = un[n];
                        if (E(i)) return null;
                        for (var r = 0; r < i.length; r++) {
                            var s = i[r],
                                o = e.match(s);
                            if (o) return parseFloat(o[o.length - 2])
                        }
                        return null
                    },
                    browserLanguage: function() {
                        return navigator.language || navigator.userLanguage
                    },
                    os: function(e) {
                        for (var t = 0; t < ln.length; t++) {
                            var n = U(ln[t], 2),
                                i = n[0],
                                r = n[1],
                                s = i.exec(e),
                                o = s && (k(r) ? r(s, e) : r);
                            if (o) return o
                        }
                        return ["", ""]
                    },
                    device: cn,
                    deviceType: function(e) {
                        var t = cn(e);
                        return t === kt || t === bt || "Kobo" === t || "Kindle Fire" === t || t === Xt ? yt : t === Bt || t === Ut || t === Ht || t === Qt ? "Console" : t === St ? "Wearable" : t ? gt : "Desktop"
                    },
                    referrer: function() {
                        return (null == u ? void 0 : u.referrer) || "$direct"
                    },
                    referringDomain: function() {
                        var e;
                        return null != u && u.referrer && (null === (e = ht(u.referrer)) || void 0 === e ? void 0 : e.host) || "$direct"
                    },
                    referrerInfo: function() {
                        return {
                            $referrer: this.referrer(),
                            $referring_domain: this.referringDomain()
                        }
                    },
                    initialPersonInfo: function() {
                        return {
                            r: this.referrer(),
                            u: null == l ? void 0 : l.href
                        }
                    },
                    initialPersonPropsFromInfo: function(e) {
                        var t, n = e.r,
                            i = e.u,
                            r = {
                                $initial_referrer: n,
                                $initial_referring_domain: null == n ? void 0 : "$direct" == n ? "$direct" : null === (t = ht(n)) || void 0 === t ? void 0 : t.host
                            };
                        if (i) {
                            r.$initial_current_url = i;
                            var s = ht(i);
                            r.$initial_host = null == s ? void 0 : s.host, r.$initial_pathname = null == s ? void 0 : s.pathname, Y(this._campaignParamsFromUrl(i), (function(e, t) {
                                r["$initial_" + re(t)] = e
                            }))
                        }
                        return n && Y(this._searchInfoFromReferrer(n), (function(e, t) {
                            r["$initial_" + re(t)] = e
                        })), r
                    },
                    properties: function() {
                        if (!f) return {};
                        var e = U(fn.os(f), 2),
                            t = e[0],
                            i = e[1];
                        return X(ie({
                            $os: t,
                            $os_version: i,
                            $browser: fn.browser(f, navigator.vendor),
                            $device: fn.device(f),
                            $device_type: fn.deviceType(f)
                        }), {
                            $current_url: null == l ? void 0 : l.href,
                            $host: null == l ? void 0 : l.host,
                            $pathname: null == l ? void 0 : l.pathname,
                            $raw_user_agent: f.length > 1e3 ? f.substring(0, 997) + "..." : f,
                            $browser_version: fn.browserVersion(f, navigator.vendor),
                            $browser_language: fn.browserLanguage(),
                            $screen_height: null == n ? void 0 : n.screen.height,
                            $screen_width: null == n ? void 0 : n.screen.width,
                            $viewport_height: null == n ? void 0 : n.innerHeight,
                            $viewport_width: null == n ? void 0 : n.innerWidth,
                            $lib: "web",
                            $lib_version: p.LIB_VERSION,
                            $insert_id: Math.random().toString(36).substring(2, 10) + Math.random().toString(36).substring(2, 10),
                            $time: Date.now() / 1e3
                        })
                    },
                    people_properties: function() {
                        if (!f) return {};
                        var e = U(fn.os(f), 2),
                            t = e[0],
                            n = e[1];
                        return X(ie({
                            $os: t,
                            $os_version: n,
                            $browser: fn.browser(f, navigator.vendor)
                        }), {
                            $browser_version: fn.browserVersion(f, navigator.vendor)
                        })
                    }
                },
                vn = ["cookie", "localstorage", "localstorage+cookie", "sessionstorage", "memory"],
                pn = function() {
                    function e(t) {
                        N(this, e), this.config = t, this.props = {}, this.campaign_params_saved = !1, this.name = function(e) {
                            var t = "";
                            return e.token && (t = e.token.replace(/\+/g, "PL").replace(/\//g, "SL").replace(/=/g, "EQ")), e.persistence_name ? "ph_" + e.persistence_name : "ph_" + t + "_posthog"
                        }(t), this.storage = this.buildStorage(t), this.load(), t.debug && $.info("Persistence loaded", t.persistence, L({}, this.props)), this.update_config(t, t), this.save()
                    }
                    return B(e, [{
                        key: "buildStorage",
                        value: function(e) {
                            -1 === vn.indexOf(e.persistence.toLowerCase()) && ($.critical("Unknown persistence type " + e.persistence + "; falling back to localStorage+cookie"), e.persistence = "localStorage+cookie");
                            var t = e.persistence.toLowerCase();
                            return "localstorage" === t && rt.is_supported() ? rt : "localstorage+cookie" === t && ot.is_supported() ? ot : "sessionstorage" === t && ct.is_supported() ? ct : "memory" === t ? ut : "cookie" === t ? nt : ot.is_supported() ? ot : nt
                        }
                    }, {
                        key: "properties",
                        value: function() {
                            var e = {};
                            return Y(this.props, (function(t, n) {
                                if (n === Ee && w(t))
                                    for (var i = Object.keys(t), r = 0; r < i.length; r++) e["$feature/".concat(i[r])] = t[i[r]];
                                else a = n, u = !1, (F(s = qe) ? u : o && s.indexOf === o ? -1 != s.indexOf(a) : (Y(s, (function(e) {
                                    if (u || (u = e === a)) return G
                                })), u)) || (e[n] = t);
                                var s, a, u
                            })), e
                        }
                    }, {
                        key: "load",
                        value: function() {
                            if (!this.disabled) {
                                var e = this.storage.parse(this.name);
                                e && (this.props = X({}, e))
                            }
                        }
                    }, {
                        key: "save",
                        value: function() {
                            this.disabled || this.storage.set(this.name, this.props, this.expire_days, this.cross_subdomain, this.secure, this.config.debug)
                        }
                    }, {
                        key: "remove",
                        value: function() {
                            this.storage.remove(this.name, !1), this.storage.remove(this.name, !0)
                        }
                    }, {
                        key: "clear",
                        value: function() {
                            this.remove(), this.props = {}
                        }
                    }, {
                        key: "register_once",
                        value: function(e, t, n) {
                            var i = this;
                            if (w(e)) {
                                E(t) && (t = "None"), this.expire_days = E(n) ? this.default_expiry : n;
                                var r = !1;
                                if (Y(e, (function(e, n) {
                                        i.props.hasOwnProperty(n) && i.props[n] !== t || (i.props[n] = e, r = !0)
                                    })), r) return this.save(), !0
                            }
                            return !1
                        }
                    }, {
                        key: "register",
                        value: function(e, t) {
                            var n = this;
                            if (w(e)) {
                                this.expire_days = E(t) ? this.default_expiry : t;
                                var i = !1;
                                if (Y(e, (function(t, r) {
                                        e.hasOwnProperty(r) && n.props[r] !== t && (n.props[r] = t, i = !0)
                                    })), i) return this.save(), !0
                            }
                            return !1
                        }
                    }, {
                        key: "unregister",
                        value: function(e) {
                            e in this.props && (delete this.props[e], this.save())
                        }
                    }, {
                        key: "update_campaign_params",
                        value: function() {
                            this.campaign_params_saved || (this.register(fn.campaignParams(this.config.custom_campaign_params)), this.campaign_params_saved = !0)
                        }
                    }, {
                        key: "update_search_keyword",
                        value: function() {
                            this.register(fn.searchInfo())
                        }
                    }, {
                        key: "update_referrer_info",
                        value: function() {
                            this.register_once(fn.referrerInfo(), void 0)
                        }
                    }, {
                        key: "set_initial_person_info",
                        value: function() {
                            this.props[Oe] || this.props[Ae] || this.register_once(H({}, Le, fn.initialPersonInfo()), void 0)
                        }
                    }, {
                        key: "get_referrer_info",
                        value: function() {
                            return ie({
                                $referrer: this.props.$referrer,
                                $referring_domain: this.props.$referring_domain
                            })
                        }
                    }, {
                        key: "get_initial_props",
                        value: function() {
                            var e = this,
                                t = {};
                            Y([Ae, Oe], (function(n) {
                                var i = e.props[n];
                                i && Y(i, (function(e, n) {
                                    t["$initial_" + re(n)] = e
                                }))
                            }));
                            var n = this.props[Le];
                            if (n) {
                                var i = fn.initialPersonPropsFromInfo(n);
                                X(t, i)
                            }
                            return t
                        }
                    }, {
                        key: "safe_merge",
                        value: function(e) {
                            return Y(this.props, (function(t, n) {
                                n in e || (e[n] = t)
                            })), e
                        }
                    }, {
                        key: "update_config",
                        value: function(e, t) {
                            if (this.default_expiry = this.expire_days = e.cookie_expiration, this.set_disabled(e.disable_persistence), this.set_cross_subdomain(e.cross_subdomain_cookie), this.set_secure(e.secure_cookie), e.persistence !== t.persistence) {
                                var n = this.buildStorage(e),
                                    i = this.props;
                                this.clear(), this.storage = n, this.props = i, this.save()
                            }
                        }
                    }, {
                        key: "set_disabled",
                        value: function(e) {
                            this.disabled = e, this.disabled ? this.remove() : this.save()
                        }
                    }, {
                        key: "set_cross_subdomain",
                        value: function(e) {
                            e !== this.cross_subdomain && (this.cross_subdomain = e, this.remove(), this.save())
                        }
                    }, {
                        key: "get_cross_subdomain",
                        value: function() {
                            return !!this.cross_subdomain
                        }
                    }, {
                        key: "set_secure",
                        value: function(e) {
                            e !== this.secure && (this.secure = e, this.remove(), this.save())
                        }
                    }, {
                        key: "set_event_timer",
                        value: function(e, t) {
                            var n = this.props[le] || {};
                            n[e] = t, this.props[le] = n, this.save()
                        }
                    }, {
                        key: "remove_event_timer",
                        value: function(e) {
                            var t = (this.props[le] || {})[e];
                            return E(t) || (delete this.props[le][e], this.save()), t
                        }
                    }, {
                        key: "get_property",
                        value: function(e) {
                            return this.props[e]
                        }
                    }, {
                        key: "set_property",
                        value: function(e, t) {
                            this.props[e] = t, this.save()
                        }
                    }]), e
                }();

            function gn(e) {
                return JSON.stringify(e, (t = [], function(e, n) {
                    if (w(n)) {
                        for (; t.length > 0 && t.at(-1) !== this;) t.pop();
                        return t.includes(n) ? "[Circular]" : (t.push(n), n)
                    }
                    return n
                })).length;
                var t
            }

            function _n(e) {
                var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 6606028.8;
                if (e.size >= t && e.data.length > 1) {
                    var n = Math.floor(e.data.length / 2),
                        i = e.data.slice(0, n),
                        r = e.data.slice(n);
                    return [_n({
                        size: gn(i),
                        data: i,
                        sessionId: e.sessionId,
                        windowId: e.windowId
                    }), _n({
                        size: gn(r),
                        data: r,
                        sessionId: e.sessionId,
                        windowId: e.windowId
                    })].flatMap((function(e) {
                        return e
                    }))
                }
                return [e]
            }
            var mn = function(e) {
                    return e[e.DomContentLoaded = 0] = "DomContentLoaded", e[e.Load = 1] = "Load", e[e.FullSnapshot = 2] = "FullSnapshot", e[e.IncrementalSnapshot = 3] = "IncrementalSnapshot", e[e.Meta = 4] = "Meta", e[e.Custom = 5] = "Custom", e[e.Plugin = 6] = "Plugin", e
                }(mn || {}),
                yn = function(e) {
                    return e[e.Mutation = 0] = "Mutation", e[e.MouseMove = 1] = "MouseMove", e[e.MouseInteraction = 2] = "MouseInteraction", e[e.Scroll = 3] = "Scroll", e[e.ViewportResize = 4] = "ViewportResize", e[e.Input = 5] = "Input", e[e.TouchMove = 6] = "TouchMove", e[e.MediaInteraction = 7] = "MediaInteraction", e[e.StyleSheetRule = 8] = "StyleSheetRule", e[e.CanvasMutation = 9] = "CanvasMutation", e[e.Font = 10] = "Font", e[e.Log = 11] = "Log", e[e.Drag = 12] = "Drag", e[e.StyleDeclaration = 13] = "StyleDeclaration", e[e.Selection = 14] = "Selection", e[e.AdoptedStyleSheet = 15] = "AdoptedStyleSheet", e[e.CustomElement = 16] = "CustomElement", e
                }(yn || {});

            function bn(e) {
                return e ? Q(e).split(/\s+/) : []
            }

            function kn(e) {
                var t = null == n ? void 0 : n.location.href;
                return !!(t && e && e.some((function(e) {
                    return t.match(e)
                })))
            }

            function wn(e) {
                var t = "";
                switch (D(e.className)) {
                    case "string":
                        t = e.className;
                        break;
                    case "object":
                        t = (e.className && "baseVal" in e.className ? e.className.baseVal : null) || e.getAttribute("class") || "";
                        break;
                    default:
                        t = ""
                }
                return bn(t)
            }

            function Sn(e) {
                return P(e) ? null : Q(e).split(/(\s+)/).filter((function(e) {
                    return Hn(e)
                })).join("").replace(/[\r\n]/g, " ").replace(/[ ]+/g, " ").substring(0, 255)
            }

            function En(e) {
                var t = "";
                return $n(e) && !On(e) && e.childNodes && e.childNodes.length && Y(e.childNodes, (function(e) {
                    var n;
                    Pn(e) && e.textContent && (t += null !== (n = Sn(e.textContent)) && void 0 !== n ? n : "")
                })), Q(t)
            }

            function xn(e) {
                return E(e.target) ? e.srcElement || null : null !== (t = e.target) && void 0 !== t && t.shadowRoot ? e.composedPath()[0] || null : e.target || null;
                var t
            }

            function In(e) {
                return !!e && 1 === e.nodeType
            }

            function Fn(e, t) {
                return !!e && !!e.tagName && e.tagName.toLowerCase() === t.toLowerCase()
            }

            function Pn(e) {
                return !!e && 3 === e.nodeType
            }

            function Rn(e) {
                return !!e && 11 === e.nodeType
            }
            var Tn = ["a", "button", "form", "input", "select", "textarea", "label"];

            function Cn(e) {
                var t = e.parentNode;
                return !(!t || !In(t)) && t
            }

            function Mn(e, t) {
                var i = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : void 0,
                    r = arguments.length > 3 ? arguments[3] : void 0,
                    s = arguments.length > 4 ? arguments[4] : void 0;
                if (!n || !e || Fn(e, "html") || !In(e)) return !1;
                if (null != i && i.url_allowlist && !kn(i.url_allowlist)) return !1;
                if (null != i && i.url_ignorelist && kn(i.url_ignorelist)) return !1;
                if (null != i && i.dom_event_allowlist) {
                    var o = i.dom_event_allowlist;
                    if (o && !o.some((function(e) {
                            return t.type === e
                        }))) return !1
                }
                for (var a = !1, u = [e], l = !0, c = e; c.parentNode && !Fn(c, "body");)
                    if (Rn(c.parentNode)) u.push(c.parentNode.host), c = c.parentNode.host;
                    else {
                        if (!(l = Cn(c))) break;
                        if (r || Tn.indexOf(l.tagName.toLowerCase()) > -1) a = !0;
                        else {
                            var d = n.getComputedStyle(l);
                            d && "pointer" === d.getPropertyValue("cursor") && (a = !0)
                        }
                        u.push(l), c = l
                    }
                if (! function(e, t) {
                        var n = null == t ? void 0 : t.element_allowlist;
                        if (E(n)) return !0;
                        var i, r = V(e);
                        try {
                            var s = function() {
                                var e = i.value;
                                if (n.some((function(t) {
                                        return e.tagName.toLowerCase() === t
                                    }))) return {
                                    v: !0
                                }
                            };
                            for (r.s(); !(i = r.n()).done;) {
                                var o = s();
                                if ("object" === D(o)) return o.v
                            }
                        } catch (e) {
                            r.e(e)
                        } finally {
                            r.f()
                        }
                        return !1
                    }(u, i)) return !1;
                if (! function(e, t) {
                        var n = null == t ? void 0 : t.css_selector_allowlist;
                        if (E(n)) return !0;
                        var i, r = V(e);
                        try {
                            var s = function() {
                                var e = i.value;
                                if (n.some((function(t) {
                                        return e.matches(t)
                                    }))) return {
                                    v: !0
                                }
                            };
                            for (r.s(); !(i = r.n()).done;) {
                                var o = s();
                                if ("object" === D(o)) return o.v
                            }
                        } catch (e) {
                            r.e(e)
                        } finally {
                            r.f()
                        }
                        return !1
                    }(u, i)) return !1;
                var h = n.getComputedStyle(e);
                if (h && "pointer" === h.getPropertyValue("cursor") && "click" === t.type) return !0;
                var f = e.tagName.toLowerCase();
                switch (f) {
                    case "html":
                        return !1;
                    case "form":
                        return (s || ["submit"]).indexOf(t.type) >= 0;
                    case "input":
                    case "select":
                    case "textarea":
                        return (s || ["change", "click"]).indexOf(t.type) >= 0;
                    default:
                        return a ? (s || ["click"]).indexOf(t.type) >= 0 : (s || ["click"]).indexOf(t.type) >= 0 && (Tn.indexOf(f) > -1 || "true" === e.getAttribute("contenteditable"))
                }
            }

            function $n(e) {
                for (var t = e; t.parentNode && !Fn(t, "body"); t = t.parentNode) {
                    var n = wn(t);
                    if (K(n, "ph-sensitive") || K(n, "ph-no-capture")) return !1
                }
                if (K(wn(e), "ph-include")) return !0;
                var i = e.type || "";
                if (x(i)) switch (i.toLowerCase()) {
                    case "hidden":
                    case "password":
                        return !1
                }
                var r = e.name || e.id || "";
                return !x(r) || !/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(r.replace(/[^a-zA-Z0-9]/g, ""))
            }

            function On(e) {
                return !!(Fn(e, "input") && !["button", "checkbox", "submit", "reset"].includes(e.type) || Fn(e, "select") || Fn(e, "textarea") || "true" === e.getAttribute("contenteditable"))
            }
            var An = "(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",
                Ln = new RegExp("^(?:".concat(An, ")$")),
                Dn = new RegExp(An),
                Nn = "\\d{3}-?\\d{2}-?\\d{4}",
                qn = new RegExp("^(".concat(Nn, ")$")),
                Bn = new RegExp("(".concat(Nn, ")"));

            function Hn(e) {
                var t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
                if (P(e)) return !1;
                if (x(e)) {
                    if (e = Q(e), (t ? Ln : Dn).test((e || "").replace(/[- ]/g, ""))) return !1;
                    if ((t ? qn : Bn).test(e)) return !1
                }
                return !0
            }

            function Un(e) {
                var t = En(e);
                return Hn(t = "".concat(t, " ").concat(jn(e)).trim()) ? t : ""
            }

            function jn(e) {
                var t = "";
                return e && e.childNodes && e.childNodes.length && Y(e.childNodes, (function(e) {
                    var n;
                    if (e && "span" === (null === (n = e.tagName) || void 0 === n ? void 0 : n.toLowerCase())) try {
                        var i = En(e);
                        t = "".concat(t, " ").concat(i).trim(), e.childNodes && e.childNodes.length && (t = "".concat(t, " ").concat(jn(e)).trim())
                    } catch (e) {
                        $.error(e)
                    }
                })), t
            }

            function Wn(e) {
                return function(e) {
                    var t = e.map((function(e) {
                        var t, n, i = "";
                        if (e.tag_name && (i += e.tag_name), e.attr_class) {
                            e.attr_class.sort();
                            var r, s = V(e.attr_class);
                            try {
                                for (s.s(); !(r = s.n()).done;) {
                                    var o = r.value;
                                    i += ".".concat(o.replace(/"/g, ""))
                                }
                            } catch (e) {
                                s.e(e)
                            } finally {
                                s.f()
                            }
                        }
                        var a = L(L(L(L({}, e.text ? {
                                text: e.text
                            } : {}), {}, {
                                "nth-child": null !== (t = e.nth_child) && void 0 !== t ? t : 0,
                                "nth-of-type": null !== (n = e.nth_of_type) && void 0 !== n ? n : 0
                            }, e.href ? {
                                href: e.href
                            } : {}), e.attr_id ? {
                                attr_id: e.attr_id
                            } : {}), e.attributes),
                            u = {};
                        return Z(a).sort((function(e, t) {
                            var n = U(e, 1)[0],
                                i = U(t, 1)[0];
                            return n.localeCompare(i)
                        })).forEach((function(e) {
                            var t = U(e, 2),
                                n = t[0],
                                i = t[1];
                            return u[zn(n.toString())] = zn(i.toString())
                        })), (i += ":") + Z(a).map((function(e) {
                            var t = U(e, 2),
                                n = t[0],
                                i = t[1];
                            return "".concat(n, '="').concat(i, '"')
                        })).join("")
                    }));
                    return t.join(";")
                }(function(e) {
                    return e.map((function(e) {
                        var t, n, i = {
                            text: null === (t = e.$el_text) || void 0 === t ? void 0 : t.slice(0, 400),
                            tag_name: e.tag_name,
                            href: null === (n = e.attr__href) || void 0 === n ? void 0 : n.slice(0, 2048),
                            attr_class: Vn(e),
                            attr_id: e.attr__id,
                            nth_child: e.nth_child,
                            nth_of_type: e.nth_of_type,
                            attributes: {}
                        };
                        return Z(e).filter((function(e) {
                            return 0 === U(e, 1)[0].indexOf("attr__")
                        })).forEach((function(e) {
                            var t = U(e, 2),
                                n = t[0],
                                r = t[1];
                            return i.attributes[n] = r
                        })), i
                    }))
                }(e))
            }

            function zn(e) {
                return e.replace(/"|\\"/g, '\\"')
            }

            function Vn(e) {
                var t = e.attr__class;
                return t ? b(t) ? t : bn(t) : void 0
            }
            var Gn = "[SessionRecording]",
                Qn = "redacted",
                Jn = {
                    initiatorTypes: ["audio", "beacon", "body", "css", "early-hint", "embed", "fetch", "frame", "iframe", "icon", "image", "img", "input", "link", "navigation", "object", "ping", "script", "track", "video", "xmlhttprequest"],
                    maskRequestFn: function(e) {
                        return e
                    },
                    recordHeaders: !1,
                    recordBody: !1,
                    recordInitialRequests: !1,
                    recordPerformance: !1,
                    performanceEntryTypeToObserve: ["first-input", "navigation", "paint", "resource"],
                    payloadSizeLimitBytes: 1e6,
                    payloadHostDenyList: [".lr-ingest.io", ".ingest.sentry.io"]
                },
                Yn = ["authorization", "x-forwarded-for", "authorization", "cookie", "set-cookie", "x-api-key", "x-real-ip", "remote-addr", "forwarded", "proxy-authorization", "x-csrf-token", "x-csrftoken", "x-xsrf-token"],
                Xn = ["password", "secret", "passwd", "api_key", "apikey", "auth", "credentials", "mysql_pwd", "privatekey", "private_key", "token"],
                Kn = ["/s/", "/e/", "/i/"];

            function Zn(e, t, n, i) {
                if (P(e)) return e;
                var r = (null == t ? void 0 : t["content-length"]) || function(e) {
                    return new Blob([e]).size
                }(e);
                return x(r) && (r = parseInt(r)), r > n ? Gn + " ".concat(i, " body too large to record (").concat(r, " bytes)") : e
            }

            function ei(e, t) {
                if (P(e)) return e;
                var n = e;
                return Hn(n, !1) || (n = Gn + " " + t + " body " + Qn), Y(Xn, (function(e) {
                    var i, r;
                    null !== (i = n) && void 0 !== i && i.length && -1 !== (null === (r = n) || void 0 === r ? void 0 : r.indexOf(e)) && (n = Gn + " " + t + " body " + Qn + " as might contain: " + e)
                })), n
            }
            var ti = B((function e(t) {
                    var n, i, r = this,
                        s = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                    N(this, e), H(this, "bucketSize", 100), H(this, "refillRate", 10), H(this, "mutationBuckets", {}), H(this, "loggedTracker", {}), H(this, "refillBuckets", (function() {
                        Object.keys(r.mutationBuckets).forEach((function(e) {
                            r.mutationBuckets[e] = r.mutationBuckets[e] + r.refillRate, r.mutationBuckets[e] >= r.bucketSize && delete r.mutationBuckets[e]
                        }))
                    })), H(this, "getNodeOrRelevantParent", (function(e) {
                        var t = r.rrweb.mirror.getNode(e);
                        if ("svg" !== (null == t ? void 0 : t.nodeName) && t instanceof Element) {
                            var n = t.closest("svg");
                            if (n) return [r.rrweb.mirror.getId(n), n]
                        }
                        return [e, t]
                    })), H(this, "numberOfChanges", (function(e) {
                        var t, n, i, r, s, o, a, u;
                        return (null !== (t = null === (n = e.removes) || void 0 === n ? void 0 : n.length) && void 0 !== t ? t : 0) + (null !== (i = null === (r = e.attributes) || void 0 === r ? void 0 : r.length) && void 0 !== i ? i : 0) + (null !== (s = null === (o = e.texts) || void 0 === o ? void 0 : o.length) && void 0 !== s ? s : 0) + (null !== (a = null === (u = e.adds) || void 0 === u ? void 0 : u.length) && void 0 !== a ? a : 0)
                    })), H(this, "throttleMutations", (function(e) {
                        if (3 !== e.type || 0 !== e.data.source) return e;
                        var t = e.data,
                            n = r.numberOfChanges(t);
                        t.attributes && (t.attributes = t.attributes.filter((function(e) {
                            var t, n, i, s = U(r.getNodeOrRelevantParent(e.id), 2),
                                o = s[0],
                                a = s[1];
                            return 0 !== r.mutationBuckets[o] && (r.mutationBuckets[o] = null !== (t = r.mutationBuckets[o]) && void 0 !== t ? t : r.bucketSize, r.mutationBuckets[o] = Math.max(r.mutationBuckets[o] - 1, 0), 0 === r.mutationBuckets[o] && (r.loggedTracker[o] || (r.loggedTracker[o] = !0, null === (n = (i = r.options).onBlockedNode) || void 0 === n || n.call(i, o, a))), e)
                        })));
                        var i = r.numberOfChanges(t);
                        return 0 !== i || n === i ? e : void 0
                    })), this.rrweb = t, this.options = s, this.refillRate = null !== (n = this.options.refillRate) && void 0 !== n ? n : this.refillRate, this.bucketSize = null !== (i = this.options.bucketSize) && void 0 !== i ? i : this.bucketSize, setInterval((function() {
                        r.refillBuckets()
                    }), 1e3)
                })),
                ni = Uint8Array,
                ii = Uint16Array,
                ri = Uint32Array,
                si = new ni([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, 0, 0, 0]),
                oi = new ni([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 0, 0]),
                ai = new ni([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]),
                ui = function(e, t) {
                    for (var n = new ii(31), i = 0; i < 31; ++i) n[i] = t += 1 << e[i - 1];
                    var r = new ri(n[30]);
                    for (i = 1; i < 30; ++i)
                        for (var s = n[i]; s < n[i + 1]; ++s) r[s] = s - n[i] << 5 | i;
                    return [n, r]
                },
                li = ui(si, 2),
                ci = li[0],
                di = li[1];
            ci[28] = 258, di[258] = 28;
            for (var hi = ui(oi, 0)[1], fi = new ii(32768), vi = 0; vi < 32768; ++vi) {
                var pi = (43690 & vi) >>> 1 | (21845 & vi) << 1;
                pi = (61680 & (pi = (52428 & pi) >>> 2 | (13107 & pi) << 2)) >>> 4 | (3855 & pi) << 4, fi[vi] = ((65280 & pi) >>> 8 | (255 & pi) << 8) >>> 1
            }
            var gi = function(e, t, n) {
                    for (var i = e.length, r = 0, s = new ii(t); r < i; ++r) ++s[e[r] - 1];
                    var o, a = new ii(t);
                    for (r = 0; r < t; ++r) a[r] = a[r - 1] + s[r - 1] << 1;
                    if (n) {
                        o = new ii(1 << t);
                        var u = 15 - t;
                        for (r = 0; r < i; ++r)
                            if (e[r])
                                for (var l = r << 4 | e[r], c = t - e[r], d = a[e[r] - 1]++ << c, h = d | (1 << c) - 1; d <= h; ++d) o[fi[d] >>> u] = l
                    } else
                        for (o = new ii(i), r = 0; r < i; ++r) o[r] = fi[a[e[r] - 1]++] >>> 15 - e[r];
                    return o
                },
                _i = new ni(288);
            for (vi = 0; vi < 144; ++vi) _i[vi] = 8;
            for (vi = 144; vi < 256; ++vi) _i[vi] = 9;
            for (vi = 256; vi < 280; ++vi) _i[vi] = 7;
            for (vi = 280; vi < 288; ++vi) _i[vi] = 8;
            var mi = new ni(32);
            for (vi = 0; vi < 32; ++vi) mi[vi] = 5;
            var yi = gi(_i, 9, 0),
                bi = gi(mi, 5, 0),
                ki = function(e) {
                    return (e / 8 >> 0) + (7 & e && 1)
                },
                wi = function(e, t, n) {
                    (null == t || t < 0) && (t = 0), (null == n || n > e.length) && (n = e.length);
                    var i = new(e instanceof ii ? ii : e instanceof ri ? ri : ni)(n - t);
                    return i.set(e.subarray(t, n)), i
                },
                Si = function(e, t, n) {
                    n <<= 7 & t;
                    var i = t / 8 >> 0;
                    e[i] |= n, e[i + 1] |= n >>> 8
                },
                Ei = function(e, t, n) {
                    n <<= 7 & t;
                    var i = t / 8 >> 0;
                    e[i] |= n, e[i + 1] |= n >>> 8, e[i + 2] |= n >>> 16
                },
                xi = function(e, t) {
                    for (var n = [], i = 0; i < e.length; ++i) e[i] && n.push({
                        s: i,
                        f: e[i]
                    });
                    var r = n.length,
                        s = n.slice();
                    if (!r) return [new ni(0), 0];
                    if (1 == r) {
                        var o = new ni(n[0].s + 1);
                        return o[n[0].s] = 1, [o, 1]
                    }
                    n.sort((function(e, t) {
                        return e.f - t.f
                    })), n.push({
                        s: -1,
                        f: 25001
                    });
                    var a = n[0],
                        u = n[1],
                        l = 0,
                        c = 1,
                        d = 2;
                    for (n[0] = {
                            s: -1,
                            f: a.f + u.f,
                            l: a,
                            r: u
                        }; c != r - 1;) a = n[n[l].f < n[d].f ? l++ : d++], u = n[l != c && n[l].f < n[d].f ? l++ : d++], n[c++] = {
                        s: -1,
                        f: a.f + u.f,
                        l: a,
                        r: u
                    };
                    var h = s[0].s;
                    for (i = 1; i < r; ++i) s[i].s > h && (h = s[i].s);
                    var f = new ii(h + 1),
                        v = Ii(n[c - 1], f, 0);
                    if (v > t) {
                        i = 0;
                        var p = 0,
                            g = v - t,
                            _ = 1 << g;
                        for (s.sort((function(e, t) {
                                return f[t.s] - f[e.s] || e.f - t.f
                            })); i < r; ++i) {
                            var m = s[i].s;
                            if (!(f[m] > t)) break;
                            p += _ - (1 << v - f[m]), f[m] = t
                        }
                        for (p >>>= g; p > 0;) {
                            var y = s[i].s;
                            f[y] < t ? p -= 1 << t - f[y]++ - 1 : ++i
                        }
                        for (; i >= 0 && p; --i) {
                            var b = s[i].s;
                            f[b] == t && (--f[b], ++p)
                        }
                        v = t
                    }
                    return [new ni(f), v]
                },
                Ii = function e(t, n, i) {
                    return -1 == t.s ? Math.max(e(t.l, n, i + 1), e(t.r, n, i + 1)) : n[t.s] = i
                },
                Fi = function(e) {
                    for (var t = e.length; t && !e[--t];);
                    for (var n = new ii(++t), i = 0, r = e[0], s = 1, o = function(e) {
                            n[i++] = e
                        }, a = 1; a <= t; ++a)
                        if (e[a] == r && a != t) ++s;
                        else {
                            if (!r && s > 2) {
                                for (; s > 138; s -= 138) o(32754);
                                s > 2 && (o(s > 10 ? s - 11 << 5 | 28690 : s - 3 << 5 | 12305), s = 0)
                            } else if (s > 3) {
                                for (o(r), --s; s > 6; s -= 6) o(8304);
                                s > 2 && (o(s - 3 << 5 | 8208), s = 0)
                            }
                            for (; s--;) o(r);
                            s = 1, r = e[a]
                        }
                    return [n.subarray(0, i), t]
                },
                Pi = function(e, t) {
                    for (var n = 0, i = 0; i < t.length; ++i) n += e[i] * t[i];
                    return n
                },
                Ri = function(e, t, n) {
                    var i = n.length,
                        r = ki(t + 2);
                    e[r] = 255 & i, e[r + 1] = i >>> 8, e[r + 2] = 255 ^ e[r], e[r + 3] = 255 ^ e[r + 1];
                    for (var s = 0; s < i; ++s) e[r + s + 4] = n[s];
                    return 8 * (r + 4 + i)
                },
                Ti = function(e, t, n, i, r, s, o, a, u, l, c) {
                    Si(t, c++, n), ++r[256];
                    for (var d = xi(r, 15), h = d[0], f = d[1], v = xi(s, 15), p = v[0], g = v[1], _ = Fi(h), m = _[0], y = _[1], b = Fi(p), k = b[0], w = b[1], S = new ii(19), E = 0; E < m.length; ++E) S[31 & m[E]]++;
                    for (E = 0; E < k.length; ++E) S[31 & k[E]]++;
                    for (var x = xi(S, 7), I = x[0], F = x[1], P = 19; P > 4 && !I[ai[P - 1]]; --P);
                    var R, T, C, M, $ = l + 5 << 3,
                        O = Pi(r, _i) + Pi(s, mi) + o,
                        A = Pi(r, h) + Pi(s, p) + o + 14 + 3 * P + Pi(S, I) + (2 * S[16] + 3 * S[17] + 7 * S[18]);
                    if ($ <= O && $ <= A) return Ri(t, c, e.subarray(u, u + l));
                    if (Si(t, c, 1 + (A < O)), c += 2, A < O) {
                        R = gi(h, f, 0), T = h, C = gi(p, g, 0), M = p;
                        var L = gi(I, F, 0);
                        for (Si(t, c, y - 257), Si(t, c + 5, w - 1), Si(t, c + 10, P - 4), c += 14, E = 0; E < P; ++E) Si(t, c + 3 * E, I[ai[E]]);
                        c += 3 * P;
                        for (var D = [m, k], N = 0; N < 2; ++N) {
                            var q = D[N];
                            for (E = 0; E < q.length; ++E) {
                                var B = 31 & q[E];
                                Si(t, c, L[B]), c += I[B], B > 15 && (Si(t, c, q[E] >>> 5 & 127), c += q[E] >>> 12)
                            }
                        }
                    } else R = yi, T = _i, C = bi, M = mi;
                    for (E = 0; E < a; ++E)
                        if (i[E] > 255) {
                            B = i[E] >>> 18 & 31, Ei(t, c, R[B + 257]), c += T[B + 257], B > 7 && (Si(t, c, i[E] >>> 23 & 31), c += si[B]);
                            var H = 31 & i[E];
                            Ei(t, c, C[H]), c += M[H], H > 3 && (Ei(t, c, i[E] >>> 5 & 8191), c += oi[H])
                        } else Ei(t, c, R[i[E]]), c += T[i[E]];
                    return Ei(t, c, R[256]), c + T[256]
                },
                Ci = new ri([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]),
                Mi = new ni(0),
                $i = function() {
                    for (var e = new ri(256), t = 0; t < 256; ++t) {
                        for (var n = t, i = 9; --i;) n = (1 & n && 3988292384) ^ n >>> 1;
                        e[t] = n
                    }
                    return e
                }(),
                Oi = function() {
                    var e = 4294967295;
                    return {
                        p: function(t) {
                            for (var n = e, i = 0; i < t.length; ++i) n = $i[255 & n ^ t[i]] ^ n >>> 8;
                            e = n
                        },
                        d: function() {
                            return 4294967295 ^ e
                        }
                    }
                },
                Ai = function(e, t, n, i, r) {
                    return function(e, t, n, i, r, s) {
                        var o = e.length,
                            a = new ni(i + o + 5 * (1 + Math.floor(o / 7e3)) + r),
                            u = a.subarray(i, a.length - r),
                            l = 0;
                        if (!t || o < 8)
                            for (var c = 0; c <= o; c += 65535) {
                                var d = c + 65535;
                                d < o ? l = Ri(u, l, e.subarray(c, d)) : (u[c] = s, l = Ri(u, l, e.subarray(c, o)))
                            } else {
                                for (var h = Ci[t - 1], f = h >>> 13, v = 8191 & h, p = (1 << n) - 1, g = new ii(32768), _ = new ii(p + 1), m = Math.ceil(n / 3), y = 2 * m, b = function(t) {
                                        return (e[t] ^ e[t + 1] << m ^ e[t + 2] << y) & p
                                    }, k = new ri(25e3), w = new ii(288), S = new ii(32), E = 0, x = 0, I = (c = 0, 0), F = 0, P = 0; c < o; ++c) {
                                    var R = b(c),
                                        T = 32767 & c,
                                        C = _[R];
                                    if (g[T] = C, _[R] = T, F <= c) {
                                        var M = o - c;
                                        if ((E > 7e3 || I > 24576) && M > 423) {
                                            l = Ti(e, u, 0, k, w, S, x, I, P, c - P, l), I = E = x = 0, P = c;
                                            for (var $ = 0; $ < 286; ++$) w[$] = 0;
                                            for ($ = 0; $ < 30; ++$) S[$] = 0
                                        }
                                        var O = 2,
                                            A = 0,
                                            L = v,
                                            D = T - C & 32767;
                                        if (M > 2 && R == b(c - D))
                                            for (var N = Math.min(f, M) - 1, q = Math.min(32767, c), B = Math.min(258, M); D <= q && --L && T != C;) {
                                                if (e[c + O] == e[c + O - D]) {
                                                    for (var H = 0; H < B && e[c + H] == e[c + H - D]; ++H);
                                                    if (H > O) {
                                                        if (O = H, A = D, H > N) break;
                                                        var U = Math.min(D, H - 2),
                                                            j = 0;
                                                        for ($ = 0; $ < U; ++$) {
                                                            var W = c - D + $ + 32768 & 32767,
                                                                z = W - g[W] + 32768 & 32767;
                                                            z > j && (j = z, C = W)
                                                        }
                                                    }
                                                }
                                                D += (T = C) - (C = g[T]) + 32768 & 32767
                                            }
                                        if (A) {
                                            k[I++] = 268435456 | di[O] << 18 | hi[A];
                                            var V = 31 & di[O],
                                                G = 31 & hi[A];
                                            x += si[V] + oi[G], ++w[257 + V], ++S[G], F = c + O, ++E
                                        } else k[I++] = e[c], ++w[e[c]]
                                    }
                                }
                                l = Ti(e, u, s, k, w, S, x, I, P, c - P, l), s || (l = Ri(u, l, Mi))
                            }
                        return wi(a, 0, i + ki(l) + r)
                    }(e, null == t.level ? 6 : t.level, null == t.mem ? Math.ceil(1.5 * Math.max(8, Math.min(13, Math.log(e.length)))) : 12 + t.mem, n, i, !r)
                },
                Li = function(e, t, n) {
                    for (; n; ++t) e[t] = n, n >>>= 8
                },
                Di = function(e, t) {
                    var n = t.filename;
                    if (e[0] = 31, e[1] = 139, e[2] = 8, e[8] = t.level < 2 ? 4 : 9 == t.level ? 2 : 0, e[9] = 3, 0 != t.mtime && Li(e, 4, Math.floor(new Date(t.mtime || Date.now()) / 1e3)), n) {
                        e[3] = 8;
                        for (var i = 0; i <= n.length; ++i) e[i + 10] = n.charCodeAt(i)
                    }
                },
                Ni = function(e) {
                    return 10 + (e.filename && e.filename.length + 1 || 0)
                };

            function qi(e, t) {
                void 0 === t && (t = {});
                var n = Oi(),
                    i = e.length;
                n.p(e);
                var r = Ai(e, t, Ni(t), 8),
                    s = r.length;
                return Di(r, t), Li(r, s - 8, n.d()), Li(r, s - 4, i), r
            }

            function Bi(e, t) {
                var n = e.length;
                if (!t && "undefined" != typeof TextEncoder) return (new TextEncoder).encode(e);
                for (var i = new ni(e.length + (e.length >>> 1)), r = 0, s = function(e) {
                        i[r++] = e
                    }, o = 0; o < n; ++o) {
                    if (r + 5 > i.length) {
                        var a = new ni(r + 8 + (n - o << 1));
                        a.set(i), i = a
                    }
                    var u = e.charCodeAt(o);
                    u < 128 || t ? s(u) : u < 2048 ? (s(192 | u >>> 6), s(128 | 63 & u)) : u > 55295 && u < 57344 ? (s(240 | (u = 65536 + (1047552 & u) | 1023 & e.charCodeAt(++o)) >>> 18), s(128 | u >>> 12 & 63), s(128 | u >>> 6 & 63), s(128 | 63 & u)) : (s(224 | u >>> 12), s(128 | u >>> 6 & 63), s(128 | 63 & u))
                }
                return wi(i, 0, r)
            }
            var Hi = [yn.MouseMove, yn.MouseInteraction, yn.Scroll, yn.ViewportResize, yn.Input, yn.TouchMove, yn.MediaInteraction, yn.Drag],
                Ui = function(e) {
                    return {
                        rrwebMethod: e,
                        enqueuedAt: Date.now(),
                        attempt: 1
                    }
                },
                ji = "[SessionRecording]";

            function Wi(e) {
                return function(e, t) {
                    for (var n = "", i = 0; i < e.length;) {
                        var r = e[i++];
                        n += String.fromCharCode(r)
                    }
                    return n
                }(qi(Bi(JSON.stringify(e))))
            }

            function zi(e) {
                return e.type === mn.Custom && "sessionIdle" === e.data.tag
            }
            var Vi, Gi = function() {
                    function e(t) {
                        var n = this;
                        if (N(this, e), H(this, "queuedRRWebEvents", []), H(this, "isIdle", !1), H(this, "_linkedFlagSeen", !1), H(this, "_lastActivityTimestamp", Date.now()), H(this, "_linkedFlag", null), H(this, "_removePageViewCaptureHook", void 0), H(this, "_onSessionIdListener", void 0), H(this, "_persistDecideOnSessionListener", void 0), H(this, "_samplingSessionListener", void 0), H(this, "_forceAllowLocalhostNetworkCapture", !1), H(this, "_onBeforeUnload", (function() {
                                n._flushBuffer()
                            })), H(this, "_onOffline", (function() {
                                n._tryAddCustomEvent("browser offline", {})
                            })), H(this, "_onOnline", (function() {
                                n._tryAddCustomEvent("browser online", {})
                            })), H(this, "_onVisibilityChange", (function() {
                                if (null != u && u.visibilityState) {
                                    var e = "window " + u.visibilityState;
                                    n._tryAddCustomEvent(e, {})
                                }
                            })), this.instance = t, this._captureStarted = !1, this._endpoint = "/s/", this.stopRrweb = void 0, this.receivedDecide = !1, !this.instance.sessionManager) throw $.error(ji + " started without valid sessionManager"), new Error(ji + " started without valid sessionManager. This is a bug.");
                        var i = this.sessionManager.checkAndGetSessionAndWindowId(),
                            r = i.sessionId,
                            s = i.windowId;
                        this.sessionId = r, this.windowId = s, this.buffer = this.clearBuffer(), this.sessionIdleThresholdMilliseconds >= this.sessionManager.sessionTimeoutMs && $.warn(ji + " session_idle_threshold_ms (".concat(this.sessionIdleThresholdMilliseconds, ") is greater than the session timeout (").concat(this.sessionManager.sessionTimeoutMs, "). Session will never be detected as idle"))
                    }
                    return B(e, [{
                        key: "sessionIdleThresholdMilliseconds",
                        get: function() {
                            return this.instance.config.session_recording.session_idle_threshold_ms || 3e5
                        }
                    }, {
                        key: "rrwebRecord",
                        get: function() {
                            var e, t;
                            return null == v || null === (e = v.__PosthogExtensions__) || void 0 === e || null === (t = e.rrweb) || void 0 === t ? void 0 : t.record
                        }
                    }, {
                        key: "started",
                        get: function() {
                            return this._captureStarted
                        }
                    }, {
                        key: "sessionManager",
                        get: function() {
                            if (!this.instance.sessionManager) throw new Error(ji + " must be started with a valid sessionManager.");
                            return this.instance.sessionManager
                        }
                    }, {
                        key: "fullSnapshotIntervalMillis",
                        get: function() {
                            var e;
                            return (null === (e = this.instance.config.session_recording) || void 0 === e ? void 0 : e.full_snapshot_interval_millis) || 3e5
                        }
                    }, {
                        key: "isSampled",
                        get: function() {
                            var e = this.instance.get_property(Se);
                            return T(e) ? e : null
                        }
                    }, {
                        key: "sessionDuration",
                        get: function() {
                            var e, t, n = null === (e = this.buffer) || void 0 === e ? void 0 : e.data[(null === (t = this.buffer) || void 0 === t ? void 0 : t.data.length) - 1],
                                i = this.sessionManager.checkAndGetSessionAndWindowId(!0).sessionStartTimestamp;
                            return n ? n.timestamp - i : null
                        }
                    }, {
                        key: "isRecordingEnabled",
                        get: function() {
                            var e = !!this.instance.get_property(ge),
                                t = !this.instance.config.disable_session_recording;
                            return n && e && t
                        }
                    }, {
                        key: "isConsoleLogCaptureEnabled",
                        get: function() {
                            var e = !!this.instance.get_property(_e),
                                t = this.instance.config.enable_recording_console_log;
                            return null != t ? t : e
                        }
                    }, {
                        key: "canvasRecording",
                        get: function() {
                            var e = this.instance.get_property(ye);
                            return e && e.fps && e.quality ? {
                                enabled: e.enabled,
                                fps: e.fps,
                                quality: e.quality
                            } : void 0
                        }
                    }, {
                        key: "networkPayloadCapture",
                        get: function() {
                            var e, t, n = this.instance.get_property(me),
                                i = {
                                    recordHeaders: null === (e = this.instance.config.session_recording) || void 0 === e ? void 0 : e.recordHeaders,
                                    recordBody: null === (t = this.instance.config.session_recording) || void 0 === t ? void 0 : t.recordBody
                                },
                                r = (null == i ? void 0 : i.recordHeaders) || (null == n ? void 0 : n.recordHeaders),
                                s = (null == i ? void 0 : i.recordBody) || (null == n ? void 0 : n.recordBody),
                                o = w(this.instance.config.capture_performance) ? this.instance.config.capture_performance.network_timing : this.instance.config.capture_performance,
                                a = !!(T(o) ? o : null == n ? void 0 : n.capturePerformance);
                            return r || s || a ? {
                                recordHeaders: r,
                                recordBody: s,
                                recordPerformance: a
                            } : void 0
                        }
                    }, {
                        key: "sampleRate",
                        get: function() {
                            var e = this.instance.get_property(be);
                            return R(e) ? e : null
                        }
                    }, {
                        key: "minimumDuration",
                        get: function() {
                            var e = this.instance.get_property(ke);
                            return R(e) ? e : null
                        }
                    }, {
                        key: "status",
                        get: function() {
                            return this.receivedDecide ? this.isRecordingEnabled ? P(this._linkedFlag) || this._linkedFlagSeen ? T(this.isSampled) ? this.isSampled ? "sampled" : "disabled" : "active" : "buffering" : "disabled" : "buffering"
                        }
                    }, {
                        key: "startIfEnabledOrStop",
                        value: function() {
                            var e = this;
                            this.isRecordingEnabled ? (this._startCapture(), null == n || n.addEventListener("beforeunload", this._onBeforeUnload), null == n || n.addEventListener("offline", this._onOffline), null == n || n.addEventListener("online", this._onOnline), null == n || n.addEventListener("visibilitychange", this._onVisibilityChange), this._setupSampling(), P(this._removePageViewCaptureHook) && (this._removePageViewCaptureHook = this.instance._addCaptureHook((function(t) {
                                try {
                                    if ("$pageview" === t) {
                                        var i = n ? e._maskUrl(n.location.href) : "";
                                        if (!i) return;
                                        e._tryAddCustomEvent("$pageview", {
                                            href: i
                                        })
                                    }
                                } catch (e) {
                                    $.error("Could not add $pageview to rrweb session", e)
                                }
                            }))), this._onSessionIdListener || (this._onSessionIdListener = this.sessionManager.onSessionId((function(t, n, i) {
                                i && e._tryAddCustomEvent("$session_id_change", {
                                    sessionId: t,
                                    windowId: n,
                                    changeReason: i
                                })
                            })))) : this.stopRecording()
                        }
                    }, {
                        key: "stopRecording",
                        value: function() {
                            var e, t, i;
                            this._captureStarted && this.stopRrweb && (this.stopRrweb(), this.stopRrweb = void 0, this._captureStarted = !1, null == n || n.removeEventListener("beforeunload", this._onBeforeUnload), null == n || n.removeEventListener("offline", this._onOffline), null == n || n.removeEventListener("online", this._onOnline), null == n || n.removeEventListener("visibilitychange", this._onVisibilityChange), this.clearBuffer(), clearInterval(this._fullSnapshotTimer), null === (e = this._removePageViewCaptureHook) || void 0 === e || e.call(this), this._removePageViewCaptureHook = void 0, null === (t = this._onSessionIdListener) || void 0 === t || t.call(this), this._onSessionIdListener = void 0, null === (i = this._samplingSessionListener) || void 0 === i || i.call(this), this._samplingSessionListener = void 0, $.info(ji + " stopped"))
                        }
                    }, {
                        key: "makeSamplingDecision",
                        value: function(e) {
                            var t, n = this.sessionId !== e,
                                i = this.sampleRate;
                            if (R(i)) {
                                var r, s = this.isSampled,
                                    o = n || !T(s);
                                !(r = o ? Math.random() < i : s) && o && $.warn(ji + " Sample rate (".concat(i, ") has determined that this sessionId (").concat(e, ") will not be sent to the server.")), this._tryAddCustomEvent("samplingDecisionMade", {
                                    sampleRate: i
                                }), null === (t = this.instance.persistence) || void 0 === t || t.register(H({}, Se, r))
                            } else {
                                var a;
                                null === (a = this.instance.persistence) || void 0 === a || a.register(H({}, Se, null))
                            }
                        }
                    }, {
                        key: "afterDecideResponse",
                        value: function(e) {
                            var t, n, i, r = this;
                            if (this._persistDecideResponse(e), this._linkedFlag = (null === (t = e.sessionRecording) || void 0 === t ? void 0 : t.linkedFlag) || null, null !== (n = e.sessionRecording) && void 0 !== n && n.endpoint && (this._endpoint = null === (i = e.sessionRecording) || void 0 === i ? void 0 : i.endpoint), this._setupSampling(), !P(this._linkedFlag) && !this._linkedFlagSeen) {
                                var s = x(this._linkedFlag) ? this._linkedFlag : this._linkedFlag.flag,
                                    o = x(this._linkedFlag) ? null : this._linkedFlag.variant;
                                this.instance.onFeatureFlags((function(e, t) {
                                    var n = w(t) && s in t,
                                        i = o ? t[s] === o : n;
                                    if (i) {
                                        var a = {
                                                linkedFlag: s,
                                                linkedVariant: o
                                            },
                                            u = "linked flag matched";
                                        $.info(ji + " " + u, a), r._tryAddCustomEvent(u, a)
                                    }
                                    r._linkedFlagSeen = i
                                }))
                            }
                            this.receivedDecide = !0, this.startIfEnabledOrStop()
                        }
                    }, {
                        key: "_setupSampling",
                        value: function() {
                            var e = this;
                            R(this.sampleRate) && P(this._samplingSessionListener) && (this._samplingSessionListener = this.sessionManager.onSessionId((function(t) {
                                e.makeSamplingDecision(t)
                            })))
                        }
                    }, {
                        key: "_persistDecideResponse",
                        value: function(e) {
                            if (this.instance.persistence) {
                                var t, n = this.instance.persistence,
                                    i = function() {
                                        var t, i, r, s, o, a, u, l, c = null === (t = e.sessionRecording) || void 0 === t ? void 0 : t.sampleRate,
                                            d = P(c) ? null : parseFloat(c),
                                            h = null === (i = e.sessionRecording) || void 0 === i ? void 0 : i.minimumDurationMilliseconds;
                                        n.register((H(l = {}, ge, !!e.sessionRecording), H(l, _e, null === (r = e.sessionRecording) || void 0 === r ? void 0 : r.consoleLogRecordingEnabled), H(l, me, L({
                                            capturePerformance: e.capturePerformance
                                        }, null === (s = e.sessionRecording) || void 0 === s ? void 0 : s.networkPayloadCapture)), H(l, ye, {
                                            enabled: null === (o = e.sessionRecording) || void 0 === o ? void 0 : o.recordCanvas,
                                            fps: null === (a = e.sessionRecording) || void 0 === a ? void 0 : a.canvasFps,
                                            quality: null === (u = e.sessionRecording) || void 0 === u ? void 0 : u.canvasQuality
                                        }), H(l, be, d), H(l, ke, E(h) ? null : h), l))
                                    };
                                i(), null === (t = this._persistDecideOnSessionListener) || void 0 === t || t.call(this), this._persistDecideOnSessionListener = this.sessionManager.onSessionId(i)
                            }
                        }
                    }, {
                        key: "log",
                        value: function(e) {
                            var t, n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "log";
                            null === (t = this.instance.sessionRecording) || void 0 === t || t.onRRwebEmit({
                                type: 6,
                                data: {
                                    plugin: "rrweb/console@1",
                                    payload: {
                                        level: n,
                                        trace: [],
                                        payload: [JSON.stringify(e)]
                                    }
                                },
                                timestamp: Date.now()
                            })
                        }
                    }, {
                        key: "_startCapture",
                        value: function() {
                            var e, t, n = this;
                            E(Object.assign) || this._captureStarted || this.instance.config.disable_session_recording || this.instance.consent.isOptedOut() || (this._captureStarted = !0, this.sessionManager.checkAndGetSessionAndWindowId(), this.rrwebRecord ? this._onScriptLoaded() : null === (e = v.__PosthogExtensions__) || void 0 === e || null === (t = e.loadExternalDependency) || void 0 === t || t.call(e, this.instance, "recorder", (function(e) {
                                if (e) return $.error(ji + " could not load recorder", e);
                                n._onScriptLoaded()
                            })))
                        }
                    }, {
                        key: "isInteractiveEvent",
                        value: function(e) {
                            var t;
                            return 3 === e.type && -1 !== Hi.indexOf(null === (t = e.data) || void 0 === t ? void 0 : t.source)
                        }
                    }, {
                        key: "_updateWindowAndSessionIds",
                        value: function(e) {
                            var t = this.isInteractiveEvent(e);
                            t || this.isIdle || e.timestamp - this._lastActivityTimestamp > this.sessionIdleThresholdMilliseconds && (this.isIdle = !0, clearInterval(this._fullSnapshotTimer), this._tryAddCustomEvent("sessionIdle", {
                                eventTimestamp: e.timestamp,
                                lastActivityTimestamp: this._lastActivityTimestamp,
                                threshold: this.sessionIdleThresholdMilliseconds,
                                bufferLength: this.buffer.data.length,
                                bufferSize: this.buffer.size
                            }), this._flushBuffer());
                            var n = !1;
                            if (t && (this._lastActivityTimestamp = e.timestamp, this.isIdle && (this.isIdle = !1, this._tryAddCustomEvent("sessionNoLongerIdle", {
                                    reason: "user activity",
                                    type: e.type
                                }), n = !0)), !this.isIdle) {
                                var i = this.sessionManager.checkAndGetSessionAndWindowId(!t, e.timestamp),
                                    r = i.windowId,
                                    s = i.sessionId,
                                    o = this.sessionId !== s,
                                    a = this.windowId !== r;
                                this.windowId = r, this.sessionId = s, o || a ? (this.stopRecording(), this.startIfEnabledOrStop()) : n && this._scheduleFullSnapshot()
                            }
                        }
                    }, {
                        key: "_tryRRWebMethod",
                        value: function(e) {
                            try {
                                return e.rrwebMethod(), !0
                            } catch (t) {
                                return this.queuedRRWebEvents.length < 10 ? this.queuedRRWebEvents.push({
                                    enqueuedAt: e.enqueuedAt || Date.now(),
                                    attempt: e.attempt++,
                                    rrwebMethod: e.rrwebMethod
                                }) : $.warn(ji + " could not emit queued rrweb event.", t, e), !1
                            }
                        }
                    }, {
                        key: "_tryAddCustomEvent",
                        value: function(e, t) {
                            var n = this;
                            return this._tryRRWebMethod(Ui((function() {
                                return n.rrwebRecord.addCustomEvent(e, t)
                            })))
                        }
                    }, {
                        key: "_tryTakeFullSnapshot",
                        value: function() {
                            var e = this;
                            return this._tryRRWebMethod(Ui((function() {
                                return e.rrwebRecord.takeFullSnapshot()
                            })))
                        }
                    }, {
                        key: "_onScriptLoaded",
                        value: function() {
                            for (var e, t = this, n = {
                                    blockClass: "ph-no-capture",
                                    blockSelector: void 0,
                                    ignoreClass: "ph-ignore-input",
                                    maskTextClass: "ph-mask",
                                    maskTextSelector: void 0,
                                    maskTextFn: void 0,
                                    maskAllInputs: !0,
                                    maskInputOptions: {
                                        password: !0
                                    },
                                    maskInputFn: void 0,
                                    slimDOMOptions: {},
                                    collectFonts: !1,
                                    inlineStylesheet: !0,
                                    recordCrossOriginIframes: !1
                                }, i = this.instance.config.session_recording, r = 0, s = Object.entries(i || {}); r < s.length; r++) {
                                var o = U(s[r], 2),
                                    a = o[0],
                                    u = o[1];
                                a in n && ("maskInputOptions" === a ? n.maskInputOptions = L({
                                    password: !0
                                }, u) : n[a] = u)
                            }
                            if (this.canvasRecording && this.canvasRecording.enabled && (n.recordCanvas = !0, n.sampling = {
                                    canvas: this.canvasRecording.fps
                                }, n.dataURLOptions = {
                                    type: "image/webp",
                                    quality: this.canvasRecording.quality
                                }), this.rrwebRecord) {
                                this.mutationRateLimiter = null !== (e = this.mutationRateLimiter) && void 0 !== e ? e : new ti(this.rrwebRecord, {
                                    onBlockedNode: function(e, n) {
                                        var i = "Too many mutations on node '".concat(e, "'. Rate limiting. This could be due to SVG animations or something similar");
                                        $.info(i, {
                                            node: n
                                        }), t.log(ji + " " + i, "warn")
                                    }
                                });
                                var l = this._gatherRRWebPlugins();
                                this.stopRrweb = this.rrwebRecord(L({
                                    emit: function(e) {
                                        t.onRRwebEmit(e)
                                    },
                                    plugins: l
                                }, n)), this._lastActivityTimestamp = Date.now(), this.isIdle = !1, this._tryAddCustomEvent("$session_options", {
                                    sessionRecordingOptions: n,
                                    activePlugins: l.map((function(e) {
                                        return null == e ? void 0 : e.name
                                    }))
                                }), this._tryAddCustomEvent("$posthog_config", {
                                    config: this.instance.config
                                }), $.info(ji + " started", {
                                    idleThreshold: this.sessionIdleThresholdMilliseconds,
                                    maxIdleTime: this.sessionManager.sessionTimeoutMs
                                })
                            } else $.error(ji + "onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")
                        }
                    }, {
                        key: "_scheduleFullSnapshot",
                        value: function() {
                            var e = this;
                            if (this._fullSnapshotTimer && clearInterval(this._fullSnapshotTimer), !this.isIdle) {
                                var t = this.fullSnapshotIntervalMillis;
                                t && (this._fullSnapshotTimer = setInterval((function() {
                                    e._tryTakeFullSnapshot()
                                }), t))
                            }
                        }
                    }, {
                        key: "_gatherRRWebPlugins",
                        value: function() {
                            var e, t, n, i, r = [],
                                s = null === (e = v.__PosthogExtensions__) || void 0 === e || null === (t = e.rrwebPlugins) || void 0 === t ? void 0 : t.getRecordConsolePlugin;
                            s && this.isConsoleLogCaptureEnabled && r.push(s());
                            var o = null === (n = v.__PosthogExtensions__) || void 0 === n || null === (i = n.rrwebPlugins) || void 0 === i ? void 0 : i.getRecordNetworkPlugin;
                            return this.networkPayloadCapture && k(o) && (!dt.includes(location.hostname) || this._forceAllowLocalhostNetworkCapture ? r.push(o(function(e, t) {
                                var n, i, r, s = {
                                        payloadSizeLimitBytes: Jn.payloadSizeLimitBytes,
                                        performanceEntryTypeToObserve: j(Jn.performanceEntryTypeToObserve),
                                        payloadHostDenyList: [].concat(j(t.payloadHostDenyList || []), j(Jn.payloadHostDenyList))
                                    },
                                    o = !1 !== e.session_recording.recordHeaders && t.recordHeaders,
                                    a = !1 !== e.session_recording.recordBody && t.recordBody,
                                    u = !1 !== e.capture_performance && t.recordPerformance,
                                    l = (n = s, r = Math.min(1e6, null !== (i = n.payloadSizeLimitBytes) && void 0 !== i ? i : 1e6), function(e) {
                                        return null != e && e.requestBody && (e.requestBody = Zn(e.requestBody, e.requestHeaders, r, "Request")), null != e && e.responseBody && (e.responseBody = Zn(e.responseBody, e.responseHeaders, r, "Response")), e
                                    }),
                                    c = function(e) {
                                        return l(function(e) {
                                            var t = ht(e.name);
                                            if (!(t && t.pathname && Kn.some((function(e) {
                                                    return 0 === t.pathname.indexOf(e)
                                                })))) return e
                                        }((n = (t = e).requestHeaders, P(n) || Y(Object.keys(null != n ? n : {}), (function(e) {
                                            Yn.includes(e.toLowerCase()) && (n[e] = Qn)
                                        })), t)));
                                        var t, n
                                    },
                                    d = k(e.session_recording.maskNetworkRequestFn);
                                return d && k(e.session_recording.maskCapturedNetworkRequestFn) && $.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."), d && (e.session_recording.maskCapturedNetworkRequestFn = function(t) {
                                    var n = e.session_recording.maskNetworkRequestFn({
                                        url: t.name
                                    });
                                    return L(L({}, t), {}, {
                                        name: null == n ? void 0 : n.url
                                    })
                                }), s.maskRequestFn = k(e.session_recording.maskCapturedNetworkRequestFn) ? function(t) {
                                    var n, i, r, s = c(t);
                                    return s && null !== (n = null === (i = (r = e.session_recording).maskCapturedNetworkRequestFn) || void 0 === i ? void 0 : i.call(r, s)) && void 0 !== n ? n : void 0
                                } : function(e) {
                                    return function(e) {
                                        if (!E(e)) return e.requestBody = ei(e.requestBody, "Request"), e.responseBody = ei(e.responseBody, "Response"), e
                                    }(c(e))
                                }, L(L(L({}, Jn), s), {}, {
                                    recordHeaders: o,
                                    recordBody: a,
                                    recordPerformance: u,
                                    recordInitialRequests: u
                                })
                            }(this.instance.config, this.networkPayloadCapture))) : $.info(ji + " NetworkCapture not started because we are on localhost.")), r
                        }
                    }, {
                        key: "onRRwebEmit",
                        value: function(e) {
                            var t;
                            if (this._processQueuedEvents(), e && w(e)) {
                                if (e.type === mn.Meta) {
                                    var n = this._maskUrl(e.data.href);
                                    if (this._lastHref = n, !n) return;
                                    e.data.href = n
                                } else this._pageViewFallBack();
                                e.type === mn.FullSnapshot && this._scheduleFullSnapshot();
                                var i = this.mutationRateLimiter ? this.mutationRateLimiter.throttleMutations(e) : e;
                                if (i) {
                                    var r = function(e) {
                                        var t = e;
                                        if (t && w(t) && 6 === t.type && w(t.data) && "rrweb/console@1" === t.data.plugin) {
                                            t.data.payload.payload.length > 10 && (t.data.payload.payload = t.data.payload.payload.slice(0, 10), t.data.payload.payload.push("...[truncated]"));
                                            for (var n = [], i = 0; i < t.data.payload.payload.length; i++) t.data.payload.payload[i] && t.data.payload.payload[i].length > 2e3 ? n.push(t.data.payload.payload[i].slice(0, 2e3) + "...[truncated]") : n.push(t.data.payload.payload[i]);
                                            return t.data.payload.payload = n, e
                                        }
                                        return e
                                    }(i);
                                    if (this._updateWindowAndSessionIds(r), !this.isIdle || zi(r)) {
                                        if (zi(r)) {
                                            var s = r.data.payload;
                                            if (s) {
                                                var o = s.lastActivityTimestamp,
                                                    a = s.threshold;
                                                r.timestamp = o + a
                                            }
                                        }
                                        var u = null === (t = this.instance.config.session_recording.compress_events) || void 0 === t || t ? function(e, t) {
                                                if (gn(e) < 1024) return e;
                                                try {
                                                    if (e.type === mn.FullSnapshot) return L(L({}, e), {}, {
                                                        data: Wi(e.data),
                                                        cv: "2024-10"
                                                    });
                                                    if (e.type === mn.IncrementalSnapshot && e.data.source === yn.Mutation) return L(L({}, e), {}, {
                                                        cv: "2024-10",
                                                        data: L(L({}, e.data), {}, {
                                                            texts: Wi(e.data.texts),
                                                            attributes: Wi(e.data.attributes),
                                                            removes: Wi(e.data.removes),
                                                            adds: Wi(e.data.adds)
                                                        })
                                                    });
                                                    if (e.type === mn.IncrementalSnapshot && e.data.source === yn.StyleSheetRule) return L(L({}, e), {}, {
                                                        cv: "2024-10",
                                                        data: L(L({}, e.data), {}, {
                                                            adds: Wi(e.data.adds),
                                                            removes: Wi(e.data.removes)
                                                        })
                                                    })
                                                } catch (n) {
                                                    $.error(ji + " could not compress event", n), t.captureException(n || "e was not an error", {
                                                        attempted_event_type: (null == e ? void 0 : e.type) || "no event type"
                                                    })
                                                }
                                                return e
                                            }(r, this.instance) : r,
                                            l = {
                                                $snapshot_bytes: gn(u),
                                                $snapshot_data: u,
                                                $session_id: this.sessionId,
                                                $window_id: this.windowId
                                            };
                                        "disabled" !== this.status ? this._captureSnapshotBuffered(l) : this.clearBuffer()
                                    }
                                }
                            }
                        }
                    }, {
                        key: "_pageViewFallBack",
                        value: function() {
                            if (!this.instance.config.capture_pageview && n) {
                                var e = this._maskUrl(n.location.href);
                                this._lastHref !== e && (this._tryAddCustomEvent("$url_changed", {
                                    href: e
                                }), this._lastHref = e)
                            }
                        }
                    }, {
                        key: "_processQueuedEvents",
                        value: function() {
                            var e = this;
                            if (this.queuedRRWebEvents.length) {
                                var t = j(this.queuedRRWebEvents);
                                this.queuedRRWebEvents = [], t.forEach((function(t) {
                                    Date.now() - t.enqueuedAt <= 2e3 && e._tryRRWebMethod(t)
                                }))
                            }
                        }
                    }, {
                        key: "_maskUrl",
                        value: function(e) {
                            var t = this.instance.config.session_recording;
                            if (t.maskNetworkRequestFn) {
                                var n, i = {
                                    url: e
                                };
                                return null === (n = i = t.maskNetworkRequestFn(i)) || void 0 === n ? void 0 : n.url
                            }
                            return e
                        }
                    }, {
                        key: "clearBuffer",
                        value: function() {
                            return this.buffer = {
                                size: 0,
                                data: [],
                                sessionId: this.sessionId,
                                windowId: this.windowId
                            }, this.buffer
                        }
                    }, {
                        key: "_flushBuffer",
                        value: function() {
                            var e = this;
                            this.flushBufferTimer && (clearTimeout(this.flushBufferTimer), this.flushBufferTimer = void 0);
                            var t = this.minimumDuration,
                                n = this.sessionDuration,
                                i = R(n) && n >= 0,
                                r = R(t) && i && n < t;
                            return "buffering" === this.status || r ? (this.flushBufferTimer = setTimeout((function() {
                                e._flushBuffer()
                            }), 2e3), this.buffer) : (this.buffer.data.length > 0 && _n(this.buffer).forEach((function(t) {
                                e._captureSnapshot({
                                    $snapshot_bytes: t.size,
                                    $snapshot_data: t.data,
                                    $session_id: t.sessionId,
                                    $window_id: t.windowId
                                })
                            })), this.clearBuffer())
                        }
                    }, {
                        key: "_captureSnapshotBuffered",
                        value: function(e) {
                            var t, n = this,
                                i = 2 + ((null === (t = this.buffer) || void 0 === t ? void 0 : t.data.length) || 0);
                            !this.isIdle && (this.buffer.size + e.$snapshot_bytes + i > 943718.4 || this.buffer.sessionId !== this.sessionId) && (this.buffer = this._flushBuffer()), this.buffer.size += e.$snapshot_bytes, this.buffer.data.push(e.$snapshot_data), this.flushBufferTimer || this.isIdle || (this.flushBufferTimer = setTimeout((function() {
                                n._flushBuffer()
                            }), 2e3))
                        }
                    }, {
                        key: "_captureSnapshot",
                        value: function(e) {
                            this.instance.capture("$snapshot", e, {
                                _url: this.instance.requestRouter.endpointFor("api", this._endpoint),
                                _noTruncate: !0,
                                _batchKey: "recordings",
                                skip_client_rate_limiting: !0
                            })
                        }
                    }, {
                        key: "overrideLinkedFlag",
                        value: function() {
                            this._linkedFlagSeen = !0
                        }
                    }]), e
                }(),
                Qi = function() {
                    function e(t) {
                        N(this, e), this.instance = t, this.instance.decideEndpointWasHit = this.instance._hasBootstrappedFeatureFlags()
                    }
                    return B(e, [{
                        key: "call",
                        value: function() {
                            var e = this,
                                n = {
                                    token: this.instance.config.token,
                                    distinct_id: this.instance.get_distinct_id(),
                                    groups: this.instance.getGroups(),
                                    person_properties: this.instance.get_property(Ie),
                                    group_properties: this.instance.get_property(Fe),
                                    disable_flags: this.instance.config.advanced_disable_feature_flags || this.instance.config.advanced_disable_feature_flags_on_first_load || void 0
                                };
                            this.instance._send_request({
                                method: "POST",
                                url: this.instance.requestRouter.endpointFor("api", "/decide/?v=3"),
                                data: n,
                                compression: this.instance.config.disable_compression ? void 0 : t.Compression.Base64,
                                timeout: this.instance.config.feature_flag_request_timeout_ms,
                                callback: function(t) {
                                    return e.parseDecideResponse(t.json)
                                }
                            })
                        }
                    }, {
                        key: "parseDecideResponse",
                        value: function(e) {
                            var t = this;
                            this.instance.featureFlags.setReloadingPaused(!1), this.instance.featureFlags._startReloadTimer();
                            var n = !e;
                            if (this.instance.config.advanced_disable_feature_flags_on_first_load || this.instance.config.advanced_disable_feature_flags || this.instance.featureFlags.receivedFeatureFlags(null != e ? e : {}, n), n) $.error("Failed to fetch feature flags from PostHog.");
                            else {
                                if (!u || !u.body) return $.info("document not ready yet, trying again in 500 milliseconds..."), void setTimeout((function() {
                                    t.parseDecideResponse(e)
                                }), 500);
                                if (this.instance._afterDecideResponse(e), e.siteApps)
                                    if (this.instance.config.opt_in_site_apps) {
                                        var i, r = V(e.siteApps);
                                        try {
                                            var s = function() {
                                                var e, n, r = i.value,
                                                    s = r.id,
                                                    o = r.url;
                                                v["__$$ph_site_app_".concat(s)] = t.instance, null === (e = v.__PosthogExtensions__) || void 0 === e || null === (n = e.loadSiteApp) || void 0 === n || n.call(e, t.instance, o, (function(e) {
                                                    if (e) return $.error("Error while initializing PostHog app with config id ".concat(s), e)
                                                }))
                                            };
                                            for (r.s(); !(i = r.n()).done;) s()
                                        } catch (e) {
                                            r.e(e)
                                        } finally {
                                            r.f()
                                        }
                                    } else e.siteApps.length > 0 && $.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')
                            }
                        }
                    }]), e
                }(),
                Ji = null != n && n.location ? pt(n.location.hash, "__posthog") || pt(location.hash, "state") : null,
                Yi = "_postHogToolbarParams";
            ! function(e) {
                e[e.UNINITIALIZED = 0] = "UNINITIALIZED", e[e.LOADING = 1] = "LOADING", e[e.LOADED = 2] = "LOADED"
            }(Vi || (Vi = {}));
            var Xi = function() {
                    function e(t) {
                        N(this, e), this.instance = t
                    }
                    return B(e, [{
                        key: "setToolbarState",
                        value: function(e) {
                            v.ph_toolbar_state = e
                        }
                    }, {
                        key: "getToolbarState",
                        value: function() {
                            var e;
                            return null !== (e = v.ph_toolbar_state) && void 0 !== e ? e : Vi.UNINITIALIZED
                        }
                    }, {
                        key: "maybeLoadToolbar",
                        value: function() {
                            var e, t, i = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : void 0,
                                r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : void 0,
                                s = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : void 0;
                            if (!n || !u) return !1;
                            i = null !== (e = i) && void 0 !== e ? e : n.location, s = null !== (t = s) && void 0 !== t ? t : n.history;
                            try {
                                if (!r) {
                                    try {
                                        n.localStorage.setItem("test", "test"), n.localStorage.removeItem("test")
                                    } catch (e) {
                                        return !1
                                    }
                                    r = null == n ? void 0 : n.localStorage
                                }
                                var o, a = Ji || pt(i.hash, "__posthog") || pt(i.hash, "state"),
                                    l = a ? te((function() {
                                        return JSON.parse(atob(decodeURIComponent(a)))
                                    })) || te((function() {
                                        return JSON.parse(decodeURIComponent(a))
                                    })) : null;
                                return l && "ph_authorize" === l.action ? ((o = l).source = "url", o && Object.keys(o).length > 0 && (l.desiredHash ? i.hash = l.desiredHash : s ? s.replaceState(s.state, "", i.pathname + i.search) : i.hash = "")) : ((o = JSON.parse(r.getItem(Yi) || "{}")).source = "localstorage", delete o.userIntent), !(!o.token || this.instance.config.token !== o.token || (this.loadToolbar(o), 0))
                            } catch (e) {
                                return !1
                            }
                        }
                    }, {
                        key: "_callLoadToolbar",
                        value: function(e) {
                            (v.ph_load_toolbar || v.ph_load_editor)(e, this.instance)
                        }
                    }, {
                        key: "loadToolbar",
                        value: function(e) {
                            var t = this,
                                i = !(null == u || !u.getElementById(Ne));
                            if (!n || i) return !1;
                            var r = "custom" === this.instance.requestRouter.region && this.instance.config.advanced_disable_toolbar_metrics,
                                s = L(L({
                                    token: this.instance.config.token
                                }, e), {}, {
                                    apiURL: this.instance.requestRouter.endpointFor("ui")
                                }, r ? {
                                    instrument: !1
                                } : {});
                            if (n.localStorage.setItem(Yi, JSON.stringify(L(L({}, s), {}, {
                                    source: void 0
                                }))), this.getToolbarState() === Vi.LOADED) this._callLoadToolbar(s);
                            else if (this.getToolbarState() === Vi.UNINITIALIZED) {
                                var o, a;
                                this.setToolbarState(Vi.LOADING), null === (o = v.__PosthogExtensions__) || void 0 === o || null === (a = o.loadExternalDependency) || void 0 === a || a.call(o, this.instance, "toolbar", (function(e) {
                                    if (e) return $.error("Failed to load toolbar", e), void t.setToolbarState(Vi.UNINITIALIZED);
                                    t.setToolbarState(Vi.LOADED), t._callLoadToolbar(s)
                                })), se(n, "turbolinks:load", (function() {
                                    t.setToolbarState(Vi.UNINITIALIZED), t.loadToolbar(s)
                                }))
                            }
                            return !0
                        }
                    }, {
                        key: "_loadEditor",
                        value: function(e) {
                            return this.loadToolbar(e)
                        }
                    }, {
                        key: "maybeLoadEditor",
                        value: function() {
                            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : void 0,
                                t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : void 0,
                                n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : void 0;
                            return this.maybeLoadToolbar(e, t, n)
                        }
                    }]), e
                }(),
                Ki = function() {
                    function e(t) {
                        N(this, e), H(this, "isPaused", !0), H(this, "queue", []), H(this, "flushTimeoutMs", 3e3), this.sendRequest = t
                    }
                    return B(e, [{
                        key: "enqueue",
                        value: function(e) {
                            this.queue.push(e), this.flushTimeout || this.setFlushTimeout()
                        }
                    }, {
                        key: "unload",
                        value: function() {
                            var e = this;
                            this.clearFlushTimeout();
                            var t = this.queue.length > 0 ? this.formatQueue() : {},
                                n = Object.values(t);
                            [].concat(j(n.filter((function(e) {
                                return 0 === e.url.indexOf("/e")
                            }))), j(n.filter((function(e) {
                                return 0 !== e.url.indexOf("/e")
                            })))).map((function(t) {
                                e.sendRequest(L(L({}, t), {}, {
                                    transport: "sendBeacon"
                                }))
                            }))
                        }
                    }, {
                        key: "enable",
                        value: function() {
                            this.isPaused = !1, this.setFlushTimeout()
                        }
                    }, {
                        key: "setFlushTimeout",
                        value: function() {
                            var e = this;
                            this.isPaused || (this.flushTimeout = setTimeout((function() {
                                if (e.clearFlushTimeout(), e.queue.length > 0) {
                                    var t = e.formatQueue(),
                                        n = function(n) {
                                            var i = t[n],
                                                r = (new Date).getTime();
                                            i.data && b(i.data) && Y(i.data, (function(e) {
                                                e.offset = Math.abs(e.timestamp - r), delete e.timestamp
                                            })), e.sendRequest(i)
                                        };
                                    for (var i in t) n(i)
                                }
                            }), this.flushTimeoutMs))
                        }
                    }, {
                        key: "clearFlushTimeout",
                        value: function() {
                            clearTimeout(this.flushTimeout), this.flushTimeout = void 0
                        }
                    }, {
                        key: "formatQueue",
                        value: function() {
                            var e = {};
                            return Y(this.queue, (function(t) {
                                var n, i = t,
                                    r = (i ? i.batchKey : null) || i.url;
                                E(e[r]) && (e[r] = L(L({}, i), {}, {
                                    data: []
                                })), null === (n = e[r].data) || void 0 === n || n.push(i.data)
                            })), this.queue = [], e
                        }
                    }]), e
                }(),
                Zi = !!d || !!c,
                er = "text/plain",
                tr = function(e, t) {
                    var n = U(e.split("?"), 2),
                        i = n[0],
                        r = n[1],
                        s = L({}, t);
                    null == r || r.split("&").forEach((function(e) {
                        var t = U(e.split("="), 1)[0];
                        delete s[t]
                    }));
                    var o = function(e) {
                        var t, n, i = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "&",
                            r = [];
                        return Y(e, (function(e, i) {
                            E(e) || E(i) || "undefined" === i || (t = encodeURIComponent(function(e) {
                                return e instanceof File
                            }(e) ? e.name : e.toString()), n = encodeURIComponent(i), r[r.length] = n + "=" + t)
                        })), r.join(i)
                    }(s);
                    return o = o ? (r ? r + "&" : "") + o : r, "".concat(i, "?").concat(o)
                },
                nr = function(e) {
                    var n = e.data,
                        i = e.compression;
                    if (n) {
                        if (i === t.Compression.GZipJS) {
                            var r = qi(Bi(JSON.stringify(n)), {
                                    mtime: 0
                                }),
                                s = new Blob([r], {
                                    type: er
                                });
                            return {
                                contentType: er,
                                body: s,
                                estimatedSize: s.size
                            }
                        }
                        if (i === t.Compression.Base64) {
                            var o = function(e) {
                                    var t, n, i, r, s, o = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
                                        a = 0,
                                        u = 0,
                                        l = "",
                                        c = [];
                                    if (!e) return e;
                                    e = function(e) {
                                        var t, n, i, r, s = "";
                                        for (t = n = 0, i = (e = (e + "").replace(/\r\n/g, "\n").replace(/\r/g, "\n")).length, r = 0; r < i; r++) {
                                            var o = e.charCodeAt(r),
                                                a = null;
                                            o < 128 ? n++ : a = o > 127 && o < 2048 ? String.fromCharCode(o >> 6 | 192, 63 & o | 128) : String.fromCharCode(o >> 12 | 224, o >> 6 & 63 | 128, 63 & o | 128), F(a) || (n > t && (s += e.substring(t, n)), s += a, t = n = r + 1)
                                        }
                                        return n > t && (s += e.substring(t, e.length)), s
                                    }(e);
                                    do {
                                        t = (s = e.charCodeAt(a++) << 16 | e.charCodeAt(a++) << 8 | e.charCodeAt(a++)) >> 18 & 63, n = s >> 12 & 63, i = s >> 6 & 63, r = 63 & s, c[u++] = o.charAt(t) + o.charAt(n) + o.charAt(i) + o.charAt(r)
                                    } while (a < e.length);
                                    switch (l = c.join(""), e.length % 3) {
                                        case 1:
                                            l = l.slice(0, -2) + "==";
                                            break;
                                        case 2:
                                            l = l.slice(0, -1) + "="
                                    }
                                    return l
                                }(JSON.stringify(n)),
                                a = function(e) {
                                    return "data=" + encodeURIComponent("string" == typeof e ? e : JSON.stringify(e))
                                }(o);
                            return {
                                contentType: "application/x-www-form-urlencoded",
                                body: a,
                                estimatedSize: new Blob([a]).size
                            }
                        }
                        var u = JSON.stringify(n);
                        return {
                            contentType: "application/json",
                            body: u,
                            estimatedSize: new Blob([u]).size
                        }
                    }
                },
                ir = [];
            d && ir.push({
                transport: "XHR",
                method: function(e) {
                    var t, n = new d;
                    n.open(e.method || "GET", e.url, !0);
                    var i = null !== (t = nr(e)) && void 0 !== t ? t : {},
                        r = i.contentType,
                        s = i.body;
                    Y(e.headers, (function(e, t) {
                        n.setRequestHeader(t, e)
                    })), r && n.setRequestHeader("Content-Type", r), e.timeout && (n.timeout = e.timeout), n.withCredentials = !0, n.onreadystatechange = function() {
                        if (4 === n.readyState) {
                            var t, i = {
                                statusCode: n.status,
                                text: n.responseText
                            };
                            if (200 === n.status) try {
                                i.json = JSON.parse(n.responseText)
                            } catch (e) {}
                            null === (t = e.callback) || void 0 === t || t.call(e, i)
                        }
                    }, n.send(s)
                }
            }), c && ir.push({
                transport: "fetch",
                method: function(e) {
                    var t, n, i = null !== (t = nr(e)) && void 0 !== t ? t : {},
                        r = i.contentType,
                        s = i.body,
                        o = i.estimatedSize,
                        a = new Headers;
                    Y(e.headers, (function(e, t) {
                        a.append(t, e)
                    })), r && a.append("Content-Type", r);
                    var u = e.url,
                        l = null;
                    if (h) {
                        var d = new h;
                        l = {
                            signal: d.signal,
                            timeout: setTimeout((function() {
                                return d.abort()
                            }), e.timeout)
                        }
                    }
                    c(u, {
                        method: (null == e ? void 0 : e.method) || "GET",
                        headers: a,
                        keepalive: "POST" === e.method && (o || 0) < 65536,
                        body: s,
                        signal: null === (n = l) || void 0 === n ? void 0 : n.signal
                    }).then((function(t) {
                        return t.text().then((function(n) {
                            var i, r = {
                                statusCode: t.status,
                                text: n
                            };
                            if (200 === t.status) try {
                                r.json = JSON.parse(n)
                            } catch (e) {
                                $.error(e)
                            }
                            null === (i = e.callback) || void 0 === i || i.call(e, r)
                        }))
                    })).catch((function(t) {
                        var n;
                        $.error(t), null === (n = e.callback) || void 0 === n || n.call(e, {
                            statusCode: 0,
                            text: t
                        })
                    })).finally((function() {
                        return l ? clearTimeout(l.timeout) : null
                    }))
                }
            }), null != a && a.sendBeacon && ir.push({
                transport: "sendBeacon",
                method: function(e) {
                    var t = tr(e.url, {
                        beacon: "1"
                    });
                    try {
                        var n, i = null !== (n = nr(e)) && void 0 !== n ? n : {},
                            r = i.contentType,
                            s = i.body,
                            o = "string" == typeof s ? new Blob([s], {
                                type: r
                            }) : s;
                        a.sendBeacon(t, o)
                    } catch (e) {}
                }
            });
            var rr, sr = ["retriesPerformedSoFar"],
                or = function() {
                    function e(t) {
                        var i = this;
                        N(this, e), H(this, "isPolling", !1), H(this, "pollIntervalMs", 3e3), H(this, "queue", []), this.instance = t, this.queue = [], this.areWeOnline = !0, !E(n) && "onLine" in n.navigator && (this.areWeOnline = n.navigator.onLine, n.addEventListener("online", (function() {
                            i.areWeOnline = !0, i.flush()
                        })), n.addEventListener("offline", (function() {
                            i.areWeOnline = !1
                        })))
                    }
                    return B(e, [{
                        key: "retriableRequest",
                        value: function(e) {
                            var t = this,
                                n = e.retriesPerformedSoFar,
                                i = function(e, t) {
                                    if (null == e) return {};
                                    var n, i, r = function(e, t) {
                                        if (null == e) return {};
                                        var n, i, r = {},
                                            s = Object.keys(e);
                                        for (i = 0; i < s.length; i++) n = s[i], t.indexOf(n) >= 0 || (r[n] = e[n]);
                                        return r
                                    }(e, t);
                                    if (Object.getOwnPropertySymbols) {
                                        var s = Object.getOwnPropertySymbols(e);
                                        for (i = 0; i < s.length; i++) n = s[i], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (r[n] = e[n])
                                    }
                                    return r
                                }(e, sr);
                            R(n) && n > 0 && (i.url = tr(i.url, {
                                retry_count: n
                            })), this.instance._send_request(L(L({}, i), {}, {
                                callback: function(e) {
                                    var r;
                                    200 !== e.statusCode && (e.statusCode < 400 || e.statusCode >= 500) && (null != n ? n : 0) < 10 ? t.enqueue(L({
                                        retriesPerformedSoFar: n
                                    }, i)) : null === (r = i.callback) || void 0 === r || r.call(i, e)
                                }
                            }))
                        }
                    }, {
                        key: "enqueue",
                        value: function(e) {
                            var t = e.retriesPerformedSoFar || 0;
                            e.retriesPerformedSoFar = t + 1;
                            var n = function(e) {
                                    var t = 3e3 * Math.pow(2, e),
                                        n = t / 2,
                                        i = Math.min(18e5, t),
                                        r = (Math.random() - .5) * (i - n);
                                    return Math.ceil(i + r)
                                }(t),
                                i = Date.now() + n;
                            this.queue.push({
                                retryAt: i,
                                requestOptions: e
                            });
                            var r = "Enqueued failed request for retry in ".concat(n);
                            navigator.onLine || (r += " (Browser is offline)"), $.warn(r), this.isPolling || (this.isPolling = !0, this.poll())
                        }
                    }, {
                        key: "poll",
                        value: function() {
                            var e = this;
                            this.poller && clearTimeout(this.poller), this.poller = setTimeout((function() {
                                e.areWeOnline && e.queue.length > 0 && e.flush(), e.poll()
                            }), this.pollIntervalMs)
                        }
                    }, {
                        key: "flush",
                        value: function() {
                            var e = Date.now(),
                                t = [],
                                n = this.queue.filter((function(n) {
                                    return n.retryAt < e || (t.push(n), !1)
                                }));
                            if (this.queue = t, n.length > 0) {
                                var i, r = V(n);
                                try {
                                    for (r.s(); !(i = r.n()).done;) {
                                        var s = i.value.requestOptions;
                                        this.retriableRequest(s)
                                    }
                                } catch (e) {
                                    r.e(e)
                                } finally {
                                    r.f()
                                }
                            }
                        }
                    }, {
                        key: "unload",
                        value: function() {
                            this.poller && (clearTimeout(this.poller), this.poller = void 0);
                            var e, t = V(this.queue);
                            try {
                                for (t.s(); !(e = t.n()).done;) {
                                    var n = e.value.requestOptions;
                                    try {
                                        this.instance._send_request(L(L({}, n), {}, {
                                            transport: "sendBeacon"
                                        }))
                                    } catch (e) {
                                        $.error(e)
                                    }
                                }
                            } catch (e) {
                                t.e(e)
                            } finally {
                                t.f()
                            }
                            this.queue = []
                        }
                    }]), e
                }(),
                ar = 1800,
                ur = function() {
                    function e(t, n, i, r) {
                        var s;
                        N(this, e), H(this, "_sessionIdChangedHandlers", []), this.config = t, this.persistence = n, this._windowId = void 0, this._sessionId = void 0, this._sessionStartTimestamp = null, this._sessionActivityTimestamp = null, this._sessionIdGenerator = i || Ke, this._windowIdGenerator = r || Ke;
                        var o = t.persistence_name || t.token,
                            a = t.session_idle_timeout_seconds || ar;
                        if (R(a) ? a > ar ? $.warn("session_idle_timeout_seconds cannot be  greater than 30 minutes. Using 30 minutes instead.") : a < 60 && $.warn("session_idle_timeout_seconds cannot be less than 60 seconds. Using 60 seconds instead.") : ($.warn("session_idle_timeout_seconds must be a number. Defaulting to 30 minutes."), a = ar), this._sessionTimeoutMs = 1e3 * Math.min(Math.max(a, 60), ar), this._window_id_storage_key = "ph_" + o + "_window_id", this._primary_window_exists_storage_key = "ph_" + o + "_primary_window_exists", this._canUseSessionStorage()) {
                            var u = ct.parse(this._window_id_storage_key),
                                l = ct.parse(this._primary_window_exists_storage_key);
                            u && !l ? this._windowId = u : ct.remove(this._window_id_storage_key), ct.set(this._primary_window_exists_storage_key, !0)
                        }
                        if (null !== (s = this.config.bootstrap) && void 0 !== s && s.sessionID) try {
                            var c = function(e) {
                                var t = e.replace(/-/g, "");
                                if (32 !== t.length) throw new Error("Not a valid UUID");
                                if ("7" !== t[12]) throw new Error("Not a UUIDv7");
                                return parseInt(t.substring(0, 12), 16)
                            }(this.config.bootstrap.sessionID);
                            this._setSessionId(this.config.bootstrap.sessionID, (new Date).getTime(), c)
                        } catch (t) {
                            $.error("Invalid sessionID in bootstrap", t)
                        }
                        this._listenToReloadWindow()
                    }
                    return B(e, [{
                        key: "sessionTimeoutMs",
                        get: function() {
                            return this._sessionTimeoutMs
                        }
                    }, {
                        key: "onSessionId",
                        value: function(e) {
                            var t = this;
                            return E(this._sessionIdChangedHandlers) && (this._sessionIdChangedHandlers = []), this._sessionIdChangedHandlers.push(e), this._sessionId && e(this._sessionId, this._windowId),
                                function() {
                                    t._sessionIdChangedHandlers = t._sessionIdChangedHandlers.filter((function(t) {
                                        return t !== e
                                    }))
                                }
                        }
                    }, {
                        key: "_canUseSessionStorage",
                        value: function() {
                            return "memory" !== this.config.persistence && !this.persistence.disabled && ct.is_supported()
                        }
                    }, {
                        key: "_setWindowId",
                        value: function(e) {
                            e !== this._windowId && (this._windowId = e, this._canUseSessionStorage() && ct.set(this._window_id_storage_key, e))
                        }
                    }, {
                        key: "_getWindowId",
                        value: function() {
                            return this._windowId ? this._windowId : this._canUseSessionStorage() ? ct.parse(this._window_id_storage_key) : null
                        }
                    }, {
                        key: "_setSessionId",
                        value: function(e, t, n) {
                            e === this._sessionId && t === this._sessionActivityTimestamp && n === this._sessionStartTimestamp || (this._sessionStartTimestamp = n, this._sessionActivityTimestamp = t, this._sessionId = e, this.persistence.register(H({}, we, [t, e, n])))
                        }
                    }, {
                        key: "_getSessionId",
                        value: function() {
                            if (this._sessionId && this._sessionActivityTimestamp && this._sessionStartTimestamp) return [this._sessionActivityTimestamp, this._sessionId, this._sessionStartTimestamp];
                            var e = this.persistence.props[we];
                            return b(e) && 2 === e.length && e.push(e[0]), e || [0, null, 0]
                        }
                    }, {
                        key: "resetSessionId",
                        value: function() {
                            this._setSessionId(null, null, null)
                        }
                    }, {
                        key: "_listenToReloadWindow",
                        value: function() {
                            var e = this;
                            null == n || n.addEventListener("beforeunload", (function() {
                                e._canUseSessionStorage() && ct.remove(e._primary_window_exists_storage_key)
                            }))
                        }
                    }, {
                        key: "checkAndGetSessionAndWindowId",
                        value: function() {
                            var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0],
                                t = (arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null) || (new Date).getTime(),
                                n = U(this._getSessionId(), 3),
                                i = n[0],
                                r = n[1],
                                s = n[2],
                                o = this._getWindowId(),
                                a = R(s) && s > 0 && Math.abs(t - s) > 864e5,
                                u = !1,
                                l = !r,
                                c = !e && Math.abs(t - i) > this.sessionTimeoutMs;
                            l || c || a ? (r = this._sessionIdGenerator(), o = this._windowIdGenerator(), $.info("[SessionId] new session ID generated", {
                                sessionId: r,
                                windowId: o,
                                changeReason: {
                                    noSessionId: l,
                                    activityTimeout: c,
                                    sessionPastMaximumLength: a
                                }
                            }), s = t, u = !0) : o || (o = this._windowIdGenerator(), u = !0);
                            var d = 0 === i || !e || a ? t : i,
                                h = 0 === s ? (new Date).getTime() : s;
                            return this._setWindowId(o), this._setSessionId(r, d, h), u && this._sessionIdChangedHandlers.forEach((function(e) {
                                return e(r, o, u ? {
                                    noSessionId: l,
                                    activityTimeout: c,
                                    sessionPastMaximumLength: a
                                } : void 0)
                            })), {
                                sessionId: r,
                                windowId: o,
                                sessionStartTimestamp: h,
                                changeReason: u ? {
                                    noSessionId: l,
                                    activityTimeout: c,
                                    sessionPastMaximumLength: a
                                } : void 0
                            }
                        }
                    }]), e
                }();
            ! function(e) {
                e.US = "us", e.EU = "eu", e.CUSTOM = "custom"
            }(rr || (rr = {}));
            var lr = "i.posthog.com",
                cr = function() {
                    function e(t) {
                        N(this, e), H(this, "_regionCache", {}), this.instance = t
                    }
                    return B(e, [{
                        key: "apiHost",
                        get: function() {
                            var e = this.instance.config.api_host.trim().replace(/\/$/, "");
                            return "https://app.posthog.com" === e ? "https://us.i.posthog.com" : e
                        }
                    }, {
                        key: "uiHost",
                        get: function() {
                            var e, t = null === (e = this.instance.config.ui_host) || void 0 === e ? void 0 : e.replace(/\/$/, "");
                            return t || (t = this.apiHost.replace(".".concat(lr), ".posthog.com")), "https://app.posthog.com" === t ? "https://us.posthog.com" : t
                        }
                    }, {
                        key: "region",
                        get: function() {
                            return this._regionCache[this.apiHost] || (/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost) ? this._regionCache[this.apiHost] = rr.US : /https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost) ? this._regionCache[this.apiHost] = rr.EU : this._regionCache[this.apiHost] = rr.CUSTOM), this._regionCache[this.apiHost]
                        }
                    }, {
                        key: "endpointFor",
                        value: function(e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "";
                            if (t && (t = "/" === t[0] ? t : "/".concat(t)), "ui" === e) return this.uiHost + t;
                            if (this.region === rr.CUSTOM) return this.apiHost + t;
                            var n = lr + t;
                            switch (e) {
                                case "assets":
                                    return "https://".concat(this.region, "-assets.").concat(n);
                                case "api":
                                    return "https://".concat(this.region, ".").concat(n)
                            }
                        }
                    }]), e
                }(),
                dr = "posthog-js";

            function hr(e) {
                var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                    n = t.organization,
                    i = t.projectId,
                    r = t.prefix,
                    s = t.severityAllowList,
                    o = void 0 === s ? ["error"] : s;
                return function(t) {
                    var s, a, u, l, c;
                    if ("*" !== o && !o.includes(t.level) || !e.__loaded) return t;
                    t.tags || (t.tags = {});
                    var d = e.requestRouter.endpointFor("ui", "/project/".concat(e.config.token, "/person/").concat(e.get_distinct_id()));
                    t.tags["PostHog Person URL"] = d, e.sessionRecordingStarted() && (t.tags["PostHog Recording URL"] = e.get_session_replay_url({
                        withTimestamp: !0
                    }));
                    var h = (null === (s = t.exception) || void 0 === s ? void 0 : s.values) || [],
                        f = {
                            $exception_message: (null === (a = h[0]) || void 0 === a ? void 0 : a.value) || t.message,
                            $exception_type: null === (u = h[0]) || void 0 === u ? void 0 : u.type,
                            $exception_personURL: d,
                            $exception_level: t.level,
                            $sentry_event_id: t.event_id,
                            $sentry_exception: t.exception,
                            $sentry_exception_message: (null === (l = h[0]) || void 0 === l ? void 0 : l.value) || t.message,
                            $sentry_exception_type: null === (c = h[0]) || void 0 === c ? void 0 : c.type,
                            $sentry_tags: t.tags,
                            $level: t.level
                        };
                    return n && i && (f.$sentry_url = (r || "https://sentry.io/organizations/") + n + "/issues/?project=" + i + "&query=" + t.event_id), e.exceptions.sendExceptionEvent(f), t
                }
            }
            var fr, vr, pr, gr = B((function e(t, n, i, r, s) {
                    N(this, e), this.name = dr, this.setupOnce = function(e) {
                        e(hr(t, {
                            organization: n,
                            projectId: i,
                            prefix: r,
                            severityAllowList: s
                        }))
                    }
                })),
                _r = function() {
                    function e(t) {
                        N(this, e), this._instance = t
                    }
                    return B(e, [{
                        key: "doPageView",
                        value: function(e) {
                            var t, i = this._previousPageViewProperties(e);
                            return this._currentPath = null !== (t = null == n ? void 0 : n.location.pathname) && void 0 !== t ? t : "", this._instance.scrollManager.resetContext(), this._prevPageviewTimestamp = e, i
                        }
                    }, {
                        key: "doPageLeave",
                        value: function(e) {
                            return this._previousPageViewProperties(e)
                        }
                    }, {
                        key: "_previousPageViewProperties",
                        value: function(e) {
                            var t = this._currentPath,
                                n = this._prevPageviewTimestamp,
                                i = this._instance.scrollManager.getContext();
                            if (!n) return {};
                            var r = {};
                            if (i) {
                                var s = i.maxScrollHeight,
                                    o = i.lastScrollY,
                                    a = i.maxScrollY,
                                    u = i.maxContentHeight,
                                    l = i.lastContentY,
                                    c = i.maxContentY;
                                E(s) || E(o) || E(a) || E(u) || E(l) || E(c) || (s = Math.ceil(s), o = Math.ceil(o), a = Math.ceil(a), u = Math.ceil(u), l = Math.ceil(l), c = Math.ceil(c), r = {
                                    $prev_pageview_last_scroll: o,
                                    $prev_pageview_last_scroll_percentage: s <= 1 ? 1 : mr(o / s, 0, 1),
                                    $prev_pageview_max_scroll: a,
                                    $prev_pageview_max_scroll_percentage: s <= 1 ? 1 : mr(a / s, 0, 1),
                                    $prev_pageview_last_content: l,
                                    $prev_pageview_last_content_percentage: u <= 1 ? 1 : mr(l / u, 0, 1),
                                    $prev_pageview_max_content: c,
                                    $prev_pageview_max_content_percentage: u <= 1 ? 1 : mr(c / u, 0, 1)
                                })
                            }
                            return t && (r.$prev_pageview_pathname = t), n && (r.$prev_pageview_duration = (e.getTime() - n.getTime()) / 1e3), r
                        }
                    }]), e
                }();

            function mr(e, t, n) {
                return Math.max(t, Math.min(e, n))
            }
            t.SurveyType = void 0, (fr = t.SurveyType || (t.SurveyType = {})).Popover = "popover", fr.API = "api", fr.Widget = "widget", t.SurveyQuestionType = void 0, (vr = t.SurveyQuestionType || (t.SurveyQuestionType = {})).Open = "open", vr.MultipleChoice = "multiple_choice", vr.SingleChoice = "single_choice", vr.Rating = "rating", vr.Link = "link", t.SurveyQuestionBranchingType = void 0, (pr = t.SurveyQuestionBranchingType || (t.SurveyQuestionBranchingType = {})).NextQuestion = "next_question", pr.End = "end", pr.ResponseBased = "response_based", pr.SpecificQuestion = "specific_question";
            var yr = function() {
                    function e() {
                        N(this, e), H(this, "events", {}), this.events = {}
                    }
                    return B(e, [{
                        key: "on",
                        value: function(e, t) {
                            var n = this;
                            return this.events[e] || (this.events[e] = []), this.events[e].push(t),
                                function() {
                                    n.events[e] = n.events[e].filter((function(e) {
                                        return e !== t
                                    }))
                                }
                        }
                    }, {
                        key: "emit",
                        value: function(e, t) {
                            var n, i = V(this.events[e] || []);
                            try {
                                for (i.s(); !(n = i.n()).done;)(0, n.value)(t)
                            } catch (e) {
                                i.e(e)
                            } finally {
                                i.f()
                            }
                            var r, s = V(this.events["*"] || []);
                            try {
                                for (s.s(); !(r = s.n()).done;)(0, r.value)(e, t)
                            } catch (e) {
                                s.e(e)
                            } finally {
                                s.f()
                            }
                        }
                    }]), e
                }(),
                br = function() {
                    function e(t) {
                        var n = this;
                        N(this, e), H(this, "_debugEventEmitter", new yr), H(this, "checkStep", (function(e, t) {
                            return n.checkStepEvent(e, t) && n.checkStepUrl(e, t) && n.checkStepElement(e, t)
                        })), H(this, "checkStepEvent", (function(e, t) {
                            return null == t || !t.event || (null == e ? void 0 : e.event) === (null == t ? void 0 : t.event)
                        })), this.instance = t, this.actionEvents = new Set, this.actionRegistry = new Set
                    }
                    return B(e, [{
                        key: "init",
                        value: function() {
                            var e, t, n = this;
                            E(null === (e = this.instance) || void 0 === e ? void 0 : e._addCaptureHook) || null === (t = this.instance) || void 0 === t || t._addCaptureHook((function(e, t) {
                                n.on(e, t)
                            }))
                        }
                    }, {
                        key: "register",
                        value: function(e) {
                            var t, n, i = this;
                            if (!E(null === (t = this.instance) || void 0 === t ? void 0 : t._addCaptureHook) && (e.forEach((function(e) {
                                    var t, n;
                                    null === (t = i.actionRegistry) || void 0 === t || t.add(e), null === (n = e.steps) || void 0 === n || n.forEach((function(e) {
                                        var t;
                                        null === (t = i.actionEvents) || void 0 === t || t.add((null == e ? void 0 : e.event) || "")
                                    }))
                                })), null !== (n = this.instance) && void 0 !== n && n.autocapture)) {
                                var r, s = new Set;
                                e.forEach((function(e) {
                                    var t;
                                    null === (t = e.steps) || void 0 === t || t.forEach((function(e) {
                                        null != e && e.selector && s.add(null == e ? void 0 : e.selector)
                                    }))
                                })), null === (r = this.instance) || void 0 === r || r.autocapture.setElementSelectors(s)
                            }
                        }
                    }, {
                        key: "on",
                        value: function(e, t) {
                            var n, i = this;
                            null != t && 0 != e.length && (this.actionEvents.has(e) || this.actionEvents.has(null == t ? void 0 : t.event)) && this.actionRegistry && (null === (n = this.actionRegistry) || void 0 === n ? void 0 : n.size) > 0 && this.actionRegistry.forEach((function(e) {
                                i.checkAction(t, e) && i._debugEventEmitter.emit("actionCaptured", e.name)
                            }))
                        }
                    }, {
                        key: "_addActionHook",
                        value: function(e) {
                            this.onAction("actionCaptured", (function(t) {
                                return e(t)
                            }))
                        }
                    }, {
                        key: "checkAction",
                        value: function(e, t) {
                            if (null == (null == t ? void 0 : t.steps)) return !1;
                            var n, i = V(t.steps);
                            try {
                                for (i.s(); !(n = i.n()).done;) {
                                    var r = n.value;
                                    if (this.checkStep(e, r)) return !0
                                }
                            } catch (e) {
                                i.e(e)
                            } finally {
                                i.f()
                            }
                            return !1
                        }
                    }, {
                        key: "onAction",
                        value: function(e, t) {
                            return this._debugEventEmitter.on(e, t)
                        }
                    }, {
                        key: "checkStepUrl",
                        value: function(t, n) {
                            if (null != n && n.url) {
                                var i, r = null == t || null === (i = t.properties) || void 0 === i ? void 0 : i.$current_url;
                                if (!r || "string" != typeof r) return !1;
                                if (!e.matchString(r, null == n ? void 0 : n.url, (null == n ? void 0 : n.url_matching) || "contains")) return !1
                            }
                            return !0
                        }
                    }, {
                        key: "checkStepElement",
                        value: function(t, n) {
                            if ((null != n && n.href || null != n && n.tag_name || null != n && n.text) && !this.getElementsList(t).some((function(t) {
                                    return !(null != n && n.href && !e.matchString(t.href || "", null == n ? void 0 : n.href, (null == n ? void 0 : n.href_matching) || "exact") || null != n && n.tag_name && t.tag_name !== (null == n ? void 0 : n.tag_name) || null != n && n.text && !e.matchString(t.text || "", null == n ? void 0 : n.text, (null == n ? void 0 : n.text_matching) || "exact") && !e.matchString(t.$el_text || "", null == n ? void 0 : n.text, (null == n ? void 0 : n.text_matching) || "exact"))
                                }))) return !1;
                            if (null != n && n.selector) {
                                var i, r = null == t || null === (i = t.properties) || void 0 === i ? void 0 : i.$element_selectors;
                                if (!r) return !1;
                                if (!r.includes(null == n ? void 0 : n.selector)) return !1
                            }
                            return !0
                        }
                    }, {
                        key: "getElementsList",
                        value: function(e) {
                            return null == (null == e ? void 0 : e.properties.$elements) ? [] : null == e ? void 0 : e.properties.$elements
                        }
                    }], [{
                        key: "matchString",
                        value: function(t, i, r) {
                            switch (r) {
                                case "regex":
                                    return !!n && ft(t, i);
                                case "exact":
                                    return i === t;
                                case "contains":
                                    var s = e.escapeStringRegexp(i).replace(/_/g, ".").replace(/%/g, ".*");
                                    return ft(t, s);
                                default:
                                    return !1
                            }
                        }
                    }, {
                        key: "escapeStringRegexp",
                        value: function(e) {
                            return e.replace(/[|\\{}()[\]^$+*?.]/g, "\\$&").replace(/-/g, "\\x2d")
                        }
                    }]), e
                }(),
                kr = function() {
                    function e(t) {
                        N(this, e), this.instance = t, this.eventToSurveys = new Map, this.actionToSurveys = new Map
                    }
                    return B(e, [{
                        key: "register",
                        value: function(e) {
                            var t;
                            E(null === (t = this.instance) || void 0 === t ? void 0 : t._addCaptureHook) || (this.setupEventBasedSurveys(e), this.setupActionBasedSurveys(e))
                        }
                    }, {
                        key: "setupActionBasedSurveys",
                        value: function(e) {
                            var t = this,
                                n = e.filter((function(e) {
                                    var t, n, i, r;
                                    return (null === (t = e.conditions) || void 0 === t ? void 0 : t.actions) && (null === (n = e.conditions) || void 0 === n || null === (i = n.actions) || void 0 === i || null === (r = i.values) || void 0 === r ? void 0 : r.length) > 0
                                }));
                            0 !== n.length && (null == this.actionMatcher && (this.actionMatcher = new br(this.instance), this.actionMatcher.init(), this.actionMatcher._addActionHook((function(e) {
                                t.onAction(e)
                            }))), n.forEach((function(e) {
                                var n, i, r, s, o, a, u, l, c, d;
                                e.conditions && null !== (n = e.conditions) && void 0 !== n && n.actions && null !== (i = e.conditions) && void 0 !== i && null !== (r = i.actions) && void 0 !== r && r.values && (null === (s = e.conditions) || void 0 === s || null === (o = s.actions) || void 0 === o || null === (a = o.values) || void 0 === a ? void 0 : a.length) > 0 && (null === (u = t.actionMatcher) || void 0 === u || u.register(e.conditions.actions.values), null === (l = e.conditions) || void 0 === l || null === (c = l.actions) || void 0 === c || null === (d = c.values) || void 0 === d || d.forEach((function(n) {
                                    if (n && n.name) {
                                        var i = t.actionToSurveys.get(n.name);
                                        i && i.push(e.id), t.actionToSurveys.set(n.name, i || [e.id])
                                    }
                                })))
                            })))
                        }
                    }, {
                        key: "setupEventBasedSurveys",
                        value: function(e) {
                            var t, n = this;
                            0 !== e.filter((function(e) {
                                var t, n, i, r;
                                return (null === (t = e.conditions) || void 0 === t ? void 0 : t.events) && (null === (n = e.conditions) || void 0 === n || null === (i = n.events) || void 0 === i || null === (r = i.values) || void 0 === r ? void 0 : r.length) > 0
                            })).length && (null === (t = this.instance) || void 0 === t || t._addCaptureHook((function(e, t) {
                                n.onEvent(e, t)
                            })), e.forEach((function(e) {
                                var t, i, r;
                                null === (t = e.conditions) || void 0 === t || null === (i = t.events) || void 0 === i || null === (r = i.values) || void 0 === r || r.forEach((function(t) {
                                    if (t && t.name) {
                                        var i = n.eventToSurveys.get(t.name);
                                        i && i.push(e.id), n.eventToSurveys.set(t.name, i || [e.id])
                                    }
                                }))
                            })))
                        }
                    }, {
                        key: "onEvent",
                        value: function(t, n) {
                            var i, r, s = (null === (i = this.instance) || void 0 === i || null === (r = i.persistence) || void 0 === r ? void 0 : r.props[Re]) || [];
                            if (e.SURVEY_SHOWN_EVENT_NAME == t && n && s.length > 0) {
                                var o, a = null == n || null === (o = n.properties) || void 0 === o ? void 0 : o.$survey_id;
                                if (a) {
                                    var u = s.indexOf(a);
                                    u >= 0 && (s.splice(u, 1), this._updateActivatedSurveys(s))
                                }
                            } else this.eventToSurveys.has(t) && this._updateActivatedSurveys(s.concat(this.eventToSurveys.get(t) || []))
                        }
                    }, {
                        key: "onAction",
                        value: function(e) {
                            var t, n, i = (null === (t = this.instance) || void 0 === t || null === (n = t.persistence) || void 0 === n ? void 0 : n.props[Re]) || [];
                            this.actionToSurveys.has(e) && this._updateActivatedSurveys(i.concat(this.actionToSurveys.get(e) || []))
                        }
                    }, {
                        key: "_updateActivatedSurveys",
                        value: function(e) {
                            var t, n;
                            null === (t = this.instance) || void 0 === t || null === (n = t.persistence) || void 0 === n || n.register(H({}, Re, j(new Set(e))))
                        }
                    }, {
                        key: "getSurveys",
                        value: function() {
                            var e, t;
                            return (null === (e = this.instance) || void 0 === e || null === (t = e.persistence) || void 0 === t ? void 0 : t.props[Re]) || []
                        }
                    }, {
                        key: "getEventToSurveys",
                        value: function() {
                            return this.eventToSurveys
                        }
                    }, {
                        key: "_getActionMatcher",
                        value: function() {
                            return this.actionMatcher
                        }
                    }]), e
                }();
            H(kr, "SURVEY_SHOWN_EVENT_NAME", "survey shown");
            var wr = "[Surveys]",
                Sr = {
                    icontains: function(e) {
                        return !!n && n.location.href.toLowerCase().indexOf(e.toLowerCase()) > -1
                    },
                    not_icontains: function(e) {
                        return !!n && -1 === n.location.href.toLowerCase().indexOf(e.toLowerCase())
                    },
                    regex: function(e) {
                        return !!n && ft(n.location.href, e)
                    },
                    not_regex: function(e) {
                        return !!n && !ft(n.location.href, e)
                    },
                    exact: function(e) {
                        return (null == n ? void 0 : n.location.href) === e
                    },
                    is_not: function(e) {
                        return (null == n ? void 0 : n.location.href) !== e
                    }
                },
                Er = function() {
                    function e(t) {
                        N(this, e), this.instance = t, this._surveyEventReceiver = null
                    }
                    return B(e, [{
                        key: "afterDecideResponse",
                        value: function(e) {
                            this._decideServerResponse = !!e.surveys, this.loadIfEnabled()
                        }
                    }, {
                        key: "loadIfEnabled",
                        value: function() {
                            var e, t, n, i = this,
                                r = null == v || null === (e = v.__PosthogExtensions__) || void 0 === e ? void 0 : e.generateSurveys;
                            this.instance.config.disable_surveys || !this._decideServerResponse || r || (null == this._surveyEventReceiver && (this._surveyEventReceiver = new kr(this.instance)), null === (t = v.__PosthogExtensions__) || void 0 === t || null === (n = t.loadExternalDependency) || void 0 === n || n.call(t, this.instance, "surveys", (function(e) {
                                var t, n;
                                if (e) return $.error(wr, "Could not load surveys script", e);
                                i._surveyManager = null === (t = v.__PosthogExtensions__) || void 0 === t || null === (n = t.generateSurveys) || void 0 === n ? void 0 : n.call(t, i.instance)
                            })))
                        }
                    }, {
                        key: "getSurveys",
                        value: function(e) {
                            var t = this,
                                n = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                            if (this.instance.config.disable_surveys) return e([]);
                            null == this._surveyEventReceiver && (this._surveyEventReceiver = new kr(this.instance));
                            var i = this.instance.get_property(Pe);
                            if (i && !n) return e(i);
                            this.instance._send_request({
                                url: this.instance.requestRouter.endpointFor("api", "/api/surveys/?token=".concat(this.instance.config.token)),
                                method: "GET",
                                transport: "XHR",
                                callback: function(n) {
                                    var i;
                                    if (200 !== n.statusCode || !n.json) return e([]);
                                    var r, s = n.json.surveys || [],
                                        o = s.filter((function(e) {
                                            var t, n, i, r, s, o, a, u, l, c, d, h;
                                            return (null === (t = e.conditions) || void 0 === t ? void 0 : t.events) && (null === (n = e.conditions) || void 0 === n || null === (i = n.events) || void 0 === i ? void 0 : i.values) && (null === (r = e.conditions) || void 0 === r || null === (s = r.events) || void 0 === s || null === (o = s.values) || void 0 === o ? void 0 : o.length) > 0 || (null === (a = e.conditions) || void 0 === a ? void 0 : a.actions) && (null === (u = e.conditions) || void 0 === u || null === (l = u.actions) || void 0 === l ? void 0 : l.values) && (null === (c = e.conditions) || void 0 === c || null === (d = c.actions) || void 0 === d || null === (h = d.values) || void 0 === h ? void 0 : h.length) > 0
                                        }));
                                    return o.length > 0 && (null === (r = t._surveyEventReceiver) || void 0 === r || r.register(o)), null === (i = t.instance.persistence) || void 0 === i || i.register(H({}, Pe, s)), e(s)
                                }
                            })
                        }
                    }, {
                        key: "getActiveMatchingSurveys",
                        value: function(e) {
                            var t = this,
                                n = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                            this.getSurveys((function(n) {
                                var i, r = n.filter((function(e) {
                                        return !(!e.start_date || e.end_date)
                                    })).filter((function(e) {
                                        var t, n, i, r;
                                        if (!e.conditions) return !0;
                                        var s = null === (t = e.conditions) || void 0 === t || !t.url || Sr[null !== (n = null === (i = e.conditions) || void 0 === i ? void 0 : i.urlMatchType) && void 0 !== n ? n : "icontains"](e.conditions.url),
                                            o = null === (r = e.conditions) || void 0 === r || !r.selector || (null == u ? void 0 : u.querySelector(e.conditions.selector));
                                        return s && o
                                    })),
                                    s = null === (i = t._surveyEventReceiver) || void 0 === i ? void 0 : i.getSurveys(),
                                    o = r.filter((function(e) {
                                        var n, i, r, o, a, u, l, c, d, h;
                                        if (!e.linked_flag_key && !e.targeting_flag_key && !e.internal_targeting_flag_key) return !0;
                                        var f = !e.linked_flag_key || t.instance.featureFlags.isFeatureEnabled(e.linked_flag_key),
                                            v = !e.targeting_flag_key || t.instance.featureFlags.isFeatureEnabled(e.targeting_flag_key),
                                            p = (null === (n = e.conditions) || void 0 === n ? void 0 : n.events) && (null === (i = e.conditions) || void 0 === i || null === (r = i.events) || void 0 === r ? void 0 : r.values) && (null === (o = e.conditions) || void 0 === o || null === (a = o.events) || void 0 === a ? void 0 : a.values.length) > 0,
                                            g = (null === (u = e.conditions) || void 0 === u ? void 0 : u.actions) && (null === (l = e.conditions) || void 0 === l || null === (c = l.actions) || void 0 === c ? void 0 : c.values) && (null === (d = e.conditions) || void 0 === d || null === (h = d.actions) || void 0 === h ? void 0 : h.values.length) > 0,
                                            _ = !p && !g || (null == s ? void 0 : s.includes(e.id)),
                                            m = t._canActivateRepeatedly(e),
                                            y = !(e.internal_targeting_flag_key && !m) || t.instance.featureFlags.isFeatureEnabled(e.internal_targeting_flag_key);
                                        return f && v && y && _
                                    }));
                                return e(o)
                            }), n)
                        }
                    }, {
                        key: "getNextSurveyStep",
                        value: function(e, n, i) {
                            var r, s = e.questions[n],
                                o = n + 1;
                            if (null === (r = s.branching) || void 0 === r || !r.type) return n === e.questions.length - 1 ? t.SurveyQuestionBranchingType.End : o;
                            if (s.branching.type === t.SurveyQuestionBranchingType.End) return t.SurveyQuestionBranchingType.End;
                            if (s.branching.type === t.SurveyQuestionBranchingType.SpecificQuestion) {
                                if (Number.isInteger(s.branching.index)) return s.branching.index
                            } else if (s.branching.type === t.SurveyQuestionBranchingType.ResponseBased) {
                                if (s.type === t.SurveyQuestionType.SingleChoice) {
                                    var a, u, l = s.choices.indexOf("".concat(i));
                                    if (null !== (a = s.branching) && void 0 !== a && null !== (u = a.responseValues) && void 0 !== u && u.hasOwnProperty(l)) {
                                        var c = s.branching.responseValues[l];
                                        return Number.isInteger(c) ? c : c === t.SurveyQuestionBranchingType.End ? t.SurveyQuestionBranchingType.End : o
                                    }
                                } else if (s.type === t.SurveyQuestionType.Rating) {
                                    var d, h;
                                    if ("number" != typeof i || !Number.isInteger(i)) throw new Error("The response type must be an integer");
                                    var f = function(e, t) {
                                        if (3 === t) {
                                            if (e < 1 || e > 3) throw new Error("The response must be in range 1-3");
                                            return 1 === e ? "negative" : 2 === e ? "neutral" : "positive"
                                        }
                                        if (5 === t) {
                                            if (e < 1 || e > 5) throw new Error("The response must be in range 1-5");
                                            return e <= 2 ? "negative" : 3 === e ? "neutral" : "positive"
                                        }
                                        if (7 === t) {
                                            if (e < 1 || e > 7) throw new Error("The response must be in range 1-7");
                                            return e <= 3 ? "negative" : 4 === e ? "neutral" : "positive"
                                        }
                                        if (10 === t) {
                                            if (e < 0 || e > 10) throw new Error("The response must be in range 0-10");
                                            return e <= 6 ? "detractors" : e <= 8 ? "passives" : "promoters"
                                        }
                                        throw new Error("The scale must be one of: 3, 5, 7, 10")
                                    }(i, s.scale);
                                    if (null !== (d = s.branching) && void 0 !== d && null !== (h = d.responseValues) && void 0 !== h && h.hasOwnProperty(f)) {
                                        var v = s.branching.responseValues[f];
                                        return Number.isInteger(v) ? v : v === t.SurveyQuestionBranchingType.End ? t.SurveyQuestionBranchingType.End : o
                                    }
                                }
                                return o
                            }
                            return $.warn(wr, "Falling back to next question index due to unexpected branching type"), o
                        }
                    }, {
                        key: "_canActivateRepeatedly",
                        value: function(e) {
                            var t;
                            return P(null === (t = v.__PosthogExtensions__) || void 0 === t ? void 0 : t.canActivateRepeatedly) ? ($.warn(wr, "canActivateRepeatedly is not defined, must init before calling"), !1) : v.__PosthogExtensions__.canActivateRepeatedly(e)
                        }
                    }, {
                        key: "canRenderSurvey",
                        value: function(e) {
                            var t = this;
                            P(this._surveyManager) ? $.warn(wr, "canActivateRepeatedly is not defined, must init before calling") : this.getSurveys((function(n) {
                                var i = n.filter((function(t) {
                                    return t.id === e
                                }))[0];
                                t._surveyManager.canRenderSurvey(i)
                            }))
                        }
                    }, {
                        key: "renderSurvey",
                        value: function(e, t) {
                            var n = this;
                            P(this._surveyManager) ? $.warn(wr, "canActivateRepeatedly is not defined, must init before calling") : this.getSurveys((function(i) {
                                var r = i.filter((function(t) {
                                    return t.id === e
                                }))[0];
                                n._surveyManager.renderSurvey(r, null == u ? void 0 : u.querySelector(t))
                            }))
                        }
                    }]), e
                }(),
                xr = function() {
                    function e(t) {
                        var n, i, r = this;
                        N(this, e), H(this, "serverLimits", {}), H(this, "lastEventRateLimited", !1), H(this, "checkForLimiting", (function(e) {
                            var t = e.text;
                            if (t && t.length) try {
                                (JSON.parse(t).quota_limited || []).forEach((function(e) {
                                    $.info("[RateLimiter] ".concat(e || "events", " is quota limited.")), r.serverLimits[e] = (new Date).getTime() + 6e4
                                }))
                            } catch (e) {
                                return void $.warn('[RateLimiter] could not rate limit - continuing. Error: "'.concat(null == e ? void 0 : e.message, '"'), {
                                    text: t
                                })
                            }
                        })), this.instance = t, this.captureEventsPerSecond = (null === (n = t.config.rate_limiting) || void 0 === n ? void 0 : n.events_per_second) || 10, this.captureEventsBurstLimit = Math.max((null === (i = t.config.rate_limiting) || void 0 === i ? void 0 : i.events_burst_limit) || 10 * this.captureEventsPerSecond, this.captureEventsPerSecond), this.lastEventRateLimited = this.clientRateLimitContext(!0).isRateLimited
                    }
                    return B(e, [{
                        key: "clientRateLimitContext",
                        value: function() {
                            var e, t, n, i = arguments.length > 0 && void 0 !== arguments[0] && arguments[0],
                                r = (new Date).getTime(),
                                s = null !== (e = null === (t = this.instance.persistence) || void 0 === t ? void 0 : t.get_property($e)) && void 0 !== e ? e : {
                                    tokens: this.captureEventsBurstLimit,
                                    last: r
                                };
                            s.tokens += (r - s.last) / 1e3 * this.captureEventsPerSecond, s.last = r, s.tokens > this.captureEventsBurstLimit && (s.tokens = this.captureEventsBurstLimit);
                            var o = s.tokens < 1;
                            return o || i || (s.tokens = Math.max(0, s.tokens - 1)), !o || this.lastEventRateLimited || i || this.instance.capture("$$client_ingestion_warning", {
                                $$client_ingestion_warning_message: "posthog-js client rate limited. Config is set to ".concat(this.captureEventsPerSecond, " events per second and ").concat(this.captureEventsBurstLimit, " events burst limit.")
                            }, {
                                skip_client_rate_limiting: !0
                            }), this.lastEventRateLimited = o, null === (n = this.instance.persistence) || void 0 === n || n.set_property($e, s), {
                                isRateLimited: o,
                                remainingTokens: s.tokens
                            }
                        }
                    }, {
                        key: "isServerRateLimited",
                        value: function(e) {
                            var t = this.serverLimits[e || "events"] || !1;
                            return !1 !== t && (new Date).getTime() < t
                        }
                    }]), e
                }(),
                Ir = function() {
                    return L({
                        initialPathName: (null == l ? void 0 : l.pathname) || "",
                        referringDomain: fn.referringDomain()
                    }, fn.campaignParams())
                },
                Fr = function() {
                    function e(t, n, i) {
                        var r = this;
                        N(this, e), H(this, "_onSessionIdCallback", (function(e) {
                            var t = r._getStoredProps();
                            if (!t || t.sessionId !== e) {
                                var n = {
                                    sessionId: e,
                                    props: r._sessionSourceParamGenerator()
                                };
                                r._persistence.register(H({}, Me, n))
                            }
                        })), this._sessionIdManager = t, this._persistence = n, this._sessionSourceParamGenerator = i || Ir, this._sessionIdManager.onSessionId(this._onSessionIdCallback)
                    }
                    return B(e, [{
                        key: "_getStoredProps",
                        value: function() {
                            return this._persistence.props[Me]
                        }
                    }, {
                        key: "getSessionProps",
                        value: function() {
                            var e, t = null === (e = this._getStoredProps()) || void 0 === e ? void 0 : e.props;
                            return t ? {
                                $client_session_initial_referring_host: t.referringDomain,
                                $client_session_initial_pathname: t.initialPathName,
                                $client_session_initial_utm_source: t.utm_source,
                                $client_session_initial_utm_campaign: t.utm_campaign,
                                $client_session_initial_utm_medium: t.utm_medium,
                                $client_session_initial_utm_content: t.utm_content,
                                $client_session_initial_utm_term: t.utm_term
                            } : {}
                        }
                    }]), e
                }(),
                Pr = ["ahrefsbot", "ahrefssiteaudit", "applebot", "baiduspider", "bingbot", "bingpreview", "bot.htm", "bot.php", "crawler", "deepscan", "duckduckbot", "facebookexternal", "facebookcatalog", "gptbot", "http://yandex.com/bots", "hubspot", "ia_archiver", "linkedinbot", "mj12bot", "msnbot", "nessus", "petalbot", "pinterest", "prerender", "rogerbot", "screaming frog", "semrushbot", "sitebulb", "slurp", "turnitin", "twitterbot", "vercelbot", "yahoo! slurp", "yandexbot", "headlesschrome", "cypress", "Google-HotelAdsVerifier", "adsbot-google", "apis-google", "duplexweb-google", "feedfetcher-google", "google favicon", "google web preview", "google-read-aloud", "googlebot", "googleweblight", "mediapartners-google", "storebot-google", "Bytespider;"],
                Rr = function(e, t) {
                    if (!e) return !1;
                    var n = e.toLowerCase();
                    return Pr.concat(t || []).some((function(e) {
                        var t = e.toLowerCase();
                        return -1 !== n.indexOf(t)
                    }))
                },
                Tr = function() {
                    function e() {
                        N(this, e), this.clicks = []
                    }
                    return B(e, [{
                        key: "isRageClick",
                        value: function(e, t, n) {
                            var i = this.clicks[this.clicks.length - 1];
                            if (i && Math.abs(e - i.x) + Math.abs(t - i.y) < 30 && n - i.timestamp < 1e3) {
                                if (this.clicks.push({
                                        x: e,
                                        y: t,
                                        timestamp: n
                                    }), 3 === this.clicks.length) return !0
                            } else this.clicks = [{
                                x: e,
                                y: t,
                                timestamp: n
                            }];
                            return !1
                        }
                    }]), e
                }();

            function Cr(e) {
                var t;
                return e.id === Ne || !(null === (t = e.closest) || void 0 === t || !t.call(e, "#" + Ne))
            }
            var Mr = function() {
                    function e(t) {
                        var i, r = this;
                        N(this, e), H(this, "rageclicks", new Tr), H(this, "_enabledServerSide", !1), H(this, "_initialized", !1), H(this, "_flushInterval", null), this.instance = t, this._enabledServerSide = !(null === (i = this.instance.persistence) || void 0 === i || !i.props[de]), null == n || n.addEventListener("beforeunload", (function() {
                            r.flush()
                        }))
                    }
                    return B(e, [{
                        key: "flushIntervalMilliseconds",
                        get: function() {
                            var e = 5e3;
                            return w(this.instance.config.capture_heatmaps) && this.instance.config.capture_heatmaps.flush_interval_milliseconds && (e = this.instance.config.capture_heatmaps.flush_interval_milliseconds), e
                        }
                    }, {
                        key: "isEnabled",
                        get: function() {
                            return E(this.instance.config.capture_heatmaps) ? E(this.instance.config.enable_heatmaps) ? this._enabledServerSide : this.instance.config.enable_heatmaps : !1 !== this.instance.config.capture_heatmaps
                        }
                    }, {
                        key: "startIfEnabled",
                        value: function() {
                            if (this.isEnabled) {
                                if (this._initialized) return;
                                $.info("[heatmaps] starting..."), this._setupListeners(), this._flushInterval = setInterval(this.flush.bind(this), this.flushIntervalMilliseconds)
                            } else {
                                var e;
                                clearInterval(null !== (e = this._flushInterval) && void 0 !== e ? e : void 0), this.getAndClearBuffer()
                            }
                        }
                    }, {
                        key: "afterDecideResponse",
                        value: function(e) {
                            var t = !!e.heatmaps;
                            this.instance.persistence && this.instance.persistence.register(H({}, de, t)), this._enabledServerSide = t, this.startIfEnabled()
                        }
                    }, {
                        key: "getAndClearBuffer",
                        value: function() {
                            var e = this.buffer;
                            return this.buffer = void 0, e
                        }
                    }, {
                        key: "_setupListeners",
                        value: function() {
                            var e = this;
                            n && u && (se(u, "click", (function(t) {
                                return e._onClick(t || (null == n ? void 0 : n.event))
                            }), !1, !0), se(u, "mousemove", (function(t) {
                                return e._onMouseMove(t || (null == n ? void 0 : n.event))
                            }), !1, !0), this._initialized = !0)
                        }
                    }, {
                        key: "_getProperties",
                        value: function(e, t) {
                            var i = this.instance.scrollManager.scrollY(),
                                r = this.instance.scrollManager.scrollX(),
                                s = this.instance.scrollManager.scrollElement(),
                                o = function(e, t, i) {
                                    for (var r = e; r && In(r) && !Fn(r, "body");) {
                                        if (r === i) return !1;
                                        if (K(t, null == n ? void 0 : n.getComputedStyle(r).position)) return !0;
                                        r = Cn(r)
                                    }
                                    return !1
                                }(xn(e), ["fixed", "sticky"], s);
                            return {
                                x: e.clientX + (o ? 0 : r),
                                y: e.clientY + (o ? 0 : i),
                                target_fixed: o,
                                type: t
                            }
                        }
                    }, {
                        key: "_onClick",
                        value: function(e) {
                            var t;
                            if (!Cr(e.target)) {
                                var n = this._getProperties(e, "click");
                                null !== (t = this.rageclicks) && void 0 !== t && t.isRageClick(e.clientX, e.clientY, (new Date).getTime()) && this._capture(L(L({}, n), {}, {
                                    type: "rageclick"
                                })), this._capture(n)
                            }
                        }
                    }, {
                        key: "_onMouseMove",
                        value: function(e) {
                            var t = this;
                            Cr(e.target) || (clearTimeout(this._mouseMoveTimeout), this._mouseMoveTimeout = setTimeout((function() {
                                t._capture(t._getProperties(e, "mousemove"))
                            }), 500))
                        }
                    }, {
                        key: "_capture",
                        value: function(e) {
                            if (n) {
                                var t = n.location.href;
                                this.buffer = this.buffer || {}, this.buffer[t] || (this.buffer[t] = []), this.buffer[t].push(e)
                            }
                        }
                    }, {
                        key: "flush",
                        value: function() {
                            this.buffer && !S(this.buffer) && this.instance.capture("$$heatmap", {
                                $heatmap_data: this.getAndClearBuffer()
                            })
                        }
                    }]), e
                }(),
                $r = function() {
                    function e(t) {
                        var n = this;
                        N(this, e), H(this, "_updateScrollData", (function() {
                            var e, t, i, r;
                            n.context || (n.context = {});
                            var s = n.scrollElement(),
                                o = n.scrollY(),
                                a = s ? Math.max(0, s.scrollHeight - s.clientHeight) : 0,
                                u = o + ((null == s ? void 0 : s.clientHeight) || 0),
                                l = (null == s ? void 0 : s.scrollHeight) || 0;
                            n.context.lastScrollY = Math.ceil(o), n.context.maxScrollY = Math.max(o, null !== (e = n.context.maxScrollY) && void 0 !== e ? e : 0), n.context.maxScrollHeight = Math.max(a, null !== (t = n.context.maxScrollHeight) && void 0 !== t ? t : 0), n.context.lastContentY = u, n.context.maxContentY = Math.max(u, null !== (i = n.context.maxContentY) && void 0 !== i ? i : 0), n.context.maxContentHeight = Math.max(l, null !== (r = n.context.maxContentHeight) && void 0 !== r ? r : 0)
                        })), this.instance = t
                    }
                    return B(e, [{
                        key: "getContext",
                        value: function() {
                            return this.context
                        }
                    }, {
                        key: "resetContext",
                        value: function() {
                            var e = this.context;
                            return setTimeout(this._updateScrollData, 0), e
                        }
                    }, {
                        key: "startMeasuringScrollPosition",
                        value: function() {
                            null == n || n.addEventListener("scroll", this._updateScrollData, !0), null == n || n.addEventListener("scrollend", this._updateScrollData, !0), null == n || n.addEventListener("resize", this._updateScrollData)
                        }
                    }, {
                        key: "scrollElement",
                        value: function() {
                            if (!this.instance.config.scroll_root_selector) return null == n ? void 0 : n.document.documentElement;
                            var e, t = V(b(this.instance.config.scroll_root_selector) ? this.instance.config.scroll_root_selector : [this.instance.config.scroll_root_selector]);
                            try {
                                for (t.s(); !(e = t.n()).done;) {
                                    var i = e.value,
                                        r = null == n ? void 0 : n.document.querySelector(i);
                                    if (r) return r
                                }
                            } catch (e) {
                                t.e(e)
                            } finally {
                                t.f()
                            }
                        }
                    }, {
                        key: "scrollY",
                        value: function() {
                            if (this.instance.config.scroll_root_selector) {
                                var e = this.scrollElement();
                                return e && e.scrollTop || 0
                            }
                            return n && (n.scrollY || n.pageYOffset || n.document.documentElement.scrollTop) || 0
                        }
                    }, {
                        key: "scrollX",
                        value: function() {
                            if (this.instance.config.scroll_root_selector) {
                                var e = this.scrollElement();
                                return e && e.scrollLeft || 0
                            }
                            return n && (n.scrollX || n.pageXOffset || n.document.documentElement.scrollLeft) || 0
                        }
                    }]), e
                }(),
                Or = "$copy_autocapture";

            function Ar(e, t) {
                return t.length > e ? t.slice(0, e) + "..." : t
            }
            var Lr, Dr = function() {
                    function e(t) {
                        N(this, e), H(this, "_initialized", !1), H(this, "_isDisabledServerSide", null), H(this, "rageclicks", new Tr), H(this, "_elementsChainAsString", !1), this.instance = t, this._elementSelectors = null
                    }
                    return B(e, [{
                        key: "config",
                        get: function() {
                            var e, t, n = w(this.instance.config.autocapture) ? this.instance.config.autocapture : {};
                            return n.url_allowlist = null === (e = n.url_allowlist) || void 0 === e ? void 0 : e.map((function(e) {
                                return new RegExp(e)
                            })), n.url_ignorelist = null === (t = n.url_ignorelist) || void 0 === t ? void 0 : t.map((function(e) {
                                return new RegExp(e)
                            })), n
                        }
                    }, {
                        key: "_addDomEventHandlers",
                        value: function() {
                            var e = this;
                            if (this.isBrowserSupported()) {
                                if (n && u) {
                                    var t = function(t) {
                                            t = t || (null == n ? void 0 : n.event);
                                            try {
                                                e._captureEvent(t)
                                            } catch (e) {
                                                $.error("Failed to capture event", e)
                                            }
                                        },
                                        i = function(t) {
                                            t = t || (null == n ? void 0 : n.event), e._captureEvent(t, Or)
                                        };
                                    se(u, "submit", t, !1, !0), se(u, "change", t, !1, !0), se(u, "click", t, !1, !0), this.config.capture_copied_text && (se(u, "copy", i, !1, !0), se(u, "cut", i, !1, !0))
                                }
                            } else $.info("Disabling Automatic Event Collection because this browser is not supported")
                        }
                    }, {
                        key: "startIfEnabled",
                        value: function() {
                            this.isEnabled && !this._initialized && (this._addDomEventHandlers(), this._initialized = !0)
                        }
                    }, {
                        key: "afterDecideResponse",
                        value: function(e) {
                            e.elementsChainAsString && (this._elementsChainAsString = e.elementsChainAsString), this.instance.persistence && this.instance.persistence.register(H({}, ce, !!e.autocapture_opt_out)), this._isDisabledServerSide = !!e.autocapture_opt_out, this.startIfEnabled()
                        }
                    }, {
                        key: "setElementSelectors",
                        value: function(e) {
                            this._elementSelectors = e
                        }
                    }, {
                        key: "getElementSelectors",
                        value: function(e) {
                            var t, n = [];
                            return null === (t = this._elementSelectors) || void 0 === t || t.forEach((function(t) {
                                var i = null == u ? void 0 : u.querySelectorAll(t);
                                null == i || i.forEach((function(i) {
                                    e === i && n.push(t)
                                }))
                            })), n
                        }
                    }, {
                        key: "isEnabled",
                        get: function() {
                            var e, t, n = null === (e = this.instance.persistence) || void 0 === e ? void 0 : e.props[ce],
                                i = this._isDisabledServerSide;
                            if (F(i) && !T(n) && !this.instance.config.advanced_disable_decide) return !1;
                            var r = null !== (t = this._isDisabledServerSide) && void 0 !== t ? t : !!n;
                            return !!this.instance.config.autocapture && !r
                        }
                    }, {
                        key: "_previousElementSibling",
                        value: function(e) {
                            if (e.previousElementSibling) return e.previousElementSibling;
                            var t = e;
                            do {
                                t = t.previousSibling
                            } while (t && !In(t));
                            return t
                        }
                    }, {
                        key: "_getAugmentPropertiesFromElement",
                        value: function(e) {
                            if (!$n(e)) return {};
                            var t = {};
                            return Y(e.attributes, (function(e) {
                                if (e.name && 0 === e.name.indexOf("data-ph-capture-attribute")) {
                                    var n = e.name.replace("data-ph-capture-attribute-", ""),
                                        i = e.value;
                                    n && i && Hn(i) && (t[n] = i)
                                }
                            })), t
                        }
                    }, {
                        key: "_getPropertiesFromElement",
                        value: function(e, t, n) {
                            var i, r = e.tagName.toLowerCase(),
                                s = {
                                    tag_name: r
                                };
                            Tn.indexOf(r) > -1 && !n && ("a" === r.toLowerCase() || "button" === r.toLowerCase() ? s.$el_text = Ar(1024, Un(e)) : s.$el_text = Ar(1024, En(e)));
                            var o = wn(e);
                            o.length > 0 && (s.classes = o.filter((function(e) {
                                return "" !== e
                            })));
                            var a = null === (i = this.config) || void 0 === i ? void 0 : i.element_attribute_ignorelist;
                            Y(e.attributes, (function(n) {
                                var i;
                                if ((!On(e) || -1 !== ["name", "id", "class", "aria-label"].indexOf(n.name)) && (null == a || !a.includes(n.name)) && !t && Hn(n.value) && (i = n.name, !x(i) || "_ngcontent" !== i.substring(0, 10) && "_nghost" !== i.substring(0, 7))) {
                                    var r = n.value;
                                    "class" === n.name && (r = bn(r).join(" ")), s["attr__" + n.name] = Ar(1024, r)
                                }
                            }));
                            for (var u = 1, l = 1, c = e; c = this._previousElementSibling(c);) u++, c.tagName === e.tagName && l++;
                            return s.nth_child = u, s.nth_of_type = l, s
                        }
                    }, {
                        key: "_getDefaultProperties",
                        value: function(e) {
                            return {
                                $event_type: e,
                                $ce_version: 1
                            }
                        }
                    }, {
                        key: "_captureEvent",
                        value: function(e) {
                            var t = this,
                                i = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "$autocapture";
                            if (this.isEnabled) {
                                var r, s = xn(e);
                                Pn(s) && (s = s.parentNode || null), "$autocapture" === i && "click" === e.type && e instanceof MouseEvent && this.instance.config.rageclick && null !== (r = this.rageclicks) && void 0 !== r && r.isRageClick(e.clientX, e.clientY, (new Date).getTime()) && this._captureEvent(e, "$rageclick");
                                var o = i === Or;
                                if (s && Mn(s, e, this.config, o, o ? ["copy", "cut"] : void 0)) {
                                    for (var a, u, l = [s], c = s; c.parentNode && !Fn(c, "body");) Rn(c.parentNode) ? (l.push(c.parentNode.host), c = c.parentNode.host) : (l.push(c.parentNode), c = c.parentNode);
                                    var d, h, f = [],
                                        v = {},
                                        p = !1;
                                    if (Y(l, (function(e) {
                                            var n = $n(e);
                                            "a" === e.tagName.toLowerCase() && (d = e.getAttribute("href"), d = n && Hn(d) && d), K(wn(e), "ph-no-capture") && (p = !0), f.push(t._getPropertiesFromElement(e, t.instance.config.mask_all_element_attributes, t.instance.config.mask_all_text));
                                            var i = t._getAugmentPropertiesFromElement(e);
                                            X(v, i)
                                        })), this.instance.config.mask_all_text || ("a" === s.tagName.toLowerCase() || "button" === s.tagName.toLowerCase() ? f[0].$el_text = Un(s) : f[0].$el_text = En(s)), d) {
                                        var g, _;
                                        f[0].attr__href = d;
                                        var m = null === (g = ht(d)) || void 0 === g ? void 0 : g.host,
                                            y = null == n || null === (_ = n.location) || void 0 === _ ? void 0 : _.host;
                                        m && y && m !== y && (h = d)
                                    }
                                    if (p) return !1;
                                    var b = X(this._getDefaultProperties(e.type), this._elementsChainAsString ? {
                                            $elements_chain: Wn(f)
                                        } : {
                                            $elements: f
                                        }, null !== (a = f[0]) && void 0 !== a && a.$el_text ? {
                                            $el_text: null === (u = f[0]) || void 0 === u ? void 0 : u.$el_text
                                        } : {}, h && "click" === e.type ? {
                                            $external_click_url: h
                                        } : {}, v),
                                        k = this.getElementSelectors(s);
                                    if (k && k.length > 0 && (b.$element_selectors = k), i === Or) {
                                        var w, S = Sn(null == n || null === (w = n.getSelection()) || void 0 === w ? void 0 : w.toString()),
                                            E = e.type || "clipboard";
                                        if (!S) return !1;
                                        b.$selected_content = S, b.$copy_type = E
                                    }
                                    return this.instance.capture(i, b), !0
                                }
                            }
                        }
                    }, {
                        key: "isBrowserSupported",
                        value: function() {
                            return k(null == u ? void 0 : u.querySelectorAll)
                        }
                    }]), e
                }(),
                Nr = function() {
                    function e(t) {
                        var n = this;
                        N(this, e), H(this, "_restoreXHRPatch", void 0), H(this, "_restoreFetchPatch", void 0), H(this, "_startCapturing", (function() {
                            var e, t, i, r;
                            E(n._restoreXHRPatch) && (null === (e = v.__PosthogExtensions__) || void 0 === e || null === (t = e.tracingHeadersPatchFns) || void 0 === t || t._patchXHR(n.instance.sessionManager)), E(n._restoreFetchPatch) && (null === (i = v.__PosthogExtensions__) || void 0 === i || null === (r = i.tracingHeadersPatchFns) || void 0 === r || r._patchFetch(n.instance.sessionManager))
                        })), this.instance = t
                    }
                    return B(e, [{
                        key: "_loadScript",
                        value: function(e) {
                            var t, n, i;
                            null !== (t = v.__PosthogExtensions__) && void 0 !== t && t.tracingHeadersPatchFns && e(), null === (n = v.__PosthogExtensions__) || void 0 === n || null === (i = n.loadExternalDependency) || void 0 === i || i.call(n, this.instance, "tracing-headers", (function(t) {
                                if (t) return $.error("[TRACING-HEADERS] failed to load script", t);
                                e()
                            }))
                        }
                    }, {
                        key: "startIfEnabledOrStop",
                        value: function() {
                            var e, t;
                            this.instance.config.__add_tracing_headers ? this._loadScript(this._startCapturing) : (null === (e = this._restoreXHRPatch) || void 0 === e || e.call(this), null === (t = this._restoreFetchPatch) || void 0 === t || t.call(this), this._restoreXHRPatch = void 0, this._restoreFetchPatch = void 0)
                        }
                    }]), e
                }();
            ! function(e) {
                e[e.PENDING = -1] = "PENDING", e[e.DENIED = 0] = "DENIED", e[e.GRANTED = 1] = "GRANTED"
            }(Lr || (Lr = {}));
            var qr = function() {
                    function e(t) {
                        N(this, e), this.instance = t
                    }
                    return B(e, [{
                        key: "config",
                        get: function() {
                            return this.instance.config
                        }
                    }, {
                        key: "consent",
                        get: function() {
                            return this.getDnt() ? Lr.DENIED : this.storedConsent
                        }
                    }, {
                        key: "isOptedOut",
                        value: function() {
                            return this.consent === Lr.DENIED || this.consent === Lr.PENDING && this.config.opt_out_capturing_by_default
                        }
                    }, {
                        key: "isOptedIn",
                        value: function() {
                            return !this.isOptedOut()
                        }
                    }, {
                        key: "optInOut",
                        value: function(e) {
                            this.storage.set(this.storageKey, e ? 1 : 0, this.config.cookie_expiration, this.config.cross_subdomain_cookie, this.config.secure_cookie)
                        }
                    }, {
                        key: "reset",
                        value: function() {
                            this.storage.remove(this.storageKey, this.config.cross_subdomain_cookie)
                        }
                    }, {
                        key: "storageKey",
                        get: function() {
                            var e = this.instance.config,
                                t = e.token;
                            return (e.opt_out_capturing_cookie_prefix || "__ph_opt_in_out_") + t
                        }
                    }, {
                        key: "storedConsent",
                        get: function() {
                            var e = this.storage.get(this.storageKey);
                            return "1" === e ? Lr.GRANTED : "0" === e ? Lr.DENIED : Lr.PENDING
                        }
                    }, {
                        key: "storage",
                        get: function() {
                            if (!this._storage) {
                                var e = this.config.opt_out_capturing_persistence_type;
                                this._storage = "localStorage" === e ? rt : nt;
                                var t = "localStorage" === e ? nt : rt;
                                t.get(this.storageKey) && (this._storage.get(this.storageKey) || this.optInOut("1" === t.get(this.storageKey)), t.remove(this.storageKey, this.config.cross_subdomain_cookie))
                            }
                            return this._storage
                        }
                    }, {
                        key: "getDnt",
                        value: function() {
                            return !!this.config.respect_dnt && !!oe([null == a ? void 0 : a.doNotTrack, null == a ? void 0 : a.msDoNotTrack, v.doNotTrack], (function(e) {
                                return K([!0, 1, "1", "yes"], e)
                            }))
                        }
                    }]), e
                }(),
                Br = "[Exception Autocapture]",
                Hr = function() {
                    function e(t) {
                        var i, r = this;
                        N(this, e), H(this, "originalOnUnhandledRejectionHandler", void 0), H(this, "startCapturing", (function() {
                            var e, t, i, s;
                            if (n && r.isEnabled && !r.hasHandlers && !r.isCapturing) {
                                var o = null === (e = v.__PosthogExtensions__) || void 0 === e || null === (t = e.errorWrappingFunctions) || void 0 === t ? void 0 : t.wrapOnError,
                                    a = null === (i = v.__PosthogExtensions__) || void 0 === i || null === (s = i.errorWrappingFunctions) || void 0 === s ? void 0 : s.wrapUnhandledRejection;
                                if (o && a) try {
                                    r.unwrapOnError = o(r.captureException.bind(r)), r.unwrapUnhandledRejection = a(r.captureException.bind(r))
                                } catch (e) {
                                    $.error(Br + " failed to start", e), r.stopCapturing()
                                } else $.error(Br + " failed to load error wrapping functions - cannot start")
                            }
                        })), this.instance = t, this.remoteEnabled = !(null === (i = this.instance.persistence) || void 0 === i || !i.props[he]), this.startIfEnabled()
                    }
                    return B(e, [{
                        key: "isEnabled",
                        get: function() {
                            var e;
                            return null !== (e = this.remoteEnabled) && void 0 !== e && e
                        }
                    }, {
                        key: "isCapturing",
                        get: function() {
                            var e;
                            return !(null == n || null === (e = n.onerror) || void 0 === e || !e.__POSTHOG_INSTRUMENTED__)
                        }
                    }, {
                        key: "hasHandlers",
                        get: function() {
                            return this.originalOnUnhandledRejectionHandler || this.unwrapOnError
                        }
                    }, {
                        key: "startIfEnabled",
                        value: function() {
                            this.isEnabled && !this.isCapturing && ($.info(Br + " enabled, starting..."), this.loadScript(this.startCapturing))
                        }
                    }, {
                        key: "loadScript",
                        value: function(e) {
                            var t, n;
                            this.hasHandlers && e(), null === (t = v.__PosthogExtensions__) || void 0 === t || null === (n = t.loadExternalDependency) || void 0 === n || n.call(t, this.instance, "exception-autocapture", (function(t) {
                                if (t) return $.error(Br + " failed to load script", t);
                                e()
                            }))
                        }
                    }, {
                        key: "stopCapturing",
                        value: function() {
                            var e, t;
                            null === (e = this.unwrapOnError) || void 0 === e || e.call(this), null === (t = this.unwrapUnhandledRejection) || void 0 === t || t.call(this)
                        }
                    }, {
                        key: "afterDecideResponse",
                        value: function(e) {
                            var t = e.autocaptureExceptions;
                            this.remoteEnabled = !!t || !1, this.instance.persistence && this.instance.persistence.register(H({}, he, this.remoteEnabled)), this.startIfEnabled()
                        }
                    }, {
                        key: "captureException",
                        value: function(e) {
                            var t = this.instance.requestRouter.endpointFor("ui");
                            e.$exception_personURL = "".concat(t, "/project/").concat(this.instance.config.token, "/person/").concat(this.instance.get_distinct_id()), this.instance.exceptions.sendExceptionEvent(e)
                        }
                    }]), e
                }(),
                Ur = 9e5,
                jr = "[Web Vitals]",
                Wr = function() {
                    function e(t) {
                        var n, i = this;
                        N(this, e), H(this, "_enabledServerSide", !1), H(this, "_initialized", !1), H(this, "buffer", {
                            url: void 0,
                            metrics: [],
                            firstMetricTimestamp: void 0
                        }), H(this, "_flushToCapture", (function() {
                            clearTimeout(i._delayedFlushTimer), 0 !== i.buffer.metrics.length && (i.instance.capture("$web_vitals", i.buffer.metrics.reduce((function(e, t) {
                                var n;
                                return L(L({}, e), {}, (H(n = {}, "$web_vitals_".concat(t.name, "_event"), L({}, t)), H(n, "$web_vitals_".concat(t.name, "_value"), t.value), n))
                            }), {})), i.buffer = {
                                url: void 0,
                                metrics: [],
                                firstMetricTimestamp: void 0
                            })
                        })), H(this, "_addToBuffer", (function(e) {
                            var t, n = null === (t = i.instance.sessionManager) || void 0 === t ? void 0 : t.checkAndGetSessionAndWindowId(!0);
                            if (E(n)) $.error(jr + "Could not read session ID. Dropping metrics!");
                            else {
                                i.buffer = i.buffer || {
                                    url: void 0,
                                    metrics: [],
                                    firstMetricTimestamp: void 0
                                };
                                var r = i._currentURL();
                                E(r) || (P(null == e ? void 0 : e.name) || P(null == e ? void 0 : e.value) ? $.error(jr + "Invalid metric received", e) : i._maxAllowedValue && e.value >= i._maxAllowedValue ? $.error(jr + "Ignoring metric with value >= " + i._maxAllowedValue, e) : (i.buffer.url !== r && (i._flushToCapture(), i._delayedFlushTimer = setTimeout(i._flushToCapture, 8e3)), E(i.buffer.url) && (i.buffer.url = r), i.buffer.firstMetricTimestamp = E(i.buffer.firstMetricTimestamp) ? Date.now() : i.buffer.firstMetricTimestamp, i.buffer.metrics.push(L(L({}, e), {}, {
                                    $current_url: r,
                                    $session_id: n.sessionId,
                                    $window_id: n.windowId,
                                    timestamp: Date.now()
                                })), i.buffer.metrics.length === i.allowedMetrics.length && i._flushToCapture()))
                            }
                        })), H(this, "_startCapturing", (function() {
                            var e, t, n, r, s = v.__PosthogExtensions__;
                            if (!E(s) && !E(s.postHogWebVitalsCallbacks)) {
                                var o = s.postHogWebVitalsCallbacks;
                                e = o.onLCP, t = o.onCLS, n = o.onFCP, r = o.onINP
                            }
                            e && t && n && r ? (i.allowedMetrics.indexOf("LCP") > -1 && e(i._addToBuffer.bind(i)), i.allowedMetrics.indexOf("CLS") > -1 && t(i._addToBuffer.bind(i)), i.allowedMetrics.indexOf("FCP") > -1 && n(i._addToBuffer.bind(i)), i.allowedMetrics.indexOf("INP") > -1 && r(i._addToBuffer.bind(i)), i._initialized = !0) : $.error(jr + "web vitals callbacks not loaded - not starting")
                        })), this.instance = t, this._enabledServerSide = !(null === (n = this.instance.persistence) || void 0 === n || !n.props[ve]), this.startIfEnabled()
                    }
                    return B(e, [{
                        key: "allowedMetrics",
                        get: function() {
                            var e, t, n = w(this.instance.config.capture_performance) ? null === (e = this.instance.config.capture_performance) || void 0 === e ? void 0 : e.web_vitals_allowed_metrics : void 0;
                            return E(n) ? (null === (t = this.instance.persistence) || void 0 === t ? void 0 : t.props[pe]) || ["CLS", "FCP", "INP", "LCP"] : n
                        }
                    }, {
                        key: "_maxAllowedValue",
                        get: function() {
                            var e = w(this.instance.config.capture_performance) && R(this.instance.config.capture_performance.__web_vitals_max_value) ? this.instance.config.capture_performance.__web_vitals_max_value : Ur;
                            return 0 < e && e <= 6e4 ? Ur : e
                        }
                    }, {
                        key: "isEnabled",
                        get: function() {
                            var e = w(this.instance.config.capture_performance) ? this.instance.config.capture_performance.web_vitals : void 0;
                            return T(e) ? e : this._enabledServerSide
                        }
                    }, {
                        key: "startIfEnabled",
                        value: function() {
                            this.isEnabled && !this._initialized && ($.info(jr + " enabled, starting..."), this.loadScript(this._startCapturing))
                        }
                    }, {
                        key: "afterDecideResponse",
                        value: function(e) {
                            var t = w(e.capturePerformance) && !!e.capturePerformance.web_vitals,
                                n = w(e.capturePerformance) ? e.capturePerformance.web_vitals_allowed_metrics : void 0;
                            this.instance.persistence && (this.instance.persistence.register(H({}, ve, t)), this.instance.persistence.register(H({}, pe, n))), this._enabledServerSide = t, this.startIfEnabled()
                        }
                    }, {
                        key: "loadScript",
                        value: function(e) {
                            var t, n, i;
                            null !== (t = v.__PosthogExtensions__) && void 0 !== t && t.postHogWebVitalsCallbacks && e(), null === (n = v.__PosthogExtensions__) || void 0 === n || null === (i = n.loadExternalDependency) || void 0 === i || i.call(n, this.instance, "web-vitals", (function(t) {
                                t ? $.error(jr + " failed to load script", t) : e()
                            }))
                        }
                    }, {
                        key: "_currentURL",
                        value: function() {
                            var e = n ? n.location.href : void 0;
                            return e || $.error(jr + "Could not determine current URL"), e
                        }
                    }]), e
                }(),
                zr = {
                    icontains: function(e, t) {
                        return !!n && t.href.toLowerCase().indexOf(e.toLowerCase()) > -1
                    },
                    not_icontains: function(e, t) {
                        return !!n && -1 === t.href.toLowerCase().indexOf(e.toLowerCase())
                    },
                    regex: function(e, t) {
                        return !!n && ft(t.href, e)
                    },
                    not_regex: function(e, t) {
                        return !!n && !ft(t.href, e)
                    },
                    exact: function(e, t) {
                        return t.href === e
                    },
                    is_not: function(e, t) {
                        return t.href !== e
                    }
                },
                Vr = function() {
                    function e(t) {
                        var n = this;
                        N(this, e), H(this, "getWebExperimentsAndEvaluateDisplayLogic", (function() {
                            var t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
                            n.getWebExperiments((function(t) {
                                e.logInfo("retrieved web experiments from the server"), n._flagToExperiments = new Map, t.forEach((function(t) {
                                    if (t.feature_flag_key && n._featureFlags && n._featureFlags[t.feature_flag_key]) {
                                        var i;
                                        n._flagToExperiments && (e.logInfo("setting flag key ", t.feature_flag_key, " to web experiment ", t), null === (i = n._flagToExperiments) || void 0 === i || i.set(t.feature_flag_key, t));
                                        var r = n._featureFlags[t.feature_flag_key];
                                        r && t.variants[r] && e.applyTransforms(t.name, r, t.variants[r].transforms)
                                    } else if (t.variants)
                                        for (var s in t.variants) {
                                            var o = t.variants[s];
                                            e.matchesTestVariant(o) && e.applyTransforms(t.name, s, o.transforms)
                                        }
                                }))
                            }), t)
                        })), this.instance = t, this.instance.onFeatureFlags && this.instance.onFeatureFlags((function(e) {
                            n.applyFeatureFlagChanges(e)
                        })), this._flagToExperiments = new Map
                    }
                    return B(e, [{
                        key: "applyFeatureFlagChanges",
                        value: function(t) {
                            var n = this;
                            e.logInfo("applying feature flags", t), P(this._flagToExperiments) || this.instance.config.disable_web_experiments || t.forEach((function(t) {
                                var i;
                                if (n._flagToExperiments && null !== (i = n._flagToExperiments) && void 0 !== i && i.has(t)) {
                                    var r, s = n.instance.getFeatureFlag(t),
                                        o = null === (r = n._flagToExperiments) || void 0 === r ? void 0 : r.get(t);
                                    s && null != o && o.variants[s] && e.applyTransforms(o.name, s, o.variants[s].transforms)
                                }
                            }))
                        }
                    }, {
                        key: "afterDecideResponse",
                        value: function(e) {
                            this._featureFlags = e.featureFlags, this.loadIfEnabled()
                        }
                    }, {
                        key: "loadIfEnabled",
                        value: function() {
                            this.instance.config.disable_web_experiments || this.getWebExperimentsAndEvaluateDisplayLogic()
                        }
                    }, {
                        key: "getWebExperiments",
                        value: function(e, t) {
                            if (this.instance.config.disable_web_experiments) return e([]);
                            var n = this.instance.get_property("$web_experiments");
                            if (n && !t) return e(n);
                            this.instance._send_request({
                                url: this.instance.requestRouter.endpointFor("api", "/api/web_experiments/?token=".concat(this.instance.config.token)),
                                method: "GET",
                                transport: "XHR",
                                callback: function(t) {
                                    if (200 !== t.statusCode || !t.json) return e([]);
                                    var n = t.json.experiments || [];
                                    return e(n)
                                }
                            })
                        }
                    }], [{
                        key: "matchesTestVariant",
                        value: function(t) {
                            return !P(t.conditions) && e.matchUrlConditions(t) && e.matchUTMConditions(t)
                        }
                    }, {
                        key: "matchUrlConditions",
                        value: function(t) {
                            var n;
                            if (P(t.conditions) || P(null === (n = t.conditions) || void 0 === n ? void 0 : n.url)) return !0;
                            var i, r, s, o = e.getWindowLocation();
                            return !!o && (null === (i = t.conditions) || void 0 === i || !i.url || zr[null !== (r = null === (s = t.conditions) || void 0 === s ? void 0 : s.urlMatchType) && void 0 !== r ? r : "icontains"](t.conditions.url, o))
                        }
                    }, {
                        key: "getWindowLocation",
                        value: function() {
                            return null == n ? void 0 : n.location
                        }
                    }, {
                        key: "matchUTMConditions",
                        value: function(e) {
                            var t;
                            if (P(e.conditions) || P(null === (t = e.conditions) || void 0 === t ? void 0 : t.utm)) return !0;
                            var n = fn.campaignParams();
                            if (n.utm_source) {
                                var i, r, s, o, a, u, l, c, d, h, f, v, p, g, _, m, y = null === (i = e.conditions) || void 0 === i || null === (r = i.utm) || void 0 === r || !r.utm_campaign || (null === (s = e.conditions) || void 0 === s || null === (o = s.utm) || void 0 === o ? void 0 : o.utm_campaign) == n.utm_campaign,
                                    b = null === (a = e.conditions) || void 0 === a || null === (u = a.utm) || void 0 === u || !u.utm_source || (null === (l = e.conditions) || void 0 === l || null === (c = l.utm) || void 0 === c ? void 0 : c.utm_source) == n.utm_source,
                                    k = null === (d = e.conditions) || void 0 === d || null === (h = d.utm) || void 0 === h || !h.utm_medium || (null === (f = e.conditions) || void 0 === f || null === (v = f.utm) || void 0 === v ? void 0 : v.utm_medium) == n.utm_medium,
                                    w = null === (p = e.conditions) || void 0 === p || null === (g = p.utm) || void 0 === g || !g.utm_term || (null === (_ = e.conditions) || void 0 === _ || null === (m = _.utm) || void 0 === m ? void 0 : m.utm_term) == n.utm_term;
                                return y && k && w && b
                            }
                            return !1
                        }
                    }, {
                        key: "logInfo",
                        value: function(e) {
                            for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), i = 1; i < t; i++) n[i - 1] = arguments[i];
                            $.info("[WebExperiments] ".concat(e), n)
                        }
                    }, {
                        key: "applyTransforms",
                        value: function(t, n, i) {
                            i.forEach((function(i) {
                                if (i.selector) {
                                    var r;
                                    e.logInfo("applying transform of variant ".concat(n, " for experiment ").concat(t, " "), i);
                                    var s = null === (r = document) || void 0 === r ? void 0 : r.querySelectorAll(i.selector);
                                    null == s || s.forEach((function(e) {
                                        var t = e;
                                        i.attributes && i.attributes.forEach((function(e) {
                                            switch (e.name) {
                                                case "text":
                                                    t.innerText = e.value;
                                                    break;
                                                case "html":
                                                    t.innerHTML = e.value;
                                                    break;
                                                case "cssClass":
                                                    t.className = e.value;
                                                    break;
                                                default:
                                                    t.setAttribute(e.name, e.value)
                                            }
                                        })), i.text && (t.innerText = i.text), i.html && (t.innerHTML = i.html), i.className && (t.className = i.className)
                                    }))
                                }
                            }))
                        }
                    }]), e
                }(),
                Gr = function() {
                    function e(t) {
                        var n;
                        N(this, e), this.instance = t, this._endpointSuffix = (null === (n = this.instance.persistence) || void 0 === n ? void 0 : n.props[fe]) || "/e/"
                    }
                    return B(e, [{
                        key: "endpoint",
                        get: function() {
                            return this.instance.requestRouter.endpointFor("api", this._endpointSuffix)
                        }
                    }, {
                        key: "afterDecideResponse",
                        value: function(e) {
                            var t = e.autocaptureExceptions;
                            this._endpointSuffix = w(t) && t.endpoint || "/e/", this.instance.persistence && this.instance.persistence.register(H({}, fe, this._endpointSuffix))
                        }
                    }, {
                        key: "sendExceptionEvent",
                        value: function(e) {
                            this.instance.capture("$exception", e, {
                                _noTruncate: !0,
                                _batchKey: "exceptionEvent",
                                _url: this.endpoint
                            })
                        }
                    }]), e
                }(),
                Qr = {},
                Jr = function() {},
                Yr = "posthog",
                Xr = !Zi && -1 === (null == f ? void 0 : f.indexOf("MSIE")) && -1 === (null == f ? void 0 : f.indexOf("Mozilla")),
                Kr = function() {
                    var e, t, i;
                    return {
                        api_host: "https://us.i.posthog.com",
                        ui_host: null,
                        token: "",
                        autocapture: !0,
                        rageclick: !0,
                        cross_subdomain_cookie: (t = null == u ? void 0 : u.location, i = null == t ? void 0 : t.hostname, !!x(i) && "herokuapp.com" !== i.split(".").slice(-2).join(".")),
                        persistence: "localStorage+cookie",
                        persistence_name: "",
                        loaded: Jr,
                        store_google: !0,
                        custom_campaign_params: [],
                        custom_blocked_useragents: [],
                        save_referrer: !0,
                        capture_pageview: !0,
                        capture_pageleave: "if_capture_pageview",
                        debug: l && x(null == l ? void 0 : l.search) && -1 !== l.search.indexOf("__posthog_debug=true") || !1,
                        verbose: !1,
                        cookie_expiration: 365,
                        upgrade: !1,
                        disable_session_recording: !1,
                        disable_persistence: !1,
                        disable_web_experiments: !0,
                        disable_surveys: !1,
                        enable_recording_console_log: void 0,
                        secure_cookie: "https:" === (null == n || null === (e = n.location) || void 0 === e ? void 0 : e.protocol),
                        ip: !0,
                        opt_out_capturing_by_default: !1,
                        opt_out_persistence_by_default: !1,
                        opt_out_useragent_filter: !1,
                        opt_out_capturing_persistence_type: "localStorage",
                        opt_out_capturing_cookie_prefix: null,
                        opt_in_site_apps: !1,
                        property_denylist: [],
                        respect_dnt: !1,
                        sanitize_properties: null,
                        request_headers: {},
                        inapp_protocol: "//",
                        inapp_link_new_window: !1,
                        request_batching: !0,
                        properties_string_max_length: 65535,
                        session_recording: {},
                        mask_all_element_attributes: !1,
                        mask_all_text: !1,
                        advanced_disable_decide: !1,
                        advanced_disable_feature_flags: !1,
                        advanced_disable_feature_flags_on_first_load: !1,
                        advanced_disable_toolbar_metrics: !1,
                        feature_flag_request_timeout_ms: 3e3,
                        on_request_error: function(e) {
                            var t = "Bad HTTP status: " + e.statusCode + " " + e.text;
                            $.error(t)
                        },
                        get_device_id: function(e) {
                            return e
                        },
                        _onCapture: Jr,
                        capture_performance: void 0,
                        name: "posthog",
                        bootstrap: {},
                        disable_compression: !1,
                        session_idle_timeout_seconds: 1800,
                        person_profiles: "always",
                        __add_tracing_headers: !1
                    }
                },
                Zr = function(e) {
                    var t = {};
                    E(e.process_person) || (t.person_profiles = e.process_person), E(e.xhr_headers) || (t.request_headers = e.xhr_headers), E(e.cookie_name) || (t.persistence_name = e.cookie_name), E(e.disable_cookie) || (t.disable_persistence = e.disable_cookie);
                    var n = X({}, t, e);
                    return b(e.property_blacklist) && (E(e.property_denylist) ? n.property_denylist = e.property_blacklist : b(e.property_denylist) ? n.property_denylist = [].concat(j(e.property_blacklist), j(e.property_denylist)) : $.error("Invalid value for property_denylist config: " + e.property_denylist)), n
                },
                es = function() {
                    function e() {
                        N(this, e), H(this, "__forceAllowLocalhost", !1)
                    }
                    return B(e, [{
                        key: "_forceAllowLocalhost",
                        get: function() {
                            return this.__forceAllowLocalhost
                        },
                        set: function(e) {
                            $.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"), this.__forceAllowLocalhost = e
                        }
                    }]), e
                }(),
                ts = function() {
                    function e() {
                        var t = this;
                        N(this, e), H(this, "webPerformance", new es), H(this, "version", p.LIB_VERSION), H(this, "_internalEventEmitter", new yr), this.config = Kr(), this.decideEndpointWasHit = !1, this.SentryIntegration = gr, this.sentryIntegration = function(e) {
                            return function(e, t) {
                                var n = hr(e, t);
                                return {
                                    name: dr,
                                    processEvent: function(e) {
                                        return n(e)
                                    }
                                }
                            }(t, e)
                        }, this.__request_queue = [], this.__loaded = !1, this.analyticsDefaultEndpoint = "/e/", this._initialPageviewCaptured = !1, this.featureFlags = new We(this), this.toolbar = new Xi(this), this.scrollManager = new $r(this), this.pageViewManager = new _r(this), this.surveys = new Er(this), this.experiments = new Vr(this), this.exceptions = new Gr(this), this.rateLimiter = new xr(this), this.requestRouter = new cr(this), this.consent = new qr(this), this.people = {
                            set: function(e, n, i) {
                                var r = x(e) ? H({}, e, n) : e;
                                t.setPersonProperties(r), null == i || i({})
                            },
                            set_once: function(e, n, i) {
                                var r = x(e) ? H({}, e, n) : e;
                                t.setPersonProperties(void 0, r), null == i || i({})
                            }
                        }, this.on("eventCaptured", (function(e) {
                            return $.info("send", e)
                        }))
                    }
                    return B(e, [{
                        key: "init",
                        value: function(t, n, i) {
                            if (i && i !== Yr) {
                                var r, s = null !== (r = Qr[i]) && void 0 !== r ? r : new e;
                                return s._init(t, n, i), Qr[i] = s, Qr[Yr][i] = s, s
                            }
                            return this._init(t, n, i)
                        }
                    }, {
                        key: "_init",
                        value: function(e) {
                            var i, r, s = this,
                                o = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                                a = arguments.length > 2 ? arguments[2] : void 0;
                            if (E(e) || I(e)) return $.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"), this;
                            if (this.__loaded) return $.warn("You have already initialized PostHog! Re-initializing is a no-op"), this;
                            this.__loaded = !0, this.config = {}, this._triggered_notifs = [], this.set_config(X({}, Kr(), Zr(o), {
                                name: a,
                                token: e
                            })), this.config.on_xhr_error && $.error("[posthog] on_xhr_error is deprecated. Use on_request_error instead"), this.compression = o.disable_compression ? void 0 : t.Compression.GZipJS, this.persistence = new pn(this.config), this.sessionPersistence = "sessionStorage" === this.config.persistence ? this.persistence : new pn(L(L({}, this.config), {}, {
                                persistence: "sessionStorage"
                            }));
                            var u = L({}, this.persistence.props),
                                l = L({}, this.sessionPersistence.props);
                            if (this._requestQueue = new Ki((function(e) {
                                    return s._send_retriable_request(e)
                                })), this._retryQueue = new or(this), this.__request_queue = [], this.sessionManager = new ur(this.config, this.persistence), this.sessionPropsManager = new Fr(this.sessionManager, this.persistence), new Nr(this).startIfEnabledOrStop(), this.sessionRecording = new Gi(this), this.sessionRecording.startIfEnabledOrStop(), this.config.disable_scroll_properties || this.scrollManager.startMeasuringScrollPosition(), this.autocapture = new Dr(this), this.autocapture.startIfEnabled(), this.surveys.loadIfEnabled(), this.heatmaps = new Mr(this), this.heatmaps.startIfEnabled(), this.webVitalsAutocapture = new Wr(this), this.exceptionObserver = new Hr(this), this.exceptionObserver.startIfEnabled(), p.DEBUG = p.DEBUG || this.config.debug, p.DEBUG && $.info("Starting in debug mode", {
                                    this: this,
                                    config: o,
                                    thisC: L({}, this.config),
                                    p: u,
                                    s: l
                                }), this._sync_opt_out_with_persistence(), void 0 !== (null === (i = o.bootstrap) || void 0 === i ? void 0 : i.distinctID)) {
                                var c, d, h = this.config.get_device_id(Ke()),
                                    f = null !== (c = o.bootstrap) && void 0 !== c && c.isIdentifiedID ? h : o.bootstrap.distinctID;
                                this.persistence.set_property(Ce, null !== (d = o.bootstrap) && void 0 !== d && d.isIdentifiedID ? "identified" : "anonymous"), this.register({
                                    distinct_id: o.bootstrap.distinctID,
                                    $device_id: f
                                })
                            }
                            if (this._hasBootstrappedFeatureFlags()) {
                                var v, g, _ = Object.keys((null === (v = o.bootstrap) || void 0 === v ? void 0 : v.featureFlags) || {}).filter((function(e) {
                                        var t, n;
                                        return !(null === (t = o.bootstrap) || void 0 === t || null === (n = t.featureFlags) || void 0 === n || !n[e])
                                    })).reduce((function(e, t) {
                                        var n, i;
                                        return e[t] = (null === (n = o.bootstrap) || void 0 === n || null === (i = n.featureFlags) || void 0 === i ? void 0 : i[t]) || !1, e
                                    }), {}),
                                    m = Object.keys((null === (g = o.bootstrap) || void 0 === g ? void 0 : g.featureFlagPayloads) || {}).filter((function(e) {
                                        return _[e]
                                    })).reduce((function(e, t) {
                                        var n, i, r, s;
                                        return null !== (n = o.bootstrap) && void 0 !== n && null !== (i = n.featureFlagPayloads) && void 0 !== i && i[t] && (e[t] = null === (r = o.bootstrap) || void 0 === r || null === (s = r.featureFlagPayloads) || void 0 === s ? void 0 : s[t]), e
                                    }), {});
                                this.featureFlags.receivedFeatureFlags({
                                    featureFlags: _,
                                    featureFlagPayloads: m
                                })
                            }
                            if (!this.get_distinct_id()) {
                                var y = this.config.get_device_id(Ke());
                                this.register_once({
                                    distinct_id: y,
                                    $device_id: y
                                }, ""), this.persistence.set_property(Ce, "anonymous")
                            }
                            return null == n || null === (r = n.addEventListener) || void 0 === r || r.call(n, "onpagehide" in self ? "pagehide" : "unload", this._handle_unload.bind(this)), this.toolbar.maybeLoadToolbar(), o.segment ? function(e, t) {
                                var n = e.config.segment;
                                if (!n) return t();
                                ! function(e, t) {
                                    var n = e.config.segment;
                                    if (!n) return t();
                                    var i = function(n) {
                                            var i = function() {
                                                return n.anonymousId() || Ke()
                                            };
                                            e.config.get_device_id = i, n.id() && (e.register({
                                                distinct_id: n.id(),
                                                $device_id: i()
                                            }), e.persistence.set_property(Ce, "identified")), t()
                                        },
                                        r = n.user();
                                    "then" in r && k(r.then) ? r.then((function(e) {
                                        return i(e)
                                    })) : i(r)
                                }(e, (function() {
                                    n.register(function(e) {
                                        Promise && Promise.resolve || $.warn("This browser does not have Promise support, and can not use the segment integration");
                                        var t = function(t, n) {
                                            var i;
                                            if (!n) return t;
                                            t.event.userId || t.event.anonymousId === e.get_distinct_id() || ($.info("Segment integration does not have a userId set, resetting PostHog"), e.reset()), t.event.userId && t.event.userId !== e.get_distinct_id() && ($.info("Segment integration has a userId set, identifying with PostHog"), e.identify(t.event.userId));
                                            var r = e._calculate_event_properties(n, null !== (i = t.event.properties) && void 0 !== i ? i : {}, new Date);
                                            return t.event.properties = Object.assign({}, r, t.event.properties), t
                                        };
                                        return {
                                            name: "PostHog JS",
                                            type: "enrichment",
                                            version: "1.0.0",
                                            isLoaded: function() {
                                                return !0
                                            },
                                            load: function() {
                                                return Promise.resolve()
                                            },
                                            track: function(e) {
                                                return t(e, e.event.event)
                                            },
                                            page: function(e) {
                                                return t(e, "$pageview")
                                            },
                                            identify: function(e) {
                                                return t(e, "$identify")
                                            },
                                            screen: function(e) {
                                                return t(e, "$screen")
                                            }
                                        }
                                    }(e)).then((function() {
                                        t()
                                    }))
                                }))
                            }(this, (function() {
                                return s._loaded()
                            })) : this._loaded(), k(this.config._onCapture) && this.on("eventCaptured", (function(e) {
                                return s.config._onCapture(e.event, e)
                            })), this
                        }
                    }, {
                        key: "_afterDecideResponse",
                        value: function(e) {
                            var n, i, r, s, o, a, u, l, c;
                            this.compression = void 0, e.supportedCompression && !this.config.disable_compression && (this.compression = K(e.supportedCompression, t.Compression.GZipJS) ? t.Compression.GZipJS : K(e.supportedCompression, t.Compression.Base64) ? t.Compression.Base64 : void 0), null !== (n = e.analytics) && void 0 !== n && n.endpoint && (this.analyticsDefaultEndpoint = e.analytics.endpoint), null === (i = this.sessionRecording) || void 0 === i || i.afterDecideResponse(e), null === (r = this.autocapture) || void 0 === r || r.afterDecideResponse(e), null === (s = this.heatmaps) || void 0 === s || s.afterDecideResponse(e), null === (o = this.experiments) || void 0 === o || o.afterDecideResponse(e), null === (a = this.surveys) || void 0 === a || a.afterDecideResponse(e), null === (u = this.webVitalsAutocapture) || void 0 === u || u.afterDecideResponse(e), null === (l = this.exceptions) || void 0 === l || l.afterDecideResponse(e), null === (c = this.exceptionObserver) || void 0 === c || c.afterDecideResponse(e)
                        }
                    }, {
                        key: "_loaded",
                        value: function() {
                            var e = this,
                                t = this.config.advanced_disable_decide;
                            t || this.featureFlags.setReloadingPaused(!0);
                            try {
                                this.config.loaded(this)
                            } catch (e) {
                                $.critical("`loaded` function failed", e)
                            }
                            this._start_queue_if_opted_in(), this.config.capture_pageview && setTimeout((function() {
                                e.consent.isOptedIn() && e._captureInitialPageview()
                            }), 1), t || (new Qi(this).call(), this.featureFlags.resetRequestQueue())
                        }
                    }, {
                        key: "_start_queue_if_opted_in",
                        value: function() {
                            var e;
                            this.has_opted_out_capturing() || this.config.request_batching && (null === (e = this._requestQueue) || void 0 === e || e.enable())
                        }
                    }, {
                        key: "_dom_loaded",
                        value: function() {
                            var e = this;
                            this.has_opted_out_capturing() || J(this.__request_queue, (function(t) {
                                return e._send_retriable_request(t)
                            })), this.__request_queue = [], this._start_queue_if_opted_in()
                        }
                    }, {
                        key: "_handle_unload",
                        value: function() {
                            var e, t;
                            this.config.request_batching ? (this._shouldCapturePageleave() && this.capture("$pageleave"), null === (e = this._requestQueue) || void 0 === e || e.unload(), null === (t = this._retryQueue) || void 0 === t || t.unload()) : this._shouldCapturePageleave() && this.capture("$pageleave", null, {
                                transport: "sendBeacon"
                            })
                        }
                    }, {
                        key: "_send_request",
                        value: function(e) {
                            var t = this;
                            this.__loaded && (Xr ? this.__request_queue.push(e) : this.rateLimiter.isServerRateLimited(e.batchKey) || (e.transport = e.transport || this.config.api_transport, e.url = tr(e.url, {
                                ip: this.config.ip ? 1 : 0
                            }), e.headers = L({}, this.config.request_headers), e.compression = "best-available" === e.compression ? this.compression : e.compression, function(e) {
                                var t, n, i, r = L({}, e);
                                r.timeout = r.timeout || 6e4, r.url = tr(r.url, {
                                    _: (new Date).getTime().toString(),
                                    ver: p.LIB_VERSION,
                                    compression: r.compression
                                });
                                var s = null !== (t = r.transport) && void 0 !== t ? t : "XHR",
                                    o = null !== (n = null === (i = oe(ir, (function(e) {
                                        return e.transport === s
                                    }))) || void 0 === i ? void 0 : i.method) && void 0 !== n ? n : ir[0].method;
                                if (!o) throw new Error("No available transport method");
                                o(r)
                            }(L(L({}, e), {}, {
                                callback: function(n) {
                                    var i, r, s;
                                    t.rateLimiter.checkForLimiting(n), n.statusCode >= 400 && (null === (r = (s = t.config).on_request_error) || void 0 === r || r.call(s, n)), null === (i = e.callback) || void 0 === i || i.call(e, n)
                                }
                            }))))
                        }
                    }, {
                        key: "_send_retriable_request",
                        value: function(e) {
                            this._retryQueue ? this._retryQueue.retriableRequest(e) : this._send_request(e)
                        }
                    }, {
                        key: "_execute_array",
                        value: function(e) {
                            var t, n = this,
                                i = [],
                                r = [],
                                s = [];
                            J(e, (function(e) {
                                e && (t = e[0], b(t) ? s.push(e) : k(e) ? e.call(n) : b(e) && "alias" === t ? i.push(e) : b(e) && -1 !== t.indexOf("capture") && k(n[t]) ? s.push(e) : r.push(e))
                            }));
                            var o = function(e, t) {
                                J(e, (function(e) {
                                    if (b(e[0])) {
                                        var n = t;
                                        Y(e, (function(e) {
                                            n = n[e[0]].apply(n, e.slice(1))
                                        }))
                                    } else this[e[0]].apply(this, e.slice(1))
                                }), t)
                            };
                            o(i, this), o(r, this), o(s, this)
                        }
                    }, {
                        key: "_hasBootstrappedFeatureFlags",
                        value: function() {
                            var e, t;
                            return (null === (e = this.config.bootstrap) || void 0 === e ? void 0 : e.featureFlags) && Object.keys(null === (t = this.config.bootstrap) || void 0 === t ? void 0 : t.featureFlags).length > 0 || !1
                        }
                    }, {
                        key: "push",
                        value: function(e) {
                            this._execute_array([e])
                        }
                    }, {
                        key: "capture",
                        value: function(e, t, n) {
                            var i;
                            if (this.__loaded && this.persistence && this.sessionPersistence && this._requestQueue) {
                                if (!this.consent.isOptedOut())
                                    if (!E(e) && x(e)) {
                                        if (this.config.opt_out_useragent_filter || !this._is_bot()) {
                                            var r = null != n && n.skip_client_rate_limiting ? void 0 : this.rateLimiter.clientRateLimitContext();
                                            if (null == r || !r.isRateLimited) {
                                                this.sessionPersistence.update_search_keyword(), this.config.store_google && this.sessionPersistence.update_campaign_params(), this.config.save_referrer && this.sessionPersistence.update_referrer_info(), (this.config.store_google || this.config.save_referrer) && this.persistence.set_initial_person_info();
                                                var s = new Date,
                                                    o = (null == n ? void 0 : n.timestamp) || s,
                                                    a = {
                                                        uuid: Ke(),
                                                        event: e,
                                                        properties: this._calculate_event_properties(e, t || {}, o)
                                                    };
                                                r && (a.properties.$lib_rate_limit_remaining_tokens = r.remainingTokens), (null == n ? void 0 : n.$set) && (a.$set = null == n ? void 0 : n.$set);
                                                var u = this._calculate_set_once_properties(null == n ? void 0 : n.$set_once);
                                                u && (a.$set_once = u), (a = function(e, t) {
                                                    return n = e, i = function(e) {
                                                            return x(e) && !F(t) ? e.slice(0, t) : e
                                                        }, r = new Set,
                                                        function e(t, n) {
                                                            return t !== Object(t) ? i ? i(t) : t : r.has(t) ? void 0 : (r.add(t), b(t) ? (s = [], J(t, (function(t) {
                                                                s.push(e(t))
                                                            }))) : (s = {}, Y(t, (function(t, n) {
                                                                r.has(t) || (s[n] = e(t))
                                                            }))), s);
                                                            var s
                                                        }(n);
                                                    var n, i, r
                                                }(a, null != n && n._noTruncate ? null : this.config.properties_string_max_length)).timestamp = o, E(null == n ? void 0 : n.timestamp) || (a.properties.$event_time_override_provided = !0, a.properties.$event_time_override_system_time = s);
                                                var l = L(L({}, a.properties.$set), a.$set);
                                                S(l) || this.setPersonPropertiesForFlags(l), this._internalEventEmitter.emit("eventCaptured", a);
                                                var c = {
                                                    method: "POST",
                                                    url: null !== (i = null == n ? void 0 : n._url) && void 0 !== i ? i : this.requestRouter.endpointFor("api", this.analyticsDefaultEndpoint),
                                                    data: a,
                                                    compression: "best-available",
                                                    batchKey: null == n ? void 0 : n._batchKey
                                                };
                                                return !this.config.request_batching || n && (null == n || !n._batchKey) || null != n && n.send_instantly ? this._send_retriable_request(c) : this._requestQueue.enqueue(c), a
                                            }
                                            $.critical("This capture call is ignored due to client rate limiting.")
                                        }
                                    } else $.error("No event name provided to posthog.capture")
                            } else $.uninitializedWarning("posthog.capture")
                        }
                    }, {
                        key: "_addCaptureHook",
                        value: function(e) {
                            return this.on("eventCaptured", (function(t) {
                                return e(t.event, t)
                            }))
                        }
                    }, {
                        key: "_calculate_event_properties",
                        value: function(e, t, n) {
                            if (n = n || new Date, !this.persistence || !this.sessionPersistence) return t;
                            var i = this.persistence.remove_event_timer(e),
                                r = L({}, t);
                            if (r.token = this.config.token, "$snapshot" === e) {
                                var s = L(L({}, this.persistence.properties()), this.sessionPersistence.properties());
                                return r.distinct_id = s.distinct_id, (!x(r.distinct_id) && !R(r.distinct_id) || I(r.distinct_id)) && $.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"), r
                            }
                            var o = fn.properties();
                            if (this.sessionManager) {
                                var a = this.sessionManager.checkAndGetSessionAndWindowId(),
                                    l = a.sessionId,
                                    c = a.windowId;
                                r.$session_id = l, r.$window_id = c
                            }
                            if (this.requestRouter.region === rr.CUSTOM && (r.$lib_custom_api_host = this.config.api_host), this.sessionPropsManager && this.config.__preview_send_client_session_params && ("$pageview" === e || "$pageleave" === e || "$autocapture" === e)) {
                                var d = this.sessionPropsManager.getSessionProps();
                                r = X(r, d)
                            }
                            if (!this.config.disable_scroll_properties) {
                                var h = {};
                                "$pageview" === e ? h = this.pageViewManager.doPageView(n) : "$pageleave" === e && (h = this.pageViewManager.doPageLeave(n)), r = X(r, h)
                            }
                            if ("$pageview" === e && u && (r.title = u.title), !E(i)) {
                                var v = n.getTime() - i;
                                r.$duration = parseFloat((v / 1e3).toFixed(3))
                            }
                            f && this.config.opt_out_useragent_filter && (r.$browser_type = this._is_bot() ? "bot" : "browser"), (r = X({}, o, this.persistence.properties(), this.sessionPersistence.properties(), r)).$is_identified = this._isIdentified(), b(this.config.property_denylist) ? Y(this.config.property_denylist, (function(e) {
                                delete r[e]
                            })) : $.error("Invalid value for property_denylist config: " + this.config.property_denylist + " or property_blacklist config: " + this.config.property_blacklist);
                            var p = this.config.sanitize_properties;
                            return p && (r = p(r, e)), r.$process_person_profile = this._hasPersonProcessing(), r
                        }
                    }, {
                        key: "_calculate_set_once_properties",
                        value: function(e) {
                            if (!this.persistence || !this._hasPersonProcessing()) return e;
                            var t = X({}, this.persistence.get_initial_props(), e || {});
                            return S(t) ? void 0 : t
                        }
                    }, {
                        key: "register",
                        value: function(e, t) {
                            var n;
                            null === (n = this.persistence) || void 0 === n || n.register(e, t)
                        }
                    }, {
                        key: "register_once",
                        value: function(e, t, n) {
                            var i;
                            null === (i = this.persistence) || void 0 === i || i.register_once(e, t, n)
                        }
                    }, {
                        key: "register_for_session",
                        value: function(e) {
                            var t;
                            null === (t = this.sessionPersistence) || void 0 === t || t.register(e)
                        }
                    }, {
                        key: "unregister",
                        value: function(e) {
                            var t;
                            null === (t = this.persistence) || void 0 === t || t.unregister(e)
                        }
                    }, {
                        key: "unregister_for_session",
                        value: function(e) {
                            var t;
                            null === (t = this.sessionPersistence) || void 0 === t || t.unregister(e)
                        }
                    }, {
                        key: "_register_single",
                        value: function(e, t) {
                            this.register(H({}, e, t))
                        }
                    }, {
                        key: "getFeatureFlag",
                        value: function(e, t) {
                            return this.featureFlags.getFeatureFlag(e, t)
                        }
                    }, {
                        key: "getFeatureFlagPayload",
                        value: function(e) {
                            var t = this.featureFlags.getFeatureFlagPayload(e);
                            try {
                                return JSON.parse(t)
                            } catch (e) {
                                return t
                            }
                        }
                    }, {
                        key: "isFeatureEnabled",
                        value: function(e, t) {
                            return this.featureFlags.isFeatureEnabled(e, t)
                        }
                    }, {
                        key: "reloadFeatureFlags",
                        value: function() {
                            this.featureFlags.reloadFeatureFlags()
                        }
                    }, {
                        key: "updateEarlyAccessFeatureEnrollment",
                        value: function(e, t) {
                            this.featureFlags.updateEarlyAccessFeatureEnrollment(e, t)
                        }
                    }, {
                        key: "getEarlyAccessFeatures",
                        value: function(e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                            return this.featureFlags.getEarlyAccessFeatures(e, t)
                        }
                    }, {
                        key: "on",
                        value: function(e, t) {
                            return this._internalEventEmitter.on(e, t)
                        }
                    }, {
                        key: "onFeatureFlags",
                        value: function(e) {
                            return this.featureFlags.onFeatureFlags(e)
                        }
                    }, {
                        key: "onSessionId",
                        value: function(e) {
                            var t, n;
                            return null !== (t = null === (n = this.sessionManager) || void 0 === n ? void 0 : n.onSessionId(e)) && void 0 !== t ? t : function() {}
                        }
                    }, {
                        key: "getSurveys",
                        value: function(e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                            this.surveys.getSurveys(e, t)
                        }
                    }, {
                        key: "getActiveMatchingSurveys",
                        value: function(e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                            this.surveys.getActiveMatchingSurveys(e, t)
                        }
                    }, {
                        key: "renderSurvey",
                        value: function(e, t) {
                            this.surveys.renderSurvey(e, t)
                        }
                    }, {
                        key: "canRenderSurvey",
                        value: function(e) {
                            this.surveys.canRenderSurvey(e)
                        }
                    }, {
                        key: "getNextSurveyStep",
                        value: function(e, t, n) {
                            return this.surveys.getNextSurveyStep(e, t, n)
                        }
                    }, {
                        key: "identify",
                        value: function(e, t, n) {
                            if (!this.__loaded || !this.persistence) return $.uninitializedWarning("posthog.identify");
                            if (R(e) && (e = e.toString(), $.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")), e) {
                                if (["distinct_id", "distinctid"].includes(e.toLowerCase())) $.critical('The string "'.concat(e, '" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.'));
                                else if (this._requirePersonProcessing("posthog.identify")) {
                                    var i = this.get_distinct_id();
                                    if (this.register({
                                            $user_id: e
                                        }), !this.get_property("$device_id")) {
                                        var r = i;
                                        this.register_once({
                                            $had_persisted_distinct_id: !0,
                                            $device_id: r
                                        }, "")
                                    }
                                    e !== i && e !== this.get_property(ue) && (this.unregister(ue), this.register({
                                        distinct_id: e
                                    }));
                                    var s = "anonymous" === (this.persistence.get_property(Ce) || "anonymous");
                                    e !== i && s ? (this.persistence.set_property(Ce, "identified"), this.setPersonPropertiesForFlags(t || {}, !1), this.capture("$identify", {
                                        distinct_id: e,
                                        $anon_distinct_id: i
                                    }, {
                                        $set: t || {},
                                        $set_once: n || {}
                                    }), this.featureFlags.setAnonymousDistinctId(i)) : (t || n) && this.setPersonProperties(t, n), e !== i && (this.reloadFeatureFlags(), this.unregister(Te))
                                }
                            } else $.error("Unique user id has not been set in posthog.identify")
                        }
                    }, {
                        key: "setPersonProperties",
                        value: function(e, t) {
                            (e || t) && this._requirePersonProcessing("posthog.setPersonProperties") && (this.setPersonPropertiesForFlags(e || {}), this.capture("$set", {
                                $set: e || {},
                                $set_once: t || {}
                            }))
                        }
                    }, {
                        key: "group",
                        value: function(e, t, n) {
                            if (e && t) {
                                if (this._requirePersonProcessing("posthog.group")) {
                                    var i = this.getGroups();
                                    i[e] !== t && this.resetGroupPropertiesForFlags(e), this.register({
                                        $groups: L(L({}, i), {}, H({}, e, t))
                                    }), n && (this.capture("$groupidentify", {
                                        $group_type: e,
                                        $group_key: t,
                                        $group_set: n
                                    }), this.setGroupPropertiesForFlags(H({}, e, n))), i[e] === t || n || this.reloadFeatureFlags()
                                }
                            } else $.error("posthog.group requires a group type and group key")
                        }
                    }, {
                        key: "resetGroups",
                        value: function() {
                            this.register({
                                $groups: {}
                            }), this.resetGroupPropertiesForFlags(), this.reloadFeatureFlags()
                        }
                    }, {
                        key: "setPersonPropertiesForFlags",
                        value: function(e) {
                            var t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
                            this._requirePersonProcessing("posthog.setPersonPropertiesForFlags") && this.featureFlags.setPersonPropertiesForFlags(e, t)
                        }
                    }, {
                        key: "resetPersonPropertiesForFlags",
                        value: function() {
                            this.featureFlags.resetPersonPropertiesForFlags()
                        }
                    }, {
                        key: "setGroupPropertiesForFlags",
                        value: function(e) {
                            var t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
                            this._requirePersonProcessing("posthog.setGroupPropertiesForFlags") && this.featureFlags.setGroupPropertiesForFlags(e, t)
                        }
                    }, {
                        key: "resetGroupPropertiesForFlags",
                        value: function(e) {
                            this.featureFlags.resetGroupPropertiesForFlags(e)
                        }
                    }, {
                        key: "reset",
                        value: function(e) {
                            var t, n, i, r;
                            if ($.info("reset"), !this.__loaded) return $.uninitializedWarning("posthog.reset");
                            var s = this.get_property("$device_id");
                            this.consent.reset(), null === (t = this.persistence) || void 0 === t || t.clear(), null === (n = this.sessionPersistence) || void 0 === n || n.clear(), null === (i = this.persistence) || void 0 === i || i.set_property(Ce, "anonymous"), null === (r = this.sessionManager) || void 0 === r || r.resetSessionId();
                            var o = this.config.get_device_id(Ke());
                            this.register_once({
                                distinct_id: o,
                                $device_id: e ? o : s
                            }, "")
                        }
                    }, {
                        key: "get_distinct_id",
                        value: function() {
                            return this.get_property("distinct_id")
                        }
                    }, {
                        key: "getGroups",
                        value: function() {
                            return this.get_property("$groups") || {}
                        }
                    }, {
                        key: "get_session_id",
                        value: function() {
                            var e, t;
                            return null !== (e = null === (t = this.sessionManager) || void 0 === t ? void 0 : t.checkAndGetSessionAndWindowId(!0).sessionId) && void 0 !== e ? e : ""
                        }
                    }, {
                        key: "get_session_replay_url",
                        value: function(e) {
                            if (!this.sessionManager) return "";
                            var t = this.sessionManager.checkAndGetSessionAndWindowId(!0),
                                n = t.sessionId,
                                i = t.sessionStartTimestamp,
                                r = this.requestRouter.endpointFor("ui", "/project/".concat(this.config.token, "/replay/").concat(n));
                            if (null != e && e.withTimestamp && i) {
                                var s, o = null !== (s = e.timestampLookBack) && void 0 !== s ? s : 10;
                                if (!i) return r;
                                var a = Math.max(Math.floor(((new Date).getTime() - i) / 1e3) - o, 0);
                                r += "?t=".concat(a)
                            }
                            return r
                        }
                    }, {
                        key: "alias",
                        value: function(e, t) {
                            return e === this.get_property(ae) ? ($.critical("Attempting to create alias for existing People user - aborting."), -2) : this._requirePersonProcessing("posthog.alias") ? (E(t) && (t = this.get_distinct_id()), e !== t ? (this._register_single(ue, e), this.capture("$create_alias", {
                                alias: e,
                                distinct_id: t
                            })) : ($.warn("alias matches current distinct_id - skipping api call."), this.identify(e), -1)) : void 0
                        }
                    }, {
                        key: "set_config",
                        value: function(e) {
                            var t, n, i, r, s = L({}, this.config);
                            w(e) && (X(this.config, Zr(e)), null === (t = this.persistence) || void 0 === t || t.update_config(this.config, s), this.sessionPersistence = "sessionStorage" === this.config.persistence ? this.persistence : new pn(L(L({}, this.config), {}, {
                                persistence: "sessionStorage"
                            })), rt.is_supported() && "true" === rt.get("ph_debug") && (this.config.debug = !0), this.config.debug && (p.DEBUG = !0, $.info("set_config", {
                                config: e,
                                oldConfig: s,
                                newConfig: L({}, this.config)
                            })), null === (n = this.sessionRecording) || void 0 === n || n.startIfEnabledOrStop(), null === (i = this.autocapture) || void 0 === i || i.startIfEnabled(), null === (r = this.heatmaps) || void 0 === r || r.startIfEnabled(), this.surveys.loadIfEnabled(), this._sync_opt_out_with_persistence())
                        }
                    }, {
                        key: "startSessionRecording",
                        value: function(e) {
                            var t, n = T(e) && e;
                            if (n || null != e && e.sampling) {
                                var i, r, s = null === (i = this.sessionManager) || void 0 === i ? void 0 : i.checkAndGetSessionAndWindowId();
                                null === (r = this.persistence) || void 0 === r || r.register(H({}, Se, !0)), $.info("Session recording started with sampling override for session: ", null == s ? void 0 : s.sessionId)
                            }(n || null != e && e.linked_flag) && (null === (t = this.sessionRecording) || void 0 === t || t.overrideLinkedFlag(), $.info("Session recording started with linked_flags override")), this.set_config({
                                disable_session_recording: !1
                            })
                        }
                    }, {
                        key: "stopSessionRecording",
                        value: function() {
                            this.set_config({
                                disable_session_recording: !0
                            })
                        }
                    }, {
                        key: "sessionRecordingStarted",
                        value: function() {
                            var e;
                            return !(null === (e = this.sessionRecording) || void 0 === e || !e.started)
                        }
                    }, {
                        key: "captureException",
                        value: function(e, t) {
                            var n, i = k(null === (n = v.__PosthogExtensions__) || void 0 === n ? void 0 : n.parseErrorAsProperties) ? v.__PosthogExtensions__.parseErrorAsProperties([e.message, void 0, void 0, void 0, e]) : L({
                                $exception_type: e.name,
                                $exception_message: e.message,
                                $exception_level: "error"
                            }, t);
                            this.exceptions.sendExceptionEvent(i)
                        }
                    }, {
                        key: "loadToolbar",
                        value: function(e) {
                            return this.toolbar.loadToolbar(e)
                        }
                    }, {
                        key: "get_property",
                        value: function(e) {
                            var t;
                            return null === (t = this.persistence) || void 0 === t ? void 0 : t.props[e]
                        }
                    }, {
                        key: "getSessionProperty",
                        value: function(e) {
                            var t;
                            return null === (t = this.sessionPersistence) || void 0 === t ? void 0 : t.props[e]
                        }
                    }, {
                        key: "toString",
                        value: function() {
                            var e, t = null !== (e = this.config.name) && void 0 !== e ? e : Yr;
                            return t !== Yr && (t = Yr + "." + t), t
                        }
                    }, {
                        key: "_isIdentified",
                        value: function() {
                            var e, t;
                            return "identified" === (null === (e = this.persistence) || void 0 === e ? void 0 : e.get_property(Ce)) || "identified" === (null === (t = this.sessionPersistence) || void 0 === t ? void 0 : t.get_property(Ce))
                        }
                    }, {
                        key: "_hasPersonProcessing",
                        value: function() {
                            var e, t, n, i;
                            return !("never" === this.config.person_profiles || "identified_only" === this.config.person_profiles && !this._isIdentified() && S(this.getGroups()) && (null === (e = this.persistence) || void 0 === e || null === (t = e.props) || void 0 === t || !t[ue]) && (null === (n = this.persistence) || void 0 === n || null === (i = n.props) || void 0 === i || !i[De]))
                        }
                    }, {
                        key: "_shouldCapturePageleave",
                        value: function() {
                            return !0 === this.config.capture_pageleave || "if_capture_pageview" === this.config.capture_pageleave && this.config.capture_pageview
                        }
                    }, {
                        key: "createPersonProfile",
                        value: function() {
                            this._hasPersonProcessing() || this._requirePersonProcessing("posthog.createPersonProfile") && this.setPersonProperties({}, {})
                        }
                    }, {
                        key: "_requirePersonProcessing",
                        value: function(e) {
                            return "never" === this.config.person_profiles ? ($.error(e + ' was called, but process_person is set to "never". This call will be ignored.'), !1) : (this._register_single(De, !0), !0)
                        }
                    }, {
                        key: "_sync_opt_out_with_persistence",
                        value: function() {
                            var e, t, n, i, r = this.consent.isOptedOut(),
                                s = this.config.opt_out_persistence_by_default,
                                o = this.config.disable_persistence || r && !!s;
                            (null === (e = this.persistence) || void 0 === e ? void 0 : e.disabled) !== o && (null === (n = this.persistence) || void 0 === n || n.set_disabled(o)), (null === (t = this.sessionPersistence) || void 0 === t ? void 0 : t.disabled) !== o && (null === (i = this.sessionPersistence) || void 0 === i || i.set_disabled(o))
                        }
                    }, {
                        key: "opt_in_capturing",
                        value: function(e) {
                            var t;
                            this.consent.optInOut(!0), this._sync_opt_out_with_persistence(), (E(null == e ? void 0 : e.captureEventName) || null != e && e.captureEventName) && this.capture(null !== (t = null == e ? void 0 : e.captureEventName) && void 0 !== t ? t : "$opt_in", null == e ? void 0 : e.captureProperties, {
                                send_instantly: !0
                            }), this.config.capture_pageview && this._captureInitialPageview()
                        }
                    }, {
                        key: "opt_out_capturing",
                        value: function() {
                            this.consent.optInOut(!1), this._sync_opt_out_with_persistence()
                        }
                    }, {
                        key: "has_opted_in_capturing",
                        value: function() {
                            return this.consent.isOptedIn()
                        }
                    }, {
                        key: "has_opted_out_capturing",
                        value: function() {
                            return this.consent.isOptedOut()
                        }
                    }, {
                        key: "clear_opt_in_out_capturing",
                        value: function() {
                            this.consent.reset(), this._sync_opt_out_with_persistence()
                        }
                    }, {
                        key: "_is_bot",
                        value: function() {
                            return a ? function(e, t) {
                                if (!e) return !1;
                                var n = e.userAgent;
                                if (n && Rr(n, t)) return !0;
                                try {
                                    var i = null == e ? void 0 : e.userAgentData;
                                    if (null != i && i.brands && i.brands.some((function(e) {
                                            return Rr(null == e ? void 0 : e.brand, t)
                                        }))) return !0
                                } catch (e) {}
                                return !!e.webdriver
                            }(a, this.config.custom_blocked_useragents) : void 0
                        }
                    }, {
                        key: "_captureInitialPageview",
                        value: function() {
                            u && !this._initialPageviewCaptured && (this._initialPageviewCaptured = !0, this.capture("$pageview", {
                                title: u.title
                            }, {
                                send_instantly: !0
                            }))
                        }
                    }, {
                        key: "debug",
                        value: function(e) {
                            !1 === e ? (null == n || n.console.log("You've disabled debug mode."), localStorage && localStorage.removeItem("ph_debug"), this.set_config({
                                debug: !1
                            })) : (null == n || n.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."), localStorage && localStorage.setItem("ph_debug", "true"), this.set_config({
                                debug: !0
                            }))
                        }
                    }]), e
                }();
            ! function(e, t) {
                for (var n = 0; n < t.length; n++) e.prototype[t[n]] = ne(e.prototype[t[n]])
            }(ts, ["identify"]);
            var ns, is = (ns = Qr[Yr] = new ts, function() {
                function e() {
                    e.done || (e.done = !0, Xr = !1, Y(Qr, (function(e) {
                        e._dom_loaded()
                    })))
                }
                null != u && u.addEventListener && ("complete" === u.readyState ? e() : u.addEventListener("DOMContentLoaded", e, !1)), n && se(n, "load", e, !0)
            }(), ns);
            t.PostHog = ts, t.default = is, t.posthog = is, t.severityLevels = ["fatal", "error", "warning", "log", "info", "debug"]
        }
    }
]);
//# sourceMappingURL=3040.bbfecbee01fa6cbe.js.map