(self["webpackChunkstores_admin"] = self["webpackChunkstores_admin"] || []).push([
    ["node_modules_cuid_index_js"], {

        /***/
        "../../node_modules/cuid/index.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                /**
                 * cuid.js
                 * Collision-resistant UID generator for browsers and node.
                 * Sequential for fast db lookups and recency sorting.
                 * Safe for element IDs and server-side lookups.
                 *
                 * Extracted from CLCTR
                 *
                 * Copyright (c) <PERSON> 2012
                 * MIT License
                 */

                var fingerprint = __webpack_require__("../../node_modules/cuid/lib/fingerprint.browser.js");
                var pad = __webpack_require__("../../node_modules/cuid/lib/pad.js");
                var getRandomValue = __webpack_require__("../../node_modules/cuid/lib/getRandomValue.browser.js");

                var c = 0,
                    blockSize = 4,
                    base = 36,
                    discreteValues = Math.pow(base, blockSize);

                function randomBlock() {
                    return pad((getRandomValue() *
                            discreteValues << 0)
                        .toString(base), blockSize);
                }

                function safeCounter() {
                    c = c < discreteValues ? c : 0;
                    c++; // this is not subliminal
                    return c - 1;
                }

                function cuid() {
                    // Starting with a lowercase letter makes
                    // it HTML element ID friendly.
                    var letter = 'c', // hard-coded allows for sequential access

                        // timestamp
                        // warning: this exposes the exact date and time
                        // that the uid was created.
                        timestamp = (new Date().getTime()).toString(base),

                        // Prevent same-machine collisions.
                        counter = pad(safeCounter().toString(base), blockSize),

                        // A few chars to generate distinct ids for different
                        // clients (so different computers are far less
                        // likely to generate the same id)
                        print = fingerprint(),

                        // Grab some more chars from Math.random()
                        random = randomBlock() + randomBlock();

                    return letter + timestamp + counter + print + random;
                }

                cuid.slug = function slug() {
                    var date = new Date().getTime().toString(36),
                        counter = safeCounter().toString(36).slice(-4),
                        print = fingerprint().slice(0, 1) +
                        fingerprint().slice(-1),
                        random = randomBlock().slice(-2);

                    return date.slice(-2) +
                        counter + print + random;
                };

                cuid.isCuid = function isCuid(stringToCheck) {
                    if (typeof stringToCheck !== 'string') return false;
                    if (stringToCheck.startsWith('c')) return true;
                    return false;
                };

                cuid.isSlug = function isSlug(stringToCheck) {
                    if (typeof stringToCheck !== 'string') return false;
                    var stringLength = stringToCheck.length;
                    if (stringLength >= 7 && stringLength <= 10) return true;
                    return false;
                };

                cuid.fingerprint = fingerprint;

                module.exports = cuid;


                /***/
            }),

        /***/
        "../../node_modules/cuid/lib/fingerprint.browser.js":
            /***/
            ((module, __unused_webpack_exports, __webpack_require__) => {

                var pad = __webpack_require__("../../node_modules/cuid/lib/pad.js");

                var env = typeof window === 'object' ? window : self;
                var globalCount = Object.keys(env).length;
                var mimeTypesLength = navigator.mimeTypes ? navigator.mimeTypes.length : 0;
                var clientId = pad((mimeTypesLength +
                        navigator.userAgent.length).toString(36) +
                    globalCount.toString(36), 4);

                module.exports = function fingerprint() {
                    return clientId;
                };


                /***/
            }),

        /***/
        "../../node_modules/cuid/lib/getRandomValue.browser.js":
            /***/
            ((module) => {


                var getRandomValue;

                var crypto = typeof window !== 'undefined' &&
                    (window.crypto || window.msCrypto) ||
                    typeof self !== 'undefined' &&
                    self.crypto;

                if (crypto) {
                    var lim = Math.pow(2, 32) - 1;
                    getRandomValue = function() {
                        return Math.abs(crypto.getRandomValues(new Uint32Array(1))[0] / lim);
                    };
                } else {
                    getRandomValue = Math.random;
                }

                module.exports = getRandomValue;


                /***/
            }),

        /***/
        "../../node_modules/cuid/lib/pad.js":
            /***/
            ((module) => {

                module.exports = function pad(num, size) {
                    var s = '000000000' + num;
                    return s.substr(s.length - size);
                };


                /***/
            })

    }
])
//# sourceMappingURL=node_modules_cuid_index_js.bf777ec67bee9d0a.js.map