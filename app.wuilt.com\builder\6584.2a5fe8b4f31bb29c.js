(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [6584], {
        66088: (e, n, t) => {
            t.d(n, {
                x: () => l
            });
            let l = function(e) {
                return e.NOT_FOUND_PAGE = "NOT_FOUND_PAGE", e.UNDER_MAINTENANCE_PAGE = "UNDER_MAINTENANCE_PAGE", e
            }({})
        },
        84755: (e, n, t) => {
            t.d(n, {
                Q: () => o,
                _: () => l
            });
            let l = function(e) {
                    return e.small = "10px 16px", e.medium = "12px 20px", e.large = "16px 28px", e
                }({}),
                o = function(e) {
                    return e.Primary = "primary", e.Secondary = "secondary", e.Tertiary = "tertiary", e
                }({})
        },
        25842: (e, n, t) => {
            t.d(n, {
                SV: () => o,
                mV: () => i,
                rZ: () => a,
                ur: () => l
            });
            let l = function(e) {
                    return e.brand1 = "--theme-color-brand1", e.brand2 = "--theme-color-brand2", e.brand3 = "--theme-color-brand3", e.brand4 = "--theme-color-brand4", e.secondary1 = "--theme-color-secondary1", e.secondary2 = "--theme-color-secondary2", e.secondary3 = "--theme-color-secondary3", e.secondary4 = "--theme-color-secondary4", e.base1 = "--theme-color-base1", e.base2 = "--theme-color-base2", e.base3 = "--theme-color-base3", e.base4 = "--theme-color-base4", e.base5 = "--theme-color-base5", e.accent1 = "--theme-color-accent1", e.accent2 = "--theme-color-accent2", e.transparent = "--theme-transparent-color", e
                }({}),
                o = function(e) {
                    return e.brand1 = "brand1", e.brand2 = "brand2", e.brand3 = "brand3", e.brand4 = "brand4", e.secondary1 = "secondary1", e.secondary2 = "secondary2", e.secondary3 = "secondary3", e.secondary4 = "secondary4", e.base1 = "base1", e.base2 = "base2", e.base3 = "base3", e.base4 = "base4", e.base5 = "base5", e.accent1 = "accent1", e.accent2 = "accent2", e
                }({}),
                a = function(e) {
                    return e.primaryBg = "primaryBg", e.secondaryBg = "secondaryBg", e.heading1 = "heading1", e.heading2 = "heading2", e.heading3 = "heading3", e.heading4 = "heading4", e.heading5 = "heading5", e.heading6 = "heading6", e.paragraph1 = "paragraph1", e.paragraph2 = "paragraph2", e.paragraph3 = "paragraph3", e.primaryButtonFill = "primaryButtonFill", e.primaryButtonText = "primaryButtonText", e.primaryButtonBorder = "primaryButtonBorder", e.primaryButtonHoverFill = "primaryButtonHoverFill", e.primaryButtonHoverText = "primaryButtonHoverText", e.primaryButtonHoverBorder = "primaryButtonHoverBorder", e.secondaryButtonFill = "secondaryButtonFill", e.secondaryButtonText = "secondaryButtonText", e.secondaryButtonBorder = "secondaryButtonBorder", e.secondaryButtonHoverFill = "secondaryButtonHoverFill", e.secondaryButtonHoverText = "secondaryButtonHoverText", e.secondaryButtonHoverBorder = "secondaryButtonHoverBorder", e.tertiaryButtonFill = "tertiaryButtonFill", e.tertiaryButtonText = "tertiaryButtonText", e.tertiaryButtonBorder = "tertiaryButtonBorder", e.tertiaryButtonHoverFill = "tertiaryButtonHoverFill", e.tertiaryButtonHoverText = "tertiaryButtonHoverText", e.tertiaryButtonHoverBorder = "tertiaryButtonHoverBorder", e.formFieldFill = "formFieldFill", e.formFieldText = "formFieldText", e.formFieldBorder = "formFieldBorder", e.formFieldLabel = "formFieldLabel", e
                }({}),
                i = function(e) {
                    return e.brandColor = "brandColor", e.adjustedBrandColor1 = "adjustedBrandColor1", e.adjustedBrandColor2 = "adjustedBrandColor2", e.adjustedBrandColor3 = "adjustedBrandColor3", e.secondaryColor = "secondaryColor", e.adjustedSecondaryColor1 = "adjustedSecondaryColor1", e.adjustedSecondaryColor2 = "adjustedSecondaryColor2", e.adjustedSecondaryColor3 = "adjustedSecondaryColor3", e.baseColor1 = "baseColor1", e.baseColor2 = "baseColor2", e.adjustedBaseColor1 = "adjustedBaseColor1", e.adjustedBaseColor2 = "adjustedBaseColor2", e.adjustedBaseColor3 = "adjustedBaseColor3", e.accentColor1 = "accentColor1", e.accentColor2 = "accentColor2", e
                }({})
        },
        94678: (e, n, t) => {
            t.d(n, {
                G9: () => r,
                Gx: () => d,
                HA: () => p,
                KQ: () => u,
                S4: () => c,
                U_: () => h,
                jX: () => s,
                rq: () => g
            });
            var l = t(2547),
                o = t(92560),
                a = t(26897),
                i = t(74499);
            let r = function(e) {
                    return e.Sharp = "sharp", e.Rounded = "rounded", e.Pill = "pill", e
                }({}),
                s = function(e) {
                    return e.Small = "small", e.Medium = "medium", e.Large = "large", e
                }({}),
                d = function(e) {
                    return e.Solid = "solid", e.Dashed = "dashed", e.None = "none", e
                }({}),
                u = function(e) {
                    return e.small = "6px 10px", e.medium = "8px 12px", e.large = "10px 14px", e
                }({}),
                c = function(e) {
                    return e.sharp = "0px", e.rounded = "8px", e.pill = "999px", e
                }({});
            const p = [{
                    content: (0, i.tZ)(a.FormattedMessage, {
                        defaultMessage: "Sharp",
                        id: "5UpJs7"
                    }),
                    icon: (0, i.tZ)(o.SharpIcon, {
                        size: "100%"
                    }),
                    id: r.Sharp
                }, {
                    content: (0, i.tZ)(a.FormattedMessage, {
                        defaultMessage: "Rounded",
                        id: "4cZKxy"
                    }),
                    icon: (0, i.tZ)(o.RoundedIcon, {
                        size: "100%"
                    }),
                    id: r.Rounded
                }, {
                    content: (0, i.tZ)(a.FormattedMessage, {
                        defaultMessage: "Pill",
                        id: "t+NyF2"
                    }),
                    icon: (0, i.tZ)(o.PillIcon, {
                        size: "100%"
                    }),
                    id: r.Pill
                }],
                g = [{
                    content: (0, i.tZ)(a.FormattedMessage, {
                        defaultMessage: "Large",
                        id: "/06iwc"
                    }),
                    component: (0, i.tZ)(l.Stack, {
                        justifyContent: "center",
                        alignItems: "center",
                        border: "1px solid",
                        borderColor: "gray.200",
                        borderRadius: "4px",
                        padding: "12px 20px",
                        boxShadow: "xs",
                        children: (0, i.tZ)(l.Box, {
                            width: "34px",
                            height: "5.6px",
                            borderRadius: "21px",
                            background: "gray.200"
                        })
                    }),
                    id: s.Large
                }, {
                    content: (0, i.tZ)(a.FormattedMessage, {
                        defaultMessage: "Medium",
                        id: "ovJ26C"
                    }),
                    component: (0, i.tZ)(l.Stack, {
                        justifyContent: "center",
                        alignItems: "center",
                        border: "1px solid",
                        borderColor: "gray.200",
                        borderRadius: "4px",
                        padding: "10px 16px",
                        boxShadow: "xs",
                        children: (0, i.tZ)(l.Box, {
                            width: "34px",
                            height: "5.6px",
                            borderRadius: "21px",
                            background: "gray.200"
                        })
                    }),
                    id: s.Medium
                }, {
                    content: (0, i.tZ)(a.FormattedMessage, {
                        defaultMessage: "Small",
                        id: "BPnT3T"
                    }),
                    component: (0, i.tZ)(l.Stack, {
                        justifyContent: "center",
                        alignItems: "center",
                        border: "1px solid",
                        borderColor: "gray.200",
                        borderRadius: "4px",
                        padding: "8px 12px",
                        boxShadow: "xs",
                        children: (0, i.tZ)(l.Box, {
                            width: "34px",
                            height: "5.6px",
                            borderRadius: "21px",
                            background: "gray.200"
                        })
                    }),
                    id: s.Small
                }],
                h = [{
                    content: (0, i.tZ)(o.StopIcon, {}),
                    id: d.None
                }, {
                    content: (0, i.tZ)(o.SquareIcon, {}),
                    id: d.Solid
                }, {
                    content: (0, i.tZ)(o.DashedSquareIcon, {}),
                    id: d.Dashed
                }]
        },
        16584: (e, n, t) => {
            t.r(n), t.d(n, {
                default: () => Un
            });
            var l = t(44486),
                o = t(22595),
                a = t(37900),
                i = t.n(a),
                r = t(96149),
                s = t.n(r),
                d = t(77686),
                u = t(19672);
            const c = (0, u.createSelector)((e => e.language), (e => ({
                language: e.language,
                locale: e.locale
            })));
            var p = t(60709),
                g = t(63231),
                h = t.n(g),
                y = t(48258),
                m = t(68797),
                v = t(35444);
            const b = (e, n) => e[n] || e.default || "no default";

            function f(e, n, t, l, o) {
                if (n.type === v.SectionValueTypes.GROUP) {
                    if (!t[e]) {
                        const e = n,
                            t = e.values && Object.keys(e.values).reduce(((n, t) => Object.assign({}, n, {
                                [t]: {
                                    id: 0,
                                    value: e.values[t].defaultValue
                                }
                            })), {});
                        return [Object.assign({}, t), Object.assign({}, t), Object.assign({}, t)]
                    }
                    return t[e]
                }
                const a = "UNIVERSAL" === n.source ? {
                    value: l[e] || b(n.defaultValue, o)
                } : t[e] || b(n.defaultValue, o);
                return n.noProcessing ? a.value : {
                    id: a.id ? a.id : "no-id",
                    value: a.value || a
                }
            }
            const x = function(e, n, t, l, o) {
                return "edit" === e ? function(e, n, t, l) {
                    return e ? Object.keys(e).reduce(((o, a) => Object.assign({}, o, {
                        [a]: f(a, e[a], n, t, l)
                    })), {}) : {}
                }(n, t, l, o) : function(e, n, t) {
                    return e ? Object.keys(e).reduce(((l, o) => Object.assign({}, l, {
                        [o]: "UNIVERSAL" === e[o].source ? t[o] : n[o] || null
                    })), {}) : {}
                }(n, t, l)
            };
            var S = t(18321),
                Z = t(26897),
                _ = t(35335);
            const C = "ExpandButton_sectionControlButton__w+W9P",
                B = "ExpandButton_--expanded__x5sJi";
            var w = t(74499);
            class $ extends a.PureComponent {
                constructor(...e) {
                    super(...e), this.handleClick = e => {
                        const {
                            onClick: n
                        } = this.props;
                        n && (e.stopPropagation(), e.preventDefault(), n(e))
                    }
                }
                render() {
                    const {
                        style: e,
                        className: n,
                        width: t,
                        expanded: l,
                        expandTo: o,
                        textColor: a,
                        iconTo: i,
                        backgroundColor: r,
                        icon: s,
                        text: d,
                        uiDirection: u,
                        uiLocale: c
                    } = this.props, p = "rtl" === u, g = `ui${c.toUpperCase()}`, y = `${o}${i}${t}`, m = parseInt(t, 10) ? `${parseInt(t,10)+13}px` : "125px";
                    return (0, w.tZ)("div", {
                        style: e,
                        className: h()("ExpandButton_sectionControlButtonContainer__gdVPM", n),
                        children: (0, w.BX)("button", {
                            type: "button",
                            className: h()(C, y, {
                                [B]: l,
                                "ExpandButton_--fromCenter__XD0Jn": "center" === o,
                                "ExpandButton_--expandToRight__kKh8S": "right" === o,
                                "ExpandButton_--expandToLeft__s61-L": "right" !== o,
                                "ExpandButton_--iconToRight__yqbby": "left" !== i,
                                "ExpandButton_--iconToLeft__sk0fZ": "left" === i
                            }),
                            onClick: this.handleClick,
                            style: Object.assign({
                                backgroundColor: r || "#fff"
                            }, p ? "right" === o ? {
                                right: 0
                            } : {
                                left: 0
                            } : "right" === o ? {
                                left: 0
                            } : {
                                right: 0
                            }),
                            children: [(0, w.tZ)("style", {
                                children: `\n                @media screen and (min-width: 600px) {\n                  .${C}.${y}.${B},\n                  .${C}.${y}:hover,\n                  .${C}.${y}:focus {\n                    width: ${t||"115px"};\n                  }\n\n\n                  .${C}.${y}.${B}:hover,\n                  .${C}.${y}.${B}:focus {\n                    width: ${m} !important;\n                  }\n                }\n              `
                            }), (0, w.tZ)("span", {
                                className: g,
                                style: {
                                    color: a
                                },
                                children: d
                            }), (0, w.tZ)("div", {
                                className: "ExpandButton_controlButton__icon__wiEFm",
                                style: Object.assign({
                                    backgroundColor: r || "#fff",
                                    color: a
                                }, p ? "left" === i ? {
                                    right: 0
                                } : {
                                    left: 0
                                } : "left" === i ? {
                                    left: 0
                                } : {
                                    right: 0
                                }),
                                children: s
                            })]
                        })
                    })
                }
            }
            const M = $,
                k = (0, Z.defineMessages)({
                    nextDesign: {
                        id: "PKlXmd",
                        defaultMessage: "Next Section Design"
                    },
                    prevDesign: {
                        id: "x8WWAl",
                        defaultMessage: "Previous Section Design"
                    },
                    moveUp: {
                        id: "5p5CJU",
                        defaultMessage: "Move Up"
                    },
                    moveDown: {
                        id: "ndFwsD",
                        defaultMessage: "Move Down"
                    },
                    addSection: {
                        id: "ckO4/Z",
                        defaultMessage: "Add Section"
                    },
                    sectionInfo: {
                        id: "tLYngi",
                        defaultMessage: "Section Info"
                    },
                    sectionSettings: {
                        id: "+VzpWv",
                        defaultMessage: "Section settings"
                    },
                    moreActions: {
                        id: "S8/4ZI",
                        defaultMessage: "More actions"
                    },
                    deleteSection: {
                        id: "gNtgVM",
                        defaultMessage: "Remove Section"
                    },
                    copySection: {
                        id: "Tao2c6",
                        defaultMessage: "Copy to another page"
                    },
                    deleteConfirmMessage: {
                        id: "V6pP8P",
                        defaultMessage: "Are you sure you want to delete this section?"
                    },
                    deleteConfirmSubMessage: {
                        id: "qbMLhY",
                        defaultMessage: "(This can’t be undone)"
                    },
                    yesDelete: {
                        id: "K3r6DQ",
                        defaultMessage: "Delete"
                    },
                    cancel: {
                        id: "47FYwb",
                        defaultMessage: "Cancel"
                    }
                }),
                I = {
                    spacer: "SectionOverlayControls_spacer__Uo1B4",
                    SectionControlsOuterContainer: "SectionOverlayControls_SectionControlsOuterContainer__aukKt",
                    "--active": "SectionOverlayControls_--active__atU7y",
                    "--global": "SectionOverlayControls_--global__CXbl0",
                    SectionControls: "SectionOverlayControls_SectionControls__BURdr",
                    "--unClickable": "SectionOverlayControls_--unClickable__Jtqam",
                    bottomControls: "SectionOverlayControls_bottomControls__JObJ6",
                    spaceBetween: "SectionOverlayControls_spaceBetween__b994K",
                    addButton: "SectionOverlayControls_addButton__Xo5Xn",
                    section: "SectionOverlayControls_section__oz-rh"
                };
            var L = t(63552),
                T = t(15740),
                F = t(32526),
                D = t(69224),
                N = t(63394);
            const O = ({
                    section: e,
                    activator: n
                }) => {
                    var t, o;
                    const a = null == e || null == (t = e.section_value) || null == (t = t.data) ? void 0 : t.id,
                        i = (0, l.useDispatch)();
                    return (0, w.tZ)(F.CustomSectionSettingsPopup, {
                        section: null == e || null == (o = e.section_value) || null == (o = o.data) ? void 0 : o.value,
                        activator: n,
                        updateUi: e => {
                            return n = a, t = e, void i((0, T.NB)(n, t));
                            var n, t
                        },
                        mutateApi: e => {
                            return n = a, t = e, void i((0, T.sW)(n, t));
                            var n, t
                        },
                        onUploadImage: e => {
                            i((0, D.pI)(N.Il.EditImage, {
                                updaterCallback: e
                            }))
                        }
                    })
                },
                z = e => {
                    const n = null == e ? void 0 : e.replace("#", ""),
                        t = 3 === (null == n ? void 0 : n.length) ? n + n : n,
                        l = parseInt(null == t ? void 0 : t.substring(0, 2), 16),
                        o = parseInt(null == t ? void 0 : t.substring(2, 4), 16),
                        a = parseInt(null == t ? void 0 : t.substring(4, 6), 16),
                        i = parseInt(null == t ? void 0 : t.substring(6, 8), 16),
                        r = Math.round(i / 255 * 100) / 100;
                    return `rgba(${l}, ${o}, ${a}, ${r||0===r?r:1})`
                },
                E = ({
                    background: e,
                    updateUi: n,
                    onUploadImage: t
                }) => {
                    const [l, o] = (0, a.useState)("color"), [i, r] = (0, a.useState)(!(null == e || !e.overlay)), s = null != e && e.image ? {
                        backgroundImage: `linear-gradient(${z(null==e?void 0:e.overlay)},${z(null==e?void 0:e.overlay)}),url(${null==e?void 0:e.image} )`,
                        backgroundPosition: "center center",
                        backgroundSize: "cover"
                    } : {}, d = e => {
                        n((n => Object.assign({}, n, {
                            background: e
                        })))
                    }, u = n => {
                        d(Object.assign({}, e, {
                            color: "",
                            overlay: n
                        }))
                    };
                    return (0, w.BX)(L.Box, {
                        children: [(0, w.BX)(L.Stack, {
                            direction: "row",
                            padding: "16px",
                            children: [(0, w.tZ)(L.Button, {
                                color: "color" === l ? "primary" : "white",
                                onClick: () => o("color"),
                                prefixIcon: (0, w.tZ)(L.ColorDropIcon, {
                                    color: "color" === l ? "white" : "info"
                                }),
                                contentAlign: "center",
                                contentWidth: "fit-content",
                                fullWidth: !0,
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "uMhpKe",
                                    defaultMessage: "Color"
                                })
                            }), (0, w.tZ)(L.Button, {
                                color: "image" === l ? "primary" : "white",
                                onClick: () => o("image"),
                                prefixIcon: (0, w.tZ)(L.ImageIcon, {}),
                                contentAlign: "center",
                                contentWidth: "fit-content",
                                fullWidth: !0,
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "+0zv6g",
                                    defaultMessage: "Image"
                                })
                            })]
                        }), "color" === l && (0, w.tZ)(L.Box, {
                            children: (0, w.BX)(L.Box, {
                                padding: "0 16px 16px 16px",
                                borderBottom: "2px solid",
                                borderBottomColor: "grey",
                                children: [(0, w.tZ)(L.Label, {
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "CMansq",
                                        defaultMessage: "Background color"
                                    })
                                }), (0, w.tZ)(L.InputColor, {
                                    value: (null == e ? void 0 : e.color) || "#FFFFFF",
                                    onChange: n => {
                                        d(Object.assign({}, e, {
                                            image: "",
                                            overlay: "#ffffff01",
                                            color: n
                                        }))
                                    }
                                })]
                            })
                        }), "image" === l && (0, w.BX)(L.Stack, {
                            padding: "0 16px 16px 16px",
                            borderBottom: "2px solid",
                            borderBottomColor: "grey",
                            children: [(0, w.BX)(L.Box, {
                                children: [(0, w.tZ)(L.Label, {
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "LOA559",
                                        defaultMessage: "Background Image"
                                    })
                                }), (0, w.tZ)(L.Stack, {
                                    justify: null != e && e.image ? "start" : "center",
                                    align: null != e && e.image ? "end" : "center",
                                    width: "100%",
                                    padding: "6px",
                                    height: "150px",
                                    borderRadius: "6px",
                                    border: "1px solid",
                                    borderColor: "overlay",
                                    backgroundColor: {
                                        cloud: "light"
                                    },
                                    style: s,
                                    onClick: n => {
                                        n.stopPropagation(), t((n => {
                                            d(Object.assign({}, e, {
                                                color: "",
                                                image: n,
                                                overlay: (null == e ? void 0 : e.overlay) || "#ffffff01"
                                            }))
                                        }))
                                    },
                                    children: null != e && e.image ? (0, w.tZ)(L.Tooltip, {
                                        content: (0, w.tZ)(Z.FormattedMessage, {
                                            defaultMessage: "Remove image",
                                            id: "7vM5rK"
                                        }),
                                        children: (0, w.tZ)(L.ButtonIcon, {
                                            size: "small",
                                            color: "white",
                                            onClick: n => {
                                                n.stopPropagation(), d(Object.assign({}, e, {
                                                    color: "",
                                                    image: ""
                                                }))
                                            },
                                            children: (0, w.tZ)(L.GarbageIcon, {})
                                        })
                                    }) : (0, w.tZ)(L.Button, {
                                        color: "white",
                                        size: "small",
                                        squared: !(null == e || !e.image),
                                        prefixIcon: (0, w.tZ)(L.UploadIcon, {}),
                                        children: (0, w.tZ)(Z.FormattedMessage, {
                                            defaultMessage: "Upload",
                                            id: "p4N05H"
                                        })
                                    })
                                })]
                            }), (0, w.tZ)(L.ToggleButton, {
                                hideIcons: !0,
                                value: i,
                                label: (0, w.tZ)(L.Label, {
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "NtjVij",
                                        defaultMessage: "Color Overlay"
                                    })
                                }),
                                onChange: e => {
                                    r(e), e || u("#FFFFFF01")
                                }
                            }), i && (0, w.BX)(L.Box, {
                                children: [(0, w.tZ)(L.Label, {
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Overlay color",
                                        id: "etgaBX"
                                    })
                                }), (0, w.tZ)(L.InputColor, {
                                    value: (null == e ? void 0 : e.overlay) || "#FFFFFF01",
                                    onChange: u
                                })]
                            })]
                        }), (0, w.tZ)(L.Box, {
                            padding: "10px 0px",
                            children: (0, w.tZ)(L.Button, {
                                plain: !0,
                                prefixIcon: (0, w.tZ)(L.ResetIcon, {
                                    color: "transparent"
                                }),
                                onClick: () => {
                                    d(Object.assign({}, e, {
                                        color: "",
                                        image: "",
                                        overlay: "#ffffff01"
                                    }))
                                },
                                children: (0, w.tZ)(L.Label, {
                                    style: {
                                        cursor: "pointer"
                                    },
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "aYpUua",
                                        defaultMessage: "Reset section background"
                                    })
                                })
                            })
                        })]
                    })
                };
            var A = t(54194);
            const j = (0, l.connect)((() => ({})), (e => ({
                    updateSectionSettingsUi(n, t) {
                        e((0, A.rL)(n, t))
                    },
                    uploadBackgroundImage: n => {
                        e((0, D.pI)(N.Il.EditImage, {
                            updaterCallback: n
                        }))
                    }
                })))((({
                    section: e,
                    uploadBackgroundImage: n,
                    updateSectionSettingsUi: t
                }) => {
                    const l = null == e ? void 0 : e.id,
                        o = null == e ? void 0 : e.section_settings;
                    return (0, w.tZ)(E, {
                        updateUi: e => {
                            const n = function(e, n) {
                                return void 0 === e ? n : "function" == typeof e ? e(n) : e
                            }(e, o);
                            null == t || t(l, n)
                        },
                        onUploadImage: e => {
                            null == n || n(e)
                        },
                        background: null == o ? void 0 : o.background
                    })
                })),
                R = ({
                    section: e,
                    activator: n
                }) => {
                    const t = (0, l.useDispatch)(),
                        {
                            pushEvent: o
                        } = (0, d.useTracking)(),
                        i = null == e ? void 0 : e.id,
                        r = null == e ? void 0 : e.section_settings,
                        s = (0, a.useRef)(r);
                    return (0, w.BX)(L.Popup, {
                        onPopupClose: () => {
                            t((0, A.re)(i, r, s.current)), s.current = r, o("Section settings updated")
                        },
                        activator: n,
                        children: [(0, w.tZ)(L.Popup.Header, {
                            children: (0, w.tZ)(L.Heading, {
                                color: "white",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "ywiwYw",
                                    defaultMessage: "Background settings"
                                })
                            })
                        }), (0, w.tZ)(L.Popup.Body, {
                            width: "400px",
                            padding: "0",
                            children: (0, w.tZ)(j, {
                                section: e
                            })
                        })]
                    })
                };
            var P = t(5862),
                U = t.n(P);
            const G = ({
                style: e,
                onPrevDesign: n,
                onNextDesign: t,
                globalNavbarSettings: l,
                updateGlobalNavbarSettingsUi: o
            }) => {
                const a = e => {
                    o(Object.assign({}, l, {
                        style: e
                    }))
                };
                return (0, w.BX)(L.Box, {
                    children: [(0, w.BX)(L.Box, {
                        borderBottom: "2px solid",
                        borderBottomColor: "grey",
                        children: [(0, w.tZ)(L.Text, {
                            fontSize: "medium",
                            fontWeight: "semiBold",
                            children: (0, w.tZ)(Z.FormattedMessage, {
                                id: "IEdhVW",
                                defaultMessage: "NAV BAR DESIGN"
                            })
                        }), (0, w.BX)(L.Box, {
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            m: "15px 0px",
                            children: [(0, w.tZ)(L.Box, {
                                flex: "1",
                                style: {
                                    paddingInlineEnd: "10px"
                                },
                                children: (0, w.tZ)(L.Button, {
                                    fullWidth: !0,
                                    color: "white",
                                    contentWidth: "auto",
                                    contentAlign: "center",
                                    prefixIcon: (0, w.tZ)(L.ChevronLeftIcon, {
                                        reverseOnRtl: !0
                                    }),
                                    onClick: n,
                                    children: (0, w.tZ)(L.Text, {
                                        color: "secondary",
                                        fontSize: "medium",
                                        fontWeight: "semiBold",
                                        children: (0, w.tZ)(Z.FormattedMessage, {
                                            id: "JJNc3c",
                                            defaultMessage: "Previous"
                                        })
                                    })
                                })
                            }), (0, w.tZ)(L.Box, {
                                flex: "1",
                                style: {
                                    paddingInlineStart: "10px"
                                },
                                children: (0, w.tZ)(L.Button, {
                                    fullWidth: !0,
                                    color: "white",
                                    contentWidth: "auto",
                                    contentAlign: "center",
                                    suffixIcon: (0, w.tZ)(L.ChevronRightIcon, {
                                        reverseOnRtl: !0
                                    }),
                                    onClick: t,
                                    children: (0, w.tZ)(L.Text, {
                                        color: "secondary",
                                        fontSize: "medium",
                                        fontWeight: "semiBold",
                                        children: (0, w.tZ)(Z.FormattedMessage, {
                                            id: "9+Ddtu",
                                            defaultMessage: "Next"
                                        })
                                    })
                                })
                            })]
                        })]
                    }), (0, w.BX)(L.Box, {
                        justify: "between",
                        margin: "10px 0px",
                        children: [(0, w.tZ)(L.ToggleButton, {
                            hideIcons: !0,
                            value: e.isTransparent,
                            onChange: n => {
                                a(Object.assign({}, e, {
                                    isTransparent: n
                                }))
                            },
                            display: "flex",
                            justify: "start",
                            align: "start",
                            mb: "20px",
                            label: (0, w.tZ)(L.Text, {
                                color: "secondary",
                                fontSize: "medium",
                                fontWeight: "semiBold",
                                style: {
                                    paddingInlineStart: "5px"
                                },
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "7kAON9",
                                    defaultMessage: "Make Nav bar transparent"
                                })
                            })
                        }), (0, w.tZ)(L.ToggleButton, {
                            hideIcons: !0,
                            value: e.isSticky,
                            onChange: n => {
                                a(Object.assign({}, e, {
                                    isSticky: n
                                }))
                            },
                            display: "flex",
                            justify: "start",
                            align: "start",
                            label: (0, w.BX)(L.Box, {
                                style: {
                                    paddingInlineStart: "5px"
                                },
                                children: [(0, w.tZ)(L.Text, {
                                    color: "secondary",
                                    fontSize: "medium",
                                    fontWeight: "semiBold",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "abQuRC",
                                        defaultMessage: "Sticky Nav bar"
                                    })
                                }), (0, w.tZ)(L.Text, {
                                    color: "info",
                                    fontSize: "medium",
                                    fontWeight: "normal",
                                    wordBreak: "break-word",
                                    maxWidth: "250px",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "CHntO8",
                                        defaultMessage: "Sticky Nav bar fixes it’s position on the top of the page, even while scrolling"
                                    })
                                })]
                            })
                        })]
                    })]
                })
            };
            var X = t(15132);
            const H = ({
                    colors: e,
                    globalNavbarSettings: n,
                    updateGlobalNavbarSettingsUi: t,
                    activeTheme: l,
                    isAppRtl: o
                }) => {
                    const a = e => {
                        t(Object.assign({}, n, {
                            colors: e
                        }))
                    };
                    return (0, w.BX)(L.Box, {
                        children: [(0, w.BX)(L.Box, {
                            mb: "10px",
                            children: [(0, w.tZ)(L.Text, {
                                mb: "5px",
                                color: "secondary",
                                fontSize: "medium",
                                fontWeight: "semiBold",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "03dSty",
                                    defaultMessage: "Text/Links color"
                                })
                            }), (0, w.tZ)(L.Stack, {
                                children: (0, w.tZ)(X.ColorAndThemeSelector, {
                                    isSiteRtl: o,
                                    theme: l,
                                    colorTypeAndValue: {
                                        type: (null == e ? void 0 : e.navLinksColorType) || "Custom",
                                        value: (null == e ? void 0 : e.navLinksColor) || "",
                                        themeColorKey: (null == e ? void 0 : e.navLinksColorThemeColorKey) || ""
                                    },
                                    onChangeColor: n => {
                                        a(Object.assign({}, e, {
                                            navLinksColor: n.value,
                                            navLinksColorType: (null == n ? void 0 : n.type) || "Custom",
                                            navLinksColorThemeColorKey: null == n ? void 0 : n.themeColorKey
                                        }))
                                    }
                                })
                            })]
                        }), n.style.isTransparent ? (0, w.tZ)(L.Box, {
                            children: (0, w.BX)(L.Box, {
                                children: [(0, w.tZ)(L.Text, {
                                    mb: "5px",
                                    color: {
                                        ink: "lighterActive"
                                    },
                                    fontSize: "medium",
                                    fontWeight: "semiBold",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "CMansq",
                                        defaultMessage: "Background color"
                                    })
                                }), (0, w.BX)(L.Box, {
                                    padding: "5px",
                                    display: "flex",
                                    justifyContent: "start",
                                    alignItems: "center",
                                    backgroundColor: "overlay",
                                    border: "2px solid transparent",
                                    borderColor: "darkerGrey",
                                    borderRadius: "8px",
                                    children: [(0, w.tZ)(L.TransparentIcon, {
                                        color: "transparent",
                                        size: "xxxl"
                                    }), (0, w.tZ)(L.Text, {
                                        color: {
                                            ink: "lighterActive"
                                        },
                                        fontSize: "medium",
                                        fontWeight: "semiBold",
                                        style: {
                                            paddingInlineStart: "5px"
                                        },
                                        children: (0, w.tZ)(Z.FormattedMessage, {
                                            id: "dF3WgL",
                                            defaultMessage: "Transparent"
                                        })
                                    })]
                                }), (0, w.tZ)(L.Alert, {
                                    type: "warning",
                                    title: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "FOgS2p",
                                        defaultMessage: "Nav bar is transparent"
                                    }),
                                    description: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "NWz7I0",
                                        defaultMessage: "You can’t edit the Nav bar background because Make Nav bar transparent is turned on. disable it from Style tab to edit the color"
                                    })
                                })]
                            })
                        }) : (0, w.BX)(L.Box, {
                            children: [(0, w.tZ)(L.Text, {
                                mb: "5px",
                                color: "secondary",
                                fontSize: "medium",
                                fontWeight: "semiBold",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "CMansq",
                                    defaultMessage: "Background color"
                                })
                            }), (0, w.tZ)(X.ColorAndThemeSelector, {
                                isSiteRtl: o,
                                theme: l,
                                colorTypeAndValue: {
                                    type: (null == e ? void 0 : e.backgroundColorType) || "Custom",
                                    value: (null == e ? void 0 : e.backgroundColor) || "",
                                    themeColorKey: (null == e ? void 0 : e.backgroundColorThemeColorKey) || ""
                                },
                                onChangeColor: n => {
                                    a(Object.assign({}, e, {
                                        backgroundColor: n.value,
                                        backgroundColorType: (null == n ? void 0 : n.type) || "Custom",
                                        backgroundColorThemeColorKey: null == n ? void 0 : n.themeColorKey
                                    }))
                                }
                            })]
                        })]
                    })
                },
                W = ({
                    sticky: e,
                    onStickyNavbarLogoUpload: n,
                    globalNavbarSettings: t,
                    updateGlobalNavbarSettingsUi: l,
                    isAppRtl: o,
                    activeTheme: a
                }) => {
                    const i = e => {
                        l(Object.assign({}, t, {
                            sticky: e
                        }))
                    };
                    return (0, w.BX)(L.Box, {
                        children: [(0, w.BX)(L.Box, {
                            children: [(0, w.tZ)(L.Text, {
                                color: "secondary",
                                fontSize: "medium",
                                fontWeight: "semiBold",
                                mb: "5px",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "03dSty",
                                    defaultMessage: "Text/Links color"
                                })
                            }), (0, w.tZ)(X.ColorAndThemeSelector, {
                                isSiteRtl: o,
                                theme: a,
                                colorTypeAndValue: {
                                    type: (null == e ? void 0 : e.navLinksColorType) || "Custom",
                                    value: (null == e ? void 0 : e.navLinksColor) || "",
                                    themeColorKey: (null == e ? void 0 : e.navLinksColorThemeColorKey) || ""
                                },
                                onChangeColor: n => {
                                    i(Object.assign({}, e, {
                                        navLinksColor: n.value,
                                        navLinksColorType: (null == n ? void 0 : n.type) || "Custom",
                                        navLinksColorThemeColorKey: null == n ? void 0 : n.themeColorKey
                                    }))
                                }
                            })]
                        }), (0, w.BX)(L.Box, {
                            margin: "20px 0px",
                            children: [(0, w.tZ)(L.Text, {
                                color: "secondary",
                                fontSize: "medium",
                                fontWeight: "semiBold",
                                mb: "5px",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "CMansq",
                                    defaultMessage: "Background color"
                                })
                            }), (0, w.tZ)(X.ColorAndThemeSelector, {
                                isSiteRtl: o,
                                theme: a,
                                colorTypeAndValue: {
                                    type: (null == e ? void 0 : e.backgroundColorType) || "Custom",
                                    value: (null == e ? void 0 : e.backgroundColor) || "",
                                    themeColorKey: (null == e ? void 0 : e.backgroundColorThemeColorKey) || ""
                                },
                                onChangeColor: n => {
                                    i(Object.assign({}, e, {
                                        backgroundColor: n.value,
                                        backgroundColorType: (null == n ? void 0 : n.type) || "Custom",
                                        backgroundColorThemeColorKey: null == n ? void 0 : n.themeColorKey
                                    }))
                                }
                            })]
                        }), (0, w.BX)(L.Stack, {
                            children: [(0, w.tZ)(L.ToggleButton, {
                                hideIcons: !0,
                                value: e.isAlternateLogo,
                                onChange: n => {
                                    i(Object.assign({}, e, {
                                        isAlternateLogo: n
                                    }))
                                },
                                display: "flex",
                                justify: "start",
                                align: "start",
                                mb: "20px",
                                label: (0, w.BX)(L.Box, {
                                    style: {
                                        paddingInlineStart: "5px"
                                    },
                                    children: [(0, w.tZ)(L.Text, {
                                        color: "secondary",
                                        fontSize: "medium",
                                        fontWeight: "semiBold",
                                        children: (0, w.tZ)(Z.FormattedMessage, {
                                            id: "WVr+6b",
                                            defaultMessage: "Alternate logo"
                                        })
                                    }), (0, w.tZ)(L.Text, {
                                        color: "info",
                                        fontSize: "medium",
                                        fontWeight: "normal",
                                        wordBreak: "break-word",
                                        maxWidth: "250px",
                                        children: (0, w.tZ)(Z.FormattedMessage, {
                                            id: "woXyzQ",
                                            defaultMessage: "Turn this on if your logo is not showing correctly, and upload your alternate logo"
                                        })
                                    })]
                                })
                            }), e.isAlternateLogo ? "empty" === e.stickyLogoUrl ? (0, w.tZ)(L.Stack, {
                                direction: "row",
                                borderRadius: "6px",
                                border: "1px solid transparent",
                                borderColor: "overlay",
                                backgroundColor: {
                                    cloud: "light"
                                },
                                justify: "center",
                                align: "center",
                                p: "30px 20px",
                                children: (0, w.tZ)(L.Button, {
                                    color: "white",
                                    prefixIcon: (0, w.tZ)(L.UploadIcon, {}),
                                    onClick: e => {
                                        e.stopPropagation(), n()
                                    },
                                    children: (0, w.tZ)(L.Text, {
                                        color: "secondary",
                                        fontSize: "medium",
                                        fontWeight: "semiBold",
                                        children: (0, w.tZ)(Z.FormattedMessage, {
                                            id: "XmcDl5",
                                            defaultMessage: "Select Image"
                                        })
                                    })
                                })
                            }) : (0, w.BX)(L.Box, {
                                children: [(0, w.BX)(L.Stack, {
                                    direction: "column",
                                    borderRadius: "6px",
                                    border: "1px solid #D0D5DD",
                                    backgroundColor: {
                                        cloud: "light"
                                    },
                                    justify: "center",
                                    align: "center",
                                    p: "10px",
                                    children: [(0, w.tZ)(L.Stack, {
                                        width: "100%",
                                        direction: "row",
                                        borderRadius: "6px",
                                        justify: "end",
                                        align: "start",
                                        children: (0, w.tZ)(L.Stack, {
                                            height: "36px",
                                            width: "36px",
                                            style: {
                                                paddingInlineStart: "5px"
                                            },
                                            pt: "3px",
                                            borderRadius: "6px",
                                            border: "1px solid #D0D5DD",
                                            boxShadow: "0px 1px 2px 0px #1018280D",
                                            cursor: "pointer",
                                            backgroundColor: "white",
                                            justify: "center",
                                            align: "center",
                                            onClick: () => i(Object.assign({}, e, {
                                                stickyLogoUrl: "empty"
                                            })),
                                            children: (0, w.tZ)(L.TrashOutlineIcon, {
                                                reverseOnRtl: !0,
                                                size: "xxl",
                                                customColor: "#344054"
                                            })
                                        })
                                    }), (0, w.tZ)(L.Box, {
                                        pb: "16px",
                                        width: "100%",
                                        display: "flex",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        children: (0, w.tZ)("img", {
                                            src: e.stickyLogoUrl,
                                            alt: "Sticky navbar logo",
                                            style: {
                                                margin: 0,
                                                padding: 0,
                                                height: "small" === e.stickyLogoSize ? "30px" : "large" === e.stickyLogoSize ? "80px" : "50px",
                                                maxHeight: "small" === e.stickyLogoSize ? "30px" : "large" === e.stickyLogoSize ? "80px" : "50px"
                                            }
                                        })
                                    })]
                                }), (0, w.tZ)(L.Stack, {
                                    my: "10px",
                                    borderRadius: "6px",
                                    border: "1px solid #D0D5DD",
                                    children: (0, w.tZ)(L.Button, {
                                        squared: !0,
                                        color: "black",
                                        fullWidth: !0,
                                        plain: !0,
                                        prefixIcon: (0, w.tZ)(L.ChangeLogoIcon, {
                                            customColor: "#344054"
                                        }),
                                        contentAlign: "center",
                                        contentWidth: "auto",
                                        onClick: e => {
                                            e.stopPropagation(), n()
                                        },
                                        children: (0, w.tZ)(L.Text, {
                                            color: "secondary",
                                            fontSize: "sm",
                                            fontWeight: "semiBold",
                                            children: (0, w.tZ)(Z.FormattedMessage, {
                                                defaultMessage: "Change logo",
                                                id: "P3mLup"
                                            })
                                        })
                                    })
                                }), (0, w.BX)(L.Box, {
                                    children: [(0, w.tZ)(L.Text, {
                                        color: "secondary",
                                        fontSize: "medium",
                                        fontWeight: "semiBold",
                                        mb: "10px",
                                        children: (0, w.tZ)(Z.FormattedMessage, {
                                            id: "Rh6vDo",
                                            defaultMessage: "Logo size"
                                        })
                                    }), (0, w.BX)(L.SelectTabs, {
                                        value: e.stickyLogoSize,
                                        fitContentWidth: !0,
                                        name: "sticky-logo-size",
                                        onChange: n => i(Object.assign({}, e, {
                                            stickyLogoSize: n
                                        })),
                                        children: [(0, w.tZ)(L.SelectTab, {
                                            label: (0, w.tZ)(Z.FormattedMessage, {
                                                id: "BPnT3T",
                                                defaultMessage: "Small"
                                            }),
                                            value: "small"
                                        }), (0, w.tZ)(L.SelectTab, {
                                            label: (0, w.tZ)(Z.FormattedMessage, {
                                                id: "ovJ26C",
                                                defaultMessage: "Medium"
                                            }),
                                            value: "medium"
                                        }), (0, w.tZ)(L.SelectTab, {
                                            label: (0, w.tZ)(Z.FormattedMessage, {
                                                id: "/06iwc",
                                                defaultMessage: "Large"
                                            }),
                                            value: "large"
                                        })]
                                    })]
                                })]
                            }) : null]
                        })]
                    })
                };
            var K = t(84065),
                V = t(71007),
                Y = t(80375);
            const Q = [{
                    id: "style",
                    content: (0, w.tZ)(Z.FormattedMessage, {
                        id: "7mL9QE",
                        defaultMessage: "Style"
                    })
                }, {
                    id: "sticky",
                    content: (0, w.tZ)(Z.FormattedMessage, {
                        id: "kpXiKr",
                        defaultMessage: "Sticky"
                    })
                }, {
                    id: "colors",
                    content: (0, w.tZ)(Z.FormattedMessage, {
                        id: "U+dGE5",
                        defaultMessage: "Colors"
                    })
                }],
                q = (0, l.connect)((e => ({
                    globalNavbarSettings: e.site.data.globalNavbarSettings.navbarSettings
                })), (e => ({
                    onStickyNavbarLogoUpload() {
                        e((0, D.pI)(N.Il.EditImage, {
                            updaterCallback: n => {
                                e((0, K.xA)(n))
                            }
                        }))
                    },
                    mutateGlobalNavbarSettingsApi(n, t) {
                        e((0, A.Vh)(n, t))
                    },
                    updateGlobalNavbarSettingsUi(n) {
                        e((0, K.LQ)(n))
                    },
                    deselectStickyNavbarLogo() {
                        e((0, K.JJ)())
                    }
                })))((({
                    sectionId: e,
                    onPrevDesign: n,
                    onNextDesign: t,
                    activator: o,
                    onStickyNavbarLogoUpload: a,
                    globalNavbarSettings: r,
                    deselectStickyNavbarLogo: s,
                    updateGlobalNavbarSettingsUi: d,
                    mutateGlobalNavbarSettingsApi: u
                }) => {
                    const [c, p] = i().useState(Q[0]), g = "rtl" === (0, l.useSelector)((e => {
                        var n;
                        return (0, Y.w)(null == e || null == (n = e.language) ? void 0 : n.language)
                    })), h = (0, l.useSelector)((e => {
                        var n;
                        return null == V.wl || null == (n = V.wl.get(e)) || null == (n = n.themes) || null == (n = n.active) || null == (n = n.theme) ? void 0 : n.content
                    })), y = i().useCallback((e => p(e)), []);
                    return (0, w.BX)(L.Popup, {
                        onPopupClose: () => {
                            u(e, r)
                        },
                        activator: o,
                        children: [(0, w.tZ)(L.Popup.Header, {
                            children: (0, w.BX)(L.Stack, {
                                direction: "row",
                                justify: "start",
                                align: "center",
                                children: [(0, w.tZ)(L.GlobalBadgeIcon, {
                                    size: "xxl",
                                    color: "transparent"
                                }), (0, w.tZ)(L.Heading, {
                                    color: "white",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "PHYp9i",
                                        defaultMessage: "Global Nav bar Settings"
                                    })
                                })]
                            })
                        }), (0, w.tZ)(L.Popup.Body, {
                            p: "0px",
                            width: "400px",
                            children: (0, w.tZ)(L.Tabs, {
                                tabs: r.style.isSticky ? Q : Q.filter((e => "sticky" !== e.id)),
                                selected: c,
                                onSelect: y,
                                children: (0, w.BX)(L.Card.Body, {
                                    children: ["style" === c.id && (0, w.tZ)(G, {
                                        onPrevDesign: n,
                                        onNextDesign: t,
                                        style: r.style,
                                        globalNavbarSettings: r,
                                        updateGlobalNavbarSettingsUi: d
                                    }), "sticky" === c.id && r.style.isSticky && (0, w.tZ)(W, {
                                        onStickyNavbarLogoUpload: a,
                                        sticky: r.sticky,
                                        globalNavbarSettings: r,
                                        deselectStickyNavbarLogo: s,
                                        updateGlobalNavbarSettingsUi: d,
                                        isAppRtl: g,
                                        activeTheme: h
                                    }), "colors" === c.id && (0, w.tZ)(H, {
                                        colors: r.colors,
                                        globalNavbarSettings: r,
                                        updateGlobalNavbarSettingsUi: d,
                                        activeTheme: h,
                                        isAppRtl: g
                                    })]
                                })
                            })
                        })]
                    })
                }));
            t(91830), t(93677);
            var J, ee = t(23882),
                ne = t(96001);

            function te({
                children: e
            }) {
                return e
            }(0, ee.default)({
                key: "wf",
                container: null == (J = window) || null == (J = J.parent) ? void 0 : J.document.head,
                stylisPlugins: [ne.Ji]
            });
            let le, oe, ae, ie = e => e;
            const re = ({
                    sectionId: e,
                    onPrevDesign: n,
                    onNextDesign: t,
                    isFirst: l,
                    isLast: o,
                    isUiRTL: a,
                    section: i,
                    isNavbar: r,
                    onMoveUp: s,
                    onMoveDown: d,
                    isUniversal: u,
                    isFooter: c,
                    isCustomSection: p,
                    maintenancePageOrErrorPage: g,
                    isMaintenancePage: h,
                    isFluidSection: y,
                    onOpenDuplicateSectionModal: m,
                    onOpenDeleteSectionModal: v,
                    onOpenSectionInfoModal: b
                }) => {
                    var f, x, S, _, C;
                    const B = (0, Z.useIntl)(),
                        $ = r || c,
                        M = "services#0016" === (null == i || null == (f = i.designInfo) ? void 0 : f.design_view) || (null == i || null == (x = i.designInfo) ? void 0 : x.design_view.startsWith("hero")) || (null == i || null == (S = i.designInfo) ? void 0 : S.design_view.startsWith("header")),
                        I = null != i && null != (_ = i.designInfo) && _.design_view.includes("custom") ? "custom" : null == i || null == (C = i.designInfo) ? void 0 : C.design_title.replace(/[^0-9](?=[0-9])/g, "$&-");
                    return (0, w.BX)(se, {
                        isUiRTL: a,
                        isGlobal: $,
                        isNavbar: r,
                        isMaintenancePage: h,
                        children: [$ ? (0, w.tZ)(L.Tooltip, {
                            maxWidth: "150px",
                            content: (0, w.tZ)(L.Text, {
                                fontSize: "medium",
                                color: "white",
                                align: "center",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "6VsWmd",
                                    defaultMessage: "Editing Global elements will affect it in all pages"
                                })
                            }),
                            children: (0, w.BX)(de, {
                                isGlobal: $,
                                children: [(0, w.tZ)(L.LinkIcon, {
                                    size: "sm"
                                }), (0, w.tZ)(L.RectangleIcon, {}), r ? (0, w.BX)(w.HY, {
                                    children: [(0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Header Section",
                                        id: "vXy+6Q"
                                    }), "-", I.replace(/[^0-9]/g, "")]
                                }) : c ? (0, w.BX)(w.HY, {
                                    children: [(0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Footer",
                                        id: "Vge+RX"
                                    }), "-", I.replace(/[^0-9]/g, "")]
                                }) : (0, w.tZ)(Z.FormattedMessage, {
                                    defaultMessage: "Section",
                                    id: "q1Cd7m"
                                })]
                            })
                        }) : (0, w.BX)(de, {
                            isGlobal: $,
                            children: [(0, w.tZ)(L.RectangleIcon, {}), (0, w.tZ)("span", {
                                className: "section-name",
                                children: I
                            }), (0, w.tZ)(Z.FormattedMessage, {
                                defaultMessage: "Section",
                                id: "q1Cd7m"
                            })]
                        }), !u && s && !l && !y && (0, w.tZ)(ue, {
                            isGlobal: $,
                            children: (0, w.tZ)(L.Tooltip, {
                                content: B.formatMessage(k.moveUp),
                                children: (0, w.tZ)(L.ButtonIcon, {
                                    size: "small",
                                    color: "white",
                                    stopOpacity: !0,
                                    onClick: s,
                                    children: (0, w.tZ)(L.ArrowUpIcon, {})
                                })
                            })
                        }), r && (0, w.tZ)(q, {
                            activator: (0, w.tZ)(ue, {
                                isGlobal: $,
                                children: (0, w.tZ)(L.Tooltip, {
                                    content: B.formatMessage(k.sectionSettings),
                                    children: (0, w.tZ)(L.ButtonIcon, {
                                        size: "small",
                                        color: "white",
                                        stopOpacity: !0,
                                        children: (0, w.tZ)(L.SettingsGeneralIcon, {})
                                    })
                                })
                            }),
                            sectionId: e,
                            onPrevDesign: n,
                            onNextDesign: t
                        }), !u && !o && d && !y && (0, w.tZ)(ue, {
                            isGlobal: $,
                            children: (0, w.tZ)(L.Tooltip, {
                                content: B.formatMessage(k.moveDown),
                                children: (0, w.tZ)(L.ButtonIcon, {
                                    size: "small",
                                    color: "white",
                                    stopOpacity: !0,
                                    onClick: d,
                                    children: (0, w.tZ)(L.ArrowDownIcon, {})
                                })
                            })
                        }), !u && p ? (0, w.tZ)(ue, {
                            isGlobal: $,
                            children: (0, w.tZ)(O, {
                                section: i,
                                activator: (0, w.tZ)("span", {
                                    children: (0, w.tZ)(L.Tooltip, {
                                        content: B.formatMessage(k.sectionSettings),
                                        children: (0, w.tZ)(L.ButtonIcon, {
                                            size: "small",
                                            color: "white",
                                            stopOpacity: !0,
                                            children: (0, w.tZ)(L.SettingsGeneralIcon, {})
                                        })
                                    })
                                })
                            })
                        }) : u || M || y ? null : (0, w.tZ)(ue, {
                            isGlobal: $,
                            children: (0, w.tZ)(R, {
                                section: i,
                                activator: (0, w.tZ)("span", {
                                    children: (0, w.tZ)(L.Tooltip, {
                                        content: B.formatMessage(k.sectionSettings),
                                        children: (0, w.tZ)(L.ButtonIcon, {
                                            size: "small",
                                            color: "white",
                                            stopOpacity: !0,
                                            children: (0, w.tZ)(L.SettingsGeneralIcon, {})
                                        })
                                    })
                                })
                            })
                        }), !u && !g && !y && (0, w.tZ)(ue, {
                            isGlobal: $,
                            children: (0, w.tZ)(te, {
                                children: (0, w.BX)(L.DropMenu, {
                                    applyHoverEffect: !0,
                                    activator: (0, w.tZ)(L.Tooltip, {
                                        content: B.formatMessage(k.moreActions),
                                        children: (0, w.tZ)(L.ButtonIcon, {
                                            size: "small",
                                            color: "white",
                                            stopOpacity: !0,
                                            children: (0, w.tZ)(L.MoreHorizIcon, {})
                                        })
                                    }),
                                    children: [(0, w.tZ)(L.Button, {
                                        plain: !0,
                                        compact: !0,
                                        prefixIcon: (0, w.tZ)(L.CopyIcon, {
                                            size: "xl"
                                        }),
                                        onClick: m,
                                        children: B.formatMessage(k.copySection)
                                    }), (0, w.tZ)(L.Button, {
                                        plain: !0,
                                        compact: !0,
                                        color: "danger",
                                        onClick: v,
                                        prefixIcon: (0, w.tZ)(L.GarbageIcon, {
                                            size: "sm"
                                        }),
                                        children: B.formatMessage(k.deleteSection)
                                    })]
                                })
                            })
                        }), b && (0, w.tZ)(ue, {
                            isGlobal: $,
                            children: (0, w.tZ)(L.Tooltip, {
                                content: B.formatMessage(k.sectionInfo),
                                children: (0, w.tZ)(L.ButtonIcon, {
                                    size: "small",
                                    color: "white",
                                    stopOpacity: !0,
                                    onClick: b,
                                    children: (0, w.tZ)(L.EyeIcon, {
                                        viewBox: "0 0 20 20"
                                    })
                                })
                            })
                        })]
                    })
                },
                se = U()(L.Box)(le || (le = ie `
  position: absolute;
  pointer-events: all;
  display: flex;
  transform: ${0};
  ${0}
  border: ${0};
  background: #fff;

  border-radius: ${0};
`), (e => e.isNavbar || e.isMaintenancePage ? "translateY(-1px);" : "translateY(-100%)"), (e => e.isUiRTL ? "left: 12px;" : "right: 12px;"), (e => e.isGlobal ? "1px solid #fac517" : "1px solid #0e9384"), (e => e.isNavbar ? e.isUiRTL ? "0px 0px 4px 0px" : "0px 0px 0px 4px" : e.isUiRTL ? "0px 4px 0px 0px" : "4px 0px 0px 0px")),
                de = U().div(oe || (oe = ie `
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border: none;
  padding-inline: 5px;
  font-size: 10px;
  font-weight: 600;
  gap: 4px;

  ${0}

  ${0}
  

  button,
  button:hover {
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
  }
  .section-name {
    text-transform: capitalize;
  }
`), (e => e.isGlobal ? "background-color: #fac515;" : "background-color: #0e9384;"), (e => e.isGlobal ? "color: black;" : "color: white;")),
                ue = U().div(ae || (ae = ie `
  padding-inline: 5px;
  font-size: 10px;
  gap: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  ${0};

  button,
  button:hover {
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
  }
`), (({
                    theme: e,
                    isGlobal: n
                }) => e.rtl ? `border-right: solid 1px ${n?"#fac515":"#0e9384"};` : `border-left: solid 1px ${n?"#fac515":"#0e9384"};`)),
                ce = {
                    border: "3px solid rgba(0,0,0,.1)",
                    minHeight: 400,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center"
                },
                pe = ({
                    category: e,
                    designId: n
                }) => (0, w.tZ)("div", {
                    style: ce,
                    children: (0, w.BX)("div", {
                        style: {
                            textAlign: "center"
                        },
                        children: [(0, w.tZ)("h4", {
                            children: "Section Design Not Found"
                        }), e && (0, w.BX)("span", {
                            children: ["For", " ", (0, w.BX)("b", {
                                children: [e, n && `#${n}`]
                            })]
                        })]
                    })
                });
            var ge = t(15878);
            class he extends i().Component {
                constructor(e) {
                    super(e), this.state = {
                        hasError: !1
                    }
                }
                static getDerivedStateFromError() {
                    return {
                        hasError: !0
                    }
                }
                componentDidCatch(e, n) {
                    this.context.pushError(e, Object.assign({}, n, this.props.options))
                }
                render() {
                    return this.state.hasError ? this.props.fallback : this.props.children
                }
            }
            he.contextType = d.TrackingContext;
            const ye = he;
            var me = t(34080);
            const ve = e => (0, w.tZ)(_.Icon, Object.assign({
                    viewBox: "0 0 58 72"
                }, e, {
                    children: (0, w.BX)("g", {
                        fill: "currentColor",
                        fillRule: "evenodd",
                        children: [(0, w.tZ)("path", {
                            fillRule: "nonzero",
                            d: "M56.19 54.438l-8.83-2.79a2.328 2.328 0 0 0-1.403 0l-8.127 2.568-8.126-2.568a2.328 2.328 0 0 0-1.404 0l-8.127 2.568-8.128-2.568a2.328 2.328 0 0 0-1.404 0l-8.83 2.79a2.325 2.325 0 0 0-1.625 2.217v13.02A2.326 2.326 0 0 0 2.514 72h52.973a2.326 2.326 0 0 0 2.327-2.325v-13.02c0-1.015-.657-1.911-1.625-2.217zM53.16 67.35H4.84v-8.992l6.502-2.055 8.128 2.568c.457.145.947.145 1.404 0L29 56.303l8.127 2.568c.457.145.947.145 1.404 0l8.126-2.568 6.501 2.055v8.992zM1.138 47.021a2.33 2.33 0 0 0 2.077.341l8.128-2.568 8.128 2.568c.457.145.947.145 1.404 0L29 44.794l8.127 2.568c.457.145.947.145 1.404 0l8.126-2.568 8.127 2.568a2.328 2.328 0 0 0 3.03-2.217V2.325A2.326 2.326 0 0 0 55.486 0H18.664a2.33 2.33 0 0 0-1.646.681L.868 16.814a2.325 2.325 0 0 0-.682 1.644v26.687c0 .742.354 1.438.952 1.876zM16.336 7.938v8.195H8.133l8.203-8.195zM4.842 20.783h13.822a2.326 2.326 0 0 0 2.327-2.325V4.65H53.16v37.321l-5.8-1.832a2.328 2.328 0 0 0-1.403 0l-8.127 2.568-8.126-2.568a2.328 2.328 0 0 0-1.404 0l-8.127 2.568-8.128-2.568a2.326 2.326 0 0 0-1.403 0l-5.8 1.832V20.783z"
                        }), (0, w.tZ)("path", {
                            d: "M50.865 28.405a2.326 2.326 0 0 0-2.327-2.325H10.134a2.326 2.326 0 1 0 0 4.65h38.404a2.327 2.327 0 0 0 2.327-2.325zM29.336 19.221h19.202a2.326 2.326 0 1 0 0-4.65H29.336a2.326 2.326 0 1 0 0 4.65z"
                        })]
                    })
                })),
                be = "SectionErrorComponent_button__1l6qc";
            var fe = t(64306);
            const xe = (0, l.connect)(null, {
                    fixSectionErrors: A.Mk
                })((function(e) {
                    const {
                        sectionId: n,
                        fixSectionErrors: t,
                        resetError: l
                    } = e, o = (0, Z.useIntl)(), {
                        open: i
                    } = (0, fe._)(), [r] = o.locale.split("-"), s = `ui${(0,Y.w)(r,"capitalized")}`, d = `ui${r.toUpperCase()}`, u = (0, a.useCallback)((() => {
                        t(n, l)
                    }), [t]), c = (0, a.useCallback)((() => {
                        i()
                    }), []);
                    return (0, w.tZ)("div", {
                        className: h()(s, d, "SectionErrorComponent_container__1KIZD"),
                        children: (0, w.BX)("div", {
                            className: "SectionErrorComponent_innerContainer__RvoZ0",
                            children: [(0, w.tZ)(ve, {
                                size: 72
                            }), (0, w.tZ)("h1", {
                                className: "SectionErrorComponent_title__5cc8s",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "/WMCDd",
                                    defaultMessage: "Something went wrong!"
                                })
                            }), (0, w.tZ)("h2", {
                                className: "SectionErrorComponent_description__KQMQ-",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "FpveFv",
                                    defaultMessage: "We have been notified of this issue."
                                })
                            }), (0, w.tZ)(me.Z, {
                                onClick: u,
                                className: be,
                                theme: "fullWhite",
                                height: "large",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "8J8S0e",
                                    defaultMessage: "FIX IT NOW"
                                })
                            }), (0, w.tZ)(me.Z, {
                                onClick: c,
                                className: be,
                                theme: "emptyWhite",
                                height: "large",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    id: "PjYk7+",
                                    defaultMessage: "CONTACT SUPPORT"
                                })
                            })]
                        })
                    })
                })),
                Se = ["boundaryType", "children"],
                Ze = "section",
                _e = "site",
                Ce = {
                    [Ze]: xe,
                    [_e]: function() {
                        const e = (0, Z.useIntl)(),
                            {
                                open: n
                            } = (0, fe._)(),
                            [t] = e.locale.split("-"),
                            l = `ui${(0,Y.w)(t,"capitalized")}`,
                            o = `ui${t.toUpperCase()}`,
                            i = (0, a.useCallback)((() => {
                                n()
                            }), []);
                        return (0, w.tZ)("div", {
                            className: h()(l, o, "SiteErrorComponent_container__jBEhX"),
                            children: (0, w.BX)("div", {
                                className: "SiteErrorComponent_innerContainer__QbaBT",
                                children: [(0, w.tZ)(ve, {
                                    size: 72
                                }), (0, w.tZ)("h1", {
                                    className: "SiteErrorComponent_title__zFOBJ",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "/WMCDd",
                                        defaultMessage: "Something went wrong!"
                                    })
                                }), (0, w.tZ)("h2", {
                                    className: "SiteErrorComponent_description__6KQpM",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "FpveFv",
                                        defaultMessage: "We have been notified of this issue."
                                    })
                                }), (0, w.tZ)(me.Z, {
                                    onClick: i,
                                    className: "SiteErrorComponent_button__aNEvl",
                                    theme: "fullWhite",
                                    height: "large",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        id: "PjYk7+",
                                        defaultMessage: "CONTACT SUPPORT"
                                    })
                                })]
                            })
                        })
                    }
                },
                Be = {
                    context: "Sections Error"
                },
                we = function(e) {
                    const {
                        boundaryType: n,
                        children: t
                    } = e, l = (0, ge.Z)(e, Se), o = Ce[n], a = o ? (0, w.tZ)(o, Object.assign({}, l)) : null;
                    return (0, w.tZ)(ye, {
                        fallback: a,
                        options: Be,
                        children: t
                    })
                };
            var $e = t(91983),
                Me = t(29449),
                ke = t.n(Me),
                Ie = t(48251);
            t.n(Ie)().init();
            const Le = function({
                    animations: e,
                    children: n,
                    isHero: t,
                    section: o
                }) {
                    var i, r, s;
                    const {
                        active: d
                    } = e, u = (0, l.useSelector)((e => {
                        var n;
                        return null == V.wl || null == (n = V.wl.get(e)) || null == (n = n.themes) || null == (n = n.active) ? void 0 : n.theme
                    })), c = null == u || null == (i = u.content) ? void 0 : i.animation, p = (0, a.useRef)(null), g = (0, a.useRef)(), h = (0, a.useCallback)((() => {
                        g.current = setTimeout((() => {
                            p.current.getBoundingClientRect().top <= (window.innerHeight || document.documentElement.clientHeight) && (p.current.className = "aos-init aos-animate")
                        }), 1e3)
                    }), []), y = (0, a.useCallback)((() => {
                        g.current && clearTimeout(g.current)
                    }), []);
                    (0, a.useEffect)((() => (p.current.className = "aos-init", h(), y)), [null == d ? void 0 : d.name, null == c ? void 0 : c.name]);
                    const m = null == o || null == (r = o.section_settings) ? void 0 : r.background,
                        v = {
                            backgroundImage: !t && !(null == m || !m.image) && `linear-gradient(${z(null==m?void 0:m.overlay)},${z(null==m?void 0:m.overlay)}),url(${null==m?void 0:m.image})`,
                            backgroundColor: !t && `${null==m?void 0:m.color}`,
                            backgroundPosition: "center center",
                            backgroundSize: "cover"
                        },
                        b = {
                            "--text-color": function(e) {
                                const n = ke()(e);
                                return n.isValid() ? n.isLight() ? "black" : "white" : "black"
                            }((null == m ? void 0 : m.color) || z(null == m ? void 0 : m.overlay))
                        },
                        f = (null == o || null == (s = o.section_design) ? void 0 : s.includes("custom_section")) ? b : Object.assign({}, b, v);
                    return (0, w.tZ)("div", {
                        ref: p,
                        "data-aos": u ? null == c ? void 0 : c.name : null == d ? void 0 : d.name,
                        "data-aos-offset": "100",
                        "data-aos-delay": "50",
                        "data-aos-duration": "1000",
                        "data-aos-easing": "ease-in-out-cubic",
                        "data-aos-mirror": "true",
                        "data-aos-once": "true",
                        style: f,
                        children: n
                    })
                },
                Te = "https://assets.wuiltsite.com/defaults/default-logo.svg",
                Fe = "medium",
                De = (0, m.injectSectionElements)((0, l.connect)((() => {
                    const e = (0, $e.sR)();
                    return (n, t) => ({
                        navbarSection: e(n, {
                            sectionId: t.universals.sections.header_expanded[t.locale],
                            mode: t.mode
                        }),
                        animations: n.site.data.styles.animations,
                        globalNavbarSettings: n.site.data.globalNavbarSettings.navbarSettings
                    })
                }))((function(e) {
                    var n, t, l;
                    const {
                        mode: o,
                        locale: r,
                        uiLocale: s,
                        navbarSection: d,
                        universals: u,
                        animations: c,
                        globalNavbarSettings: p,
                        editSectionEventHandlers: g,
                        spacer: h = !1
                    } = e, [y, b] = (0, a.useState)(!1), {
                        style: f,
                        sticky: S,
                        colors: Z
                    } = p, _ = !(null == p || null == (n = p.style) || !n.isTransparent), C = !(null == p || null == (t = p.style) || !t.isSticky), B = null == u ? void 0 : u.data, [$, M] = (null == d || null == (l = d.section_design) ? void 0 : l.split("#")) || [], k = (0, a.useMemo)((() => {
                        const e = (0, v.getSectionDesign)($, M);
                        if (e) {
                            const {
                                component: n,
                                styles: t,
                                meta: l
                            } = e;
                            return {
                                SectionComponent: (0, m.injectSectionElements)(n),
                                styles: t,
                                meta: l
                            }
                        }
                        return {
                            SectionComponent: () => (0, w.tZ)(pe, {
                                category: $,
                                designId: M
                            }),
                            styles: {
                                toString: () => "",
                                locals: {}
                            },
                            meta: {}
                        }
                    }), [null == d ? void 0 : d.section_design]), {
                        SectionComponent: I,
                        styles: L,
                        meta: T
                    } = k, F = x(o, T, null == d ? void 0 : d.section_value, B, r);
                    let D, N;
                    var O, z, E, A, j, R, P, U, G, X, H;
                    y && null != f && f.isSticky && null != S && S.isAlternateLogo && "empty" !== (null == S ? void 0 : S.stickyLogoUrl) ? (D = null == S ? void 0 : S.stickyLogoUrl, N = null == S ? void 0 : S.stickyLogoSize) : "edit" === o ? (D = null != (O = null != (z = null == F || null == (E = F.logo) || null == (E = E.value) ? void 0 : E.url) ? z : null == F || null == (A = F.logo) ? void 0 : A.value) ? O : Te, N = null != (j = null == F || null == (R = F.logo) || null == (R = R.value) ? void 0 : R.size) ? j : Fe) : (D = null != (P = null != (U = null == F || null == (G = F.logo) ? void 0 : G.url) ? U : null == F ? void 0 : F.logo) ? P : Te, N = null != (X = null == F || null == (H = F.logo) ? void 0 : H.size) ? X : Fe);
                    const W = {
                            size: N,
                            url: D
                        },
                        K = C && y,
                        V = {
                            content: "edit" === o ? Object.assign({}, null == F ? void 0 : F.logo, {
                                value: W,
                                isLogoControlsDisabled: K
                            }) : Object.assign({}, null == F ? void 0 : F.logo, W)
                        },
                        Q = () => {
                            b(window.pageYOffset > 0)
                        };
                    (0, a.useEffect)((() => (window.addEventListener("scroll", Q), () => {
                        window.removeEventListener("scroll", Q)
                    })));
                    let q = {
                        width: "100%"
                    };
                    const J = {
                        globalNavbarSettings: {
                            backgroundColor: "",
                            navLinksColor: ""
                        }
                    };
                    C && y ? (q = _ ? Object.assign({}, q, {
                        zIndex: "9999",
                        position: "fixed",
                        top: 0
                    }) : Object.assign({}, q, {
                        zIndex: "9999",
                        position: "sticky",
                        top: 0
                    }), J.globalNavbarSettings = {
                        backgroundColor: null == S ? void 0 : S.backgroundColor,
                        navLinksColor: null == S ? void 0 : S.navLinksColor
                    }) : (_ && (q = Object.assign({}, q, {
                        position: "absolute",
                        zIndex: "9999"
                    })), J.globalNavbarSettings = {
                        backgroundColor: _ ? "transparent" : null == Z ? void 0 : Z.backgroundColor,
                        navLinksColor: null == Z ? void 0 : Z.navLinksColor
                    });
                    const [ee, ne] = i().useState(!1);
                    if (h) return (0, w.tZ)(I, Object.assign({
                        classes: L
                    }, F, {
                        logo: V.content,
                        globalNavbarSettings: {
                            backgroundColor: "#fff",
                            navLinksColor: "#000"
                        }
                    }));
                    const te = (0, w.tZ)("div", {
                        id: "navbar-wrapper-container",
                        style: "view" === o ? q : null,
                        children: (0, w.tZ)(Le, {
                            animations: c,
                            isHero: !0,
                            children: (0, w.tZ)(I, Object.assign({
                                classes: L
                            }, F, {
                                logo: V.content
                            }, J))
                        })
                    });
                    return "edit" === o ? (0, w.tZ)(qe, {
                        isFirst: !0,
                        isNavbar: !0,
                        isLast: !1,
                        uiLocale: s,
                        section: d,
                        navbarStyles: q,
                        uiDirection: (0, Y.w)(s),
                        isSectionInfoModalOpened: ee,
                        onOpenSectionInfoModal: function() {
                            ne(!0)
                        },
                        onCloseSectionInfoModal: function() {
                            ne(!1)
                        },
                        onSelectPreviousSection: function() {
                            g.onPrevSectionDesign(null == d ? void 0 : d.id)
                        },
                        onSelectNextSection: function() {
                            g.onNextSectionDesign(null == d ? void 0 : d.id)
                        },
                        onAddSectionAfter: function() {
                            g.onRequestAddSectionAfter(null == d ? void 0 : d.id)
                        },
                        children: (0, w.tZ)(we, {
                            section: d,
                            boundaryType: Ze,
                            sectionId: d.id,
                            siteId: d.siteId,
                            children: te
                        })
                    }) : (0, w.tZ)(we, {
                        section: d,
                        boundaryType: Ze,
                        sectionId: d.id,
                        siteId: d.siteId,
                        children: te
                    })
                })));
            var Ne = t(34062),
                Oe = t(66088);
            const ze = {
                    fontWeight: "bold",
                    fontSize: "1.4rem"
                },
                Ee = {
                    info: (0, w.tZ)(_.Eye, {
                        size: "1.2rem"
                    }),
                    delete: (0, w.tZ)(_.Trash, {
                        size: "1.2rem"
                    }),
                    add: (0, w.tZ)(_.Add, {
                        size: "1.2rem"
                    }),
                    down: (0, w.tZ)(_.ArrowDown, {
                        size: "1.2rem"
                    }),
                    up: (0, w.tZ)(_.ArrowUp, {
                        size: "1.2rem"
                    }),
                    left: (0, w.tZ)(_.ArrowLeft, {
                        size: "1.1rem",
                        style: ze
                    }),
                    right: (0, w.tZ)(_.ArrowRight, {
                        size: "1.1rem",
                        style: ze
                    }),
                    imageStack: (0, w.tZ)(_.ImageStack, {
                        size: "1.5rem"
                    }),
                    settings: (0, w.tZ)(_.Gear, {
                        size: "1.5rem"
                    })
                },
                Ae = ({
                    isNavbar: e = !1,
                    isActive: n,
                    isUniversal: t,
                    isFirst: o,
                    isLast: a,
                    onNextDesign: i,
                    onPrevDesign: r,
                    onMoveUp: s,
                    onMoveDown: d,
                    onAdd: u,
                    uiDirection: c,
                    uiLocale: p,
                    sectionId: g,
                    section: y,
                    isBelowGlobalTransparentNavbar: m,
                    locale: v,
                    universals: b,
                    siteId: f,
                    onOpenSectionInfoModal: x,
                    onOpenDuplicateSectionModal: S,
                    onOpenDeleteSectionModal: _
                }) => {
                    var C, B, $;
                    const L = (0, l.useSelector)(Ne.zE),
                        T = (0, l.useSelector)((e => (0, Ne.D0)(e, f))),
                        F = null == T || null == (C = T.plan) ? void 0 : C.id,
                        D = 1 === F || 15 === F || 18 === F,
                        N = (null == L ? void 0 : L.type) === Oe.x.NOT_FOUND_PAGE,
                        O = (null == L ? void 0 : L.type) === Oe.x.UNDER_MAINTENANCE_PAGE,
                        z = (O || N) && D,
                        E = (0, Z.useIntl)(),
                        A = "rtl" === c,
                        j = null == y || null == (B = y.section_design) ? void 0 : B.startsWith("custom_section"),
                        R = null == y || null == ($ = y.section_design) ? void 0 : $.startsWith("fluid_section"),
                        P = a && t,
                        U = P || e;
                    return (0, w.BX)("div", {
                        className: h()(I.SectionControlsOuterContainer, {
                            [I["--global"]]: U,
                            [I["--active"]]: n,
                            [I["--unClickable"]]: !0
                        }),
                        children: [m && b ? (0, w.tZ)("div", {
                            className: I.spacer,
                            children: (0, w.tZ)(De, {
                                spacer: !0,
                                locale: v,
                                uiLocale: p,
                                universals: b,
                                editSectionEventHandlers: {}
                            }, "sec-navbar-spacer")
                        }) : null, (0, w.BX)("div", {
                            className: h()("ui", I.SectionControls, {
                                [I["--active"]]: n,
                                [I["--global"]]: U
                            }),
                            children: [!z && (0, w.tZ)(re, {
                                sectionId: g,
                                onNextDesign: i,
                                onPrevDesign: r,
                                isUiRTL: A,
                                isFirst: o,
                                isLast: a,
                                section: y,
                                isNavbar: e,
                                onMoveUp: s,
                                onMoveDown: d,
                                isUniversal: t,
                                isFooter: P,
                                isCustomSection: j,
                                maintenancePageOrErrorPage: O || N,
                                isMaintenancePage: O,
                                isFluidSection: R,
                                onOpenDuplicateSectionModal: S,
                                onOpenDeleteSectionModal: _,
                                onOpenSectionInfoModal: x
                            }), (!(a && t) || o) && !N && !O && n && (0, w.tZ)(M, {
                                uiDirection: c,
                                uiLocale: p,
                                expandTo: "center",
                                iconTo: "left",
                                className: I.addButton,
                                width: "150px",
                                onClick: u,
                                text: E.formatMessage(k.addSection),
                                backgroundColor: "#0e9384",
                                textColor: "#fff",
                                icon: Ee.add
                            }), !e && !j && !R && (0, w.BX)("div", {
                                className: I.bottomControls,
                                children: [(0, w.tZ)(M, {
                                    expanded: !0,
                                    uiDirection: c,
                                    uiLocale: p,
                                    iconTo: "left",
                                    text: E.formatMessage(k.prevDesign),
                                    width: "190px",
                                    icon: A ? Ee.right : Ee.left,
                                    onClick: r
                                }), (0, w.tZ)("div", {
                                    className: I.spaceBetween
                                }), (0, w.tZ)(M, {
                                    expanded: !0,
                                    uiDirection: c,
                                    uiLocale: p,
                                    expandTo: "right",
                                    className: I.nextButton,
                                    width: "180px",
                                    onClick: i,
                                    icon: A ? Ee.left : Ee.right,
                                    text: E.formatMessage(k.nextDesign)
                                })]
                            })]
                        })]
                    })
                },
                je = ({
                    section: e,
                    onClose: n
                }) => (0, w.BX)(L.Modal, {
                    show: !0,
                    onClose: n,
                    modalWidth: "medium",
                    id: "outer",
                    children: [(0, w.tZ)(L.Modal.Header, {
                        splitHeader: !1,
                        children: (0, w.tZ)(L.Stack, {
                            spacing: "tight",
                            children: (0, w.tZ)(L.Heading, {
                                fontSize: "lg",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    defaultMessage: "Section Info",
                                    id: "tLYngi"
                                })
                            })
                        })
                    }), (0, w.tZ)(L.Modal.Body, {
                        children: (0, w.tZ)("textarea", {
                            dir: "ltr",
                            style: {
                                width: "100%",
                                height: 500,
                                fontFamily: "monospace"
                            },
                            rows: 90,
                            value: JSON.stringify(e, null, 2)
                        })
                    })]
                });
            var Re = t(2547);
            const Pe = ["children"],
                Ue = ["hideCloseButton", "children"],
                Ge = ["children"],
                Xe = ["children"],
                He = e => {
                    let {
                        children: n
                    } = e, t = (0, ge.Z)(e, Pe);
                    return (0, w.BX)(Re.Modal, Object.assign({
                        isCentered: !0
                    }, t, {
                        children: [(0, w.tZ)(Re.ModalOverlay, {
                            bg: "#101828c9",
                            backdropFilter: "blur(8px)"
                        }), (0, w.tZ)(Re.ModalContent, {
                            children: n
                        })]
                    }))
                };
            He.Header = e => {
                let {
                    hideCloseButton: n = !1,
                    children: t
                } = e, l = (0, ge.Z)(e, Ue);
                return (0, w.tZ)(Re.ModalHeader, Object.assign({
                    padding: "20px 20px 16px"
                }, l, {
                    children: (0, w.BX)(Re.HStack, {
                        width: "full",
                        alignItems: "start",
                        justifyContent: "space-between",
                        children: [t, !n && (0, w.tZ)(Re.ModalCloseButton, {
                            top: "16px",
                            position: "static"
                        })]
                    })
                }))
            }, He.Body = e => {
                let {
                    children: n
                } = e, t = (0, ge.Z)(e, Ge);
                return (0, w.tZ)(Re.ModalBody, Object.assign({
                    p: "16px 20px"
                }, t, {
                    children: n
                }))
            }, He.Footer = e => {
                let {
                    children: n
                } = e, t = (0, ge.Z)(e, Xe);
                return (0, w.tZ)(Re.ModalFooter, Object.assign({
                    p: "20px"
                }, t, {
                    children: n
                }))
            };
            const We = ["onDeleteSection"],
                Ke = e => {
                    let {
                        onDeleteSection: n
                    } = e, t = (0, ge.Z)(e, We);
                    return (0, w.BX)(He, Object.assign({
                        size: "sm"
                    }, t, {
                        children: [(0, w.tZ)(He.Header, {
                            borderBottom: "1px solid var(--chakra-colors-gray-200)",
                            children: (0, w.tZ)(Re.Text, {
                                variant: "textMd",
                                fontWeight: "semibold",
                                color: "gray.800",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    defaultMessage: "Delete section?",
                                    id: "ePICzf"
                                })
                            })
                        }), (0, w.tZ)(He.Body, {
                            children: (0, w.tZ)(Re.Text, {
                                variant: "textSm",
                                color: "gray.600",
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    defaultMessage: "Are you sure you want to delete this section? This action cannot be undone.",
                                    id: "rid0j2"
                                })
                            })
                        }), (0, w.BX)(He.Footer, {
                            gap: "12px",
                            justifyContent: "space-between",
                            borderTop: "1px solid var(--chakra-colors-gray-200)",
                            children: [(0, w.tZ)(Re.Button, {
                                size: "lg",
                                width: "full",
                                variant: "secondaryGray",
                                onClick: t.onClose,
                                children: (0, w.tZ)(Re.Text, {
                                    variant: "textMd",
                                    fontWeight: "semibold",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Cancel",
                                        id: "47FYwb"
                                    })
                                })
                            }), (0, w.tZ)(Re.Button, {
                                size: "lg",
                                width: "full",
                                type: "submit",
                                variant: "errorPrimary",
                                onClick: n,
                                children: (0, w.tZ)(Re.Text, {
                                    variant: "textMd",
                                    fontWeight: "semibold",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Delete",
                                        id: "K3r6DQ"
                                    })
                                })
                            })]
                        })]
                    }))
                },
                Ve = ({
                    section: e,
                    isOpen: n,
                    onClose: t,
                    onDuplicate: o
                }) => {
                    const {
                        pushEvent: i
                    } = (0, d.useTracking)(), r = (0, l.useSelector)((e => (0, Ne.gw)(e))), [s, u] = (0, a.useState)();
                    return (0, w.BX)(L.Modal, {
                        show: n,
                        onClose: t,
                        modalWidth: "small",
                        children: [(0, w.tZ)(L.Modal.Header, {
                            splitHeader: !1,
                            children: (0, w.BX)(L.Stack, {
                                spacing: "tight",
                                children: [(0, w.tZ)(L.Heading, {
                                    fontSize: "md2",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Copy section to another page",
                                        id: "q8yQZh"
                                    })
                                }), (0, w.tZ)(L.Text, {
                                    fontSize: "sm",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Place a copy of this section on any page of your site",
                                        id: "i7+OeK"
                                    })
                                }), (0, w.tZ)(L.Text, {
                                    py: "5px",
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "<span>Undo/redo</span> doesn’t support copy section yet.",
                                        id: "wzKSSY",
                                        values: {
                                            span: (...e) => (0, w.tZ)("span", {
                                                style: {
                                                    fontWeight: "bold"
                                                },
                                                children: e
                                            })
                                        }
                                    })
                                })]
                            })
                        }), (0, w.BX)(L.Modal.Body, {
                            pt: "0px",
                            children: [(0, w.tZ)(L.Label, {
                                children: (0, w.tZ)(Z.FormattedMessage, {
                                    defaultMessage: "Pages",
                                    id: "CxfKLC"
                                })
                            }), (0, w.tZ)(L.Select, {
                                placeholder: (0, w.tZ)(Z.FormattedMessage, {
                                    defaultMessage: "Select page",
                                    id: "rvz2nM"
                                }),
                                options: null == r || null == r.map ? void 0 : r.map((e => ({
                                    value: null == e ? void 0 : e.id,
                                    label: null == e ? void 0 : e.name
                                }))),
                                onChange: e => {
                                    const n = null == e ? void 0 : e.map((e => null == e ? void 0 : e.value));
                                    u(n)
                                },
                                isMulti: !0
                            }), (0, w.BX)(L.Stack, {
                                mt: "20px",
                                direction: "row",
                                justify: "end",
                                children: [(0, w.tZ)(L.Button, {
                                    color: "white",
                                    onClick: t,
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Cancel",
                                        id: "47FYwb"
                                    })
                                }), (0, w.tZ)(L.Button, {
                                    onClick: () => {
                                        var n;
                                        o(s), i("Site section copied", {
                                            section_name: null == e || null == (n = e.section_design) ? void 0 : n.split("#")[0]
                                        }), t()
                                    },
                                    children: (0, w.tZ)(Z.FormattedMessage, {
                                        defaultMessage: "Copy to page",
                                        id: "gCWaqa"
                                    })
                                })]
                            })]
                        })]
                    })
                },
                Ye = (0, a.memo)(Ve),
                Qe = () => {
                    const {
                        env: e,
                        stage: n
                    } = (0, S.h)();
                    return "development" === e || "canary" === n || "staging" === n
                },
                qe = ({
                    locale: e,
                    siteId: n,
                    section: t,
                    isFirst: l,
                    isLast: o,
                    isNavbar: i,
                    uiLocale: r,
                    uiDirection: s,
                    children: d,
                    universals: u,
                    navbarStyles: c,
                    isGlobalNavbarTransparent: p,
                    isSectionInfoModalOpened: g,
                    isDuplicateSectionModalOpened: h,
                    isDeleteSectionModalOpened: y,
                    onOpenSectionInfoModal: m,
                    onCloseSectionInfoModal: v,
                    onOpenDuplicateSectionModal: b,
                    onCloseDuplicateSectionModal: f,
                    onOpenDeleteSectionModal: x,
                    onCloseDeleteSectionModal: S,
                    onSelectPreviousSection: Z,
                    onSelectNextSection: _,
                    onAddSectionAfter: C,
                    onDuplicateSection: B,
                    onMoveSectionUp: $,
                    onMoveSectionDown: M,
                    onDeleteSection: k
                }) => {
                    const [I, L] = (0, a.useState)(!1), T = () => {
                        L(!0)
                    };
                    return (0, w.BX)("div", {
                        id: null == t ? void 0 : t.id,
                        onMouseEnter: T,
                        onMouseLeave: () => {
                            L(!1)
                        },
                        onMouseOver: T,
                        style: Object.assign({
                            position: "relative"
                        }, c),
                        children: [d, h && (0, w.tZ)(Ye, {
                            section: t,
                            isOpen: h,
                            onClose: f,
                            onDuplicate: B
                        }), y && (0, w.tZ)(Ke, {
                            onClose: S,
                            isOpen: y,
                            onDeleteSection: k
                        }), g && (0, w.tZ)(je, {
                            section: t,
                            onClose: v
                        }), (0, w.tZ)(Ae, {
                            locale: e,
                            universals: u,
                            isNavbar: i,
                            uiLocale: r,
                            uiDirection: s,
                            isFirst: l,
                            isLast: o,
                            isUniversal: t.universal,
                            isActive: I,
                            onNextDesign: _,
                            onPrevDesign: Z,
                            onMoveUp: $,
                            onMoveDown: M,
                            onAdd: C,
                            sectionId: null == t ? void 0 : t.id,
                            section: t,
                            isBelowGlobalTransparentNavbar: l && !i && !!p,
                            siteId: n,
                            onOpenSectionInfoModal: Qe() ? m : void 0,
                            onOpenDuplicateSectionModal: b,
                            onOpenDeleteSectionModal: x
                        })]
                    })
                },
                Je = (0, u.createSelector)([e => e.site.data.globalNavbarSettings], (e => e.navbarSettings.style.isTransparent)),
                en = (0, m.injectSectionElements)((function(e) {
                    var n;
                    const {
                        universals: t,
                        uiLocale: o,
                        isFirst: r,
                        locale: s,
                        isLast: d,
                        editSectionEventHandlers: u,
                        siteId: c,
                        setActiveSectionRef: p
                    } = e, g = (0, $e.sR)(), h = (0, l.useSelector)((n => g(n, e))), y = (0, l.useSelector)(Ne.zE), b = (0, l.useSelector)((e => V.wl.get(e).animations)), f = "embed#0004" === h.section_design, S = null == h || null == (n = h.section_design) ? void 0 : n.startsWith("fluid_section"), Z = (0, l.useSelector)(Je), _ = (0, l.useSelector)(Ne.WC);
                    if ("string" != typeof(null == h ? void 0 : h.section_design)) throw Error(`section ${null==h?void 0:h.id} have invalid section design`);
                    const [C, B] = h.section_design.split("#"), $ = (0, a.useMemo)((() => {
                        const e = (0, v.getSectionDesign)(C, B);
                        if (e) {
                            const {
                                component: n,
                                styles: t,
                                meta: l
                            } = e;
                            return {
                                SectionComponent: (0, m.injectSectionElements)(n),
                                styles: t,
                                meta: l
                            }
                        }
                        return {
                            SectionComponent: () => (0, w.tZ)(pe, {
                                category: C,
                                designId: B
                            }),
                            styles: {
                                toString: () => "",
                                locals: {}
                            },
                            meta: {}
                        }
                    }), [h.section_design]), {
                        SectionComponent: M,
                        styles: k,
                        meta: I
                    } = $, L = t.data, T = x(_, I, h.section_value, L), [F, D] = i().useState(!1), [N, O] = i().useState(!1), {
                        onOpen: z,
                        onClose: E,
                        isOpen: A
                    } = (0, Re.useDisclosure)();

                    function j() {
                        O(!0)
                    }

                    function R() {
                        z()
                    }

                    function P() {
                        u.onMoveSectionUp(h.id)
                    }

                    function U() {
                        u.onMoveSectionDown(h.id)
                    }
                    const G = (0, w.tZ)(w.HY, {
                        children: (0, w.tZ)(we, {
                            boundaryType: Ze,
                            sectionId: h.id,
                            siteId: h.siteId,
                            children: (0, w.tZ)(Le, {
                                animations: b,
                                isHero: !1,
                                section: h,
                                children: (0, w.tZ)("div", {
                                    className: r && Z && !S && !f ? "SectionRenderer_extraPaddingForGlobalNavbar__bvAtQ" : void 0,
                                    children: (0, w.tZ)(M, Object.assign({
                                        classes: k
                                    }, T, {
                                        sectionId: null == h ? void 0 : h.id,
                                        activePage: y,
                                        pageContext: {
                                            props: {
                                                isFirst: r,
                                                isLast: d,
                                                isFirstAndNavTransparent: r && Z
                                            },
                                            actions: "edit" === _ ? {
                                                duplicateSection: j,
                                                moveSectionUp: P,
                                                moveSectionDown: U,
                                                deleteSection: R,
                                                setActiveSectionRef: p
                                            } : {}
                                        }
                                    }))
                                })
                            })
                        })
                    });
                    return "edit" === _ ? (0, w.tZ)(qe, {
                        siteId: c,
                        locale: s,
                        section: h,
                        isFirst: r,
                        isLast: d,
                        uiLocale: o,
                        universals: t,
                        uiDirection: (0, Y.w)(o),
                        isGlobalNavbarTransparent: Z,
                        isSectionInfoModalOpened: F,
                        isDuplicateSectionModalOpened: N,
                        isDeleteSectionModalOpened: A,
                        onOpenSectionInfoModal: function() {
                            D(!0)
                        },
                        onCloseSectionInfoModal: function() {
                            D(!1)
                        },
                        onOpenDuplicateSectionModal: j,
                        onCloseDuplicateSectionModal: function() {
                            O(!1)
                        },
                        onOpenDeleteSectionModal: R,
                        onCloseDeleteSectionModal: function() {
                            E()
                        },
                        onSelectPreviousSection: function() {
                            u.onPrevSectionDesign(h.id)
                        },
                        onSelectNextSection: function() {
                            u.onNextSectionDesign(h.id)
                        },
                        onAddSectionAfter: function() {
                            u.onRequestAddSectionAfter(h.id)
                        },
                        onMoveSectionUp: P,
                        onMoveSectionDown: U,
                        onDeleteSection: function() {
                            u.onDeleteSection(h.id), E()
                        },
                        onDuplicateSection: function(e) {
                            u.onCloneSection(e, null == h ? void 0 : h.id, I)
                        },
                        children: G
                    }) : G
                })),
                nn = function(e) {
                    const {
                        pageId: n
                    } = e;
                    return (0, a.useEffect)((() => {
                        window.scrollTo(0, 0)
                    }), [n]), null
                };
            var tn = t(31408),
                ln = t(1445);
            const on = window.parent._w && window.parent._w.store || ln.Z;
            var an = t(2530),
                rn = t(19869),
                sn = t(58116),
                dn = t(56693),
                un = t(66001);
            const cn = function({
                    code: e,
                    type: n
                }) {
                    const t = (0, l.useSelector)(Ne.WC);
                    return (0, a.useEffect)((() => {
                        if ("edit" === t) return;
                        const l = document.createRange().createContextualFragment(e);
                        e && ("body" === n ? document.body.appendChild(l) : "head" === n && document.head.appendChild(l))
                    }), [t, e, n]), (0, w.tZ)(w.HY, {})
                },
                pn = function(e) {
                    const {
                        siteId: n,
                        pageData: t,
                        locale: o,
                        uiLocale: i,
                        universals: r,
                        siteData: s
                    } = e, u = (null == t ? void 0 : t.type) === Oe.x.UNDER_MAINTENANCE_PAGE, c = s.siteSettings.website_settings.find((e => "HEAD_CODE" === e.alias)), g = s.siteSettings.website_settings.find((e => "BODY_CODE" === e.alias)), m = t.page_sections, v = !!m, b = (0, l.useDispatch)(), {
                        pushEvent: f
                    } = (0, d.useTracking)(), [x, S] = (0, a.useState)(null), {
                        breakpoint: Z
                    } = (0, X.useChakraBreakpoints)();

                    function _(e) {
                        e !== x && S(e)
                    }
                    const C = (0, a.useMemo)((() => ({
                        onDeleteSection: function(e) {
                            const n = (0, Ne.ae)(on.getState(), e),
                                {
                                    siteId: t,
                                    pageId: l,
                                    section_design: o,
                                    section_category: a
                                } = n;
                            b((0, A.F$)(t, l, e, o, a)), f("Site section deleted")
                        },
                        onRequestAddSectionAfter: function(e) {
                            b((0, rn.w0)({
                                sectionId: e,
                                control: "ADD"
                            })), b((0, sn.Un)()), b((0, dn.ly)()), b((0, un._M)())
                        },
                        onMoveSectionUp: function(e) {
                            const n = (0, Ne.ae)(on.getState(), e);
                            b((0, y.wZ)(n.siteId, n.pageId, e))
                        },
                        onMoveSectionDown: function(e) {
                            const n = (0, Ne.ae)(on.getState(), e);
                            on.dispatch((0, y.lD)(n.siteId, n.pageId, e))
                        },
                        onNextSectionDesign: function(e) {
                            const n = (0, Ne.ae)(on.getState(), e);
                            let t = (0, an._B)(n.category_id)(on.getState());
                            !n.category_id && n.section_category && (t = (0, an.gk)(n.section_category)(on.getState()));
                            const l = (() => {
                                let e = null;
                                return t.forEach(((l, o) => {
                                    l.design_view === n.section_design && (o < t.length - 1 ? e = t[o + 1] : [e] = t)
                                })), e || ([e] = t), e ? {
                                    view: e.design_view,
                                    id: e.id
                                } : void 0
                            })();
                            l && b((0, A.Cs)(n.id, l.id, l.view))
                        },
                        onPrevSectionDesign: function(e) {
                            const n = (0, Ne.ae)(on.getState(), e);
                            let t = (0, an._B)(n.category_id)(on.getState());
                            !n.category_id && n.section_category && (t = (0, an.gk)(n.section_category)(on.getState()));
                            const l = (() => {
                                let e = null;
                                return t.forEach(((l, o) => {
                                    l.design_view === n.section_design && (e = o > 0 ? t[o - 1] : t[t.length - 1])
                                })), e || ([e] = t), e ? {
                                    view: e.design_view,
                                    id: e.id
                                } : void 0
                            })();
                            l && b((0, A.Cs)(n.id, l.id, l.view))
                        },
                        onCloneSection: function(e, n, t) {
                            const l = (0, Ne.ae)(on.getState(), n),
                                {
                                    siteId: o
                                } = l;
                            on.dispatch((0, A.GV)(o, e, l, t)), f("Site section cloned")
                        }
                    })), []);
                    return (0, a.useEffect)((() => {
                        !v && n && null != t && t.id && b((0, y.ij)(n, null == t ? void 0 : t.id))
                    }), [v]), (0, a.useEffect)((() => {
                        null != t && t.language && b((0, tn.fN)(null == t ? void 0 : t.language))
                    }), [null == t ? void 0 : t.language]), (0, a.useEffect)((() => {
                        x && x.scrollIntoView({
                            behavior: "smooth",
                            block: "start"
                        })
                    }), [Z]), null != t && t.language && null != t && t.page_sections ? (0, w.BX)(w.HY, {
                        children: [(0, w.tZ)(cn, {
                            code: null == c ? void 0 : c.value,
                            type: "head"
                        }), (0, w.tZ)(nn, {
                            pageId: null == t ? void 0 : t.id
                        }), (0, w.BX)(p.Helmet, {
                            children: [(0, w.tZ)("html", {
                                lang: null == t ? void 0 : t.language,
                                className: (0, Y.w)(null == t ? void 0 : t.language)
                            }), (0, w.tZ)("body", {
                                className: h()((0, Y.w)(null == t ? void 0 : t.language), "ar" === i ? "uiRtl" : "uiLtr")
                            }), (0, w.tZ)("style", {
                                type: "text/css",
                                children: `\n        body {\n            direction: ${(0,Y.w)(i)};\n        }\n        body div[id="root"] {\n            direction: ${(0,Y.w)(null==t?void 0:t.language)};\n        }\n    `
                            })]
                        }), !u && (0, w.tZ)(De, {
                            locale: o,
                            uiLocale: i,
                            universals: r,
                            editSectionEventHandlers: C
                        }, `sec-navbar-${o}`), m.map(((e, t) => (0, w.tZ)(en, {
                            locale: o,
                            uiLocale: i,
                            universals: r,
                            sectionId: e,
                            isFirst: 0 === t,
                            isLast: (null == m ? void 0 : m.length) - 1 <= t,
                            editSectionEventHandlers: C,
                            siteId: n,
                            setActiveSectionRef: _
                        }, `sec-${e}`))), !u && (0, w.tZ)(en, {
                            isLast: !0,
                            locale: o,
                            uiLocale: i,
                            universals: r,
                            sectionId: r.sections.footer[o],
                            editSectionEventHandlers: C
                        }, `sec-footer-${o}`), (0, w.tZ)(cn, {
                            code: null == g ? void 0 : g.value,
                            type: "body"
                        })]
                    }) : (0, w.tZ)(d.LoadingScreen, {})
                };
            var gn = t(15733),
                hn = t(98859),
                yn = t(12402),
                mn = t(42022),
                vn = t(9975),
                bn = t(72766);
            const fn = window.parent._w && window.parent._w.history || {
                    navigate: null,
                    location: null
                },
                xn = {
                    onEdit(e, n, t) {
                        switch (t) {
                            case m.ElementsTypes.Text:
                            case m.ElementsTypes.HTMLText:
                                on.dispatch((0, T.sW)(e, n));
                                break;
                            case m.ElementsTypes.SocialLinks:
                                on.dispatch((0, yn.Pj)("socialLinks", {
                                    id: e,
                                    value: n
                                }));
                                break;
                            case m.ElementsTypes.Video:
                                on.dispatch((0, yn.Pj)("VideoControls", {
                                    id: e
                                }));
                                break;
                            case m.ElementsTypes.Map:
                                on.dispatch((0, mn.m7)("map", {
                                    id: e,
                                    value: n
                                }));
                                break;
                            case m.ElementsTypes.Embed:
                                on.dispatch((0, mn.m7)("embed", {
                                    id: e,
                                    value: n
                                }))
                        }
                    },
                    onItemMoveForward(e, n) {
                        on.dispatch((0, hn.Pb)(e, n))
                    },
                    onItemMoveBackward(e, n) {
                        on.dispatch((0, hn.YR)(e, n))
                    },
                    onItemDuplicate(e, n) {
                        on.dispatch((0, gn.vC)(e, n))
                    },
                    onItemDelete(e, n) {
                        const t = e.split("-")[2];
                        on.dispatch((0, gn.Uu)(t, e, n))
                    },
                    onNavToPage(e) {
                        const n = (0, Ne.ck)(on.getState());
                        fn.navigate && fn.navigate(`/site/${n}/edit/${e.id}`)
                    },
                    onChangeLocale(e) {
                        on.dispatch(((n, t) => {
                            const l = (0, Ne.ck)(t());
                            on.dispatch((0, tn.wo)(l, e))
                        }))
                    },
                    onToggleMenuSettings(e) {
                        on.dispatch((0, yn.D2)()), on.dispatch((0, bn.Tu)()), on.dispatch((0, sn.Un)()), on.dispatch((0, un._M)()), e !== vn.og.Footer ? on.dispatch((0, dn.Pn)()) : on.dispatch((0, dn.rA)())
                    },
                    onSortMultiValues(e, n) {
                        on.dispatch((0, hn.FZ)(e, n))
                    },
                    onMutateApi(e, n) {
                        on.dispatch((0, T.sW)(e, n))
                    },
                    onUpdateUi(e, n) {
                        on.dispatch((0, T.NB)(e, n))
                    },
                    onUploadImage(e) {
                        on.dispatch((0, D.pI)(N.Il.EditImage, {
                            updaterCallback: e
                        }))
                    },
                    onAddSlides(e) {
                        on.dispatch((0, D.pI)(N.Il.EditSlideShow, {
                            valueId: e
                        }))
                    }
                },
                Sn = ["page", "children"],
                Zn = window.parent._w && window.parent._w.history || {
                    navigate: () => {},
                    location: () => {}
                },
                _n = "https://api.wuilt.com/";

            function Cn(e, n) {
                if (!e) return;
                const t = (0, Ne.ck)(on.getState()),
                    l = (0, Ne.WC)(on.getState()),
                    o = window.parent.location.origin,
                    a = `/site/${t}/${l}/${e.id}`;
                "_blank" === n ? window.parent.open(`${o}${a}`, "_blank") : Zn.navigate(a)
            }
            const Bn = {
                    Image: e => (0, w.tZ)("img", Object.assign({}, e)),
                    imageLoader: e => e.src,
                    Head: () => null,
                    Link: e => (0, w.tZ)("a", Object.assign({}, e, {
                        target: "_blank",
                        children: e.children
                    })),
                    PageLink: e => {
                        const {
                            page: n,
                            children: t
                        } = e, l = (0, ge.Z)(e, Sn);
                        return (0, w.tZ)("a", Object.assign({}, l, {
                            onClick: e => {
                                e.preventDefault(), Cn(n, null == l ? void 0 : l.target)
                            },
                            children: t || (null == n ? void 0 : n.name) || null
                        }))
                    },
                    useRouter: function() {
                        return {
                            pushPage: (e, n) => Cn({
                                id: e
                            }, n),
                            replacePage: (e, n) => Cn({
                                id: e
                            }, n)
                        }
                    }
                },
                wn = e => {
                    var n;
                    const {
                        siteId: t,
                        appLanguage: l,
                        siteLanguage: o,
                        userEmail: a,
                        pages: i,
                        site: r,
                        editorWindow: s,
                        activeTheme: u
                    } = e;
                    return {
                        getFormSubmitUrl: e => `${_n}${e}/submitv2`,
                        uploadFileUrl: (e, n) => `${_n}${n}/${e}/upload-file-v2`,
                        createFormUrl: `${_n}${t}/create-form-v2`,
                        updateFormUrl: `${_n}${t}/update-website-forms`,
                        deleteFormUrl: () => `${_n}${t}/forms/delete`,
                        siteId: t,
                        baseApiUrl: _n,
                        userEmail: a,
                        appDirection: (0, Y.w)(l),
                        siteDirection: (0, Y.w)(o),
                        fireTrackEvent: d.trackingMethods.pushEvent,
                        pages: i,
                        site: r,
                        editorWindow: s,
                        theme: null == u || null == (n = u.theme) ? void 0 : n.content
                    }
                };
            var $n = t(74826);
            const Mn = {},
                kn = e => {
                    const {
                        siteId: n,
                        mode: t,
                        siteLanguage: l
                    } = e;
                    return Mn[n] || (Mn[n] = {}), Mn[n][l] || (Mn[n][l] = {}), Mn[n][l][t] || (Mn[n][l][t] = (e => "edit" === e.mode ? (e => ({
                        elements: (0, m.editorElementFactory)(xn),
                        mode: "edit",
                        base: Bn,
                        props: wn(e)
                    }))(e) : (e => ({
                        elements: m.defaultElements,
                        mode: "view",
                        base: Bn,
                        props: wn(e)
                    }))(e))(e)), Mn[n][l][t]
                },
                In = function({
                    children: e
                }) {
                    const {
                        user: n
                    } = (0, d.useAuth)(), t = (0, l.useSelector)(Ne.ck), o = (0, l.useSelector)((e => e.window)), {
                        ownerEmail: a
                    } = (0, $n.Z)(), i = (0, l.useSelector)((e => (0, Ne.D0)(e, t))), r = (0, l.useSelector)(Ne.WC), s = (0, l.useSelector)(Ne.E2), u = a || (null == n ? void 0 : n.email), c = (0, l.useSelector)((e => {
                        var n;
                        return null == e || null == (n = e.language) ? void 0 : n.language
                    })), p = (0, l.useSelector)((e => {
                        var n;
                        return null == e || null == (n = e.site) || null == (n = n.data) || null == (n = n.locales) ? void 0 : n.activeLocale
                    })), g = (0, l.useSelector)((e => V.wl.get(e).themes.active));
                    return (0, w.tZ)(m.SectionElementsProvider, {
                        value: kn({
                            mode: r,
                            pages: s,
                            siteId: t,
                            userEmail: u,
                            appLanguage: c,
                            siteLanguage: p,
                            site: i,
                            editorWindow: o,
                            activeTheme: g
                        }),
                        children: e
                    })
                },
                Ln = (0, l.connect)((e => {
                    var n, t;
                    const l = (0, Ne.EY)(e),
                        o = e.site.data.styles;
                    return {
                        colors: o.colors.active.colors,
                        currentFonts: null == o || null == (n = o.fonts) || null == (n = n.active[l]) ? void 0 : n.fonts,
                        allFonts: o.fonts.active,
                        theme: null == o || null == (t = o.themes) || null == (t = t.active) ? void 0 : t.theme
                    }
                }))((function(e) {
                    var n, t, l, o, a, i;
                    const {
                        colors: r,
                        currentFonts: s,
                        allFonts: d
                    } = e, u = Object.keys(d).reduce(((e, n) => {
                        var t, l;
                        const o = d[n].fonts;
                        return `${e}@import url('${null==o||null==(t=o.body_font)?void 0:t.url}');\n@import url('${null==o||null==(l=o.headline_font)?void 0:l.url}');`
                    }), "");
                    return (0, w.BX)(w.HY, {
                        children: [(0, w.tZ)("style", {
                            id: "root-vars",
                            children: `:root {\n            --theme-color-primary: ${r[0]};\n            --theme-color-secondary: ${r[1]};\n            --theme-color-gradient: ${r[3]};\n            --theme-color-text-on-primary: ${r[3]};\n            --theme-color-text-on-dark: ${r[0]};\n            --theme-color-dark: ${r[0]};\n            --theme-color-light: ${r[2]};\n            --theme-button-radius: 8px;\n          }`
                        }), (0, w.tZ)("style", {
                            children: `\n         ${u}\n          body.uiRtl {\n            direction: rtl; \n          }\n          body.uiLtr {\n            direction: ltr;\n          }\n\n          body, body *, p, p *, span, span * {\n\t\t\t\t\t\toverflow: unset;\n              font-family: ${null==s||null==(n=s.body_font)?void 0:n.fontFamily};\n                font-weight: ${null==s||null==(t=s.body_font)?void 0:t.weight};\n                font-style:${null!=s&&null!=(l=s.body_font)&&l.isItalic?"italic":"normal"};\n          }\n\n          h1, h2, h3, h4, h5, h6,\n          h1 *, h2 *, h3 *, h4 *, h5 *, h6 * {\n            font-family: ${null==s||null==(o=s.headline_font)?void 0:o.fontFamily};\n                    font-weight: ${null==s||null==(a=s.headline_font)?void 0:a.weight};\n                    font-style:${null!=s&&null!=(i=s.headline_font)&&i.isItalic?"italic":"normal"};\n          }\n          `
                        })]
                    })
                }));
            var Tn = t(49211),
                Fn = t(25842),
                Dn = t(94678),
                Nn = t(84755);
            const On = () => {
                    var e, n, t, o, a, i, r, s, d, u, c, p, g, h, y, m, v, b, f, x, S, Z, _, C, B, $, M, k, I, L, T, F, D, N, O, z, E, A, j, R, P, U, G, X, H, W, K, V, Y, Q, q, J, ee, ne, te, le, oe, ae, ie, re, se, de, ue, ce, pe, ge, he, ye, me, ve, be, fe, xe, Se, Ze, _e, Ce, Be, we, $e, Me, ke, Ie, Le, Te, Fe, De, Oe, ze, Ee, Ae, je, Re, Pe, Ue, Ge, Xe, He, We, Ke, Ve, Ye, Qe, qe, Je, en, nn, tn, ln, on, an, rn, sn, dn, un, cn, pn, gn, hn, yn, mn, vn, bn, fn, xn, Sn, Zn, _n, Cn, Bn, wn, $n, Mn, kn, In, Ln, On, zn, En, An, jn, Rn, Pn, Un, Gn, Xn, Hn, Wn, Kn, Vn, Yn;
                    const Qn = (0, l.useSelector)((e => Tn.wl.get(e).themes.active.theme.content)),
                        qn = (0, l.useSelector)((e => (0, Ne.EY)(e))),
                        Jn = (0, l.useSelector)((e => {
                            var n;
                            return null == e || null == (n = e.site) || null == (n = n.data) || null == (n = n.locales) ? void 0 : n.locales
                        })).reduce(((e, n) => {
                            var t, l, o, a, i, r, s, d, u, c, p, g, h, y, m;
                            return `${e}@import url('${null==Qn||null==(t=Qn.typography[n])||null==(t=t.headings)||null==(t=t.default)?void 0:t.url}');\n   @import url('${null==Qn||null==(l=Qn.typography[n])||null==(l=l.headings)||null==(l=l.h1)?void 0:l.url}');\n   @import url('${null==Qn||null==(o=Qn.typography[n])||null==(o=o.headings)||null==(o=o.h2)?void 0:o.url}');\n   @import url('${null==Qn||null==(a=Qn.typography[n])||null==(a=a.headings)||null==(a=a.h3)?void 0:a.url}');\n   @import url('${null==Qn||null==(i=Qn.typography[n])||null==(i=i.headings)||null==(i=i.h4)?void 0:i.url}');\n   @import url('${null==Qn||null==(r=Qn.typography[n])||null==(r=r.headings)||null==(r=r.h5)?void 0:r.url}');\n   @import url('${null==Qn||null==(s=Qn.typography[n])||null==(s=s.headings)||null==(s=s.h6)?void 0:s.url}');\n   @import url('${null==Qn||null==(d=Qn.typography[n])||null==(d=d.paragraphs)||null==(d=d.default)?void 0:d.url}');\n   @import url('${null==Qn||null==(u=Qn.typography[n])||null==(u=u.paragraphs)||null==(u=u.p1)?void 0:u.url}');\n   @import url('${null==Qn||null==(c=Qn.typography[n])||null==(c=c.paragraphs)||null==(c=c.p2)?void 0:c.url}');\n   @import url('${null==Qn||null==(p=Qn.typography[n])||null==(p=p.paragraphs)||null==(p=p.p3)?void 0:p.url}');\n  @import url('${null==Qn||null==(g=Qn.typography[n])||null==(g=g.buttons)||null==(g=g.default)?void 0:g.url}');\n  @import url('${null==Qn||null==(h=Qn.typography[n])||null==(h=h.buttons)||null==(h=h.primary)?void 0:h.url}');\n  @import url('${null==Qn||null==(y=Qn.typography[n])||null==(y=y.buttons)||null==(y=y.secondary)?void 0:y.url}');\n  @import url('${null==Qn||null==(m=Qn.typography[n])||null==(m=m.buttons)||null==(m=m.tertiary)?void 0:m.url}');`
                        }), ""),
                        et = .5,
                        nt = .75,
                        tt = .85,
                        lt = .9,
                        ot = null == Qn || null == (e = Qn.typography[qn]) || null == (e = e.headings) || null == (e = e.h1) ? void 0 : e.fontSize,
                        at = null == Qn || null == (n = Qn.typography[qn]) || null == (n = n.headings) || null == (n = n.h2) ? void 0 : n.fontSize,
                        it = null == Qn || null == (t = Qn.typography[qn]) || null == (t = t.headings) || null == (t = t.h3) ? void 0 : t.fontSize,
                        rt = null == Qn || null == (o = Qn.typography[qn]) || null == (o = o.headings) || null == (o = o.h4) ? void 0 : o.fontSize,
                        st = null == Qn || null == (a = Qn.typography[qn]) || null == (a = a.headings) || null == (a = a.h5) ? void 0 : a.fontSize,
                        dt = null == Qn || null == (i = Qn.typography[qn]) || null == (i = i.headings) || null == (i = i.h6) ? void 0 : i.fontSize,
                        ut = null == Qn || null == (r = Qn.typography[qn]) || null == (r = r.paragraphs) || null == (r = r.p1) ? void 0 : r.fontSize,
                        ct = null == Qn || null == (s = Qn.typography[qn]) || null == (s = s.paragraphs) || null == (s = s.p2) ? void 0 : s.fontSize,
                        pt = null == Qn || null == (d = Qn.typography[qn]) || null == (d = d.paragraphs) || null == (d = d.p3) ? void 0 : d.fontSize;
                    return (0, w.BX)(w.HY, {
                        children: [(0, w.tZ)("style", {
                            id: "root-vars",
                            children: `:root {\n        --theme-color-brand1: ${Qn.colors.brand1};\n        --theme-color-brand2: ${Qn.colors.brand2};\n        --theme-color-brand3: ${Qn.colors.brand3};\n        --theme-color-brand4: ${Qn.colors.brand4};\n        --theme-color-secondary1: ${Qn.colors.secondary1};\n        --theme-color-secondary2: ${Qn.colors.secondary2};\n        --theme-color-secondary3: ${Qn.colors.secondary3};\n        --theme-color-secondary4: ${Qn.colors.secondary4};\n        --theme-color-base1: ${Qn.colors.base1};\n        --theme-color-base2: ${Qn.colors.base2};\n        --theme-color-base3: ${Qn.colors.base3};\n        --theme-color-base4: ${Qn.colors.base4};\n        --theme-color-base5: ${Qn.colors.base5};\n        --theme-color-accent1: ${Qn.colors.accent1};\n        --theme-color-accent2: ${Qn.colors.accent2};\n        --white-background-color: white;\n        --theme-transparent-color: transparent;\n        --heading1-font-size:clamp(${ot*nt}px, calc(${ot*nt}px + (${ot} - ${ot*nt}) * ((100vw - 375px) / (1065))), ${ot}px);\n        --heading2-font-size:clamp(${at*nt}px, calc(${at*nt}px + (${at} - ${at*nt}) * ((100vw - 375px) / (1065))), ${at}px);\n        --heading3-font-size:clamp(${it*nt}px, calc(${it*nt}px + (${it} - ${it*nt}) * ((100vw - 375px) / (1065))), ${it}px);\n        --heading4-font-size:clamp(${rt*tt}px, calc(${rt*tt}px + (${rt} - ${rt*tt}) * ((100vw - 375px) / (1065))), ${rt}px);\n        --heading5-font-size:clamp(${st*tt}px, calc(${st*tt}px + (${st} - ${st*tt}) * ((100vw - 375px) / (1065))), ${st}px);\n        --heading6-font-size:clamp(${dt*tt}px, calc(${dt*tt}px + (${dt} - ${dt*tt}) * ((100vw - 375px) / (1065))), ${dt}px);\n        --paragraph1-font-size:clamp(${ut*lt}px, calc(${ut*lt}px + (${ut} - ${ut*lt}) * ((100vw - 375px) / (1065))), ${ut}px);\n        --paragraph2-font-size:clamp(${ct*lt}px, calc(${ct*lt}px + (${ct} - ${ct*lt}) * ((100vw - 375px) / (1065))), ${ct}px);\n        --paragraph3-font-size:clamp(${pt*lt}px, calc(${pt*lt}px + (${pt} - ${pt*lt}) * ((100vw - 375px) / (1065))), ${pt}px);\n        }`
                        }), (0, w.tZ)("style", {
                            children: `\n         ${Jn}\n   #general-text, #general-text * {\n\t\toverflow: unset;\n      font-family: ${null==Qn||null==(u=Qn.typography[qn])||null==(u=u.paragraphs)||null==(u=u.p1)?void 0:u.fontFamily};\n      font-weight: ${null==Qn||null==(c=Qn.typography[qn])||null==(c=c.paragraphs)||null==(c=c.p1)?void 0:c.weight};\n      font-style:  ${null!=Qn&&null!=(p=Qn.typography[qn])&&null!=(p=p.paragraphs)&&null!=(p=p.p1)&&p.isItalic?"italic":"normal"};\n    }\n  #general-button-text, #general-button-text * {\n\t\tfont-family: ${null==Qn||null==(g=Qn.typography[qn])||null==(g=g.buttons)||null==(g=g.primary)?void 0:g.fontFamily};\n    font-weight: ${null==Qn||null==(h=Qn.typography[qn])||null==(h=h.buttons)||null==(h=h.primary)?void 0:h.weight};\n    font-style:  ${null!=Qn&&null!=(y=Qn.typography[qn])&&null!=(y=y.buttons)&&null!=(y=y.primary)&&y.isItalic?"italic":"normal"};\n     letter-spacing: ${null==Qn||null==(m=Qn.typography[qn])||null==(m=m.buttons)||null==(m=m.primary)?void 0:m.letterSpacing}px;\n     font-size: 16px;\n  }\n  .button-view-primary {\n      background-color: var(${Fn.ur[null==Qn||null==(v=Qn.colors)||null==(v=v.buttons)||null==(v=v.primary)||null==(v=v.default)?void 0:v.background]});\n    border: ${null==Qn||null==(b=Qn.buttons)||null==(b=b.primary)||null==(b=b.border)?void 0:b.width} solid\n      var(${Fn.ur[null==Qn||null==(f=Qn.colors)||null==(f=f.buttons)||null==(f=f.primary)||null==(f=f.default)?void 0:f.border]});\n    padding: ${Nn._[null==Qn||null==(x=Qn.buttons)||null==(x=x.primary)?void 0:x.padding]};\n  border-start-start-radius: ${null==Qn||null==(S=Qn.buttons)||null==(S=S.primary)||null==(S=S.border)?void 0:S.borderStartStartRadius}px;\n    border-start-end-radius: ${null==Qn||null==(Z=Qn.buttons)||null==(Z=Z.primary)||null==(Z=Z.border)?void 0:Z.borderStartEndRadius}px;\n    border-end-start-radius: ${null==Qn||null==(_=Qn.buttons)||null==(_=_.primary)||null==(_=_.border)?void 0:_.borderEndStartRadius}px;\n    border-end-end-radius: ${null==Qn||null==(C=Qn.buttons)||null==(C=C.primary)||null==(C=C.border)?void 0:C.borderEndEndRadius}px;\n    &:hover {\n      background-color: var(\n        ${Fn.ur[null==Qn||null==(B=Qn.colors)||null==(B=B.buttons)||null==(B=B.primary)||null==(B=B.hover)?void 0:B.background]}\n      );\n      border: ${null==Qn||null==($=Qn.buttons)||null==($=$.primary)||null==($=$.border)?void 0:$.width} solid\n      var(${Fn.ur[null==Qn||null==(M=Qn.colors)||null==(M=M.buttons)||null==(M=M.primary)||null==(M=M.hover)?void 0:M.border]});\n      * {\n      color: var(\n        ${Fn.ur[null==Qn||null==(k=Qn.colors)||null==(k=k.buttons)||null==(k=k.primary)||null==(k=k.hover)?void 0:k.text]}\n      );\n      }\n    }\n}\n\n .button-view-primary, .button-view-primary * {\n    color: var(${Fn.ur[null==Qn||null==(I=Qn.colors)||null==(I=I.buttons)||null==(I=I.primary)||null==(I=I.default)?void 0:I.text]});\n  font-family: ${null==Qn||null==(L=Qn.typography[qn])||null==(L=L.buttons)||null==(L=L.primary)?void 0:L.fontFamily};\n    font-weight: ${null==Qn||null==(T=Qn.typography[qn])||null==(T=T.buttons)||null==(T=T.primary)?void 0:T.weight};\n    font-style:  ${null!=Qn&&null!=(F=Qn.typography[qn])&&null!=(F=F.buttons)&&null!=(F=F.primary)&&F.isItalic?"italic":"normal"};\n    font-size: ${null==Qn||null==(D=Qn.typography[qn])||null==(D=D.buttons)||null==(D=D.primary)?void 0:D.fontSize}px;\n    letter-spacing: ${null==Qn||null==(N=Qn.typography[qn])||null==(N=N.buttons)||null==(N=N.primary)?void 0:N.letterSpacing}px;\n }\n\n.button-view-secondary {\n    background-color: var(${Fn.ur[null==Qn||null==(O=Qn.colors)||null==(O=O.buttons)||null==(O=O.secondary)||null==(O=O.default)?void 0:O.background]});\n    border: ${null==Qn||null==(z=Qn.buttons)||null==(z=z.secondary)||null==(z=z.border)?void 0:z.width} solid\n      var(${Fn.ur[null==Qn||null==(E=Qn.colors)||null==(E=E.buttons)||null==(E=E.secondary)||null==(E=E.default)?void 0:E.border]});\n    padding: ${Nn._[null==Qn||null==(A=Qn.buttons)||null==(A=A.secondary)?void 0:A.padding]};\n    border-start-start-radius: ${null==Qn||null==(j=Qn.buttons)||null==(j=j.secondary)||null==(j=j.border)?void 0:j.borderStartStartRadius}px;\n    border-start-end-radius: ${null==Qn||null==(R=Qn.buttons)||null==(R=R.secondary)||null==(R=R.border)?void 0:R.borderStartEndRadius}px;\n    border-end-start-radius: ${null==Qn||null==(P=Qn.buttons)||null==(P=P.secondary)||null==(P=P.border)?void 0:P.borderEndStartRadius}px;\n    border-end-end-radius: ${null==Qn||null==(U=Qn.buttons)||null==(U=U.secondary)||null==(U=U.border)?void 0:U.borderEndEndRadius}px;\n    &:hover {\n      background-color: var(\n        ${Fn.ur[null==Qn||null==(G=Qn.colors)||null==(G=G.buttons)||null==(G=G.secondary)||null==(G=G.hover)?void 0:G.background]}\n      );\n      border: ${null==Qn||null==(X=Qn.buttons)||null==(X=X.secondary)||null==(X=X.border)?void 0:X.width} solid\n        var(${Fn.ur[null==Qn||null==(H=Qn.colors)||null==(H=H.buttons)||null==(H=H.secondary)||null==(H=H.hover)?void 0:H.border]});\n      * {\n      color: var(\n        ${Fn.ur[null==Qn||null==(W=Qn.colors)||null==(W=W.buttons)||null==(W=W.secondary)||null==(W=W.hover)?void 0:W.text]}\n      );\n      }\n    }\n}\n\n.button-view-secondary, .button-view-secondary * {\n color: var(${Fn.ur[null==Qn||null==(K=Qn.colors)||null==(K=K.buttons)||null==(K=K.secondary)||null==(K=K.default)?void 0:K.text]});\n  font-family: ${null==Qn||null==(V=Qn.typography[qn])||null==(V=V.buttons)||null==(V=V.secondary)?void 0:V.fontFamily};\n    font-weight: ${null==Qn||null==(Y=Qn.typography[qn])||null==(Y=Y.buttons)||null==(Y=Y.secondary)?void 0:Y.weight};\n    font-style:  ${null!=Qn&&null!=(Q=Qn.typography[qn])&&null!=(Q=Q.buttons)&&null!=(Q=Q.secondary)&&Q.isItalic?"italic":"normal"};\n    font-size: ${null==Qn||null==(q=Qn.typography[qn])||null==(q=q.buttons)||null==(q=q.secondary)?void 0:q.fontSize}px;\n    letter-spacing: ${null==Qn||null==(J=Qn.typography[qn])||null==(J=J.buttons)||null==(J=J.secondary)?void 0:J.letterSpacing}px;\n }\n\n.button-view-tertiary {\n    background-color: var(${Fn.ur[null==Qn||null==(ee=Qn.colors)||null==(ee=ee.buttons)||null==(ee=ee.tertiary)||null==(ee=ee.default)?void 0:ee.background]});\n    border: ${null==Qn||null==(ne=Qn.buttons)||null==(ne=ne.tertiary)||null==(ne=ne.border)?void 0:ne.width} solid\n    var(${Fn.ur[null==Qn||null==(te=Qn.colors)||null==(te=te.buttons)||null==(te=te.tertiary)||null==(te=te.default)?void 0:te.border]});\n    padding: ${Nn._[null==Qn||null==(le=Qn.buttons)||null==(le=le.tertiary)?void 0:le.padding]};\n    border-start-start-radius: ${null==Qn||null==(oe=Qn.buttons)||null==(oe=oe.tertiary)||null==(oe=oe.border)?void 0:oe.borderStartStartRadius}px;\n    border-start-end-radius: ${null==Qn||null==(ae=Qn.buttons)||null==(ae=ae.tertiary)||null==(ae=ae.border)?void 0:ae.borderStartEndRadius}px;\n    border-end-start-radius: ${null==Qn||null==(ie=Qn.buttons)||null==(ie=ie.tertiary)||null==(ie=ie.border)?void 0:ie.borderEndStartRadius}px;\n    border-end-end-radius: ${null==Qn||null==(re=Qn.buttons)||null==(re=re.tertiary)||null==(re=re.border)?void 0:re.borderEndEndRadius}px;\n    &:hover {\n      background-color: var(\n        ${Fn.ur[null==Qn||null==(se=Qn.colors)||null==(se=se.buttons)||null==(se=se.tertiary)||null==(se=se.hover)?void 0:se.background]}\n      );\n      border: ${null==Qn||null==(de=Qn.buttons)||null==(de=de.tertiary)||null==(de=de.border)?void 0:de.width} solid\n        var(${Fn.ur[null==Qn||null==(ue=Qn.colors)||null==(ue=ue.buttons)||null==(ue=ue.tertiary)||null==(ue=ue.hover)?void 0:ue.border]});\n      * {\n      color: var(\n        ${Fn.ur[null==Qn||null==(ce=Qn.colors)||null==(ce=ce.buttons)||null==(ce=ce.tertiary)||null==(ce=ce.hover)?void 0:ce.text]}\n      );\n       }\n    }\n}\n\n.button-view-tertiary, .button-view-tertiary * {\n color: var(${Fn.ur[null==Qn||null==(pe=Qn.colors)||null==(pe=pe.buttons)||null==(pe=pe.tertiary)||null==(pe=pe.default)?void 0:pe.text]});\n  font-family: ${null==Qn||null==(ge=Qn.typography[qn])||null==(ge=ge.buttons)||null==(ge=ge.tertiary)?void 0:ge.fontFamily};\n    font-weight: ${null==Qn||null==(he=Qn.typography[qn])||null==(he=he.buttons)||null==(he=he.tertiary)?void 0:he.weight};\n    font-style:  ${null!=Qn&&null!=(ye=Qn.typography[qn])&&null!=(ye=ye.buttons)&&null!=(ye=ye.tertiary)&&ye.isItalic?"italic":"normal"};\n    font-size: ${null==Qn||null==(me=Qn.typography[qn])||null==(me=me.buttons)||null==(me=me.tertiary)?void 0:me.fontSize}px;\n    letter-spacing: ${null==Qn||null==(ve=Qn.typography[qn])||null==(ve=ve.buttons)||null==(ve=ve.tertiary)?void 0:ve.letterSpacing}px;\n }\n\n#global-heading-1-styles, #global-heading-1-styles * {\n    color: var(${Fn.ur[null==Qn||null==(be=Qn.colors)||null==(be=be.text)?void 0:be.heading1]});\n    font-family: ${null==Qn||null==(fe=Qn.typography[qn])||null==(fe=fe.headings)||null==(fe=fe.h1)?void 0:fe.fontFamily};\n    font-weight: ${null==Qn||null==(xe=Qn.typography[qn])||null==(xe=xe.headings)||null==(xe=xe.h1)?void 0:xe.weight};\n    font-style:  ${null!=Qn&&null!=(Se=Qn.typography[qn])&&null!=(Se=Se.headings)&&null!=(Se=Se.h1)&&Se.isItalic?"italic":"normal"};\n    font-size: var(--heading1-font-size);\n    line-height: ${null!=(Ze=Qn.typography[qn])&&null!=(Ze=Ze.headings)&&null!=(Ze=Ze.h1)&&Ze.isAutomatic?"normal":(null==Qn||null==(_e=Qn.typography[qn])||null==(_e=_e.headings)||null==(_e=_e.h1)?void 0:_e.lineHeight)+"px"};\n    letter-spacing: ${null==Qn||null==(Ce=Qn.typography[qn])||null==(Ce=Ce.headings)||null==(Ce=Ce.h1)?void 0:Ce.letterSpacing}px;\n    @media (min-width: 1024px) and (max-width: 1280px) {\n     --heading1-font-size: clamp(${ot*et}px, calc(${ot*et}px + (${ot} - ${ot*et}) * ((100vw - 375px) / (1065))), ${ot}px);\n    }\n    @media (max-width: 1280px) {\n      line-height: ${null!=(Be=Qn.typography[qn])&&null!=(Be=Be.headings)&&null!=(Be=Be.h1)&&Be.isAutomatic?"normal":"calc(1.2*var(--heading1-font-size))"};\n    }\n }\n\n#global-heading-2-styles, #global-heading-2-styles * {\n    color: var(\n            ${Fn.ur[null==Qn||null==(we=Qn.colors)||null==(we=we.text)?void 0:we.heading2]}\n            );\n    font-family: ${null==Qn||null==($e=Qn.typography[qn])||null==($e=$e.headings)||null==($e=$e.h2)?void 0:$e.fontFamily};\n    font-weight: ${null==Qn||null==(Me=Qn.typography[qn])||null==(Me=Me.headings)||null==(Me=Me.h2)?void 0:Me.weight};\n    font-style:  ${null!=Qn&&null!=(ke=Qn.typography[qn])&&null!=(ke=ke.headings)&&null!=(ke=ke.h2)&&ke.isItalic?"italic":"normal"};\n    font-size: var(--heading2-font-size);\n    letter-spacing: ${null==Qn||null==(Ie=Qn.typography[qn])||null==(Ie=Ie.headings)||null==(Ie=Ie.h2)?void 0:Ie.letterSpacing}px;\n    line-height: ${null!=Qn&&null!=(Le=Qn.typography[qn])&&null!=(Le=Le.headings)&&null!=(Le=Le.h2)&&Le.isAutomatic?"normal":(null==Qn||null==(Te=Qn.typography[qn])||null==(Te=Te.headings)||null==(Te=Te.h2)?void 0:Te.lineHeight)+"px"};\n      @media (min-width: 1024px) and (max-width: 1280px) {\n     --heading2-font-size: clamp(${at*et}px, calc(${at*et}px + (${at} - ${at*et}) * ((100vw - 375px) / (1065))), ${at}px);\n    }\n    @media (max-width: 1280px) {\n      line-height: ${null!=(Fe=Qn.typography[qn])&&null!=(Fe=Fe.headings)&&null!=(Fe=Fe.h2)&&Fe.isAutomatic?"normal":"calc(1.2*var(--heading2-font-size))"};\n    }\n}\n\n#global-heading-3-styles, #global-heading-3-styles * {\n    color: var(\n            ${Fn.ur[null==Qn||null==(De=Qn.colors)||null==(De=De.text)?void 0:De.heading3]}\n            );\n  font-family: ${null==Qn||null==(Oe=Qn.typography[qn])||null==(Oe=Oe.headings)||null==(Oe=Oe.h3)?void 0:Oe.fontFamily};\n    font-weight: ${null==Qn||null==(ze=Qn.typography[qn])||null==(ze=ze.headings)||null==(ze=ze.h3)?void 0:ze.weight};\n    font-style:  ${null!=Qn&&null!=(Ee=Qn.typography[qn])&&null!=(Ee=Ee.headings)&&null!=(Ee=Ee.h3)&&Ee.isItalic?"italic":"normal"};\n    font-size: var(--heading3-font-size);\n    letter-spacing: ${null==Qn||null==(Ae=Qn.typography[qn])||null==(Ae=Ae.headings)||null==(Ae=Ae.h3)?void 0:Ae.letterSpacing}px;\n    line-height: ${null!=Qn&&null!=(je=Qn.typography[qn])&&null!=(je=je.headings)&&null!=(je=je.h3)&&je.isAutomatic?"normal":(null==Qn||null==(Re=Qn.typography[qn])||null==(Re=Re.headings)||null==(Re=Re.h3)?void 0:Re.lineHeight)+"px"};\n      @media (min-width: 1024px) and (max-width: 1280px) {\n     --heading3-font-size: clamp(${it*et}px, calc(${it*et}px + (${it} - ${it*et}) * ((100vw - 375px) / (1065))), ${it}px);\n    }\n    @media (max-width: 1280px) {\n      line-height: ${null!=(Pe=Qn.typography[qn])&&null!=(Pe=Pe.headings)&&null!=(Pe=Pe.h3)&&Pe.isAutomatic?"normal":"calc(1.2*var(--heading3-font-size))"};\n    }\n}\n\n#global-heading-4-styles, #global-heading-4-styles * {\n    color: var(\n            ${Fn.ur[null==Qn||null==(Ue=Qn.colors)||null==(Ue=Ue.text)?void 0:Ue.heading4]}\n            );\n  font-family: ${null==Qn||null==(Ge=Qn.typography[qn])||null==(Ge=Ge.headings)||null==(Ge=Ge.h4)?void 0:Ge.fontFamily};\n    font-weight: ${null==Qn||null==(Xe=Qn.typography[qn])||null==(Xe=Xe.headings)||null==(Xe=Xe.h4)?void 0:Xe.weight};\n    font-style:  ${null!=Qn&&null!=(He=Qn.typography[qn])&&null!=(He=He.headings)&&null!=(He=He.h4)&&He.isItalic?"italic":"normal"};\n    font-size: var(--heading4-font-size);\n    line-height: ${null!=Qn&&null!=(We=Qn.typography[qn])&&null!=(We=We.headings)&&null!=(We=We.h4)&&We.isAutomatic?"normal":(null==Qn||null==(Ke=Qn.typography[qn])||null==(Ke=Ke.headings)||null==(Ke=Ke.h4)?void 0:Ke.lineHeight)+"px"};\n    @media (max-width: 1280px) {\n      line-height: ${null!=(Ve=Qn.typography[qn])&&null!=(Ve=Ve.headings)&&null!=(Ve=Ve.h4)&&Ve.isAutomatic?"normal":"calc(1.2*var(--heading4-font-size))"};\n    }\n    letter-spacing: ${null==Qn||null==(Ye=Qn.typography[qn])||null==(Ye=Ye.headings)||null==(Ye=Ye.h4)?void 0:Ye.letterSpacing}px;\n }\n\n#global-heading-5-styles, #global-heading-5-styles * {\n    color: var(\n            ${Fn.ur[null==Qn||null==(Qe=Qn.colors)||null==(Qe=Qe.text)?void 0:Qe.heading5]}\n            );\n  font-family: ${null==Qn||null==(qe=Qn.typography[qn])||null==(qe=qe.headings)||null==(qe=qe.h5)?void 0:qe.fontFamily};\n    font-weight: ${null==Qn||null==(Je=Qn.typography[qn])||null==(Je=Je.headings)||null==(Je=Je.h5)?void 0:Je.weight};\n    font-style:  ${null!=Qn&&null!=(en=Qn.typography[qn])&&null!=(en=en.headings)&&null!=(en=en.h5)&&en.isItalic?"italic":"normal"};\n    font-size: var(--heading5-font-size);\n    line-height: ${null!=Qn&&null!=(nn=Qn.typography[qn])&&null!=(nn=nn.headings)&&null!=(nn=nn.h5)&&nn.isAutomatic?"normal":(null==Qn||null==(tn=Qn.typography[qn])||null==(tn=tn.headings)||null==(tn=tn.h5)?void 0:tn.lineHeight)+"px"};\n    @media (max-width: 1280px) {\n      line-height: ${null!=(ln=Qn.typography[qn])&&null!=(ln=ln.headings)&&null!=(ln=ln.h5)&&ln.isAutomatic?"normal":"calc(1.2*var(--heading5-font-size))"};\n    }\n    letter-spacing: ${null==Qn||null==(on=Qn.typography[qn])||null==(on=on.headings)||null==(on=on.h5)?void 0:on.letterSpacing}px;\n }\n\n#global-heading-6-styles, #global-heading-6-styles * {\n    color: var(\n            ${Fn.ur[null==Qn||null==(an=Qn.colors)||null==(an=an.text)?void 0:an.heading6]}\n            );\n  font-family: ${null==Qn||null==(rn=Qn.typography[qn])||null==(rn=rn.headings)||null==(rn=rn.h6)?void 0:rn.fontFamily};\n    font-weight: ${null==Qn||null==(sn=Qn.typography[qn])||null==(sn=sn.headings)||null==(sn=sn.h6)?void 0:sn.weight};\n    font-style:  ${null!=Qn&&null!=(dn=Qn.typography[qn])&&null!=(dn=dn.headings)&&null!=(dn=dn.h6)&&dn.isItalic?"italic":"normal"};\n    font-size:var(--heading6-font-size);\n    line-height: ${null!=Qn&&null!=(un=Qn.typography[qn])&&null!=(un=un.headings)&&null!=(un=un.h6)&&un.isAutomatic?"normal":(null==Qn||null==(cn=Qn.typography[qn])||null==(cn=cn.headings)||null==(cn=cn.h6)?void 0:cn.lineHeight)+"px"};\n    @media (max-width: 1280px) {\n      line-height: ${null!=(pn=Qn.typography[qn])&&null!=(pn=pn.headings)&&null!=(pn=pn.h6)&&pn.isAutomatic?"normal":"calc(1.2*var(--heading6-font-size))"};\n    }\n    letter-spacing: ${null==Qn||null==(gn=Qn.typography[qn])||null==(gn=gn.headings)||null==(gn=gn.h6)?void 0:gn.letterSpacing}px;\n }\n\n#global-paragraph-1-styles, #global-paragraph-1-styles * {\n    color: var(\n    ${Fn.ur[null==Qn||null==(hn=Qn.colors)||null==(hn=hn.text)?void 0:hn.paragraph1]}\n            );\n  font-family: ${null==Qn||null==(yn=Qn.typography[qn])||null==(yn=yn.paragraphs)||null==(yn=yn.p1)?void 0:yn.fontFamily};\n    font-weight: ${null==Qn||null==(mn=Qn.typography[qn])||null==(mn=mn.paragraphs)||null==(mn=mn.p1)?void 0:mn.weight};\n    font-style:  ${null!=Qn&&null!=(vn=Qn.typography[qn])&&null!=(vn=vn.paragraphs)&&null!=(vn=vn.p1)&&vn.isItalic?"italic":"normal"};\n    font-size: var(--paragraph1-font-size);\n    line-height: ${null!=Qn&&null!=(bn=Qn.typography[qn])&&null!=(bn=bn.paragraphs)&&null!=(bn=bn.p1)&&bn.isAutomatic?"normal":(null==Qn||null==(fn=Qn.typography[qn])||null==(fn=fn.paragraphs)||null==(fn=fn.p1)?void 0:fn.lineHeight)+"px"};\n    @media (max-width: 1280px) {\n      line-height: ${null!=Qn&&null!=(xn=Qn.typography[qn])&&null!=(xn=xn.paragraphs)&&null!=(xn=xn.p1)&&xn.isAutomatic?"normal":"calc(1.2*var(--paragraph1-font-size))"};\n    }\n    letter-spacing: ${null==Qn||null==(Sn=Qn.typography[qn])||null==(Sn=Sn.paragraphs)||null==(Sn=Sn.p1)?void 0:Sn.letterSpacing}px;\n }\n#global-paragraph-2-styles, #global-paragraph-2-styles * {\n    color: var(\n    ${Fn.ur[null==Qn||null==(Zn=Qn.colors)||null==(Zn=Zn.text)?void 0:Zn.paragraph2]}\n            );\n  font-family: ${null==Qn||null==(_n=Qn.typography[qn])||null==(_n=_n.paragraphs)||null==(_n=_n.p2)?void 0:_n.fontFamily};\n    font-weight: ${null==Qn||null==(Cn=Qn.typography[qn])||null==(Cn=Cn.paragraphs)||null==(Cn=Cn.p2)?void 0:Cn.weight};\n    font-style:  ${null!=Qn&&null!=(Bn=Qn.typography[qn])&&null!=(Bn=Bn.paragraphs)&&null!=(Bn=Bn.p2)&&Bn.isItalic?"italic":"normal"};\n    font-size: var(--paragraph2-font-size);\n    line-height: ${null!=Qn&&null!=(wn=Qn.typography[qn])&&null!=(wn=wn.paragraphs)&&null!=(wn=wn.p2)&&wn.isAutomatic?"normal":(null==Qn||null==($n=Qn.typography[qn])||null==($n=$n.paragraphs)||null==($n=$n.p2)?void 0:$n.lineHeight)+"px"};\n    @media (max-width: 1280px) {\n      line-height: ${null!=Qn&&null!=(Mn=Qn.typography[qn])&&null!=(Mn=Mn.paragraphs)&&null!=(Mn=Mn.p2)&&Mn.isAutomatic?"normal":"calc(1.2*var(--paragraph2-font-size))"};\n    }\n    letter-spacing: ${null==Qn||null==(kn=Qn.typography[qn])||null==(kn=kn.paragraphs)||null==(kn=kn.p2)?void 0:kn.letterSpacing}px;\n }\n\n #global-paragraph-3-styles, #global-paragraph-3-styles * {\n    color: var(\n    ${Fn.ur[null==Qn||null==(In=Qn.colors)||null==(In=In.text)?void 0:In.paragraph3]}\n            );\n  font-family: ${null==Qn||null==(Ln=Qn.typography[qn])||null==(Ln=Ln.paragraphs)||null==(Ln=Ln.p3)?void 0:Ln.fontFamily};\n    font-weight: ${null==Qn||null==(On=Qn.typography[qn])||null==(On=On.paragraphs)||null==(On=On.p3)?void 0:On.weight};\n    font-style:  ${null!=Qn&&null!=(zn=Qn.typography[qn])&&null!=(zn=zn.paragraphs)&&null!=(zn=zn.p3)&&zn.isItalic?"italic":"normal"};\n    font-size: var(--paragraph3-font-size);\n    line-height: ${null!=Qn&&null!=(En=Qn.typography[qn])&&null!=(En=En.paragraphs)&&null!=(En=En.p3)&&En.isAutomatic?"normal":(null==Qn||null==(An=Qn.typography[qn])||null==(An=An.paragraphs)||null==(An=An.p3)?void 0:An.lineHeight)+"px"};\n    @media (max-width: 1280px) {\n      line-height: ${null!=Qn&&null!=(jn=Qn.typography[qn])&&null!=(jn=jn.paragraphs)&&null!=(jn=jn.p3)&&jn.isAutomatic?"normal":"calc(1.2*var(--paragraph3-font-size))"};\n    }\n    letter-spacing: ${null==Qn||null==(Rn=Qn.typography[qn])||null==(Rn=Rn.paragraphs)||null==(Rn=Rn.p3)?void 0:Rn.letterSpacing}px;\n }\n\n.general-input-wrapper-view , \n.general-input-wrapper-view:focus-within ,\n.select__control , \n.select__control:focus , \n.select__control:hover {\n    background-color:  var(${null!=Qn&&null!=(Pn=Qn.form)&&Pn.fill?Fn.ur[null==Qn||null==(Un=Qn.colors)||null==(Un=Un.formField)?void 0:Un.fill]:"--white-background-color"}) !important;\n    color: var(${Fn.ur[null==Qn||null==(Gn=Qn.colors)||null==(Gn=Gn.formField)?void 0:Gn.text]}) !important;\n    border: 1px ${null==Qn||null==(Xn=Qn.form)||null==(Xn=Xn.border)?void 0:Xn.style}\n      var(${Fn.ur[null==Qn||null==(Hn=Qn.colors)||null==(Hn=Hn.formField)?void 0:Hn.border]}) !important;\n    box-shadow: none;\n    padding: ${Dn.KQ[null==Qn||null==(Wn=Qn.form)?void 0:Wn.size]} !important;\n    border-radius: ${Dn.S4[null==Qn||null==(Kn=Qn.form)?void 0:Kn.style]} !important;\n}\n  .general-input-wrapper-view input::placeholder,\n  .general-input-wrapper-view select::placeholder,\n  .general-input-wrapper-view textarea::placeholder  {\n    color: var(${Fn.ur[null==Qn||null==(Vn=Qn.colors)||null==(Vn=Vn.formField)?void 0:Vn.text]}) !important;\n    }\n  .general-input-label {\n    color: var(${Fn.ur[null==Qn||null==(Yn=Qn.colors)||null==(Yn=Yn.formField)?void 0:Yn.label]}) !important;\n  }\n      `
                        })]
                    })
                },
                zn = (0, l.connect)((e => {
                    var n;
                    const t = (0, Ne.ck)(e);
                    return {
                        locale: (0, Ne.EY)(e),
                        uiLocale: c(e).language,
                        siteId: t,
                        pageId: (0, Ne.Qm)(e),
                        pageData: (0, Ne.zE)(e),
                        universals: (0, Ne.Mf)(e),
                        siteData: (0, Ne.D0)(e, t),
                        activeTheme: null == V.wl || null == (n = V.wl.get(e)) || null == (n = n.themes) ? void 0 : n.active
                    }
                }))((function(e) {
                    const {
                        universals: n,
                        pageData: t,
                        locale: l,
                        siteId: o,
                        pageId: i,
                        uiLocale: r,
                        siteData: s,
                        activeTheme: u
                    } = e, {
                        setContext: c
                    } = (0, d.useTracking)();
                    return (0, a.useEffect)((() => {
                        var e, n, t, l;
                        c("site", {
                            id: null == s ? void 0 : s.id,
                            name: null == s ? void 0 : s.site_name,
                            domain: (l = null == s || null == (e = s.domain_name) ? void 0 : e.domain_name, l ? -1 === l.indexOf(".") ? `${l}.wuiltweb.com` : l : ""),
                            defaultLocale: null == s ? void 0 : s.defaultLocale,
                            isSubscribed: 1 !== (null == s || null == (n = s.plan) ? void 0 : n.id),
                            plan: null == s || null == (t = s.plan) ? void 0 : t.name
                        })
                    }), []), (0, w.tZ)(we, {
                        boundaryType: _e,
                        siteId: o,
                        pageId: i,
                        children: (0, w.BX)(In, {
                            children: [null != u && u.theme ? (0, w.tZ)(On, {}) : (0, w.tZ)(Ln, {}), (0, w.tZ)(pn, {
                                locale: l,
                                siteId: o,
                                uiLocale: r,
                                pageData: t,
                                universals: n,
                                siteData: s
                            })]
                        })
                    })
                }));
            var En = t(53724);
            const An = () => ((0, a.useEffect)((() => {
                En.Z.subject.subscribe((({
                    sectionId: e
                }) => {
                    setTimeout((() => {
                        const n = document.getElementById(e);
                        n && s()(n, {
                            duration: 300,
                            offset: 0
                        })
                    }), 60)
                }))
            }), []), (0, w.tZ)(zn, {}));
            var jn = t(84848),
                Rn = t(81292);
            const Pn = () => (history.navigate = (0, jn.useNavigate)(), history.location = (0, jn.useLocation)(), null),
                Un = () => {
                    const {
                        locale: e
                    } = (0, d.useLocale)();
                    return on ? (0, w.BX)(w.HY, {
                        children: [(0, w.tZ)(Pn, {}), (0, w.tZ)(l.Provider, {
                            store: on,
                            children: (0, w.tZ)(Re.ChakraProvider, {
                                theme: (0, Rn.g)(e.dir || "ltr"),
                                toastOptions: {
                                    defaultOptions: {
                                        position: "top-right",
                                        isClosable: !0
                                    }
                                },
                                children: (0, w.tZ)(o.Z, {
                                    children: (0, w.tZ)(An, {})
                                })
                            })
                        })]
                    }) : null
                }
        },
        9242: (e, n, t) => {
            t.d(n, {
                Q: () => l
            });
            let l = function(e) {
                return e.OWNER = "OWNER", e.CUSTOM = "CUSTOM", e
            }({})
        },
        74826: (e, n, t) => {
            t.d(n, {
                Z: () => u
            });
            var l = t(77686),
                o = t(9242),
                a = t(44486),
                i = t(37044),
                r = t(75675),
                s = t(37900),
                d = t(59274);
            const u = e => {
                const {
                    user: n
                } = (0, l.useAuth)(), t = (0, a.useSelector)((e => (0, r.Q3)(e))), u = (0, a.useSelector)((e => (0, i.A9)(e))), [c, p] = (0, s.useState)(t || u);
                (0, s.useEffect)((() => {
                    (async () => {
                        if (!c) {
                            const n = await (0, d.m7)(e);
                            p(n)
                        }
                    })()
                }), []);
                const g = null == c ? void 0 : c.find((e => (null == e ? void 0 : e.role) === o.Q.OWNER)),
                    h = null == g ? void 0 : g.mail,
                    y = h === (null == n ? void 0 : n.email);
                return {
                    usersList: c,
                    setUsersList: p,
                    isOwnerUser: y,
                    ownerEmail: h
                }
            }
        },
        8352: (e, n, t) => {
            t.d(n, {
                Z: () => b
            });
            var l = t(37900),
                o = t.n(l),
                a = t(23147),
                i = t.n(a),
                r = t(84848),
                s = t(29822),
                d = t.n(s);
            const u = "Button_prefix__P7M4N";
            var c = t(63231),
                p = t.n(c);
            const g = {
                button: "ButtonLayout_button__szCQ5",
                transparent: "ButtonLayout_transparent__TJKE6",
                "close-standard": "ButtonLayout_close-standard__1XCP5",
                "close-dark": "ButtonLayout_close-dark__CIC1j",
                "close-transparent": "ButtonLayout_close-transparent__cAMIU",
                "icon-greybackground": "ButtonLayout_icon-greybackground__tV2bb",
                "icon-standard": "ButtonLayout_icon-standard__pEQGn",
                "icon-standardsecondary": "ButtonLayout_icon-standardsecondary__OJTdV",
                "icon-white": "ButtonLayout_icon-white__U-Qe0",
                "icon-whitesecondary": "ButtonLayout_icon-whitesecondary__shahu",
                inner: "ButtonLayout_inner__vVjxR",
                heightsmall: "ButtonLayout_heightsmall__XXHXt",
                heightlarge: "ButtonLayout_heightlarge__FiZCS",
                "heightx-large": "ButtonLayout_heightx-large__0K3Mp",
                fullGreen: "ButtonLayout_fullGreen__8Znfi",
                fullWhite: "ButtonLayout_fullWhite__pYhWL",
                fullKohly: "ButtonLayout_fullKohly__-h68g",
                fullTransparent: "ButtonLayout_fullTransparent__7J7+D",
                hover: "ButtonLayout_hover__ZbRuW",
                dashedGreen: "ButtonLayout_dashedGreen__691eF",
                fullTransparentIconOnDark: "ButtonLayout_fullTransparentIconOnDark__t8C3x",
                fullTransparentOnDark: "ButtonLayout_fullTransparentOnDark__NF3xa",
                fullGold: "ButtonLayout_fullGold__4kuUp",
                emptyGold: "ButtonLayout_emptyGold__nTtxs",
                emptyGolden: "ButtonLayout_emptyGolden__wu8Gq",
                editorBar: "ButtonLayout_editorBar__RWXF8",
                editorBarIcon: "ButtonLayout_editorBarIcon__lcd46",
                active: "ButtonLayout_active__bIndz",
                yellowGradient: "ButtonLayout_yellowGradient__OabQU",
                yellowGradientHover: "ButtonLayout_yellowGradientHover__LpOeI",
                yellowGradientActive: "ButtonLayout_yellowGradientActive__jGY6P",
                blueGradient: "ButtonLayout_blueGradient__vb01I",
                fullblue: "ButtonLayout_fullblue__embNk",
                login: "ButtonLayout_login__uoqZP",
                fullpurple: "ButtonLayout_fullpurple__3VXmM",
                fullred: "ButtonLayout_fullred__phnqJ",
                emptyWhite: "ButtonLayout_emptyWhite__1JXSZ",
                emptyGreen: "ButtonLayout_emptyGreen__cl0Mr",
                emptyRed: "ButtonLayout_emptyRed__2PUKY",
                emptyblue: "ButtonLayout_emptyblue__IB4sI",
                emptylogin: "ButtonLayout_emptylogin__exEgE",
                emptypurple: "ButtonLayout_emptypurple__XkZJT",
                transparentblue: "ButtonLayout_transparentblue__1LboR",
                emptybluesecondary: "ButtonLayout_emptybluesecondary__N5sw+",
                whiteblueprimary: "ButtonLayout_whiteblueprimary__M9aVk",
                whiteblue: "ButtonLayout_whiteblue__kwoQk",
                whitebluesecondary: "ButtonLayout_whitebluesecondary__8ddUX",
                emptyred: "ButtonLayout_emptyred__5LA7P",
                disabled: "ButtonLayout_disabled__lICC8",
                fullgreen: "ButtonLayout_fullgreen__+AuAv",
                emptygreen: "ButtonLayout_emptygreen__-azim",
                plainGreen: "ButtonLayout_plainGreen__R97tS",
                plainRed: "ButtonLayout_plainRed__rZZch"
            };
            var h = t(74499);
            const y = e => {
                const {
                    theme: n,
                    hover: t,
                    active: l,
                    disabled: a,
                    height: i,
                    children: r,
                    matchParent: s
                } = e, d = p()({
                    [g.button]: !0,
                    [g[n]]: !0,
                    [g.hover]: t,
                    [g.active]: l,
                    [g.disabled]: a,
                    [g[`height${i}`]]: "medium" !== i
                }, r.props.className), u = Object.assign({}, r.props.style, {
                    height: i,
                    display: "inline-block"
                });
                return s && (u.width = "100%"), 1 === o().Children.count(r) ? o().cloneElement(r, {
                    className: d,
                    style: u
                }, (0, h.tZ)("div", {
                    className: g.inner,
                    children: r.props.children
                })) : r
            };
            y.defaultProps = {
                height: "medium",
                theme: "fullGreen"
            }, y.propTypes = {
                active: i().bool,
                disabled: i().bool,
                height: i().oneOf(["small", "medium", "large", "x-large"]),
                hover: i().bool,
                matchParent: i().bool,
                theme: i().oneOf(["transparent", "fullTransparent", "fullTransparentOnDark", "fullTransparentIconOnDark", "fullGreen", "fullWhite", "fullKohly", "editorBar", "editorBarIcon", "emptyGold", "emptyRed", "emptyGreen", "emptyWhite", "yellowGradient", "yellowGradientActive", "yellowGradientHover", "plainGreen", "plainRed", "fullred"])
            }, y.displayName = "ButtonLayout";
            const m = (0, l.memo)(y);
            class v extends l.PureComponent {
                constructor(...e) {
                    super(...e), this.addIcon = (e, n, t) => {
                        const {
                            iconSize: l
                        } = this.props, a = l || ("small" === t ? "8px" : "medium" === t ? "12px" : "16px"), i = e === u ? "btn-prefix" : "btn-suffix";
                        return n ? (0, h.tZ)("div", {
                            className: e,
                            "data-hook": i,
                            children: o().cloneElement(n, {
                                size: a
                            })
                        }) : null
                    }, this.addPrefix = () => {
                        const {
                            prefixIcon: e,
                            height: n
                        } = this.props;
                        return this.addIcon(u, e, n)
                    }, this.addSuffix = () => {
                        const {
                            suffixIcon: e,
                            height: n
                        } = this.props;
                        return this.addIcon("Button_suffix__O7s1g", e, n)
                    }
                }
                render() {
                    const {
                        style: e,
                        to: n,
                        route: t,
                        disabled: l,
                        onClick: o,
                        children: a,
                        type: i,
                        className: s,
                        onMouseEnter: u,
                        onMouseLeave: c
                    } = this.props, p = d()(["id", "style", "onClick", "prefixIcon", "suffixIcon", "type"], this.props), g = t ? r.Link : "button";
                    return (0, h.tZ)(m, Object.assign({}, p, {
                        children: t ? (0, h.BX)(r.Link, {
                            to: n,
                            style: e,
                            className: s,
                            disabled: l,
                            type: i,
                            onMouseEnter: u,
                            onMouseLeave: c,
                            children: [this.addPrefix(), a, this.addSuffix()]
                        }) : (0, h.BX)(g, {
                            onClick: o,
                            style: e,
                            className: s,
                            disabled: l,
                            type: i,
                            onMouseEnter: u,
                            onMouseLeave: c,
                            children: [this.addPrefix(), a, this.addSuffix()]
                        })
                    }))
                }
            }
            v.propTypes = Object.assign({}, m.propTypes, {
                children: a.node,
                id: a.string,
                prefixIcon: a.node,
                suffixIcon: a.node,
                type: a.string,
                onClick: a.func,
                onMouseEnter: a.func,
                onMouseLeave: a.func
            }), v.defaultProps = m.defaultProps, v.displayName = "Button";
            const b = v
        },
        34080: (e, n, t) => {
            t.d(n, {
                Z: () => l.Z
            });
            var l = t(8352)
        }
    }
]);
//# sourceMappingURL=6584.2a5fe8b4f31bb29c.js.map