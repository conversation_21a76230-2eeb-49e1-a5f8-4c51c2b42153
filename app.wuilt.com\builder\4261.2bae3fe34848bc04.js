(self.webpackChunkeditor = self.webpackChunkeditor || []).push([
    [4261], {
        90988: function(e, t, r) {
            ! function(e, t, r) {
                var n = new Map,
                    i = new Map,
                    o = !0,
                    a = !1;

                function s(e) {
                    return e.replace(/[\s,]+/g, " ").trim()
                }

                function u(e) {
                    var r = new Set,
                        n = [];
                    return e.definitions.forEach((function(e) {
                        if ("FragmentDefinition" === e.kind) {
                            var t = e.name.value,
                                a = s((c = e.loc).source.body.substring(c.start, c.end)),
                                u = i.get(t);
                            u && !u.has(a) ? o && console.warn("Warning: fragment with name " + t + " already exists.\ngraphql-tag enforces all fragment names across your application to be unique; read more about\nthis in the docs: http://dev.apollodata.com/core/fragments.html#unique-names") : u || i.set(t, u = new Set), u.add(a), r.has(a) || (r.add(a), n.push(e))
                        } else n.push(e);
                        var c
                    })), t.__assign(t.__assign({}, e), {
                        definitions: n
                    })
                }

                function c(e) {
                    var t = s(e);
                    if (!n.has(t)) {
                        var i = r.parse(e, {
                            experimentalFragmentVariables: a,
                            allowLegacyFragmentVariables: a
                        });
                        if (!i || "Document" !== i.kind) throw new Error("Not a valid GraphQL document.");
                        n.set(t, function(e) {
                            var t = new Set(e.definitions);
                            t.forEach((function(e) {
                                e.loc && delete e.loc, Object.keys(e).forEach((function(r) {
                                    var n = e[r];
                                    n && "object" == typeof n && t.add(n)
                                }))
                            }));
                            var r = e.loc;
                            return r && (delete r.startToken, delete r.endToken), e
                        }(u(i)))
                    }
                    return n.get(t)
                }

                function l(e) {
                    for (var t = [], r = 1; r < arguments.length; r++) t[r - 1] = arguments[r];
                    "string" == typeof e && (e = [e]);
                    var n = e[0];
                    return t.forEach((function(t, r) {
                        t && "Document" === t.kind ? n += t.loc.source.body : n += t, n += e[r + 1]
                    })), c(n)
                }

                function f() {
                    n.clear(), i.clear()
                }

                function h() {
                    o = !1
                }

                function p() {
                    a = !0
                }

                function d() {
                    a = !1
                }
                var y, v = {
                    gql: l,
                    resetCaches: f,
                    disableFragmentWarnings: h,
                    enableExperimentalFragmentVariables: p,
                    disableExperimentalFragmentVariables: d
                };
                (y = l || (l = {})).gql = v.gql, y.resetCaches = v.resetCaches, y.disableFragmentWarnings = v.disableFragmentWarnings, y.enableExperimentalFragmentVariables = v.enableExperimentalFragmentVariables, y.disableExperimentalFragmentVariables = v.disableExperimentalFragmentVariables, l.default = l;
                var m = l;
                e.default = m, e.disableExperimentalFragmentVariables = d, e.disableFragmentWarnings = h, e.enableExperimentalFragmentVariables = p, e.gql = l, e.resetCaches = f, Object.defineProperty(e, "__esModule", {
                    value: !0
                })
            }(t, r(22970), r(48100))
        },
        13315: (e, t, r) => {
            e.exports = r(90988).gql
        },
        47923: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(57304),
                i = r(22970),
                o = r(63271),
                a = r(73805),
                s = r(51117),
                u = r(98788),
                c = r(73229),
                l = r(97278),
                f = function() {
                    function e() {
                        this.assumeImmutableResults = !1, this.getFragmentDoc = o.wrap(a.getFragmentQueryDocument)
                    }
                    return e.prototype.batch = function(e) {
                        var t, r = this,
                            n = "string" == typeof e.optimistic ? e.optimistic : !1 === e.optimistic ? null : void 0;
                        return this.performTransaction((function() {
                            return t = e.update(r)
                        }), n), t
                    }, e.prototype.recordOptimisticTransaction = function(e, t) {
                        this.performTransaction(e, t)
                    }, e.prototype.transformDocument = function(e) {
                        return e
                    }, e.prototype.transformForLink = function(e) {
                        return e
                    }, e.prototype.identify = function(e) {}, e.prototype.gc = function() {
                        return []
                    }, e.prototype.modify = function(e) {
                        return !1
                    }, e.prototype.readQuery = function(e, t) {
                        return void 0 === t && (t = !!e.optimistic), this.read(i.__assign(i.__assign({}, e), {
                            rootId: e.id || "ROOT_QUERY",
                            optimistic: t
                        }))
                    }, e.prototype.readFragment = function(e, t) {
                        return void 0 === t && (t = !!e.optimistic), this.read(i.__assign(i.__assign({}, e), {
                            query: this.getFragmentDoc(e.fragment, e.fragmentName),
                            rootId: e.id,
                            optimistic: t
                        }))
                    }, e.prototype.writeQuery = function(e) {
                        var t = e.id,
                            r = e.data,
                            n = i.__rest(e, ["id", "data"]);
                        return this.write(Object.assign(n, {
                            dataId: t || "ROOT_QUERY",
                            result: r
                        }))
                    }, e.prototype.writeFragment = function(e) {
                        var t = e.id,
                            r = e.data,
                            n = e.fragment,
                            o = e.fragmentName,
                            a = i.__rest(e, ["id", "data", "fragment", "fragmentName"]);
                        return this.write(Object.assign(a, {
                            query: this.getFragmentDoc(n, o),
                            dataId: t,
                            result: r
                        }))
                    }, e.prototype.updateQuery = function(e, t) {
                        return this.batch({
                            update: function(r) {
                                var n = r.readQuery(e),
                                    o = t(n);
                                return null == o ? n : (r.writeQuery(i.__assign(i.__assign({}, e), {
                                    data: o
                                })), o)
                            }
                        })
                    }, e.prototype.updateFragment = function(e, t) {
                        return this.batch({
                            update: function(r) {
                                var n = r.readFragment(e),
                                    o = t(n);
                                return null == o ? n : (r.writeFragment(i.__assign(i.__assign({}, e), {
                                    data: o
                                })), o)
                            }
                        })
                    }, e
                }();
            t.Cache = void 0, t.Cache || (t.Cache = {});
            var h = function(e) {
                    function t(r, n, i, o) {
                        var a, s = e.call(this, r) || this;
                        if (s.message = r, s.path = n, s.query = i, s.variables = o, Array.isArray(s.path)) {
                            s.missing = s.message;
                            for (var u = s.path.length - 1; u >= 0; --u) s.missing = ((a = {})[s.path[u]] = s.missing, a)
                        } else s.missing = s.path;
                        return s.__proto__ = t.prototype, s
                    }
                    return i.__extends(t, e), t
                }(Error),
                p = Object.prototype.hasOwnProperty;

            function d(e) {
                return null == e
            }

            function y(e, t) {
                var r = e.__typename,
                    n = e.id,
                    i = e._id;
                if ("string" == typeof r && (t && (t.keyObject = d(n) ? d(i) ? void 0 : {
                        _id: i
                    } : {
                        id: n
                    }), d(n) && !d(i) && (n = i), !d(n))) return "".concat(r, ":").concat("number" == typeof n || "string" == typeof n ? n : JSON.stringify(n))
            }
            var v = {
                dataIdFromObject: y,
                addTypename: !0,
                resultCaching: !0,
                canonizeResults: !1
            };

            function m(e) {
                var t = e.canonizeResults;
                return void 0 === t ? v.canonizeResults : t
            }
            var g = /^[_a-z][_0-9a-z]*/i;

            function b(e) {
                var t = e.match(g);
                return t ? t[0] : e
            }

            function _(e, t, r) {
                return !!a.isNonNullObject(t) && (a.isArray(t) ? t.every((function(t) {
                    return _(e, t, r)
                })) : e.selections.every((function(e) {
                    if (a.isField(e) && a.shouldInclude(e, r)) {
                        var n = a.resultKeyNameFromField(e);
                        return p.call(t, n) && (!e.selectionSet || _(e.selectionSet, t[n], r))
                    }
                    return !0
                })))
            }

            function w(e) {
                return a.isNonNullObject(e) && !a.isReference(e) && !a.isArray(e)
            }

            function O(e, t) {
                var r = a.createFragmentMap(a.getFragmentDefinitions(e));
                return {
                    fragmentMap: r,
                    lookupFragment: function(e) {
                        var n = r[e];
                        return !n && t && (n = t.lookup(e)), n || null
                    }
                }
            }
            var k = Object.create(null),
                S = function() {
                    return k
                },
                E = Object.create(null);
            t.EntityStore = function() {
                function e(e, t) {
                    var r = this;
                    this.policies = e, this.group = t, this.data = Object.create(null), this.rootIds = Object.create(null), this.refs = Object.create(null), this.getFieldValue = function(e, t) {
                        return a.maybeDeepFreeze(a.isReference(e) ? r.get(e.__ref, t) : e && e[t])
                    }, this.canRead = function(e) {
                        return a.isReference(e) ? r.has(e.__ref) : "object" == typeof e
                    }, this.toReference = function(e, t) {
                        if ("string" == typeof e) return a.makeReference(e);
                        if (a.isReference(e)) return e;
                        var n = r.policies.identify(e)[0];
                        if (n) {
                            var i = a.makeReference(n);
                            return t && r.merge(n, e), i
                        }
                    }
                }
                return e.prototype.toObject = function() {
                    return i.__assign({}, this.data)
                }, e.prototype.has = function(e) {
                    return void 0 !== this.lookup(e, !0)
                }, e.prototype.get = function(e, t) {
                    if (this.group.depend(e, t), p.call(this.data, e)) {
                        var r = this.data[e];
                        if (r && p.call(r, t)) return r[t]
                    }
                    return "__typename" === t && p.call(this.policies.rootTypenamesById, e) ? this.policies.rootTypenamesById[e] : this instanceof q ? this.parent.get(e, t) : void 0
                }, e.prototype.lookup = function(e, t) {
                    return t && this.group.depend(e, "__exists"), p.call(this.data, e) ? this.data[e] : this instanceof q ? this.parent.lookup(e, t) : this.policies.rootTypenamesById[e] ? Object.create(null) : void 0
                }, e.prototype.merge = function(e, t) {
                    var r, i = this;
                    a.isReference(e) && (e = e.__ref), a.isReference(t) && (t = t.__ref);
                    var o = "string" == typeof e ? this.lookup(r = e) : e,
                        s = "string" == typeof t ? this.lookup(r = t) : t;
                    if (s) {
                        n.invariant("string" == typeof r, 1);
                        var u = new a.DeepMerger(x).merge(o, s);
                        if (this.data[r] = u, u !== o && (delete this.refs[r], this.group.caching)) {
                            var c = Object.create(null);
                            o || (c.__exists = 1), Object.keys(s).forEach((function(e) {
                                if (!o || o[e] !== u[e]) {
                                    c[e] = 1;
                                    var t = b(e);
                                    t === e || i.policies.hasKeyArgs(u.__typename, t) || (c[t] = 1), void 0 !== u[e] || i instanceof q || delete u[e]
                                }
                            })), !c.__typename || o && o.__typename || this.policies.rootTypenamesById[r] !== u.__typename || delete c.__typename, Object.keys(c).forEach((function(e) {
                                return i.group.dirty(r, e)
                            }))
                        }
                    }
                }, e.prototype.modify = function(e, t) {
                    var r = this,
                        n = this.lookup(e);
                    if (n) {
                        var o = Object.create(null),
                            s = !1,
                            u = !0,
                            c = {
                                DELETE: k,
                                INVALIDATE: E,
                                isReference: a.isReference,
                                toReference: this.toReference,
                                canRead: this.canRead,
                                readField: function(t, n) {
                                    return r.policies.readField("string" == typeof t ? {
                                        fieldName: t,
                                        from: n || a.makeReference(e)
                                    } : t, {
                                        store: r
                                    })
                                }
                            };
                        if (Object.keys(n).forEach((function(l) {
                                var f = b(l),
                                    h = n[l];
                                if (void 0 !== h) {
                                    var p = "function" == typeof t ? t : t[l] || t[f];
                                    if (p) {
                                        var d = p === S ? k : p(a.maybeDeepFreeze(h), i.__assign(i.__assign({}, c), {
                                            fieldName: f,
                                            storeFieldName: l,
                                            storage: r.getStorage(e, l)
                                        }));
                                        d === E ? r.group.dirty(e, l) : (d === k && (d = void 0), d !== h && (o[l] = d, s = !0, h = d))
                                    }
                                    void 0 !== h && (u = !1)
                                }
                            })), s) return this.merge(e, o), u && (this instanceof q ? this.data[e] = void 0 : delete this.data[e], this.group.dirty(e, "__exists")), !0
                    }
                    return !1
                }, e.prototype.delete = function(e, t, r) {
                    var n, i = this.lookup(e);
                    if (i) {
                        var o = this.getFieldValue(i, "__typename"),
                            a = t && r ? this.policies.getStoreFieldName({
                                typename: o,
                                fieldName: t,
                                args: r
                            }) : t;
                        return this.modify(e, a ? ((n = {})[a] = S, n) : S)
                    }
                    return !1
                }, e.prototype.evict = function(e, t) {
                    var r = !1;
                    return e.id && (p.call(this.data, e.id) && (r = this.delete(e.id, e.fieldName, e.args)), this instanceof q && this !== t && (r = this.parent.evict(e, t) || r), (e.fieldName || r) && this.group.dirty(e.id, e.fieldName || "__exists")), r
                }, e.prototype.clear = function() {
                    this.replace(null)
                }, e.prototype.extract = function() {
                    var e = this,
                        t = this.toObject(),
                        r = [];
                    return this.getRootIdSet().forEach((function(t) {
                        p.call(e.policies.rootTypenamesById, t) || r.push(t)
                    })), r.length && (t.__META = {
                        extraRootIds: r.sort()
                    }), t
                }, e.prototype.replace = function(e) {
                    var t = this;
                    if (Object.keys(this.data).forEach((function(r) {
                            e && p.call(e, r) || t.delete(r)
                        })), e) {
                        var r = e.__META,
                            n = i.__rest(e, ["__META"]);
                        Object.keys(n).forEach((function(e) {
                            t.merge(e, n[e])
                        })), r && r.extraRootIds.forEach(this.retain, this)
                    }
                }, e.prototype.retain = function(e) {
                    return this.rootIds[e] = (this.rootIds[e] || 0) + 1
                }, e.prototype.release = function(e) {
                    if (this.rootIds[e] > 0) {
                        var t = --this.rootIds[e];
                        return t || delete this.rootIds[e], t
                    }
                    return 0
                }, e.prototype.getRootIdSet = function(e) {
                    return void 0 === e && (e = new Set), Object.keys(this.rootIds).forEach(e.add, e), this instanceof q ? this.parent.getRootIdSet(e) : Object.keys(this.policies.rootTypenamesById).forEach(e.add, e), e
                }, e.prototype.gc = function() {
                    var e = this,
                        t = this.getRootIdSet(),
                        r = this.toObject();
                    t.forEach((function(n) {
                        p.call(r, n) && (Object.keys(e.findChildRefIds(n)).forEach(t.add, t), delete r[n])
                    }));
                    var n = Object.keys(r);
                    if (n.length) {
                        for (var i = this; i instanceof q;) i = i.parent;
                        n.forEach((function(e) {
                            return i.delete(e)
                        }))
                    }
                    return n
                }, e.prototype.findChildRefIds = function(e) {
                    if (!p.call(this.refs, e)) {
                        var t = this.refs[e] = Object.create(null),
                            r = this.data[e];
                        if (!r) return t;
                        var n = new Set([r]);
                        n.forEach((function(e) {
                            a.isReference(e) && (t[e.__ref] = !0), a.isNonNullObject(e) && Object.keys(e).forEach((function(t) {
                                var r = e[t];
                                a.isNonNullObject(r) && n.add(r)
                            }))
                        }))
                    }
                    return this.refs[e]
                }, e.prototype.makeCacheKey = function() {
                    return this.group.keyMaker.lookupArray(arguments)
                }, e
            }();
            var R, D, P = function() {
                function e(e, t) {
                    void 0 === t && (t = null), this.caching = e, this.parent = t, this.d = null, this.resetCaching()
                }
                return e.prototype.resetCaching = function() {
                    this.d = this.caching ? o.dep() : null, this.keyMaker = new u.Trie(a.canUseWeakMap)
                }, e.prototype.depend = function(e, t) {
                    if (this.d) {
                        this.d(F(e, t));
                        var r = b(t);
                        r !== t && this.d(F(e, r)), this.parent && this.parent.depend(e, t)
                    }
                }, e.prototype.dirty = function(e, t) {
                    this.d && this.d.dirty(F(e, t), "__exists" === t ? "forget" : "setDirty")
                }, e
            }();

            function F(e, t) {
                return t + "#" + e
            }

            function T(e, t) {
                M(e) && e.group.depend(t, "__exists")
            }
            D = function(e) {
                function t(t) {
                    var r = t.policies,
                        n = t.resultCaching,
                        i = void 0 === n || n,
                        o = t.seed,
                        s = e.call(this, r, new P(i)) || this;
                    return s.stump = new C(s), s.storageTrie = new u.Trie(a.canUseWeakMap), o && s.replace(o), s
                }
                return i.__extends(t, e), t.prototype.addLayer = function(e, t) {
                    return this.stump.addLayer(e, t)
                }, t.prototype.removeLayer = function() {
                    return this
                }, t.prototype.getStorage = function() {
                    return this.storageTrie.lookupArray(arguments)
                }, t
            }(R = t.EntityStore || (t.EntityStore = {})), R.Root = D;
            var q = function(e) {
                    function t(t, r, n, i) {
                        var o = e.call(this, r.policies, i) || this;
                        return o.id = t, o.parent = r, o.replay = n, o.group = i, n(o), o
                    }
                    return i.__extends(t, e), t.prototype.addLayer = function(e, r) {
                        return new t(e, this, r, this.group)
                    }, t.prototype.removeLayer = function(e) {
                        var t = this,
                            r = this.parent.removeLayer(e);
                        return e === this.id ? (this.group.caching && Object.keys(this.data).forEach((function(e) {
                            var n = t.data[e],
                                i = r.lookup(e);
                            i ? n ? n !== i && Object.keys(n).forEach((function(r) {
                                s.equal(n[r], i[r]) || t.group.dirty(e, r)
                            })) : (t.group.dirty(e, "__exists"), Object.keys(i).forEach((function(r) {
                                t.group.dirty(e, r)
                            }))) : t.delete(e)
                        })), r) : r === this.parent ? this : r.addLayer(this.id, this.replay)
                    }, t.prototype.toObject = function() {
                        return i.__assign(i.__assign({}, this.parent.toObject()), this.data)
                    }, t.prototype.findChildRefIds = function(t) {
                        var r = this.parent.findChildRefIds(t);
                        return p.call(this.data, t) ? i.__assign(i.__assign({}, r), e.prototype.findChildRefIds.call(this, t)) : r
                    }, t.prototype.getStorage = function() {
                        for (var e = this.parent; e.parent;) e = e.parent;
                        return e.getStorage.apply(e, arguments)
                    }, t
                }(t.EntityStore),
                C = function(e) {
                    function t(t) {
                        return e.call(this, "EntityStore.Stump", t, (function() {}), new P(t.group.caching, t.group)) || this
                    }
                    return i.__extends(t, e), t.prototype.removeLayer = function() {
                        return this
                    }, t.prototype.merge = function() {
                        return this.parent.merge.apply(this.parent, arguments)
                    }, t
                }(q);

            function x(e, t, r) {
                var n = e[r],
                    i = t[r];
                return s.equal(n, i) ? n : i
            }

            function M(e) {
                return !!(e instanceof t.EntityStore && e.group.caching)
            }
            var Q, j, N = function() {
                    function e() {
                        this.known = new(a.canUseWeakSet ? WeakSet : Set), this.pool = new u.Trie(a.canUseWeakMap), this.passes = new WeakMap, this.keysByJSON = new Map, this.empty = this.admit({})
                    }
                    return e.prototype.isKnown = function(e) {
                        return a.isNonNullObject(e) && this.known.has(e)
                    }, e.prototype.pass = function(e) {
                        if (a.isNonNullObject(e)) {
                            var t = function(e) {
                                return a.isNonNullObject(e) ? a.isArray(e) ? e.slice(0) : i.__assign({
                                    __proto__: Object.getPrototypeOf(e)
                                }, e) : e
                            }(e);
                            return this.passes.set(t, e), t
                        }
                        return e
                    }, e.prototype.admit = function(e) {
                        var t = this;
                        if (a.isNonNullObject(e)) {
                            var r = this.passes.get(e);
                            if (r) return r;
                            switch (Object.getPrototypeOf(e)) {
                                case Array.prototype:
                                    if (this.known.has(e)) return e;
                                    var n = e.map(this.admit, this);
                                    return (u = this.pool.lookupArray(n)).array || (this.known.add(u.array = n), !1 !== globalThis.__DEV__ && Object.freeze(n)), u.array;
                                case null:
                                case Object.prototype:
                                    if (this.known.has(e)) return e;
                                    var i = Object.getPrototypeOf(e),
                                        o = [i],
                                        s = this.sortedKeys(e);
                                    o.push(s.json);
                                    var u, c = o.length;
                                    if (s.sorted.forEach((function(r) {
                                            o.push(t.admit(e[r]))
                                        })), !(u = this.pool.lookupArray(o)).object) {
                                        var l = u.object = Object.create(i);
                                        this.known.add(l), s.sorted.forEach((function(e, t) {
                                            l[e] = o[c + t]
                                        })), !1 !== globalThis.__DEV__ && Object.freeze(l)
                                    }
                                    return u.object
                            }
                        }
                        return e
                    }, e.prototype.sortedKeys = function(e) {
                        var t = Object.keys(e),
                            r = this.pool.lookupArray(t);
                        if (!r.keys) {
                            t.sort();
                            var n = JSON.stringify(t);
                            (r.keys = this.keysByJSON.get(n)) || this.keysByJSON.set(n, r.keys = {
                                sorted: t,
                                json: n
                            })
                        }
                        return r.keys
                    }, e
                }(),
                I = Object.assign((function(e) {
                    if (a.isNonNullObject(e)) {
                        void 0 === Q && A();
                        var t = Q.admit(e),
                            r = j.get(t);
                        return void 0 === r && j.set(t, r = JSON.stringify(t)), r
                    }
                    return JSON.stringify(e)
                }), {
                    reset: A
                });

            function A() {
                Q = new N, j = new(a.canUseWeakMap ? WeakMap : Map)
            }

            function L(e) {
                return [e.selectionSet, e.objectOrReference, e.context, e.context.canonizeResults]
            }
            var V = function() {
                function e(e) {
                    var t = this;
                    this.knownResults = new(a.canUseWeakMap ? WeakMap : Map), this.config = a.compact(e, {
                        addTypename: !1 !== e.addTypename,
                        canonizeResults: m(e)
                    }), this.canon = e.canon || new N, this.executeSelectionSet = o.wrap((function(e) {
                        var r, n = e.context.canonizeResults,
                            o = L(e);
                        o[3] = !n;
                        var a = (r = t.executeSelectionSet).peek.apply(r, o);
                        return a ? n ? i.__assign(i.__assign({}, a), {
                            result: t.canon.admit(a.result)
                        }) : a : (T(e.context.store, e.enclosingRef.__ref), t.execSelectionSetImpl(e))
                    }), {
                        max: this.config.resultCacheMaxSize,
                        keyArgs: L,
                        makeCacheKey: function(e, t, r, n) {
                            if (M(r.store)) return r.store.makeCacheKey(e, a.isReference(t) ? t.__ref : t, r.varString, n)
                        }
                    }), this.executeSubSelectedArray = o.wrap((function(e) {
                        return T(e.context.store, e.enclosingRef.__ref), t.execSubSelectedArrayImpl(e)
                    }), {
                        max: this.config.resultCacheMaxSize,
                        makeCacheKey: function(e) {
                            var t = e.field,
                                r = e.array,
                                n = e.context;
                            if (M(n.store)) return n.store.makeCacheKey(t, r, n.varString)
                        }
                    })
                }
                return e.prototype.resetCanon = function() {
                    this.canon = new N
                }, e.prototype.diffQueryAgainstStore = function(e) {
                    var t = e.store,
                        r = e.query,
                        n = e.rootId,
                        o = void 0 === n ? "ROOT_QUERY" : n,
                        s = e.variables,
                        u = e.returnPartialData,
                        c = void 0 === u || u,
                        l = e.canonizeResults,
                        f = void 0 === l ? this.config.canonizeResults : l,
                        p = this.config.cache.policies;
                    s = i.__assign(i.__assign({}, a.getDefaultValues(a.getQueryDefinition(r))), s);
                    var d, y = a.makeReference(o),
                        v = this.executeSelectionSet({
                            selectionSet: a.getMainDefinition(r).selectionSet,
                            objectOrReference: y,
                            enclosingRef: y,
                            context: i.__assign({
                                store: t,
                                query: r,
                                policies: p,
                                variables: s,
                                varString: I(s),
                                canonizeResults: f
                            }, O(r, this.config.fragments))
                        });
                    if (v.missing && (d = [new h(z(v.missing), v.missing, r, s)], !c)) throw d[0];
                    return {
                        result: v.result,
                        complete: !d,
                        missing: d
                    }
                }, e.prototype.isFresh = function(e, t, r, n) {
                    if (M(n.store) && this.knownResults.get(e) === r) {
                        var i = this.executeSelectionSet.peek(r, t, n, this.canon.isKnown(e));
                        if (i && e === i.result) return !0
                    }
                    return !1
                }, e.prototype.execSelectionSetImpl = function(e) {
                    var t = this,
                        r = e.selectionSet,
                        i = e.objectOrReference,
                        o = e.enclosingRef,
                        s = e.context;
                    if (a.isReference(i) && !s.policies.rootTypenamesById[i.__ref] && !s.store.has(i.__ref)) return {
                        result: this.canon.empty,
                        missing: "Dangling reference to missing ".concat(i.__ref, " object")
                    };
                    var u, l = s.variables,
                        f = s.policies,
                        h = s.store.getFieldValue(i, "__typename"),
                        p = [],
                        d = new a.DeepMerger;

                    function y(e, t) {
                        var r;
                        return e.missing && (u = d.merge(u, ((r = {})[t] = e.missing, r))), e.result
                    }
                    this.config.addTypename && "string" == typeof h && !f.rootIdsByTypename[h] && p.push({
                        __typename: h
                    });
                    var v = new Set(r.selections);
                    v.forEach((function(e) {
                        var r, m;
                        if (a.shouldInclude(e, l))
                            if (a.isField(e)) {
                                var g = f.readField({
                                        fieldName: e.name.value,
                                        field: e,
                                        variables: s.variables,
                                        from: i
                                    }, s),
                                    b = a.resultKeyNameFromField(e);
                                void 0 === g ? a.addTypenameToDocument.added(e) || (u = d.merge(u, ((r = {})[b] = "Can't find field '".concat(e.name.value, "' on ").concat(a.isReference(i) ? i.__ref + " object" : "object " + JSON.stringify(i, null, 2)), r))) : a.isArray(g) ? g = y(t.executeSubSelectedArray({
                                    field: e,
                                    array: g,
                                    enclosingRef: o,
                                    context: s
                                }), b) : e.selectionSet ? null != g && (g = y(t.executeSelectionSet({
                                    selectionSet: e.selectionSet,
                                    objectOrReference: g,
                                    enclosingRef: a.isReference(g) ? g : o,
                                    context: s
                                }), b)) : s.canonizeResults && (g = t.canon.pass(g)), void 0 !== g && p.push(((m = {})[b] = g, m))
                            } else {
                                var _ = a.getFragmentFromSelection(e, s.lookupFragment);
                                if (!_ && e.kind === c.Kind.FRAGMENT_SPREAD) throw n.newInvariantError(7, e.name.value);
                                _ && f.fragmentMatches(_, h) && _.selectionSet.selections.forEach(v.add, v)
                            }
                    }));
                    var m = {
                            result: a.mergeDeepArray(p),
                            missing: u
                        },
                        g = s.canonizeResults ? this.canon.admit(m) : a.maybeDeepFreeze(m);
                    return g.result && this.knownResults.set(g.result, r), g
                }, e.prototype.execSubSelectedArrayImpl = function(e) {
                    var t, r = this,
                        i = e.field,
                        o = e.array,
                        s = e.enclosingRef,
                        u = e.context,
                        c = new a.DeepMerger;

                    function l(e, r) {
                        var n;
                        return e.missing && (t = c.merge(t, ((n = {})[r] = e.missing, n))), e.result
                    }
                    return i.selectionSet && (o = o.filter(u.store.canRead)), o = o.map((function(e, t) {
                        return null === e ? null : a.isArray(e) ? l(r.executeSubSelectedArray({
                            field: i,
                            array: e,
                            enclosingRef: s,
                            context: u
                        }), t) : i.selectionSet ? l(r.executeSelectionSet({
                            selectionSet: i.selectionSet,
                            objectOrReference: e,
                            enclosingRef: a.isReference(e) ? e : s,
                            context: u
                        }), t) : (!1 !== globalThis.__DEV__ && function(e, t, r) {
                            if (!t.selectionSet) {
                                var i = new Set([r]);
                                i.forEach((function(r) {
                                    a.isNonNullObject(r) && (n.invariant(!a.isReference(r), 8, function(e, t) {
                                        return a.isReference(t) ? e.get(t.__ref, "__typename") : t && t.__typename
                                    }(e, r), t.name.value), Object.values(r).forEach(i.add, i))
                                }))
                            }
                        }(u.store, i, e), e)
                    })), {
                        result: u.canonizeResults ? this.canon.admit(o) : o,
                        missing: t
                    }
                }, e
            }();

            function z(e) {
                try {
                    JSON.stringify(e, (function(e, t) {
                        if ("string" == typeof t) throw t;
                        return t
                    }))
                } catch (e) {
                    return e
                }
            }
            var W = new l.Slot,
                U = new WeakMap;

            function B(e) {
                var t = U.get(e);
                return t || U.set(e, t = {
                    vars: new Set,
                    dep: o.dep()
                }), t
            }

            function K(e) {
                B(e).vars.forEach((function(t) {
                    return t.forgetCache(e)
                }))
            }

            function H(e) {
                var t = new Set,
                    r = new Set,
                    n = function(o) {
                        if (arguments.length > 0) {
                            if (e !== o) {
                                e = o, t.forEach((function(e) {
                                    B(e).dep.dirty(n),
                                        function(e) {
                                            e.broadcastWatches && e.broadcastWatches()
                                        }(e)
                                }));
                                var a = Array.from(r);
                                r.clear(), a.forEach((function(t) {
                                    return t(e)
                                }))
                            }
                        } else {
                            var s = W.getValue();
                            s && (i(s), B(s).dep(n))
                        }
                        return e
                    };
                n.onNextChange = function(e) {
                    return r.add(e),
                        function() {
                            r.delete(e)
                        }
                };
                var i = n.attachCache = function(e) {
                    return t.add(e), B(e).vars.add(n), n
                };
                return n.forgetCache = function(e) {
                    return t.delete(e)
                }, n
            }
            var G = Object.create(null);

            function J(e) {
                var t = JSON.stringify(e);
                return G[t] || (G[t] = Object.create(null))
            }

            function Y(e) {
                var t = J(e);
                return t.keyFieldsFn || (t.keyFieldsFn = function(t, r) {
                    var i = function(e, t) {
                            return r.readField(t, e)
                        },
                        o = r.keyObject = $(e, (function(e) {
                            var o = te(r.storeObject, e, i);
                            return void 0 === o && t !== r.storeObject && p.call(t, e[0]) && (o = te(t, e, ee)), n.invariant(void 0 !== o, 2, e.join("."), t), o
                        }));
                    return "".concat(r.typename, ":").concat(JSON.stringify(o))
                })
            }

            function X(e) {
                var t = J(e);
                return t.keyArgsFn || (t.keyArgsFn = function(t, r) {
                    var n = r.field,
                        i = r.variables,
                        o = r.fieldName,
                        s = $(e, (function(e) {
                            var r = e[0],
                                o = r.charAt(0);
                            if ("@" !== o)
                                if ("$" !== o) {
                                    if (t) return te(t, e)
                                } else {
                                    var s = r.slice(1);
                                    if (i && p.call(i, s)) {
                                        var u = e.slice(0);
                                        return u[0] = s, te(i, u)
                                    }
                                }
                            else if (n && a.isNonEmptyArray(n.directives)) {
                                var c = r.slice(1),
                                    l = n.directives.find((function(e) {
                                        return e.name.value === c
                                    })),
                                    f = l && a.argumentsObjectFromField(l, i);
                                return f && te(f, e.slice(1))
                            }
                        })),
                        u = JSON.stringify(s);
                    return (t || "{}" !== u) && (o += ":" + u), o
                })
            }

            function $(e, t) {
                var r = new a.DeepMerger;
                return Z(e).reduce((function(e, n) {
                    var i, o = t(n);
                    if (void 0 !== o) {
                        for (var a = n.length - 1; a >= 0; --a)(i = {})[n[a]] = o, o = i;
                        e = r.merge(e, o)
                    }
                    return e
                }), Object.create(null))
            }

            function Z(e) {
                var t = J(e);
                if (!t.paths) {
                    var r = t.paths = [],
                        n = [];
                    e.forEach((function(t, i) {
                        a.isArray(t) ? (Z(t).forEach((function(e) {
                            return r.push(n.concat(e))
                        })), n.length = 0) : (n.push(t), a.isArray(e[i + 1]) || (r.push(n.slice(0)), n.length = 0))
                    }))
                }
                return t.paths
            }

            function ee(e, t) {
                return e[t]
            }

            function te(e, t, r) {
                return r = r || ee, re(t.reduce((function e(t, n) {
                    return a.isArray(t) ? t.map((function(t) {
                        return e(t, n)
                    })) : t && r(t, n)
                }), e))
            }

            function re(e) {
                return a.isNonNullObject(e) ? a.isArray(e) ? e.map(re) : $(Object.keys(e).sort(), (function(t) {
                    return te(e, t)
                })) : e
            }

            function ne(e) {
                return void 0 !== e.args ? e.args : e.field ? a.argumentsObjectFromField(e.field, e.variables) : null
            }
            a.getStoreKeyName.setStringify(I);
            var ie = function() {},
                oe = function(e, t) {
                    return t.fieldName
                },
                ae = function(e, t, r) {
                    return (0, r.mergeObjects)(e, t)
                },
                se = function(e, t) {
                    return t
                },
                ue = function() {
                    function e(e) {
                        this.config = e, this.typePolicies = Object.create(null), this.toBeAdded = Object.create(null), this.supertypeMap = new Map, this.fuzzySubtypes = new Map, this.rootIdsByTypename = Object.create(null), this.rootTypenamesById = Object.create(null), this.usingPossibleTypes = !1, this.config = i.__assign({
                            dataIdFromObject: y
                        }, e), this.cache = this.config.cache, this.setRootTypename("Query"), this.setRootTypename("Mutation"), this.setRootTypename("Subscription"), e.possibleTypes && this.addPossibleTypes(e.possibleTypes), e.typePolicies && this.addTypePolicies(e.typePolicies)
                    }
                    return e.prototype.identify = function(e, t) {
                        var r, n = this,
                            o = t && (t.typename || (null === (r = t.storeObject) || void 0 === r ? void 0 : r.__typename)) || e.__typename;
                        if (o === this.rootTypenamesById.ROOT_QUERY) return ["ROOT_QUERY"];
                        for (var s, u = t && t.storeObject || e, c = i.__assign(i.__assign({}, t), {
                                typename: o,
                                storeObject: u,
                                readField: t && t.readField || function() {
                                    var e = le(arguments, u);
                                    return n.readField(e, {
                                        store: n.cache.data,
                                        variables: e.variables
                                    })
                                }
                            }), l = o && this.getTypePolicy(o), f = l && l.keyFn || this.config.dataIdFromObject; f;) {
                            var h = f(i.__assign(i.__assign({}, e), u), c);
                            if (!a.isArray(h)) {
                                s = h;
                                break
                            }
                            f = Y(h)
                        }
                        return s = s ? String(s) : void 0, c.keyObject ? [s, c.keyObject] : [s]
                    }, e.prototype.addTypePolicies = function(e) {
                        var t = this;
                        Object.keys(e).forEach((function(r) {
                            var n = e[r],
                                o = n.queryType,
                                a = n.mutationType,
                                s = n.subscriptionType,
                                u = i.__rest(n, ["queryType", "mutationType", "subscriptionType"]);
                            o && t.setRootTypename("Query", r), a && t.setRootTypename("Mutation", r), s && t.setRootTypename("Subscription", r), p.call(t.toBeAdded, r) ? t.toBeAdded[r].push(u) : t.toBeAdded[r] = [u]
                        }))
                    }, e.prototype.updateTypePolicy = function(e, t) {
                        var r = this,
                            n = this.getTypePolicy(e),
                            i = t.keyFields,
                            o = t.fields;

                        function s(e, t) {
                            e.merge = "function" == typeof t ? t : !0 === t ? ae : !1 === t ? se : e.merge
                        }
                        s(n, t.merge), n.keyFn = !1 === i ? ie : a.isArray(i) ? Y(i) : "function" == typeof i ? i : n.keyFn, o && Object.keys(o).forEach((function(t) {
                            var n = r.getFieldPolicy(e, t, !0),
                                i = o[t];
                            if ("function" == typeof i) n.read = i;
                            else {
                                var u = i.keyArgs,
                                    c = i.read,
                                    l = i.merge;
                                n.keyFn = !1 === u ? oe : a.isArray(u) ? X(u) : "function" == typeof u ? u : n.keyFn, "function" == typeof c && (n.read = c), s(n, l)
                            }
                            n.read && n.merge && (n.keyFn = n.keyFn || oe)
                        }))
                    }, e.prototype.setRootTypename = function(e, t) {
                        void 0 === t && (t = e);
                        var r = "ROOT_" + e.toUpperCase(),
                            i = this.rootTypenamesById[r];
                        t !== i && (n.invariant(!i || i === e, 3, e), i && delete this.rootIdsByTypename[i], this.rootIdsByTypename[t] = r, this.rootTypenamesById[r] = t)
                    }, e.prototype.addPossibleTypes = function(e) {
                        var t = this;
                        this.usingPossibleTypes = !0, Object.keys(e).forEach((function(r) {
                            t.getSupertypeSet(r, !0), e[r].forEach((function(e) {
                                t.getSupertypeSet(e, !0).add(r);
                                var n = e.match(g);
                                n && n[0] === e || t.fuzzySubtypes.set(e, new RegExp(e))
                            }))
                        }))
                    }, e.prototype.getTypePolicy = function(e) {
                        var t = this;
                        if (!p.call(this.typePolicies, e)) {
                            var r = this.typePolicies[e] = Object.create(null);
                            r.fields = Object.create(null);
                            var n = this.supertypeMap.get(e);
                            !n && this.fuzzySubtypes.size && (n = this.getSupertypeSet(e, !0), this.fuzzySubtypes.forEach((function(r, i) {
                                if (r.test(e)) {
                                    var o = t.supertypeMap.get(i);
                                    o && o.forEach((function(e) {
                                        return n.add(e)
                                    }))
                                }
                            }))), n && n.size && n.forEach((function(e) {
                                var n = t.getTypePolicy(e),
                                    o = n.fields,
                                    a = i.__rest(n, ["fields"]);
                                Object.assign(r, a), Object.assign(r.fields, o)
                            }))
                        }
                        var o = this.toBeAdded[e];
                        return o && o.length && o.splice(0).forEach((function(r) {
                            t.updateTypePolicy(e, r)
                        })), this.typePolicies[e]
                    }, e.prototype.getFieldPolicy = function(e, t, r) {
                        if (e) {
                            var n = this.getTypePolicy(e).fields;
                            return n[t] || r && (n[t] = Object.create(null))
                        }
                    }, e.prototype.getSupertypeSet = function(e, t) {
                        var r = this.supertypeMap.get(e);
                        return !r && t && this.supertypeMap.set(e, r = new Set), r
                    }, e.prototype.fragmentMatches = function(e, t, r, i) {
                        var o = this;
                        if (!e.typeCondition) return !0;
                        if (!t) return !1;
                        var a = e.typeCondition.name.value;
                        if (t === a) return !0;
                        if (this.usingPossibleTypes && this.supertypeMap.has(a))
                            for (var s = this.getSupertypeSet(t, !0), u = [s], c = function(e) {
                                    var t = o.getSupertypeSet(e, !1);
                                    t && t.size && u.indexOf(t) < 0 && u.push(t)
                                }, l = !(!r || !this.fuzzySubtypes.size), f = !1, h = 0; h < u.length; ++h) {
                                var p = u[h];
                                if (p.has(a)) return s.has(a) || (f && !1 !== globalThis.__DEV__ && n.invariant.warn(4, t, a), s.add(a)), !0;
                                p.forEach(c), l && h === u.length - 1 && _(e.selectionSet, r, i) && (l = !1, f = !0, this.fuzzySubtypes.forEach((function(e, r) {
                                    var n = t.match(e);
                                    n && n[0] === t && c(r)
                                })))
                            }
                        return !1
                    }, e.prototype.hasKeyArgs = function(e, t) {
                        var r = this.getFieldPolicy(e, t, !1);
                        return !(!r || !r.keyFn)
                    }, e.prototype.getStoreFieldName = function(e) {
                        var t, r = e.typename,
                            n = e.fieldName,
                            i = this.getFieldPolicy(r, n, !1),
                            o = i && i.keyFn;
                        if (o && r)
                            for (var s = {
                                    typename: r,
                                    fieldName: n,
                                    field: e.field || null,
                                    variables: e.variables
                                }, u = ne(e); o;) {
                                var c = o(u, s);
                                if (!a.isArray(c)) {
                                    t = c || n;
                                    break
                                }
                                o = X(c)
                            }
                        return void 0 === t && (t = e.field ? a.storeKeyNameFromField(e.field, e.variables) : a.getStoreKeyName(n, ne(e))), !1 === t ? n : n === b(t) ? t : n + ":" + t
                    }, e.prototype.readField = function(e, t) {
                        var r = e.from;
                        if (r && (e.field || e.fieldName)) {
                            if (void 0 === e.typename) {
                                var n = t.store.getFieldValue(r, "__typename");
                                n && (e.typename = n)
                            }
                            var i = this.getStoreFieldName(e),
                                o = b(i),
                                s = t.store.getFieldValue(r, i),
                                u = this.getFieldPolicy(e.typename, o, !1),
                                c = u && u.read;
                            if (c) {
                                var l = ce(this, r, e, t, t.store.getStorage(a.isReference(r) ? r.__ref : r, i));
                                return W.withValue(this.cache, c, [s, l])
                            }
                            return s
                        }
                    }, e.prototype.getReadFunction = function(e, t) {
                        var r = this.getFieldPolicy(e, t, !1);
                        return r && r.read
                    }, e.prototype.getMergeFunction = function(e, t, r) {
                        var n = this.getFieldPolicy(e, t, !1),
                            i = n && n.merge;
                        return !i && r && (i = (n = this.getTypePolicy(r)) && n.merge), i
                    }, e.prototype.runMergeFunction = function(e, t, r, n, i) {
                        var o = r.field,
                            a = r.typename,
                            s = r.merge;
                        return s === ae ? fe(n.store)(e, t) : s === se ? t : (n.overwrite && (e = void 0), s(e, t, ce(this, void 0, {
                            typename: a,
                            fieldName: o.name.value,
                            field: o,
                            variables: n.variables
                        }, n, i || Object.create(null))))
                    }, e
                }();

            function ce(e, t, r, n, i) {
                var o = e.getStoreFieldName(r),
                    s = b(o),
                    u = r.variables || n.variables,
                    c = n.store,
                    l = c.toReference,
                    f = c.canRead;
                return {
                    args: ne(r),
                    field: r.field || null,
                    fieldName: s,
                    storeFieldName: o,
                    variables: u,
                    isReference: a.isReference,
                    toReference: l,
                    storage: i,
                    cache: e.cache,
                    canRead: f,
                    readField: function() {
                        return e.readField(le(arguments, t, u), n)
                    },
                    mergeObjects: fe(n.store)
                }
            }

            function le(e, t, r) {
                var o, s = e[0],
                    u = e[1],
                    c = e.length;
                return "string" == typeof s ? o = {
                    fieldName: s,
                    from: c > 1 ? u : t
                } : (o = i.__assign({}, s), p.call(o, "from") || (o.from = t)), !1 !== globalThis.__DEV__ && void 0 === o.from && !1 !== globalThis.__DEV__ && n.invariant.warn(5, a.stringifyForDisplay(Array.from(e))), void 0 === o.variables && (o.variables = r), o
            }

            function fe(e) {
                return function(t, r) {
                    if (a.isArray(t) || a.isArray(r)) throw n.newInvariantError(6);
                    if (a.isNonNullObject(t) && a.isNonNullObject(r)) {
                        var o = e.getFieldValue(t, "__typename"),
                            s = e.getFieldValue(r, "__typename");
                        if (o && s && o !== s) return r;
                        if (a.isReference(t) && w(r)) return e.merge(t.__ref, r), t;
                        if (w(t) && a.isReference(r)) return e.merge(t, r.__ref), r;
                        if (w(t) && w(r)) return i.__assign(i.__assign({}, t), r)
                    }
                    return r
                }
            }

            function he(e, t, r) {
                var n = "".concat(t).concat(r),
                    o = e.flavors.get(n);
                return o || e.flavors.set(n, o = e.clientOnly === t && e.deferred === r ? e : i.__assign(i.__assign({}, e), {
                    clientOnly: t,
                    deferred: r
                })), o
            }
            var pe = function() {
                    function e(e, t, r) {
                        this.cache = e, this.reader = t, this.fragments = r
                    }
                    return e.prototype.writeToStore = function(e, t) {
                        var r = this,
                            o = t.query,
                            u = t.result,
                            c = t.dataId,
                            l = t.variables,
                            f = t.overwrite,
                            h = a.getOperationDefinition(o),
                            p = new a.DeepMerger;
                        l = i.__assign(i.__assign({}, a.getDefaultValues(h)), l);
                        var d = i.__assign(i.__assign({
                                store: e,
                                written: Object.create(null),
                                merge: function(e, t) {
                                    return p.merge(e, t)
                                },
                                variables: l,
                                varString: I(l)
                            }, O(o, this.fragments)), {
                                overwrite: !!f,
                                incomingById: new Map,
                                clientOnly: !1,
                                deferred: !1,
                                flavors: new Map
                            }),
                            y = this.processSelectionSet({
                                result: u || Object.create(null),
                                dataId: c,
                                selectionSet: h.selectionSet,
                                mergeTree: {
                                    map: new Map
                                },
                                context: d
                            });
                        if (!a.isReference(y)) throw n.newInvariantError(9, u);
                        return d.incomingById.forEach((function(t, i) {
                            var o = t.storeObject,
                                u = t.mergeTree,
                                c = t.fieldNodeSet,
                                l = a.makeReference(i);
                            if (u && u.map.size) {
                                var f = r.applyMerges(u, l, o, d);
                                if (a.isReference(f)) return;
                                o = f
                            }
                            if (!1 !== globalThis.__DEV__ && !d.overwrite) {
                                var h = Object.create(null);
                                c.forEach((function(e) {
                                    e.selectionSet && (h[e.name.value] = !0)
                                })), Object.keys(o).forEach((function(e) {
                                    (function(e) {
                                        return !0 === h[b(e)]
                                    })(e) && ! function(e) {
                                        var t = u && u.map.get(e);
                                        return Boolean(t && t.info && t.info.merge)
                                    }(e) && function(e, t, r, i) {
                                        var o = function(e) {
                                                var t = i.getFieldValue(e, r);
                                                return "object" == typeof t && t
                                            },
                                            u = o(e);
                                        if (u) {
                                            var c = o(t);
                                            if (c && !a.isReference(u) && !s.equal(u, c) && !Object.keys(u).every((function(e) {
                                                    return void 0 !== i.getFieldValue(c, e)
                                                }))) {
                                                var l = i.getFieldValue(e, "__typename") || i.getFieldValue(t, "__typename"),
                                                    f = b(r),
                                                    h = "".concat(l, ".").concat(f);
                                                if (!be.has(h)) {
                                                    be.add(h);
                                                    var p = [];
                                                    a.isArray(u) || a.isArray(c) || [u, c].forEach((function(e) {
                                                        var t = i.getFieldValue(e, "__typename");
                                                        "string" != typeof t || p.includes(t) || p.push(t)
                                                    })), !1 !== globalThis.__DEV__ && n.invariant.warn(12, f, l, p.length ? "either ensure all objects of type " + p.join(" and ") + " have an ID or a custom merge function, or " : "", h, u, c)
                                                }
                                            }
                                        }
                                    }(l, o, e, d.store)
                                }))
                            }
                            e.merge(i, o)
                        })), e.retain(y.__ref), y
                    }, e.prototype.processSelectionSet = function(e) {
                        var t = this,
                            r = e.dataId,
                            o = e.result,
                            s = e.selectionSet,
                            u = e.context,
                            c = e.mergeTree,
                            l = this.cache.policies,
                            f = Object.create(null),
                            h = r && l.rootTypenamesById[r] || a.getTypenameFromResult(o, s, u.fragmentMap) || r && u.store.get(r, "__typename");
                        "string" == typeof h && (f.__typename = h);
                        var p = function() {
                                var e = le(arguments, f, u.variables);
                                if (a.isReference(e.from)) {
                                    var t = u.incomingById.get(e.from.__ref);
                                    if (t) {
                                        var r = l.readField(i.__assign(i.__assign({}, e), {
                                            from: t.storeObject
                                        }), u);
                                        if (void 0 !== r) return r
                                    }
                                }
                                return l.readField(e, u)
                            },
                            d = new Set;
                        this.flattenFields(s, o, u, h).forEach((function(e, r) {
                            var i, s = a.resultKeyNameFromField(r),
                                u = o[s];
                            if (d.add(r), void 0 !== u) {
                                var y = l.getStoreFieldName({
                                        typename: h,
                                        fieldName: r.name.value,
                                        field: r,
                                        variables: e.variables
                                    }),
                                    v = ye(c, y),
                                    m = t.processFieldValue(u, r, r.selectionSet ? he(e, !1, !1) : e, v),
                                    g = void 0;
                                r.selectionSet && (a.isReference(m) || w(m)) && (g = p("__typename", m));
                                var b = l.getMergeFunction(h, r.name.value, g);
                                b ? v.info = {
                                    field: r,
                                    typename: h,
                                    merge: b
                                } : ge(c, y), f = e.merge(f, ((i = {})[y] = m, i))
                            } else !1 === globalThis.__DEV__ || e.clientOnly || e.deferred || a.addTypenameToDocument.added(r) || l.getReadFunction(h, r.name.value) || !1 !== globalThis.__DEV__ && n.invariant.error(10, a.resultKeyNameFromField(r), o)
                        }));
                        try {
                            var y = l.identify(o, {
                                    typename: h,
                                    selectionSet: s,
                                    fragmentMap: u.fragmentMap,
                                    storeObject: f,
                                    readField: p
                                }),
                                v = y[0],
                                m = y[1];
                            r = r || v, m && (f = u.merge(f, m))
                        } catch (e) {
                            if (!r) throw e
                        }
                        if ("string" == typeof r) {
                            var g = a.makeReference(r),
                                b = u.written[r] || (u.written[r] = []);
                            if (b.indexOf(s) >= 0) return g;
                            if (b.push(s), this.reader && this.reader.isFresh(o, g, s, u)) return g;
                            var _ = u.incomingById.get(r);
                            return _ ? (_.storeObject = u.merge(_.storeObject, f), _.mergeTree = ve(_.mergeTree, c), d.forEach((function(e) {
                                return _.fieldNodeSet.add(e)
                            }))) : u.incomingById.set(r, {
                                storeObject: f,
                                mergeTree: me(c) ? void 0 : c,
                                fieldNodeSet: d
                            }), g
                        }
                        return f
                    }, e.prototype.processFieldValue = function(e, t, r, n) {
                        var i = this;
                        return t.selectionSet && null !== e ? a.isArray(e) ? e.map((function(e, o) {
                            var a = i.processFieldValue(e, t, r, ye(n, o));
                            return ge(n, o), a
                        })) : this.processSelectionSet({
                            result: e,
                            selectionSet: t.selectionSet,
                            context: r,
                            mergeTree: n
                        }) : !1 !== globalThis.__DEV__ ? a.cloneDeep(e) : e
                    }, e.prototype.flattenFields = function(e, t, r, i) {
                        void 0 === i && (i = a.getTypenameFromResult(t, e, r.fragmentMap));
                        var o = new Map,
                            s = this.cache.policies,
                            l = new u.Trie(!1);
                        return function e(u, f) {
                            var h = l.lookup(u, f.clientOnly, f.deferred);
                            h.visited || (h.visited = !0, u.selections.forEach((function(u) {
                                if (a.shouldInclude(u, r.variables)) {
                                    var l = f.clientOnly,
                                        h = f.deferred;
                                    if (l && h || !a.isNonEmptyArray(u.directives) || u.directives.forEach((function(e) {
                                            var t = e.name.value;
                                            if ("client" === t && (l = !0), "defer" === t) {
                                                var n = a.argumentsObjectFromField(e, r.variables);
                                                n && !1 === n.if || (h = !0)
                                            }
                                        })), a.isField(u)) {
                                        var p = o.get(u);
                                        p && (l = l && p.clientOnly, h = h && p.deferred), o.set(u, he(r, l, h))
                                    } else {
                                        var d = a.getFragmentFromSelection(u, r.lookupFragment);
                                        if (!d && u.kind === c.Kind.FRAGMENT_SPREAD) throw n.newInvariantError(11, u.name.value);
                                        d && s.fragmentMatches(d, i, t, r.variables) && e(d.selectionSet, he(r, l, h))
                                    }
                                }
                            })))
                        }(e, r), o
                    }, e.prototype.applyMerges = function(e, t, r, o, s) {
                        var u, c = this;
                        if (e.map.size && !a.isReference(r)) {
                            var l, f = a.isArray(r) || !a.isReference(t) && !w(t) ? void 0 : t,
                                h = r;
                            f && !s && (s = [a.isReference(f) ? f.__ref : f]);
                            var p = function(e, t) {
                                return a.isArray(e) ? "number" == typeof t ? e[t] : void 0 : o.store.getFieldValue(e, String(t))
                            };
                            e.map.forEach((function(e, t) {
                                var r = p(f, t),
                                    i = p(h, t);
                                if (void 0 !== i) {
                                    s && s.push(t);
                                    var a = c.applyMerges(e, r, i, o, s);
                                    a !== i && (l = l || new Map).set(t, a), s && n.invariant(s.pop() === t)
                                }
                            })), l && (r = a.isArray(h) ? h.slice(0) : i.__assign({}, h), l.forEach((function(e, t) {
                                r[t] = e
                            })))
                        }
                        return e.info ? this.cache.policies.runMergeFunction(t, r, e.info, o, s && (u = o.store).getStorage.apply(u, s)) : r
                    }, e
                }(),
                de = [];

            function ye(e, t) {
                var r = e.map;
                return r.has(t) || r.set(t, de.pop() || {
                    map: new Map
                }), r.get(t)
            }

            function ve(e, t) {
                if (e === t || !t || me(t)) return e;
                if (!e || me(e)) return t;
                var r = e.info && t.info ? i.__assign(i.__assign({}, e.info), t.info) : e.info || t.info,
                    n = e.map.size && t.map.size,
                    o = {
                        info: r,
                        map: n ? new Map : e.map.size ? e.map : t.map
                    };
                if (n) {
                    var a = new Set(t.map.keys());
                    e.map.forEach((function(e, r) {
                        o.map.set(r, ve(e, t.map.get(r))), a.delete(r)
                    })), a.forEach((function(r) {
                        o.map.set(r, ve(t.map.get(r), e.map.get(r)))
                    }))
                }
                return o
            }

            function me(e) {
                return !e || !(e.info || e.map.size)
            }

            function ge(e, t) {
                var r = e.map,
                    n = r.get(t);
                n && me(n) && (de.push(n), r.delete(t))
            }
            var be = new Set,
                _e = function(e) {
                    function r(t) {
                        void 0 === t && (t = {});
                        var r = e.call(this) || this;
                        return r.watches = new Set, r.addTypenameTransform = new a.DocumentTransform(a.addTypenameToDocument), r.assumeImmutableResults = !0, r.makeVar = H, r.txCount = 0, r.config = function(e) {
                            return a.compact(v, e)
                        }(t), r.addTypename = !!r.config.addTypename, r.policies = new ue({
                            cache: r,
                            dataIdFromObject: r.config.dataIdFromObject,
                            possibleTypes: r.config.possibleTypes,
                            typePolicies: r.config.typePolicies
                        }), r.init(), r
                    }
                    return i.__extends(r, e), r.prototype.init = function() {
                        var e = this.data = new t.EntityStore.Root({
                            policies: this.policies,
                            resultCaching: this.config.resultCaching
                        });
                        this.optimisticData = e.stump, this.resetResultCache()
                    }, r.prototype.resetResultCache = function(e) {
                        var t = this,
                            r = this.storeReader,
                            n = this.config.fragments;
                        this.storeWriter = new pe(this, this.storeReader = new V({
                            cache: this,
                            addTypename: this.addTypename,
                            resultCacheMaxSize: this.config.resultCacheMaxSize,
                            canonizeResults: m(this.config),
                            canon: e ? void 0 : r && r.canon,
                            fragments: n
                        }), n), this.maybeBroadcastWatch = o.wrap((function(e, r) {
                            return t.broadcastWatch(e, r)
                        }), {
                            max: this.config.resultCacheMaxSize,
                            makeCacheKey: function(e) {
                                var r = e.optimistic ? t.optimisticData : t.data;
                                if (M(r)) {
                                    var n = e.optimistic,
                                        i = e.id,
                                        o = e.variables;
                                    return r.makeCacheKey(e.query, e.callback, I({
                                        optimistic: n,
                                        id: i,
                                        variables: o
                                    }))
                                }
                            }
                        }), new Set([this.data.group, this.optimisticData.group]).forEach((function(e) {
                            return e.resetCaching()
                        }))
                    }, r.prototype.restore = function(e) {
                        return this.init(), e && this.data.replace(e), this
                    }, r.prototype.extract = function(e) {
                        return void 0 === e && (e = !1), (e ? this.optimisticData : this.data).extract()
                    }, r.prototype.read = function(e) {
                        var t = e.returnPartialData,
                            r = void 0 !== t && t;
                        try {
                            return this.storeReader.diffQueryAgainstStore(i.__assign(i.__assign({}, e), {
                                store: e.optimistic ? this.optimisticData : this.data,
                                config: this.config,
                                returnPartialData: r
                            })).result || null
                        } catch (e) {
                            if (e instanceof h) return null;
                            throw e
                        }
                    }, r.prototype.write = function(e) {
                        try {
                            return ++this.txCount, this.storeWriter.writeToStore(this.data, e)
                        } finally {
                            --this.txCount || !1 === e.broadcast || this.broadcastWatches()
                        }
                    }, r.prototype.modify = function(e) {
                        if (p.call(e, "id") && !e.id) return !1;
                        var t = e.optimistic ? this.optimisticData : this.data;
                        try {
                            return ++this.txCount, t.modify(e.id || "ROOT_QUERY", e.fields)
                        } finally {
                            --this.txCount || !1 === e.broadcast || this.broadcastWatches()
                        }
                    }, r.prototype.diff = function(e) {
                        return this.storeReader.diffQueryAgainstStore(i.__assign(i.__assign({}, e), {
                            store: e.optimistic ? this.optimisticData : this.data,
                            rootId: e.id || "ROOT_QUERY",
                            config: this.config
                        }))
                    }, r.prototype.watch = function(e) {
                        var t, r = this;
                        return this.watches.size || B(t = this).vars.forEach((function(e) {
                                return e.attachCache(t)
                            })), this.watches.add(e), e.immediate && this.maybeBroadcastWatch(e),
                            function() {
                                r.watches.delete(e) && !r.watches.size && K(r), r.maybeBroadcastWatch.forget(e)
                            }
                    }, r.prototype.gc = function(e) {
                        I.reset();
                        var t = this.optimisticData.gc();
                        return e && !this.txCount && (e.resetResultCache ? this.resetResultCache(e.resetResultIdentities) : e.resetResultIdentities && this.storeReader.resetCanon()), t
                    }, r.prototype.retain = function(e, t) {
                        return (t ? this.optimisticData : this.data).retain(e)
                    }, r.prototype.release = function(e, t) {
                        return (t ? this.optimisticData : this.data).release(e)
                    }, r.prototype.identify = function(e) {
                        if (a.isReference(e)) return e.__ref;
                        try {
                            return this.policies.identify(e)[0]
                        } catch (e) {
                            !1 !== globalThis.__DEV__ && n.invariant.warn(e)
                        }
                    }, r.prototype.evict = function(e) {
                        if (!e.id) {
                            if (p.call(e, "id")) return !1;
                            e = i.__assign(i.__assign({}, e), {
                                id: "ROOT_QUERY"
                            })
                        }
                        try {
                            return ++this.txCount, this.optimisticData.evict(e, this.data)
                        } finally {
                            --this.txCount || !1 === e.broadcast || this.broadcastWatches()
                        }
                    }, r.prototype.reset = function(e) {
                        var t = this;
                        return this.init(), I.reset(), e && e.discardWatches ? (this.watches.forEach((function(e) {
                            return t.maybeBroadcastWatch.forget(e)
                        })), this.watches.clear(), K(this)) : this.broadcastWatches(), Promise.resolve()
                    }, r.prototype.removeOptimistic = function(e) {
                        var t = this.optimisticData.removeLayer(e);
                        t !== this.optimisticData && (this.optimisticData = t, this.broadcastWatches())
                    }, r.prototype.batch = function(e) {
                        var t, r = this,
                            n = e.update,
                            o = e.optimistic,
                            a = void 0 === o || o,
                            s = e.removeOptimistic,
                            u = e.onWatchUpdated,
                            c = function(e) {
                                var i = r,
                                    o = i.data,
                                    a = i.optimisticData;
                                ++r.txCount, e && (r.data = r.optimisticData = e);
                                try {
                                    return t = n(r)
                                } finally {
                                    --r.txCount, r.data = o, r.optimisticData = a
                                }
                            },
                            l = new Set;
                        return u && !this.txCount && this.broadcastWatches(i.__assign(i.__assign({}, e), {
                            onWatchUpdated: function(e) {
                                return l.add(e), !1
                            }
                        })), "string" == typeof a ? this.optimisticData = this.optimisticData.addLayer(a, c) : !1 === a ? c(this.data) : c(), "string" == typeof s && (this.optimisticData = this.optimisticData.removeLayer(s)), u && l.size ? (this.broadcastWatches(i.__assign(i.__assign({}, e), {
                            onWatchUpdated: function(e, t) {
                                var r = u.call(this, e, t);
                                return !1 !== r && l.delete(e), r
                            }
                        })), l.size && l.forEach((function(e) {
                            return r.maybeBroadcastWatch.dirty(e)
                        }))) : this.broadcastWatches(e), t
                    }, r.prototype.performTransaction = function(e, t) {
                        return this.batch({
                            update: e,
                            optimistic: t || null !== t
                        })
                    }, r.prototype.transformDocument = function(e) {
                        return this.addTypenameToDocument(this.addFragmentsToDocument(e))
                    }, r.prototype.broadcastWatches = function(e) {
                        var t = this;
                        this.txCount || this.watches.forEach((function(r) {
                            return t.maybeBroadcastWatch(r, e)
                        }))
                    }, r.prototype.addFragmentsToDocument = function(e) {
                        var t = this.config.fragments;
                        return t ? t.transform(e) : e
                    }, r.prototype.addTypenameToDocument = function(e) {
                        return this.addTypename ? this.addTypenameTransform.transformDocument(e) : e
                    }, r.prototype.broadcastWatch = function(e, t) {
                        var r = e.lastDiff,
                            n = this.diff(e);
                        t && (e.optimistic && "string" == typeof t.optimistic && (n.fromOptimisticTransaction = !0), t.onWatchUpdated && !1 === t.onWatchUpdated.call(this, e, n, r)) || r && s.equal(r.result, n.result) || e.callback(e.lastDiff = n, r)
                    }, r
                }(f),
                we = Array.prototype.forEach,
                Oe = function() {
                    function e() {
                        for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                        this.registry = Object.create(null), this.resetCaches(), e.length && this.register.apply(this, e)
                    }
                    return e.prototype.register = function() {
                        var e = this,
                            t = new Map;
                        return we.call(arguments, (function(e) {
                            a.getFragmentDefinitions(e).forEach((function(e) {
                                t.set(e.name.value, e)
                            }))
                        })), t.forEach((function(t, r) {
                            t !== e.registry[r] && (e.registry[r] = t, e.invalidate(r))
                        })), this
                    }, e.prototype.invalidate = function(e) {}, e.prototype.resetCaches = function() {
                        this.invalidate = (this.lookup = this.cacheUnaryMethod("lookup")).dirty, this.transform = this.cacheUnaryMethod("transform"), this.findFragmentSpreads = this.cacheUnaryMethod("findFragmentSpreads")
                    }, e.prototype.cacheUnaryMethod = function(t) {
                        var r = this,
                            n = e.prototype[t];
                        return o.wrap((function() {
                            return n.apply(r, arguments)
                        }), {
                            makeCacheKey: function(e) {
                                return e
                            }
                        })
                    }, e.prototype.lookup = function(e) {
                        return this.registry[e] || null
                    }, e.prototype.transform = function(e) {
                        var t = this,
                            r = new Map;
                        a.getFragmentDefinitions(e).forEach((function(e) {
                            r.set(e.name.value, e)
                        }));
                        var n = new Set,
                            o = function(e) {
                                r.has(e) || n.add(e)
                            },
                            s = function(e) {
                                return Object.keys(t.findFragmentSpreads(e)).forEach(o)
                            };
                        s(e);
                        var u = [],
                            c = Object.create(null);
                        if (n.forEach((function(e) {
                                var n = r.get(e);
                                if (n) s(c[e] = n);
                                else {
                                    u.push(e);
                                    var i = t.lookup(e);
                                    i && s(c[e] = i)
                                }
                            })), u.length) {
                            var l = [];
                            u.forEach((function(e) {
                                var t = c[e];
                                t && l.push(t)
                            })), l.length && (e = i.__assign(i.__assign({}, e), {
                                definitions: e.definitions.concat(l)
                            }))
                        }
                        return e
                    }, e.prototype.findFragmentSpreads = function(e) {
                        var t = Object.create(null);
                        return c.visit(e, {
                            FragmentSpread: function(e) {
                                t[e.name.value] = e
                            }
                        }), t
                    }, e
                }();
            t.isReference = a.isReference, t.makeReference = a.makeReference, t.ApolloCache = f, t.InMemoryCache = _e, t.MissingFieldError = h, t.Policies = ue, t.cacheSlot = W, t.canonicalStringify = I, t.createFragmentRegistry = function() {
                for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                return new(Oe.bind.apply(Oe, i.__spreadArray([void 0], e, !1)))
            }, t.defaultDataIdFromObject = y, t.fieldNameFromStoreName = b, t.makeVar = H
        },
        32319: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970),
                i = r(57304),
                o = r(4110),
                a = r(98938),
                s = r(51117),
                u = r(73805),
                c = r(47923),
                l = r(49860),
                f = r(73229),
                h = r(63781),
                p = r(17754),
                d = r(13315);

            function y(e) {
                return e && "object" == typeof e && "default" in e ? e.default : e
            }
            var v = y(s);

            function m(e) {
                return null !== e && "object" == typeof e
            }
            var g, b = Object.prototype.hasOwnProperty,
                _ = function(e, t, r) {
                    return this.merge(e[r], t[r])
                },
                w = function() {
                    function e(e) {
                        void 0 === e && (e = _), this.reconciler = e, this.isObject = m, this.pastCopies = new Set
                    }
                    return e.prototype.merge = function(e, t) {
                        for (var r = this, i = [], o = 2; o < arguments.length; o++) i[o - 2] = arguments[o];
                        return m(t) && m(e) ? (Object.keys(t).forEach((function(o) {
                            if (b.call(e, o)) {
                                var a = e[o];
                                if (t[o] !== a) {
                                    var s = r.reconciler.apply(r, n.__spreadArray([e, t, o], i, !1));
                                    s !== a && ((e = r.shallowCopyForMerge(e))[o] = s)
                                }
                            } else(e = r.shallowCopyForMerge(e))[o] = t[o]
                        })), e) : t
                    }, e.prototype.shallowCopyForMerge = function(e) {
                        return m(e) && (this.pastCopies.has(e) || (e = Array.isArray(e) ? e.slice(0) : n.__assign({
                            __proto__: Object.getPrototypeOf(e)
                        }, e), this.pastCopies.add(e))), e
                    }, e
                }();

            function O(e) {
                return !!e && e < 7
            }

            function k(e, t, r, i) {
                var o = t.data,
                    a = n.__rest(t, ["data"]),
                    s = r.data,
                    c = n.__rest(r, ["data"]);
                return v(a, c) && S(u.getMainDefinition(e).selectionSet, o, s, {
                    fragmentMap: u.createFragmentMap(u.getFragmentDefinitions(e)),
                    variables: i
                })
            }

            function S(e, t, r, n) {
                if (t === r) return !0;
                var i = new Set;
                return e.selections.every((function(e) {
                    if (i.has(e)) return !0;
                    if (i.add(e), !u.shouldInclude(e, n.variables)) return !0;
                    if (E(e)) return !0;
                    if (u.isField(e)) {
                        var o = u.resultKeyNameFromField(e),
                            a = t && t[o],
                            s = r && r[o],
                            c = e.selectionSet;
                        if (!c) return v(a, s);
                        var l = Array.isArray(a),
                            f = Array.isArray(s);
                        if (l !== f) return !1;
                        if (l && f) {
                            var h = a.length;
                            if (s.length !== h) return !1;
                            for (var p = 0; p < h; ++p)
                                if (!S(c, a[p], s[p], n)) return !1;
                            return !0
                        }
                        return S(c, a, s, n)
                    }
                    var d = u.getFragmentFromSelection(e, n.fragmentMap);
                    return d ? !!E(d) || S(d.selectionSet, t, r, n) : void 0
                }))
            }

            function E(e) {
                return !!e.directives && e.directives.some(R)
            }

            function R(e) {
                return "nonreactive" === e.name.value
            }
            t.NetworkStatus = void 0, (g = t.NetworkStatus || (t.NetworkStatus = {}))[g.loading = 1] = "loading", g[g.setVariables = 2] = "setVariables", g[g.fetchMore = 3] = "fetchMore", g[g.refetch = 4] = "refetch", g[g.poll = 6] = "poll", g[g.ready = 7] = "ready", g[g.error = 8] = "error";
            var D = Object.assign,
                P = Object.hasOwnProperty,
                F = function(e) {
                    function r(t) {
                        var r = t.queryManager,
                            i = t.queryInfo,
                            o = t.options,
                            a = e.call(this, (function(e) {
                                try {
                                    var t = e._subscription._observer;
                                    t && !t.error && (t.error = q)
                                } catch (e) {}
                                var r = !a.observers.size;
                                a.observers.add(e);
                                var n = a.last;
                                return n && n.error ? e.error && e.error(n.error) : n && n.result && e.next && e.next(n.result), r && a.reobserve().catch((function() {})),
                                    function() {
                                        a.observers.delete(e) && !a.observers.size && a.tearDownQuery()
                                    }
                            })) || this;
                        a.observers = new Set, a.subscriptions = new Set, a.queryInfo = i, a.queryManager = r, a.waitForOwnResult = x(o.fetchPolicy), a.isTornDown = !1;
                        var s = r.defaultOptions.watchQuery,
                            c = (void 0 === s ? {} : s).fetchPolicy,
                            l = void 0 === c ? "cache-first" : c,
                            f = o.fetchPolicy,
                            h = void 0 === f ? l : f,
                            p = o.initialFetchPolicy,
                            d = void 0 === p ? "standby" === h ? l : h : p;
                        a.options = n.__assign(n.__assign({}, o), {
                            initialFetchPolicy: d,
                            fetchPolicy: h
                        }), a.queryId = i.queryId || r.generateQueryId();
                        var y = u.getOperationDefinition(a.query);
                        return a.queryName = y && y.name && y.name.value, a
                    }
                    return n.__extends(r, e), Object.defineProperty(r.prototype, "query", {
                        get: function() {
                            return this.lastQuery || this.options.query
                        },
                        enumerable: !1,
                        configurable: !0
                    }), Object.defineProperty(r.prototype, "variables", {
                        get: function() {
                            return this.options.variables
                        },
                        enumerable: !1,
                        configurable: !0
                    }), r.prototype.result = function() {
                        var e = this;
                        return new Promise((function(t, r) {
                            var n = {
                                    next: function(r) {
                                        t(r), e.observers.delete(n), e.observers.size || e.queryManager.removeQuery(e.queryId), setTimeout((function() {
                                            i.unsubscribe()
                                        }), 0)
                                    },
                                    error: r
                                },
                                i = e.subscribe(n)
                        }))
                    }, r.prototype.getCurrentResult = function(e) {
                        void 0 === e && (e = !0);
                        var r = this.getLastResult(!0),
                            i = this.queryInfo.networkStatus || r && r.networkStatus || t.NetworkStatus.ready,
                            o = n.__assign(n.__assign({}, r), {
                                loading: O(i),
                                networkStatus: i
                            }),
                            a = this.options.fetchPolicy,
                            u = void 0 === a ? "cache-first" : a;
                        if (x(u) || this.queryManager.getDocumentInfo(this.query).hasForcedResolvers);
                        else if (this.waitForOwnResult) this.queryInfo.updateWatch();
                        else {
                            var c = this.queryInfo.getDiff();
                            (c.complete || this.options.returnPartialData) && (o.data = c.result), s.equal(o.data, {}) && (o.data = void 0), c.complete ? (delete o.partial, !c.complete || o.networkStatus !== t.NetworkStatus.loading || "cache-first" !== u && "cache-only" !== u || (o.networkStatus = t.NetworkStatus.ready, o.loading = !1)) : o.partial = !0, !1 === globalThis.__DEV__ || c.complete || this.options.partialRefetch || o.loading || o.data || o.error || C(c.missing)
                        }
                        return e && this.updateLastResult(o), o
                    }, r.prototype.isDifferentFromLastResult = function(e, t) {
                        return !this.last || (this.queryManager.getDocumentInfo(this.query).hasNonreactiveDirective ? !k(this.query, this.last.result, e, this.variables) : !s.equal(this.last.result, e)) || t && !s.equal(this.last.variables, t)
                    }, r.prototype.getLast = function(e, t) {
                        var r = this.last;
                        if (r && r[e] && (!t || s.equal(r.variables, this.variables))) return r[e]
                    }, r.prototype.getLastResult = function(e) {
                        return this.getLast("result", e)
                    }, r.prototype.getLastError = function(e) {
                        return this.getLast("error", e)
                    }, r.prototype.resetLastResults = function() {
                        delete this.last, this.isTornDown = !1
                    }, r.prototype.resetQueryStoreErrors = function() {
                        this.queryManager.resetErrors(this.queryId)
                    }, r.prototype.refetch = function(e) {
                        var r, o = {
                                pollInterval: 0
                            },
                            a = this.options.fetchPolicy;
                        if (o.fetchPolicy = "cache-and-network" === a ? a : "no-cache" === a ? "no-cache" : "network-only", !1 !== globalThis.__DEV__ && e && P.call(e, "variables")) {
                            var c = u.getQueryDefinition(this.query),
                                l = c.variableDefinitions;
                            l && l.some((function(e) {
                                return "variables" === e.variable.name.value
                            })) || !1 !== globalThis.__DEV__ && i.invariant.warn(18, e, (null === (r = c.name) || void 0 === r ? void 0 : r.value) || c)
                        }
                        return e && !s.equal(this.options.variables, e) && (o.variables = this.options.variables = n.__assign(n.__assign({}, this.options.variables), e)), this.queryInfo.resetLastWrite(), this.reobserve(o, t.NetworkStatus.refetch)
                    }, r.prototype.fetchMore = function(e) {
                        var r = this,
                            i = n.__assign(n.__assign({}, e.query ? e : n.__assign(n.__assign(n.__assign(n.__assign({}, this.options), {
                                query: this.options.query
                            }), e), {
                                variables: n.__assign(n.__assign({}, this.options.variables), e.variables)
                            })), {
                                fetchPolicy: "no-cache"
                            });
                        i.query = this.transformDocument(i.query);
                        var o = this.queryManager.generateQueryId();
                        this.lastQuery = e.query ? this.transformDocument(this.options.query) : i.query;
                        var a = this.queryInfo,
                            s = a.networkStatus;
                        a.networkStatus = t.NetworkStatus.fetchMore, i.notifyOnNetworkStatusChange && this.observe();
                        var u = new Set;
                        return this.queryManager.fetchQuery(o, i, t.NetworkStatus.fetchMore).then((function(n) {
                            return r.queryManager.removeQuery(o), a.networkStatus === t.NetworkStatus.fetchMore && (a.networkStatus = s), r.queryManager.cache.batch({
                                update: function(t) {
                                    var o = e.updateQuery;
                                    o ? t.updateQuery({
                                        query: r.query,
                                        variables: r.variables,
                                        returnPartialData: !0,
                                        optimistic: !1
                                    }, (function(e) {
                                        return o(e, {
                                            fetchMoreResult: n.data,
                                            variables: i.variables
                                        })
                                    })) : t.writeQuery({
                                        query: i.query,
                                        variables: i.variables,
                                        data: n.data
                                    })
                                },
                                onWatchUpdated: function(e) {
                                    u.add(e.query)
                                }
                            }), n
                        })).finally((function() {
                            u.has(r.query) || T(r)
                        }))
                    }, r.prototype.subscribeToMore = function(e) {
                        var t = this,
                            r = this.queryManager.startGraphQLSubscription({
                                query: e.document,
                                variables: e.variables,
                                context: e.context
                            }).subscribe({
                                next: function(r) {
                                    var n = e.updateQuery;
                                    n && t.updateQuery((function(e, t) {
                                        var i = t.variables;
                                        return n(e, {
                                            subscriptionData: r,
                                            variables: i
                                        })
                                    }))
                                },
                                error: function(t) {
                                    e.onError ? e.onError(t) : !1 !== globalThis.__DEV__ && i.invariant.error(19, t)
                                }
                            });
                        return this.subscriptions.add(r),
                            function() {
                                t.subscriptions.delete(r) && r.unsubscribe()
                            }
                    }, r.prototype.setOptions = function(e) {
                        return this.reobserve(e)
                    }, r.prototype.silentSetOptions = function(e) {
                        var t = u.compact(this.options, e || {});
                        D(this.options, t)
                    }, r.prototype.setVariables = function(e) {
                        return s.equal(this.variables, e) ? this.observers.size ? this.result() : Promise.resolve() : (this.options.variables = e, this.observers.size ? this.reobserve({
                            fetchPolicy: this.options.initialFetchPolicy,
                            variables: e
                        }, t.NetworkStatus.setVariables) : Promise.resolve())
                    }, r.prototype.updateQuery = function(e) {
                        var t = this.queryManager,
                            r = e(t.cache.diff({
                                query: this.options.query,
                                variables: this.variables,
                                returnPartialData: !0,
                                optimistic: !1
                            }).result, {
                                variables: this.variables
                            });
                        r && (t.cache.writeQuery({
                            query: this.options.query,
                            data: r,
                            variables: this.variables
                        }), t.broadcastQueries())
                    }, r.prototype.startPolling = function(e) {
                        this.options.pollInterval = e, this.updatePolling()
                    }, r.prototype.stopPolling = function() {
                        this.options.pollInterval = 0, this.updatePolling()
                    }, r.prototype.applyNextFetchPolicy = function(e, t) {
                        if (t.nextFetchPolicy) {
                            var r = t.fetchPolicy,
                                n = void 0 === r ? "cache-first" : r,
                                i = t.initialFetchPolicy,
                                o = void 0 === i ? n : i;
                            "standby" === n || ("function" == typeof t.nextFetchPolicy ? t.fetchPolicy = t.nextFetchPolicy(n, {
                                reason: e,
                                options: t,
                                observable: this,
                                initialFetchPolicy: o
                            }) : t.fetchPolicy = "variables-changed" === e ? o : t.nextFetchPolicy)
                        }
                        return t.fetchPolicy
                    }, r.prototype.fetch = function(e, t) {
                        return this.queryManager.setObservableQuery(this), this.queryManager.fetchConcastWithInfo(this.queryId, e, t)
                    }, r.prototype.updatePolling = function() {
                        var e = this;
                        if (!this.queryManager.ssrMode) {
                            var r = this.pollingInfo,
                                n = this.options.pollInterval;
                            if (n) {
                                if (!r || r.interval !== n) {
                                    i.invariant(n, 20), (r || (this.pollingInfo = {})).interval = n;
                                    var o = function() {
                                            e.pollingInfo && (O(e.queryInfo.networkStatus) ? a() : e.reobserve({
                                                fetchPolicy: "no-cache" === e.options.initialFetchPolicy ? "no-cache" : "network-only"
                                            }, t.NetworkStatus.poll).then(a, a))
                                        },
                                        a = function() {
                                            var t = e.pollingInfo;
                                            t && (clearTimeout(t.timeout), t.timeout = setTimeout(o, t.interval))
                                        };
                                    a()
                                }
                            } else r && (clearTimeout(r.timeout), delete this.pollingInfo)
                        }
                    }, r.prototype.updateLastResult = function(e, t) {
                        void 0 === t && (t = this.variables);
                        var r = this.getLastError();
                        return r && this.last && !s.equal(t, this.last.variables) && (r = void 0), this.last = n.__assign({
                            result: this.queryManager.assumeImmutableResults ? e : u.cloneDeep(e),
                            variables: t
                        }, r ? {
                            error: r
                        } : null)
                    }, r.prototype.reobserveAsConcast = function(e, r) {
                        var i = this;
                        this.isTornDown = !1;
                        var o = r === t.NetworkStatus.refetch || r === t.NetworkStatus.fetchMore || r === t.NetworkStatus.poll,
                            a = this.options.variables,
                            c = this.options.fetchPolicy,
                            l = u.compact(this.options, e || {}),
                            f = o ? l : D(this.options, l),
                            h = this.transformDocument(f.query);
                        this.lastQuery = h, o || (this.updatePolling(), e && e.variables && !s.equal(e.variables, a) && "standby" !== f.fetchPolicy && f.fetchPolicy === c && (this.applyNextFetchPolicy("variables-changed", f), void 0 === r && (r = t.NetworkStatus.setVariables)));
                        var p = h === f.query ? f : n.__assign(n.__assign({}, f), {
                            query: h
                        });
                        this.waitForOwnResult && (this.waitForOwnResult = x(p.fetchPolicy));
                        var d = function() {
                                i.concast === m && (i.waitForOwnResult = !1)
                            },
                            y = p.variables && n.__assign({}, p.variables),
                            v = this.fetch(p, r),
                            m = v.concast,
                            g = v.fromLink,
                            b = {
                                next: function(e) {
                                    d(), i.reportResult(e, y)
                                },
                                error: function(e) {
                                    d(), i.reportError(e, y)
                                }
                            };
                        return o || !g && this.concast || (this.concast && this.observer && this.concast.removeObserver(this.observer), this.concast = m, this.observer = b), m.addObserver(b), m
                    }, r.prototype.reobserve = function(e, t) {
                        return this.reobserveAsConcast(e, t).promise
                    }, r.prototype.observe = function() {
                        this.reportResult(this.getCurrentResult(!1), this.variables)
                    }, r.prototype.reportResult = function(e, t) {
                        var r = this.getLastError(),
                            n = this.isDifferentFromLastResult(e, t);
                        (r || !e.partial || this.options.returnPartialData) && this.updateLastResult(e, t), (r || n) && u.iterateObserversSafely(this.observers, "next", e)
                    }, r.prototype.reportError = function(e, r) {
                        var i = n.__assign(n.__assign({}, this.getLastResult()), {
                            error: e,
                            errors: e.graphQLErrors,
                            networkStatus: t.NetworkStatus.error,
                            loading: !1
                        });
                        this.updateLastResult(i, r), u.iterateObserversSafely(this.observers, "error", this.last.error = e)
                    }, r.prototype.hasObservers = function() {
                        return this.observers.size > 0
                    }, r.prototype.tearDownQuery = function() {
                        this.isTornDown || (this.concast && this.observer && (this.concast.removeObserver(this.observer), delete this.concast, delete this.observer), this.stopPolling(), this.subscriptions.forEach((function(e) {
                            return e.unsubscribe()
                        })), this.subscriptions.clear(), this.queryManager.stopQuery(this.queryId), this.observers.clear(), this.isTornDown = !0)
                    }, r.prototype.transformDocument = function(e) {
                        return this.queryManager.transform(e)
                    }, r
                }(u.Observable);

            function T(e) {
                var t = e.options,
                    r = t.fetchPolicy,
                    n = t.nextFetchPolicy;
                return "cache-and-network" === r || "network-only" === r ? e.reobserve({
                    fetchPolicy: "cache-first",
                    nextFetchPolicy: function() {
                        return this.nextFetchPolicy = n, "function" == typeof n ? n.apply(this, arguments) : r
                    }
                }) : e.reobserve()
            }

            function q(e) {
                !1 !== globalThis.__DEV__ && i.invariant.error(21, e.message, e.stack)
            }

            function C(e) {
                !1 !== globalThis.__DEV__ && e && !1 !== globalThis.__DEV__ && i.invariant.debug(22, e)
            }

            function x(e) {
                return "network-only" === e || "no-cache" === e || "standby" === e
            }
            u.fixObservableSubclass(F);
            var M = function() {
                    function e(e) {
                        var t = e.cache,
                            r = e.client,
                            n = e.resolvers,
                            i = e.fragmentMatcher;
                        this.selectionsToResolveCache = new WeakMap, this.cache = t, r && (this.client = r), n && this.addResolvers(n), i && this.setFragmentMatcher(i)
                    }
                    return e.prototype.addResolvers = function(e) {
                        var t = this;
                        this.resolvers = this.resolvers || {}, Array.isArray(e) ? e.forEach((function(e) {
                            t.resolvers = u.mergeDeep(t.resolvers, e)
                        })) : this.resolvers = u.mergeDeep(this.resolvers, e)
                    }, e.prototype.setResolvers = function(e) {
                        this.resolvers = {}, this.addResolvers(e)
                    }, e.prototype.getResolvers = function() {
                        return this.resolvers || {}
                    }, e.prototype.runResolvers = function(e) {
                        var t = e.document,
                            r = e.remoteResult,
                            i = e.context,
                            o = e.variables,
                            a = e.onlyRunForcedResolvers,
                            s = void 0 !== a && a;
                        return n.__awaiter(this, void 0, void 0, (function() {
                            return n.__generator(this, (function(e) {
                                return t ? [2, this.resolveDocument(t, r.data, i, o, this.fragmentMatcher, s).then((function(e) {
                                    return n.__assign(n.__assign({}, r), {
                                        data: e.result
                                    })
                                }))] : [2, r]
                            }))
                        }))
                    }, e.prototype.setFragmentMatcher = function(e) {
                        this.fragmentMatcher = e
                    }, e.prototype.getFragmentMatcher = function() {
                        return this.fragmentMatcher
                    }, e.prototype.clientQuery = function(e) {
                        return u.hasDirectives(["client"], e) && this.resolvers ? e : null
                    }, e.prototype.serverQuery = function(e) {
                        return u.removeClientSetsFromDocument(e)
                    }, e.prototype.prepareContext = function(e) {
                        var t = this.cache;
                        return n.__assign(n.__assign({}, e), {
                            cache: t,
                            getCacheKey: function(e) {
                                return t.identify(e)
                            }
                        })
                    }, e.prototype.addExportedVariables = function(e, t, r) {
                        return void 0 === t && (t = {}), void 0 === r && (r = {}), n.__awaiter(this, void 0, void 0, (function() {
                            return n.__generator(this, (function(i) {
                                return e ? [2, this.resolveDocument(e, this.buildRootValueFromCache(e, t) || {}, this.prepareContext(r), t).then((function(e) {
                                    return n.__assign(n.__assign({}, t), e.exportedVariables)
                                }))] : [2, n.__assign({}, t)]
                            }))
                        }))
                    }, e.prototype.shouldForceResolvers = function(e) {
                        var t = !1;
                        return f.visit(e, {
                            Directive: {
                                enter: function(e) {
                                    if ("client" === e.name.value && e.arguments && (t = e.arguments.some((function(e) {
                                            return "always" === e.name.value && "BooleanValue" === e.value.kind && !0 === e.value.value
                                        })))) return f.BREAK
                                }
                            }
                        }), t
                    }, e.prototype.buildRootValueFromCache = function(e, t) {
                        return this.cache.diff({
                            query: u.buildQueryFromSelectionSet(e),
                            variables: t,
                            returnPartialData: !0,
                            optimistic: !1
                        }).result
                    }, e.prototype.resolveDocument = function(e, t, r, i, o, a) {
                        return void 0 === r && (r = {}), void 0 === i && (i = {}), void 0 === o && (o = function() {
                            return !0
                        }), void 0 === a && (a = !1), n.__awaiter(this, void 0, void 0, (function() {
                            var s, c, l, f, h, p, d, y, v, m;
                            return n.__generator(this, (function(g) {
                                return s = u.getMainDefinition(e), c = u.getFragmentDefinitions(e), l = u.createFragmentMap(c), f = this.collectSelectionsToResolve(s, l), h = s.operation, p = h ? h.charAt(0).toUpperCase() + h.slice(1) : "Query", y = (d = this).cache, v = d.client, m = {
                                    fragmentMap: l,
                                    context: n.__assign(n.__assign({}, r), {
                                        cache: y,
                                        client: v
                                    }),
                                    variables: i,
                                    fragmentMatcher: o,
                                    defaultOperationType: p,
                                    exportedVariables: {},
                                    selectionsToResolve: f,
                                    onlyRunForcedResolvers: a
                                }, [2, this.resolveSelectionSet(s.selectionSet, !1, t, m).then((function(e) {
                                    return {
                                        result: e,
                                        exportedVariables: m.exportedVariables
                                    }
                                }))]
                            }))
                        }))
                    }, e.prototype.resolveSelectionSet = function(e, t, r, o) {
                        return n.__awaiter(this, void 0, void 0, (function() {
                            var a, s, c, l, f, h = this;
                            return n.__generator(this, (function(p) {
                                return a = o.fragmentMap, s = o.context, c = o.variables, l = [r], f = function(e) {
                                    return n.__awaiter(h, void 0, void 0, (function() {
                                        var f, h;
                                        return n.__generator(this, (function(n) {
                                            return (t || o.selectionsToResolve.has(e)) && u.shouldInclude(e, c) ? u.isField(e) ? [2, this.resolveField(e, t, r, o).then((function(t) {
                                                var r;
                                                void 0 !== t && l.push(((r = {})[u.resultKeyNameFromField(e)] = t, r))
                                            }))] : (u.isInlineFragment(e) ? f = e : (f = a[e.name.value], i.invariant(f, 16, e.name.value)), f && f.typeCondition && (h = f.typeCondition.name.value, o.fragmentMatcher(r, h, s)) ? [2, this.resolveSelectionSet(f.selectionSet, t, r, o).then((function(e) {
                                                l.push(e)
                                            }))] : [2]) : [2]
                                        }))
                                    }))
                                }, [2, Promise.all(e.selections.map(f)).then((function() {
                                    return u.mergeDeepArray(l)
                                }))]
                            }))
                        }))
                    }, e.prototype.resolveField = function(e, t, r, i) {
                        return n.__awaiter(this, void 0, void 0, (function() {
                            var o, a, s, l, f, h, p, d, y, v = this;
                            return n.__generator(this, (function(n) {
                                return r ? (o = i.variables, a = e.name.value, s = u.resultKeyNameFromField(e), l = a !== s, f = r[s] || r[a], h = Promise.resolve(f), i.onlyRunForcedResolvers && !this.shouldForceResolvers(e) || (p = r.__typename || i.defaultOperationType, (d = this.resolvers && this.resolvers[p]) && (y = d[l ? a : s]) && (h = Promise.resolve(c.cacheSlot.withValue(this.cache, y, [r, u.argumentsObjectFromField(e, o), i.context, {
                                    field: e,
                                    fragmentMap: i.fragmentMap
                                }])))), [2, h.then((function(r) {
                                    var n, o;
                                    if (void 0 === r && (r = f), e.directives && e.directives.forEach((function(e) {
                                            "export" === e.name.value && e.arguments && e.arguments.forEach((function(e) {
                                                "as" === e.name.value && "StringValue" === e.value.kind && (i.exportedVariables[e.value.value] = r)
                                            }))
                                        })), !e.selectionSet) return r;
                                    if (null == r) return r;
                                    var a = null !== (o = null === (n = e.directives) || void 0 === n ? void 0 : n.some((function(e) {
                                        return "client" === e.name.value
                                    }))) && void 0 !== o && o;
                                    return Array.isArray(r) ? v.resolveSubSelectedArray(e, t || a, r, i) : e.selectionSet ? v.resolveSelectionSet(e.selectionSet, t || a, r, i) : void 0
                                }))]) : [2, null]
                            }))
                        }))
                    }, e.prototype.resolveSubSelectedArray = function(e, t, r, n) {
                        var i = this;
                        return Promise.all(r.map((function(r) {
                            return null === r ? null : Array.isArray(r) ? i.resolveSubSelectedArray(e, t, r, n) : e.selectionSet ? i.resolveSelectionSet(e.selectionSet, t, r, n) : void 0
                        })))
                    }, e.prototype.collectSelectionsToResolve = function(e, t) {
                        var r = function(e) {
                                return !Array.isArray(e)
                            },
                            n = this.selectionsToResolveCache;
                        return function e(o) {
                            if (!n.has(o)) {
                                var a = new Set;
                                n.set(o, a), f.visit(o, {
                                    Directive: function(e, t, n, i, o) {
                                        "client" === e.name.value && o.forEach((function(e) {
                                            r(e) && f.isSelectionNode(e) && a.add(e)
                                        }))
                                    },
                                    FragmentSpread: function(n, o, s, u, c) {
                                        var l = t[n.name.value];
                                        i.invariant(l, 17, n.name.value);
                                        var h = e(l);
                                        h.size > 0 && (c.forEach((function(e) {
                                            r(e) && f.isSelectionNode(e) && a.add(e)
                                        })), a.add(n), h.forEach((function(e) {
                                            a.add(e)
                                        })))
                                    }
                                })
                            }
                            return n.get(o)
                        }(e)
                    }, e
                }(),
                Q = new(u.canUseWeakMap ? WeakMap : Map);

            function j(e, t) {
                var r = e[t];
                "function" == typeof r && (e[t] = function() {
                    return Q.set(e, (Q.get(e) + 1) % 1e15), r.apply(this, arguments)
                })
            }

            function N(e) {
                e.notifyTimeout && (clearTimeout(e.notifyTimeout), e.notifyTimeout = void 0)
            }
            var I = function() {
                function e(e, t) {
                    void 0 === t && (t = e.generateQueryId()), this.queryId = t, this.listeners = new Set, this.document = null, this.lastRequestId = 1, this.subscriptions = new Set, this.stopped = !1, this.dirty = !1, this.observableQuery = null;
                    var r = this.cache = e.cache;
                    Q.has(r) || (Q.set(r, 0), j(r, "evict"), j(r, "modify"), j(r, "reset"))
                }
                return e.prototype.init = function(e) {
                    var r = e.networkStatus || t.NetworkStatus.loading;
                    return this.variables && this.networkStatus !== t.NetworkStatus.loading && !s.equal(this.variables, e.variables) && (r = t.NetworkStatus.setVariables), s.equal(e.variables, this.variables) || (this.lastDiff = void 0), Object.assign(this, {
                        document: e.document,
                        variables: e.variables,
                        networkError: null,
                        graphQLErrors: this.graphQLErrors || [],
                        networkStatus: r
                    }), e.observableQuery && this.setObservableQuery(e.observableQuery), e.lastRequestId && (this.lastRequestId = e.lastRequestId), this
                }, e.prototype.reset = function() {
                    N(this), this.dirty = !1
                }, e.prototype.getDiff = function(e) {
                    void 0 === e && (e = this.variables);
                    var t = this.getDiffOptions(e);
                    if (this.lastDiff && s.equal(t, this.lastDiff.options)) return this.lastDiff.diff;
                    this.updateWatch(this.variables = e);
                    var r = this.observableQuery;
                    if (r && "no-cache" === r.options.fetchPolicy) return {
                        complete: !1
                    };
                    var n = this.cache.diff(t);
                    return this.updateLastDiff(n, t), n
                }, e.prototype.updateLastDiff = function(e, t) {
                    this.lastDiff = e ? {
                        diff: e,
                        options: t || this.getDiffOptions()
                    } : void 0
                }, e.prototype.getDiffOptions = function(e) {
                    var t;
                    return void 0 === e && (e = this.variables), {
                        query: this.document,
                        variables: e,
                        returnPartialData: !0,
                        optimistic: !0,
                        canonizeResults: null === (t = this.observableQuery) || void 0 === t ? void 0 : t.options.canonizeResults
                    }
                }, e.prototype.setDiff = function(e) {
                    var t = this,
                        r = this.lastDiff && this.lastDiff.diff;
                    this.updateLastDiff(e), this.dirty || s.equal(r && r.result, e && e.result) || (this.dirty = !0, this.notifyTimeout || (this.notifyTimeout = setTimeout((function() {
                        return t.notify()
                    }), 0)))
                }, e.prototype.setObservableQuery = function(e) {
                    var t = this;
                    e !== this.observableQuery && (this.oqListener && this.listeners.delete(this.oqListener), this.observableQuery = e, e ? (e.queryInfo = this, this.listeners.add(this.oqListener = function() {
                        t.getDiff().fromOptimisticTransaction ? e.observe() : T(e)
                    })) : delete this.oqListener)
                }, e.prototype.notify = function() {
                    var e = this;
                    N(this), this.shouldNotify() && this.listeners.forEach((function(t) {
                        return t(e)
                    })), this.dirty = !1
                }, e.prototype.shouldNotify = function() {
                    if (!this.dirty || !this.listeners.size) return !1;
                    if (O(this.networkStatus) && this.observableQuery) {
                        var e = this.observableQuery.options.fetchPolicy;
                        if ("cache-only" !== e && "cache-and-network" !== e) return !1
                    }
                    return !0
                }, e.prototype.stop = function() {
                    if (!this.stopped) {
                        this.stopped = !0, this.reset(), this.cancel(), this.cancel = e.prototype.cancel, this.subscriptions.forEach((function(e) {
                            return e.unsubscribe()
                        }));
                        var t = this.observableQuery;
                        t && t.stopPolling()
                    }
                }, e.prototype.cancel = function() {}, e.prototype.updateWatch = function(e) {
                    var t = this;
                    void 0 === e && (e = this.variables);
                    var r = this.observableQuery;
                    if (!r || "no-cache" !== r.options.fetchPolicy) {
                        var i = n.__assign(n.__assign({}, this.getDiffOptions(e)), {
                            watcher: this,
                            callback: function(e) {
                                return t.setDiff(e)
                            }
                        });
                        this.lastWatch && s.equal(i, this.lastWatch) || (this.cancel(), this.cancel = this.cache.watch(this.lastWatch = i))
                    }
                }, e.prototype.resetLastWrite = function() {
                    this.lastWrite = void 0
                }, e.prototype.shouldWrite = function(e, t) {
                    var r = this.lastWrite;
                    return !(r && r.dmCount === Q.get(this.cache) && s.equal(t, r.variables) && s.equal(e.data, r.result.data))
                }, e.prototype.markResult = function(e, t, r, n) {
                    var i = this,
                        o = new u.DeepMerger,
                        a = u.isNonEmptyArray(e.errors) ? e.errors.slice(0) : [];
                    if (this.reset(), "incremental" in e && u.isNonEmptyArray(e.incremental)) {
                        var s = u.mergeIncrementalData(this.getDiff().result, e);
                        e.data = s
                    } else if ("hasNext" in e && e.hasNext) {
                        var c = this.getDiff();
                        e.data = o.merge(c.result, e.data)
                    }
                    this.graphQLErrors = a, "no-cache" === r.fetchPolicy ? this.updateLastDiff({
                        result: e.data,
                        complete: !0
                    }, this.getDiffOptions(r.variables)) : 0 !== n && (A(e, r.errorPolicy) ? this.cache.performTransaction((function(o) {
                        if (i.shouldWrite(e, r.variables)) o.writeQuery({
                            query: t,
                            data: e.data,
                            variables: r.variables,
                            overwrite: 1 === n
                        }), i.lastWrite = {
                            result: e,
                            variables: r.variables,
                            dmCount: Q.get(i.cache)
                        };
                        else if (i.lastDiff && i.lastDiff.diff.complete) return void(e.data = i.lastDiff.diff.result);
                        var a = i.getDiffOptions(r.variables),
                            s = o.diff(a);
                        i.stopped || i.updateWatch(r.variables), i.updateLastDiff(s, a), s.complete && (e.data = s.result)
                    })) : this.lastWrite = void 0)
                }, e.prototype.markReady = function() {
                    return this.networkError = null, this.networkStatus = t.NetworkStatus.ready
                }, e.prototype.markError = function(e) {
                    return this.networkStatus = t.NetworkStatus.error, this.lastWrite = void 0, this.reset(), e.graphQLErrors && (this.graphQLErrors = e.graphQLErrors), e.networkError && (this.networkError = e.networkError), e
                }, e
            }();

            function A(e, t) {
                void 0 === t && (t = "none");
                var r = "ignore" === t || "all" === t,
                    n = !u.graphQLResultHasError(e);
                return !n && r && e.data && (n = !0), n
            }
            var L = Object.prototype.hasOwnProperty,
                V = function() {
                    function e(e) {
                        var t = e.cache,
                            r = e.link,
                            n = e.defaultOptions,
                            i = e.documentTransform,
                            o = e.queryDeduplication,
                            a = void 0 !== o && o,
                            s = e.onBroadcast,
                            c = e.ssrMode,
                            l = void 0 !== c && c,
                            f = e.clientAwareness,
                            h = void 0 === f ? {} : f,
                            p = e.localState,
                            d = e.assumeImmutableResults,
                            y = void 0 === d ? !!t.assumeImmutableResults : d,
                            v = this;
                        this.clientAwareness = {}, this.queries = new Map, this.fetchCancelFns = new Map, this.transformCache = new(u.canUseWeakMap ? WeakMap : Map), this.queryIdCounter = 1, this.requestIdCounter = 1, this.mutationIdCounter = 1, this.inFlightLinkObservables = new Map;
                        var m = new u.DocumentTransform((function(e) {
                            return v.cache.transformDocument(e)
                        }), {
                            cache: !1
                        });
                        this.cache = t, this.link = r, this.defaultOptions = n || Object.create(null), this.queryDeduplication = a, this.clientAwareness = h, this.localState = p || new M({
                            cache: t
                        }), this.ssrMode = l, this.assumeImmutableResults = y, this.documentTransform = i ? m.concat(i).concat(m) : m, (this.onBroadcast = s) && (this.mutationStore = Object.create(null))
                    }
                    return e.prototype.stop = function() {
                        var e = this;
                        this.queries.forEach((function(t, r) {
                            e.stopQueryNoBroadcast(r)
                        })), this.cancelPendingFetches(i.newInvariantError(23))
                    }, e.prototype.cancelPendingFetches = function(e) {
                        this.fetchCancelFns.forEach((function(t) {
                            return t(e)
                        })), this.fetchCancelFns.clear()
                    }, e.prototype.mutate = function(e) {
                        var t, r, o = e.mutation,
                            a = e.variables,
                            s = e.optimisticResponse,
                            c = e.updateQueries,
                            f = e.refetchQueries,
                            h = void 0 === f ? [] : f,
                            p = e.awaitRefetchQueries,
                            d = void 0 !== p && p,
                            y = e.update,
                            v = e.onQueryUpdated,
                            m = e.fetchPolicy,
                            g = void 0 === m ? (null === (t = this.defaultOptions.mutate) || void 0 === t ? void 0 : t.fetchPolicy) || "network-only" : m,
                            b = e.errorPolicy,
                            _ = void 0 === b ? (null === (r = this.defaultOptions.mutate) || void 0 === r ? void 0 : r.errorPolicy) || "none" : b,
                            w = e.keepRootFields,
                            O = e.context;
                        return n.__awaiter(this, void 0, void 0, (function() {
                            var e, t, r, f;
                            return n.__generator(this, (function(p) {
                                switch (p.label) {
                                    case 0:
                                        return i.invariant(o, 24), i.invariant("network-only" === g || "no-cache" === g, 25), e = this.generateMutationId(), o = this.cache.transformForLink(this.transform(o)), t = this.getDocumentInfo(o).hasClientExports, a = this.getVariables(o, a), t ? [4, this.localState.addExportedVariables(o, a, O)] : [3, 2];
                                    case 1:
                                        a = p.sent(), p.label = 2;
                                    case 2:
                                        return r = this.mutationStore && (this.mutationStore[e] = {
                                            mutation: o,
                                            variables: a,
                                            loading: !0,
                                            error: null
                                        }), s && this.markMutationOptimistic(s, {
                                            mutationId: e,
                                            document: o,
                                            variables: a,
                                            fetchPolicy: g,
                                            errorPolicy: _,
                                            context: O,
                                            updateQueries: c,
                                            update: y,
                                            keepRootFields: w
                                        }), this.broadcastQueries(), f = this, [2, new Promise((function(t, i) {
                                            return u.asyncMap(f.getObservableFromLink(o, n.__assign(n.__assign({}, O), {
                                                optimisticResponse: s
                                            }), a, !1), (function(t) {
                                                if (u.graphQLResultHasError(t) && "none" === _) throw new l.ApolloError({
                                                    graphQLErrors: u.getGraphQLErrorsFromResult(t)
                                                });
                                                r && (r.loading = !1, r.error = null);
                                                var i = n.__assign({}, t);
                                                return "function" == typeof h && (h = h(i)), "ignore" === _ && u.graphQLResultHasError(i) && delete i.errors, f.markMutationResult({
                                                    mutationId: e,
                                                    result: i,
                                                    document: o,
                                                    variables: a,
                                                    fetchPolicy: g,
                                                    errorPolicy: _,
                                                    context: O,
                                                    update: y,
                                                    updateQueries: c,
                                                    awaitRefetchQueries: d,
                                                    refetchQueries: h,
                                                    removeOptimistic: s ? e : void 0,
                                                    onQueryUpdated: v,
                                                    keepRootFields: w
                                                })
                                            })).subscribe({
                                                next: function(e) {
                                                    f.broadcastQueries(), "hasNext" in e && !1 !== e.hasNext || t(e)
                                                },
                                                error: function(t) {
                                                    r && (r.loading = !1, r.error = t), s && f.cache.removeOptimistic(e), f.broadcastQueries(), i(t instanceof l.ApolloError ? t : new l.ApolloError({
                                                        networkError: t
                                                    }))
                                                }
                                            })
                                        }))]
                                }
                            }))
                        }))
                    }, e.prototype.markMutationResult = function(e, t) {
                        var r = this;
                        void 0 === t && (t = this.cache);
                        var i = e.result,
                            o = [],
                            a = "no-cache" === e.fetchPolicy;
                        if (!a && A(i, e.errorPolicy)) {
                            if (u.isExecutionPatchIncrementalResult(i) || o.push({
                                    result: i.data,
                                    dataId: "ROOT_MUTATION",
                                    query: e.document,
                                    variables: e.variables
                                }), u.isExecutionPatchIncrementalResult(i) && u.isNonEmptyArray(i.incremental)) {
                                var s = t.diff({
                                        id: "ROOT_MUTATION",
                                        query: this.getDocumentInfo(e.document).asQuery,
                                        variables: e.variables,
                                        optimistic: !1,
                                        returnPartialData: !0
                                    }),
                                    c = void 0;
                                s.result && (c = function(e, t) {
                                    var r, n = e,
                                        i = new w;
                                    return "incremental" in t && (r = t.incremental, Array.isArray(r) && r.length > 0) && t.incremental.forEach((function(e) {
                                        for (var t = e.data, r = e.path, o = r.length - 1; o >= 0; --o) {
                                            var a = r[o],
                                                s = isNaN(+a) ? {} : [];
                                            s[a] = t, t = s
                                        }
                                        n = i.merge(n, t)
                                    })), n
                                }(s.result, i)), void 0 !== c && (i.data = c, o.push({
                                    result: c,
                                    dataId: "ROOT_MUTATION",
                                    query: e.document,
                                    variables: e.variables
                                }))
                            }
                            var l = e.updateQueries;
                            l && this.queries.forEach((function(e, n) {
                                var a = e.observableQuery,
                                    s = a && a.queryName;
                                if (s && L.call(l, s)) {
                                    var c = l[s],
                                        f = r.queries.get(n),
                                        h = f.document,
                                        p = f.variables,
                                        d = t.diff({
                                            query: h,
                                            variables: p,
                                            returnPartialData: !0,
                                            optimistic: !1
                                        }),
                                        y = d.result;
                                    if (d.complete && y) {
                                        var v = c(y, {
                                            mutationResult: i,
                                            queryName: h && u.getOperationName(h) || void 0,
                                            queryVariables: p
                                        });
                                        v && o.push({
                                            result: v,
                                            dataId: "ROOT_QUERY",
                                            query: h,
                                            variables: p
                                        })
                                    }
                                }
                            }))
                        }
                        if (o.length > 0 || e.refetchQueries || e.update || e.onQueryUpdated || e.removeOptimistic) {
                            var f = [];
                            if (this.refetchQueries({
                                    updateCache: function(t) {
                                        a || o.forEach((function(e) {
                                            return t.write(e)
                                        }));
                                        var s = e.update,
                                            c = !u.isExecutionPatchResult(i) || u.isExecutionPatchIncrementalResult(i) && !i.hasNext;
                                        if (s) {
                                            if (!a) {
                                                var l = t.diff({
                                                    id: "ROOT_MUTATION",
                                                    query: r.getDocumentInfo(e.document).asQuery,
                                                    variables: e.variables,
                                                    optimistic: !1,
                                                    returnPartialData: !0
                                                });
                                                l.complete && ("incremental" in (i = n.__assign(n.__assign({}, i), {
                                                    data: l.result
                                                })) && delete i.incremental, "hasNext" in i && delete i.hasNext)
                                            }
                                            c && s(t, i, {
                                                context: e.context,
                                                variables: e.variables
                                            })
                                        }
                                        a || e.keepRootFields || !c || t.modify({
                                            id: "ROOT_MUTATION",
                                            fields: function(e, t) {
                                                var r = t.fieldName,
                                                    n = t.DELETE;
                                                return "__typename" === r ? e : n
                                            }
                                        })
                                    },
                                    include: e.refetchQueries,
                                    optimistic: !1,
                                    removeOptimistic: e.removeOptimistic,
                                    onQueryUpdated: e.onQueryUpdated || null
                                }).forEach((function(e) {
                                    return f.push(e)
                                })), e.awaitRefetchQueries || e.onQueryUpdated) return Promise.all(f).then((function() {
                                return i
                            }))
                        }
                        return Promise.resolve(i)
                    }, e.prototype.markMutationOptimistic = function(e, t) {
                        var r = this,
                            o = "function" == typeof e ? e(t.variables) : e;
                        return this.cache.recordOptimisticTransaction((function(e) {
                            try {
                                r.markMutationResult(n.__assign(n.__assign({}, t), {
                                    result: {
                                        data: o
                                    }
                                }), e)
                            } catch (e) {
                                !1 !== globalThis.__DEV__ && i.invariant.error(e)
                            }
                        }), t.mutationId)
                    }, e.prototype.fetchQuery = function(e, t, r) {
                        return this.fetchConcastWithInfo(e, t, r).concast.promise
                    }, e.prototype.getQueryStore = function() {
                        var e = Object.create(null);
                        return this.queries.forEach((function(t, r) {
                            e[r] = {
                                variables: t.variables,
                                networkStatus: t.networkStatus,
                                networkError: t.networkError,
                                graphQLErrors: t.graphQLErrors
                            }
                        })), e
                    }, e.prototype.resetErrors = function(e) {
                        var t = this.queries.get(e);
                        t && (t.networkError = void 0, t.graphQLErrors = [])
                    }, e.prototype.transform = function(e) {
                        return this.documentTransform.transformDocument(e)
                    }, e.prototype.getDocumentInfo = function(e) {
                        var t = this.transformCache;
                        if (!t.has(e)) {
                            var r = {
                                hasClientExports: u.hasClientExports(e),
                                hasForcedResolvers: this.localState.shouldForceResolvers(e),
                                hasNonreactiveDirective: u.hasDirectives(["nonreactive"], e),
                                clientQuery: this.localState.clientQuery(e),
                                serverQuery: u.removeDirectivesFromDocument([{
                                    name: "client",
                                    remove: !0
                                }, {
                                    name: "connection"
                                }, {
                                    name: "nonreactive"
                                }], e),
                                defaultVars: u.getDefaultValues(u.getOperationDefinition(e)),
                                asQuery: n.__assign(n.__assign({}, e), {
                                    definitions: e.definitions.map((function(e) {
                                        return "OperationDefinition" === e.kind && "query" !== e.operation ? n.__assign(n.__assign({}, e), {
                                            operation: "query"
                                        }) : e
                                    }))
                                })
                            };
                            t.set(e, r)
                        }
                        return t.get(e)
                    }, e.prototype.getVariables = function(e, t) {
                        return n.__assign(n.__assign({}, this.getDocumentInfo(e).defaultVars), t)
                    }, e.prototype.watchQuery = function(e) {
                        var t = this.transform(e.query);
                        void 0 === (e = n.__assign(n.__assign({}, e), {
                            variables: this.getVariables(t, e.variables)
                        })).notifyOnNetworkStatusChange && (e.notifyOnNetworkStatusChange = !1);
                        var r = new I(this),
                            i = new F({
                                queryManager: this,
                                queryInfo: r,
                                options: e
                            });
                        return i.lastQuery = t, this.queries.set(i.queryId, r), r.init({
                            document: t,
                            observableQuery: i,
                            variables: i.variables
                        }), i
                    }, e.prototype.query = function(e, t) {
                        var r = this;
                        return void 0 === t && (t = this.generateQueryId()), i.invariant(e.query, 26), i.invariant("Document" === e.query.kind, 27), i.invariant(!e.returnPartialData, 28), i.invariant(!e.pollInterval, 29), this.fetchQuery(t, n.__assign(n.__assign({}, e), {
                            query: this.transform(e.query)
                        })).finally((function() {
                            return r.stopQuery(t)
                        }))
                    }, e.prototype.generateQueryId = function() {
                        return String(this.queryIdCounter++)
                    }, e.prototype.generateRequestId = function() {
                        return this.requestIdCounter++
                    }, e.prototype.generateMutationId = function() {
                        return String(this.mutationIdCounter++)
                    }, e.prototype.stopQueryInStore = function(e) {
                        this.stopQueryInStoreNoBroadcast(e), this.broadcastQueries()
                    }, e.prototype.stopQueryInStoreNoBroadcast = function(e) {
                        var t = this.queries.get(e);
                        t && t.stop()
                    }, e.prototype.clearStore = function(e) {
                        return void 0 === e && (e = {
                            discardWatches: !0
                        }), this.cancelPendingFetches(i.newInvariantError(30)), this.queries.forEach((function(e) {
                            e.observableQuery ? e.networkStatus = t.NetworkStatus.loading : e.stop()
                        })), this.mutationStore && (this.mutationStore = Object.create(null)), this.cache.reset(e)
                    }, e.prototype.getObservableQueries = function(e) {
                        var t = this;
                        void 0 === e && (e = "active");
                        var r = new Map,
                            o = new Map,
                            a = new Set;
                        return Array.isArray(e) && e.forEach((function(e) {
                            "string" == typeof e ? o.set(e, !1) : u.isDocumentNode(e) ? o.set(t.transform(e), !1) : u.isNonNullObject(e) && e.query && a.add(e)
                        })), this.queries.forEach((function(t, n) {
                            var i = t.observableQuery,
                                a = t.document;
                            if (i) {
                                if ("all" === e) return void r.set(n, i);
                                var s = i.queryName;
                                if ("standby" === i.options.fetchPolicy || "active" === e && !i.hasObservers()) return;
                                ("active" === e || s && o.has(s) || a && o.has(a)) && (r.set(n, i), s && o.set(s, !0), a && o.set(a, !0))
                            }
                        })), a.size && a.forEach((function(e) {
                            var o = u.makeUniqueId("legacyOneTimeQuery"),
                                a = t.getQuery(o).init({
                                    document: e.query,
                                    variables: e.variables
                                }),
                                s = new F({
                                    queryManager: t,
                                    queryInfo: a,
                                    options: n.__assign(n.__assign({}, e), {
                                        fetchPolicy: "network-only"
                                    })
                                });
                            i.invariant(s.queryId === o), a.setObservableQuery(s), r.set(o, s)
                        })), !1 !== globalThis.__DEV__ && o.size && o.forEach((function(e, t) {
                            e || !1 !== globalThis.__DEV__ && i.invariant.warn("string" == typeof t ? 31 : 32, t)
                        })), r
                    }, e.prototype.reFetchObservableQueries = function(e) {
                        var t = this;
                        void 0 === e && (e = !1);
                        var r = [];
                        return this.getObservableQueries(e ? "all" : "active").forEach((function(n, i) {
                            var o = n.options.fetchPolicy;
                            n.resetLastResults(), (e || "standby" !== o && "cache-only" !== o) && r.push(n.refetch()), t.getQuery(i).setDiff(null)
                        })), this.broadcastQueries(), Promise.all(r)
                    }, e.prototype.setObservableQuery = function(e) {
                        this.getQuery(e.queryId).setObservableQuery(e)
                    }, e.prototype.startGraphQLSubscription = function(e) {
                        var t = this,
                            r = e.query,
                            n = e.fetchPolicy,
                            i = e.errorPolicy,
                            o = e.variables,
                            a = e.context,
                            s = void 0 === a ? {} : a;
                        r = this.transform(r), o = this.getVariables(r, o);
                        var c = function(e) {
                            return t.getObservableFromLink(r, s, e).map((function(o) {
                                "no-cache" !== n && (A(o, i) && t.cache.write({
                                    query: r,
                                    result: o.data,
                                    dataId: "ROOT_SUBSCRIPTION",
                                    variables: e
                                }), t.broadcastQueries());
                                var a = u.graphQLResultHasError(o),
                                    s = l.graphQLResultHasProtocolErrors(o);
                                if (a || s) {
                                    var c = {};
                                    throw a && (c.graphQLErrors = o.errors), s && (c.protocolErrors = o.extensions[l.PROTOCOL_ERRORS_SYMBOL]), new l.ApolloError(c)
                                }
                                return o
                            }))
                        };
                        if (this.getDocumentInfo(r).hasClientExports) {
                            var f = this.localState.addExportedVariables(r, o, s).then(c);
                            return new u.Observable((function(e) {
                                var t = null;
                                return f.then((function(r) {
                                        return t = r.subscribe(e)
                                    }), e.error),
                                    function() {
                                        return t && t.unsubscribe()
                                    }
                            }))
                        }
                        return c(o)
                    }, e.prototype.stopQuery = function(e) {
                        this.stopQueryNoBroadcast(e), this.broadcastQueries()
                    }, e.prototype.stopQueryNoBroadcast = function(e) {
                        this.stopQueryInStoreNoBroadcast(e), this.removeQuery(e)
                    }, e.prototype.removeQuery = function(e) {
                        this.fetchCancelFns.delete(e), this.queries.has(e) && (this.getQuery(e).stop(), this.queries.delete(e))
                    }, e.prototype.broadcastQueries = function() {
                        this.onBroadcast && this.onBroadcast(), this.queries.forEach((function(e) {
                            return e.notify()
                        }))
                    }, e.prototype.getLocalState = function() {
                        return this.localState
                    }, e.prototype.getObservableFromLink = function(e, t, r, i) {
                        var a, s, l = this;
                        void 0 === i && (i = null !== (a = null == t ? void 0 : t.queryDeduplication) && void 0 !== a ? a : this.queryDeduplication);
                        var f = this.getDocumentInfo(e),
                            h = f.serverQuery,
                            p = f.clientQuery;
                        if (h) {
                            var d = this.inFlightLinkObservables,
                                y = this.link,
                                v = {
                                    query: h,
                                    variables: r,
                                    operationName: u.getOperationName(h) || void 0,
                                    context: this.prepareContext(n.__assign(n.__assign({}, t), {
                                        forceFetch: !i
                                    }))
                                };
                            if (t = v.context, i) {
                                var m = u.print(h),
                                    g = d.get(m) || new Map;
                                d.set(m, g);
                                var b = c.canonicalStringify(r);
                                if (!(s = g.get(b))) {
                                    var _ = new u.Concast([o.execute(y, v)]);
                                    g.set(b, s = _), _.beforeNext((function() {
                                        g.delete(b) && g.size < 1 && d.delete(m)
                                    }))
                                }
                            } else s = new u.Concast([o.execute(y, v)])
                        } else s = new u.Concast([u.Observable.of({
                            data: {}
                        })]), t = this.prepareContext(t);
                        return p && (s = u.asyncMap(s, (function(e) {
                            return l.localState.runResolvers({
                                document: p,
                                remoteResult: e,
                                context: t,
                                variables: r
                            })
                        }))), s
                    }, e.prototype.getResultsFromLink = function(e, r, n) {
                        var i = e.lastRequestId = this.generateRequestId(),
                            o = this.cache.transformForLink(n.query);
                        return u.asyncMap(this.getObservableFromLink(o, n.context, n.variables), (function(a) {
                            var s = u.getGraphQLErrorsFromResult(a),
                                c = s.length > 0;
                            if (i >= e.lastRequestId) {
                                if (c && "none" === n.errorPolicy) throw e.markError(new l.ApolloError({
                                    graphQLErrors: s
                                }));
                                e.markResult(a, o, n, r), e.markReady()
                            }
                            var f = {
                                data: a.data,
                                loading: !1,
                                networkStatus: t.NetworkStatus.ready
                            };
                            return c && "ignore" !== n.errorPolicy && (f.errors = s, f.networkStatus = t.NetworkStatus.error), f
                        }), (function(t) {
                            var r = l.isApolloError(t) ? t : new l.ApolloError({
                                networkError: t
                            });
                            throw i >= e.lastRequestId && e.markError(r), r
                        }))
                    }, e.prototype.fetchConcastWithInfo = function(e, r, n) {
                        var i = this;
                        void 0 === n && (n = t.NetworkStatus.loading);
                        var o, a, s = r.query,
                            c = this.getVariables(s, r.variables),
                            l = this.getQuery(e),
                            f = this.defaultOptions.watchQuery,
                            h = r.fetchPolicy,
                            p = void 0 === h ? f && f.fetchPolicy || "cache-first" : h,
                            d = r.errorPolicy,
                            y = void 0 === d ? f && f.errorPolicy || "none" : d,
                            v = r.returnPartialData,
                            m = void 0 !== v && v,
                            g = r.notifyOnNetworkStatusChange,
                            b = void 0 !== g && g,
                            _ = r.context,
                            w = void 0 === _ ? {} : _,
                            O = Object.assign({}, r, {
                                query: s,
                                variables: c,
                                fetchPolicy: p,
                                errorPolicy: y,
                                returnPartialData: m,
                                notifyOnNetworkStatusChange: b,
                                context: w
                            }),
                            k = function(e) {
                                O.variables = e;
                                var t = i.fetchQueryByPolicy(l, O, n);
                                return "standby" !== O.fetchPolicy && t.sources.length > 0 && l.observableQuery && l.observableQuery.applyNextFetchPolicy("after-fetch", r), t
                            },
                            S = function() {
                                return i.fetchCancelFns.delete(e)
                            };
                        if (this.fetchCancelFns.set(e, (function(e) {
                                S(), setTimeout((function() {
                                    return o.cancel(e)
                                }))
                            })), this.getDocumentInfo(O.query).hasClientExports) o = new u.Concast(this.localState.addExportedVariables(O.query, O.variables, O.context).then(k).then((function(e) {
                            return e.sources
                        }))), a = !0;
                        else {
                            var E = k(O.variables);
                            a = E.fromLink, o = new u.Concast(E.sources)
                        }
                        return o.promise.then(S, S), {
                            concast: o,
                            fromLink: a
                        }
                    }, e.prototype.refetchQueries = function(e) {
                        var t = this,
                            r = e.updateCache,
                            n = e.include,
                            i = e.optimistic,
                            o = void 0 !== i && i,
                            a = e.removeOptimistic,
                            s = void 0 === a ? o ? u.makeUniqueId("refetchQueries") : void 0 : a,
                            c = e.onQueryUpdated,
                            l = new Map;
                        n && this.getObservableQueries(n).forEach((function(e, r) {
                            l.set(r, {
                                oq: e,
                                lastDiff: t.getQuery(r).getDiff()
                            })
                        }));
                        var f = new Map;
                        return r && this.cache.batch({
                            update: r,
                            optimistic: o && s || !1,
                            removeOptimistic: s,
                            onWatchUpdated: function(e, t, r) {
                                var n = e.watcher instanceof I && e.watcher.observableQuery;
                                if (n) {
                                    if (c) {
                                        l.delete(n.queryId);
                                        var i = c(n, t, r);
                                        return !0 === i && (i = n.refetch()), !1 !== i && f.set(n, i), i
                                    }
                                    null !== c && l.set(n.queryId, {
                                        oq: n,
                                        lastDiff: r,
                                        diff: t
                                    })
                                }
                            }
                        }), l.size && l.forEach((function(e, r) {
                            var n, i = e.oq,
                                o = e.lastDiff,
                                a = e.diff;
                            if (c) {
                                if (!a) {
                                    var s = i.queryInfo;
                                    s.reset(), a = s.getDiff()
                                }
                                n = c(i, a, o)
                            }
                            c && !0 !== n || (n = i.refetch()), !1 !== n && f.set(i, n), r.indexOf("legacyOneTimeQuery") >= 0 && t.stopQueryNoBroadcast(r)
                        })), s && this.cache.removeOptimistic(s), f
                    }, e.prototype.fetchQueryByPolicy = function(e, r, i) {
                        var o = this,
                            a = r.query,
                            c = r.variables,
                            l = r.fetchPolicy,
                            f = r.refetchWritePolicy,
                            h = r.errorPolicy,
                            p = r.returnPartialData,
                            d = r.context,
                            y = r.notifyOnNetworkStatusChange,
                            v = e.networkStatus;
                        e.init({
                            document: a,
                            variables: c,
                            networkStatus: i
                        });
                        var m = function() {
                                return e.getDiff(c)
                            },
                            g = function(r, i) {
                                void 0 === i && (i = e.networkStatus || t.NetworkStatus.loading);
                                var l = r.result;
                                !1 === globalThis.__DEV__ || p || s.equal(l, {}) || C(r.missing);
                                var f = function(e) {
                                    return u.Observable.of(n.__assign({
                                        data: e,
                                        loading: O(i),
                                        networkStatus: i
                                    }, r.complete ? null : {
                                        partial: !0
                                    }))
                                };
                                return l && o.getDocumentInfo(a).hasForcedResolvers ? o.localState.runResolvers({
                                    document: a,
                                    remoteResult: {
                                        data: l
                                    },
                                    context: d,
                                    variables: c,
                                    onlyRunForcedResolvers: !0
                                }).then((function(e) {
                                    return f(e.data || void 0)
                                })) : "none" === h && i === t.NetworkStatus.refetch && Array.isArray(r.missing) ? f(void 0) : f(l)
                            },
                            b = "no-cache" === l ? 0 : i === t.NetworkStatus.refetch && "merge" !== f ? 1 : 2,
                            _ = function() {
                                return o.getResultsFromLink(e, b, {
                                    query: a,
                                    variables: c,
                                    context: d,
                                    fetchPolicy: l,
                                    errorPolicy: h
                                })
                            },
                            w = y && "number" == typeof v && v !== i && O(i);
                        switch (l) {
                            default:
                                case "cache-first":
                                return (k = m()).complete ? {
                                fromLink: !1,
                                sources: [g(k, e.markReady())]
                            } : p || w ? {
                                fromLink: !0,
                                sources: [g(k), _()]
                            } : {
                                fromLink: !0,
                                sources: [_()]
                            };
                            case "cache-and-network":
                                    var k;
                                return (k = m()).complete || p || w ? {
                                    fromLink: !0,
                                    sources: [g(k), _()]
                                } : {
                                    fromLink: !0,
                                    sources: [_()]
                                };
                            case "cache-only":
                                    return {
                                    fromLink: !1,
                                    sources: [g(m(), e.markReady())]
                                };
                            case "network-only":
                                    return w ? {
                                    fromLink: !0,
                                    sources: [g(m()), _()]
                                } : {
                                    fromLink: !0,
                                    sources: [_()]
                                };
                            case "no-cache":
                                    return w ? {
                                    fromLink: !0,
                                    sources: [g(e.getDiff()), _()]
                                } : {
                                    fromLink: !0,
                                    sources: [_()]
                                };
                            case "standby":
                                    return {
                                    fromLink: !1,
                                    sources: []
                                }
                        }
                    }, e.prototype.getQuery = function(e) {
                        return e && !this.queries.has(e) && this.queries.set(e, new I(this, e)), this.queries.get(e)
                    }, e.prototype.prepareContext = function(e) {
                        void 0 === e && (e = {});
                        var t = this.localState.prepareContext(e);
                        return n.__assign(n.__assign({}, t), {
                            clientAwareness: this.clientAwareness
                        })
                    }, e
                }(),
                z = !1,
                W = function() {
                    function e(e) {
                        var t = this;
                        if (this.resetStoreCallbacks = [], this.clearStoreCallbacks = [], !e.cache) throw i.newInvariantError(13);
                        var r = e.uri,
                            n = e.credentials,
                            s = e.headers,
                            u = e.cache,
                            c = e.documentTransform,
                            l = e.ssrMode,
                            f = void 0 !== l && l,
                            h = e.ssrForceFetchDelay,
                            p = void 0 === h ? 0 : h,
                            d = e.connectToDevTools,
                            y = void 0 === d ? "object" == typeof window && !window.__APOLLO_CLIENT__ && !1 !== globalThis.__DEV__ : d,
                            v = e.queryDeduplication,
                            m = void 0 === v || v,
                            g = e.defaultOptions,
                            b = e.assumeImmutableResults,
                            _ = void 0 === b ? u.assumeImmutableResults : b,
                            w = e.resolvers,
                            O = e.typeDefs,
                            k = e.fragmentMatcher,
                            S = e.name,
                            E = e.version,
                            R = e.link;
                        if (R || (R = r ? new a.HttpLink({
                                uri: r,
                                credentials: n,
                                headers: s
                            }) : o.ApolloLink.empty()), this.link = R, this.cache = u, this.disableNetworkFetches = f || p > 0, this.queryDeduplication = m, this.defaultOptions = g || Object.create(null), this.typeDefs = O, p && setTimeout((function() {
                                return t.disableNetworkFetches = !1
                            }), p), this.watchQuery = this.watchQuery.bind(this), this.query = this.query.bind(this), this.mutate = this.mutate.bind(this), this.resetStore = this.resetStore.bind(this), this.reFetchObservableQueries = this.reFetchObservableQueries.bind(this), y && "object" == typeof window && (window.__APOLLO_CLIENT__ = this), !z && y && !1 !== globalThis.__DEV__ && (z = !0, "undefined" != typeof window && window.document && window.top === window.self && !window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__)) {
                            var D = window.navigator,
                                P = D && D.userAgent,
                                F = void 0;
                            "string" == typeof P && (P.indexOf("Chrome/") > -1 ? F = "https://chrome.google.com/webstore/detail/apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm" : P.indexOf("Firefox/") > -1 && (F = "https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/")), F && !1 !== globalThis.__DEV__ && i.invariant.log("Download the Apollo DevTools for a better development experience: %s", F)
                        }
                        this.version = "3.8.1", this.localState = new M({
                            cache: u,
                            client: this,
                            resolvers: w,
                            fragmentMatcher: k
                        }), this.queryManager = new V({
                            cache: this.cache,
                            link: this.link,
                            defaultOptions: this.defaultOptions,
                            documentTransform: c,
                            queryDeduplication: m,
                            ssrMode: f,
                            clientAwareness: {
                                name: S,
                                version: E
                            },
                            localState: this.localState,
                            assumeImmutableResults: _,
                            onBroadcast: y ? function() {
                                t.devToolsHookCb && t.devToolsHookCb({
                                    action: {},
                                    state: {
                                        queries: t.queryManager.getQueryStore(),
                                        mutations: t.queryManager.mutationStore || {}
                                    },
                                    dataWithOptimisticResults: t.cache.extract(!0)
                                })
                            } : void 0
                        })
                    }
                    return Object.defineProperty(e.prototype, "documentTransform", {
                        get: function() {
                            return this.queryManager.documentTransform
                        },
                        enumerable: !1,
                        configurable: !0
                    }), e.prototype.stop = function() {
                        this.queryManager.stop()
                    }, e.prototype.watchQuery = function(e) {
                        return this.defaultOptions.watchQuery && (e = u.mergeOptions(this.defaultOptions.watchQuery, e)), !this.disableNetworkFetches || "network-only" !== e.fetchPolicy && "cache-and-network" !== e.fetchPolicy || (e = n.__assign(n.__assign({}, e), {
                            fetchPolicy: "cache-first"
                        })), this.queryManager.watchQuery(e)
                    }, e.prototype.query = function(e) {
                        return this.defaultOptions.query && (e = u.mergeOptions(this.defaultOptions.query, e)), i.invariant("cache-and-network" !== e.fetchPolicy, 14), this.disableNetworkFetches && "network-only" === e.fetchPolicy && (e = n.__assign(n.__assign({}, e), {
                            fetchPolicy: "cache-first"
                        })), this.queryManager.query(e)
                    }, e.prototype.mutate = function(e) {
                        return this.defaultOptions.mutate && (e = u.mergeOptions(this.defaultOptions.mutate, e)), this.queryManager.mutate(e)
                    }, e.prototype.subscribe = function(e) {
                        return this.queryManager.startGraphQLSubscription(e)
                    }, e.prototype.readQuery = function(e, t) {
                        return void 0 === t && (t = !1), this.cache.readQuery(e, t)
                    }, e.prototype.readFragment = function(e, t) {
                        return void 0 === t && (t = !1), this.cache.readFragment(e, t)
                    }, e.prototype.writeQuery = function(e) {
                        var t = this.cache.writeQuery(e);
                        return !1 !== e.broadcast && this.queryManager.broadcastQueries(), t
                    }, e.prototype.writeFragment = function(e) {
                        var t = this.cache.writeFragment(e);
                        return !1 !== e.broadcast && this.queryManager.broadcastQueries(), t
                    }, e.prototype.__actionHookForDevTools = function(e) {
                        this.devToolsHookCb = e
                    }, e.prototype.__requestRaw = function(e) {
                        return o.execute(this.link, e)
                    }, e.prototype.resetStore = function() {
                        var e = this;
                        return Promise.resolve().then((function() {
                            return e.queryManager.clearStore({
                                discardWatches: !1
                            })
                        })).then((function() {
                            return Promise.all(e.resetStoreCallbacks.map((function(e) {
                                return e()
                            })))
                        })).then((function() {
                            return e.reFetchObservableQueries()
                        }))
                    }, e.prototype.clearStore = function() {
                        var e = this;
                        return Promise.resolve().then((function() {
                            return e.queryManager.clearStore({
                                discardWatches: !0
                            })
                        })).then((function() {
                            return Promise.all(e.clearStoreCallbacks.map((function(e) {
                                return e()
                            })))
                        }))
                    }, e.prototype.onResetStore = function(e) {
                        var t = this;
                        return this.resetStoreCallbacks.push(e),
                            function() {
                                t.resetStoreCallbacks = t.resetStoreCallbacks.filter((function(t) {
                                    return t !== e
                                }))
                            }
                    }, e.prototype.onClearStore = function(e) {
                        var t = this;
                        return this.clearStoreCallbacks.push(e),
                            function() {
                                t.clearStoreCallbacks = t.clearStoreCallbacks.filter((function(t) {
                                    return t !== e
                                }))
                            }
                    }, e.prototype.reFetchObservableQueries = function(e) {
                        return this.queryManager.reFetchObservableQueries(e)
                    }, e.prototype.refetchQueries = function(e) {
                        var t = this.queryManager.refetchQueries(e),
                            r = [],
                            n = [];
                        t.forEach((function(e, t) {
                            r.push(t), n.push(e)
                        }));
                        var o = Promise.all(n);
                        return o.queries = r, o.results = n, o.catch((function(e) {
                            !1 !== globalThis.__DEV__ && i.invariant.debug(15, e)
                        })), o
                    }, e.prototype.getObservableQueries = function(e) {
                        return void 0 === e && (e = "active"), this.queryManager.getObservableQueries(e)
                    }, e.prototype.extract = function(e) {
                        return this.cache.extract(e)
                    }, e.prototype.restore = function(e) {
                        return this.cache.restore(e)
                    }, e.prototype.addResolvers = function(e) {
                        this.localState.addResolvers(e)
                    }, e.prototype.setResolvers = function(e) {
                        this.localState.setResolvers(e)
                    }, e.prototype.getResolvers = function() {
                        return this.localState.getResolvers()
                    }, e.prototype.setLocalStateFragmentMatcher = function(e) {
                        this.localState.setFragmentMatcher(e)
                    }, e.prototype.setLink = function(e) {
                        this.link = this.queryManager.link = e
                    }, e
                }();
            for (var U in p.setVerbosity(!1 !== globalThis.__DEV__ ? "log" : "silent"), t.DocumentTransform = u.DocumentTransform, t.Observable = u.Observable, t.isReference = u.isReference, t.makeReference = u.makeReference, t.mergeOptions = u.mergeOptions, t.ApolloCache = c.ApolloCache, t.Cache = c.Cache, t.InMemoryCache = c.InMemoryCache, t.MissingFieldError = c.MissingFieldError, t.defaultDataIdFromObject = c.defaultDataIdFromObject, t.makeVar = c.makeVar, t.ApolloError = l.ApolloError, t.isApolloError = l.isApolloError, t.fromError = h.fromError, t.fromPromise = h.fromPromise, t.throwServerError = h.throwServerError, t.toPromise = h.toPromise, t.setLogVerbosity = p.setVerbosity, t.disableExperimentalFragmentVariables = d.disableExperimentalFragmentVariables, t.disableFragmentWarnings = d.disableFragmentWarnings, t.enableExperimentalFragmentVariables = d.enableExperimentalFragmentVariables, t.gql = d.gql, t.resetCaches = d.resetCaches, t.ApolloClient = W, t.ObservableQuery = F, t.isNetworkRequestSettled = function(e) {
                    return 7 === e || 8 === e
                }, o) "default" === U || t.hasOwnProperty(U) || (t[U] = o[U]);
            for (var U in a) "default" === U || t.hasOwnProperty(U) || (t[U] = a[U])
        },
        49860: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(22970);
            r(57304);
            var i = r(73805),
                o = Symbol(),
                a = function(e) {
                    function t(r) {
                        var o, a, s = r.graphQLErrors,
                            u = r.protocolErrors,
                            c = r.clientErrors,
                            l = r.networkError,
                            f = r.errorMessage,
                            h = r.extraInfo,
                            p = e.call(this, f) || this;
                        return p.name = "ApolloError", p.graphQLErrors = s || [], p.protocolErrors = u || [], p.clientErrors = c || [], p.networkError = l || null, p.message = f || (o = p, a = n.__spreadArray(n.__spreadArray(n.__spreadArray([], o.graphQLErrors, !0), o.clientErrors, !0), o.protocolErrors, !0), o.networkError && a.push(o.networkError), a.map((function(e) {
                            return i.isNonNullObject(e) && e.message || "Error message not found."
                        })).join("\n")), p.extraInfo = h, p.__proto__ = t.prototype, p
                    }
                    return n.__extends(t, e), t
                }(Error);
            t.ApolloError = a, t.PROTOCOL_ERRORS_SYMBOL = o, t.graphQLResultHasProtocolErrors = function(e) {
                return !!e.extensions && Array.isArray(e.extensions[o])
            }, t.isApolloError = function(e) {
                return e.hasOwnProperty("graphQLErrors")
            }
        },
        98938: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(57304),
                i = r(22970),
                o = r(73805),
                a = r(63781),
                s = r(49860),
                u = r(4110);

            function c(e) {
                var t = {
                    next: function() {
                        return e.read()
                    }
                };
                return o.canUseAsyncIteratorSymbol && (t[Symbol.asyncIterator] = function() {
                    return this
                }), t
            }

            function l(e) {
                var t, r, n, i = e;
                if (e.body && (i = e.body), n = i, o.canUseAsyncIteratorSymbol && n[Symbol.asyncIterator]) return r = i[Symbol.asyncIterator](), (t = {
                    next: function() {
                        return r.next()
                    }
                })[Symbol.asyncIterator] = function() {
                    return this
                }, t;
                if (function(e) {
                        return !!e.getReader
                    }(i)) return c(i.getReader());
                if (function(e) {
                        return !!e.stream
                    }(i)) return c(i.stream().getReader());
                if (function(e) {
                        return !!e.arrayBuffer
                    }(i)) return function(e) {
                    var t = !1,
                        r = {
                            next: function() {
                                return t ? Promise.resolve({
                                    value: void 0,
                                    done: !0
                                }) : (t = !0, new Promise((function(t, r) {
                                    e.then((function(e) {
                                        t({
                                            value: e,
                                            done: !1
                                        })
                                    })).catch(r)
                                })))
                            }
                        };
                    return o.canUseAsyncIteratorSymbol && (r[Symbol.asyncIterator] = function() {
                        return this
                    }), r
                }(i.arrayBuffer());
                if (function(e) {
                        return !!e.pipe
                    }(i)) return function(e) {
                    var t = null,
                        r = null,
                        n = !1,
                        i = [],
                        a = [];

                    function s(e) {
                        if (!r) {
                            if (a.length) {
                                var t = a.shift();
                                if (Array.isArray(t) && t[0]) return t[0]({
                                    value: e,
                                    done: !1
                                })
                            }
                            i.push(e)
                        }
                    }

                    function u(e) {
                        r = e, a.slice().forEach((function(t) {
                            t[1](e)
                        })), !t || t()
                    }

                    function c() {
                        n = !0, a.slice().forEach((function(e) {
                            e[0]({
                                value: void 0,
                                done: !0
                            })
                        })), !t || t()
                    }
                    t = function() {
                        t = null, e.removeListener("data", s), e.removeListener("error", u), e.removeListener("end", c), e.removeListener("finish", c), e.removeListener("close", c)
                    }, e.on("data", s), e.on("error", u), e.on("end", c), e.on("finish", c), e.on("close", c);
                    var l = {
                        next: function() {
                            return new Promise((function(e, t) {
                                return r ? t(r) : i.length ? e({
                                    value: i.shift(),
                                    done: !1
                                }) : n ? e({
                                    value: void 0,
                                    done: !0
                                }) : void a.push([e, t])
                            }))
                        }
                    };
                    return o.canUseAsyncIteratorSymbol && (l[Symbol.asyncIterator] = function() {
                        return this
                    }), l
                }(i);
                throw new Error("Unknown body type for responseIterator. Please pass a streamable response.")
            }

            function f(e) {
                return null !== (t = e) && "object" == typeof t && "payload" in e;
                var t
            }
            var h = Object.prototype.hasOwnProperty;

            function p(e) {
                var t = {};
                return e.split("\n").forEach((function(e) {
                    var r = e.indexOf(":");
                    if (r > -1) {
                        var n = e.slice(0, r).trim().toLowerCase(),
                            i = e.slice(r + 1).trim();
                        t[n] = i
                    }
                })), t
            }

            function d(e, t) {
                e.status >= 300 && a.throwServerError(e, function() {
                    try {
                        return JSON.parse(t)
                    } catch (e) {
                        return t
                    }
                }(), "Response not successful: Received status code ".concat(e.status));
                try {
                    return JSON.parse(t)
                } catch (n) {
                    var r = n;
                    throw r.name = "ServerParseError", r.response = e, r.statusCode = e.status, r.bodyText = t, r
                }
            }

            function y(e) {
                return function(t) {
                    return t.text().then((function(e) {
                        return d(t, e)
                    })).then((function(r) {
                        return t.status >= 300 && a.throwServerError(t, r, "Response not successful: Received status code ".concat(t.status)), Array.isArray(r) || h.call(r, "data") || h.call(r, "errors") || a.throwServerError(t, r, "Server response was missing for query '".concat(Array.isArray(e) ? e.map((function(e) {
                            return e.operationName
                        })) : e.operationName, "'.")), r
                    }))
                }
            }
            var v = function(e, t) {
                    var r;
                    try {
                        r = JSON.stringify(e)
                    } catch (e) {
                        var i = n.newInvariantError(37, t, e.message);
                        throw i.parseError = e, i
                    }
                    return r
                },
                m = {
                    http: {
                        includeQuery: !0,
                        includeExtensions: !1,
                        preserveHeaderCase: !1
                    },
                    headers: {
                        accept: "*/*",
                        "content-type": "application/json"
                    },
                    options: {
                        method: "POST"
                    }
                },
                g = function(e, t) {
                    return t(e)
                };

            function b(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                var a = {},
                    s = {};
                r.forEach((function(e) {
                    a = i.__assign(i.__assign(i.__assign({}, a), e.options), {
                        headers: i.__assign(i.__assign({}, a.headers), e.headers)
                    }), e.credentials && (a.credentials = e.credentials), s = i.__assign(i.__assign({}, s), e.http)
                })), a.headers && (a.headers = function(e, t) {
                    if (!t) {
                        var r = Object.create(null);
                        return Object.keys(Object(e)).forEach((function(t) {
                            r[t.toLowerCase()] = e[t]
                        })), r
                    }
                    var n = Object.create(null);
                    Object.keys(Object(e)).forEach((function(t) {
                        n[t.toLowerCase()] = {
                            originalName: t,
                            value: e[t]
                        }
                    }));
                    var i = Object.create(null);
                    return Object.keys(n).forEach((function(e) {
                        i[n[e].originalName] = n[e].value
                    })), i
                }(a.headers, s.preserveHeaderCase));
                var u = e.operationName,
                    c = e.extensions,
                    l = e.variables,
                    f = e.query,
                    h = {
                        operationName: u,
                        variables: l
                    };
                return s.includeExtensions && (h.extensions = c), s.includeQuery && (h.query = t(f, o.print)), {
                    options: a,
                    body: h
                }
            }
            var _ = function(e) {
                    if (!e && "undefined" == typeof fetch) throw n.newInvariantError(35)
                },
                w = function(e, t) {
                    return e.getContext().uri || ("function" == typeof t ? t(e) : t || "/graphql")
                };

            function O(e, t) {
                var r = [],
                    n = function(e, t) {
                        r.push("".concat(e, "=").concat(encodeURIComponent(t)))
                    };
                if ("query" in t && n("query", t.query), t.operationName && n("operationName", t.operationName), t.variables) {
                    var i = void 0;
                    try {
                        i = v(t.variables, "Variables map")
                    } catch (e) {
                        return {
                            parseError: e
                        }
                    }
                    n("variables", i)
                }
                if (t.extensions) {
                    var o = void 0;
                    try {
                        o = v(t.extensions, "Extensions map")
                    } catch (e) {
                        return {
                            parseError: e
                        }
                    }
                    n("extensions", o)
                }
                var a = "",
                    s = e,
                    u = e.indexOf("#"); - 1 !== u && (a = e.substr(u), s = e.substr(0, u));
                var c = -1 === s.indexOf("?") ? "?" : "&";
                return {
                    newURI: s + c + r.join("&") + a
                }
            }
            var k = o.maybe((function() {
                    return fetch
                })),
                S = function(e) {
                    void 0 === e && (e = {});
                    var t = e.uri,
                        r = void 0 === t ? "/graphql" : t,
                        c = e.fetch,
                        h = e.print,
                        S = void 0 === h ? g : h,
                        E = e.includeExtensions,
                        R = e.preserveHeaderCase,
                        D = e.useGETForQueries,
                        P = e.includeUnusedVariables,
                        F = void 0 !== P && P,
                        T = i.__rest(e, ["uri", "fetch", "print", "includeExtensions", "preserveHeaderCase", "useGETForQueries", "includeUnusedVariables"]);
                    !1 !== globalThis.__DEV__ && _(c || k);
                    var q = {
                        http: {
                            includeExtensions: E,
                            preserveHeaderCase: R
                        },
                        options: T.fetchOptions,
                        credentials: T.credentials,
                        headers: T.headers
                    };
                    return new u.ApolloLink((function(e) {
                        var t = w(e, r),
                            u = e.getContext(),
                            h = {};
                        if (u.clientAwareness) {
                            var g = u.clientAwareness,
                                _ = g.name,
                                E = g.version;
                            _ && (h["apollographql-client-name"] = _), E && (h["apollographql-client-version"] = E)
                        }
                        var R = i.__assign(i.__assign({}, h), u.headers),
                            P = {
                                http: u.http,
                                options: u.fetchOptions,
                                credentials: u.credentials,
                                headers: R
                            };
                        if (o.hasDirectives(["client"], e.query)) {
                            var T = o.removeClientSetsFromDocument(e.query);
                            if (!T) return a.fromError(new Error("HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`."));
                            e.query = T
                        }
                        var C, x = b(e, S, m, q, P),
                            M = x.options,
                            Q = x.body;
                        Q.variables && !F && (Q.variables = a.filterOperationVariables(Q.variables, e.query)), M.signal || "undefined" == typeof AbortController || (C = new AbortController, M.signal = C.signal);
                        var j, N = "OperationDefinition" === (j = o.getMainDefinition(e.query)).kind && "subscription" === j.operation,
                            I = o.hasDirectives(["defer"], e.query);
                        if (D && !e.query.definitions.some((function(e) {
                                return "OperationDefinition" === e.kind && "mutation" === e.operation
                            })) && (M.method = "GET"), I || N) {
                            M.headers = M.headers || {};
                            var A = "multipart/mixed;";
                            N && I && !1 !== globalThis.__DEV__ && n.invariant.warn(36), N ? A += "boundary=graphql;subscriptionSpec=1.0,application/json" : I && (A += "deferSpec=20220824,application/json"), M.headers.accept = A
                        }
                        if ("GET" === M.method) {
                            var L = O(t, Q),
                                V = L.newURI,
                                z = L.parseError;
                            if (z) return a.fromError(z);
                            t = V
                        } else try {
                            M.body = v(Q, "Payload")
                        } catch (z) {
                            return a.fromError(z)
                        }
                        return new o.Observable((function(r) {
                            var n = c || o.maybe((function() {
                                    return fetch
                                })) || k,
                                a = r.next.bind(r);
                            return n(t, M).then((function(t) {
                                    var r;
                                    e.setContext({
                                        response: t
                                    });
                                    var n = null === (r = t.headers) || void 0 === r ? void 0 : r.get("content-type");
                                    return null !== n && /^multipart\/mixed/i.test(n) ? function(e, t) {
                                        var r;
                                        return i.__awaiter(this, void 0, void 0, (function() {
                                            var n, o, a, u, c, h, y, v, m, g, b, _, w, O, k, S, E, R, D, P, F, T, q;
                                            return i.__generator(this, (function(C) {
                                                switch (C.label) {
                                                    case 0:
                                                        if (void 0 === TextDecoder) throw new Error("TextDecoder must be defined in the environment: please import a polyfill.");
                                                        n = new TextDecoder("utf-8"), o = null === (r = e.headers) || void 0 === r ? void 0 : r.get("content-type"), a = "boundary=", u = (null == o ? void 0 : o.includes(a)) ? null == o ? void 0 : o.substring((null == o ? void 0 : o.indexOf(a)) + 9).replace(/['"]/g, "").replace(/\;(.*)/gm, "").trim() : "-", c = "\r\n--".concat(u), h = "", y = l(e), v = !0, C.label = 1;
                                                    case 1:
                                                        return v ? [4, y.next()] : [3, 3];
                                                    case 2:
                                                        for (m = C.sent(), g = m.value, b = m.done, _ = "string" == typeof g ? g : n.decode(g), w = h.length - c.length + 1, v = !b, O = (h += _).indexOf(c, w); O > -1;) {
                                                            if (k = void 0, T = [h.slice(0, O), h.slice(O + c.length)], h = T[1], S = (k = T[0]).indexOf("\r\n\r\n"), E = p(k.slice(0, S)), (R = E["content-type"]) && -1 === R.toLowerCase().indexOf("application/json")) throw new Error("Unsupported patch content type: application/json is required.");
                                                            if (D = k.slice(S))
                                                                if (P = d(e, D), Object.keys(P).length > 1 || "data" in P || "incremental" in P || "errors" in P || "payload" in P) f(P) ? (F = {}, "payload" in P && (F = i.__assign({}, P.payload)), "errors" in P && (F = i.__assign(i.__assign({}, F), {
                                                                    extensions: i.__assign(i.__assign({}, "extensions" in F ? F.extensions : null), (q = {}, q[s.PROTOCOL_ERRORS_SYMBOL] = P.errors, q))
                                                                })), t(F)) : t(P);
                                                                else if (1 === Object.keys(P).length && "hasNext" in P && !P.hasNext) return [2];
                                                            O = h.indexOf(c)
                                                        }
                                                        return [3, 1];
                                                    case 3:
                                                        return [2]
                                                }
                                            }))
                                        }))
                                    }(t, a) : y(e)(t).then(a)
                                })).then((function() {
                                    C = void 0, r.complete()
                                })).catch((function(e) {
                                    C = void 0,
                                        function(e, t) {
                                            e.result && e.result.errors && e.result.data && t.next(e.result), t.error(e)
                                        }(e, r)
                                })),
                                function() {
                                    C && C.abort()
                                }
                        }))
                    }))
                },
                E = function(e) {
                    function t(t) {
                        void 0 === t && (t = {});
                        var r = e.call(this, S(t).request) || this;
                        return r.options = t, r
                    }
                    return i.__extends(t, e), t
                }(u.ApolloLink);
            t.HttpLink = E, t.checkFetcher = _, t.createHttpLink = S, t.createSignalIfSupported = function() {
                if ("undefined" == typeof AbortController) return {
                    controller: !1,
                    signal: !1
                };
                var e = new AbortController;
                return {
                    controller: e,
                    signal: e.signal
                }
            }, t.defaultPrinter = g, t.fallbackHttpConfig = m, t.parseAndCheckHttpResponse = y, t.rewriteURIForGET = O, t.selectHttpOptionsAndBody = function(e, t) {
                for (var r = [], n = 2; n < arguments.length; n++) r[n - 2] = arguments[n];
                return r.unshift(t), b.apply(void 0, i.__spreadArray([e, g], r, !1))
            }, t.selectHttpOptionsAndBodyInternal = b, t.selectURI = w, t.serializeFetchParameter = v
        },
        34261: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(32319),
                i = r(3920);
            for (var o in n) "default" === o || t.hasOwnProperty(o) || (t[o] = n[o]);
            for (var o in i) "default" === o || t.hasOwnProperty(o) || (t[o] = i[o])
        },
        55970: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(57304),
                i = r(69151),
                o = r(73805),
                a = r(22970);

            function s(e) {
                if (e && e.__esModule) return e;
                var t = Object.create(null);
                if (e)
                    for (var r in e) t[r] = e[r];
                return t.default = e, Object.freeze(t)
            }
            var u = s(i),
                c = o.canUseSymbol ? Symbol.for("__APOLLO_CONTEXT__") : "__APOLLO_CONTEXT__";

            function l() {
                n.invariant("createContext" in u, 43);
                var e = u.createContext[c];
                return e || (Object.defineProperty(u.createContext, c, {
                    value: e = u.createContext({}),
                    enumerable: !1,
                    writable: !1,
                    configurable: !0
                }), e.displayName = "ApolloContext"), e
            }
            var f = l;
            t.ApolloConsumer = function(e) {
                var t = l();
                return u.createElement(t.Consumer, null, (function(t) {
                    return n.invariant(t && t.client, 42), e.children(t.client)
                }))
            }, t.ApolloProvider = function(e) {
                var t = e.client,
                    r = e.children,
                    i = l(),
                    o = u.useContext(i),
                    s = u.useMemo((function() {
                        return a.__assign(a.__assign({}, o), {
                            client: t || o.client
                        })
                    }), [o, t]);
                return n.invariant(s.client, 44), u.createElement(i.Provider, {
                    value: s
                }, r)
            }, t.getApolloContext = l, t.resetApolloContext = f
        },
        28765: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n = r(57304),
                i = r(69151),
                o = r(55970),
                a = r(22970),
                s = r(73805),
                u = r(51117),
                c = r(49860),
                l = r(32319),
                f = r(17365),
                h = r(47923),
                p = r(98788);

            function d(e) {
                if (e && e.__esModule) return e;
                var t = Object.create(null);
                if (e)
                    for (var r in e) t[r] = e[r];
                return t.default = e, Object.freeze(t)
            }
            var y = d(i);

            function v(e) {
                var t = y.useContext(o.getApolloContext()),
                    r = e || t.client;
                return n.invariant(!!r, 47), r
            }
            var m = !1,
                g = y.useSyncExternalStore || function(e, t, r) {
                    var i = t();
                    !1 === globalThis.__DEV__ || m || i === t() || (m = !0, !1 !== globalThis.__DEV__ && n.invariant.error(56));
                    var o = y.useState({
                            inst: {
                                value: i,
                                getSnapshot: t
                            }
                        }),
                        a = o[0].inst,
                        u = o[1];
                    return s.canUseLayoutEffect ? y.useLayoutEffect((function() {
                        Object.assign(a, {
                            value: i,
                            getSnapshot: t
                        }), b(a) && u({
                            inst: a
                        })
                    }), [e, i, t]) : Object.assign(a, {
                        value: i,
                        getSnapshot: t
                    }), y.useEffect((function() {
                        return b(a) && u({
                            inst: a
                        }), e((function() {
                            b(a) && u({
                                inst: a
                            })
                        }))
                    }), [e]), i
                };

            function b(e) {
                var t = e.value,
                    r = e.getSnapshot;
                try {
                    return t !== r()
                } catch (e) {
                    return !0
                }
            }
            var _ = Object.prototype.hasOwnProperty;

            function w(e, t) {
                var r = y.useRef();
                r.current && e === r.current.client && t === r.current.query || (r.current = new O(e, t, r.current));
                var n = r.current;
                return n.forceUpdateState = y.useReducer((function(e) {
                    return e + 1
                }), 0)[1], n
            }
            var O = function() {
                    function e(e, t, r) {
                        var n = this;
                        this.client = e, this.query = t, this.forceUpdate = function() {
                            return n.forceUpdateState()
                        }, this.ssrDisabledResult = s.maybeDeepFreeze({
                            loading: !0,
                            data: void 0,
                            error: void 0,
                            networkStatus: l.NetworkStatus.loading
                        }), this.skipStandbyResult = s.maybeDeepFreeze({
                            loading: !1,
                            data: void 0,
                            error: void 0,
                            networkStatus: l.NetworkStatus.ready
                        }), this.toQueryResultCache = new(s.canUseWeakMap ? WeakMap : Map), f.verifyDocumentType(t, f.DocumentType.Query);
                        var i = r && r.result,
                            o = i && i.data;
                        o && (this.previousData = o)
                    }
                    return e.prototype.forceUpdateState = function() {
                        !1 !== globalThis.__DEV__ && n.invariant.warn(48)
                    }, e.prototype.executeQuery = function(e) {
                        var t, r = this;
                        e.query && Object.assign(this, {
                            query: e.query
                        }), this.watchQueryOptions = this.createWatchQueryOptions(this.queryHookOptions = e);
                        var n = this.observable.reobserveAsConcast(this.getObsQueryOptions());
                        return this.previousData = (null === (t = this.result) || void 0 === t ? void 0 : t.data) || this.previousData, this.result = void 0, this.forceUpdate(), new Promise((function(e) {
                            var t;
                            n.subscribe({
                                next: function(e) {
                                    t = e
                                },
                                error: function() {
                                    e(r.toQueryResult(r.observable.getCurrentResult()))
                                },
                                complete: function() {
                                    e(r.toQueryResult(t))
                                }
                            })
                        }))
                    }, e.prototype.useQuery = function(e) {
                        var t = this;
                        this.renderPromises = y.useContext(o.getApolloContext()).renderPromises, this.useOptions(e);
                        var r = this.useObservableQuery(),
                            n = g(y.useCallback((function(e) {
                                if (t.renderPromises) return function() {};
                                t.forceUpdate = e;
                                var n = function() {
                                        var e = t.result,
                                            n = r.getCurrentResult();
                                        e && e.loading === n.loading && e.networkStatus === n.networkStatus && u.equal(e.data, n.data) || t.setResult(n)
                                    },
                                    i = function(e) {
                                        var a = r.last;
                                        o.unsubscribe();
                                        try {
                                            r.resetLastResults(), o = r.subscribe(n, i)
                                        } finally {
                                            r.last = a
                                        }
                                        if (!_.call(e, "graphQLErrors")) throw e;
                                        var s = t.result;
                                        (!s || s && s.loading || !u.equal(e, s.error)) && t.setResult({
                                            data: s && s.data,
                                            error: e,
                                            loading: !1,
                                            networkStatus: l.NetworkStatus.error
                                        })
                                    },
                                    o = r.subscribe(n, i);
                                return function() {
                                    setTimeout((function() {
                                        return o.unsubscribe()
                                    })), t.forceUpdate = function() {
                                        return t.forceUpdateState()
                                    }
                                }
                            }), [r, this.renderPromises, this.client.disableNetworkFetches]), (function() {
                                return t.getCurrentResult()
                            }), (function() {
                                return t.getCurrentResult()
                            }));
                        return this.unsafeHandlePartialRefetch(n), this.toQueryResult(n)
                    }, e.prototype.useOptions = function(t) {
                        var r, n = this.createWatchQueryOptions(this.queryHookOptions = t),
                            i = this.watchQueryOptions;
                        u.equal(n, i) || (this.watchQueryOptions = n, i && this.observable && (this.observable.reobserve(this.getObsQueryOptions()), this.previousData = (null === (r = this.result) || void 0 === r ? void 0 : r.data) || this.previousData, this.result = void 0)), this.onCompleted = t.onCompleted || e.prototype.onCompleted, this.onError = t.onError || e.prototype.onError, !this.renderPromises && !this.client.disableNetworkFetches || !1 !== this.queryHookOptions.ssr || this.queryHookOptions.skip ? this.queryHookOptions.skip || "standby" === this.watchQueryOptions.fetchPolicy ? this.result = this.skipStandbyResult : this.result !== this.ssrDisabledResult && this.result !== this.skipStandbyResult || (this.result = void 0) : this.result = this.ssrDisabledResult
                    }, e.prototype.getObsQueryOptions = function() {
                        var e = [],
                            t = this.client.defaultOptions.watchQuery;
                        return t && e.push(t), this.queryHookOptions.defaultOptions && e.push(this.queryHookOptions.defaultOptions), e.push(s.compact(this.observable && this.observable.options, this.watchQueryOptions)), e.reduce(s.mergeOptions)
                    }, e.prototype.createWatchQueryOptions = function(e) {
                        var t;
                        void 0 === e && (e = {});
                        var r = e.skip;
                        e.ssr, e.onCompleted, e.onError, e.defaultOptions;
                        var n = a.__rest(e, ["skip", "ssr", "onCompleted", "onError", "defaultOptions"]),
                            i = Object.assign(n, {
                                query: this.query
                            });
                        if (!this.renderPromises || "network-only" !== i.fetchPolicy && "cache-and-network" !== i.fetchPolicy || (i.fetchPolicy = "cache-first"), i.variables || (i.variables = {}), r) {
                            var o = i.fetchPolicy,
                                s = void 0 === o ? this.getDefaultFetchPolicy() : o,
                                u = i.initialFetchPolicy,
                                c = void 0 === u ? s : u;
                            Object.assign(i, {
                                initialFetchPolicy: c,
                                fetchPolicy: "standby"
                            })
                        } else i.fetchPolicy || (i.fetchPolicy = (null === (t = this.observable) || void 0 === t ? void 0 : t.options.initialFetchPolicy) || this.getDefaultFetchPolicy());
                        return i
                    }, e.prototype.getDefaultFetchPolicy = function() {
                        var e, t;
                        return (null === (e = this.queryHookOptions.defaultOptions) || void 0 === e ? void 0 : e.fetchPolicy) || (null === (t = this.client.defaultOptions.watchQuery) || void 0 === t ? void 0 : t.fetchPolicy) || "cache-first"
                    }, e.prototype.onCompleted = function(e) {}, e.prototype.onError = function(e) {}, e.prototype.useObservableQuery = function() {
                        var e = this.observable = this.renderPromises && this.renderPromises.getSSRObservable(this.watchQueryOptions) || this.observable || this.client.watchQuery(this.getObsQueryOptions());
                        this.obsQueryFields = y.useMemo((function() {
                            return {
                                refetch: e.refetch.bind(e),
                                reobserve: e.reobserve.bind(e),
                                fetchMore: e.fetchMore.bind(e),
                                updateQuery: e.updateQuery.bind(e),
                                startPolling: e.startPolling.bind(e),
                                stopPolling: e.stopPolling.bind(e),
                                subscribeToMore: e.subscribeToMore.bind(e)
                            }
                        }), [e]);
                        var t = !(!1 === this.queryHookOptions.ssr || this.queryHookOptions.skip);
                        return this.renderPromises && t && (this.renderPromises.registerSSRObservable(e), e.getCurrentResult().loading && this.renderPromises.addObservableQueryPromise(e)), e
                    }, e.prototype.setResult = function(e) {
                        var t = this.result;
                        t && t.data && (this.previousData = t.data), this.result = e, this.forceUpdate(), this.handleErrorOrCompleted(e, t)
                    }, e.prototype.handleErrorOrCompleted = function(e, t) {
                        var r = this;
                        if (!e.loading) {
                            var i = this.toApolloError(e);
                            Promise.resolve().then((function() {
                                i ? r.onError(i) : e.data && (null == t ? void 0 : t.networkStatus) !== e.networkStatus && e.networkStatus === l.NetworkStatus.ready && r.onCompleted(e.data)
                            })).catch((function(e) {
                                !1 !== globalThis.__DEV__ && n.invariant.warn(e)
                            }))
                        }
                    }, e.prototype.toApolloError = function(e) {
                        return s.isNonEmptyArray(e.errors) ? new c.ApolloError({
                            graphQLErrors: e.errors
                        }) : e.error
                    }, e.prototype.getCurrentResult = function() {
                        return this.result || this.handleErrorOrCompleted(this.result = this.observable.getCurrentResult()), this.result
                    }, e.prototype.toQueryResult = function(e) {
                        var t = this.toQueryResultCache.get(e);
                        if (t) return t;
                        var r = e.data;
                        e.partial;
                        var n = a.__rest(e, ["data", "partial"]);
                        return this.toQueryResultCache.set(e, t = a.__assign(a.__assign(a.__assign({
                            data: r
                        }, n), this.obsQueryFields), {
                            client: this.client,
                            observable: this.observable,
                            variables: this.observable.variables,
                            called: !this.queryHookOptions.skip,
                            previousData: this.previousData
                        })), !t.error && s.isNonEmptyArray(e.errors) && (t.error = new c.ApolloError({
                            graphQLErrors: e.errors
                        })), t
                    }, e.prototype.unsafeHandlePartialRefetch = function(e) {
                        !e.partial || !this.queryHookOptions.partialRefetch || e.loading || e.data && 0 !== Object.keys(e.data).length || "cache-only" === this.observable.options.fetchPolicy || (Object.assign(e, {
                            loading: !0,
                            networkStatus: l.NetworkStatus.refetch
                        }), this.observable.refetch())
                    }, e
                }(),
                k = ["refetch", "reobserve", "fetchMore", "updateQuery", "startPolling", "subscribeToMore"];

            function S(e) {
                var t = {
                    data: e.result,
                    complete: !!e.complete
                };
                return e.missing && (t.missing = s.mergeDeepArray(e.missing.map((function(e) {
                    return e.missing
                })))), t
            }
            var E = y.use || function(e) {
                    var t = s.wrapPromiseWithState(e);
                    switch (t.status) {
                        case "pending":
                            throw t;
                        case "rejected":
                            throw t.reason;
                        case "fulfilled":
                            return t.value
                    }
                },
                R = Symbol(),
                D = ["canonizeResults", "context", "errorPolicy", "fetchPolicy", "refetchWritePolicy", "returnPartialData"],
                P = function() {
                    function e(e, t) {
                        var r, n = this;
                        this.listeners = new Set, this.status = "loading", this.references = 0, this.handleNext = this.handleNext.bind(this), this.handleError = this.handleError.bind(this), this.dispose = this.dispose.bind(this), this.observable = e, this.result = e.getCurrentResult(!1), this.key = t.key, t.onDispose && (this.onDispose = t.onDispose), l.isNetworkRequestSettled(this.result.networkStatus) || this.result.data && (!this.result.partial || this.watchQueryOptions.returnPartialData) ? (this.promise = s.createFulfilledPromise(this.result), this.status = "idle") : this.promise = new Promise((function(e, t) {
                            n.resolve = e, n.reject = t
                        })), this.subscription = e.filter((function(e) {
                            var t = e.data;
                            return !u.equal(t, {})
                        })).subscribe({
                            next: this.handleNext,
                            error: this.handleError
                        }), this.autoDisposeTimeoutId = setTimeout(this.dispose, null !== (r = t.autoDisposeTimeoutMs) && void 0 !== r ? r : 3e4)
                    }
                    return Object.defineProperty(e.prototype, "watchQueryOptions", {
                        get: function() {
                            return this.observable.options
                        },
                        enumerable: !1,
                        configurable: !0
                    }), e.prototype.retain = function() {
                        var e = this;
                        this.references++, clearTimeout(this.autoDisposeTimeoutId);
                        var t = !1;
                        return function() {
                            t || (t = !0, e.references--, setTimeout((function() {
                                e.references || e.dispose()
                            })))
                        }
                    }, e.prototype.didChangeOptions = function(e) {
                        var t = this;
                        return D.some((function(r) {
                            return !u.equal(t.watchQueryOptions[r], e[r])
                        }))
                    }, e.prototype.applyOptions = function(e) {
                        var t = this.watchQueryOptions,
                            r = t.fetchPolicy,
                            n = t.canonizeResults;
                        return "standby" === r && r !== e.fetchPolicy ? this.initiateFetch(this.observable.reobserve(e)) : (this.observable.silentSetOptions(e), n !== e.canonizeResults && (this.result = a.__assign(a.__assign({}, this.result), this.observable.getCurrentResult()), this.promise = s.createFulfilledPromise(this.result))), this.promise
                    }, e.prototype.listen = function(e) {
                        var t = this;
                        return this.listeners.add(e),
                            function() {
                                t.listeners.delete(e)
                            }
                    }, e.prototype.refetch = function(e) {
                        return this.initiateFetch(this.observable.refetch(e))
                    }, e.prototype.fetchMore = function(e) {
                        return this.initiateFetch(this.observable.fetchMore(e))
                    }, e.prototype.dispose = function() {
                        this.subscription.unsubscribe(), this.onDispose()
                    }, e.prototype.onDispose = function() {}, e.prototype.handleNext = function(e) {
                        var t;
                        switch (this.status) {
                            case "loading":
                                void 0 === e.data && (e.data = this.result.data), this.status = "idle", this.result = e, null === (t = this.resolve) || void 0 === t || t.call(this, e);
                                break;
                            case "idle":
                                if (e.data === this.result.data) return;
                                void 0 === e.data && (e.data = this.result.data), this.result = e, this.promise = s.createFulfilledPromise(e), this.deliver(this.promise)
                        }
                    }, e.prototype.handleError = function(e) {
                        var t;
                        switch (this.status) {
                            case "loading":
                                this.status = "idle", null === (t = this.reject) || void 0 === t || t.call(this, e);
                                break;
                            case "idle":
                                this.promise = s.createRejectedPromise(e), this.deliver(this.promise)
                        }
                    }, e.prototype.deliver = function(e) {
                        this.listeners.forEach((function(t) {
                            return t(e)
                        }))
                    }, e.prototype.initiateFetch = function(e) {
                        var t = this;
                        return this.status = "loading", this.promise = new Promise((function(e, r) {
                            t.resolve = e, t.reject = r
                        })), this.promise.catch((function() {})), e.then((function(e) {
                            var r;
                            "loading" === t.status && (t.status = "idle", t.result = e, null === (r = t.resolve) || void 0 === r || r.call(t, e))
                        })).catch((function() {})), e
                    }, e
                }(),
                F = function() {
                    function e(e) {
                        void 0 === e && (e = Object.create(null)), this.queryRefs = new p.Trie(s.canUseWeakMap), this.options = e
                    }
                    return e.prototype.getQueryRef = function(e, t) {
                        var r = this.queryRefs.lookupArray(e);
                        return r.current || (r.current = new P(t(), {
                            key: e,
                            autoDisposeTimeoutMs: this.options.autoDisposeTimeoutMs,
                            onDispose: function() {
                                delete r.current
                            }
                        })), r.current
                    }, e
                }(),
                T = Symbol.for("apollo.suspenseCache");

            function q(e) {
                var t;
                return e[T] || (e[T] = new F(null === (t = e.defaultOptions.react) || void 0 === t ? void 0 : t.suspense)), e[T]
            }
            var C = Symbol.for("apollo.skipToken");

            function x(e) {
                return s.isNonEmptyArray(e.errors) ? new l.ApolloError({
                    graphQLErrors: e.errors
                }) : e.error
            }

            function M(e) {
                var t, r, i, o = e.client,
                    s = e.query,
                    c = e.options;
                return t = function() {
                    var e;
                    if (c === C) return {
                        query: s,
                        fetchPolicy: "standby"
                    };
                    var t = c.fetchPolicy || (null === (e = o.defaultOptions.watchQuery) || void 0 === e ? void 0 : e.fetchPolicy) || "cache-first",
                        r = a.__assign(a.__assign({}, c), {
                            fetchPolicy: t,
                            query: s,
                            notifyOnNetworkStatusChange: !1,
                            nextFetchPolicy: void 0
                        });
                    return !1 !== globalThis.__DEV__ && function(e) {
                        var t = e.query,
                            r = e.fetchPolicy,
                            i = e.returnPartialData;
                        f.verifyDocumentType(t, f.DocumentType.Query),
                            function(e) {
                                void 0 === e && (e = "cache-first"), n.invariant(["cache-first", "network-only", "no-cache", "cache-and-network"].includes(e), 54, e)
                            }(r),
                            function(e, t) {
                                "no-cache" === e && t && !1 !== globalThis.__DEV__ && n.invariant.warn(55)
                            }(r, i)
                    }(r), c.skip && (r.fetchPolicy = "standby"), r
                }, r = [o, c, s], (i = y.useRef()).current && u.equal(i.current.deps, r) || (i.current = {
                    value: t(),
                    deps: r
                }), i.current.value
            }
            t.skipToken = C, t.useApolloClient = v, t.useBackgroundQuery = function(e, t) {
                void 0 === t && (t = Object.create(null));
                var r = v(t.client),
                    n = q(r),
                    i = M({
                        client: r,
                        query: e,
                        options: t
                    }),
                    o = i.fetchPolicy,
                    s = i.variables,
                    u = t.queryKey,
                    c = void 0 === u ? [] : u,
                    l = y.useRef("standby" !== o);
                l.current || (l.current = "standby" !== o);
                var f = a.__spreadArray([e, h.canonicalStringify(s)], [].concat(c), !0),
                    p = n.getQueryRef(f, (function() {
                        return r.watchQuery(i)
                    })),
                    d = y.useState((function() {
                        return new Map([
                            [p.key, p.promise]
                        ])
                    })),
                    m = d[0],
                    g = d[1];
                if (p.didChangeOptions(i)) {
                    var b = p.applyOptions(i);
                    m.set(p.key, b)
                }
                y.useEffect((function() {
                    return p.retain()
                }), [p]);
                var _ = y.useCallback((function(e) {
                        var t = p.fetchMore(e);
                        return g((function(e) {
                            return new Map(e).set(p.key, p.promise)
                        })), t
                    }), [p]),
                    w = y.useCallback((function(e) {
                        var t = p.refetch(e);
                        return g((function(e) {
                            return new Map(e).set(p.key, p.promise)
                        })), t
                    }), [p]);
                p.promiseCache = m;
                var O = y.useMemo((function() {
                    return function(e) {
                        var t;
                        return (t = {})[R] = e, t
                    }(p)
                }), [p]);
                return [l.current ? O : void 0, {
                    fetchMore: _,
                    refetch: w
                }]
            }, t.useFragment = function(e) {
                var t = v().cache,
                    r = e.fragment,
                    n = e.fragmentName,
                    i = e.from,
                    o = e.optimistic,
                    s = void 0 === o || o,
                    c = a.__rest(e, ["fragment", "fragmentName", "from", "optimistic"]),
                    l = a.__assign(a.__assign({}, c), {
                        returnPartialData: !0,
                        id: "string" == typeof i ? i : t.identify(i),
                        query: t.getFragmentDoc(r, n),
                        optimistic: s
                    }),
                    f = y.useRef(),
                    h = t.diff(l),
                    p = function() {
                        var e = S(h);
                        return f.current && u.equal(f.current.data, e.data) ? f.current : f.current = e
                    };
                return g((function(e) {
                    var r = 0,
                        n = t.watch(a.__assign(a.__assign({}, l), {
                            immediate: !0,
                            callback: function(t) {
                                u.equal(t, h) || (f.current = S(h = t), r = setTimeout(e))
                            }
                        }));
                    return function() {
                        n(), clearTimeout(r)
                    }
                }), p, p)
            }, t.useLazyQuery = function(e, t) {
                var r, n = y.useRef(),
                    i = y.useRef(),
                    o = y.useRef(),
                    u = s.mergeOptions(t, n.current || {}),
                    c = null !== (r = null == u ? void 0 : u.query) && void 0 !== r ? r : e;
                i.current = u, o.current = c;
                var l = w(v(t && t.client), c),
                    f = l.useQuery(a.__assign(a.__assign({}, u), {
                        skip: !n.current
                    })),
                    h = f.observable.options.initialFetchPolicy || l.getDefaultFetchPolicy(),
                    p = Object.assign(f, {
                        called: !!n.current
                    }),
                    d = y.useMemo((function() {
                        for (var e = {}, t = function(t) {
                                var r = p[t];
                                e[t] = function() {
                                    return n.current || (n.current = Object.create(null), l.forceUpdateState()), r.apply(this, arguments)
                                }
                            }, r = 0, i = k; r < i.length; r++) t(i[r]);
                        return e
                    }), []);
                return Object.assign(p, d), [y.useCallback((function(e) {
                    n.current = e ? a.__assign(a.__assign({}, e), {
                        fetchPolicy: e.fetchPolicy || h
                    }) : {
                        fetchPolicy: h
                    };
                    var t = s.mergeOptions(i.current, a.__assign({
                            query: o.current
                        }, n.current)),
                        r = l.executeQuery(a.__assign(a.__assign({}, t), {
                            skip: !1
                        })).then((function(e) {
                            return Object.assign(e, d)
                        }));
                    return r.catch((function() {})), r
                }), []), p]
            }, t.useMutation = function(e, t) {
                var r = v(null == t ? void 0 : t.client);
                f.verifyDocumentType(e, f.DocumentType.Mutation);
                var n = y.useState({
                        called: !1,
                        loading: !1,
                        client: r
                    }),
                    i = n[0],
                    o = n[1],
                    l = y.useRef({
                        result: i,
                        mutationId: 0,
                        isMounted: !0,
                        client: r,
                        mutation: e,
                        options: t
                    });
                Object.assign(l.current, {
                    client: r,
                    options: t,
                    mutation: e
                });
                var h = y.useCallback((function(e) {
                        void 0 === e && (e = {});
                        var t = l.current,
                            r = t.options,
                            n = t.mutation,
                            i = a.__assign(a.__assign({}, r), {
                                mutation: n
                            }),
                            f = e.client || l.current.client;
                        l.current.result.loading || i.ignoreResults || !l.current.isMounted || o(l.current.result = {
                            loading: !0,
                            error: void 0,
                            data: void 0,
                            called: !0,
                            client: f
                        });
                        var h = ++l.current.mutationId,
                            p = s.mergeOptions(i, e);
                        return f.mutate(p).then((function(t) {
                            var r, n, i = t.data,
                                a = t.errors,
                                s = a && a.length > 0 ? new c.ApolloError({
                                    graphQLErrors: a
                                }) : void 0,
                                d = e.onError || (null === (r = l.current.options) || void 0 === r ? void 0 : r.onError);
                            if (s && d && d(s, p), h === l.current.mutationId && !p.ignoreResults) {
                                var y = {
                                    called: !0,
                                    loading: !1,
                                    data: i,
                                    error: s,
                                    client: f
                                };
                                l.current.isMounted && !u.equal(l.current.result, y) && o(l.current.result = y)
                            }
                            var v = e.onCompleted || (null === (n = l.current.options) || void 0 === n ? void 0 : n.onCompleted);
                            return s || null == v || v(t.data, p), t
                        })).catch((function(t) {
                            var r;
                            if (h === l.current.mutationId && l.current.isMounted) {
                                var n = {
                                    loading: !1,
                                    error: t,
                                    data: void 0,
                                    called: !0,
                                    client: f
                                };
                                u.equal(l.current.result, n) || o(l.current.result = n)
                            }
                            var i = e.onError || (null === (r = l.current.options) || void 0 === r ? void 0 : r.onError);
                            if (i) return i(t, p), {
                                data: void 0,
                                errors: t
                            };
                            throw t
                        }))
                    }), []),
                    p = y.useCallback((function() {
                        l.current.isMounted && o({
                            called: !1,
                            loading: !1,
                            client: r
                        })
                    }), []);
                return y.useEffect((function() {
                    return l.current.isMounted = !0,
                        function() {
                            l.current.isMounted = !1
                        }
                }), []), [h, a.__assign({
                    reset: p
                }, i)]
            }, t.useQuery = function(e, t) {
                return void 0 === t && (t = Object.create(null)), w(v(t.client), e).useQuery(t)
            }, t.useReactiveVar = function(e) {
                var t = e(),
                    r = y.useState(t)[1];
                return y.useEffect((function() {
                    var n = e();
                    if (t === n) return e.onNextChange(r);
                    r(n)
                }), [t]), t
            }, t.useReadQuery = function(e) {
                var t = function(e) {
                    return e[R]
                }(e);
                n.invariant(t.promiseCache, 49);
                var r = t.promiseCache,
                    i = t.key;
                r.has(i) || r.set(i, t.promise);
                var o = g(y.useCallback((function(e) {
                        return t.listen((function(r) {
                            t.promiseCache.set(t.key, r), e()
                        }))
                    }), [t]), (function() {
                        return r.get(i)
                    }), (function() {
                        return r.get(i)
                    })),
                    a = E(o);
                return y.useMemo((function() {
                    return {
                        data: a.data,
                        networkStatus: a.networkStatus,
                        error: x(a)
                    }
                }), [a])
            }, t.useSubscription = function(e, t) {
                var r = y.useRef(!1),
                    i = v(null == t ? void 0 : t.client);
                f.verifyDocumentType(e, f.DocumentType.Subscription);
                var o = y.useState({
                        loading: !(null == t ? void 0 : t.skip),
                        error: void 0,
                        data: void 0,
                        variables: null == t ? void 0 : t.variables
                    }),
                    a = o[0],
                    s = o[1];
                r.current || (r.current = !0, (null == t ? void 0 : t.onSubscriptionData) && !1 !== globalThis.__DEV__ && n.invariant.warn(t.onData ? 50 : 51), (null == t ? void 0 : t.onSubscriptionComplete) && !1 !== globalThis.__DEV__ && n.invariant.warn(t.onComplete ? 52 : 53));
                var c = y.useState((function() {
                        return (null == t ? void 0 : t.skip) ? null : i.subscribe({
                            query: e,
                            variables: null == t ? void 0 : t.variables,
                            fetchPolicy: null == t ? void 0 : t.fetchPolicy,
                            context: null == t ? void 0 : t.context
                        })
                    })),
                    l = c[0],
                    h = c[1],
                    p = y.useRef(!1);
                y.useEffect((function() {
                    return function() {
                        p.current = !0
                    }
                }), []);
                var d = y.useRef({
                    client: i,
                    subscription: e,
                    options: t
                });
                return y.useEffect((function() {
                    var r, n, o, a, c = null == t ? void 0 : t.shouldResubscribe;
                    "function" == typeof c && (c = !!c(t)), (null == t ? void 0 : t.skip) ? (!(null == t ? void 0 : t.skip) != !(null === (r = d.current.options) || void 0 === r ? void 0 : r.skip) || p.current) && (s({
                        loading: !1,
                        data: void 0,
                        error: void 0,
                        variables: null == t ? void 0 : t.variables
                    }), h(null), p.current = !1) : (!1 === c || i === d.current.client && e === d.current.subscription && (null == t ? void 0 : t.fetchPolicy) === (null === (n = d.current.options) || void 0 === n ? void 0 : n.fetchPolicy) && !(null == t ? void 0 : t.skip) == !(null === (o = d.current.options) || void 0 === o ? void 0 : o.skip) && u.equal(null == t ? void 0 : t.variables, null === (a = d.current.options) || void 0 === a ? void 0 : a.variables)) && !p.current || (s({
                        loading: !0,
                        data: void 0,
                        error: void 0,
                        variables: null == t ? void 0 : t.variables
                    }), h(i.subscribe({
                        query: e,
                        variables: null == t ? void 0 : t.variables,
                        fetchPolicy: null == t ? void 0 : t.fetchPolicy,
                        context: null == t ? void 0 : t.context
                    })), p.current = !1), Object.assign(d.current, {
                        client: i,
                        subscription: e,
                        options: t
                    })
                }), [i, e, t, p.current]), y.useEffect((function() {
                    if (l) {
                        var e = !1,
                            r = l.subscribe({
                                next: function(r) {
                                    var n, o;
                                    if (!e) {
                                        var a = {
                                            loading: !1,
                                            data: r.data,
                                            error: void 0,
                                            variables: null == t ? void 0 : t.variables
                                        };
                                        s(a), (null === (n = d.current.options) || void 0 === n ? void 0 : n.onData) ? d.current.options.onData({
                                            client: i,
                                            data: a
                                        }) : (null === (o = d.current.options) || void 0 === o ? void 0 : o.onSubscriptionData) && d.current.options.onSubscriptionData({
                                            client: i,
                                            subscriptionData: a
                                        })
                                    }
                                },
                                error: function(r) {
                                    var n, i;
                                    e || (s({
                                        loading: !1,
                                        data: void 0,
                                        error: r,
                                        variables: null == t ? void 0 : t.variables
                                    }), null === (i = null === (n = d.current.options) || void 0 === n ? void 0 : n.onError) || void 0 === i || i.call(n, r))
                                },
                                complete: function() {
                                    var t, r;
                                    e || ((null === (t = d.current.options) || void 0 === t ? void 0 : t.onComplete) ? d.current.options.onComplete() : (null === (r = d.current.options) || void 0 === r ? void 0 : r.onSubscriptionComplete) && d.current.options.onSubscriptionComplete())
                                }
                            });
                        return function() {
                            e = !0, setTimeout((function() {
                                r.unsubscribe()
                            }))
                        }
                    }
                }), [l]), a
            }, t.useSuspenseQuery = function(e, t) {
                void 0 === t && (t = Object.create(null));
                var r = v(t.client),
                    n = q(r),
                    i = M({
                        client: r,
                        query: e,
                        options: t
                    }),
                    o = i.fetchPolicy,
                    s = i.variables,
                    u = t.queryKey,
                    c = void 0 === u ? [] : u,
                    f = a.__spreadArray([e, h.canonicalStringify(s)], [].concat(c), !0),
                    p = n.getQueryRef(f, (function() {
                        return r.watchQuery(i)
                    })),
                    d = y.useState((function() {
                        return new Map([
                            [p.key, p.promise]
                        ])
                    })),
                    m = d[0],
                    g = d[1],
                    b = m.get(p.key);
                p.didChangeOptions(i) && (b = p.applyOptions(i), m.set(p.key, b)), b || (b = p.promise, m.set(p.key, b)), y.useEffect((function() {
                    var e = p.retain(),
                        t = p.listen((function(e) {
                            g((function(t) {
                                return new Map(t).set(p.key, e)
                            }))
                        }));
                    return function() {
                        t(), e()
                    }
                }), [p]);
                var _ = y.useMemo((function() {
                        var e = x(p.result);
                        return {
                            loading: !1,
                            data: p.result.data,
                            networkStatus: e ? l.NetworkStatus.error : l.NetworkStatus.ready,
                            error: e
                        }
                    }), [p.result]),
                    w = "standby" === o ? _ : E(b),
                    O = y.useCallback((function(e) {
                        var t = p.fetchMore(e);
                        return g((function(e) {
                            return new Map(e).set(p.key, p.promise)
                        })), t
                    }), [p]),
                    k = y.useCallback((function(e) {
                        var t = p.refetch(e);
                        return g((function(e) {
                            return new Map(e).set(p.key, p.promise)
                        })), t
                    }), [p]),
                    S = y.useCallback((function(e) {
                        return p.observable.subscribeToMore(e)
                    }), [p]);
                return y.useMemo((function() {
                    return {
                        client: r,
                        data: w.data,
                        error: x(w),
                        networkStatus: w.networkStatus,
                        fetchMore: O,
                        refetch: k,
                        subscribeToMore: S
                    }
                }), [r, O, k, w, S])
            }
        },
        17365: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            var n, i = r(57304);
            t.DocumentType = void 0, (n = t.DocumentType || (t.DocumentType = {}))[n.Query = 0] = "Query", n[n.Mutation = 1] = "Mutation", n[n.Subscription = 2] = "Subscription";
            var o = new Map;

            function a(e) {
                var r;
                switch (e) {
                    case t.DocumentType.Query:
                        r = "Query";
                        break;
                    case t.DocumentType.Mutation:
                        r = "Mutation";
                        break;
                    case t.DocumentType.Subscription:
                        r = "Subscription"
                }
                return r
            }

            function s(e) {
                var r, n, a = o.get(e);
                if (a) return a;
                i.invariant(!!e && !!e.kind, 57, e);
                for (var s = [], u = [], c = [], l = [], f = 0, h = e.definitions; f < h.length; f++) {
                    var p = h[f];
                    if ("FragmentDefinition" !== p.kind) {
                        if ("OperationDefinition" === p.kind) switch (p.operation) {
                            case "query":
                                u.push(p);
                                break;
                            case "mutation":
                                c.push(p);
                                break;
                            case "subscription":
                                l.push(p)
                        }
                    } else s.push(p)
                }
                i.invariant(!s.length || u.length || c.length || l.length, 58), i.invariant(u.length + c.length + l.length <= 1, 59, e, u.length, l.length, c.length), n = u.length ? t.DocumentType.Query : t.DocumentType.Mutation, u.length || c.length || (n = t.DocumentType.Subscription);
                var d = u.length ? u : c.length ? c : l;
                i.invariant(1 === d.length, 60, e, d.length);
                var y = d[0];
                r = y.variableDefinitions || [];
                var v = {
                    name: y.name && "Name" === y.name.kind ? y.name.value : "data",
                    type: n,
                    variables: r
                };
                return o.set(e, v), v
            }
            t.operationName = a, t.parser = s, t.verifyDocumentType = function(e, t) {
                var r = s(e),
                    n = a(t),
                    o = a(r.type);
                i.invariant(r.type === t, 61, n, n, o)
            }
        },
        3920: (e, t, r) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), r(57304);
            var n = r(55970),
                i = r(28765),
                o = r(22970),
                a = r(98788),
                s = r(73805),
                u = r(51117),
                c = r(32319),
                l = r(17365),
                f = ["canonizeResults", "context", "errorPolicy", "fetchPolicy", "refetchWritePolicy", "returnPartialData"],
                h = function() {
                    function e(e, t) {
                        var r, n = this;
                        this.listeners = new Set, this.status = "loading", this.references = 0, this.handleNext = this.handleNext.bind(this), this.handleError = this.handleError.bind(this), this.dispose = this.dispose.bind(this), this.observable = e, this.result = e.getCurrentResult(!1), this.key = t.key, t.onDispose && (this.onDispose = t.onDispose), c.isNetworkRequestSettled(this.result.networkStatus) || this.result.data && (!this.result.partial || this.watchQueryOptions.returnPartialData) ? (this.promise = s.createFulfilledPromise(this.result), this.status = "idle") : this.promise = new Promise((function(e, t) {
                            n.resolve = e, n.reject = t
                        })), this.subscription = e.filter((function(e) {
                            var t = e.data;
                            return !u.equal(t, {})
                        })).subscribe({
                            next: this.handleNext,
                            error: this.handleError
                        }), this.autoDisposeTimeoutId = setTimeout(this.dispose, null !== (r = t.autoDisposeTimeoutMs) && void 0 !== r ? r : 3e4)
                    }
                    return Object.defineProperty(e.prototype, "watchQueryOptions", {
                        get: function() {
                            return this.observable.options
                        },
                        enumerable: !1,
                        configurable: !0
                    }), e.prototype.retain = function() {
                        var e = this;
                        this.references++, clearTimeout(this.autoDisposeTimeoutId);
                        var t = !1;
                        return function() {
                            t || (t = !0, e.references--, setTimeout((function() {
                                e.references || e.dispose()
                            })))
                        }
                    }, e.prototype.didChangeOptions = function(e) {
                        var t = this;
                        return f.some((function(r) {
                            return !u.equal(t.watchQueryOptions[r], e[r])
                        }))
                    }, e.prototype.applyOptions = function(e) {
                        var t = this.watchQueryOptions,
                            r = t.fetchPolicy,
                            n = t.canonizeResults;
                        return "standby" === r && r !== e.fetchPolicy ? this.initiateFetch(this.observable.reobserve(e)) : (this.observable.silentSetOptions(e), n !== e.canonizeResults && (this.result = o.__assign(o.__assign({}, this.result), this.observable.getCurrentResult()), this.promise = s.createFulfilledPromise(this.result))), this.promise
                    }, e.prototype.listen = function(e) {
                        var t = this;
                        return this.listeners.add(e),
                            function() {
                                t.listeners.delete(e)
                            }
                    }, e.prototype.refetch = function(e) {
                        return this.initiateFetch(this.observable.refetch(e))
                    }, e.prototype.fetchMore = function(e) {
                        return this.initiateFetch(this.observable.fetchMore(e))
                    }, e.prototype.dispose = function() {
                        this.subscription.unsubscribe(), this.onDispose()
                    }, e.prototype.onDispose = function() {}, e.prototype.handleNext = function(e) {
                        var t;
                        switch (this.status) {
                            case "loading":
                                void 0 === e.data && (e.data = this.result.data), this.status = "idle", this.result = e, null === (t = this.resolve) || void 0 === t || t.call(this, e);
                                break;
                            case "idle":
                                if (e.data === this.result.data) return;
                                void 0 === e.data && (e.data = this.result.data), this.result = e, this.promise = s.createFulfilledPromise(e), this.deliver(this.promise)
                        }
                    }, e.prototype.handleError = function(e) {
                        var t;
                        switch (this.status) {
                            case "loading":
                                this.status = "idle", null === (t = this.reject) || void 0 === t || t.call(this, e);
                                break;
                            case "idle":
                                this.promise = s.createRejectedPromise(e), this.deliver(this.promise)
                        }
                    }, e.prototype.deliver = function(e) {
                        this.listeners.forEach((function(t) {
                            return t(e)
                        }))
                    }, e.prototype.initiateFetch = function(e) {
                        var t = this;
                        return this.status = "loading", this.promise = new Promise((function(e, r) {
                            t.resolve = e, t.reject = r
                        })), this.promise.catch((function() {})), e.then((function(e) {
                            var r;
                            "loading" === t.status && (t.status = "idle", t.result = e, null === (r = t.resolve) || void 0 === r || r.call(t, e))
                        })).catch((function() {})), e
                    }, e
                }(),
                p = function() {
                    function e(e) {
                        void 0 === e && (e = Object.create(null)), this.queryRefs = new a.Trie(s.canUseWeakMap), this.options = e
                    }
                    return e.prototype.getQueryRef = function(e, t) {
                        var r = this.queryRefs.lookupArray(e);
                        return r.current || (r.current = new h(t(), {
                            key: e,
                            autoDisposeTimeoutMs: this.options.autoDisposeTimeoutMs,
                            onDispose: function() {
                                delete r.current
                            }
                        })), r.current
                    }, e
                }(),
                d = function(e) {
                    function t() {
                        throw e.call(this), new Error("It is no longer necessary to create a `SuspenseCache` instance and pass it into the `ApolloProvider`.\nPlease remove this code from your application. \n\nThis export will be removed with the final 3.8 release.")
                    }
                    return o.__extends(t, e), t
                }(p);
            for (var y in t.ApolloConsumer = n.ApolloConsumer, t.ApolloProvider = n.ApolloProvider, t.getApolloContext = n.getApolloContext, t.resetApolloContext = n.resetApolloContext, t.DocumentType = l.DocumentType, t.operationName = l.operationName, t.parser = l.parser, t.SuspenseCache = d, i) "default" === y || t.hasOwnProperty(y) || (t[y] = i[y])
        },
        97278: (e, t, r) => {
            var n = null,
                i = {},
                o = 1;

            function a(e) {
                try {
                    return e()
                } catch (e) {}
            }
            var s = "@wry/context:Slot",
                u = a((function() {
                    return globalThis
                })) || a((function() {
                    return r.g
                })) || Object.create(null),
                c = u[s] || Array[s] || function(e) {
                    try {
                        Object.defineProperty(u, s, {
                            value: e,
                            enumerable: !1,
                            writable: !1,
                            configurable: !0
                        })
                    } finally {
                        return e
                    }
                }(function() {
                    function e() {
                        this.id = ["slot", o++, Date.now(), Math.random().toString(36).slice(2)].join(":")
                    }
                    return e.prototype.hasValue = function() {
                        for (var e = n; e; e = e.parent)
                            if (this.id in e.slots) {
                                var t = e.slots[this.id];
                                if (t === i) break;
                                return e !== n && (n.slots[this.id] = t), !0
                            }
                        return n && (n.slots[this.id] = i), !1
                    }, e.prototype.getValue = function() {
                        if (this.hasValue()) return n.slots[this.id]
                    }, e.prototype.withValue = function(e, t, r, i) {
                        var o, a = ((o = {
                                __proto__: null
                            })[this.id] = e, o),
                            s = n;
                        n = {
                            parent: s,
                            slots: a
                        };
                        try {
                            return t.apply(i, r)
                        } finally {
                            n = s
                        }
                    }, e.bind = function(e) {
                        var t = n;
                        return function() {
                            var r = n;
                            try {
                                return n = t, e.apply(this, arguments)
                            } finally {
                                n = r
                            }
                        }
                    }, e.noContext = function(e, t, r) {
                        if (!n) return e.apply(r, t);
                        var i = n;
                        try {
                            return n = null, e.apply(r, t)
                        } finally {
                            n = i
                        }
                    }, e
                }()),
                l = c.bind,
                f = c.noContext,
                h = [];
            t.Slot = c, t.asyncFromGen = function(e) {
                return function() {
                    var t = e.apply(this, arguments),
                        r = l(t.next),
                        n = l(t.throw);
                    return new Promise((function(e, i) {
                        function o(r, n) {
                            try {
                                var o = r.call(t, n)
                            } catch (e) {
                                return i(e)
                            }
                            var u, c = o.done ? e : a;
                            (u = o.value) && "function" == typeof u.then ? o.value.then(c, o.done ? i : s) : c(o.value)
                        }
                        var a = function(e) {
                                return o(r, e)
                            },
                            s = function(e) {
                                return o(n, e)
                            };
                        a()
                    }))
                }
            }, t.bind = l, t.noContext = f, t.setTimeout = function(e, t) {
                return setTimeout(l(e), t)
            }, t.wrapYieldingFiberMethods = function(e) {
                if (h.indexOf(e) < 0) {
                    var t = function(e, t) {
                        var r = e[t];
                        e[t] = function() {
                            return f(r, arguments, this)
                        }
                    };
                    t(e, "yield"), t(e.prototype, "run"), t(e.prototype, "throwInto"), h.push(e)
                }
                return e
            }
        },
        51117: (e, t) => {
            Object.defineProperty(t, "__esModule", {
                value: !0
            });
            const {
                toString: r,
                hasOwnProperty: n
            } = Object.prototype, i = Function.prototype.toString, o = new Map;

            function a(e, t) {
                try {
                    return s(e, t)
                } finally {
                    o.clear()
                }
            }

            function s(e, t) {
                if (e === t) return !0;
                const o = r.call(e);
                if (o !== r.call(t)) return !1;
                switch (o) {
                    case "[object Array]":
                        if (e.length !== t.length) return !1;
                    case "[object Object]":
                        {
                            if (f(e, t)) return !0;
                            const r = u(e),
                                i = u(t),
                                o = r.length;
                            if (o !== i.length) return !1;
                            for (let e = 0; e < o; ++e)
                                if (!n.call(t, r[e])) return !1;
                            for (let n = 0; n < o; ++n) {
                                const i = r[n];
                                if (!s(e[i], t[i])) return !1
                            }
                            return !0
                        }
                    case "[object Error]":
                        return e.name === t.name && e.message === t.message;
                    case "[object Number]":
                        if (e != e) return t != t;
                    case "[object Boolean]":
                    case "[object Date]":
                        return +e == +t;
                    case "[object RegExp]":
                    case "[object String]":
                        return e == `${t}`;
                    case "[object Map]":
                    case "[object Set]":
                        {
                            if (e.size !== t.size) return !1;
                            if (f(e, t)) return !0;
                            const r = e.entries(),
                                n = "[object Map]" === o;
                            for (;;) {
                                const e = r.next();
                                if (e.done) break;
                                const [i, o] = e.value;
                                if (!t.has(i)) return !1;
                                if (n && !s(o, t.get(i))) return !1
                            }
                            return !0
                        }
                    case "[object Uint16Array]":
                    case "[object Uint8Array]":
                    case "[object Uint32Array]":
                    case "[object Int32Array]":
                    case "[object Int8Array]":
                    case "[object Int16Array]":
                    case "[object ArrayBuffer]":
                        e = new Uint8Array(e), t = new Uint8Array(t);
                    case "[object DataView]":
                        {
                            let r = e.byteLength;
                            if (r === t.byteLength)
                                for (; r-- && e[r] === t[r];);
                            return -1 === r
                        }
                    case "[object AsyncFunction]":
                    case "[object GeneratorFunction]":
                    case "[object AsyncGeneratorFunction]":
                    case "[object Function]":
                        {
                            const r = i.call(e);
                            return r === i.call(t) && ! function(e, t) {
                                const r = e.length - t.length;
                                return r >= 0 && e.indexOf(t, r) === r
                            }(r, l)
                        }
                }
                return !1
            }

            function u(e) {
                return Object.keys(e).filter(c, e)
            }

            function c(e) {
                return void 0 !== this[e]
            }
            const l = "{ [native code] }";

            function f(e, t) {
                let r = o.get(e);
                if (r) {
                    if (r.has(t)) return !0
                } else o.set(e, r = new Set);
                return r.add(t), !1
            }
            t.default = a, t.equal = a
        },
        63271: (e, t, r) => {
            var n = r(98788),
                i = r(97278);

            function o() {}
            var a = function() {
                    function e(e, t) {
                        void 0 === e && (e = 1 / 0), void 0 === t && (t = o), this.max = e, this.dispose = t, this.map = new Map, this.newest = null, this.oldest = null
                    }
                    return e.prototype.has = function(e) {
                        return this.map.has(e)
                    }, e.prototype.get = function(e) {
                        var t = this.getNode(e);
                        return t && t.value
                    }, e.prototype.getNode = function(e) {
                        var t = this.map.get(e);
                        if (t && t !== this.newest) {
                            var r = t.older,
                                n = t.newer;
                            n && (n.older = r), r && (r.newer = n), t.older = this.newest, t.older.newer = t, t.newer = null, this.newest = t, t === this.oldest && (this.oldest = n)
                        }
                        return t
                    }, e.prototype.set = function(e, t) {
                        var r = this.getNode(e);
                        return r ? r.value = t : (r = {
                            key: e,
                            value: t,
                            newer: null,
                            older: this.newest
                        }, this.newest && (this.newest.newer = r), this.newest = r, this.oldest = this.oldest || r, this.map.set(e, r), r.value)
                    }, e.prototype.clean = function() {
                        for (; this.oldest && this.map.size > this.max;) this.delete(this.oldest.key)
                    }, e.prototype.delete = function(e) {
                        var t = this.map.get(e);
                        return !!t && (t === this.newest && (this.newest = t.older), t === this.oldest && (this.oldest = t.newer), t.newer && (t.newer.older = t.older), t.older && (t.older.newer = t.newer), this.map.delete(e), this.dispose(t.value, e), !0)
                    }, e
                }(),
                s = new i.Slot,
                u = Object.prototype.hasOwnProperty,
                c = Array.from || function(e) {
                    var t = [];
                    return e.forEach((function(e) {
                        return t.push(e)
                    })), t
                };

            function l(e) {
                var t = e.unsubscribe;
                "function" == typeof t && (e.unsubscribe = void 0, t())
            }
            var f = [],
                h = 100;

            function p(e, t) {
                if (!e) throw new Error(t || "assertion failure")
            }

            function d(e) {
                switch (e.length) {
                    case 0:
                        throw new Error("unknown value");
                    case 1:
                        return e[0];
                    case 2:
                        throw e[1]
                }
            }
            var y = function() {
                function e(t) {
                    this.fn = t, this.parents = new Set, this.childValues = new Map, this.dirtyChildren = null, this.dirty = !0, this.recomputing = !1, this.value = [], this.deps = null, ++e.count
                }
                return e.prototype.peek = function() {
                    if (1 === this.value.length && !g(this)) return v(this), this.value[0]
                }, e.prototype.recompute = function(e) {
                    return p(!this.recomputing, "already recomputing"), v(this), g(this) ? function(e, t) {
                        return E(e), s.withValue(e, m, [e, t]),
                            function(e, t) {
                                if ("function" == typeof e.subscribe) try {
                                    l(e), e.unsubscribe = e.subscribe.apply(null, t)
                                } catch (t) {
                                    return e.setDirty(), !1
                                }
                                return !0
                            }(e, t) && function(e) {
                                e.dirty = !1, g(e) || _(e)
                            }(e), d(e.value)
                    }(this, e) : d(this.value)
                }, e.prototype.setDirty = function() {
                    this.dirty || (this.dirty = !0, this.value.length = 0, b(this), l(this))
                }, e.prototype.dispose = function() {
                    var e = this;
                    this.setDirty(), E(this), w(this, (function(t, r) {
                        t.setDirty(), R(t, e)
                    }))
                }, e.prototype.forget = function() {
                    this.dispose()
                }, e.prototype.dependOn = function(e) {
                    e.add(this), this.deps || (this.deps = f.pop() || new Set), this.deps.add(e)
                }, e.prototype.forgetDeps = function() {
                    var e = this;
                    this.deps && (c(this.deps).forEach((function(t) {
                        return t.delete(e)
                    })), this.deps.clear(), f.push(this.deps), this.deps = null)
                }, e.count = 0, e
            }();

            function v(e) {
                var t = s.getValue();
                if (t) return e.parents.add(t), t.childValues.has(e) || t.childValues.set(e, []), g(e) ? O(t, e) : k(t, e), t
            }

            function m(e, t) {
                e.recomputing = !0, e.value.length = 0;
                try {
                    e.value[0] = e.fn.apply(null, t)
                } catch (t) {
                    e.value[1] = t
                }
                e.recomputing = !1
            }

            function g(e) {
                return e.dirty || !(!e.dirtyChildren || !e.dirtyChildren.size)
            }

            function b(e) {
                w(e, O)
            }

            function _(e) {
                w(e, k)
            }

            function w(e, t) {
                var r = e.parents.size;
                if (r)
                    for (var n = c(e.parents), i = 0; i < r; ++i) t(n[i], e)
            }

            function O(e, t) {
                p(e.childValues.has(t)), p(g(t));
                var r = !g(e);
                if (e.dirtyChildren) {
                    if (e.dirtyChildren.has(t)) return
                } else e.dirtyChildren = f.pop() || new Set;
                e.dirtyChildren.add(t), r && b(e)
            }

            function k(e, t) {
                p(e.childValues.has(t)), p(!g(t));
                var r, n, i, o = e.childValues.get(t);
                0 === o.length ? e.childValues.set(t, t.value.slice(0)) : (r = o, n = t.value, (i = r.length) > 0 && i === n.length && r[i - 1] === n[i - 1] || e.setDirty()), S(e, t), g(e) || _(e)
            }

            function S(e, t) {
                var r = e.dirtyChildren;
                r && (r.delete(t), 0 === r.size && (f.length < h && f.push(r), e.dirtyChildren = null))
            }

            function E(e) {
                e.childValues.size > 0 && e.childValues.forEach((function(t, r) {
                    R(e, r)
                })), e.forgetDeps(), p(null === e.dirtyChildren)
            }

            function R(e, t) {
                t.parents.delete(e), e.childValues.delete(t), S(e, t)
            }
            var D, P = {
                setDirty: !0,
                dispose: !0,
                forget: !0
            };

            function F() {
                for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                return (D || (D = new n.Trie("function" == typeof WeakMap))).lookupArray(e)
            }
            var T = new Set;
            Object.defineProperty(t, "KeyTrie", {
                enumerable: !0,
                get: function() {
                    return n.Trie
                }
            }), Object.defineProperty(t, "asyncFromGen", {
                enumerable: !0,
                get: function() {
                    return i.asyncFromGen
                }
            }), Object.defineProperty(t, "bindContext", {
                enumerable: !0,
                get: function() {
                    return i.bind
                }
            }), Object.defineProperty(t, "noContext", {
                enumerable: !0,
                get: function() {
                    return i.noContext
                }
            }), Object.defineProperty(t, "setTimeout", {
                enumerable: !0,
                get: function() {
                    return i.setTimeout
                }
            }), t.defaultMakeCacheKey = F, t.dep = function(e) {
                var t = new Map,
                    r = e && e.subscribe;

                function n(e) {
                    var n = s.getValue();
                    if (n) {
                        var i = t.get(e);
                        i || t.set(e, i = new Set), n.dependOn(i), "function" == typeof r && (l(i), i.unsubscribe = r(e))
                    }
                }
                return n.dirty = function(e, r) {
                    var n = t.get(e);
                    if (n) {
                        var i = r && u.call(P, r) ? r : "setDirty";
                        c(n).forEach((function(e) {
                            return e[i]()
                        })), t.delete(e), l(n)
                    }
                }, n
            }, t.nonReactive = function(e) {
                return s.withValue(void 0, e)
            }, t.wrap = function(e, t) {
                var r = void 0 === t ? Object.create(null) : t,
                    n = r.max,
                    i = void 0 === n ? Math.pow(2, 16) : n,
                    o = r.makeCacheKey,
                    u = void 0 === o ? F : o,
                    c = r.keyArgs,
                    l = r.subscribe,
                    f = new a(i, (function(e) {
                        return e.dispose()
                    })),
                    h = function() {
                        var t = u.apply(null, c ? c.apply(null, arguments) : arguments);
                        if (void 0 === t) return e.apply(null, arguments);
                        var r = f.get(t);
                        r || (f.set(t, r = new y(e)), r.subscribe = l, r.forget = function() {
                            return f.delete(t)
                        });
                        var n = r.recompute(Array.prototype.slice.call(arguments));
                        return f.set(t, r), T.add(f), s.hasValue() || (T.forEach((function(e) {
                            return e.clean()
                        })), T.clear()), n
                    };

                function p(e) {
                    var t = f.get(e);
                    t && t.setDirty()
                }

                function d(e) {
                    var t = f.get(e);
                    if (t) return t.peek()
                }

                function v(e) {
                    return f.delete(e)
                }
                return Object.defineProperty(h, "size", {
                    get: function() {
                        return f.map.size
                    },
                    configurable: !1,
                    enumerable: !1
                }), Object.freeze(h.options = {
                    max: i,
                    makeCacheKey: u,
                    keyArgs: c,
                    subscribe: l
                }), h.dirtyKey = p, h.dirty = function() {
                    p(u.apply(null, arguments))
                }, h.peekKey = d, h.peek = function() {
                    return d(u.apply(null, arguments))
                }, h.forgetKey = v, h.forget = function() {
                    return v(u.apply(null, arguments))
                }, h.makeCacheKey = u, h.getKey = c ? function() {
                    return u.apply(null, c.apply(null, arguments))
                } : u, Object.freeze(h)
            }
        }
    }
]);
//# sourceMappingURL=4261.2bae3fe34848bc04.js.map